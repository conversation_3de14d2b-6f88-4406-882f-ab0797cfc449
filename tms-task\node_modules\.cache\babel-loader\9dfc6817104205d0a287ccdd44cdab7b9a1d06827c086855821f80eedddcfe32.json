{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useParams,useNavigate}from\"react-router-dom\";import*as httpSettings from\"@common/api/http\";import{Switch,Table,Select,Skeleton,Button,Popover}from\"antd\";import DraggablePopUp from\"@components/DraggablePopUp\";import moment from\"moment\";import CreateTeamModal,{CREATETYPE_UPGRADE}from\"@components/CreateTeam\";import ProdAuthMbrsDrawer from\"../user/ProdAuthMbrsDrawer\";import MbrAuthProdsDrawer from\"../user/MbrAuthProdsDrawer\";import{CloudDownloadBuy}from\"@/settings/utils/CloudDownloadDraggable\";import ProductVip from\"../../../personal/assets/images/productVip.png\";import ProductVip_x from\"../../../personal/assets/images/productVip_x.png\";import{eOrderStatus,eProductGroupId,eProductId,eProductStatus}from\"@common/utils/enum\";import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const{Column,ColumnGroup}=Table;export default function Settings_Product(_ref){let{teamId:propsTeamId,productId:propsProductId,visible:propsVisible}=_ref;//const dispatch = useDispatch();\nconst navigate=useNavigate();const paramsTeamId=useParams().teamId;const teamId=propsTeamId||paramsTeamId;// 优先使用props，否则使用路由参数\nconst paramsProductId=useParams().productId;const _prodId=propsProductId||paramsProductId;// 优先用props，其次用路由\nconst[productsLoading,setProductsLoading]=useState(false);const[groupList,setGroupList]=useState([]);const[productsForUi,setProductsForUi]=useState([]);//UI呈现的产品列表(会有类型过滤导致显示的产品列表有筛选)\nconst[productsFinal,setProductsFinal]=useState([]);//实际完整的产品列表\nconst[createTeamModalVisible,setCreateTeamModalVisible]=useState(false);const[typeValue,setTypeValue]=useState(0);const[listHeight,setListHeight]=useState(0);const[prodAuthMbrsDrawerVisible,setProdAuthMbrsDrawerVisible]=useState(false);//显示授权穿梭框\nconst[currentAuthProd,setCurrentAuthProd]=useState(null);//\"当前\"点击的授权应用\nconst[historyVisible,setHistoryVisible]=useState(false);const[historyLoading,setHistoryLoading]=useState(false);const[historyList,setHistoryList]=useState([]);const[historyItem,setHistoryItem]=useState(null);const[cloudBuyVisible,setCloudBuyVisible]=useState(false);const[tipsShow,setTipsShow]=useState(false);const[expireVisible,setExpireVisible]=useState(false);const[expireItem,setExpireItem]=useState(null);const[productId,setProductId]=useState(_prodId);const[allUsers,setAllUsers]=useState([]);// 所有用户列表\nconst[authDrawerVisible,setAuthDrawerVisible]=useState(false);// 成员授权应用抽屉\nconst[currentMember,setCurrentMember]=useState(null);// 当前选中的成员\nuseEffect(()=>{loadTeamProductList();loadUserList();},[]);useEffect(()=>{changeListHeight();window.addEventListener(\"resize\",changeListHeight);return()=>{window.removeEventListener(\"resize\",changeListHeight);};},[]);// 最大高度\nconst changeListHeight=()=>{// topbar、标题、页面header、页面footer、table表头页脚\nlet listHeight=document.documentElement.clientHeight-320;setListHeight(listHeight);};//20250621 跳转到产品页时，打开授权对话框\nuseEffect(()=>{if(productId&&productsFinal.length>0&&propsVisible){let item=productsFinal.filter(i=>i.productId==productId);if(paramsProductId){//代表是在 Settings Page形态，而不是 Drawer\nconst newUrl=`/#/${teamId}/settings/product`;//移除 productId\nwindow.history.replaceState({},'',newUrl);setProductId(null);}if(item.length>0)//打开授权产品对话框\nauthProductToMembers(item[0]);}},[_prodId,productsFinal,propsVisible]);//加载授权产品列表\nasync function loadTeamProductList(){setProductsLoading(true);await httpSettings.team_711_get_team_product_list({teamId:teamId}).then(res=>{if(res.resultCode==200){productListFormat(res.productList||[]);}});setProductsLoading(false);}// 加载用户列表\nasync function loadUserList(){try{const res=await httpSettings.setting_202_get_team_allusergrp({teamId:teamId});if(res.resultCode===200){const users=res.userList||[];setAllUsers(users);}}catch(error){console.error('加载用户列表失败:',error);}}function productListFormat(){let productList=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];let _prodList=[];productList.forEach(product=>{let item=_prodList.find(_product=>product.groupId==_product.groupId);if(!item){_prodList.push({groupName:product.groupName,groupId:product.groupId,products:[product]});}else{item.products.push(product);}});setGroupList([..._prodList]);let _allProds=[];_prodList.forEach(group=>{let groupList=group.products.map((_prod,index)=>{var _productsFinal$find,_productsFinal$find2;return{..._prod,key:_prod.productId,isRowSpan:index==0?true:false,groupListLength:index==0?group.products.length:0,authCntDesc:!!_prod.authCntDesc?_prod.authCntDesc:((_productsFinal$find=productsFinal.find(product=>product.productId==_prod.productId))===null||_productsFinal$find===void 0?void 0:_productsFinal$find.authCntDesc)||\"\",objCntDesc:!!_prod.objCntDesc?_prod.objCntDesc:((_productsFinal$find2=productsFinal.find(product=>product.productId==_prod.productId))===null||_productsFinal$find2===void 0?void 0:_productsFinal$find2.objCntDesc)||\"\"};});_allProds=_allProds.concat(groupList);});setProductsForUi([..._allProds]);setProductsFinal([..._allProds]);}//启用/禁用某个应用\nfunction setToggle(productId,enableFlg){httpSettings.team_712_toggle_product({teamId:teamId,productId:productId,enableFlg:enableFlg}).then(res=>{if(res.resultCode==200){productListFormat(res.productList||[]);}});}function authProductToMembers(_product){//打开授权对话框\nsetCurrentAuthProd(_product);setProdAuthMbrsDrawerVisible(true);}function reGetData(){loadTeamProductList();setCreateTeamModalVisible(false);setTipsShow(true);}//显示到期时间\nfunction timeFormat(item){let time=item===null||item===void 0?void 0:item.expirationDt;return(item===null||item===void 0?void 0:item.freeFlg)==1?\"∞\":time?time==\"2099-12-31 23:59:59\"?\"∞\":moment(time).format(\"YYYY-MM-DD\"):\"-\";}function lineColorFormat(item){if(item.enableFlg!=1||item.statusType==eProductStatus.Status_3_Unreleased//即将推出\n){return\"#AAAAAA\";}return\"#000\";}function lineColorXFormat(item,dataIndex){if(item.productId!=eProductId.Pid_11_Explorer&&item.productId!=eProductId.Pid_12_Space&&item.freeFlg==0&&!!item.expirationDt&&moment().isAfter(moment(item.expirationDt))){//已过期\nif(item.enableFlg==1&&dataIndex==\"expirationDt\"){return\"red\";//未禁用\n}return\"#AAAAAA\";}return lineColorFormat(item);}function operationFormat(item){if(item.productId==eProductId.Pid_11_Explorer||item.productId==eProductId.Pid_12_Space||item.statusType==eProductStatus.Status_3_Unreleased){return/*#__PURE__*/_jsx(\"div\",{children:\"-\"});}// if(item.statusType == eProductStatus.Status_3_Unreleased ){ //即将推出\n//   return (\n//     <>\n//       <div style={{width:147}}/>\n//       <div style={{width:50,display:'flex',justifyContent:'center'}}>\n//         <Switch size=\"small\"\n//           checkedChildren=\"启用\"\n//           unCheckedChildren=\"禁用\"\n//           checked={item.enableFlg == 1}\n//           onChange={() => item.enableFlg == 1 ? setToggle(item.productId, 0) : setToggle(item.productId, 1)}\n//         />\n//       </div>\n//     </>\n//   );\n// }\nreturn/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{width:50,textAlign:\"center\"},children:item.freeFlg==0&&!!item.expirationDt&&moment().isAfter(moment(item.expirationDt))?/*#__PURE__*///已过期\n_jsx(\"a\",{onClick:()=>item.productId!=eProductId.Pid_13_Cdisk?setCreateTeamModalVisible(true):setCloudBuyVisible(true),style:item.enableFlg==1?{color:\"#fff\",backgroundColor:\"red\",borderRadius:5,padding:\"2px 8px\"}:{color:\"#fff\",backgroundColor:\"#aaa\",borderRadius:5,padding:\"2px 8px\"},children:\"\\u7EED\\u8D39\"}):/*#__PURE__*/_jsx(\"a\",{onClick:()=>item.productId!=eProductId.Pid_13_Cdisk?setCreateTeamModalVisible(true):setCloudBuyVisible(true),children:\"\\u8D2D\\u4E70\"})}),/*#__PURE__*/_jsx(\"div\",{style:{margin:\"0px 5px\"},children:\"|\"}),/*#__PURE__*/_jsx(\"a\",{style:{width:70,display:\"flex\",justifyContent:\"center\"},onClick:()=>{setHistoryVisible(true);getHistoryList(item);setHistoryItem(item);},children:\"\\u8D2D\\u4E70\\u5386\\u53F2\"}),/*#__PURE__*/_jsx(\"div\",{style:{margin:\"0px 5px\"},children:\"|\"}),/*#__PURE__*/_jsx(\"div\",{style:{width:60,display:\"flex\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(Switch,{size:\"small\",disabled://item.enableFlg != 1 ||   //20250523 Jim 注释掉，不然无法再开启 tmsbug-12291\nitem.statusType==eProductStatus.Status_3_Unreleased,checkedChildren:\"\\u542F\\u7528\",unCheckedChildren:\"\\u7981\\u7528\",checked:item.enableFlg==1,onChange:()=>item.enableFlg==1?setToggle(item.productId,0):setToggle(item.productId,1)})})]});}//单个“应用”的购买历史\nasync function getHistoryList(item){setHistoryLoading(true);await httpSettings.team_722_get_order_list_by_product_id({teamId,productId:item.productId}).then(res=>{if(res.resultCode==200){let list=(res.orderList||[]).map(history=>{history.key=history.id;history.status=history.statusType==eOrderStatus.Status_1_Paid?\"已支付\":\"未支付\";return history;});setHistoryList([...list]);}});setHistoryLoading(false);}function expireVisibleChange(visible,item){setExpireVisible(visible);setExpireItem(item);}const columns=[{title:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:\"\\u7C7B\\u522B\"}),dataIndex:\"groupName\",key:\"groupName\",render:(groupName,item)=>/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:groupName}),onCell:item=>{if(item.isRowSpan){return{rowSpan:item.groupListLength};}else{return{rowSpan:0};}}},{title:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:\"\\u5E94\\u7528\"}),dataIndex:\"productName\",key:\"productName\",render:(productName,item)=>/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",color:lineColorFormat(item)},children:[productName,item.statusType==eProductStatus.Status_2_QA&&/*#__PURE__*/_jsx(\"span\",{style:{color:\"#70B603\",fontSize:12,marginLeft:10},children:\"\\u5185\\u6D4B\\u4E2D\"}),item.statusType==eProductStatus.Status_3_Unreleased&&/*#__PURE__*/_jsx(\"span\",{style:{color:\"#F59A23\",fontSize:12,marginLeft:10},children:\"\\u5373\\u5C06\\u63A8\\u51FA\"})]}),onCell:item=>{return{rowSpan:1};}},{title:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:\"\\u6709\\u6548\\u671F\\u81F3\"}),dataIndex:\"expirationDt\",key:\"expirationDt\",render:(expirationDt,item)=>{if(item.productId!=eProductId.Pid_11_Explorer&&item.productId!=eProductId.Pid_12_Space&&item.freeFlg==0&&!!item.expirationDt&&moment().isAfter(moment(item.expirationDt))){return/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},title:`应用'${item.productName}'Vip已过期，请续费`,children:/*#__PURE__*/_jsx(\"a\",{onClick:()=>{item.productId==eProductId.Pid_13_Cdisk?setCloudBuyVisible(true):setCreateTeamModalVisible(true);},style:{color:lineColorXFormat(item,\"expirationDt\")},children:timeFormat(item)})});}return/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(\"div\",{style:{color:lineColorXFormat(item)},children:timeFormat(item)})});},onCell:item=>{return{rowSpan:1};}},{title:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:\"\\u5E94\\u7528\\u7248\\u672C\"}),dataIndex:\"freeFlg\",key:\"freeFlg\",render:(freeFlg,item)=>/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\",color:lineColorFormat(item)},children:item.groupId==eProductGroupId.Pgid_1_OS&&item.productId!=eProductId.Pid_13_Cdisk?\"-\":freeFlg==0?!!item.expirationDt&&moment().isAfter(moment(item.expirationDt))?/*#__PURE__*/_jsx(\"a\",{style:{color:\"inherit\"},onClick:()=>item.productId!=eProductId.Pid_13_Cdisk?setCreateTeamModalVisible(true):setCloudBuyVisible(true),children:/*#__PURE__*/_jsx(\"img\",{style:{height:22},src:ProductVip_x})}):/*#__PURE__*/_jsx(\"img\",{style:{height:22},src:ProductVip}):\"基础版\"}),onCell:item=>{return{rowSpan:1};}},{title:/*#__PURE__*/_jsx(Popover,{open:tipsShow,placement:\"bottom\",trigger:\"click\",title:/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:\"bold\",fontSize:16},children:\"\\u63D0\\u793A\"}),/*#__PURE__*/_jsx(\"a\",{style:{marginLeft:20,color:\"#666\"},onClick:()=>setTipsShow(false),children:/*#__PURE__*/_jsx(\"span\",{className:\"iconfont guanbi\",style:{fontSize:16}})})]}),content:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:\"center\"},children:[/*#__PURE__*/_jsx(\"div\",{children:\"\\u53EA\\u6709\\u6388\\u6743\\u4E86\\u7684\\u6210\\u5458\\u624D\\u80FD\\u6B63\\u5E38\\u4F7F\\u7528Vip\\u5E94\\u7528\"}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u60A8\\u8D2D\\u4E70\\u4E86Vip\\u5E94\\u7528\\uFF0C\\u53EF\\u70B9\\u51FB\\u201C\\u5DF2\\u6388\\u6743/\\u603B\\u6388\\u6743\\u6570\\u201D\\u5217\\u7684\"}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u84DD\\u8272\\u94FE\\u63A5\\uFF0C\\u8FDB\\u884C\\u5E94\\u7528\\u6388\\u6743\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:30},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",style:{borderRadius:5},onClick:()=>setTipsShow(false),children:\"\\u6211\\u77E5\\u9053\\u4E86\"})})]}),children:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:\"\\u5DF2\\u6388\\u6743/\\u603B\\u6388\\u6743\\u6570\"})}),dataIndex:\"authCntDesc\",key:\"authCntDesc\",render:(authCntDesc,_product)=>{return/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{flex:0.5}}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,display:\"flex\",justifyContent:\"center\"},children:_product.groupId!=eProductGroupId.Pgid_1_OS?/*#__PURE__*/_jsx(\"a\",{onClick:()=>{!!_product.expirationDt&&moment().isAfter(moment(_product.expirationDt))?expireVisibleChange(true,_product):authProductToMembers(_product);},children:authCntDesc||\"\"}):/*#__PURE__*/_jsx(\"div\",{children:\"-\"})}),/*#__PURE__*/_jsx(\"div\",{style:{flex:0.5,display:\"flex\",justifyContent:\"end\"},children:_product.groupId!=eProductGroupId.Pgid_1_OS&&_product.freeFlg==0&&!!_product.expirationDt&&moment().isAfter(moment(_product.expirationDt))&&_product.authUserCnt>_product.authCnt?/*#__PURE__*/_jsx(\"a\",{style:_product.enableFlg==1?{color:\"red\"}:{color:\"#aaa\"},onClick:()=>expireVisibleChange(true,_product),className:\"iconfont guoqitishi\",title:`应用'${_product.productName}'Vip已过期，请续费\\n授权人数超出授权额度，请检查。`}):_product.productId!=eProductId.Pid_11_Explorer&&_product.productId!=eProductId.Pid_12_Space&&_product.freeFlg==0&&!!_product.expirationDt&&moment().isAfter(moment(_product.expirationDt))?/*#__PURE__*/_jsx(\"a\",{style:_product.enableFlg==1?{color:\"red\"}:{color:\"#aaa\"},onClick:()=>expireVisibleChange(true,_product),className:\"iconfont guoqitishi\",title:`应用'${_product.productName}'Vip已过期，请续费`}):_product.authUserCnt>_product.authCnt&&_product.freeFlg==0&&_product.groupId!=eProductGroupId.Pgid_1_OS?/*#__PURE__*/_jsx(\"a\",{style:{color:\"#F59A23\"},onClick:()=>authProductToMembers(_product),title:`授权人数超出授权额度，请检查。`,className:\"iconfont guoqitishi\"}):null})]});},onCell:item=>{return{rowSpan:1};}},{title:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:\"\\u5DF2\\u4F7F\\u7528/\\u603B\\u91CF\"}),dataIndex:\"objCntDesc\",key:\"objCntDesc\",render:(objCntDesc,item)=>{return/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\"},children:[/*#__PURE__*/_jsx(\"div\",{title:item.freeFlg==1||item.productId==eProductId.Pid_13_Cdisk?objCntDesc==\"-\"?\"\":objCntDesc:item.totalCntDesc,className:\"tms-text-overflow\",style:{color:lineColorXFormat(item)},children:usedFormat(item.freeFlg==1||item.productId==eProductId.Pid_13_Cdisk?objCntDesc==\"-\"?\"\":objCntDesc:item.totalCntDesc)}),item.objCntWarningFlg==1&&(item.freeFlg==1||item.productId==eProductId.Pid_13_Cdisk)?/*#__PURE__*/_jsx(\"a\",{style:{color:\"#F59A23\"},onClick:()=>item.productId!=eProductId.Pid_13_Cdisk?setCreateTeamModalVisible(true):setCloudBuyVisible(true),title:`应用'${item.productName}'免费额度已用完，请购买Vip版本`,className:\"iconfont guoqitishi\"}):null]});},onCell:item=>{return{rowSpan:1};}},{title:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:\"\\u64CD\\u4F5C\"}),dataIndex:\"operation\",key:\"operation\",width:230,render:(operation,item)=>/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\",alignItems:\"center\"},children:operationFormat(item)}),onCell:item=>{return{rowSpan:1};}}];function usedFormat(){let str=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"\";let singleList=str.split(\"\");let count=singleList.filter(single=>single==\"∞\").length;if(count==1){return/*#__PURE__*/_jsxs(\"span\",{children:[str.substring(0,str.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str.substring(str.indexOf(\"∞\")+1,str.length)]});}if(count==2){let str2d=str.substring(str.indexOf(\"∞\")+1,str.length);return/*#__PURE__*/_jsxs(\"span\",{children:[str.substring(0,str.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str2d.substring(0,str2d.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str2d.substring(str2d.indexOf(\"∞\")+1,str2d.length)]});}if(count==3){let str2d=str.substring(str.indexOf(\"∞\")+1,str.length);let str3d=str2d.substring(str2d.indexOf(\"∞\")+1,str2d.length);return/*#__PURE__*/_jsxs(\"span\",{children:[str.substring(0,str.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str2d.substring(0,str2d.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str3d.substring(0,str3d.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str3d.substring(str3d.indexOf(\"∞\")+1,str3d.length)]});}if(count==4){let str2d=str.substring(str.indexOf(\"∞\")+1,str.length);let str3d=str2d.substring(str2d.indexOf(\"∞\")+1,str2d.length);let str4d=str3d.substring(str3d.indexOf(\"∞\")+1,str3d.length);return/*#__PURE__*/_jsxs(\"span\",{children:[str.substring(0,str.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str2d.substring(0,str2d.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str3d.substring(0,str3d.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str4d.substring(0,str4d.indexOf(\"∞\")),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16},children:\"\\u221E\"}),str4d.substring(str4d.indexOf(\"∞\")+1,str4d.length)]});}return str;}//应用类型下拉选择，切换类型\nfunction onProductTypeFilterChanged(value){setTypeValue(value);let filterList=value==0?productsFinal:productsFinal.filter(product=>product.groupId==value);setProductsForUi([...filterList]);}function addMonthCntFormat(addMonthCnt){return addMonthCnt==6?\"半年\":addMonthCnt==12?\"1年\":addMonthCnt==24?\"2年\":addMonthCnt==36?\"3年\":addMonthCnt==0?\"-\":addMonthCnt.toString()+\"个月\";}function orderNoFormat(item){if(!!item.orderDt){return moment(item.orderDt).format(\"YYYYMMDDHHmmss\");}if(!!item.paidDt){return moment(item.paidDt).format(\"YYYYMMDDHHmmss\");}return\"-\";}function orderNoClick(item){window.open(window.location.origin+`/#/personal/myorder/${item.orderId}`);}function sort(a,b){let _a=orderNoFormat(a);let _b=orderNoFormat(b);if(_a!=\"-\"&&_b!=\"-\"){return parseInt(_a)-parseInt(_b);}if(_a!=\"-\"&&_b==\"-\"){return parseInt(_a)-0;}if(_a==\"-\"&&_b!=\"-\"){return 0-parseInt(_b);}return 0;}function onClose(saveFlg){setProdAuthMbrsDrawerVisible(false);//关闭授权弹出框\n}return/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsx(Skeleton,{loading:productsLoading,children:/*#__PURE__*/_jsxs(\"div\",{style:{padding:\"10px 0px 5px 20px\",fontSize:14,height:\"100%\"},className:\"product-set\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\"},children:[\"\\u5E94\\u7528\\u7C7B\\u522B\\uFF1A\",/*#__PURE__*/_jsxs(Select,{dropdownMatchSelectWidth:false,value:typeValue,style:{marginLeft:10},onChange:onProductTypeFilterChanged,children:[/*#__PURE__*/_jsx(Select.Option,{value:0,children:\"\\u5168\\u90E8\"}),groupList.map(group=>{return/*#__PURE__*/_jsx(Select.Option,{value:group.groupId,children:group.groupName});})]})]}),!!productsFinal.find(product=>product.freeFlg==0)&&/*#__PURE__*/_jsx(\"div\",{style:{paddingRight:20},children:/*#__PURE__*/_jsx(\"a\",{onClick:()=>{navigate(`/personal/myorder`);},children:\"\\u67E5\\u770B\\u8BA2\\u5355\"})})]}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\",marginTop:10},children:/*#__PURE__*/_jsx(Table,{size:\"small\",className:\"small-table\",bordered:true,style:{paddingRight:20},pagination:false,scroll:{// y: \"calc(100vh - 160px)\", // 不支持calc https://github.com/ant-design/ant-design/issues/31909\ny:listHeight},columns:columns,dataSource:productsForUi})}),/*#__PURE__*/_jsxs(\"div\",{style:{height:80},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginTop:10,height:20,fontSize:12,color:\"#999\",visibility:\"visible\"},children:\"\\u5E94\\u7528\\u4F9D\\u8D56\\u5173\\u7CFB\\u5907\\u6CE8\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginLeft:10,height:20,fontSize:12,color:\"#999\",visibility:\"visible\"},children:\"1. \\u62A5\\u8868\\u3001\\u7CFB\\u7EDF\\u8BA2\\u9605(\\u4F9D\\u8D56\\u4E8E\\u9AD8\\u7EA7\\u641C\\u7D22)\\uFF0C\\u4EEA\\u8868\\u677F(\\u4F9D\\u8D56\\u4E8E\\u9AD8\\u7EA7\\u641C\\u7D22+\\u62A5\\u8868)\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginLeft:10,height:20,fontSize:12,color:\"#999\",visibility:\"visible\"},children:\"2. \\u8BD5\\u5377(\\u4F9D\\u8D56\\u4E8E\\u9898\\u5E93)\\uFF0C\\u8003\\u8BD5/\\u4F5C\\u4E1A(\\u4F9D\\u8D56\\u4E8E\\u8BD5\\u5377+\\u9898\\u5E93)\"})]})]})}),/*#__PURE__*/_jsx(CreateTeamModal,{teamId:teamId,type:CREATETYPE_UPGRADE,visible:createTeamModalVisible,onCancel:()=>setCreateTeamModalVisible(false),onOk:()=>reGetData(),productList:productsFinal}),!!cloudBuyVisible&&/*#__PURE__*/_jsx(CloudDownloadBuy,{visible:cloudBuyVisible,teamId:teamId,onCancel:()=>setCloudBuyVisible(false),reGet:()=>{loadTeamProductList();setCloudBuyVisible(false);}}),!!currentAuthProd&&/*#__PURE__*/_jsx(ProdAuthMbrsDrawer,{teamId:teamId,currentAuthProd:currentAuthProd,allUsers:allUsers,prodAuthMbrsDrawerVisible:prodAuthMbrsDrawerVisible,setProdAuthMbrsDrawerVisible:setProdAuthMbrsDrawerVisible,loadTeamMemberList:loadTeamProductList,onOpenMemberAuthDrawer:member=>{// 切换到成员授权应用抽屉\nsetCurrentMember(member);setAuthDrawerVisible(true);setProdAuthMbrsDrawerVisible(false);}}),!!currentMember&&/*#__PURE__*/_jsx(MbrAuthProdsDrawer,{teamId:teamId,currentMember:currentMember,authProdList:currentMember===null||currentMember===void 0?void 0:currentMember.authProductList,loadTeamMemberList:loadTeamProductList,authDrawerVisible:authDrawerVisible,setAuthDrawerVisible:setAuthDrawerVisible,allUsers:allUsers,onMemberChange:member=>{setCurrentMember(member);}}),/*#__PURE__*/_jsx(DraggablePopUp,{className:\"tms-modal\",title:`购买历史-${historyItem===null||historyItem===void 0?void 0:historyItem.productName}`,open:historyVisible,centered:true,width:1000,onCancel:()=>setHistoryVisible(false),footer:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",style:{borderRadius:5},onClick:()=>setHistoryVisible(false),children:\"\\u6211\\u77E5\\u9053\\u4E86\"})}),children:/*#__PURE__*/_jsx(Skeleton,{loading:historyLoading,children:/*#__PURE__*/_jsxs(Table,{size:\"small\",className:\"before-header\",bordered:true,dataSource:historyList,showSorterTooltip:false,pagination:{position:[\"bottomCenter\"],size:\"small\",pageSize:10,showQuickJumper:true,showSizeChanger:false,total:historyList.length,showTotal:total=>{return`共${total}条`;}},children:[/*#__PURE__*/_jsx(Column,{title:\"#\",dataIndex:\"seqNo\",render:(seqNo,item,index)=>/*#__PURE__*/_jsx(\"div\",{children:index+1})},\"seqNo\"),/*#__PURE__*/_jsx(Column,{title:\"订单号\",dataIndex:\"orderNo\",render:(orderNo,item)=>item.ownerFlg==1&&(!!item.orderDt||!!item.paidDt)?/*#__PURE__*/_jsx(\"a\",{onClick:()=>orderNoClick(item),children:orderNoFormat(item)}):/*#__PURE__*/_jsx(\"div\",{children:orderNoFormat(item)})},\"orderNo\"),/*#__PURE__*/_jsxs(ColumnGroup,{title:\"购买前\",className:\"top-header-a\",children:[/*#__PURE__*/_jsx(Column,{title:(historyItem===null||historyItem===void 0?void 0:historyItem.productId)==eProductId.Pid_13_Cdisk?\"下载流量\":\"授权数\",dataIndex:\"authCntBefore\",className:\"top-header-a\",render:authCntBefore=>(historyItem===null||historyItem===void 0?void 0:historyItem.productId)==eProductId.Pid_13_Cdisk?/*#__PURE__*/_jsxs(\"div\",{children:[authCntBefore,\"G\"]}):authCntBefore>0?/*#__PURE__*/_jsxs(\"div\",{children:[authCntBefore,\"\\u4EBA\"]}):/*#__PURE__*/_jsx(\"div\",{children:\"-\"})},\"authCntBefore\"),/*#__PURE__*/_jsx(Column,{title:\"有效期至\",dataIndex:\"expirationDtBefore\",className:\"top-header-a\",render:expirationDtBefore=>/*#__PURE__*/_jsx(\"div\",{children:!!expirationDtBefore?moment(expirationDtBefore).format(\"YYYY-MM-DD\"):\"-\"})},\"expirationDtBefore\")]}),/*#__PURE__*/_jsxs(ColumnGroup,{title:\"规格选择\",className:\"top-header-b\",children:[/*#__PURE__*/_jsx(Column,{title:(historyItem===null||historyItem===void 0?void 0:historyItem.productId)==eProductId.Pid_13_Cdisk?\"下载流量\":\"增/减员数\",dataIndex:\"adjustAuthCnt\",className:\"top-header-b\",render:adjustAuthCnt=>(historyItem===null||historyItem===void 0?void 0:historyItem.productId)==eProductId.Pid_13_Cdisk?/*#__PURE__*/_jsxs(\"div\",{children:[adjustAuthCnt,\"G\"]}):/*#__PURE__*/_jsxs(\"div\",{children:[adjustAuthCnt<0?\"\":\"+\",adjustAuthCnt||0,\"\\u4EBA\"]})},\"adjustAuthCnt\"),/*#__PURE__*/_jsx(Column,{title:\"购买时长\",dataIndex:\"adjustMonthCnt\",className:\"top-header-b\",render:adjustMonthCnt=>/*#__PURE__*/_jsx(\"div\",{children:addMonthCntFormat(adjustMonthCnt)})},\"adjustMonthCnt\")]}),/*#__PURE__*/_jsxs(ColumnGroup,{title:\"购买结果\",className:\"top-header-c\",children:[/*#__PURE__*/_jsx(Column,{title:(historyItem===null||historyItem===void 0?void 0:historyItem.productId)==eProductId.Pid_13_Cdisk?\"下载流量\":\"授权数\",dataIndex:\"authCnt\",className:\"top-header-c\",render:authCnt=>(historyItem===null||historyItem===void 0?void 0:historyItem.productId)==eProductId.Pid_13_Cdisk?/*#__PURE__*/_jsxs(\"div\",{children:[authCnt,\"G\"]}):/*#__PURE__*/_jsxs(\"div\",{children:[authCnt,\"\\u4EBA\"]})},\"authCnt\"),/*#__PURE__*/_jsx(Column,{title:\"有效期至\",dataIndex:\"expirationDt\",className:\"top-header-c\",render:expirationDt=>/*#__PURE__*/_jsx(\"div\",{children:!!expirationDt?moment(expirationDt).format(\"YYYY-MM-DD\"):\"-\"})},\"expirationDt\")]}),/*#__PURE__*/_jsx(Column,{title:\"下单人\",dataIndex:\"creatorName\",render:creatorName=>/*#__PURE__*/_jsx(\"div\",{children:!!creatorName?creatorName:\"-\"})},\"creatorName\"),/*#__PURE__*/_jsx(Column,{title:\"下单时间\",dataIndex:\"orderDt\",render:orderDt=>/*#__PURE__*/_jsx(\"div\",{children:!!orderDt?moment(orderDt).format(\"YYYY-MM-DD HH:mm:ss\"):\"-\"}),sorter:sort,defaultSortOrder:\"descend\"},\"orderDt\"),/*#__PURE__*/_jsx(Column,{title:\"状态\",dataIndex:\"status\",render:(status,item)=>/*#__PURE__*/_jsx(\"div\",{children:status})},\"status\")]})})}),/*#__PURE__*/_jsx(DraggablePopUp,{className:\"tms-modal\",title:\"\\u5E94\\u7528\\u8FC7\\u671F\\u63D0\\u793A\",open:expireVisible&&!!expireItem,centered:true,width:400,maskClosable:false,onCancel:()=>expireVisibleChange(false,null),footer:/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",justifyContent:\"center\"},children:[/*#__PURE__*/_jsx(Button,{style:{borderRadius:5},onClick:()=>expireVisibleChange(false,null),children:\"\\u6211\\u77E5\\u9053\\u4E86\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",style:{borderRadius:5},onClick:()=>{(expireItem===null||expireItem===void 0?void 0:expireItem.productId)==eProductId.Pid_13_Cdisk?setCloudBuyVisible(true):setCreateTeamModalVisible(true);expireVisibleChange(false,null);},children:\"\\u7EED\\u8D39\"})]}),children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:\"center\"},children:[\"\\u5E94\\u7528(\",expireItem===null||expireItem===void 0?void 0:expireItem.productName,\")\\u6709\\u6548\\u671F\\u81F3\",timeFormat(expireItem),\"\\uFF0C\\u5DF2\\u8FC7\\u671F\"]})})]});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}