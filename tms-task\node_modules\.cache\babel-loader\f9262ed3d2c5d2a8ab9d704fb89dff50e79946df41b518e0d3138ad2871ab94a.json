{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\PageShare\\\\PageShare.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Button, Checkbox, Table, Select, Popconfirm, Input, Avatar } from \"antd\";\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport { setting_202_get_team_allusergrp, team_570_get_share_user_list, team_568_save_share_user } from \"@common/api/http\";\nimport \"./PageShare.scss\";\nimport { nanoid } from \"nanoid\";\nimport { CaretDownOutlined } from '@ant-design/icons';\nimport { useNavigate } from \"react-router-dom\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { NoAvatarIcon } from '@common/components/IconUtil';\nimport { useQueryClient } from \"@tanstack/react-query\";\nimport { useQuerySetting324_getNodeLockStatus } from \"@common/service/commonHooks\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SHARESTATUS_0 = 0; // 不是管理员，看不见共享图标\nconst SHARESTATUS_1 = 1; // 灰色共享图标，初始状态，没有共享\nconst SHARESTATUS_2 = 2; // 绿色共享图标，有共享\n\n/**\r\n * @description 共享链接点击后弹窗 团队共享\r\n */\nfunction PageShare({\n  teamId,\n  nodeId,\n  setVisible\n}) {\n  _s();\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n  const [finalDataSource, setFinalDataSource] = useState([]);\n  const [dataSource, setDataSource] = useState([]);\n  const [allUserList, setAllUserList] = useState([]);\n  const [teamShareNodeId, setTeamShareNodeId] = useState(null);\n  useEffect(() => {\n    getAllUserList();\n  }, []);\n  function getAllUserList() {\n    setting_202_get_team_allusergrp({\n      teamId\n    }).then(res => {\n      let list = ((res === null || res === void 0 ? void 0 : res.userList) || []).filter(user => user.deleteFlg == 0 && user.enableFlg == 1);\n      if (res.resultCode == 200) {\n        setAllUserList(list);\n      }\n      getShareUserList(list);\n    });\n  }\n  function getShareUserList(memberList) {\n    team_570_get_share_user_list({\n      teamId,\n      nodeId\n    }).then(res => {\n      if (res.resultCode == 200) {\n        let dataList = (res.userList || []).filter(user => user.userId).map(user => {\n          var _memberList$find;\n          user.key = user.userId;\n          user.userName = /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [avatarFormat((_memberList$find = memberList.find(mem => mem.userId == user.userId)) === null || _memberList$find === void 0 ? void 0 : _memberList$find.avatar, user.userName), user.userName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 27\n          }, this);\n          user.isNewAdd = false;\n          return user;\n        });\n        if (dataList.length < memberList.length) {\n          dataList = [addNewRow()].concat(dataList);\n        }\n        setFinalDataSource([...dataList]);\n        setDataSource([...dataList]);\n        setTeamShareNodeId(res.teamShareNodeId);\n        return;\n      }\n      let list = [];\n      list.push(addNewRow());\n      setFinalDataSource([...list]);\n      setDataSource([...list]);\n    });\n  }\n  function avatarFormat(src, name) {\n    return /*#__PURE__*/_jsxDEV(Avatar, {\n      style: {\n        marginRight: 5\n      },\n      src: src,\n      icon: /*#__PURE__*/_jsxDEV(NoAvatarIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 61\n      }, this),\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this);\n  }\n  function addNewRow() {\n    return {\n      key: nanoid(),\n      userId: null,\n      userName: '',\n      isNewAdd: true\n    };\n  }\n  function userNameChange(value, name, index, flag) {\n    dataSource[index].isNewAdd = false;\n    dataSource[index].key = value;\n    dataSource[index].userId = value;\n    dataSource[index].userName = name;\n    let list = [];\n    if (flag != 1) {\n      list = [addNewRow()].concat(dataSource);\n    } else {\n      list = dataSource;\n    }\n    setFinalDataSource([...list]);\n    setDataSource([...list]);\n  }\n  function deleteRow(userId) {\n    let list = [];\n    let filterList = dataSource.filter(data => data.userId != userId);\n    if (dataSource.find(data => data.isNewAdd)) {\n      list = filterList;\n    } else {\n      list = [addNewRow()].concat(filterList);\n    }\n    setDataSource(list);\n    setFinalDataSource(list);\n  }\n  const columns = [{\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center'\n      },\n      children: \"\\u6210\\u5458\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 14\n    }, this),\n    dataIndex: 'userName',\n    key: 'userName',\n    width: '20%',\n    render: (value, row, rowIndex) => row.isNewAdd ? /*#__PURE__*/_jsxDEV(Select, {\n      showSearch: true,\n      bordered: false,\n      style: {\n        width: 120,\n        alignItems: 'center'\n      },\n      placeholder: \"\\u8BF7\\u9009\\u62E9\",\n      value: value ? value : null\n      //dropdownStyle={allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).length > 6 && {maxHeight:200}}\n      ,\n      optionFilterProp: \"children\",\n      filterOption: (input, option) => {\n        var _option$key;\n        return ((_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : '').toLowerCase().includes(input.toLowerCase());\n      },\n      dropdownMatchSelectWidth: false,\n      suffixIcon: /*#__PURE__*/_jsxDEV(CaretDownOutlined, {\n        style: {\n          pointerEvents: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 21\n      }, this),\n      onChange: (value, item) => userNameChange(value, item.label, rowIndex, allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).length == 1 ? 1 : 2),\n      options: allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).map((user, userIndex) => ({\n        value: user.userId,\n        label: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [avatarFormat(user.avatar, user.userName), user.userName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 18\n        }, this),\n        key: user.userName\n      }))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)\n  }, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center'\n      },\n      children: \"\\u6743\\u9650\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 16\n    }, this),\n    dataIndex: 'priv',\n    key: 'priv',\n    width: '65%',\n    render: (value, row, rowIndex) => row.userId && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n        disabled: true,\n        className: \"fontsize-12 share-grey-checked-without-border\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          marginLeft: 10\n        },\n        children: \"\\u8BFB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center'\n      },\n      children: \"\\u64CD\\u4F5C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 14\n    }, this),\n    dataIndex: 'operation',\n    width: '15%',\n    key: 'operation',\n    render: (value, row) => row.userId && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u5417?\",\n        okText: \"确定\",\n        cancelText: \"取消\",\n        onConfirm: () => deleteRow(row.userId),\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"fontsize-12\",\n          type: \"link\",\n          size: \"small\",\n          style: {\n            borderRadius: 4\n          },\n          children: \"\\u79FB\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)\n  }];\n  function keywordsSelect(e) {\n    let keywords = e.target.value;\n    let dataList = finalDataSource.filter(user => user.userName == '' || user.userName != '' && user.userName.props.children[1].toLowerCase().includes(keywords.toLowerCase()));\n    setDataSource([...dataList]);\n  }\n\n  //fix tmsbug-6316: 共享对话框，点击“我的”或“团队共享”蓝色按钮，页面出错\n  function navigateTo() {\n    !!teamShareNodeId && navigate(`/team/${teamId}/explorer/${teamShareNodeId}`);\n  }\n  function onOk() {\n    let params = {\n      teamId: teamId,\n      nodeId: nodeId,\n      userList: dataSource.filter(data => !data.isNewAdd).map(data => ({\n        shareUserId: data.userId\n      }))\n    };\n    team_568_save_share_user(params).then(res => {\n      if (res.resultCode == 200) {\n        globalUtil.success('共享操作成功');\n        queryClient.invalidateQueries('use_query_setting_324_get_node_lock_status');\n        setVisible(false);\n      }\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        marginBottom: 10,\n        marginTop: -10\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#999',\n          marginRight: 10\n        },\n        children: \"\\u5F53\\u524D\\u5BF9\\u8C61\\u5171\\u4EAB\\u7ED9\\u5982\\u4E0B\\u56E2\\u961F\\u6210\\u5458\\uFF0C\\u5171\\u4EAB\\u4F4D\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        onClick: navigateTo,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"iconfont chengyuan fontsize-14\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), \"\\u6211\\u7684\\xA0->\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"iconfont fenxiang2 fontsize-14\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), \"\\u56E2\\u961F\\u5171\\u4EAB\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Input, {\n      placeholder: \"\\u5173\\u952E\\u5B57\\u6A21\\u7CCA\\u641C\\u7D22\",\n      autoComplete: \"off\",\n      style: {\n        borderRadius: 5,\n        width: 300,\n        marginBottom: 10\n      },\n      onChange: keywordsSelect\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      size: \"small\",\n      columns: columns,\n      dataSource: dataSource,\n      pagination: false,\n      scroll: dataSource.length > 6 && {\n        y: 260\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: '#999',\n        marginTop: 10\n      },\n      children: [\"\\u5171\", dataSource.filter(data => !data.isNewAdd).length, \"\\u6761\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: '#999',\n        marginTop: 30\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u5907\\u6CE8\\uFF1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"1\\u3001\\u5F53\\u524D\\u5BF9\\u8C61\\u53EF\\u5171\\u4EAB\\u7ED9\\u56E2\\u961F\\u6210\\u5458(\\u4E0D\\u9650\\u4E8E\\u5F53\\u524D\\u534F\\u4F5C\\u7FA4\\u7684\\u6210\\u5458)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"2\\u3001\\u5F53\\u524D\\u5BF9\\u8C61\\u8282\\u70B9\\u53CA\\u5176\\u5B50\\u8282\\u70B9\\uFF0C\\u4E00\\u5E76\\u5171\\u4EAB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"3\\u3001\\u5171\\u4EAB\\u7684\\u5BF9\\u8C61\\u4E0D\\u53D7\\u89D2\\u8272\\u6743\\u9650\\u7684\\u9650\\u5236\\uFF0C\\u4E5F\\u4E0D\\u53D7\\u5F53\\u524D\\u201C\\u5BF9\\u8C61\\u8BBE\\u9650(\", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"iconfont lock1\",\n          style: {\n            color: \"#D9001B\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 43\n        }, this), \")\\u201D\\u7684\\u9650\\u5236\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        marginTop: 20\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        size: \"middle\",\n        style: {\n          borderRadius: 5,\n          marginRight: 10\n        },\n        onClick: () => setVisible(false),\n        children: \"\\u53D6\\u6D88\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"middle\",\n        style: {\n          borderRadius: 5\n        },\n        onClick: () => onOk(),\n        children: \"\\u786E\\u5B9A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n}\n_s(PageShare, \"AevUr4l260mpvpcg3EhdEj6sjtQ=\", false, function () {\n  return [useNavigate, useQueryClient];\n});\n_c = PageShare;\nfunction PowerShare({\n  teamId,\n  nodeId,\n  shareNo\n}) {\n  _s2();\n  const [shareStatus, setShareStatus] = useState(SHARESTATUS_0);\n  const [visible, setVisible] = useState(false);\n  const {\n    data: status = {\n      lockStatus: 0,\n      shareStatus: 0\n    }\n  } = useQuerySetting324_getNodeLockStatus(teamId, nodeId);\n  useEffect(() => {\n    setShareStatus(status.shareStatus);\n  }, [status]);\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [nodeId && shareStatus != SHARESTATUS_0 && (shareNo ? /*#__PURE__*/_jsxDEV(\"a\", {\n      onClick: () => setVisible(true),\n      children: shareNo == '-' ? '' : shareNo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Button, {\n      type: \"text\",\n      style: {\n        marginLeft: \"5px\"\n      },\n      className: \"sidebarTextColor\",\n      title: \"\\u56E2\\u961F\\u5171\\u4EAB\",\n      onClick: () => setVisible(true),\n      icon: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"iconfont fenxiang2\",\n        style: SHARESTATUS_2 == shareStatus ? {\n          color: \"#02dd10\"\n        } : {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this)), nodeId && shareStatus != SHARESTATUS_0 && /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold'\n        },\n        children: \"\\u56E2\\u961F\\u5171\\u4EAB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 18\n      }, this),\n      width: 700,\n      open: visible,\n      destroyOnClose: true,\n      maskClosable: false,\n      footer: null,\n      onCancel: () => setVisible(false),\n      children: /*#__PURE__*/_jsxDEV(PageShare, {\n        teamId: teamId,\n        nodeId: nodeId,\n        setVisible: setVisible\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 236,\n    columnNumber: 10\n  }, this);\n}\n_s2(PowerShare, \"NMSRPv/EGZlMZXRRkgLXtIEZoTM=\", false, function () {\n  return [useQuerySetting324_getNodeLockStatus];\n});\n_c2 = PowerShare;\nexport default _c3 = /*#__PURE__*/React.memo(PowerShare);\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"PageShare\");\n$RefreshReg$(_c2, \"PowerShare\");\n$RefreshReg$(_c3, \"%default%\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Checkbox", "Table", "Select", "Popconfirm", "Input", "Avatar", "DraggablePopUp", "setting_202_get_team_allusergrp", "team_570_get_share_user_list", "team_568_save_share_user", "nanoid", "CaretDownOutlined", "useNavigate", "globalUtil", "NoAvatarIcon", "useQueryClient", "useQuerySetting324_getNodeLockStatus", "jsxDEV", "_jsxDEV", "SHARESTATUS_0", "SHARESTATUS_1", "SHARESTATUS_2", "PageShare", "teamId", "nodeId", "setVisible", "_s", "navigate", "queryClient", "finalDataSource", "setFinalDataSource", "dataSource", "setDataSource", "allUserList", "setAllUserList", "teamShareNodeId", "setTeamShareNodeId", "getAllUserList", "then", "res", "list", "userList", "filter", "user", "deleteFlg", "enableFlg", "resultCode", "getShareUserList", "memberList", "dataList", "userId", "map", "_memberList$find", "key", "userName", "style", "display", "alignItems", "children", "avatarFormat", "find", "mem", "avatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isNewAdd", "length", "addNewRow", "concat", "push", "src", "name", "marginRight", "icon", "size", "userNameChange", "value", "index", "flag", "deleteRow", "filterList", "data", "columns", "title", "justifyContent", "dataIndex", "width", "render", "row", "rowIndex", "showSearch", "bordered", "placeholder", "optionFilterProp", "filterOption", "input", "option", "_option$key", "toLowerCase", "includes", "dropdownMatchSelectWidth", "suffixIcon", "pointerEvents", "onChange", "item", "label", "options", "userIndex", "disabled", "className", "marginLeft", "okText", "cancelText", "onConfirm", "type", "borderRadius", "keywordsSelect", "e", "keywords", "target", "props", "navigateTo", "onOk", "params", "shareUserId", "success", "invalidateQueries", "Fragment", "marginBottom", "marginTop", "color", "onClick", "autoComplete", "pagination", "scroll", "y", "_c", "PowerShare", "shareNo", "_s2", "shareStatus", "setShareStatus", "visible", "status", "lockStatus", "fontWeight", "open", "destroyOnClose", "maskClosable", "footer", "onCancel", "_c2", "_c3", "memo", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/PageShare/PageShare.jsx"], "sourcesContent": ["import React, { useEffect,useState } from \"react\";\r\nimport {Button, Checkbox, Table, Select,Popconfirm, Input, Avatar} from \"antd\";\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";\r\nimport { setting_202_get_team_allusergrp, team_570_get_share_user_list, team_568_save_share_user } from \"@common/api/http\";\r\nimport \"./PageShare.scss\";\r\nimport { nanoid } from \"nanoid\";\r\nimport { CaretDownOutlined } from '@ant-design/icons';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { NoAvatarIcon } from '@common/components/IconUtil';\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { useQuerySetting324_getNodeLockStatus } from \"@common/service/commonHooks\";\r\n\r\nconst SHARESTATUS_0 = 0; // 不是管理员，看不见共享图标\r\nconst SHARESTATUS_1 = 1; // 灰色共享图标，初始状态，没有共享\r\nconst SHARESTATUS_2 = 2; // 绿色共享图标，有共享\r\n\r\n/**\r\n * @description 共享链接点击后弹窗 团队共享\r\n */\r\nfunction PageShare({teamId,nodeId,setVisible}) {\r\n  const navigate = useNavigate();\r\n  const queryClient = useQueryClient();\r\n\r\n  const [finalDataSource,setFinalDataSource] = useState([]);\r\n  const [dataSource,setDataSource] = useState([]);\r\n  const [allUserList,setAllUserList] = useState([]);\r\n  const [teamShareNodeId,setTeamShareNodeId] = useState(null);\r\n\r\n  useEffect(()=>{\r\n    getAllUserList()\r\n  },[]);\r\n\r\n  function getAllUserList() {\r\n    setting_202_get_team_allusergrp({teamId}).then(res => {\r\n      let list = (res?.userList||[]).filter(user => user.deleteFlg == 0 && user.enableFlg == 1)\r\n      if(res.resultCode == 200){\r\n        setAllUserList(list)\r\n      }\r\n      getShareUserList(list)\r\n    });\r\n  }\r\n\r\n  function getShareUserList(memberList){\r\n    team_570_get_share_user_list({teamId,nodeId}).then(res => {\r\n      if(res.resultCode == 200){\r\n        let dataList = (res.userList||[]).filter(user => user.userId).map(user => {\r\n          user.key = user.userId\r\n          user.userName = <div style={{display:'flex',alignItems:'center'}}>{avatarFormat(memberList.find(mem => mem.userId == user.userId)?.avatar,user.userName)}{user.userName}</div>\r\n          user.isNewAdd = false\r\n          return user\r\n        })\r\n        if(dataList.length < memberList.length){\r\n          dataList = [addNewRow()].concat(dataList)\r\n        }\r\n        setFinalDataSource([...dataList])\r\n        setDataSource([...dataList])\r\n        setTeamShareNodeId(res.teamShareNodeId);\r\n        return\r\n      }\r\n      let list = []\r\n      list.push(addNewRow())\r\n      setFinalDataSource([...list])\r\n      setDataSource([...list])\r\n    });\r\n  }\r\n\r\n  function avatarFormat(src,name) {\r\n    return (<Avatar style={{marginRight:5}} src={src} icon={<NoAvatarIcon/>} size={24}/>);\r\n  }\r\n\r\n  function addNewRow() {\r\n    return {key:nanoid(),userId:null,userName:'',isNewAdd:true}\r\n  }\r\n\r\n  function userNameChange(value,name,index,flag) {\r\n    dataSource[index].isNewAdd = false\r\n    dataSource[index].key = value\r\n    dataSource[index].userId = value\r\n    dataSource[index].userName = name\r\n    let list = []\r\n    if(flag != 1){\r\n      list = [addNewRow()].concat(dataSource)\r\n    }else{\r\n      list = dataSource\r\n    }\r\n    setFinalDataSource([...list])\r\n    setDataSource([...list])\r\n  }\r\n\r\n  function deleteRow(userId) {\r\n    let list = []\r\n    let filterList = dataSource.filter(data => data.userId != userId)\r\n    if(dataSource.find(data => data.isNewAdd)){\r\n      list = filterList\r\n    }else{\r\n      list = [addNewRow()].concat(filterList)\r\n    }\r\n    setDataSource(list)\r\n    setFinalDataSource(list)\r\n  }\r\n\r\n  const columns = [\r\n    {\r\n      title: <div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>成员</div>,\r\n      dataIndex: 'userName',\r\n      key: 'userName',\r\n      width: '20%',\r\n      render: (value, row, rowIndex) => \r\n      row.isNewAdd ? \r\n      <Select \r\n       showSearch\r\n        bordered={false}\r\n        style={{width:120,alignItems:'center'}}\r\n        placeholder='请选择'\r\n        value={value ? value : null}\r\n        //dropdownStyle={allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).length > 6 && {maxHeight:200}}\r\n        optionFilterProp=\"children\"\r\n        filterOption={(input, option) => (option?.key ?? '').toLowerCase().includes(input.toLowerCase())}\r\n        dropdownMatchSelectWidth={false}\r\n        suffixIcon={<CaretDownOutlined style={{pointerEvents:'none'}}/>}\r\n        onChange={(value,item) =>\r\n          userNameChange(value,item.label,rowIndex,\r\n                    allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).length == 1 ? 1 : 2\r\n          )\r\n        }\r\n        options={allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).map((user,userIndex) => ({\r\n          value: user.userId,\r\n          label: <div style={{display:'flex',alignItems:'center'}}>{avatarFormat(user.avatar,user.userName)}{user.userName}</div>,\r\n          key: user.userName,\r\n        }))}\r\n      />\r\n      :\r\n      <div>{value}</div>\r\n    },\r\n    {\r\n        title: <div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>权限</div>,\r\n        dataIndex: 'priv',\r\n        key: 'priv',\r\n        width: '65%',\r\n        render: (value, row, rowIndex) => \r\n        row.userId &&\r\n        <div style={{display:'flex',alignItems:'center',justifyContent:'center'}}>\r\n          <Checkbox disabled className='fontsize-12 share-grey-checked-without-border'/>\r\n          <span style={{marginLeft:10}}>读</span>\r\n        </div>\r\n    },\r\n    {\r\n      title: <div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>操作</div>, \r\n      dataIndex: 'operation', \r\n      width: '15%', \r\n      key: 'operation',\r\n      render: (value, row) => \r\n      row.userId && \r\n      <div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>\r\n        <Popconfirm title=\"确定删除吗?\" okText={\"确定\"} cancelText={\"取消\"} onConfirm={() => deleteRow(row.userId)}>\r\n          <Button className=\"fontsize-12\" type=\"link\" size=\"small\" style={{ borderRadius: 4 }}>移除</Button>\r\n        </Popconfirm>\r\n      </div>\r\n    },\r\n  ];\r\n\r\n  function keywordsSelect(e) {\r\n    let keywords = e.target.value\r\n    let dataList = finalDataSource.filter(user => user.userName == '' || (user.userName != '' && user.userName.props.children[1].toLowerCase().includes(keywords.toLowerCase())))\r\n    setDataSource([...dataList])\r\n  }\r\n\r\n  //fix tmsbug-6316: 共享对话框，点击“我的”或“团队共享”蓝色按钮，页面出错\r\n  function navigateTo() {\r\n    !!teamShareNodeId && navigate(`/team/${teamId}/explorer/${teamShareNodeId}`)\r\n  }\r\n\r\n  function onOk() {\r\n    let params = {\r\n      teamId: teamId,\r\n      nodeId: nodeId,\r\n      userList: dataSource.filter(data => !data.isNewAdd).map(data => ({\r\n                  shareUserId: data.userId\r\n                }))\r\n    }\r\n    team_568_save_share_user(params).then(res => {\r\n      if(res.resultCode == 200){\r\n        globalUtil.success('共享操作成功')\r\n        queryClient.invalidateQueries('use_query_setting_324_get_node_lock_status');\r\n        setVisible(false);\r\n      }\r\n    });\r\n  }\r\n\r\n  return (\r\n    <React.Fragment>\r\n      <div style={{display:'flex',alignItems: 'center',marginBottom:10,marginTop:-10}}>\r\n        <span style={{color:'#999',marginRight:10}}>当前对象共享给如下团队成员，共享位置</span>\r\n        <a onClick={navigateTo}>\r\n          {/* fixBUG tmsbug-3670 团队共享，弹框文案修改*/}\r\n          <span className=\"iconfont chengyuan fontsize-14\" />我的&nbsp;-&gt;&nbsp;\r\n          <span className=\"iconfont fenxiang2 fontsize-14\" />团队共享\r\n        </a>\r\n      </div>\r\n      <Input placeholder=\"关键字模糊搜索\" \r\n             autoComplete=\"off\" \r\n             style={{borderRadius:5,width:300,marginBottom:10}}\r\n             onChange={keywordsSelect}/>\r\n      <Table \r\n        bordered\r\n        size=\"small\"\r\n        columns={columns} \r\n        dataSource={dataSource} \r\n        pagination={false}\r\n        scroll={dataSource.length > 6 && { y: 260 }}/>\r\n      <div style={{color:'#999',marginTop:10}}>共{dataSource.filter(data => !data.isNewAdd).length}条</div>\r\n      <div style={{color:'#999',marginTop:30}}>\r\n        <div>备注：</div>\r\n        <div>1、当前对象可共享给团队成员(不限于当前协作群的成员)</div>\r\n        <div>2、当前对象节点及其子节点，一并共享</div>\r\n        <div>3、共享的对象不受角色权限的限制，也不受当前“对象设限({<span className=\"iconfont lock1\" style={{color:\"#D9001B\"}}/>})”的限制</div>\r\n      </div>\r\n      <div style={{display:'flex',justifyContent:'center',alignItems:'center',marginTop:20}}>\r\n        <Button type=\"default\" size=\"middle\" style={{borderRadius:5,marginRight:10}} onClick={()=>setVisible(false)}>取消</Button>\r\n        <Button type=\"primary\" size=\"middle\" style={{borderRadius:5}} onClick={()=>onOk()}>确定</Button>\r\n      </div>\r\n    </React.Fragment>\r\n  );\r\n}\r\n\r\nfunction PowerShare({teamId, nodeId, shareNo}) {\r\n  const [shareStatus,setShareStatus] = useState(SHARESTATUS_0);\r\n  const [visible, setVisible] = useState(false);\r\n  const {data: status = {lockStatus: 0, shareStatus: 0}} = useQuerySetting324_getNodeLockStatus(teamId,nodeId);\r\n\r\n  useEffect(() => {\r\n    setShareStatus(status.shareStatus)\r\n  },[status])\r\n\r\n  return <React.Fragment>\r\n    { nodeId && shareStatus != SHARESTATUS_0 \r\n      && ( shareNo ? \r\n        <a onClick={() => setVisible(true)}>{shareNo == '-' ? '' : shareNo}</a>\r\n        :\r\n        <Button \r\n          type=\"text\" \r\n          style={{marginLeft: \"5px\"}}\r\n          className=\"sidebarTextColor\"\r\n          title=\"团队共享\"\r\n          onClick={() => setVisible(true)}\r\n          icon={\r\n            <span \r\n              className='iconfont fenxiang2'\r\n              style={SHARESTATUS_2 == shareStatus ? {color:\"#02dd10\"} : {}}>\r\n            </span>}>\r\n        </Button>\r\n      )}\r\n      {nodeId && shareStatus != SHARESTATUS_0 &&\r\n        <DraggablePopUp\r\n          title={<div style={{fontWeight:'bold'}}>团队共享</div>}\r\n          width={700}\r\n          open={visible}\r\n          destroyOnClose\r\n          maskClosable={false}\r\n          footer={null}\r\n          onCancel={() => setVisible(false)}\r\n        >\r\n            <PageShare teamId={teamId} nodeId={nodeId} setVisible={setVisible}/>\r\n      </DraggablePopUp>}\r\n  </React.Fragment>\r\n}\r\n\r\nexport default React.memo(PowerShare)"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAACC,QAAQ,QAAQ,OAAO;AACjD,SAAQC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAACC,UAAU,EAAEC,KAAK,EAAEC,MAAM,QAAO,MAAM;AAC9E,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,+BAA+B,EAAEC,4BAA4B,EAAEC,wBAAwB,QAAQ,kBAAkB;AAC1H,OAAO,kBAAkB;AACzB,SAASC,MAAM,QAAQ,QAAQ;AAC/B,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,oCAAoC,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnF,MAAMC,aAAa,GAAG,CAAC,CAAC,CAAC;AACzB,MAAMC,aAAa,GAAG,CAAC,CAAC,CAAC;AACzB,MAAMC,aAAa,GAAG,CAAC,CAAC,CAAC;;AAEzB;AACA;AACA;AACA,SAASC,SAASA,CAAC;EAACC,MAAM;EAACC,MAAM;EAACC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,WAAW,GAAGb,cAAc,CAAC,CAAC;EAEpC,MAAM,CAACc,eAAe,EAACC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACzD,MAAM,CAACiC,UAAU,EAACC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC/C,MAAM,CAACmC,WAAW,EAACC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACjD,MAAM,CAACqC,eAAe,EAACC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAE3DD,SAAS,CAAC,MAAI;IACZwC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAC,EAAE,CAAC;EAEL,SAASA,cAAcA,CAAA,EAAG;IACxB9B,+BAA+B,CAAC;MAACgB;IAAM,CAAC,CAAC,CAACe,IAAI,CAACC,GAAG,IAAI;MACpD,IAAIC,IAAI,GAAG,CAAC,CAAAD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,QAAQ,KAAE,EAAE,EAAEC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAI,CAAC,IAAID,IAAI,CAACE,SAAS,IAAI,CAAC,CAAC;MACzF,IAAGN,GAAG,CAACO,UAAU,IAAI,GAAG,EAAC;QACvBZ,cAAc,CAACM,IAAI,CAAC;MACtB;MACAO,gBAAgB,CAACP,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ;EAEA,SAASO,gBAAgBA,CAACC,UAAU,EAAC;IACnCxC,4BAA4B,CAAC;MAACe,MAAM;MAACC;IAAM,CAAC,CAAC,CAACc,IAAI,CAACC,GAAG,IAAI;MACxD,IAAGA,GAAG,CAACO,UAAU,IAAI,GAAG,EAAC;QACvB,IAAIG,QAAQ,GAAG,CAACV,GAAG,CAACE,QAAQ,IAAE,EAAE,EAAEC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACO,MAAM,CAAC,CAACC,GAAG,CAACR,IAAI,IAAI;UAAA,IAAAS,gBAAA;UACxET,IAAI,CAACU,GAAG,GAAGV,IAAI,CAACO,MAAM;UACtBP,IAAI,CAACW,QAAQ,gBAAGpC,OAAA;YAAKqC,KAAK,EAAE;cAACC,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC;YAAQ,CAAE;YAAAC,QAAA,GAAEC,YAAY,EAAAP,gBAAA,GAACJ,UAAU,CAACY,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACX,MAAM,IAAIP,IAAI,CAACO,MAAM,CAAC,cAAAE,gBAAA,uBAAjDA,gBAAA,CAAmDU,MAAM,EAACnB,IAAI,CAACW,QAAQ,CAAC,EAAEX,IAAI,CAACW,QAAQ;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;UAC9KvB,IAAI,CAACwB,QAAQ,GAAG,KAAK;UACrB,OAAOxB,IAAI;QACb,CAAC,CAAC;QACF,IAAGM,QAAQ,CAACmB,MAAM,GAAGpB,UAAU,CAACoB,MAAM,EAAC;UACrCnB,QAAQ,GAAG,CAACoB,SAAS,CAAC,CAAC,CAAC,CAACC,MAAM,CAACrB,QAAQ,CAAC;QAC3C;QACAnB,kBAAkB,CAAC,CAAC,GAAGmB,QAAQ,CAAC,CAAC;QACjCjB,aAAa,CAAC,CAAC,GAAGiB,QAAQ,CAAC,CAAC;QAC5Bb,kBAAkB,CAACG,GAAG,CAACJ,eAAe,CAAC;QACvC;MACF;MACA,IAAIK,IAAI,GAAG,EAAE;MACbA,IAAI,CAAC+B,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;MACtBvC,kBAAkB,CAAC,CAAC,GAAGU,IAAI,CAAC,CAAC;MAC7BR,aAAa,CAAC,CAAC,GAAGQ,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEA,SAASmB,YAAYA,CAACa,GAAG,EAACC,IAAI,EAAE;IAC9B,oBAAQvD,OAAA,CAACb,MAAM;MAACkD,KAAK,EAAE;QAACmB,WAAW,EAAC;MAAC,CAAE;MAACF,GAAG,EAAEA,GAAI;MAACG,IAAI,eAAEzD,OAAA,CAACJ,YAAY;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAE;MAACU,IAAI,EAAE;IAAG;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC;EACtF;EAEA,SAASG,SAASA,CAAA,EAAG;IACnB,OAAO;MAAChB,GAAG,EAAC3C,MAAM,CAAC,CAAC;MAACwC,MAAM,EAAC,IAAI;MAACI,QAAQ,EAAC,EAAE;MAACa,QAAQ,EAAC;IAAI,CAAC;EAC7D;EAEA,SAASU,cAAcA,CAACC,KAAK,EAACL,IAAI,EAACM,KAAK,EAACC,IAAI,EAAE;IAC7CjD,UAAU,CAACgD,KAAK,CAAC,CAACZ,QAAQ,GAAG,KAAK;IAClCpC,UAAU,CAACgD,KAAK,CAAC,CAAC1B,GAAG,GAAGyB,KAAK;IAC7B/C,UAAU,CAACgD,KAAK,CAAC,CAAC7B,MAAM,GAAG4B,KAAK;IAChC/C,UAAU,CAACgD,KAAK,CAAC,CAACzB,QAAQ,GAAGmB,IAAI;IACjC,IAAIjC,IAAI,GAAG,EAAE;IACb,IAAGwC,IAAI,IAAI,CAAC,EAAC;MACXxC,IAAI,GAAG,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAACC,MAAM,CAACvC,UAAU,CAAC;IACzC,CAAC,MAAI;MACHS,IAAI,GAAGT,UAAU;IACnB;IACAD,kBAAkB,CAAC,CAAC,GAAGU,IAAI,CAAC,CAAC;IAC7BR,aAAa,CAAC,CAAC,GAAGQ,IAAI,CAAC,CAAC;EAC1B;EAEA,SAASyC,SAASA,CAAC/B,MAAM,EAAE;IACzB,IAAIV,IAAI,GAAG,EAAE;IACb,IAAI0C,UAAU,GAAGnD,UAAU,CAACW,MAAM,CAACyC,IAAI,IAAIA,IAAI,CAACjC,MAAM,IAAIA,MAAM,CAAC;IACjE,IAAGnB,UAAU,CAAC6B,IAAI,CAACuB,IAAI,IAAIA,IAAI,CAAChB,QAAQ,CAAC,EAAC;MACxC3B,IAAI,GAAG0C,UAAU;IACnB,CAAC,MAAI;MACH1C,IAAI,GAAG,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAACC,MAAM,CAACY,UAAU,CAAC;IACzC;IACAlD,aAAa,CAACQ,IAAI,CAAC;IACnBV,kBAAkB,CAACU,IAAI,CAAC;EAC1B;EAEA,MAAM4C,OAAO,GAAG,CACd;IACEC,KAAK,eAAEnE,OAAA;MAAKqC,KAAK,EAAE;QAACC,OAAO,EAAC,MAAM;QAAC8B,cAAc,EAAC,QAAQ;QAAC7B,UAAU,EAAC;MAAQ,CAAE;MAAAC,QAAA,EAAC;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IACzFqB,SAAS,EAAE,UAAU;IACrBlC,GAAG,EAAE,UAAU;IACfmC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACX,KAAK,EAAEY,GAAG,EAAEC,QAAQ,KAC7BD,GAAG,CAACvB,QAAQ,gBACZjD,OAAA,CAAChB,MAAM;MACN0F,UAAU;MACTC,QAAQ,EAAE,KAAM;MAChBtC,KAAK,EAAE;QAACiC,KAAK,EAAC,GAAG;QAAC/B,UAAU,EAAC;MAAQ,CAAE;MACvCqC,WAAW,EAAC,oBAAK;MACjBhB,KAAK,EAAEA,KAAK,GAAGA,KAAK,GAAG;MACvB;MAAA;MACAiB,gBAAgB,EAAC,UAAU;MAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM;QAAA,IAAAC,WAAA;QAAA,OAAK,EAAAA,WAAA,GAACD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE7C,GAAG,cAAA8C,WAAA,cAAAA,WAAA,GAAI,EAAE,EAAEC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC;MAAA,CAAC;MACjGE,wBAAwB,EAAE,KAAM;MAChCC,UAAU,eAAErF,OAAA,CAACP,iBAAiB;QAAC4C,KAAK,EAAE;UAACiD,aAAa,EAAC;QAAM;MAAE;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAE;MAChEuC,QAAQ,EAAEA,CAAC3B,KAAK,EAAC4B,IAAI,KACnB7B,cAAc,CAACC,KAAK,EAAC4B,IAAI,CAACC,KAAK,EAAChB,QAAQ,EAC9B1D,WAAW,CAACS,MAAM,CAACC,IAAI,IAAI,CAACZ,UAAU,CAAC6B,IAAI,CAACuB,IAAI,IAAIA,IAAI,CAACjC,MAAM,IAAIP,IAAI,CAACO,MAAM,CAAC,CAAC,CAACkB,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAC7G,CACD;MACDwC,OAAO,EAAE3E,WAAW,CAACS,MAAM,CAACC,IAAI,IAAI,CAACZ,UAAU,CAAC6B,IAAI,CAACuB,IAAI,IAAIA,IAAI,CAACjC,MAAM,IAAIP,IAAI,CAACO,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAACR,IAAI,EAACkE,SAAS,MAAM;QACjH/B,KAAK,EAAEnC,IAAI,CAACO,MAAM;QAClByD,KAAK,eAAEzF,OAAA;UAAKqC,KAAK,EAAE;YAACC,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC;UAAQ,CAAE;UAAAC,QAAA,GAAEC,YAAY,CAAChB,IAAI,CAACmB,MAAM,EAACnB,IAAI,CAACW,QAAQ,CAAC,EAAEX,IAAI,CAACW,QAAQ;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;QACvHb,GAAG,EAAEV,IAAI,CAACW;MACZ,CAAC,CAAC;IAAE;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEFhD,OAAA;MAAAwC,QAAA,EAAMoB;IAAK;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACnB,CAAC,EACD;IACImB,KAAK,eAAEnE,OAAA;MAAKqC,KAAK,EAAE;QAACC,OAAO,EAAC,MAAM;QAAC8B,cAAc,EAAC,QAAQ;QAAC7B,UAAU,EAAC;MAAQ,CAAE;MAAAC,QAAA,EAAC;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IACzFqB,SAAS,EAAE,MAAM;IACjBlC,GAAG,EAAE,MAAM;IACXmC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACX,KAAK,EAAEY,GAAG,EAAEC,QAAQ,KAC7BD,GAAG,CAACxC,MAAM,iBACVhC,OAAA;MAAKqC,KAAK,EAAE;QAACC,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAAC6B,cAAc,EAAC;MAAQ,CAAE;MAAA5B,QAAA,gBACvExC,OAAA,CAAClB,QAAQ;QAAC8G,QAAQ;QAACC,SAAS,EAAC;MAA+C;QAAAhD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC9EhD,OAAA;QAAMqC,KAAK,EAAE;UAACyD,UAAU,EAAC;QAAE,CAAE;QAAAtD,QAAA,EAAC;MAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC;EACT,CAAC,EACD;IACEmB,KAAK,eAAEnE,OAAA;MAAKqC,KAAK,EAAE;QAACC,OAAO,EAAC,MAAM;QAAC8B,cAAc,EAAC,QAAQ;QAAC7B,UAAU,EAAC;MAAQ,CAAE;MAAAC,QAAA,EAAC;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IACzFqB,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAE,KAAK;IACZnC,GAAG,EAAE,WAAW;IAChBoC,MAAM,EAAEA,CAACX,KAAK,EAAEY,GAAG,KACnBA,GAAG,CAACxC,MAAM,iBACVhC,OAAA;MAAKqC,KAAK,EAAE;QAACC,OAAO,EAAC,MAAM;QAAC8B,cAAc,EAAC,QAAQ;QAAC7B,UAAU,EAAC;MAAQ,CAAE;MAAAC,QAAA,eACvExC,OAAA,CAACf,UAAU;QAACkF,KAAK,EAAC,iCAAQ;QAAC4B,MAAM,EAAE,IAAK;QAACC,UAAU,EAAE,IAAK;QAACC,SAAS,EAAEA,CAAA,KAAMlC,SAAS,CAACS,GAAG,CAACxC,MAAM,CAAE;QAAAQ,QAAA,eAChGxC,OAAA,CAACnB,MAAM;UAACgH,SAAS,EAAC,aAAa;UAACK,IAAI,EAAC,MAAM;UAACxC,IAAI,EAAC,OAAO;UAACrB,KAAK,EAAE;YAAE8D,YAAY,EAAE;UAAE,CAAE;UAAA3D,QAAA,EAAC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EACP,CAAC,CACF;EAED,SAASoD,cAAcA,CAACC,CAAC,EAAE;IACzB,IAAIC,QAAQ,GAAGD,CAAC,CAACE,MAAM,CAAC3C,KAAK;IAC7B,IAAI7B,QAAQ,GAAGpB,eAAe,CAACa,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACW,QAAQ,IAAI,EAAE,IAAKX,IAAI,CAACW,QAAQ,IAAI,EAAE,IAAIX,IAAI,CAACW,QAAQ,CAACoE,KAAK,CAAChE,QAAQ,CAAC,CAAC,CAAC,CAAC0C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACmB,QAAQ,CAACpB,WAAW,CAAC,CAAC,CAAE,CAAC;IAC7KpE,aAAa,CAAC,CAAC,GAAGiB,QAAQ,CAAC,CAAC;EAC9B;;EAEA;EACA,SAAS0E,UAAUA,CAAA,EAAG;IACpB,CAAC,CAACxF,eAAe,IAAIR,QAAQ,CAAC,SAASJ,MAAM,aAAaY,eAAe,EAAE,CAAC;EAC9E;EAEA,SAASyF,IAAIA,CAAA,EAAG;IACd,IAAIC,MAAM,GAAG;MACXtG,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA,MAAM;MACdiB,QAAQ,EAAEV,UAAU,CAACW,MAAM,CAACyC,IAAI,IAAI,CAACA,IAAI,CAAChB,QAAQ,CAAC,CAAChB,GAAG,CAACgC,IAAI,KAAK;QACrD2C,WAAW,EAAE3C,IAAI,CAACjC;MACpB,CAAC,CAAC;IACd,CAAC;IACDzC,wBAAwB,CAACoH,MAAM,CAAC,CAACvF,IAAI,CAACC,GAAG,IAAI;MAC3C,IAAGA,GAAG,CAACO,UAAU,IAAI,GAAG,EAAC;QACvBjC,UAAU,CAACkH,OAAO,CAAC,QAAQ,CAAC;QAC5BnG,WAAW,CAACoG,iBAAiB,CAAC,4CAA4C,CAAC;QAC3EvG,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACJ;EAEA,oBACEP,OAAA,CAACtB,KAAK,CAACqI,QAAQ;IAAAvE,QAAA,gBACbxC,OAAA;MAAKqC,KAAK,EAAE;QAACC,OAAO,EAAC,MAAM;QAACC,UAAU,EAAE,QAAQ;QAACyE,YAAY,EAAC,EAAE;QAACC,SAAS,EAAC,CAAC;MAAE,CAAE;MAAAzE,QAAA,gBAC9ExC,OAAA;QAAMqC,KAAK,EAAE;UAAC6E,KAAK,EAAC,MAAM;UAAC1D,WAAW,EAAC;QAAE,CAAE;QAAAhB,QAAA,EAAC;MAAkB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrEhD,OAAA;QAAGmH,OAAO,EAAEV,UAAW;QAAAjE,QAAA,gBAErBxC,OAAA;UAAM6F,SAAS,EAAC;QAAgC;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,0BACnD,eAAAhD,OAAA;UAAM6F,SAAS,EAAC;QAAgC;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BACrD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACNhD,OAAA,CAACd,KAAK;MAAC0F,WAAW,EAAC,4CAAS;MACrBwC,YAAY,EAAC,KAAK;MAClB/E,KAAK,EAAE;QAAC8D,YAAY,EAAC,CAAC;QAAC7B,KAAK,EAAC,GAAG;QAAC0C,YAAY,EAAC;MAAE,CAAE;MAClDzB,QAAQ,EAAEa;IAAe;MAAAvD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eAClChD,OAAA,CAACjB,KAAK;MACJ4F,QAAQ;MACRjB,IAAI,EAAC,OAAO;MACZQ,OAAO,EAAEA,OAAQ;MACjBrD,UAAU,EAAEA,UAAW;MACvBwG,UAAU,EAAE,KAAM;MAClBC,MAAM,EAAEzG,UAAU,CAACqC,MAAM,GAAG,CAAC,IAAI;QAAEqE,CAAC,EAAE;MAAI;IAAE;MAAA1E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eAChDhD,OAAA;MAAKqC,KAAK,EAAE;QAAC6E,KAAK,EAAC,MAAM;QAACD,SAAS,EAAC;MAAE,CAAE;MAAAzE,QAAA,GAAC,QAAC,EAAC3B,UAAU,CAACW,MAAM,CAACyC,IAAI,IAAI,CAACA,IAAI,CAAChB,QAAQ,CAAC,CAACC,MAAM,EAAC,QAAC;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACnGhD,OAAA;MAAKqC,KAAK,EAAE;QAAC6E,KAAK,EAAC,MAAM;QAACD,SAAS,EAAC;MAAE,CAAE;MAAAzE,QAAA,gBACtCxC,OAAA;QAAAwC,QAAA,EAAK;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACdhD,OAAA;QAAAwC,QAAA,EAAK;MAA2B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtChD,OAAA;QAAAwC,QAAA,EAAK;MAAkB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7BhD,OAAA;QAAAwC,QAAA,GAAK,gKAA4B,eAACxC,OAAA;UAAM6F,SAAS,EAAC,gBAAgB;UAACxD,KAAK,EAAE;YAAC6E,KAAK,EAAC;UAAS;QAAE;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,EAAC,2BAAK;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CAAC,eACNhD,OAAA;MAAKqC,KAAK,EAAE;QAACC,OAAO,EAAC,MAAM;QAAC8B,cAAc,EAAC,QAAQ;QAAC7B,UAAU,EAAC,QAAQ;QAAC0E,SAAS,EAAC;MAAE,CAAE;MAAAzE,QAAA,gBACpFxC,OAAA,CAACnB,MAAM;QAACqH,IAAI,EAAC,SAAS;QAACxC,IAAI,EAAC,QAAQ;QAACrB,KAAK,EAAE;UAAC8D,YAAY,EAAC,CAAC;UAAC3C,WAAW,EAAC;QAAE,CAAE;QAAC2D,OAAO,EAAEA,CAAA,KAAI5G,UAAU,CAAC,KAAK,CAAE;QAAAiC,QAAA,EAAC;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxHhD,OAAA,CAACnB,MAAM;QAACqH,IAAI,EAAC,SAAS;QAACxC,IAAI,EAAC,QAAQ;QAACrB,KAAK,EAAE;UAAC8D,YAAY,EAAC;QAAC,CAAE;QAACgB,OAAO,EAAEA,CAAA,KAAIT,IAAI,CAAC,CAAE;QAAAlE,QAAA,EAAC;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAErB;AAACxC,EAAA,CA5MQJ,SAAS;EAAA,QACCV,WAAW,EACRG,cAAc;AAAA;AAAA2H,EAAA,GAF3BpH,SAAS;AA8MlB,SAASqH,UAAUA,CAAC;EAACpH,MAAM;EAAEC,MAAM;EAAEoH;AAAO,CAAC,EAAE;EAAAC,GAAA;EAC7C,MAAM,CAACC,WAAW,EAACC,cAAc,CAAC,GAAGjJ,QAAQ,CAACqB,aAAa,CAAC;EAC5D,MAAM,CAAC6H,OAAO,EAAEvH,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAACqF,IAAI,EAAE8D,MAAM,GAAG;MAACC,UAAU,EAAE,CAAC;MAAEJ,WAAW,EAAE;IAAC;EAAC,CAAC,GAAG9H,oCAAoC,CAACO,MAAM,EAACC,MAAM,CAAC;EAE5G3B,SAAS,CAAC,MAAM;IACdkJ,cAAc,CAACE,MAAM,CAACH,WAAW,CAAC;EACpC,CAAC,EAAC,CAACG,MAAM,CAAC,CAAC;EAEX,oBAAO/H,OAAA,CAACtB,KAAK,CAACqI,QAAQ;IAAAvE,QAAA,GAClBlC,MAAM,IAAIsH,WAAW,IAAI3H,aAAa,KACjCyH,OAAO,gBACV1H,OAAA;MAAGmH,OAAO,EAAEA,CAAA,KAAM5G,UAAU,CAAC,IAAI,CAAE;MAAAiC,QAAA,EAAEkF,OAAO,IAAI,GAAG,GAAG,EAAE,GAAGA;IAAO;MAAA7E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,gBAEvEhD,OAAA,CAACnB,MAAM;MACLqH,IAAI,EAAC,MAAM;MACX7D,KAAK,EAAE;QAACyD,UAAU,EAAE;MAAK,CAAE;MAC3BD,SAAS,EAAC,kBAAkB;MAC5B1B,KAAK,EAAC,0BAAM;MACZgD,OAAO,EAAEA,CAAA,KAAM5G,UAAU,CAAC,IAAI,CAAE;MAChCkD,IAAI,eACFzD,OAAA;QACE6F,SAAS,EAAC,oBAAoB;QAC9BxD,KAAK,EAAElC,aAAa,IAAIyH,WAAW,GAAG;UAACV,KAAK,EAAC;QAAS,CAAC,GAAG,CAAC;MAAE;QAAArE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,CACV,EACA1C,MAAM,IAAIsH,WAAW,IAAI3H,aAAa,iBACrCD,OAAA,CAACZ,cAAc;MACb+E,KAAK,eAAEnE,OAAA;QAAKqC,KAAK,EAAE;UAAC4F,UAAU,EAAC;QAAM,CAAE;QAAAzF,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MACnDsB,KAAK,EAAE,GAAI;MACX4D,IAAI,EAAEJ,OAAQ;MACdK,cAAc;MACdC,YAAY,EAAE,KAAM;MACpBC,MAAM,EAAE,IAAK;MACbC,QAAQ,EAAEA,CAAA,KAAM/H,UAAU,CAAC,KAAK,CAAE;MAAAiC,QAAA,eAEhCxC,OAAA,CAACI,SAAS;QAACC,MAAM,EAAEA,MAAO;QAACC,MAAM,EAAEA,MAAO;QAACC,UAAU,EAAEA;MAAW;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AACnB;AAAC2E,GAAA,CAxCQF,UAAU;EAAA,QAGwC3H,oCAAoC;AAAA;AAAAyI,GAAA,GAHtFd,UAAU;AA0CnB,eAAAe,GAAA,gBAAe9J,KAAK,CAAC+J,IAAI,CAAChB,UAAU,CAAC;AAAA,IAAAD,EAAA,EAAAe,GAAA,EAAAC,GAAA;AAAAE,YAAA,CAAAlB,EAAA;AAAAkB,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}