{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LcsDiff } from '../../../base/common/diff/diff.js';\nimport * as strings from '../../../base/common/strings.js';\nconst MINIMUM_MATCHING_CHARACTER_LENGTH = 3;\nfunction computeDiff(originalSequence, modifiedSequence, continueProcessingPredicate, pretty) {\n  const diffAlgo = new LcsDiff(originalSequence, modifiedSequence, continueProcessingPredicate);\n  return diffAlgo.ComputeDiff(pretty);\n}\nclass LineSequence {\n  constructor(lines) {\n    const startColumns = [];\n    const endColumns = [];\n    for (let i = 0, length = lines.length; i < length; i++) {\n      startColumns[i] = getFirstNonBlankColumn(lines[i], 1);\n      endColumns[i] = getLastNonBlankColumn(lines[i], 1);\n    }\n    this.lines = lines;\n    this._startColumns = startColumns;\n    this._endColumns = endColumns;\n  }\n  getElements() {\n    const elements = [];\n    for (let i = 0, len = this.lines.length; i < len; i++) {\n      elements[i] = this.lines[i].substring(this._startColumns[i] - 1, this._endColumns[i] - 1);\n    }\n    return elements;\n  }\n  getStrictElement(index) {\n    return this.lines[index];\n  }\n  getStartLineNumber(i) {\n    return i + 1;\n  }\n  getEndLineNumber(i) {\n    return i + 1;\n  }\n  createCharSequence(shouldIgnoreTrimWhitespace, startIndex, endIndex) {\n    const charCodes = [];\n    const lineNumbers = [];\n    const columns = [];\n    let len = 0;\n    for (let index = startIndex; index <= endIndex; index++) {\n      const lineContent = this.lines[index];\n      const startColumn = shouldIgnoreTrimWhitespace ? this._startColumns[index] : 1;\n      const endColumn = shouldIgnoreTrimWhitespace ? this._endColumns[index] : lineContent.length + 1;\n      for (let col = startColumn; col < endColumn; col++) {\n        charCodes[len] = lineContent.charCodeAt(col - 1);\n        lineNumbers[len] = index + 1;\n        columns[len] = col;\n        len++;\n      }\n      if (!shouldIgnoreTrimWhitespace && index < endIndex) {\n        // Add \\n if trim whitespace is not ignored\n        charCodes[len] = 10 /* CharCode.LineFeed */;\n        lineNumbers[len] = index + 1;\n        columns[len] = lineContent.length + 1;\n        len++;\n      }\n    }\n    return new CharSequence(charCodes, lineNumbers, columns);\n  }\n}\nclass CharSequence {\n  constructor(charCodes, lineNumbers, columns) {\n    this._charCodes = charCodes;\n    this._lineNumbers = lineNumbers;\n    this._columns = columns;\n  }\n  toString() {\n    return '[' + this._charCodes.map((s, idx) => (s === 10 /* CharCode.LineFeed */ ? '\\\\n' : String.fromCharCode(s)) + `-(${this._lineNumbers[idx]},${this._columns[idx]})`).join(', ') + ']';\n  }\n  _assertIndex(index, arr) {\n    if (index < 0 || index >= arr.length) {\n      throw new Error(`Illegal index`);\n    }\n  }\n  getElements() {\n    return this._charCodes;\n  }\n  getStartLineNumber(i) {\n    if (i > 0 && i === this._lineNumbers.length) {\n      // the start line number of the element after the last element\n      // is the end line number of the last element\n      return this.getEndLineNumber(i - 1);\n    }\n    this._assertIndex(i, this._lineNumbers);\n    return this._lineNumbers[i];\n  }\n  getEndLineNumber(i) {\n    if (i === -1) {\n      // the end line number of the element before the first element\n      // is the start line number of the first element\n      return this.getStartLineNumber(i + 1);\n    }\n    this._assertIndex(i, this._lineNumbers);\n    if (this._charCodes[i] === 10 /* CharCode.LineFeed */) {\n      return this._lineNumbers[i] + 1;\n    }\n    return this._lineNumbers[i];\n  }\n  getStartColumn(i) {\n    if (i > 0 && i === this._columns.length) {\n      // the start column of the element after the last element\n      // is the end column of the last element\n      return this.getEndColumn(i - 1);\n    }\n    this._assertIndex(i, this._columns);\n    return this._columns[i];\n  }\n  getEndColumn(i) {\n    if (i === -1) {\n      // the end column of the element before the first element\n      // is the start column of the first element\n      return this.getStartColumn(i + 1);\n    }\n    this._assertIndex(i, this._columns);\n    if (this._charCodes[i] === 10 /* CharCode.LineFeed */) {\n      return 1;\n    }\n    return this._columns[i] + 1;\n  }\n}\nclass CharChange {\n  constructor(originalStartLineNumber, originalStartColumn, originalEndLineNumber, originalEndColumn, modifiedStartLineNumber, modifiedStartColumn, modifiedEndLineNumber, modifiedEndColumn) {\n    this.originalStartLineNumber = originalStartLineNumber;\n    this.originalStartColumn = originalStartColumn;\n    this.originalEndLineNumber = originalEndLineNumber;\n    this.originalEndColumn = originalEndColumn;\n    this.modifiedStartLineNumber = modifiedStartLineNumber;\n    this.modifiedStartColumn = modifiedStartColumn;\n    this.modifiedEndLineNumber = modifiedEndLineNumber;\n    this.modifiedEndColumn = modifiedEndColumn;\n  }\n  static createFromDiffChange(diffChange, originalCharSequence, modifiedCharSequence) {\n    const originalStartLineNumber = originalCharSequence.getStartLineNumber(diffChange.originalStart);\n    const originalStartColumn = originalCharSequence.getStartColumn(diffChange.originalStart);\n    const originalEndLineNumber = originalCharSequence.getEndLineNumber(diffChange.originalStart + diffChange.originalLength - 1);\n    const originalEndColumn = originalCharSequence.getEndColumn(diffChange.originalStart + diffChange.originalLength - 1);\n    const modifiedStartLineNumber = modifiedCharSequence.getStartLineNumber(diffChange.modifiedStart);\n    const modifiedStartColumn = modifiedCharSequence.getStartColumn(diffChange.modifiedStart);\n    const modifiedEndLineNumber = modifiedCharSequence.getEndLineNumber(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n    const modifiedEndColumn = modifiedCharSequence.getEndColumn(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n    return new CharChange(originalStartLineNumber, originalStartColumn, originalEndLineNumber, originalEndColumn, modifiedStartLineNumber, modifiedStartColumn, modifiedEndLineNumber, modifiedEndColumn);\n  }\n}\nfunction postProcessCharChanges(rawChanges) {\n  if (rawChanges.length <= 1) {\n    return rawChanges;\n  }\n  const result = [rawChanges[0]];\n  let prevChange = result[0];\n  for (let i = 1, len = rawChanges.length; i < len; i++) {\n    const currChange = rawChanges[i];\n    const originalMatchingLength = currChange.originalStart - (prevChange.originalStart + prevChange.originalLength);\n    const modifiedMatchingLength = currChange.modifiedStart - (prevChange.modifiedStart + prevChange.modifiedLength);\n    // Both of the above should be equal, but the continueProcessingPredicate may prevent this from being true\n    const matchingLength = Math.min(originalMatchingLength, modifiedMatchingLength);\n    if (matchingLength < MINIMUM_MATCHING_CHARACTER_LENGTH) {\n      // Merge the current change into the previous one\n      prevChange.originalLength = currChange.originalStart + currChange.originalLength - prevChange.originalStart;\n      prevChange.modifiedLength = currChange.modifiedStart + currChange.modifiedLength - prevChange.modifiedStart;\n    } else {\n      // Add the current change\n      result.push(currChange);\n      prevChange = currChange;\n    }\n  }\n  return result;\n}\nclass LineChange {\n  constructor(originalStartLineNumber, originalEndLineNumber, modifiedStartLineNumber, modifiedEndLineNumber, charChanges) {\n    this.originalStartLineNumber = originalStartLineNumber;\n    this.originalEndLineNumber = originalEndLineNumber;\n    this.modifiedStartLineNumber = modifiedStartLineNumber;\n    this.modifiedEndLineNumber = modifiedEndLineNumber;\n    this.charChanges = charChanges;\n  }\n  static createFromDiffResult(shouldIgnoreTrimWhitespace, diffChange, originalLineSequence, modifiedLineSequence, continueCharDiff, shouldComputeCharChanges, shouldPostProcessCharChanges) {\n    let originalStartLineNumber;\n    let originalEndLineNumber;\n    let modifiedStartLineNumber;\n    let modifiedEndLineNumber;\n    let charChanges = undefined;\n    if (diffChange.originalLength === 0) {\n      originalStartLineNumber = originalLineSequence.getStartLineNumber(diffChange.originalStart) - 1;\n      originalEndLineNumber = 0;\n    } else {\n      originalStartLineNumber = originalLineSequence.getStartLineNumber(diffChange.originalStart);\n      originalEndLineNumber = originalLineSequence.getEndLineNumber(diffChange.originalStart + diffChange.originalLength - 1);\n    }\n    if (diffChange.modifiedLength === 0) {\n      modifiedStartLineNumber = modifiedLineSequence.getStartLineNumber(diffChange.modifiedStart) - 1;\n      modifiedEndLineNumber = 0;\n    } else {\n      modifiedStartLineNumber = modifiedLineSequence.getStartLineNumber(diffChange.modifiedStart);\n      modifiedEndLineNumber = modifiedLineSequence.getEndLineNumber(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n    }\n    if (shouldComputeCharChanges && diffChange.originalLength > 0 && diffChange.originalLength < 20 && diffChange.modifiedLength > 0 && diffChange.modifiedLength < 20 && continueCharDiff()) {\n      // Compute character changes for diff chunks of at most 20 lines...\n      const originalCharSequence = originalLineSequence.createCharSequence(shouldIgnoreTrimWhitespace, diffChange.originalStart, diffChange.originalStart + diffChange.originalLength - 1);\n      const modifiedCharSequence = modifiedLineSequence.createCharSequence(shouldIgnoreTrimWhitespace, diffChange.modifiedStart, diffChange.modifiedStart + diffChange.modifiedLength - 1);\n      if (originalCharSequence.getElements().length > 0 && modifiedCharSequence.getElements().length > 0) {\n        let rawChanges = computeDiff(originalCharSequence, modifiedCharSequence, continueCharDiff, true).changes;\n        if (shouldPostProcessCharChanges) {\n          rawChanges = postProcessCharChanges(rawChanges);\n        }\n        charChanges = [];\n        for (let i = 0, length = rawChanges.length; i < length; i++) {\n          charChanges.push(CharChange.createFromDiffChange(rawChanges[i], originalCharSequence, modifiedCharSequence));\n        }\n      }\n    }\n    return new LineChange(originalStartLineNumber, originalEndLineNumber, modifiedStartLineNumber, modifiedEndLineNumber, charChanges);\n  }\n}\nexport class DiffComputer {\n  constructor(originalLines, modifiedLines, opts) {\n    this.shouldComputeCharChanges = opts.shouldComputeCharChanges;\n    this.shouldPostProcessCharChanges = opts.shouldPostProcessCharChanges;\n    this.shouldIgnoreTrimWhitespace = opts.shouldIgnoreTrimWhitespace;\n    this.shouldMakePrettyDiff = opts.shouldMakePrettyDiff;\n    this.originalLines = originalLines;\n    this.modifiedLines = modifiedLines;\n    this.original = new LineSequence(originalLines);\n    this.modified = new LineSequence(modifiedLines);\n    this.continueLineDiff = createContinueProcessingPredicate(opts.maxComputationTime);\n    this.continueCharDiff = createContinueProcessingPredicate(opts.maxComputationTime === 0 ? 0 : Math.min(opts.maxComputationTime, 5000)); // never run after 5s for character changes...\n  }\n  computeDiff() {\n    if (this.original.lines.length === 1 && this.original.lines[0].length === 0) {\n      // empty original => fast path\n      if (this.modified.lines.length === 1 && this.modified.lines[0].length === 0) {\n        return {\n          quitEarly: false,\n          changes: []\n        };\n      }\n      return {\n        quitEarly: false,\n        changes: [{\n          originalStartLineNumber: 1,\n          originalEndLineNumber: 1,\n          modifiedStartLineNumber: 1,\n          modifiedEndLineNumber: this.modified.lines.length,\n          charChanges: [{\n            modifiedEndColumn: 0,\n            modifiedEndLineNumber: 0,\n            modifiedStartColumn: 0,\n            modifiedStartLineNumber: 0,\n            originalEndColumn: 0,\n            originalEndLineNumber: 0,\n            originalStartColumn: 0,\n            originalStartLineNumber: 0\n          }]\n        }]\n      };\n    }\n    if (this.modified.lines.length === 1 && this.modified.lines[0].length === 0) {\n      // empty modified => fast path\n      return {\n        quitEarly: false,\n        changes: [{\n          originalStartLineNumber: 1,\n          originalEndLineNumber: this.original.lines.length,\n          modifiedStartLineNumber: 1,\n          modifiedEndLineNumber: 1,\n          charChanges: [{\n            modifiedEndColumn: 0,\n            modifiedEndLineNumber: 0,\n            modifiedStartColumn: 0,\n            modifiedStartLineNumber: 0,\n            originalEndColumn: 0,\n            originalEndLineNumber: 0,\n            originalStartColumn: 0,\n            originalStartLineNumber: 0\n          }]\n        }]\n      };\n    }\n    const diffResult = computeDiff(this.original, this.modified, this.continueLineDiff, this.shouldMakePrettyDiff);\n    const rawChanges = diffResult.changes;\n    const quitEarly = diffResult.quitEarly;\n    // The diff is always computed with ignoring trim whitespace\n    // This ensures we get the prettiest diff\n    if (this.shouldIgnoreTrimWhitespace) {\n      const lineChanges = [];\n      for (let i = 0, length = rawChanges.length; i < length; i++) {\n        lineChanges.push(LineChange.createFromDiffResult(this.shouldIgnoreTrimWhitespace, rawChanges[i], this.original, this.modified, this.continueCharDiff, this.shouldComputeCharChanges, this.shouldPostProcessCharChanges));\n      }\n      return {\n        quitEarly: quitEarly,\n        changes: lineChanges\n      };\n    }\n    // Need to post-process and introduce changes where the trim whitespace is different\n    // Note that we are looping starting at -1 to also cover the lines before the first change\n    const result = [];\n    let originalLineIndex = 0;\n    let modifiedLineIndex = 0;\n    for (let i = -1 /* !!!! */, len = rawChanges.length; i < len; i++) {\n      const nextChange = i + 1 < len ? rawChanges[i + 1] : null;\n      const originalStop = nextChange ? nextChange.originalStart : this.originalLines.length;\n      const modifiedStop = nextChange ? nextChange.modifiedStart : this.modifiedLines.length;\n      while (originalLineIndex < originalStop && modifiedLineIndex < modifiedStop) {\n        const originalLine = this.originalLines[originalLineIndex];\n        const modifiedLine = this.modifiedLines[modifiedLineIndex];\n        if (originalLine !== modifiedLine) {\n          // These lines differ only in trim whitespace\n          // Check the leading whitespace\n          {\n            let originalStartColumn = getFirstNonBlankColumn(originalLine, 1);\n            let modifiedStartColumn = getFirstNonBlankColumn(modifiedLine, 1);\n            while (originalStartColumn > 1 && modifiedStartColumn > 1) {\n              const originalChar = originalLine.charCodeAt(originalStartColumn - 2);\n              const modifiedChar = modifiedLine.charCodeAt(modifiedStartColumn - 2);\n              if (originalChar !== modifiedChar) {\n                break;\n              }\n              originalStartColumn--;\n              modifiedStartColumn--;\n            }\n            if (originalStartColumn > 1 || modifiedStartColumn > 1) {\n              this._pushTrimWhitespaceCharChange(result, originalLineIndex + 1, 1, originalStartColumn, modifiedLineIndex + 1, 1, modifiedStartColumn);\n            }\n          }\n          // Check the trailing whitespace\n          {\n            let originalEndColumn = getLastNonBlankColumn(originalLine, 1);\n            let modifiedEndColumn = getLastNonBlankColumn(modifiedLine, 1);\n            const originalMaxColumn = originalLine.length + 1;\n            const modifiedMaxColumn = modifiedLine.length + 1;\n            while (originalEndColumn < originalMaxColumn && modifiedEndColumn < modifiedMaxColumn) {\n              const originalChar = originalLine.charCodeAt(originalEndColumn - 1);\n              const modifiedChar = originalLine.charCodeAt(modifiedEndColumn - 1);\n              if (originalChar !== modifiedChar) {\n                break;\n              }\n              originalEndColumn++;\n              modifiedEndColumn++;\n            }\n            if (originalEndColumn < originalMaxColumn || modifiedEndColumn < modifiedMaxColumn) {\n              this._pushTrimWhitespaceCharChange(result, originalLineIndex + 1, originalEndColumn, originalMaxColumn, modifiedLineIndex + 1, modifiedEndColumn, modifiedMaxColumn);\n            }\n          }\n        }\n        originalLineIndex++;\n        modifiedLineIndex++;\n      }\n      if (nextChange) {\n        // Emit the actual change\n        result.push(LineChange.createFromDiffResult(this.shouldIgnoreTrimWhitespace, nextChange, this.original, this.modified, this.continueCharDiff, this.shouldComputeCharChanges, this.shouldPostProcessCharChanges));\n        originalLineIndex += nextChange.originalLength;\n        modifiedLineIndex += nextChange.modifiedLength;\n      }\n    }\n    return {\n      quitEarly: quitEarly,\n      changes: result\n    };\n  }\n  _pushTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn) {\n    if (this._mergeTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn)) {\n      // Merged into previous\n      return;\n    }\n    let charChanges = undefined;\n    if (this.shouldComputeCharChanges) {\n      charChanges = [new CharChange(originalLineNumber, originalStartColumn, originalLineNumber, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedLineNumber, modifiedEndColumn)];\n    }\n    result.push(new LineChange(originalLineNumber, originalLineNumber, modifiedLineNumber, modifiedLineNumber, charChanges));\n  }\n  _mergeTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn) {\n    const len = result.length;\n    if (len === 0) {\n      return false;\n    }\n    const prevChange = result[len - 1];\n    if (prevChange.originalEndLineNumber === 0 || prevChange.modifiedEndLineNumber === 0) {\n      // Don't merge with inserts/deletes\n      return false;\n    }\n    if (prevChange.originalEndLineNumber + 1 === originalLineNumber && prevChange.modifiedEndLineNumber + 1 === modifiedLineNumber) {\n      prevChange.originalEndLineNumber = originalLineNumber;\n      prevChange.modifiedEndLineNumber = modifiedLineNumber;\n      if (this.shouldComputeCharChanges && prevChange.charChanges) {\n        prevChange.charChanges.push(new CharChange(originalLineNumber, originalStartColumn, originalLineNumber, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedLineNumber, modifiedEndColumn));\n      }\n      return true;\n    }\n    return false;\n  }\n}\nfunction getFirstNonBlankColumn(txt, defaultValue) {\n  const r = strings.firstNonWhitespaceIndex(txt);\n  if (r === -1) {\n    return defaultValue;\n  }\n  return r + 1;\n}\nfunction getLastNonBlankColumn(txt, defaultValue) {\n  const r = strings.lastNonWhitespaceIndex(txt);\n  if (r === -1) {\n    return defaultValue;\n  }\n  return r + 2;\n}\nfunction createContinueProcessingPredicate(maximumRuntime) {\n  if (maximumRuntime === 0) {\n    return () => true;\n  }\n  const startTime = Date.now();\n  return () => {\n    return Date.now() - startTime < maximumRuntime;\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}