{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\quickAcess\\\\views\\\\DataPush\\\\EditDrawer\\\\OpDataPush.jsx\",\n  _s = $RefreshSig$();\n/*\r\n * @Author: Walt <EMAIL>\r\n * @Date: 2025-01-21 11:34:18\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2025-08-05 14:10:24\r\n * @Description: 新建\"事件推送\"\r\n */\nimport { ExclamationCircleOutlined } from \"@ant-design/icons\";\nimport { Button, Input, Drawer, Form, Checkbox, Avatar, Select, Space, Dropdown, Cascader, Modal } from \"antd\";\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport React, { useEffect, useRef, useState, useMemo } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport '../DataPush.scss';\nimport * as httpQuickAccess from \"@common/api/http\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { eNodeTypeId, eNodeType } from \"@common/utils/TsbConfig\";\nimport { NoAvatarIcon } from '@common/components/IconUtil';\nimport TextModelModal from './TextModel/TextModelModal';\nimport PlanModal from './Plan/PlanModal';\nimport SubscribeContent from './SubscribeContent/SubscribeContent';\nimport { ePriorityTypeObj, ePriorityType, eMsgChannel, eMsgChannelObj, eTriggerTime, eTriggerTimeObj, eExecTimeType, eEventParentType } from \"src/quickAcess/utils/Config\";\nimport { useQueryTeam599GetDataDictionary } from \"src/quickAcess/service/quickHooks\";\nimport { eSelectionListId } from \"@common/utils/enum\";\nimport { getEventParentType } from \"@common/utils/logicUtils\";\nimport Conditions from \"./Conditions\";\nimport TemplateDrawer from \"./TemplateDrawer/TemplateDrawer\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport FormEditor from \"@components/FormEditor/FormEditor\";\nimport DraggableDrawer from \"@components/DraggableDrawer\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  TextArea\n} = Input;\n\n// 新建/编辑 事件推送\nexport default function OpDataPush({\n  allMember,\n  opType,\n  closeDrawer,\n  setModalVisible,\n  modalVisible,\n  editData,\n  isChange,\n  setIsChange,\n  selectedKey,\n  typeList,\n  fromType,\n  ganttName,\n  nodeType\n}) {\n  _s();\n  var _eNodeType$nodeType;\n  const {\n    teamId,\n    nodeId\n  } = useParams();\n  const [form] = Form.useForm();\n  // Form.Item布局样式\n  const formItemLayout = {\n    labelCol: {\n      span: 2\n    }\n  };\n\n  // Form.Item的样式\n  const style = {\n    style: {\n      height: \"50%\",\n      marginBottom: 20\n    }\n  };\n  const [pushwhenEmpty, setPushwhenEmpty] = useState(false);\n  const [subscribeData, setSubscribeData] = useState(null); // 先决条件\n  const [description, setDescription] = useState(\"\"); // 备注\n  const [msgTplDetail, setMsgTplDetail] = useState(); // 模版\n\n  const {\n    data: {\n      dataDictionaries = [],\n      templateVariables = []\n    } = {\n      dataDictionaries: [],\n      templateVariables: [],\n      workflowVariables: []\n    },\n    isLoading: isLoadingTeamXxx,\n    refetch: refetchTeamXxx\n  } = useQueryTeam599GetDataDictionary({\n    teamId,\n    selectionId: eSelectionListId.Selection_1970_datapush\n  });\n  useEffect(() => {\n    if (modalVisible) {\n      if (!!editData) {\n        form.setFieldsValue({\n          subscrTitle: editData.subscrTitle || '',\n          // toUids: (editData.toUids||'').split(',').filter(uid => !!uid),\n          toUids: editData.toUids,\n          ccUids: (editData.ccUids || '').split(',').filter(uid => !!uid),\n          msgChannel: editData.msgChannel,\n          priorityNo: editData.priorityNo,\n          tplTitle: editData.tplTitle,\n          // msgTpl: editData.msgTpl,\n          msgTpl: editData.tplBody,\n          attachmentFlg: !!editData.attachmentFlg,\n          objRefFlg: !!editData.objRefFlg\n        });\n        setDescription(editData.description || '');\n        let obj = !!editData.objId ? {\n          objId: editData.objId,\n          objType: editData.objType,\n          objName: editData.objName,\n          objNodeId: editData.objNodeId\n        } : null;\n        setSubscribeData(obj);\n        setPushwhenEmpty(editData.pushWhenEmpty == 1);\n        setMsgTplDetail({\n          msgTplId: editData.msgTplId\n        });\n      } else {\n        form.resetFields();\n      }\n    }\n  }, [modalVisible]);\n  useEffect(() => {\n    if (!isLoadingTeamXxx && !!editData) {\n      let {\n        eventType\n      } = editData;\n      if (!eventType) {\n        return;\n      }\n      const eventTypeParentNode = dataDictionaries.find(data => data.children.some(item => item.code == eventType));\n      const eventTypeNode = ((eventTypeParentNode === null || eventTypeParentNode === void 0 ? void 0 : eventTypeParentNode.children) || []).find(item => item.code == eventType);\n      const eventTypeList = [eventTypeParentNode.code, eventTypeNode.code];\n      form.setFieldValue(\"eventType\", eventTypeList);\n    }\n  }, [isLoadingTeamXxx, editData]);\n  function avatarFormat(src, name) {\n    return /*#__PURE__*/_jsxDEV(Avatar, {\n      style: {\n        marginRight: 5\n      },\n      src: src,\n      icon: /*#__PURE__*/_jsxDEV(NoAvatarIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 64\n      }, this),\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 13\n    }, this);\n  }\n  function onFinish(opType) {\n    debugger;\n    let values = form.getFieldsValue(true);\n    console.log(\"values\", values);\n    const {\n      msgTpl,\n      toUids,\n      ccUids,\n      priorityNo\n    } = values;\n    // if(opType == 0){\n    //   if(!!subscribeData?.objId){\n    //     if((msgTpl||'').indexOf('%订阅数据%') == -1){\n    //      return Modal.confirm({\n    //         title: \"提示\",\n    //         icon: <ExclamationCircleOutlined />,\n    //         content: <div style={{margin:'10px 0px',fontSize:12}}>\n    //         <div>内容框中，未检测到有 %订阅数据% 占位符，若不含此占位符，订阅邮件中将不会有 搜索或报表结果。</div>\n    //         <div>点击\"继续保存\"，表示您不需要包含订阅数据，仅以\"内容\"框中文案作为邮件主体内容；</div>\n    //         <div>点击\"重新修改\"，停在当前界面，您可在\"内容\"框中某个位置，点击蓝色%订阅数据%插入占位符。</div>\n    //       </div>,\n    //         okText: \"继续保存\", \n    //         cancelText: \"重新修改\",\n    //         onOk: () => {\n    //           onFinish(1)\n    //         },\n    //         onCancel: () => {\n    //           console.log(\"Cancel\");\n    //         },\n    //       });\n    //     }\n    //   }\n    // }\n    let params = {\n      teamId: teamId,\n      subscrTitle: values.subscrTitle,\n      msgChannel: values.msgChannel,\n      objId: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objId,\n      // 先决条件保存的 搜索queryId \n      objType: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objType,\n      // objType固定传 9\n      objName: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objName,\n      // msgTpl: values.msgTpl,\n      msgTplId: msgTplDetail.tplId,\n      // toUids: (toUids||[]).join(','),\n      toUids: toUids,\n      ccUids: (ccUids || []).join(','),\n      execTimeType: eExecTimeType.dataPush,\n      eventType: eventType,\n      priorityNo: priorityNo,\n      description: description,\n      pushWhenEmpty: pushwhenEmpty ? 1 : 0,\n      projectNodeId: nodeId,\n      nodeType: nodeType\n    };\n    if (!!editData) {\n      params.nodeId = selectedKey;\n    }\n    httpQuickAccess.team_523_save_sched_task_src(params).then(res => {\n      if (res.resultCode == 200) {\n        closeDrawer(1);\n      }\n    });\n  }\n  function _onClose() {\n    let values = form.getFieldsValue(true);\n    closeDrawer(0, {\n      values: values,\n      objId: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objId,\n      msgTpl: values.msgTpl || '',\n      // toUids: values.toUids||[],\n      toUids: values.toUids,\n      ccUids: values.ccUids || [],\n      pushwhenEmpty: pushwhenEmpty ? 1 : 0\n    });\n  }\n\n  // 模版\n  function onClickTemplateVariables(formKey, info) {\n    let formValue = form.getFieldValue(formKey);\n    if (formKey == \"tplTitle\") {\n      form.setFieldValue(formKey, `${formValue !== null && formValue !== void 0 ? formValue : \"\"}${info.key}`);\n    } else if (formKey == \"msgTpl\") {\n      form.setFieldValue(formKey, `${formValue !== null && formValue !== void 0 ? formValue : \"\"}${info.key}`);\n    } else {\n      var _info$key;\n      form.setFieldValue(formKey, [...(formValue !== null && formValue !== void 0 ? formValue : []), (_info$key = info.key) !== null && _info$key !== void 0 ? _info$key : \"\"]);\n    }\n  }\n  function onChangeRemark(e) {\n    setDescription(e.target.value);\n  }\n  function treeFormat(list = []) {\n    return list.map(tree => {\n      let _tree = {\n        label: tree.value,\n        value: tree.code\n      };\n      if ((tree.children || []).length > 0) {\n        _tree.children = treeFormat(tree.children);\n      }\n      return _tree;\n    });\n  }\n  const eventTypeList = Form.useWatch(\"eventType\", form);\n  const msgChannel = Form.useWatch(\"msgChannel\", form);\n  const eventType = eventTypeList ? eventTypeList[eventTypeList.length - 1] : undefined;\n  const eventParentType = useMemo(() => {\n    return getEventParentType(dataDictionaries, eventType);\n  }, [dataDictionaries, eventType]);\n  const dataDictionariesOptions = useMemo(() => {\n    return treeFormat(dataDictionaries);\n  }, [dataDictionaries]);\n  const selectDataSource = () => {\n    globalEventBus.emit(\"openTemplateDrawerEvent\", \"\", {\n      msgChannel,\n      callback: selectDataSourceCallback\n    });\n  };\n  function selectDataSourceCallback(msgChannel, checkMsgTpl) {\n    console.log(\"checkMsgTpl\", checkMsgTpl);\n    if (!!checkMsgTpl) {\n      setMsgTplDetail(checkMsgTpl);\n      form.setFieldsValue({\n        msgChannel,\n        // 系统推送通道\n        \"tplTitle\": checkMsgTpl.tplTitle,\n        \"msgTpl\": checkMsgTpl.tplBody,\n        \"attachmentFlg\": !!checkMsgTpl.attachmentFlg,\n        // 附件\n        \"objRefFlg\": !!checkMsgTpl.objRefFlg // 对象关联\n      });\n    }\n  }\n  function onClickSubmit() {\n    if (!msgTplDetail) {\n      return globalUtil.warning(\"请选择模版\");\n    }\n    form.submit();\n  }\n  const selectTemplateView = formKey => {\n    return /*#__PURE__*/_jsxDEV(Dropdown, {\n      menu: {\n        items: templateVariables.map(obj => ({\n          key: obj.value,\n          value: obj.value,\n          label: obj.value\n        })),\n        onClick: info => onClickTemplateVariables(formKey, info)\n      },\n      placement: \"bottom\",\n      arrow: {\n        pointAtCenter: true\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        children: \"\\u9009\\u62E9\\u6A21\\u7248\\u53D8\\u91CF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 3\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n    className: \"tms-drawer\",\n    title: `${opType == 0 ? \"新建\" : '编辑'}${fromType == 'gantt' ? '进度推送订阅' : '事件推送'}`,\n    width: '70%',\n    onClose: () => _onClose(),\n    open: modalVisible,\n    destroyOnClose: true //关闭时销毁子元素,避免重新打开数据不会刷新\n    ,\n    preserve: false,\n    closable: true,\n    centered: true,\n    footer: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'end'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: 20,\n        children: [!!subscribeData && /*#__PURE__*/_jsxDEV(Checkbox, {\n          className: \"subscribe-way-mark-check\",\n          onChange: e => setPushwhenEmpty(e.target.checked),\n          checked: pushwhenEmpty,\n          children: \"\\u8BA2\\u9605\\u6570\\u636E\\u4E3A\\u7A7A\\u65F6\\uFF0C\\u4F9D\\u7136\\u63A8\\u9001\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          value: description,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\",\n          style: {\n            borderRadius: 5,\n            width: 300\n          },\n          autoComplete: \"off\",\n          onChange: onChangeRemark\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            borderRadius: 5\n          },\n          type: 'primary',\n          onClick: () => {\n            onClickSubmit();\n          },\n          children: \"\\u63D0\\u4EA4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 9\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(Form, {\n      className: \"subscribe-way\",\n      form: form,\n      ...formItemLayout,\n      labelAlign: \"right\",\n      onFinish: () => onFinish(0),\n      initialValues: {\n        msgChannel: eMsgChannel.mail,\n        // 默认邮件\n        triggerTime: eTriggerTime.immediately // 默认即时触发\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"label-header\",\n        children: \"\\u63A8\\u9001\\u89C4\\u5219\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u540D\\u79F0\",\n        rules: [{\n          required: true,\n          message: '名称不能为空'\n        }],\n        ...style,\n        name: \"subscrTitle\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          style: {\n            width: 300,\n            borderRadius: 5\n          },\n          autoComplete: \"off\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u9009\\u62E9\\u4E8B\\u4EF6\",\n        rules: [{\n          required: true,\n          message: '事件不能为空'\n        }],\n        ...style,\n        name: \"eventType\",\n        children: /*#__PURE__*/_jsxDEV(Cascader, {\n          style: {\n            width: 300,\n            borderRadius: 3\n          }\n          // changeOnSelect\n          ,\n          allowClear: true,\n          popupClassName: \"tms-cascader-popup\",\n          expandTrigger: \"hover\",\n          options: dataDictionariesOptions,\n          placeholder: `请选择事件`,\n          displayRender: (label, selectedOptions) => label.join(\"-\"),\n          showSearch: {\n            filter: (inputValue, path) => path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), (eventParentType == eEventParentType.businessOp || eventParentType == eEventParentType.electronStream) && /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u5148\\u51B3\\u6761\\u4EF6\",\n        ...style,\n        children: /*#__PURE__*/_jsxDEV(Conditions, {\n          subscribeData: subscribeData,\n          setSubscribeData: setSubscribeData,\n          eventParentType: eventParentType\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u89E6\\u53D1\\u65F6\\u673A\",\n        ...style,\n        name: \"triggerTime\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          showSearch: true,\n          allowClear: true,\n          style: {\n            width: 300\n          },\n          placeholder: \"\\u8BF7\\u9009\\u62E9\\u89E6\\u53D1\\u65F6\\u673A\",\n          disabled: true,\n          options: [eTriggerTimeObj[eTriggerTime.immediately]]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u4F18\\u5148\\u7EA7\",\n        ...style,\n        name: \"priorityNo\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          showSearch: true,\n          allowClear: true,\n          style: {\n            width: 300\n          },\n          placeholder: \"\\u8BF7\\u9009\\u62E9\\u4F18\\u5148\\u7EA7\",\n          options: [ePriorityTypeObj[ePriorityType.high], ePriorityTypeObj[ePriorityType.middle], ePriorityTypeObj[ePriorityType.low]]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u7CFB\\u7EDF\\u63A8\\u9001\\u901A\\u9053\",\n        ...style,\n        name: \"msgChannel\",\n        children: /*#__PURE__*/_jsxDEV(Select\n        // disabled={true}\n        , {\n          showSearch: true,\n          allowClear: true,\n          style: {\n            width: 300\n          },\n          placeholder: \"\\u8BF7\\u9009\\u62E9\",\n          options: [eMsgChannelObj[eMsgChannel.mail], eMsgChannelObj[eMsgChannel.msg], eMsgChannelObj[eMsgChannel.voice], eMsgChannelObj[eMsgChannel.wechat], eMsgChannelObj[eMsgChannel.inSiteMsg]]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 12\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 10\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"label-header\",\n        children: \"\\u63A8\\u9001\\u5185\\u5BB9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6536\\u4EF6\\u4EBA\",\n        required: true,\n        ...style,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"toUids\",\n          rules: [{\n            required: true,\n            message: '收件人不能为空'\n          }],\n          noStyle: true,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            showSearch: true,\n            allowClear: true\n            // mode='multiple' fix tmsbug-11663:系统订阅，收件人，期望从多选改为单选\n            ,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u6536\\u4EF6\\u4EBA\",\n            dropdownMatchSelectWidth: false,\n            style: {\n              borderRadius: 5,\n              width: 300\n            },\n            filterOption: (input, option) => {\n              var _option$name;\n              return ((_option$name = option === null || option === void 0 ? void 0 : option.name) !== null && _option$name !== void 0 ? _option$name : '').toLowerCase().includes(input.toLowerCase());\n            },\n            options: allMember.map(user => ({\n              key: user.key.toString(),\n              value: user.key.toString(),\n              icon: avatarFormat(user.avatar, user.label),\n              label: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [avatarFormat(user.avatar, user.label), user.label]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 26\n              }, this),\n              name: user.label\n            })).concat(templateVariables.map(obj => ({\n              key: obj.value,\n              value: obj.value,\n              label: obj.value,\n              name: obj.value\n            })))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 14\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 12\n        }, this), selectTemplateView(\"toUids\")]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6284\\u9001\\u4EBA\",\n        ...style,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"ccUids\",\n          noStyle: true,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            showSearch: true,\n            mode: \"multiple\",\n            allowClear: true,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u6284\\u9001\\u4EBA\",\n            style: {\n              borderRadius: 5,\n              width: 300\n            },\n            filterOption: (input, option) => {\n              var _option$name2;\n              return ((_option$name2 = option === null || option === void 0 ? void 0 : option.name) !== null && _option$name2 !== void 0 ? _option$name2 : '').toLowerCase().includes(input.toLowerCase());\n            },\n            options: allMember.map(user => ({\n              key: user.key.toString(),\n              value: user.key.toString(),\n              label: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [avatarFormat(user.avatar, user.label), user.label]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 24\n              }, this),\n              name: user.label\n            })).concat(templateVariables.map(obj => ({\n              key: obj.value,\n              value: obj.value,\n              label: obj.value,\n              name: obj.value\n            })))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), selectTemplateView(\"ccUids\")]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u4E3B\\u9898\",\n        ...style,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"tplTitle\",\n          noStyle: true,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            style: {\n              borderRadius: 5,\n              width: 300\n            },\n            autoComplete: \"off\",\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          onClick: selectDataSource,\n          children: \"\\u9009\\u62E9\\u6A21\\u7248\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6B63\\u6587\",\n        ...style,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 5\n          },\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"msgTpl\",\n            noStyle: true,\n            children: /*#__PURE__*/_jsxDEV(FormEditor, {\n              disabled: true,\n              placeholder: \"\",\n              editorProps: {\n                uploadParams: {\n                  teamId: teamId,\n                  nodeId: nodeId,\n                  moduleName: (_eNodeType$nodeType = eNodeType[nodeType]) === null || _eNodeType$nodeType === void 0 ? void 0 : _eNodeType$nodeType.nameEn,\n                  objType: nodeType\n                },\n                heightMin: '200px',\n                autofocus: false\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), (eventParentType == eEventParentType.businessOp || eventParentType == eEventParentType.electronStream) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u9644\\u4EF6\",\n          ...style,\n          name: \"attachmentFlg\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 16\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5BF9\\u8C61\\u5173\\u8054\",\n          ...style,\n          name: \"objRefFlg\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 16\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TemplateDrawer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      className: \"tms-modal\",\n      title: \"\\u63D0\\u793A\",\n      centered: true,\n      width: 300,\n      open: isChange,\n      onCancel: () => setIsChange(false),\n      onOk: () => {\n        setIsChange(false);\n        setModalVisible(false);\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          margin: '10px 0px'\n        },\n        children: \"\\u60A8\\u6B63\\u5728\\u7F16\\u8F91\\\"\\u4E8B\\u4EF6\\u63A8\\u9001\\\"\\uFF0C\\u786E\\u5B9A\\u653E\\u5F03\\u7F16\\u8F91\\uFF1F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 250,\n    columnNumber: 5\n  }, this);\n}\n_s(OpDataPush, \"fa1X1MesW9o/tUr4r9IkBWNF9U4=\", false, function () {\n  return [useParams, Form.useForm, useQueryTeam599GetDataDictionary, Form.useWatch, Form.useWatch];\n});\n_c = OpDataPush;\nvar _c;\n$RefreshReg$(_c, \"OpDataPush\");", "map": {"version": 3, "names": ["ExclamationCircleOutlined", "<PERSON><PERSON>", "Input", "Drawer", "Form", "Checkbox", "Avatar", "Select", "Space", "Dropdown", "<PERSON>r", "Modal", "DraggablePopUp", "React", "useEffect", "useRef", "useState", "useMemo", "useParams", "httpQuickAccess", "globalUtil", "eNodeTypeId", "eNodeType", "NoAvatarIcon", "TextModelModal", "PlanModal", "SubscribeContent", "ePriorityTypeObj", "ePriorityType", "eMsgChannel", "eMsgChannelObj", "eTriggerTime", "eTriggerTimeObj", "eExecTimeType", "eEventParentType", "useQueryTeam599GetDataDictionary", "eSelectionListId", "getEventParentType", "Conditions", "TemplateDrawer", "globalEventBus", "FormEditor", "DraggableDrawer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TextArea", "OpDataPush", "allMember", "opType", "closeDrawer", "setModalVisible", "modalVisible", "editData", "isChange", "setIsChange", "<PERSON><PERSON><PERSON>", "typeList", "fromType", "ganttName", "nodeType", "_s", "_eNodeType$nodeType", "teamId", "nodeId", "form", "useForm", "formItemLayout", "labelCol", "span", "style", "height", "marginBottom", "pushwhenEmpty", "setPushwhenEmpty", "subscribeData", "setSubscribeData", "description", "setDescription", "msgTplDetail", "setMsgTplDetail", "data", "dataDictionaries", "templateVariables", "workflowVariables", "isLoading", "isLoadingTeamXxx", "refetch", "refetchTeamXxx", "selectionId", "Selection_1970_datapush", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscrTitle", "toUids", "ccUids", "split", "filter", "uid", "msgChannel", "priorityNo", "tplTitle", "msgTpl", "tplBody", "attachmentFlg", "objRefFlg", "obj", "objId", "objType", "objName", "objNodeId", "pushWhenEmpty", "msgTplId", "resetFields", "eventType", "eventTypeParentNode", "find", "children", "some", "item", "code", "eventTypeNode", "eventTypeList", "setFieldValue", "avatarFormat", "src", "name", "marginRight", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onFinish", "values", "getFieldsValue", "console", "log", "params", "tplId", "join", "execTimeType", "dataPush", "projectNodeId", "team_523_save_sched_task_src", "then", "res", "resultCode", "_onClose", "onClickTemplateVariables", "formKey", "info", "formValue", "getFieldValue", "key", "_info$key", "onChangeRemark", "e", "target", "value", "treeFormat", "list", "map", "tree", "_tree", "label", "length", "useWatch", "undefined", "eventParentType", "dataDictionariesOptions", "selectDataSource", "emit", "callback", "selectDataSourceCallback", "checkMsgTpl", "onClickSubmit", "warning", "submit", "selectTemplateView", "menu", "items", "onClick", "placement", "arrow", "pointAtCenter", "type", "className", "title", "width", "onClose", "open", "destroyOnClose", "preserve", "closable", "centered", "footer", "display", "alignItems", "justifyContent", "onChange", "checked", "placeholder", "borderRadius", "autoComplete", "labelAlign", "initialValues", "mail", "triggerTime", "immediately", "<PERSON><PERSON>", "rules", "required", "message", "allowClear", "popupClassName", "expandTrigger", "options", "displayRender", "selectedOptions", "showSearch", "inputValue", "path", "option", "toLowerCase", "indexOf", "businessOp", "electronStream", "disabled", "high", "middle", "low", "msg", "voice", "wechat", "inSiteMsg", "noStyle", "dropdownMatchSelectWidth", "filterOption", "input", "_option$name", "includes", "user", "toString", "avatar", "concat", "mode", "_option$name2", "marginTop", "editorProps", "uploadParams", "moduleName", "nameEn", "heightMin", "autofocus", "valuePropName", "onCancel", "onOk", "textAlign", "margin", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/quickAcess/views/DataPush/EditDrawer/OpDataPush.jsx"], "sourcesContent": ["/*\r\n * @Author: <PERSON> <EMAIL>\r\n * @Date: 2025-01-21 11:34:18\r\n * @LastEditors: <PERSON> <EMAIL>\r\n * @LastEditTime: 2025-08-05 14:10:24\r\n * @Description: 新建\"事件推送\"\r\n */\r\nimport { ExclamationCircleOutlined } from \"@ant-design/icons\";\r\nimport { Button, Input, Drawer, Form, Checkbox, Avatar, Select, Space, Dropdown, Cascader, Modal } from \"antd\";\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";\r\nimport React, { useEffect, useRef, useState, useMemo } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport '../DataPush.scss';\r\nimport * as httpQuickAccess from \"@common/api/http\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { eNodeTypeId, eNodeType } from \"@common/utils/TsbConfig\";\r\nimport { NoAvatarIcon } from '@common/components/IconUtil';\r\nimport TextModelModal from './TextModel/TextModelModal';\r\nimport PlanModal from './Plan/PlanModal';\r\nimport SubscribeContent from './SubscribeContent/SubscribeContent';\r\nimport { ePriorityTypeObj, ePriorityType, eMsgChannel, eMsgChannelObj, eTriggerTime, eTriggerTimeObj, eExecTimeType, eEventParentType } from \"src/quickAcess/utils/Config\";\r\nimport { useQueryTeam599GetDataDictionary } from \"src/quickAcess/service/quickHooks\";\r\nimport { eSelectionListId } from \"@common/utils/enum\"\r\nimport {getEventParentType} from \"@common/utils/logicUtils\"\r\nimport Conditions from \"./Conditions\";\r\nimport TemplateDrawer from \"./TemplateDrawer/TemplateDrawer\"\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport FormEditor from \"@components/FormEditor/FormEditor\"\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\n\r\nconst { TextArea } = Input\r\n\r\n// 新建/编辑 事件推送\r\nexport default function OpDataPush({ allMember,opType,closeDrawer, setModalVisible,modalVisible,editData,isChange,setIsChange,selectedKey,typeList,fromType, ganttName, nodeType, }) {\r\n  const { teamId,nodeId } = useParams();\r\n  const [form] = Form.useForm();\r\n  // Form.Item布局样式\r\n  const formItemLayout = { labelCol: { span: 2 } };\r\n\r\n  // Form.Item的样式\r\n  const style = { style: {  height: \"50%\", marginBottom: 20 } };\r\n\r\n  const [pushwhenEmpty,setPushwhenEmpty] = useState(false);\r\n  const [subscribeData, setSubscribeData] = useState(null); // 先决条件\r\n  const [description, setDescription] = useState(\"\"); // 备注\r\n  const [msgTplDetail, setMsgTplDetail] = useState(); // 模版\r\n\r\n  const { data: { dataDictionaries=[], templateVariables=[] } = { dataDictionaries: [], templateVariables: [], workflowVariables: [] }, isLoading: isLoadingTeamXxx,refetch: refetchTeamXxx} = useQueryTeam599GetDataDictionary({teamId, selectionId: eSelectionListId.Selection_1970_datapush});\r\n\r\n  useEffect(()=>{\r\n    if(modalVisible){\r\n      if(!!editData){\r\n        form.setFieldsValue({\r\n          subscrTitle: editData.subscrTitle||'',\r\n          // toUids: (editData.toUids||'').split(',').filter(uid => !!uid),\r\n          toUids: editData.toUids,\r\n          ccUids: (editData.ccUids||'').split(',').filter(uid => !!uid),\r\n          msgChannel: editData.msgChannel,\r\n          priorityNo: editData.priorityNo,\r\n          tplTitle: editData.tplTitle,\r\n          // msgTpl: editData.msgTpl,\r\n          msgTpl: editData.tplBody,\r\n          attachmentFlg: !!editData.attachmentFlg,\r\n          objRefFlg: !!editData.objRefFlg,\r\n        });\r\n        setDescription(editData.description||'');\r\n        let obj = !!editData.objId ? {objId: editData.objId, objType: editData.objType, objName: editData.objName, objNodeId: editData.objNodeId } : null\r\n        setSubscribeData(obj);\r\n        setPushwhenEmpty(editData.pushWhenEmpty == 1);\r\n        setMsgTplDetail({\r\n          msgTplId: editData.msgTplId,\r\n        });\r\n      } else {\r\n        form.resetFields();\r\n      }\r\n    }\r\n  },[modalVisible]);\r\n\r\n  useEffect(()=>{\r\n    if(!isLoadingTeamXxx && !!editData){\r\n      let { eventType } = editData\r\n      if(!eventType){\r\n        return ;\r\n      }\r\n      const eventTypeParentNode = dataDictionaries.find(data => data.children.some(item => item.code == eventType));\r\n      const eventTypeNode = (eventTypeParentNode?.children || []).find(item => item.code == eventType);\r\n      const eventTypeList = [eventTypeParentNode.code, eventTypeNode.code]\r\n      form.setFieldValue(\"eventType\", eventTypeList);\r\n    }\r\n  },[isLoadingTeamXxx, editData])\r\n\r\n  function avatarFormat(src, name) {\r\n    return (<Avatar style={{ marginRight: 5 }} src={src} icon={<NoAvatarIcon />} size={24} />);\r\n  }\r\n\r\n  function onFinish(opType){\r\n    debugger\r\n    let values = form.getFieldsValue(true);\r\n    console.log(\"values\", values);\r\n    const { msgTpl, toUids, ccUids, priorityNo } = values;\r\n    // if(opType == 0){\r\n    //   if(!!subscribeData?.objId){\r\n    //     if((msgTpl||'').indexOf('%订阅数据%') == -1){\r\n    //      return Modal.confirm({\r\n    //         title: \"提示\",\r\n    //         icon: <ExclamationCircleOutlined />,\r\n    //         content: <div style={{margin:'10px 0px',fontSize:12}}>\r\n    //         <div>内容框中，未检测到有 %订阅数据% 占位符，若不含此占位符，订阅邮件中将不会有 搜索或报表结果。</div>\r\n    //         <div>点击\"继续保存\"，表示您不需要包含订阅数据，仅以\"内容\"框中文案作为邮件主体内容；</div>\r\n    //         <div>点击\"重新修改\"，停在当前界面，您可在\"内容\"框中某个位置，点击蓝色%订阅数据%插入占位符。</div>\r\n    //       </div>,\r\n    //         okText: \"继续保存\", \r\n    //         cancelText: \"重新修改\",\r\n    //         onOk: () => {\r\n    //           onFinish(1)\r\n    //         },\r\n    //         onCancel: () => {\r\n    //           console.log(\"Cancel\");\r\n    //         },\r\n    //       });\r\n    //     }\r\n    //   }\r\n    // }\r\n    let params = {\r\n      teamId: teamId,\r\n      subscrTitle: values.subscrTitle,\r\n      msgChannel: values.msgChannel,\r\n      objId: subscribeData?.objId,  // 先决条件保存的 搜索queryId \r\n      objType: subscribeData?.objType, // objType固定传 9\r\n      objName: subscribeData?.objName,\r\n      // msgTpl: values.msgTpl,\r\n      msgTplId: msgTplDetail.tplId,\r\n      // toUids: (toUids||[]).join(','),\r\n      toUids:toUids,\r\n      ccUids: (ccUids||[]).join(','),\r\n      execTimeType: eExecTimeType.dataPush, \r\n      eventType: eventType,\r\n      priorityNo: priorityNo,\r\n      description: description,\r\n      pushWhenEmpty: pushwhenEmpty ? 1 : 0,\r\n      projectNodeId: nodeId,\r\n      nodeType: nodeType,\r\n    }\r\n    if(!!editData){\r\n      params.nodeId = selectedKey\r\n    }\r\n    httpQuickAccess.team_523_save_sched_task_src(params).then(res => {\r\n      if(res.resultCode == 200){\r\n        closeDrawer(1);\r\n      }\r\n    });\r\n  }\r\n\r\n  function _onClose(){\r\n    let values = form.getFieldsValue(true);\r\n    closeDrawer(0,{\r\n      values: values, \r\n      objId: subscribeData?.objId, \r\n      msgTpl: values.msgTpl||'', \r\n      // toUids: values.toUids||[],\r\n      toUids: values.toUids,\r\n      ccUids: values.ccUids||[],\r\n      pushwhenEmpty: pushwhenEmpty ? 1 : 0,\r\n    })\r\n  }\r\n\r\n  // 模版\r\n  function onClickTemplateVariables (formKey, info) {\r\n    let formValue = form.getFieldValue(formKey);\r\n    if(formKey == \"tplTitle\"){\r\n      form.setFieldValue(formKey, `${formValue??\"\"}${info.key}`);\r\n    } else if (formKey ==  \"msgTpl\"){\r\n      form.setFieldValue(formKey, `${formValue??\"\"}${info.key}`);\r\n    } else {\r\n      form.setFieldValue(formKey, [...(formValue??[]), info.key??\"\"]);\r\n    }\r\n  }\r\n\r\n  function onChangeRemark (e) {\r\n   setDescription(e.target.value);\r\n  }\r\n\r\n  function treeFormat(list = []){\r\n    return list.map(tree => {\r\n      let _tree = {label: tree.value, value: tree.code}\r\n      if((tree.children||[]).length > 0){\r\n        _tree.children = treeFormat(tree.children)\r\n      }\r\n      return _tree\r\n    })\r\n  }\r\n\r\n  const eventTypeList = Form.useWatch(\"eventType\",form);\r\n  const msgChannel = Form.useWatch(\"msgChannel\",form);\r\n  const eventType = eventTypeList ? eventTypeList[eventTypeList.length -1] : undefined;\r\n\r\n  const eventParentType = useMemo(()=>{\r\n     return getEventParentType(dataDictionaries, eventType);\r\n  },[dataDictionaries, eventType])\r\n  \r\n  const dataDictionariesOptions = useMemo(() => {\r\n    return treeFormat(dataDictionaries);\r\n  }, [dataDictionaries]);\r\n\r\n  const selectDataSource = () => {\r\n    globalEventBus.emit(\"openTemplateDrawerEvent\", \"\", { msgChannel, callback: selectDataSourceCallback});\r\n  }\r\n\r\n  function selectDataSourceCallback (msgChannel, checkMsgTpl) {\r\n    console.log(\"checkMsgTpl\", checkMsgTpl);\r\n    if(!!checkMsgTpl){\r\n      setMsgTplDetail(checkMsgTpl);\r\n      form.setFieldsValue({\r\n        msgChannel, // 系统推送通道\r\n       \"tplTitle\": checkMsgTpl.tplTitle,\r\n       \"msgTpl\": checkMsgTpl.tplBody,\r\n       \"attachmentFlg\": !!checkMsgTpl.attachmentFlg, // 附件\r\n       \"objRefFlg\": !!checkMsgTpl.objRefFlg,  // 对象关联\r\n     });\r\n    }\r\n  }\r\n\r\n  function onClickSubmit () {\r\n    if(!msgTplDetail){\r\n      return globalUtil.warning(\"请选择模版\");\r\n    }\r\n    form.submit();\r\n  }\r\n\r\n  const selectTemplateView = (formKey) => {\r\n    return <Dropdown\r\n    menu={{\r\n      items: templateVariables.map(obj => ({\r\n        key: obj.value,\r\n        value: obj.value,\r\n        label: obj.value,\r\n      })),\r\n      onClick: (info)=>onClickTemplateVariables(formKey, info)\r\n    }}\r\n    placement=\"bottom\"\r\n    arrow={{\r\n      pointAtCenter: true,\r\n    }}\r\n  >\r\n  <Button type='link'>选择模版变量</Button>\r\n  </Dropdown>\r\n  }\r\n\r\n  return (\r\n    <DraggableDrawer\r\n      className=\"tms-drawer\"\r\n      title={`${opType == 0 ? \"新建\" : '编辑'}${fromType == 'gantt' ? '进度推送订阅' : '事件推送'}`}\r\n      width={'70%'}\r\n      onClose={() => _onClose()}\r\n      open={modalVisible}\r\n      destroyOnClose={true} //关闭时销毁子元素,避免重新打开数据不会刷新\r\n      preserve={false}\r\n      closable\r\n      centered\r\n      footer={\r\n        <div style={{display:'flex',alignItems:'center',justifyContent:'end'}}>\r\n          <Space size={20}>\r\n          {!!subscribeData &&\r\n          <Checkbox\r\n            className=\"subscribe-way-mark-check\"\r\n            onChange={(e)=> setPushwhenEmpty(e.target.checked)} \r\n            checked={pushwhenEmpty}\r\n          >\r\n            订阅数据为空时，依然推送。\r\n          </Checkbox>\r\n          }\r\n          <Input value={description} placeholder=\"请输入备注\" style={{ borderRadius: 5, width:300 }} autoComplete=\"off\" onChange={onChangeRemark}></Input>\r\n          <Button style={{borderRadius:5}} type={'primary'} onClick={()=>{onClickSubmit()}}>提交</Button>\r\n          </Space>\r\n        </div>\r\n      }\r\n    >\r\n      <Form\r\n        className=\"subscribe-way\"\r\n        form={form}\r\n        {...formItemLayout}\r\n        labelAlign=\"right\"\r\n        onFinish={()=>onFinish(0)}\r\n        initialValues={{\r\n          msgChannel: eMsgChannel.mail, // 默认邮件\r\n          triggerTime: eTriggerTime.immediately, // 默认即时触发\r\n        }}\r\n      >\r\n        <div className=\"label-header\">推送规则</div>\r\n        {/* 订阅名称 */}\r\n        <Form.Item label=\"名称\" rules={[{required: true, message: '名称不能为空'}]} {...style} name='subscrTitle'>\r\n          <Input style={{ width: 300,  borderRadius: 5 }} autoComplete=\"off\"/>\r\n        </Form.Item>\r\n        <Form.Item label=\"选择事件\" rules={[{required: true, message: '事件不能为空'}]}  {...style} name=\"eventType\">\r\n          <Cascader\r\n            style={{ width: 300, borderRadius: 3 }}\r\n            // changeOnSelect\r\n            allowClear\r\n            popupClassName=\"tms-cascader-popup\"\r\n            expandTrigger=\"hover\"\r\n            options={dataDictionariesOptions}\r\n            placeholder={`请选择事件`}\r\n            displayRender={(label, selectedOptions) => label.join(\"-\") }\r\n            showSearch={{\r\n              filter: (inputValue, path) =>path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1),\r\n            }}\r\n          />\r\n        </Form.Item>\r\n        {\r\n          (eventParentType == eEventParentType.businessOp || eventParentType == eEventParentType.electronStream) && \r\n          <Form.Item label=\"先决条件\" {...style} >\r\n            <Conditions subscribeData={subscribeData} setSubscribeData={setSubscribeData} eventParentType={eventParentType}/>\r\n          </Form.Item>\r\n        }\r\n        <Form.Item label=\"触发时机\" {...style} name=\"triggerTime\">\r\n          <Select\r\n            showSearch\r\n            allowClear\r\n            style={{ width: 300 }}\r\n            placeholder='请选择触发时机'\r\n            disabled={true}\r\n            options={[\r\n              eTriggerTimeObj[eTriggerTime.immediately],\r\n            ]}\r\n          />\r\n        </Form.Item>\r\n        <Form.Item label=\"优先级\" {...style} name=\"priorityNo\">\r\n          <Select\r\n            showSearch\r\n            allowClear\r\n            style={{ width: 300 }}\r\n            placeholder='请选择优先级'\r\n            options={[\r\n              ePriorityTypeObj[ePriorityType.high],\r\n              ePriorityTypeObj[ePriorityType.middle],\r\n              ePriorityTypeObj[ePriorityType.low],\r\n            ]}\r\n          />\r\n        </Form.Item>\r\n         <Form.Item label='系统推送通道' {...style} name=\"msgChannel\">\r\n           <Select\r\n            // disabled={true}\r\n            showSearch\r\n            allowClear\r\n            style={{ width: 300 }}\r\n            placeholder='请选择'\r\n            options={[\r\n              eMsgChannelObj[eMsgChannel.mail],\r\n              eMsgChannelObj[eMsgChannel.msg],\r\n              eMsgChannelObj[eMsgChannel.voice],\r\n              eMsgChannelObj[eMsgChannel.wechat],\r\n              eMsgChannelObj[eMsgChannel.inSiteMsg],\r\n            ]}\r\n          />\r\n        </Form.Item>\r\n        <div className=\"label-header\">推送内容</div>\r\n        {/* 收件人 */}\r\n        <Form.Item label=\"收件人\" required {...style}>\r\n           <Form.Item name=\"toUids\" rules={[{required: true, message: '收件人不能为空'}]} noStyle>\r\n             <Select\r\n                showSearch\r\n                allowClear\r\n                // mode='multiple' fix tmsbug-11663:系统订阅，收件人，期望从多选改为单选\r\n                placeholder=\"请选择收件人\"\r\n                dropdownMatchSelectWidth={false}\r\n                style={{ borderRadius: 5, width:300 }}\r\n                filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}\r\n                options={allMember.map(user => ({\r\n                  key: user.key.toString(),\r\n                  value: user.key.toString(),\r\n                  icon: avatarFormat(user.avatar, user.label),\r\n                  label: <div style={{ display: 'flex', alignItems: 'center' }}>{avatarFormat(user.avatar, user.label)}{user.label}</div>,\r\n                  name: user.label,\r\n                })).concat(templateVariables.map(obj => ({\r\n                    key: obj.value,\r\n                    value: obj.value,\r\n                    label: obj.value,\r\n                    name: obj.value,\r\n                  })))}\r\n              />\r\n           </Form.Item>\r\n          {selectTemplateView(\"toUids\")}\r\n        </Form.Item>\r\n        {/* 抄送人 */}\r\n        <Form.Item label=\"抄送人\" {...style}>\r\n          <Form.Item name=\"ccUids\" noStyle>\r\n            <Select\r\n              showSearch\r\n              mode=\"multiple\"\r\n              allowClear\r\n              placeholder=\"请选择抄送人\"\r\n              style={{ borderRadius: 5, width:300 }}\r\n              filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}\r\n              options={allMember.map(user => ({\r\n                key: user.key.toString(),\r\n                value: user.key.toString(),\r\n                label: <div style={{ display: 'flex', alignItems: 'center' }}>{avatarFormat(user.avatar, user.label)}{user.label}</div>,\r\n                name: user.label,\r\n              })).concat(templateVariables.map(obj => ({\r\n                key: obj.value,\r\n                value: obj.value,\r\n                label: obj.value,\r\n                name: obj.value,\r\n              })))}\r\n            />\r\n           </Form.Item>\r\n           {selectTemplateView(\"ccUids\")}\r\n        </Form.Item>\r\n        <Form.Item label=\"主题\"  {...style}>\r\n            <Form.Item name=\"tplTitle\" noStyle>\r\n              <Input style={{ borderRadius: 5, width:300 }} autoComplete=\"off\" disabled={true}/>\r\n            </Form.Item>\r\n            <Button type=\"link\" onClick={selectDataSource}>选择模版</Button>\r\n        </Form.Item>\r\n        {/* 订阅数据源 */}\r\n        {/* <Form.Item label=\"订阅数据源\" {...style}>\r\n          <div style={{display:'flex',alignItems:'center'}}>\r\n            <Select\r\n              style={{ width: 300 }}\r\n              open={false}\r\n              placeholder='请选择'\r\n              value={subscribeData?.objName}\r\n              suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}\r\n              onChange={(value)=>_onChange(value,0)}\r\n              allowClear\r\n              onClick={() => setSubscribeDataVisible(true)} />\r\n            <div style={{color:'#999',marginLeft:10}}>备注：订阅邮件中将呈现的搜索结果或报表</div>\r\n          </div>\r\n        </Form.Item> */}\r\n        <Form.Item label=\"正文\" {...style}>\r\n          <div style={{marginTop:5}}>\r\n            <Form.Item name=\"msgTpl\" noStyle>\r\n              <FormEditor\r\n                disabled={true}\r\n                placeholder={\"\"}\r\n                editorProps = {\r\n                  {\r\n                    uploadParams : {\r\n                      teamId: teamId,\r\n                      nodeId: nodeId,\r\n                      moduleName: eNodeType[nodeType]?.nameEn,\r\n                      objType: nodeType\r\n                    },\r\n                    heightMin:'200px',\r\n                    autofocus:false,\r\n                  }\r\n                }/>\r\n             </Form.Item>\r\n            {/* <div style={{color:'#999'}}>提示：占位符 <a onClick={()=>editRef.current.insert('%订阅数据%')}>%订阅数据%</a>，代表需要订阅的搜索结果或报表数据，请点击占位符，将其包含进正文。</div> */}\r\n          </div>\r\n        </Form.Item>\r\n        {\r\n          (eventParentType == eEventParentType.businessOp || eventParentType == eEventParentType.electronStream) && <>\r\n            <Form.Item label=\"附件\" {...style} name=\"attachmentFlg\" valuePropName=\"checked\">\r\n               <Checkbox  disabled={true}/>\r\n            </Form.Item>\r\n            <Form.Item label=\"对象关联\"  {...style} name=\"objRefFlg\" valuePropName=\"checked\" >\r\n               <Checkbox  disabled={true}/>\r\n            </Form.Item>\r\n          </>\r\n        }\r\n        {/* 备注 */}\r\n        {/* <Form.Item label=\"备注\" {...style} name='description'>\r\n          <TextArea style={{ minHeight: 200, borderRadius: 5 }} autoComplete=\"off\"/>\r\n        </Form.Item> */}\r\n      </Form>\r\n      {/* 模版列表 */}\r\n      <TemplateDrawer />\r\n      <DraggablePopUp\r\n        className=\"tms-modal\"\r\n        title='提示'\r\n        centered\r\n        width={300}\r\n        open={isChange}\r\n        onCancel={()=>setIsChange(false)}\r\n        onOk={()=>{\r\n          setIsChange(false)\r\n          setModalVisible(false)\r\n        }}\r\n      >\r\n        <div style={{textAlign:'center',margin:'10px 0px'}}>\r\n          您正在编辑\"事件推送\"，确定放弃编辑？\r\n        </div>\r\n      </DraggablePopUp>\r\n    </DraggableDrawer>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,QAAQ,mBAAmB;AAC7D,SAASC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AAC9G,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AACnE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAO,kBAAkB;AACzB,OAAO,KAAKC,eAAe,MAAM,kBAAkB;AACnD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,WAAW,EAAEC,SAAS,QAAQ,yBAAyB;AAChE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,cAAc,EAAEC,YAAY,EAAEC,eAAe,EAAEC,aAAa,EAAEC,gBAAgB,QAAQ,6BAA6B;AAC1K,SAASC,gCAAgC,QAAQ,mCAAmC;AACpF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAOC,UAAU,MAAM,mCAAmC;AAC1D,OAAOC,eAAe,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAM;EAAEC;AAAS,CAAC,GAAG7C,KAAK;;AAE1B;AACA,eAAe,SAAS8C,UAAUA,CAAC;EAAEC,SAAS;EAACC,MAAM;EAACC,WAAW;EAAEC,eAAe;EAACC,YAAY;EAACC,QAAQ;EAACC,QAAQ;EAACC,WAAW;EAACC,WAAW;EAACC,QAAQ;EAACC,QAAQ;EAAEC,SAAS;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,mBAAA;EACnL,MAAM;IAAEC,MAAM;IAACC;EAAO,CAAC,GAAG/C,SAAS,CAAC,CAAC;EACrC,MAAM,CAACgD,IAAI,CAAC,GAAG9D,IAAI,CAAC+D,OAAO,CAAC,CAAC;EAC7B;EACA,MAAMC,cAAc,GAAG;IAAEC,QAAQ,EAAE;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC;;EAEhD;EACA,MAAMC,KAAK,GAAG;IAAEA,KAAK,EAAE;MAAGC,MAAM,EAAE,KAAK;MAAEC,YAAY,EAAE;IAAG;EAAE,CAAC;EAE7D,MAAM,CAACC,aAAa,EAACC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACxD,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD,MAAM;IAAEkE,IAAI,EAAE;MAAEC,gBAAgB,GAAC,EAAE;MAAEC,iBAAiB,GAAC;IAAG,CAAC,GAAG;MAAED,gBAAgB,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,iBAAiB,EAAE;IAAG,CAAC;IAAEC,SAAS,EAAEC,gBAAgB;IAACC,OAAO,EAAEC;EAAc,CAAC,GAAGtD,gCAAgC,CAAC;IAAC6B,MAAM;IAAE0B,WAAW,EAAEtD,gBAAgB,CAACuD;EAAuB,CAAC,CAAC;EAE9R7E,SAAS,CAAC,MAAI;IACZ,IAAGuC,YAAY,EAAC;MACd,IAAG,CAAC,CAACC,QAAQ,EAAC;QACZY,IAAI,CAAC0B,cAAc,CAAC;UAClBC,WAAW,EAAEvC,QAAQ,CAACuC,WAAW,IAAE,EAAE;UACrC;UACAC,MAAM,EAAExC,QAAQ,CAACwC,MAAM;UACvBC,MAAM,EAAE,CAACzC,QAAQ,CAACyC,MAAM,IAAE,EAAE,EAAEC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC;UAC7DC,UAAU,EAAE7C,QAAQ,CAAC6C,UAAU;UAC/BC,UAAU,EAAE9C,QAAQ,CAAC8C,UAAU;UAC/BC,QAAQ,EAAE/C,QAAQ,CAAC+C,QAAQ;UAC3B;UACAC,MAAM,EAAEhD,QAAQ,CAACiD,OAAO;UACxBC,aAAa,EAAE,CAAC,CAAClD,QAAQ,CAACkD,aAAa;UACvCC,SAAS,EAAE,CAAC,CAACnD,QAAQ,CAACmD;QACxB,CAAC,CAAC;QACF1B,cAAc,CAACzB,QAAQ,CAACwB,WAAW,IAAE,EAAE,CAAC;QACxC,IAAI4B,GAAG,GAAG,CAAC,CAACpD,QAAQ,CAACqD,KAAK,GAAG;UAACA,KAAK,EAAErD,QAAQ,CAACqD,KAAK;UAAEC,OAAO,EAAEtD,QAAQ,CAACsD,OAAO;UAAEC,OAAO,EAAEvD,QAAQ,CAACuD,OAAO;UAAEC,SAAS,EAAExD,QAAQ,CAACwD;QAAU,CAAC,GAAG,IAAI;QACjJjC,gBAAgB,CAAC6B,GAAG,CAAC;QACrB/B,gBAAgB,CAACrB,QAAQ,CAACyD,aAAa,IAAI,CAAC,CAAC;QAC7C9B,eAAe,CAAC;UACd+B,QAAQ,EAAE1D,QAAQ,CAAC0D;QACrB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL9C,IAAI,CAAC+C,WAAW,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAC,CAAC5D,YAAY,CAAC,CAAC;EAEjBvC,SAAS,CAAC,MAAI;IACZ,IAAG,CAACyE,gBAAgB,IAAI,CAAC,CAACjC,QAAQ,EAAC;MACjC,IAAI;QAAE4D;MAAU,CAAC,GAAG5D,QAAQ;MAC5B,IAAG,CAAC4D,SAAS,EAAC;QACZ;MACF;MACA,MAAMC,mBAAmB,GAAGhC,gBAAgB,CAACiC,IAAI,CAAClC,IAAI,IAAIA,IAAI,CAACmC,QAAQ,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,IAAIN,SAAS,CAAC,CAAC;MAC7G,MAAMO,aAAa,GAAG,CAAC,CAAAN,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEE,QAAQ,KAAI,EAAE,EAAED,IAAI,CAACG,IAAI,IAAIA,IAAI,CAACC,IAAI,IAAIN,SAAS,CAAC;MAChG,MAAMQ,aAAa,GAAG,CAACP,mBAAmB,CAACK,IAAI,EAAEC,aAAa,CAACD,IAAI,CAAC;MACpEtD,IAAI,CAACyD,aAAa,CAAC,WAAW,EAAED,aAAa,CAAC;IAChD;EACF,CAAC,EAAC,CAACnC,gBAAgB,EAAEjC,QAAQ,CAAC,CAAC;EAE/B,SAASsE,YAAYA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAC/B,oBAAQlF,OAAA,CAACtC,MAAM;MAACiE,KAAK,EAAE;QAAEwD,WAAW,EAAE;MAAE,CAAE;MAACF,GAAG,EAAEA,GAAI;MAACG,IAAI,eAAEpF,OAAA,CAACrB,YAAY;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAACC,IAAI,EAAE;IAAG;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3F;EAEA,SAASE,QAAQA,CAACpF,MAAM,EAAC;IACvB;IACA,IAAIqF,MAAM,GAAGrE,IAAI,CAACsE,cAAc,CAAC,IAAI,CAAC;IACtCC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEH,MAAM,CAAC;IAC7B,MAAM;MAAEjC,MAAM;MAAER,MAAM;MAAEC,MAAM;MAAEK;IAAW,CAAC,GAAGmC,MAAM;IACrD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAII,MAAM,GAAG;MACX3E,MAAM,EAAEA,MAAM;MACd6B,WAAW,EAAE0C,MAAM,CAAC1C,WAAW;MAC/BM,UAAU,EAAEoC,MAAM,CAACpC,UAAU;MAC7BQ,KAAK,EAAE/B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+B,KAAK;MAAG;MAC9BC,OAAO,EAAEhC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgC,OAAO;MAAE;MACjCC,OAAO,EAAEjC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiC,OAAO;MAC/B;MACAG,QAAQ,EAAEhC,YAAY,CAAC4D,KAAK;MAC5B;MACA9C,MAAM,EAACA,MAAM;MACbC,MAAM,EAAE,CAACA,MAAM,IAAE,EAAE,EAAE8C,IAAI,CAAC,GAAG,CAAC;MAC9BC,YAAY,EAAE7G,aAAa,CAAC8G,QAAQ;MACpC7B,SAAS,EAAEA,SAAS;MACpBd,UAAU,EAAEA,UAAU;MACtBtB,WAAW,EAAEA,WAAW;MACxBiC,aAAa,EAAErC,aAAa,GAAG,CAAC,GAAG,CAAC;MACpCsE,aAAa,EAAE/E,MAAM;MACrBJ,QAAQ,EAAEA;IACZ,CAAC;IACD,IAAG,CAAC,CAACP,QAAQ,EAAC;MACZqF,MAAM,CAAC1E,MAAM,GAAGR,WAAW;IAC7B;IACAtC,eAAe,CAAC8H,4BAA4B,CAACN,MAAM,CAAC,CAACO,IAAI,CAACC,GAAG,IAAI;MAC/D,IAAGA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAC;QACvBjG,WAAW,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC;EACJ;EAEA,SAASkG,QAAQA,CAAA,EAAE;IACjB,IAAId,MAAM,GAAGrE,IAAI,CAACsE,cAAc,CAAC,IAAI,CAAC;IACtCrF,WAAW,CAAC,CAAC,EAAC;MACZoF,MAAM,EAAEA,MAAM;MACd5B,KAAK,EAAE/B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+B,KAAK;MAC3BL,MAAM,EAAEiC,MAAM,CAACjC,MAAM,IAAE,EAAE;MACzB;MACAR,MAAM,EAAEyC,MAAM,CAACzC,MAAM;MACrBC,MAAM,EAAEwC,MAAM,CAACxC,MAAM,IAAE,EAAE;MACzBrB,aAAa,EAAEA,aAAa,GAAG,CAAC,GAAG;IACrC,CAAC,CAAC;EACJ;;EAEA;EACA,SAAS4E,wBAAwBA,CAAEC,OAAO,EAAEC,IAAI,EAAE;IAChD,IAAIC,SAAS,GAAGvF,IAAI,CAACwF,aAAa,CAACH,OAAO,CAAC;IAC3C,IAAGA,OAAO,IAAI,UAAU,EAAC;MACvBrF,IAAI,CAACyD,aAAa,CAAC4B,OAAO,EAAE,GAAGE,SAAS,aAATA,SAAS,cAATA,SAAS,GAAE,EAAE,GAAGD,IAAI,CAACG,GAAG,EAAE,CAAC;IAC5D,CAAC,MAAM,IAAIJ,OAAO,IAAK,QAAQ,EAAC;MAC9BrF,IAAI,CAACyD,aAAa,CAAC4B,OAAO,EAAE,GAAGE,SAAS,aAATA,SAAS,cAATA,SAAS,GAAE,EAAE,GAAGD,IAAI,CAACG,GAAG,EAAE,CAAC;IAC5D,CAAC,MAAM;MAAA,IAAAC,SAAA;MACL1F,IAAI,CAACyD,aAAa,CAAC4B,OAAO,EAAE,CAAC,IAAIE,SAAS,aAATA,SAAS,cAATA,SAAS,GAAE,EAAE,CAAC,GAAAG,SAAA,GAAEJ,IAAI,CAACG,GAAG,cAAAC,SAAA,cAAAA,SAAA,GAAE,EAAE,CAAC,CAAC;IACjE;EACF;EAEA,SAASC,cAAcA,CAAEC,CAAC,EAAE;IAC3B/E,cAAc,CAAC+E,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B;EAEA,SAASC,UAAUA,CAACC,IAAI,GAAG,EAAE,EAAC;IAC5B,OAAOA,IAAI,CAACC,GAAG,CAACC,IAAI,IAAI;MACtB,IAAIC,KAAK,GAAG;QAACC,KAAK,EAAEF,IAAI,CAACJ,KAAK;QAAEA,KAAK,EAAEI,IAAI,CAAC5C;MAAI,CAAC;MACjD,IAAG,CAAC4C,IAAI,CAAC/C,QAAQ,IAAE,EAAE,EAAEkD,MAAM,GAAG,CAAC,EAAC;QAChCF,KAAK,CAAChD,QAAQ,GAAG4C,UAAU,CAACG,IAAI,CAAC/C,QAAQ,CAAC;MAC5C;MACA,OAAOgD,KAAK;IACd,CAAC,CAAC;EACJ;EAEA,MAAM3C,aAAa,GAAGtH,IAAI,CAACoK,QAAQ,CAAC,WAAW,EAACtG,IAAI,CAAC;EACrD,MAAMiC,UAAU,GAAG/F,IAAI,CAACoK,QAAQ,CAAC,YAAY,EAACtG,IAAI,CAAC;EACnD,MAAMgD,SAAS,GAAGQ,aAAa,GAAGA,aAAa,CAACA,aAAa,CAAC6C,MAAM,GAAE,CAAC,CAAC,GAAGE,SAAS;EAEpF,MAAMC,eAAe,GAAGzJ,OAAO,CAAC,MAAI;IACjC,OAAOoB,kBAAkB,CAAC8C,gBAAgB,EAAE+B,SAAS,CAAC;EACzD,CAAC,EAAC,CAAC/B,gBAAgB,EAAE+B,SAAS,CAAC,CAAC;EAEhC,MAAMyD,uBAAuB,GAAG1J,OAAO,CAAC,MAAM;IAC5C,OAAOgJ,UAAU,CAAC9E,gBAAgB,CAAC;EACrC,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAEtB,MAAMyF,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpI,cAAc,CAACqI,IAAI,CAAC,yBAAyB,EAAE,EAAE,EAAE;MAAE1E,UAAU;MAAE2E,QAAQ,EAAEC;IAAwB,CAAC,CAAC;EACvG,CAAC;EAED,SAASA,wBAAwBA,CAAE5E,UAAU,EAAE6E,WAAW,EAAE;IAC1DvC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEsC,WAAW,CAAC;IACvC,IAAG,CAAC,CAACA,WAAW,EAAC;MACf/F,eAAe,CAAC+F,WAAW,CAAC;MAC5B9G,IAAI,CAAC0B,cAAc,CAAC;QAClBO,UAAU;QAAE;QACb,UAAU,EAAE6E,WAAW,CAAC3E,QAAQ;QAChC,QAAQ,EAAE2E,WAAW,CAACzE,OAAO;QAC7B,eAAe,EAAE,CAAC,CAACyE,WAAW,CAACxE,aAAa;QAAE;QAC9C,WAAW,EAAE,CAAC,CAACwE,WAAW,CAACvE,SAAS,CAAG;MACzC,CAAC,CAAC;IACH;EACF;EAEA,SAASwE,aAAaA,CAAA,EAAI;IACxB,IAAG,CAACjG,YAAY,EAAC;MACf,OAAO5D,UAAU,CAAC8J,OAAO,CAAC,OAAO,CAAC;IACpC;IACAhH,IAAI,CAACiH,MAAM,CAAC,CAAC;EACf;EAEA,MAAMC,kBAAkB,GAAI7B,OAAO,IAAK;IACtC,oBAAO3G,OAAA,CAACnC,QAAQ;MAChB4K,IAAI,EAAE;QACJC,KAAK,EAAElG,iBAAiB,CAAC+E,GAAG,CAACzD,GAAG,KAAK;UACnCiD,GAAG,EAAEjD,GAAG,CAACsD,KAAK;UACdA,KAAK,EAAEtD,GAAG,CAACsD,KAAK;UAChBM,KAAK,EAAE5D,GAAG,CAACsD;QACb,CAAC,CAAC,CAAC;QACHuB,OAAO,EAAG/B,IAAI,IAAGF,wBAAwB,CAACC,OAAO,EAAEC,IAAI;MACzD,CAAE;MACFgC,SAAS,EAAC,QAAQ;MAClBC,KAAK,EAAE;QACLC,aAAa,EAAE;MACjB,CAAE;MAAArE,QAAA,eAEJzE,OAAA,CAAC3C,MAAM;QAAC0L,IAAI,EAAC,MAAM;QAAAtE,QAAA,EAAC;MAAM;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EACX,CAAC;EAED,oBACExF,OAAA,CAACF,eAAe;IACdkJ,SAAS,EAAC,YAAY;IACtBC,KAAK,EAAE,GAAG3I,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,GAAGS,QAAQ,IAAI,OAAO,GAAG,QAAQ,GAAG,MAAM,EAAG;IAChFmI,KAAK,EAAE,KAAM;IACbC,OAAO,EAAEA,CAAA,KAAM1C,QAAQ,CAAC,CAAE;IAC1B2C,IAAI,EAAE3I,YAAa;IACnB4I,cAAc,EAAE,IAAK,CAAC;IAAA;IACtBC,QAAQ,EAAE,KAAM;IAChBC,QAAQ;IACRC,QAAQ;IACRC,MAAM,eACJzJ,OAAA;MAAK2B,KAAK,EAAE;QAAC+H,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,cAAc,EAAC;MAAK,CAAE;MAAAnF,QAAA,eACpEzE,OAAA,CAACpC,KAAK;QAAC6H,IAAI,EAAE,EAAG;QAAAhB,QAAA,GACf,CAAC,CAACzC,aAAa,iBAChBhC,OAAA,CAACvC,QAAQ;UACPuL,SAAS,EAAC,0BAA0B;UACpCa,QAAQ,EAAG3C,CAAC,IAAInF,gBAAgB,CAACmF,CAAC,CAACC,MAAM,CAAC2C,OAAO,CAAE;UACnDA,OAAO,EAAEhI,aAAc;UAAA2C,QAAA,EACxB;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAEXxF,OAAA,CAAC1C,KAAK;UAAC8J,KAAK,EAAElF,WAAY;UAAC6H,WAAW,EAAC,gCAAO;UAACpI,KAAK,EAAE;YAAEqI,YAAY,EAAE,CAAC;YAAEd,KAAK,EAAC;UAAI,CAAE;UAACe,YAAY,EAAC,KAAK;UAACJ,QAAQ,EAAE5C;QAAe;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC3IxF,OAAA,CAAC3C,MAAM;UAACsE,KAAK,EAAE;YAACqI,YAAY,EAAC;UAAC,CAAE;UAACjB,IAAI,EAAE,SAAU;UAACJ,OAAO,EAAEA,CAAA,KAAI;YAACN,aAAa,CAAC,CAAC;UAAA,CAAE;UAAA5D,QAAA,EAAC;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;IAAAf,QAAA,gBAEDzE,OAAA,CAACxC,IAAI;MACHwL,SAAS,EAAC,eAAe;MACzB1H,IAAI,EAAEA,IAAK;MAAA,GACPE,cAAc;MAClB0I,UAAU,EAAC,OAAO;MAClBxE,QAAQ,EAAEA,CAAA,KAAIA,QAAQ,CAAC,CAAC,CAAE;MAC1ByE,aAAa,EAAE;QACb5G,UAAU,EAAEtE,WAAW,CAACmL,IAAI;QAAE;QAC9BC,WAAW,EAAElL,YAAY,CAACmL,WAAW,CAAE;MACzC,CAAE;MAAA7F,QAAA,gBAEFzE,OAAA;QAAKgJ,SAAS,EAAC,cAAc;QAAAvE,QAAA,EAAC;MAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAExCxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,cAAI;QAAC8C,KAAK,EAAE,CAAC;UAACC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAQ,CAAC,CAAE;QAAA,GAAK/I,KAAK;QAAEuD,IAAI,EAAC,aAAa;QAAAT,QAAA,eAC/FzE,OAAA,CAAC1C,KAAK;UAACqE,KAAK,EAAE;YAAEuH,KAAK,EAAE,GAAG;YAAGc,YAAY,EAAE;UAAE,CAAE;UAACC,YAAY,EAAC;QAAK;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACZxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,0BAAM;QAAC8C,KAAK,EAAE,CAAC;UAACC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAQ,CAAC,CAAE;QAAA,GAAM/I,KAAK;QAAEuD,IAAI,EAAC,WAAW;QAAAT,QAAA,eAChGzE,OAAA,CAAClC,QAAQ;UACP6D,KAAK,EAAE;YAAEuH,KAAK,EAAE,GAAG;YAAEc,YAAY,EAAE;UAAE;UACrC;UAAA;UACAW,UAAU;UACVC,cAAc,EAAC,oBAAoB;UACnCC,aAAa,EAAC,OAAO;UACrBC,OAAO,EAAE/C,uBAAwB;UACjCgC,WAAW,EAAE,OAAQ;UACrBgB,aAAa,EAAEA,CAACrD,KAAK,EAAEsD,eAAe,KAAKtD,KAAK,CAACzB,IAAI,CAAC,GAAG,CAAG;UAC5DgF,UAAU,EAAE;YACV5H,MAAM,EAAEA,CAAC6H,UAAU,EAAEC,IAAI,KAAIA,IAAI,CAACzG,IAAI,CAAE0G,MAAM,IAAKA,MAAM,CAAC1D,KAAK,CAAC2D,WAAW,CAAC,CAAC,CAACC,OAAO,CAACJ,UAAU,CAACG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UACtH;QAAE;UAAAhG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,EAEV,CAACsC,eAAe,IAAIxI,gBAAgB,CAACiM,UAAU,IAAIzD,eAAe,IAAIxI,gBAAgB,CAACkM,cAAc,kBACrGxL,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,0BAAM;QAAA,GAAK/F,KAAK;QAAA8C,QAAA,eAC/BzE,OAAA,CAACN,UAAU;UAACsC,aAAa,EAAEA,aAAc;UAACC,gBAAgB,EAAEA,gBAAiB;UAAC6F,eAAe,EAAEA;QAAgB;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG,CAAC,eAEdxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,0BAAM;QAAA,GAAK/F,KAAK;QAAEuD,IAAI,EAAC,aAAa;QAAAT,QAAA,eACnDzE,OAAA,CAACrC,MAAM;UACLsN,UAAU;UACVN,UAAU;UACVhJ,KAAK,EAAE;YAAEuH,KAAK,EAAE;UAAI,CAAE;UACtBa,WAAW,EAAC,4CAAS;UACrB0B,QAAQ,EAAE,IAAK;UACfX,OAAO,EAAE,CACP1L,eAAe,CAACD,YAAY,CAACmL,WAAW,CAAC;QACzC;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eACZxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,oBAAK;QAAA,GAAK/F,KAAK;QAAEuD,IAAI,EAAC,YAAY;QAAAT,QAAA,eACjDzE,OAAA,CAACrC,MAAM;UACLsN,UAAU;UACVN,UAAU;UACVhJ,KAAK,EAAE;YAAEuH,KAAK,EAAE;UAAI,CAAE;UACtBa,WAAW,EAAC,sCAAQ;UACpBe,OAAO,EAAE,CACP/L,gBAAgB,CAACC,aAAa,CAAC0M,IAAI,CAAC,EACpC3M,gBAAgB,CAACC,aAAa,CAAC2M,MAAM,CAAC,EACtC5M,gBAAgB,CAACC,aAAa,CAAC4M,GAAG,CAAC;QACnC;UAAAvG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eACXxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,sCAAQ;QAAA,GAAK/F,KAAK;QAAEuD,IAAI,EAAC,YAAY;QAAAT,QAAA,eACpDzE,OAAA,CAACrC;QACA;QAAA;UACAsN,UAAU;UACVN,UAAU;UACVhJ,KAAK,EAAE;YAAEuH,KAAK,EAAE;UAAI,CAAE;UACtBa,WAAW,EAAC,oBAAK;UACjBe,OAAO,EAAE,CACP5L,cAAc,CAACD,WAAW,CAACmL,IAAI,CAAC,EAChClL,cAAc,CAACD,WAAW,CAAC4M,GAAG,CAAC,EAC/B3M,cAAc,CAACD,WAAW,CAAC6M,KAAK,CAAC,EACjC5M,cAAc,CAACD,WAAW,CAAC8M,MAAM,CAAC,EAClC7M,cAAc,CAACD,WAAW,CAAC+M,SAAS,CAAC;QACrC;UAAA3G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eACZxF,OAAA;QAAKgJ,SAAS,EAAC,cAAc;QAAAvE,QAAA,EAAC;MAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAExCxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,oBAAK;QAAC+C,QAAQ;QAAA,GAAK9I,KAAK;QAAA8C,QAAA,gBACtCzE,OAAA,CAACxC,IAAI,CAAC+M,IAAI;UAACrF,IAAI,EAAC,QAAQ;UAACsF,KAAK,EAAE,CAAC;YAACC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAS,CAAC,CAAE;UAACuB,OAAO;UAAAxH,QAAA,eAC7EzE,OAAA,CAACrC,MAAM;YACJsN,UAAU;YACVN,UAAU;YACV;YAAA;YACAZ,WAAW,EAAC,sCAAQ;YACpBmC,wBAAwB,EAAE,KAAM;YAChCvK,KAAK,EAAE;cAAEqI,YAAY,EAAE,CAAC;cAAEd,KAAK,EAAC;YAAI,CAAE;YACtCiD,YAAY,EAAEA,CAACC,KAAK,EAAEhB,MAAM;cAAA,IAAAiB,YAAA;cAAA,OAAK,EAAAA,YAAA,GAACjB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAElG,IAAI,cAAAmH,YAAA,cAAAA,YAAA,GAAI,EAAE,EAAEhB,WAAW,CAAC,CAAC,CAACiB,QAAQ,CAACF,KAAK,CAACf,WAAW,CAAC,CAAC,CAAC;YAAA,CAAC;YAClGP,OAAO,EAAEzK,SAAS,CAACkH,GAAG,CAACgF,IAAI,KAAK;cAC9BxF,GAAG,EAAEwF,IAAI,CAACxF,GAAG,CAACyF,QAAQ,CAAC,CAAC;cACxBpF,KAAK,EAAEmF,IAAI,CAACxF,GAAG,CAACyF,QAAQ,CAAC,CAAC;cAC1BpH,IAAI,EAAEJ,YAAY,CAACuH,IAAI,CAACE,MAAM,EAAEF,IAAI,CAAC7E,KAAK,CAAC;cAC3CA,KAAK,eAAE1H,OAAA;gBAAK2B,KAAK,EAAE;kBAAE+H,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAlF,QAAA,GAAEO,YAAY,CAACuH,IAAI,CAACE,MAAM,EAAEF,IAAI,CAAC7E,KAAK,CAAC,EAAE6E,IAAI,CAAC7E,KAAK;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;cACvHN,IAAI,EAAEqH,IAAI,CAAC7E;YACb,CAAC,CAAC,CAAC,CAACgF,MAAM,CAAClK,iBAAiB,CAAC+E,GAAG,CAACzD,GAAG,KAAK;cACrCiD,GAAG,EAAEjD,GAAG,CAACsD,KAAK;cACdA,KAAK,EAAEtD,GAAG,CAACsD,KAAK;cAChBM,KAAK,EAAE5D,GAAG,CAACsD,KAAK;cAChBlC,IAAI,EAAEpB,GAAG,CAACsD;YACZ,CAAC,CAAC,CAAC;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,EACZgD,kBAAkB,CAAC,QAAQ,CAAC;MAAA;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAEZxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,oBAAK;QAAA,GAAK/F,KAAK;QAAA8C,QAAA,gBAC9BzE,OAAA,CAACxC,IAAI,CAAC+M,IAAI;UAACrF,IAAI,EAAC,QAAQ;UAAC+G,OAAO;UAAAxH,QAAA,eAC9BzE,OAAA,CAACrC,MAAM;YACLsN,UAAU;YACV0B,IAAI,EAAC,UAAU;YACfhC,UAAU;YACVZ,WAAW,EAAC,sCAAQ;YACpBpI,KAAK,EAAE;cAAEqI,YAAY,EAAE,CAAC;cAAEd,KAAK,EAAC;YAAI,CAAE;YACtCiD,YAAY,EAAEA,CAACC,KAAK,EAAEhB,MAAM;cAAA,IAAAwB,aAAA;cAAA,OAAK,EAAAA,aAAA,GAACxB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAElG,IAAI,cAAA0H,aAAA,cAAAA,aAAA,GAAI,EAAE,EAAEvB,WAAW,CAAC,CAAC,CAACiB,QAAQ,CAACF,KAAK,CAACf,WAAW,CAAC,CAAC,CAAC;YAAA,CAAC;YAClGP,OAAO,EAAEzK,SAAS,CAACkH,GAAG,CAACgF,IAAI,KAAK;cAC9BxF,GAAG,EAAEwF,IAAI,CAACxF,GAAG,CAACyF,QAAQ,CAAC,CAAC;cACxBpF,KAAK,EAAEmF,IAAI,CAACxF,GAAG,CAACyF,QAAQ,CAAC,CAAC;cAC1B9E,KAAK,eAAE1H,OAAA;gBAAK2B,KAAK,EAAE;kBAAE+H,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAlF,QAAA,GAAEO,YAAY,CAACuH,IAAI,CAACE,MAAM,EAAEF,IAAI,CAAC7E,KAAK,CAAC,EAAE6E,IAAI,CAAC7E,KAAK;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;cACvHN,IAAI,EAAEqH,IAAI,CAAC7E;YACb,CAAC,CAAC,CAAC,CAACgF,MAAM,CAAClK,iBAAiB,CAAC+E,GAAG,CAACzD,GAAG,KAAK;cACvCiD,GAAG,EAAEjD,GAAG,CAACsD,KAAK;cACdA,KAAK,EAAEtD,GAAG,CAACsD,KAAK;cAChBM,KAAK,EAAE5D,GAAG,CAACsD,KAAK;cAChBlC,IAAI,EAAEpB,GAAG,CAACsD;YACZ,CAAC,CAAC,CAAC;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EACXgD,kBAAkB,CAAC,QAAQ,CAAC;MAAA;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACZxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,cAAI;QAAA,GAAM/F,KAAK;QAAA8C,QAAA,gBAC5BzE,OAAA,CAACxC,IAAI,CAAC+M,IAAI;UAACrF,IAAI,EAAC,UAAU;UAAC+G,OAAO;UAAAxH,QAAA,eAChCzE,OAAA,CAAC1C,KAAK;YAACqE,KAAK,EAAE;cAAEqI,YAAY,EAAE,CAAC;cAAEd,KAAK,EAAC;YAAI,CAAE;YAACe,YAAY,EAAC,KAAK;YAACwB,QAAQ,EAAE;UAAK;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACZxF,OAAA,CAAC3C,MAAM;UAAC0L,IAAI,EAAC,MAAM;UAACJ,OAAO,EAAEX,gBAAiB;UAAAvD,QAAA,EAAC;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAgBZxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;QAAC7C,KAAK,EAAC,cAAI;QAAA,GAAK/F,KAAK;QAAA8C,QAAA,eAC7BzE,OAAA;UAAK2B,KAAK,EAAE;YAACkL,SAAS,EAAC;UAAC,CAAE;UAAApI,QAAA,eACxBzE,OAAA,CAACxC,IAAI,CAAC+M,IAAI;YAACrF,IAAI,EAAC,QAAQ;YAAC+G,OAAO;YAAAxH,QAAA,eAC9BzE,OAAA,CAACH,UAAU;cACT4L,QAAQ,EAAE,IAAK;cACf1B,WAAW,EAAE,EAAG;cAChB+C,WAAW,EACT;gBACEC,YAAY,EAAG;kBACb3L,MAAM,EAAEA,MAAM;kBACdC,MAAM,EAAEA,MAAM;kBACd2L,UAAU,GAAA7L,mBAAA,GAAEzC,SAAS,CAACuC,QAAQ,CAAC,cAAAE,mBAAA,uBAAnBA,mBAAA,CAAqB8L,MAAM;kBACvCjJ,OAAO,EAAE/C;gBACX,CAAC;gBACDiM,SAAS,EAAC,OAAO;gBACjBC,SAAS,EAAC;cACZ;YACD;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,EAEV,CAACsC,eAAe,IAAIxI,gBAAgB,CAACiM,UAAU,IAAIzD,eAAe,IAAIxI,gBAAgB,CAACkM,cAAc,kBAAKxL,OAAA,CAAAE,SAAA;QAAAuE,QAAA,gBACxGzE,OAAA,CAACxC,IAAI,CAAC+M,IAAI;UAAC7C,KAAK,EAAC,cAAI;UAAA,GAAK/F,KAAK;UAAEuD,IAAI,EAAC,eAAe;UAACkI,aAAa,EAAC,SAAS;UAAA3I,QAAA,eAC1EzE,OAAA,CAACvC,QAAQ;YAAEgO,QAAQ,EAAE;UAAK;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACZxF,OAAA,CAACxC,IAAI,CAAC+M,IAAI;UAAC7C,KAAK,EAAC,0BAAM;UAAA,GAAM/F,KAAK;UAAEuD,IAAI,EAAC,WAAW;UAACkI,aAAa,EAAC,SAAS;UAAA3I,QAAA,eACzEzE,OAAA,CAACvC,QAAQ;YAAEgO,QAAQ,EAAE;UAAK;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA,eACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMD,CAAC,eAEPxF,OAAA,CAACL,cAAc;MAAA0F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBxF,OAAA,CAAChC,cAAc;MACbgL,SAAS,EAAC,WAAW;MACrBC,KAAK,EAAC,cAAI;MACVO,QAAQ;MACRN,KAAK,EAAE,GAAI;MACXE,IAAI,EAAEzI,QAAS;MACf0M,QAAQ,EAAEA,CAAA,KAAIzM,WAAW,CAAC,KAAK,CAAE;MACjC0M,IAAI,EAAEA,CAAA,KAAI;QACR1M,WAAW,CAAC,KAAK,CAAC;QAClBJ,eAAe,CAAC,KAAK,CAAC;MACxB,CAAE;MAAAiE,QAAA,eAEFzE,OAAA;QAAK2B,KAAK,EAAE;UAAC4L,SAAS,EAAC,QAAQ;UAACC,MAAM,EAAC;QAAU,CAAE;QAAA/I,QAAA,EAAC;MAEpD;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEtB;AAACtE,EAAA,CArcuBd,UAAU;EAAA,QACN9B,SAAS,EACpBd,IAAI,CAAC+D,OAAO,EAYkKhC,gCAAgC,EAiJvM/B,IAAI,CAACoK,QAAQ,EAChBpK,IAAI,CAACoK,QAAQ;AAAA;AAAA6F,EAAA,GAhKVrN,UAAU;AAAA,IAAAqN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}