{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\issueTrack\\\\views\\\\IssuePartition\\\\CreateIssuePartition.js\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport TEmpty from '@components/TEmpty';\nimport { ExclamationCircleOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';\nimport * as http from \"@common/api/http\";\nimport { track_002_get_issuegrp_info_query } from \"@common/api/query/query\";\nimport { useQuerySetting202_getTeamAllUsers, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\nimport { compareArr, isEmpty } from '@common/utils/ArrayUtils';\nimport { eConsolePropId, eEditingMode, eEnableFlg } from \"@common/utils/enum\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { getQueryableAttrNodeList, transformAttrNodeListForUI, transformCriteriaListForUI, updateObjNodeInfoQuery } from '@common/utils/logicUtils';\nimport * as toolUtil from \"@common/utils/toolUtil\";\nimport { eNodeTypeId, refreshTeamMenu } from \"@common/utils/TsbConfig\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { Button, Drawer, Form, Input, Modal, Space, Spin } from \"antd\";\nimport { useEffect, useRef, useState } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport SearchDisplayField from 'src/quickAcess/views/Search/SearchDetail/SearchDisplayField';\nimport SearchEditTable from \"src/quickAcess/views/Search/SearchDetail/SearchEditTable\";\nimport SearchReultPreviewModal from 'src/quickAcess/views/Search/SearchDetail/SearchReultPreviewModal';\nimport { useQueryTrack005_getSubclass, useQueryTrack019GetPartitionDetail, useQuerySetting409_getTeamAttrgrpProps } from \"../../service/issueHooks\";\nimport AddPartitionAttr from \"./AddPartitionAttr\";\nimport CustomerFormTable from \"./CustomerFormTable\";\nimport \"./IssuePartition.scss\";\nimport DraggableDrawer from \"@components/DraggableDrawer\";\n\n// 新建&编辑 采集口径\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CreateIssuePartition() {\n  _s();\n  var _projectInfo$issueAli3, _projectInfo$issueAli4, _projectInfo$issueAli5, _projectInfo$issueAli6;\n  const {\n    teamId\n  } = useParams();\n  const [form] = Form.useForm();\n  const tRef = useRef(Object.create(null));\n  // 弹窗显示配置\n  const [isModalVisible, setIsModalVisible] = useState(false); //采集口径弹窗\n  const [searchPreFlg, setSearchPreFlg] = useState(false); //是否搜索预览\n  const [searchDisplayFieldOpen, setSearchDisplayFieldOpen] = useState(false); //显示字段弹窗\n  const [isAddAttrModalVisible, setIsAddAttrModalVisible] = useState(false); //添加字段弹窗\n  // 数据配置\n  const [nodeItem, setNodeItem] = useState({}); //节点信息\n  const [opType, setOpType] = useState(eEditingMode.Creating_0); //0:新建 1：编辑\n  const [selectFields, setSelectFields] = useState([]); //已选择的表单字段\n  const [initTableData, setInitTableData] = useState([]); // 初始数据\n  const [attrNodeList, setAttrNodeList] = useState([]); // 字段属性列表\n  const [checkedValues, setCheckedValues] = useState([]); // 显示字段勾选\n  const [modalKey, setModalKey] = useState(toolUtil.guid()); // 显示字段勾选\n  const [showFormFields, setShowFormFields] = useState(true); // 控制表单字段设置的显示\n  // 接口调用\n  //（1）获取subclassid\n  const {\n    subclassNid,\n    projectId,\n    isLoading: isLoadingGetSubclass /*refetch: refetchGetSubclass*/\n  } = useQueryTrack005_getSubclass(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, !!(nodeItem !== null && nodeItem !== void 0 && nodeItem.nodeId));\n  //（2）获取自定义表单字段\n  const {\n    subclassAttrList = [],\n    exprsList = [],\n    isLoading: isLoadingGetSubclassAttrs /*refetch: refetchGetAttrs*/\n  } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, subclassNid, !!subclassNid);\n  // (2.1) 编辑时获取采集口径详情 TODO: useQuery cacheTime 需要在相关key变化时才会清除缓存数据，所以需要一个变量key去触发再次打开的更新操作\n  const {\n    attrList = [],\n    criteriaList = [],\n    bizNodeId,\n    objType,\n    createFlg,\n    isLoading: isLoadingGetGetPartitionDetail /*refetch: refetchGetPartitionDetail*/\n  } = useQueryTrack019GetPartitionDetail(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, modalKey, opType == eEditingMode.Modifying_1 && !!(nodeItem !== null && nodeItem !== void 0 && nodeItem.nodeId) && !isLoadingGetSubclassAttrs);\n  //（3）字典数据\n  const {\n    data: selectionList /*isLoading: isLoadingCodeValueList, refetch: refetchGetValueList*/\n  } = useQuerySetting407_getCodeValueList(teamId);\n  //（4）获取人员列表\n  const {\n    data: userList /*isLoading, refetch: refetchGetUserList*/\n  } = useQuerySetting202_getTeamAllUsers(teamId);\n  // (5) 项目信息\n  const {\n    data: projectInfo = {}\n  } = useQuery(track_002_get_issuegrp_info_query(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, projectId, !!projectId));\n\n  // 新增：动态设置浏览器标题\n  const prevTitle = useRef(document.title);\n  useEffect(() => {\n    if (isModalVisible) {\n      prevTitle.current = document.title;\n      if (opType) {\n        var _projectInfo$issueAli;\n        document.title = `${(_projectInfo$issueAli = projectInfo.issueAlias) !== null && _projectInfo$issueAli !== void 0 ? _projectInfo$issueAli : \"\"}采集口径设置`;\n      } else {\n        var _projectInfo$issueAli2;\n        document.title = `新建${(_projectInfo$issueAli2 = projectInfo.issueAlias) !== null && _projectInfo$issueAli2 !== void 0 ? _projectInfo$issueAli2 : \"\"}采集口径`;\n      }\n    } else {\n      document.title = prevTitle.current;\n    }\n    return () => {\n      document.title = prevTitle.current;\n    };\n  }, [isModalVisible, opType, projectInfo.issueAlias]);\n\n  // 选择样式\n  const selectStyle = {\n    width: 300,\n    borderRadius: 5\n  };\n\n  // 监听打开新建/编辑采集口径弹窗\n  useEffect(() => {\n    globalEventBus.on(\"openCreateIssuePartition\", openCreateIssueProjectEvent);\n    return () => globalEventBus.off(\"openCreateIssuePartition\", openCreateIssueProjectEvent);\n  }, []);\n\n  // 打开创建项目弹窗事件\n  const openCreateIssueProjectEvent = (target, args) => {\n    setModalKey(toolUtil.guid());\n    console.log(\"target,args\", target, args);\n    // 如果nodeType为31703，则为编辑采集口径\n    if (args.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp || args.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp) {\n      setOpType(eEditingMode.Modifying_1);\n    } else {\n      setOpType(eEditingMode.Creating_0);\n    }\n    setNodeItem(args);\n    setIsModalVisible(true);\n  };\n\n  // 打开弹窗/关闭弹窗\n  useEffect(() => {\n    if (isModalVisible && opType && !isLoadingGetGetPartitionDetail) {\n      // 加载数据\n      loadIssuePartitionDetail();\n    } else {\n      // 初始化数据\n      initialIssuePartitionData();\n    }\n  }, [isModalVisible, isLoadingGetGetPartitionDetail]);\n\n  // 打开搜索预览弹窗\n  const openSearchReultPreviewModal = requst => {\n    globalEventBus.emit(\"openSearchReultPreviewModalEvent\", \"\", {\n      searchPreRequest: requst,\n      onDisplayClick: onDisplayClick.bind(this)\n    });\n  };\n\n  // 自定义表单字段\n  useEffect(() => {\n    if (!isLoadingGetSubclassAttrs) {\n      changePropertyTypeList(subclassAttrList);\n    }\n  }, [isLoadingGetSubclassAttrs]);\n\n  // 编辑搜索数据\n  useEffect(() => {\n    if (opType == eEditingMode.Modifying_1 && !isLoadingGetGetPartitionDetail && !isEmpty(attrNodeList)) {\n      changeCriteriaList();\n    }\n  }, [isLoadingGetGetPartitionDetail, attrNodeList]);\n\n  // 自定义表单数据处理\n  const changePropertyTypeList = attrgrpProps => {\n    let attrList = transformAttrNodeListForUI(attrgrpProps, userList, selectionList, true);\n    const checkedValues = attrList.filter(item => item.checked).map(item => item.nodeId);\n    const properTypeListUI = getQueryableAttrNodeList(attrList); //过滤59类型\n    setAttrNodeList(properTypeListUI);\n    setCheckedValues(checkedValues);\n  };\n\n  // 编辑搜索数据\n  const changeCriteriaList = () => {\n    let _criteriaList = transformCriteriaListForUI(criteriaList, attrNodeList, selectionList);\n    setInitTableData(_criteriaList);\n  };\n\n  // 初始化数据\n  function initialIssuePartitionData() {\n    // console.log(\"正在清空数据...\")\n    setInitTableData([]); //清空高级搜索数据\n    setSelectFields([]); //清空字段属性数据\n  }\n\n  // 加载采集口径详情\n  function loadIssuePartitionDetail() {\n    const isCreateEnabled = createFlg == eEnableFlg.enable;\n    form.setFieldsValue({\n      name: nodeItem.name,\n      // name回显\n      createFlg: isCreateEnabled\n    });\n    // 根据createFlg设置表单字段设置的显示状态\n    setShowFormFields(isCreateEnabled);\n    const _attrList = [...attrList];\n    _attrList.forEach(attr => {\n      const propertyList = subclassAttrList.find(subclass => attr.attrNid == subclass.nodeId).propertyList;\n      attr.attrModifyableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\n      attr.attrQueryableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\n    });\n    setSelectFields(_attrList);\n  }\n\n  // 默认值配置\n  const getDefAttrPropValuepByType = (propList = [], type) => {\n    return (propList.find(item => item.propType == type) || {\n      propValue: \"0\"\n    }).propValue;\n  };\n\n  // 取消\n  const handleCancel = () => {\n    setIsModalVisible(false);\n  };\n\n  // 选择字段数据处理\n  const onSelectFields = items => {\n    let fieldList = [];\n    selectFields.filter(el => {\n      items.map((_el, index) => {\n        if (el.attrNid == _el.nodeId) {\n          fieldList.push(el);\n          delete items[index];\n        }\n      });\n    });\n    items.map(item => {\n      let propertyList = item.propertyList;\n      const _attrModifyableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\n      const _attrQueryableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\n      let field = {\n        attrNid: item.nodeId,\n        defaultVal: null,\n        attrVisibleFlg: '',\n        attrModifyableFlg: _attrModifyableFlg,\n        attrModifyableFlgBack: _attrModifyableFlg,\n        attrQueryableFlg: _attrQueryableFlg,\n        attrQueryableFlgBack: _attrQueryableFlg\n      };\n      fieldList.push(field);\n    });\n    setSelectFields(fieldList);\n    setIsAddAttrModalVisible(false);\n  };\n\n  // + 动态条件\n  const handleOnAddSearchCode = () => {\n    tRef.current.addSearchCode();\n  };\n\n  // 搜索预览 无需校验搜索名称\n  const handleSearchPreClick = () => {\n    setSearchPreFlg(true);\n    form.submit();\n  };\n\n  // 显示字段\n  const onDisplayClick = e => {\n    setSearchDisplayFieldOpen(true);\n  };\n\n  // 显示字段数据处理\n  const assembleQueryAttrList = () => {\n    return checkedValues.map(checkedValue => ({\n      attrNid: checkedValue\n    }));\n  };\n\n  // 保存显示字段\n  const handleSearchDisplayFieldOnOk = (e, values) => {\n    setSearchDisplayFieldOpen(false);\n    setCheckedValues(values);\n    globalEventBus.emit(\"changeSearchReultPreviewEvent\", \"\", {\n      values\n    }); // 根据显示字段过滤\n  };\n\n  // 取消保存字段\n  const handleSearchDisplayFieldOnCancel = (e, values) => {\n    if (!compareArr(checkedValues, values)) {\n      Modal.confirm({\n        title: '提醒',\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 23\n        }, this),\n        content: \"放弃后将不会保存显示字段，确定要放弃？\",\n        okText: '确定',\n        cancelText: '取消',\n        onOk: () => {\n          setSearchDisplayFieldOpen(false);\n        }\n      });\n    } else {\n      setSearchDisplayFieldOpen(false);\n    }\n  };\n\n  // 点击 确定\n  const handleSaveClick = () => {\n    setSearchPreFlg(false);\n    form.submit();\n  };\n\n  // 表单提交 form.submit()\n  const onFinish = values => {\n    const {\n      name\n    } = values;\n    if (!searchPreFlg && !name) {\n      // 校验名称\n      return globalUtil.warning(\"请填写采集口径名称!\");\n    }\n    let criteriaList = tRef.current.getCriteriaListForBackend();\n    // 搜索预览\n    if (searchPreFlg) {\n      const queryAttrList = assembleQueryAttrList();\n      let requst = {\n        teamId: teamId,\n        bizNodeId: opType == eEditingMode.Modifying_1 ? bizNodeId : nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId,\n        name: name,\n        objType: opType == eEditingMode.Modifying_1 ? objType : eNodeTypeId.nt_317_objtype_issue_project,\n        advanceQueryFlg: \"1\",\n        criteriaList: criteriaList,\n        queryAttrList: queryAttrList\n      };\n      // 搜索预览\n      requst = {\n        ...requst,\n        pageNum: 1\n      }; //默认查询第一页\n      return openSearchReultPreviewModal(requst);\n    }\n    selectFields.forEach((el, index) => {\n      el.seqNo = index + 1;\n    });\n    if (opType) {\n      // 编辑\n      let params = {\n        teamId,\n        nodeId: nodeItem.nodeId,\n        name,\n        createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable,\n        attrList: selectFields,\n        criteriaList\n      };\n      editPartition(params);\n    } else {\n      // 新建\n      let params = {\n        teamId,\n        objNodeId: nodeItem.nodeId,\n        name,\n        createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable,\n        attrList: selectFields,\n        criteriaList\n      };\n      createPartition(params);\n    }\n  };\n\n  // 新建采集口径\n  function createPartition(params) {\n    http.track_018_create_issue_partition(params).then(result => {\n      if (result.resultCode == 200) {\n        var _result$nodeTree$;\n        refreshTeamMenu();\n        setIsModalVisible(false);\n        nodeItem.callback && nodeItem.callback((result === null || result === void 0 ? void 0 : (_result$nodeTree$ = result.nodeTree[0]) === null || _result$nodeTree$ === void 0 ? void 0 : _result$nodeTree$.children[0]) || {});\n      }\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n\n  // 编辑采集口径\n  function editPartition(params) {\n    http.track_020_modify_issue_partition(params).then(result => {\n      if (result.resultCode == 200) {\n        refreshTeamMenu();\n        setIsModalVisible(false);\n        updateObjNodeInfoQuery(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, {\n          label: params.name,\n          // 更新名称\n          nodeName: params.name // 更新名称\n        }); // 更新节点名称\n      }\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n\n  // 处理\"可新建\"复选框变化\n  const handleCreateFlgChange = e => {\n    const checked = e.target.checked;\n\n    // 如果取消勾选且表单字段设置有值，弹出确认对话框\n    if (!checked && !isEmpty(selectFields)) {\n      Modal.confirm({\n        title: \"提示\",\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 15\n        }, this),\n        content: `取消\"可新建\"功能，会清除表单字段设置，是否取消？`,\n        okText: \"取消\",\n        cancelText: \"不取消\",\n        zIndex: 1002,\n        onOk: () => {\n          // 用户确认取消，清除表单字段设置并隐藏\n          setSelectFields([]);\n          setShowFormFields(false);\n          form.setFieldValue('createFlg', false);\n        },\n        onCancel: () => {\n          // 用户选择不取消，恢复勾选状态\n          form.setFieldValue('createFlg', true);\n        }\n      });\n    } else {\n      // 如果重新勾选，显示表单字段设置\n      setShowFormFields(checked);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n    className: \"tms-drawer IssuePartition\",\n    width: \"60%\",\n    title: opType ? `${(_projectInfo$issueAli3 = projectInfo.issueAlias) !== null && _projectInfo$issueAli3 !== void 0 ? _projectInfo$issueAli3 : \"\"}采集口径设置` : `新建 ${(_projectInfo$issueAli4 = projectInfo.issueAlias) !== null && _projectInfo$issueAli4 !== void 0 ? _projectInfo$issueAli4 : \"\"}采集口径`,\n    destroyOnClose: true,\n    open: isModalVisible,\n    onClose: handleCancel,\n    footer: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"right\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: 20,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            borderRadius: 5\n          },\n          onClick: handleCancel,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          style: {\n            borderRadius: 5\n          },\n          onClick: handleSaveClick,\n          children: \"\\u63D0\\u4EA4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 17\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(Spin, {\n      spinning: isLoadingGetSubclass || isLoadingGetSubclassAttrs,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        labelCol: {\n          span: 4\n        },\n        wrapperCol: {\n          span: 19\n        },\n        onFinish: onFinish,\n        preserve: false // Modal关闭后销毁form字段数据\n        ,\n        autoComplete: \"off\" // 取消自动补充功能\n        ,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"采集口径名称\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            size: 20,\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              required: true,\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                style: selectStyle,\n                autoComplete: \"off\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 22\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"createFlg\",\n              valuePropName: \"checked\",\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                onChange: handleCreateFlgChange,\n                children: \"\\u53EF\\u65B0\\u5EFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 22\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"采集口径条件\",\n          children: isEmpty(attrNodeList) ? /*#__PURE__*/_jsxDEV(TEmpty, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 46\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(SearchEditTable, {\n              attrNodeList: attrNodeList,\n              exprs: exprsList,\n              criteriaList: initTableData,\n              selectionList: selectionList,\n              queryType: 0,\n              ref: tRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-edit-btns\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {\n                  className: \"color-yellow\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 41\n                }, this),\n                onClick: handleOnAddSearchCode,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"+ \\u52A8\\u6001\\u6761\\u4EF6\\uFF08\", /*#__PURE__*/_jsxDEV(FunctionOutlined, {\n                    style: {\n                      margin: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 50\n                  }, this), \"\\uFF09\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 47\n                }, this),\n                className: \"defaultBtn_light\",\n                onClick: handleSearchPreClick,\n                children: \"\\u7ED3\\u679C\\u9884\\u89C8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remarks\",\n              children: [\"\\u5907\\u6CE81\\uFF1A\\u6B64\\u5904\\u7684\\u81EA\\u5B9A\\u4E49\\u641C\\u7D22\\uFF0C\\u7528\\u4E8E\\u663E\\u793A\\u5F53\\u524D\\u91C7\\u96C6\\u53E3\\u5F84\\u5BF9\\u5E94\\u7684\", (_projectInfo$issueAli5 = projectInfo.issueAlias) !== null && _projectInfo$issueAli5 !== void 0 ? _projectInfo$issueAli5 : \"\", \"\\u5217\\u8868\\u3002\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remarks\",\n              children: \"\\u5907\\u6CE82\\uFF1A\\u70B9\\u51FB\\u786E\\u5B9A\\u6309\\u94AE\\u4EC5\\u4FDD\\u5B58\\u8868\\u5355(\\u5373\\u6761\\u4EF6)\\uFF0C\\u9884\\u89C8\\u7ED3\\u679C\\u4E0D\\u505A\\u4FDD\\u5B58\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 17\n        }, this), showFormFields && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"表单字段设置\",\n          children: [/*#__PURE__*/_jsxDEV(CustomerFormTable, {\n            selectFields: selectFields,\n            setSelectFields: setSelectFields,\n            selectionList: selectionList,\n            userList: userList,\n            subclassAttrList: subclassAttrList\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            className: \"fontsize-12\",\n            onClick: () => setIsAddAttrModalVisible(true),\n            children: \"+ \\u6DFB\\u52A0\\u5B57\\u6BB5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"remarks\",\n            children: [\"\\u5907\\u6CE81\\uFF1A\\u5728\\u9879\\u76EE\\u201C\", projectInfo.name, \"\\u201D\\u7684\\u81EA\\u5B9A\\u4E49\\u5B57\\u6BB5\\u7684\\u57FA\\u7840\\u4E0A\\uFF0C\\u8FDB\\u4E00\\u6B65\\u9650\\u7F29\\u4E0A\\u9762\\u8868\\u683C\\u4E2D\\u5B57\\u6BB5\\u7684 \\u201C\\u662F\\u5426\\u663E\\u793A\\u201D\\uFF0C\\u201C\\u53EF\\u4FEE\\u6539\\u201D\\uFF0C\\u201C\\u53EF\\u641C\\u7D22\\u201D\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"remarks\",\n            children: [\"\\u5907\\u6CE82\\uFF1A\\u5728\\u5F53\\u524D\\u91C7\\u96C6\\u53E3\\u5F84\\u4E2D\\u63D0\\u4EA4\\u7684\\u65B0\\u5EFA\", (_projectInfo$issueAli6 = projectInfo.issueAlias) !== null && _projectInfo$issueAli6 !== void 0 ? _projectInfo$issueAli6 : \"\", \"\\uFF0C\\u5176\\u5BF9\\u5E94\\u4E0A\\u9762\\u8868\\u683C\\u4E2D\\u7684\\u5B57\\u6BB5\\uFF0C\\u5982\\u679C\\u503C\\u4E3A\\u7A7A\\uFF0C\\u5219\\u4F7F\\u7528\\u201D\\u7F3A\\u7701\\u503C\\u201D\\u5217\\u4E2D\\u7684\\u503C\\uFF0C\\u8FDB\\u884C\\u6700\\u540E\\u7684\\u6570\\u636E\\u4FDD\\u5B58\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 37\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AddPartitionAttr, {\n      selectFields: selectFields,\n      attrList: subclassAttrList,\n      visible: isAddAttrModalVisible,\n      onSelectFields: onSelectFields,\n      onCancel: () => setIsAddAttrModalVisible(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(SearchReultPreviewModal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(SearchDisplayField, {\n      open: searchDisplayFieldOpen,\n      onOk: handleSearchDisplayFieldOnOk,\n      onCancel: handleSearchDisplayFieldOnCancel,\n      nodeName: nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeName,\n      checkedValues: checkedValues,\n      attrNodeList: attrNodeList\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 359,\n    columnNumber: 12\n  }, this);\n}\n_s(CreateIssuePartition, \"BCfjgUz9n1krSDJQMPj3mizjncE=\", false, function () {\n  return [useParams, Form.useForm, useQueryTrack005_getSubclass, useQuerySetting409_getTeamAttrgrpProps, useQueryTrack019GetPartitionDetail, useQuerySetting407_getCodeValueList, useQuerySetting202_getTeamAllUsers, useQuery];\n});\n_c = CreateIssuePartition;\nvar _c;\n$RefreshReg$(_c, \"CreateIssuePartition\");", "map": {"version": 3, "names": ["TEmpty", "ExclamationCircleOutlined", "FunctionOutlined", "QuestionCircleOutlined", "SearchOutlined", "http", "track_002_get_issuegrp_info_query", "useQuerySetting202_getTeamAllUsers", "useQuerySetting407_getCodeValueList", "compareArr", "isEmpty", "eConsolePropId", "eEditingMode", "eEnableFlg", "globalEventBus", "globalUtil", "getQueryableAttrNodeList", "transformAttrNodeListForUI", "transformCriteriaListForUI", "updateObjNodeInfoQuery", "toolUtil", "eNodeTypeId", "refreshTeamMenu", "useQuery", "<PERSON><PERSON>", "Drawer", "Form", "Input", "Modal", "Space", "Spin", "useEffect", "useRef", "useState", "useParams", "SearchDisplayField", "SearchEditTable", "SearchReultPreviewModal", "useQueryTrack005_getSubclass", "useQueryTrack019GetPartitionDetail", "useQuerySetting409_getTeamAttrgrpProps", "AddPartitionAttr", "CustomerFormTable", "DraggableDrawer", "jsxDEV", "_jsxDEV", "CreateIssuePartition", "_s", "_projectInfo$issueAli3", "_projectInfo$issueAli4", "_projectInfo$issueAli5", "_projectInfo$issueAli6", "teamId", "form", "useForm", "tRef", "Object", "create", "isModalVisible", "setIsModalVisible", "searchPreFlg", "setSearchPreFlg", "searchDisplayFieldOpen", "setSearchDisplayFieldOpen", "isAddAttrModalVisible", "setIsAddAttrModalVisible", "nodeItem", "setNodeItem", "opType", "setOpType", "Creating_0", "selectFields", "setSelectFields", "initTableData", "setInitTableData", "attrNodeList", "setAttrNodeList", "checkedValues", "setCheckedValues", "modalKey", "setModalKey", "guid", "showFormFields", "<PERSON>S<PERSON><PERSON><PERSON><PERSON><PERSON>s", "subclassNid", "projectId", "isLoading", "isLoadingGetSubclass", "nodeId", "subclassAttrList", "exprsList", "isLoadingGetSubclassAttrs", "attrList", "criteriaList", "bizNodeId", "objType", "createFlg", "isLoadingGetGetPartitionDetail", "Modifying_1", "data", "selectionList", "userList", "projectInfo", "prevTitle", "document", "title", "current", "_projectInfo$issueAli", "issueAlias", "_projectInfo$issueAli2", "selectStyle", "width", "borderRadius", "on", "openCreateIssueProjectEvent", "off", "target", "args", "console", "log", "nodeType", "nt_31703_objtype_issue_project_adhoc_grp", "loadIssuePartitionDetail", "initialIssuePartitionData", "openSearchReultPreviewModal", "requst", "emit", "searchPreRequest", "onDisplayClick", "bind", "changePropertyTypeList", "changeCriteriaList", "attrgrpProps", "filter", "item", "checked", "map", "properTypeListUI", "_criteriaList", "isCreateEnabled", "enable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "_attrList", "for<PERSON>ach", "attr", "propertyList", "find", "subclass", "attrNid", "attrModifyableFlgBack", "getDefAttrPropValuepByType", "Prop_60_modifiable", "attrQueryableFlgBack", "Prop_59_queryable", "propList", "type", "propType", "propValue", "handleCancel", "onSelectFields", "items", "fieldList", "el", "_el", "index", "push", "_attrModifyableFlg", "_attrQueryableFlg", "field", "defaultVal", "attrVisibleFlg", "attrModifyableFlg", "attrQueryableFlg", "handleOnAddSearchCode", "addSearchCode", "handleSearchPreClick", "submit", "e", "assembleQueryAttrList", "checkedValue", "handleSearchDisplayFieldOnOk", "values", "handleSearchDisplayFieldOnCancel", "confirm", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "okText", "cancelText", "onOk", "handleSaveClick", "onFinish", "warning", "getCriteriaListForBackend", "queryAttrList", "nt_317_objtype_issue_project", "advanceQueryFlg", "pageNum", "seqNo", "params", "disable", "editPartition", "objNodeId", "createPartition", "track_018_create_issue_partition", "then", "result", "resultCode", "_result$nodeTree$", "callback", "nodeTree", "children", "catch", "err", "track_020_modify_issue_partition", "label", "nodeName", "handleCreateFlgChange", "zIndex", "setFieldValue", "onCancel", "className", "destroyOnClose", "open", "onClose", "footer", "style", "textAlign", "size", "onClick", "spinning", "labelCol", "span", "wrapperCol", "preserve", "autoComplete", "<PERSON><PERSON>", "required", "noStyle", "valuePropName", "Checkbox", "onChange", "exprs", "queryType", "ref", "margin", "visible", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/issueTrack/views/IssuePartition/CreateIssuePartition.js"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport TEmpty from '@components/TEmpty';\r\nimport { ExclamationCircleOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';\r\nimport * as http from \"@common/api/http\";\r\nimport { track_002_get_issuegrp_info_query } from \"@common/api/query/query\";\r\nimport { useQuerySetting202_getTeamAllUsers, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\r\nimport { compareArr, isEmpty } from '@common/utils/ArrayUtils';\r\nimport { eConsolePropId, eEditingMode, eEnableFlg } from \"@common/utils/enum\";\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { getQueryableAttrNodeList, transformAttrNodeListForUI, transformCriteriaListForUI, updateObjNodeInfoQuery } from '@common/utils/logicUtils';\r\nimport * as toolUtil from \"@common/utils/toolUtil\";\r\nimport { eNodeTypeId, refreshTeamMenu } from \"@common/utils/TsbConfig\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { Button, Drawer, Form, Input, Modal, Space, Spin } from \"antd\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport SearchDisplayField from 'src/quickAcess/views/Search/SearchDetail/SearchDisplayField';\r\nimport SearchEditTable from \"src/quickAcess/views/Search/SearchDetail/SearchEditTable\";\r\nimport SearchReultPreviewModal from 'src/quickAcess/views/Search/SearchDetail/SearchReultPreviewModal';\r\nimport { useQueryTrack005_getSubclass, useQueryTrack019GetPartitionDetail, useQuerySetting409_getTeamAttrgrpProps } from \"../../service/issueHooks\";\r\nimport AddPartitionAttr from \"./AddPartitionAttr\";\r\nimport CustomerFormTable from \"./CustomerFormTable\";\r\nimport \"./IssuePartition.scss\";\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\n\r\n// 新建&编辑 采集口径\r\nexport default function CreateIssuePartition() {\r\n    const { teamId } = useParams();\r\n    const [form] = Form.useForm();\r\n    const tRef = useRef(Object.create(null));\r\n    // 弹窗显示配置\r\n    const [isModalVisible, setIsModalVisible] = useState(false); //采集口径弹窗\r\n    const [searchPreFlg, setSearchPreFlg] = useState(false); //是否搜索预览\r\n    const [searchDisplayFieldOpen, setSearchDisplayFieldOpen] = useState(false); //显示字段弹窗\r\n    const [isAddAttrModalVisible, setIsAddAttrModalVisible] = useState(false); //添加字段弹窗\r\n    // 数据配置\r\n    const [nodeItem, setNodeItem] = useState({}); //节点信息\r\n    const [opType, setOpType] = useState(eEditingMode.Creating_0 ); //0:新建 1：编辑\r\n    const [selectFields, setSelectFields] = useState([]); //已选择的表单字段\r\n    const [initTableData, setInitTableData] = useState([]); // 初始数据\r\n    const [attrNodeList, setAttrNodeList] = useState([]); // 字段属性列表\r\n    const [checkedValues, setCheckedValues] = useState([]); // 显示字段勾选\r\n    const [modalKey, setModalKey] = useState(toolUtil.guid()); // 显示字段勾选\r\n    const [showFormFields, setShowFormFields] = useState(true); // 控制表单字段设置的显示\r\n    // 接口调用\r\n    //（1）获取subclassid\r\n    const { subclassNid, projectId, isLoading: isLoadingGetSubclass, /*refetch: refetchGetSubclass*/ } = useQueryTrack005_getSubclass(teamId, nodeItem?.nodeId, !!nodeItem?.nodeId);\r\n    //（2）获取自定义表单字段\r\n    const { subclassAttrList=[], exprsList=[], isLoading: isLoadingGetSubclassAttrs, /*refetch: refetchGetAttrs*/ } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeItem?.nodeId, subclassNid, !!subclassNid);\r\n    // (2.1) 编辑时获取采集口径详情 TODO: useQuery cacheTime 需要在相关key变化时才会清除缓存数据，所以需要一个变量key去触发再次打开的更新操作\r\n    const { attrList=[], criteriaList=[], bizNodeId, objType, createFlg, isLoading: isLoadingGetGetPartitionDetail, /*refetch: refetchGetPartitionDetail*/ } = useQueryTrack019GetPartitionDetail(teamId, nodeItem?.nodeId, modalKey, opType == eEditingMode.Modifying_1 && !!nodeItem?.nodeId && !isLoadingGetSubclassAttrs );\r\n    //（3）字典数据\r\n    const { data: selectionList, /*isLoading: isLoadingCodeValueList, refetch: refetchGetValueList*/ } = useQuerySetting407_getCodeValueList(teamId);\r\n    //（4）获取人员列表\r\n    const { data: userList, /*isLoading, refetch: refetchGetUserList*/ } = useQuerySetting202_getTeamAllUsers(teamId);\r\n    // (5) 项目信息\r\n    const {data: projectInfo = {}} = useQuery(track_002_get_issuegrp_info_query(teamId, nodeItem?.nodeId, projectId, !!projectId))\r\n    \r\n    // 新增：动态设置浏览器标题\r\n    const prevTitle = useRef(document.title);\r\n    useEffect(() => {\r\n      if (isModalVisible) {\r\n        prevTitle.current = document.title;\r\n        if (opType) {\r\n          document.title = `${projectInfo.issueAlias ?? \"\"}采集口径设置`;\r\n        } else {\r\n          document.title = `新建${projectInfo.issueAlias ?? \"\"}采集口径`;\r\n        }\r\n      } else {\r\n        document.title = prevTitle.current;\r\n      }\r\n      return () => {\r\n        document.title = prevTitle.current;\r\n      };\r\n    }, [isModalVisible, opType, projectInfo.issueAlias]);\r\n    \r\n    // 选择样式\r\n    const selectStyle = { width: 300, borderRadius: 5 };\r\n\r\n    // 监听打开新建/编辑采集口径弹窗\r\n    useEffect(() => {\r\n        globalEventBus.on(\"openCreateIssuePartition\", openCreateIssueProjectEvent)\r\n        return () => globalEventBus.off(\"openCreateIssuePartition\", openCreateIssueProjectEvent)\r\n    }, [])\r\n\r\n    // 打开创建项目弹窗事件\r\n    const openCreateIssueProjectEvent = (target, args) => {\r\n        setModalKey(toolUtil.guid());\r\n        console.log(\"target,args\", target, args)\r\n        // 如果nodeType为31703，则为编辑采集口径\r\n        if (args.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp || args.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp) {\r\n            setOpType(eEditingMode.Modifying_1 )\r\n        } else {\r\n            setOpType(eEditingMode.Creating_0 )\r\n        }\r\n        setNodeItem(args);\r\n        setIsModalVisible(true);\r\n    }\r\n\r\n    // 打开弹窗/关闭弹窗\r\n    useEffect(() => {\r\n      if (isModalVisible && opType && !isLoadingGetGetPartitionDetail) {\r\n          // 加载数据\r\n          loadIssuePartitionDetail()\r\n      } else {\r\n          // 初始化数据\r\n          initialIssuePartitionData()\r\n      }\r\n    }, [isModalVisible, isLoadingGetGetPartitionDetail])\r\n\r\n    // 打开搜索预览弹窗\r\n    const openSearchReultPreviewModal = (requst) => {\r\n        globalEventBus.emit(\"openSearchReultPreviewModalEvent\", \"\", { searchPreRequest: requst, onDisplayClick: onDisplayClick.bind(this) });\r\n    }\r\n\r\n    // 自定义表单字段\r\n    useEffect(() => {\r\n        if (!isLoadingGetSubclassAttrs) {\r\n          changePropertyTypeList(subclassAttrList)\r\n        }\r\n    }, [isLoadingGetSubclassAttrs])\r\n\r\n    // 编辑搜索数据\r\n    useEffect(() => {\r\n      if (opType == eEditingMode.Modifying_1 && !isLoadingGetGetPartitionDetail && !isEmpty(attrNodeList)) {\r\n        changeCriteriaList()\r\n      }\r\n  }, [isLoadingGetGetPartitionDetail, attrNodeList])\r\n\r\n    // 自定义表单数据处理\r\n    const changePropertyTypeList = (attrgrpProps) => {\r\n        let attrList = transformAttrNodeListForUI(attrgrpProps, userList, selectionList, true);\r\n        const checkedValues = attrList.filter(item => item.checked).map(item => item.nodeId);\r\n        const properTypeListUI = getQueryableAttrNodeList(attrList); //过滤59类型\r\n        setAttrNodeList(properTypeListUI);\r\n        setCheckedValues(checkedValues);\r\n    }\r\n\r\n    // 编辑搜索数据\r\n    const changeCriteriaList = () =>{\r\n      let _criteriaList = transformCriteriaListForUI(criteriaList, attrNodeList, selectionList);\r\n      setInitTableData(_criteriaList);\r\n    }\r\n\r\n    // 初始化数据\r\n    function initialIssuePartitionData() {\r\n        // console.log(\"正在清空数据...\")\r\n        setInitTableData([]); //清空高级搜索数据\r\n        setSelectFields([]); //清空字段属性数据\r\n    }\r\n\r\n    // 加载采集口径详情\r\n    function loadIssuePartitionDetail() {\r\n      const isCreateEnabled = createFlg == eEnableFlg.enable;\r\n        form.setFieldsValue({\r\n            name: nodeItem.name,// name回显\r\n            createFlg: isCreateEnabled\r\n        });\r\n         // 根据createFlg设置表单字段设置的显示状态\r\n          setShowFormFields(isCreateEnabled);\r\n\r\n        const _attrList = [...attrList];\r\n        _attrList.forEach((attr)=>{\r\n          const propertyList = subclassAttrList.find(subclass =>  attr.attrNid == subclass.nodeId).propertyList;\r\n          attr.attrModifyableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\r\n          attr.attrQueryableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\r\n        })\r\n        setSelectFields(_attrList)\r\n    }\r\n\r\n    // 默认值配置\r\n    const getDefAttrPropValuepByType = (propList = [], type) => {\r\n        return (propList.find((item) => item.propType == type) || { propValue: \"0\" }).propValue;\r\n    }\r\n\r\n    // 取消\r\n    const handleCancel = () => {\r\n        setIsModalVisible(false)\r\n    }\r\n\r\n    // 选择字段数据处理\r\n    const onSelectFields = (items) => {\r\n        let fieldList = []\r\n        selectFields.filter(el => {\r\n            items.map((_el, index) => {\r\n                if (el.attrNid == _el.nodeId) {\r\n                    fieldList.push(el)\r\n                    delete items[index]\r\n                }\r\n            })\r\n        })\r\n        items.map((item) => {\r\n            let propertyList = item.propertyList\r\n            const _attrModifyableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\r\n            const _attrQueryableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\r\n            let field = {\r\n                attrNid: item.nodeId,\r\n                defaultVal: null,\r\n                attrVisibleFlg: '',\r\n                attrModifyableFlg: _attrModifyableFlg,\r\n                attrModifyableFlgBack: _attrModifyableFlg,\r\n                attrQueryableFlg: _attrQueryableFlg,\r\n                attrQueryableFlgBack: _attrQueryableFlg,\r\n            }\r\n            fieldList.push(field)\r\n        })\r\n        setSelectFields(fieldList)\r\n        setIsAddAttrModalVisible(false)\r\n    }\r\n\r\n    // + 动态条件\r\n    const handleOnAddSearchCode = () => {\r\n        tRef.current.addSearchCode();\r\n    }\r\n\r\n    // 搜索预览 无需校验搜索名称\r\n    const handleSearchPreClick = () => {\r\n        setSearchPreFlg(true);\r\n        form.submit();\r\n    }\r\n\r\n    // 显示字段\r\n    const onDisplayClick = (e) => {\r\n        setSearchDisplayFieldOpen(true);\r\n    };\r\n\r\n    // 显示字段数据处理\r\n    const assembleQueryAttrList = () => {\r\n        return checkedValues.map(checkedValue => ({ attrNid: checkedValue }));\r\n    }\r\n\r\n    // 保存显示字段\r\n    const handleSearchDisplayFieldOnOk = (e, values) => {\r\n        setSearchDisplayFieldOpen(false);\r\n        setCheckedValues(values);\r\n        globalEventBus.emit(\"changeSearchReultPreviewEvent\", \"\", { values }) // 根据显示字段过滤\r\n    }\r\n\r\n    // 取消保存字段\r\n    const handleSearchDisplayFieldOnCancel = (e, values) => {\r\n        if (!compareArr(checkedValues, values)) {\r\n            Modal.confirm({\r\n                title: '提醒',\r\n                icon: <ExclamationCircleOutlined />,\r\n                content: \"放弃后将不会保存显示字段，确定要放弃？\",\r\n                okText: '确定',\r\n                cancelText: '取消',\r\n                onOk: () => { setSearchDisplayFieldOpen(false); }\r\n            });\r\n        } else {\r\n            setSearchDisplayFieldOpen(false);\r\n        }\r\n    };\r\n\r\n    // 点击 确定\r\n    const handleSaveClick = () => {\r\n        setSearchPreFlg(false);\r\n        form.submit();\r\n    }\r\n\r\n    // 表单提交 form.submit()\r\n    const onFinish = (values) => {\r\n        const { name } = values;\r\n        if (!searchPreFlg && !name) {\r\n            // 校验名称\r\n            return globalUtil.warning(\"请填写采集口径名称!\")\r\n        }\r\n        let criteriaList = tRef.current.getCriteriaListForBackend()\r\n        // 搜索预览\r\n        if (searchPreFlg) {\r\n            const queryAttrList = assembleQueryAttrList();\r\n            let requst = {\r\n                teamId: teamId,\r\n                bizNodeId: opType == eEditingMode.Modifying_1 ? bizNodeId : nodeItem?.nodeId,\r\n                name: name,\r\n                objType: opType == eEditingMode.Modifying_1 ? objType : eNodeTypeId.nt_317_objtype_issue_project,\r\n                advanceQueryFlg: \"1\",\r\n                criteriaList: criteriaList,\r\n                queryAttrList: queryAttrList\r\n            }\r\n            // 搜索预览\r\n            requst = { ...requst, pageNum: 1 } //默认查询第一页\r\n            return openSearchReultPreviewModal(requst);\r\n        }\r\n        selectFields.forEach((el, index) => {\r\n            el.seqNo = index + 1\r\n        })\r\n        if (opType) {\r\n            // 编辑\r\n            let params = { teamId, nodeId: nodeItem.nodeId, name, createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable, attrList: selectFields, criteriaList }\r\n            editPartition(params);\r\n        } else {\r\n            // 新建\r\n            let params = { teamId, objNodeId: nodeItem.nodeId, name, createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable, attrList: selectFields, criteriaList }\r\n            createPartition(params);\r\n        }\r\n    };\r\n\r\n    // 新建采集口径\r\n    function createPartition(params) {\r\n        http.track_018_create_issue_partition(params).then(result => {\r\n            if (result.resultCode == 200) {\r\n                refreshTeamMenu();\r\n                setIsModalVisible(false);\r\n                nodeItem.callback && nodeItem.callback(result?.nodeTree[0]?.children[0] || {});\r\n            }\r\n        }).catch(err => {\r\n            console.log(err)\r\n        })\r\n    }\r\n\r\n    // 编辑采集口径\r\n    function editPartition(params) {\r\n        http.track_020_modify_issue_partition(params).then(result => {\r\n            if (result.resultCode == 200) {\r\n                refreshTeamMenu();\r\n                setIsModalVisible(false);\r\n                updateObjNodeInfoQuery(teamId, nodeItem?.nodeId, {\r\n                    label: params.name,   // 更新名称\r\n                    nodeName: params.name, // 更新名称\r\n                  });// 更新节点名称\r\n            }\r\n        }).catch(err => {\r\n            console.log(err)\r\n        })\r\n    }\r\n\r\n    // 处理\"可新建\"复选框变化\r\n  const handleCreateFlgChange = (e) => {\r\n    const checked = e.target.checked;\r\n\r\n    // 如果取消勾选且表单字段设置有值，弹出确认对话框\r\n    if (!checked && !isEmpty(selectFields)) {\r\n      Modal.confirm({\r\n        title: \"提示\",\r\n        icon: <ExclamationCircleOutlined />,\r\n        content: `取消\"可新建\"功能，会清除表单字段设置，是否取消？`,\r\n        okText: \"取消\",\r\n        cancelText: \"不取消\",\r\n        zIndex: 1002,\r\n        onOk: () => {\r\n          // 用户确认取消，清除表单字段设置并隐藏\r\n          setSelectFields([]);\r\n          setShowFormFields(false);\r\n          form.setFieldValue('createFlg', false);\r\n        },\r\n        onCancel: () => {\r\n          // 用户选择不取消，恢复勾选状态\r\n          form.setFieldValue('createFlg', true);\r\n        },\r\n      });\r\n    } else {\r\n      // 如果重新勾选，显示表单字段设置\r\n      setShowFormFields(checked);\r\n    } \r\n  }\r\n\r\n    return <DraggableDrawer\r\n        className=\"tms-drawer IssuePartition\"\r\n        width={\"60%\"}\r\n        title={opType ? `${projectInfo.issueAlias??\"\"}采集口径设置` : `新建 ${projectInfo.issueAlias??\"\"}采集口径`}\r\n        destroyOnClose={true}\r\n        open={isModalVisible}\r\n        onClose={handleCancel}\r\n        footer={<div style={{ textAlign: \"right\" }} >\r\n            <Space size={20}>\r\n                <Button style={{ borderRadius: 5 }} onClick={handleCancel}>取消</Button>\r\n                <Button type=\"primary\" style={{ borderRadius: 5 }} onClick={handleSaveClick}>提交</Button>\r\n            </Space>\r\n        </div>}\r\n    >\r\n        <Spin spinning={isLoadingGetSubclass || isLoadingGetSubclassAttrs}>\r\n            <Form\r\n                form={form}\r\n                labelCol={{ span: 4 }}\r\n                wrapperCol={{ span: 19 }}\r\n                onFinish={onFinish}\r\n                preserve={false}// Modal关闭后销毁form字段数据\r\n                autoComplete={\"off\"} // 取消自动补充功能\r\n            >\r\n                <Form.Item label={\"采集口径名称\"}>\r\n                  <Space size={20}>\r\n                     <Form.Item name=\"name\" required={true} noStyle>\r\n                      <Input style={selectStyle} autoComplete=\"off\"/>\r\n                     </ Form.Item>\r\n                     <Form.Item name=\"createFlg\" valuePropName=\"checked\" noStyle>\r\n                      <Checkbox onChange={handleCreateFlgChange}>可新建</Checkbox>\r\n                     </Form.Item>        \r\n                  </Space>\r\n                </Form.Item>\r\n                <Form.Item label={\"采集口径条件\"}>\r\n                    {isEmpty(attrNodeList) ? <TEmpty/> :\r\n                        <div>\r\n                            <SearchEditTable attrNodeList={attrNodeList} exprs={exprsList} criteriaList={initTableData} selectionList={selectionList} queryType={0} ref={tRef} />\r\n                            <div className=\"search-edit-btns\">\r\n                                <Button \r\n                                    type=\"link\" \r\n                                    icon={\r\n                                        <QuestionCircleOutlined className=\"color-yellow\" />\r\n                                    } \r\n                                    onClick={handleOnAddSearchCode}\r\n                                >\r\n                                    <span>+ 动态条件（<FunctionOutlined style={{ margin: 0 }} />）</span>\r\n                                </Button>\r\n                                <Button icon={<SearchOutlined />} className=\"defaultBtn_light\" onClick={handleSearchPreClick} >结果预览</Button>\r\n                            </div>\r\n                            <div className=\"remarks\">备注1：此处的自定义搜索，用于显示当前采集口径对应的{projectInfo.issueAlias??\"\"}列表。</div>\r\n                            <div className=\"remarks\">备注2：点击确定按钮仅保存表单(即条件)，预览结果不做保存。</div>\r\n                        </div>\r\n                    }\r\n                </Form.Item>\r\n                {\r\n                  showFormFields && <Form.Item label={\"表单字段设置\"}>\r\n                    <CustomerFormTable selectFields={selectFields} setSelectFields={setSelectFields} selectionList={selectionList} userList={userList} subclassAttrList={subclassAttrList} />\r\n                    <a className=\"fontsize-12\" onClick={() => setIsAddAttrModalVisible(true)}>+ 添加字段</a>\r\n                    <div className=\"remarks\">备注1：在项目“{projectInfo.name}”的自定义字段的基础上，进一步限缩上面表格中字段的 “是否显示”，“可修改”，“可搜索”。</div>\r\n                    <div className=\"remarks\">备注2：在当前采集口径中提交的新建{projectInfo.issueAlias??\"\"}，其对应上面表格中的字段，如果值为空，则使用”缺省值”列中的值，进行最后的数据保存。</div>\r\n                </Form.Item>\r\n                }\r\n            </Form>\r\n        </Spin>\r\n        {/* 添加字段 */}\r\n        <AddPartitionAttr selectFields={selectFields}\r\n                          attrList={subclassAttrList}\r\n                          visible={isAddAttrModalVisible}\r\n                          onSelectFields={onSelectFields}\r\n                          onCancel={() => setIsAddAttrModalVisible(false)} />\r\n        {/* 搜索结果预览 */}\r\n        <SearchReultPreviewModal />\r\n        {/* 显示字段 */}\r\n        <SearchDisplayField\r\n            open={searchDisplayFieldOpen}\r\n            onOk={handleSearchDisplayFieldOnOk}\r\n            onCancel={handleSearchDisplayFieldOnCancel}\r\n            nodeName={nodeItem?.nodeName}\r\n            checkedValues={checkedValues}\r\n            attrNodeList={attrNodeList}\r\n        />\r\n    </DraggableDrawer>\r\n}"], "mappings": ";;AAAA;AACA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,SAASC,yBAAyB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,cAAc,QAAQ,mBAAmB;AACvH,OAAO,KAAKC,IAAI,MAAM,kBAAkB;AACxC,SAASC,iCAAiC,QAAQ,yBAAyB;AAC3E,SAASC,kCAAkC,EAAEC,mCAAmC,QAAQ,6BAA6B;AACrH,SAASC,UAAU,EAAEC,OAAO,QAAQ,0BAA0B;AAC9D,SAASC,cAAc,EAAEC,YAAY,EAAEC,UAAU,QAAQ,oBAAoB;AAC7E,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,wBAAwB,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,sBAAsB,QAAQ,0BAA0B;AACnJ,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,SAASC,WAAW,EAAEC,eAAe,QAAQ,yBAAyB;AACtE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AACtE,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,kBAAkB,MAAM,6DAA6D;AAC5F,OAAOC,eAAe,MAAM,0DAA0D;AACtF,OAAOC,uBAAuB,MAAM,kEAAkE;AACtG,SAASC,4BAA4B,EAAEC,kCAAkC,EAAEC,sCAAsC,QAAQ,0BAA0B;AACnJ,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAO,uBAAuB;AAC9B,OAAOC,eAAe,MAAM,6BAA6B;;AAEzD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,eAAe,SAASC,oBAAoBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC3C,MAAM;IAAEC;EAAO,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACmB,IAAI,CAAC,GAAG3B,IAAI,CAAC4B,OAAO,CAAC,CAAC;EAC7B,MAAMC,IAAI,GAAGvB,MAAM,CAACwB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACxC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC6B,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7E,MAAM,CAAC+B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3E;EACA,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAACrB,YAAY,CAAC0D,UAAW,CAAC,CAAC,CAAC;EAChE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAACb,QAAQ,CAAC6D,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D;EACA;EACA,MAAM;IAAEmD,WAAW;IAAEC,SAAS;IAAEC,SAAS,EAAEC,oBAAoB,CAAE;EAAgC,CAAC,GAAGjD,4BAA4B,CAACc,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,MAAM,EAAE,CAAC,EAACtB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEsB,MAAM,EAAC;EAC/K;EACA,MAAM;IAAEC,gBAAgB,GAAC,EAAE;IAAEC,SAAS,GAAC,EAAE;IAAEJ,SAAS,EAAEK,yBAAyB,CAAE;EAA6B,CAAC,GAAGnD,sCAAsC,CAACY,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,MAAM,EAAEJ,WAAW,EAAE,CAAC,CAACA,WAAW,CAAC;EAC9M;EACA,MAAM;IAAEQ,QAAQ,GAAC,EAAE;IAAEC,YAAY,GAAC,EAAE;IAAEC,SAAS;IAAEC,OAAO;IAAEC,SAAS;IAAEV,SAAS,EAAEW,8BAA8B,CAAE;EAAuC,CAAC,GAAG1D,kCAAkC,CAACa,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,MAAM,EAAET,QAAQ,EAAEX,MAAM,IAAIxD,YAAY,CAACsF,WAAW,IAAI,CAAC,EAAChC,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEsB,MAAM,KAAI,CAACG,yBAA0B,CAAC;EAC1T;EACA,MAAM;IAAEQ,IAAI,EAAEC,aAAa,CAAE;EAAoE,CAAC,GAAG5F,mCAAmC,CAAC4C,MAAM,CAAC;EAChJ;EACA,MAAM;IAAE+C,IAAI,EAAEE,QAAQ,CAAE;EAA2C,CAAC,GAAG9F,kCAAkC,CAAC6C,MAAM,CAAC;EACjH;EACA,MAAM;IAAC+C,IAAI,EAAEG,WAAW,GAAG,CAAC;EAAC,CAAC,GAAG/E,QAAQ,CAACjB,iCAAiC,CAAC8C,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,MAAM,EAAEH,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC,CAAC;;EAE9H;EACA,MAAMkB,SAAS,GAAGvE,MAAM,CAACwE,QAAQ,CAACC,KAAK,CAAC;EACxC1E,SAAS,CAAC,MAAM;IACd,IAAI2B,cAAc,EAAE;MAClB6C,SAAS,CAACG,OAAO,GAAGF,QAAQ,CAACC,KAAK;MAClC,IAAIrC,MAAM,EAAE;QAAA,IAAAuC,qBAAA;QACVH,QAAQ,CAACC,KAAK,GAAG,IAAAE,qBAAA,GAAGL,WAAW,CAACM,UAAU,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,QAAQ;MAC1D,CAAC,MAAM;QAAA,IAAAE,sBAAA;QACLL,QAAQ,CAACC,KAAK,GAAG,MAAAI,sBAAA,GAAKP,WAAW,CAACM,UAAU,cAAAC,sBAAA,cAAAA,sBAAA,GAAI,EAAE,MAAM;MAC1D;IACF,CAAC,MAAM;MACLL,QAAQ,CAACC,KAAK,GAAGF,SAAS,CAACG,OAAO;IACpC;IACA,OAAO,MAAM;MACXF,QAAQ,CAACC,KAAK,GAAGF,SAAS,CAACG,OAAO;IACpC,CAAC;EACH,CAAC,EAAE,CAAChD,cAAc,EAAEU,MAAM,EAAEkC,WAAW,CAACM,UAAU,CAAC,CAAC;;EAEpD;EACA,MAAME,WAAW,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAE,CAAC;;EAEnD;EACAjF,SAAS,CAAC,MAAM;IACZjB,cAAc,CAACmG,EAAE,CAAC,0BAA0B,EAAEC,2BAA2B,CAAC;IAC1E,OAAO,MAAMpG,cAAc,CAACqG,GAAG,CAAC,0BAA0B,EAAED,2BAA2B,CAAC;EAC5F,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,2BAA2B,GAAGA,CAACE,MAAM,EAAEC,IAAI,KAAK;IAClDrC,WAAW,CAAC5D,QAAQ,CAAC6D,IAAI,CAAC,CAAC,CAAC;IAC5BqC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEH,MAAM,EAAEC,IAAI,CAAC;IACxC;IACA,IAAIA,IAAI,CAACG,QAAQ,IAAInG,WAAW,CAACoG,wCAAwC,IAAIJ,IAAI,CAACG,QAAQ,IAAInG,WAAW,CAACoG,wCAAwC,EAAE;MAChJpD,SAAS,CAACzD,YAAY,CAACsF,WAAY,CAAC;IACxC,CAAC,MAAM;MACH7B,SAAS,CAACzD,YAAY,CAAC0D,UAAW,CAAC;IACvC;IACAH,WAAW,CAACkD,IAAI,CAAC;IACjB1D,iBAAiB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACd,IAAI2B,cAAc,IAAIU,MAAM,IAAI,CAAC6B,8BAA8B,EAAE;MAC7D;MACAyB,wBAAwB,CAAC,CAAC;IAC9B,CAAC,MAAM;MACH;MACAC,yBAAyB,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACjE,cAAc,EAAEuC,8BAA8B,CAAC,CAAC;;EAEpD;EACA,MAAM2B,2BAA2B,GAAIC,MAAM,IAAK;IAC5C/G,cAAc,CAACgH,IAAI,CAAC,kCAAkC,EAAE,EAAE,EAAE;MAAEC,gBAAgB,EAAEF,MAAM;MAAEG,cAAc,EAAEA,cAAc,CAACC,IAAI,CAAC,IAAI;IAAE,CAAC,CAAC;EACxI,CAAC;;EAED;EACAlG,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC4D,yBAAyB,EAAE;MAC9BuC,sBAAsB,CAACzC,gBAAgB,CAAC;IAC1C;EACJ,CAAC,EAAE,CAACE,yBAAyB,CAAC,CAAC;;EAE/B;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIqC,MAAM,IAAIxD,YAAY,CAACsF,WAAW,IAAI,CAACD,8BAA8B,IAAI,CAACvF,OAAO,CAACiE,YAAY,CAAC,EAAE;MACnGwD,kBAAkB,CAAC,CAAC;IACtB;EACJ,CAAC,EAAE,CAAClC,8BAA8B,EAAEtB,YAAY,CAAC,CAAC;;EAEhD;EACA,MAAMuD,sBAAsB,GAAIE,YAAY,IAAK;IAC7C,IAAIxC,QAAQ,GAAG3E,0BAA0B,CAACmH,YAAY,EAAE/B,QAAQ,EAAED,aAAa,EAAE,IAAI,CAAC;IACtF,MAAMvB,aAAa,GAAGe,QAAQ,CAACyC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,CAACC,GAAG,CAACF,IAAI,IAAIA,IAAI,CAAC9C,MAAM,CAAC;IACpF,MAAMiD,gBAAgB,GAAGzH,wBAAwB,CAAC4E,QAAQ,CAAC,CAAC,CAAC;IAC7DhB,eAAe,CAAC6D,gBAAgB,CAAC;IACjC3D,gBAAgB,CAACD,aAAa,CAAC;EACnC,CAAC;;EAED;EACA,MAAMsD,kBAAkB,GAAGA,CAAA,KAAK;IAC9B,IAAIO,aAAa,GAAGxH,0BAA0B,CAAC2E,YAAY,EAAElB,YAAY,EAAEyB,aAAa,CAAC;IACzF1B,gBAAgB,CAACgE,aAAa,CAAC;EACjC,CAAC;;EAED;EACA,SAASf,yBAAyBA,CAAA,EAAG;IACjC;IACAjD,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBF,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;EACzB;;EAEA;EACA,SAASkD,wBAAwBA,CAAA,EAAG;IAClC,MAAMiB,eAAe,GAAG3C,SAAS,IAAInF,UAAU,CAAC+H,MAAM;IACpDvF,IAAI,CAACwF,cAAc,CAAC;MAChBC,IAAI,EAAE5E,QAAQ,CAAC4E,IAAI;MAAC;MACpB9C,SAAS,EAAE2C;IACf,CAAC,CAAC;IACD;IACCxD,iBAAiB,CAACwD,eAAe,CAAC;IAEpC,MAAMI,SAAS,GAAG,CAAC,GAAGnD,QAAQ,CAAC;IAC/BmD,SAAS,CAACC,OAAO,CAAEC,IAAI,IAAG;MACxB,MAAMC,YAAY,GAAGzD,gBAAgB,CAAC0D,IAAI,CAACC,QAAQ,IAAKH,IAAI,CAACI,OAAO,IAAID,QAAQ,CAAC5D,MAAM,CAAC,CAAC0D,YAAY;MACrGD,IAAI,CAACK,qBAAqB,GAAGC,0BAA0B,CAACL,YAAY,EAAEvI,cAAc,CAAC6I,kBAAkB,CAAC;MACxGP,IAAI,CAACQ,oBAAoB,GAAGF,0BAA0B,CAACL,YAAY,EAAEvI,cAAc,CAAC+I,iBAAiB,CAAC;IACxG,CAAC,CAAC;IACFlF,eAAe,CAACuE,SAAS,CAAC;EAC9B;;EAEA;EACA,MAAMQ,0BAA0B,GAAGA,CAACI,QAAQ,GAAG,EAAE,EAAEC,IAAI,KAAK;IACxD,OAAO,CAACD,QAAQ,CAACR,IAAI,CAAEb,IAAI,IAAKA,IAAI,CAACuB,QAAQ,IAAID,IAAI,CAAC,IAAI;MAAEE,SAAS,EAAE;IAAI,CAAC,EAAEA,SAAS;EAC3F,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvBpG,iBAAiB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMqG,cAAc,GAAIC,KAAK,IAAK;IAC9B,IAAIC,SAAS,GAAG,EAAE;IAClB3F,YAAY,CAAC8D,MAAM,CAAC8B,EAAE,IAAI;MACtBF,KAAK,CAACzB,GAAG,CAAC,CAAC4B,GAAG,EAAEC,KAAK,KAAK;QACtB,IAAIF,EAAE,CAACd,OAAO,IAAIe,GAAG,CAAC5E,MAAM,EAAE;UAC1B0E,SAAS,CAACI,IAAI,CAACH,EAAE,CAAC;UAClB,OAAOF,KAAK,CAACI,KAAK,CAAC;QACvB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACFJ,KAAK,CAACzB,GAAG,CAAEF,IAAI,IAAK;MAChB,IAAIY,YAAY,GAAGZ,IAAI,CAACY,YAAY;MACpC,MAAMqB,kBAAkB,GAAGhB,0BAA0B,CAACL,YAAY,EAAEvI,cAAc,CAAC6I,kBAAkB,CAAC;MACtG,MAAMgB,iBAAiB,GAAGjB,0BAA0B,CAACL,YAAY,EAAEvI,cAAc,CAAC+I,iBAAiB,CAAC;MACpG,IAAIe,KAAK,GAAG;QACRpB,OAAO,EAAEf,IAAI,CAAC9C,MAAM;QACpBkF,UAAU,EAAE,IAAI;QAChBC,cAAc,EAAE,EAAE;QAClBC,iBAAiB,EAAEL,kBAAkB;QACrCjB,qBAAqB,EAAEiB,kBAAkB;QACzCM,gBAAgB,EAAEL,iBAAiB;QACnCf,oBAAoB,EAAEe;MAC1B,CAAC;MACDN,SAAS,CAACI,IAAI,CAACG,KAAK,CAAC;IACzB,CAAC,CAAC;IACFjG,eAAe,CAAC0F,SAAS,CAAC;IAC1BjG,wBAAwB,CAAC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAM6G,qBAAqB,GAAGA,CAAA,KAAM;IAChCvH,IAAI,CAACmD,OAAO,CAACqE,aAAa,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IAC/BnH,eAAe,CAAC,IAAI,CAAC;IACrBR,IAAI,CAAC4H,MAAM,CAAC,CAAC;EACjB,CAAC;;EAED;EACA,MAAMjD,cAAc,GAAIkD,CAAC,IAAK;IAC1BnH,yBAAyB,CAAC,IAAI,CAAC;EACnC,CAAC;;EAED;EACA,MAAMoH,qBAAqB,GAAGA,CAAA,KAAM;IAChC,OAAOtG,aAAa,CAAC2D,GAAG,CAAC4C,YAAY,KAAK;MAAE/B,OAAO,EAAE+B;IAAa,CAAC,CAAC,CAAC;EACzE,CAAC;;EAED;EACA,MAAMC,4BAA4B,GAAGA,CAACH,CAAC,EAAEI,MAAM,KAAK;IAChDvH,yBAAyB,CAAC,KAAK,CAAC;IAChCe,gBAAgB,CAACwG,MAAM,CAAC;IACxBxK,cAAc,CAACgH,IAAI,CAAC,+BAA+B,EAAE,EAAE,EAAE;MAAEwD;IAAO,CAAC,CAAC,EAAC;EACzE,CAAC;;EAED;EACA,MAAMC,gCAAgC,GAAGA,CAACL,CAAC,EAAEI,MAAM,KAAK;IACpD,IAAI,CAAC7K,UAAU,CAACoE,aAAa,EAAEyG,MAAM,CAAC,EAAE;MACpC1J,KAAK,CAAC4J,OAAO,CAAC;QACV/E,KAAK,EAAE,IAAI;QACXgF,IAAI,eAAE5I,OAAA,CAAC5C,yBAAyB;UAAAyL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACnCC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAI;QAChBC,IAAI,EAAEA,CAAA,KAAM;UAAElI,yBAAyB,CAAC,KAAK,CAAC;QAAE;MACpD,CAAC,CAAC;IACN,CAAC,MAAM;MACHA,yBAAyB,CAAC,KAAK,CAAC;IACpC;EACJ,CAAC;;EAED;EACA,MAAMmI,eAAe,GAAGA,CAAA,KAAM;IAC1BrI,eAAe,CAAC,KAAK,CAAC;IACtBR,IAAI,CAAC4H,MAAM,CAAC,CAAC;EACjB,CAAC;;EAED;EACA,MAAMkB,QAAQ,GAAIb,MAAM,IAAK;IACzB,MAAM;MAAExC;IAAK,CAAC,GAAGwC,MAAM;IACvB,IAAI,CAAC1H,YAAY,IAAI,CAACkF,IAAI,EAAE;MACxB;MACA,OAAO/H,UAAU,CAACqL,OAAO,CAAC,YAAY,CAAC;IAC3C;IACA,IAAIvG,YAAY,GAAGtC,IAAI,CAACmD,OAAO,CAAC2F,yBAAyB,CAAC,CAAC;IAC3D;IACA,IAAIzI,YAAY,EAAE;MACd,MAAM0I,aAAa,GAAGnB,qBAAqB,CAAC,CAAC;MAC7C,IAAItD,MAAM,GAAG;QACTzE,MAAM,EAAEA,MAAM;QACd0C,SAAS,EAAE1B,MAAM,IAAIxD,YAAY,CAACsF,WAAW,GAAGJ,SAAS,GAAG5B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,MAAM;QAC5EsD,IAAI,EAAEA,IAAI;QACV/C,OAAO,EAAE3B,MAAM,IAAIxD,YAAY,CAACsF,WAAW,GAAGH,OAAO,GAAG1E,WAAW,CAACkL,4BAA4B;QAChGC,eAAe,EAAE,GAAG;QACpB3G,YAAY,EAAEA,YAAY;QAC1ByG,aAAa,EAAEA;MACnB,CAAC;MACD;MACAzE,MAAM,GAAG;QAAE,GAAGA,MAAM;QAAE4E,OAAO,EAAE;MAAE,CAAC,EAAC;MACnC,OAAO7E,2BAA2B,CAACC,MAAM,CAAC;IAC9C;IACAtD,YAAY,CAACyE,OAAO,CAAC,CAACmB,EAAE,EAAEE,KAAK,KAAK;MAChCF,EAAE,CAACuC,KAAK,GAAGrC,KAAK,GAAG,CAAC;IACxB,CAAC,CAAC;IACF,IAAIjG,MAAM,EAAE;MACR;MACA,IAAIuI,MAAM,GAAG;QAAEvJ,MAAM;QAAEoC,MAAM,EAAEtB,QAAQ,CAACsB,MAAM;QAAEsD,IAAI;QAAE9C,SAAS,EAAEsF,MAAM,CAACtF,SAAS,GAAGnF,UAAU,CAAC+H,MAAM,GAAG/H,UAAU,CAAC+L,OAAO;QAAEhH,QAAQ,EAAErB,YAAY;QAAEsB;MAAa,CAAC;MAClKgH,aAAa,CAACF,MAAM,CAAC;IACzB,CAAC,MAAM;MACH;MACA,IAAIA,MAAM,GAAG;QAAEvJ,MAAM;QAAE0J,SAAS,EAAE5I,QAAQ,CAACsB,MAAM;QAAEsD,IAAI;QAAE9C,SAAS,EAAEsF,MAAM,CAACtF,SAAS,GAAGnF,UAAU,CAAC+H,MAAM,GAAG/H,UAAU,CAAC+L,OAAO;QAAEhH,QAAQ,EAAErB,YAAY;QAAEsB;MAAa,CAAC;MACrKkH,eAAe,CAACJ,MAAM,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,SAASI,eAAeA,CAACJ,MAAM,EAAE;IAC7BtM,IAAI,CAAC2M,gCAAgC,CAACL,MAAM,CAAC,CAACM,IAAI,CAACC,MAAM,IAAI;MACzD,IAAIA,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;QAAA,IAAAC,iBAAA;QAC1B9L,eAAe,CAAC,CAAC;QACjBqC,iBAAiB,CAAC,KAAK,CAAC;QACxBO,QAAQ,CAACmJ,QAAQ,IAAInJ,QAAQ,CAACmJ,QAAQ,CAAC,CAAAH,MAAM,aAANA,MAAM,wBAAAE,iBAAA,GAANF,MAAM,CAAEI,QAAQ,CAAC,CAAC,CAAC,cAAAF,iBAAA,uBAAnBA,iBAAA,CAAqBG,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC,CAAC;MAClF;IACJ,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAI;MACZnG,OAAO,CAACC,GAAG,CAACkG,GAAG,CAAC;IACpB,CAAC,CAAC;EACN;;EAEA;EACA,SAASZ,aAAaA,CAACF,MAAM,EAAE;IAC3BtM,IAAI,CAACqN,gCAAgC,CAACf,MAAM,CAAC,CAACM,IAAI,CAACC,MAAM,IAAI;MACzD,IAAIA,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;QAC1B7L,eAAe,CAAC,CAAC;QACjBqC,iBAAiB,CAAC,KAAK,CAAC;QACxBxC,sBAAsB,CAACiC,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,MAAM,EAAE;UAC7CmI,KAAK,EAAEhB,MAAM,CAAC7D,IAAI;UAAI;UACtB8E,QAAQ,EAAEjB,MAAM,CAAC7D,IAAI,CAAE;QACzB,CAAC,CAAC,CAAC;MACT;IACJ,CAAC,CAAC,CAAC0E,KAAK,CAACC,GAAG,IAAI;MACZnG,OAAO,CAACC,GAAG,CAACkG,GAAG,CAAC;IACpB,CAAC,CAAC;EACN;;EAEA;EACF,MAAMI,qBAAqB,GAAI3C,CAAC,IAAK;IACnC,MAAM3C,OAAO,GAAG2C,CAAC,CAAC9D,MAAM,CAACmB,OAAO;;IAEhC;IACA,IAAI,CAACA,OAAO,IAAI,CAAC7H,OAAO,CAAC6D,YAAY,CAAC,EAAE;MACtC3C,KAAK,CAAC4J,OAAO,CAAC;QACZ/E,KAAK,EAAE,IAAI;QACXgF,IAAI,eAAE5I,OAAA,CAAC5C,yBAAyB;UAAAyL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACnCC,OAAO,EAAE,2BAA2B;QACpCC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,KAAK;QACjB8B,MAAM,EAAE,IAAI;QACZ7B,IAAI,EAAEA,CAAA,KAAM;UACV;UACAzH,eAAe,CAAC,EAAE,CAAC;UACnBW,iBAAiB,CAAC,KAAK,CAAC;UACxB9B,IAAI,CAAC0K,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC;QACxC,CAAC;QACDC,QAAQ,EAAEA,CAAA,KAAM;UACd;UACA3K,IAAI,CAAC0K,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC;QACvC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA5I,iBAAiB,CAACoD,OAAO,CAAC;IAC5B;EACF,CAAC;EAEC,oBAAO1F,OAAA,CAACF,eAAe;IACnBsL,SAAS,EAAC,2BAA2B;IACrClH,KAAK,EAAE,KAAM;IACbN,KAAK,EAAErC,MAAM,GAAG,IAAApB,sBAAA,GAAGsD,WAAW,CAACM,UAAU,cAAA5D,sBAAA,cAAAA,sBAAA,GAAE,EAAE,QAAQ,GAAG,OAAAC,sBAAA,GAAMqD,WAAW,CAACM,UAAU,cAAA3D,sBAAA,cAAAA,sBAAA,GAAE,EAAE,MAAO;IAC/FiL,cAAc,EAAE,IAAK;IACrBC,IAAI,EAAEzK,cAAe;IACrB0K,OAAO,EAAErE,YAAa;IACtBsE,MAAM,eAAExL,OAAA;MAAKyL,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAhB,QAAA,eACvC1K,OAAA,CAAChB,KAAK;QAAC2M,IAAI,EAAE,EAAG;QAAAjB,QAAA,gBACZ1K,OAAA,CAACrB,MAAM;UAAC8M,KAAK,EAAE;YAAEtH,YAAY,EAAE;UAAE,CAAE;UAACyH,OAAO,EAAE1E,YAAa;UAAAwD,QAAA,EAAC;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtEhJ,OAAA,CAACrB,MAAM;UAACoI,IAAI,EAAC,SAAS;UAAC0E,KAAK,EAAE;YAAEtH,YAAY,EAAE;UAAE,CAAE;UAACyH,OAAO,EAAEvC,eAAgB;UAAAqB,QAAA,EAAC;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAE;IAAA0B,QAAA,gBAEP1K,OAAA,CAACf,IAAI;MAAC4M,QAAQ,EAAEnJ,oBAAoB,IAAII,yBAA0B;MAAA4H,QAAA,eAC9D1K,OAAA,CAACnB,IAAI;QACD2B,IAAI,EAAEA,IAAK;QACXsL,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QACtBC,UAAU,EAAE;UAAED,IAAI,EAAE;QAAG,CAAE;QACzBzC,QAAQ,EAAEA,QAAS;QACnB2C,QAAQ,EAAE,KAAM;QAAA;QAChBC,YAAY,EAAE,KAAM,CAAC;QAAA;QAAAxB,QAAA,gBAErB1K,OAAA,CAACnB,IAAI,CAACsN,IAAI;UAACrB,KAAK,EAAE,QAAS;UAAAJ,QAAA,eACzB1K,OAAA,CAAChB,KAAK;YAAC2M,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACb1K,OAAA,CAACnB,IAAI,CAACsN,IAAI;cAAClG,IAAI,EAAC,MAAM;cAACmG,QAAQ,EAAE,IAAK;cAACC,OAAO;cAAA3B,QAAA,eAC7C1K,OAAA,CAAClB,KAAK;gBAAC2M,KAAK,EAAExH,WAAY;gBAACiI,YAAY,EAAC;cAAK;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACbhJ,OAAA,CAACnB,IAAI,CAACsN,IAAI;cAAClG,IAAI,EAAC,WAAW;cAACqG,aAAa,EAAC,SAAS;cAACD,OAAO;cAAA3B,QAAA,eAC1D1K,OAAA,CAACuM,QAAQ;gBAACC,QAAQ,EAAExB,qBAAsB;gBAAAN,QAAA,EAAC;cAAG;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACZhJ,OAAA,CAACnB,IAAI,CAACsN,IAAI;UAACrB,KAAK,EAAE,QAAS;UAAAJ,QAAA,EACtB7M,OAAO,CAACiE,YAAY,CAAC,gBAAG9B,OAAA,CAAC7C,MAAM;YAAA0L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,gBAC9BhJ,OAAA;YAAA0K,QAAA,gBACI1K,OAAA,CAACT,eAAe;cAACuC,YAAY,EAAEA,YAAa;cAAC2K,KAAK,EAAE5J,SAAU;cAACG,YAAY,EAAEpB,aAAc;cAAC2B,aAAa,EAAEA,aAAc;cAACmJ,SAAS,EAAE,CAAE;cAACC,GAAG,EAAEjM;YAAK;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrJhJ,OAAA;cAAKoL,SAAS,EAAC,kBAAkB;cAAAV,QAAA,gBAC7B1K,OAAA,CAACrB,MAAM;gBACHoI,IAAI,EAAC,MAAM;gBACX6B,IAAI,eACA5I,OAAA,CAAC1C,sBAAsB;kBAAC8N,SAAS,EAAC;gBAAc;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACrD;gBACD4C,OAAO,EAAE3D,qBAAsB;gBAAAyC,QAAA,eAE/B1K,OAAA;kBAAA0K,QAAA,GAAM,kCAAO,eAAA1K,OAAA,CAAC3C,gBAAgB;oBAACoO,KAAK,EAAE;sBAAEmB,MAAM,EAAE;oBAAE;kBAAE;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACThJ,OAAA,CAACrB,MAAM;gBAACiK,IAAI,eAAE5I,OAAA,CAACzC,cAAc;kBAAAsL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACoC,SAAS,EAAC,kBAAkB;gBAACQ,OAAO,EAAEzD,oBAAqB;gBAAAuC,QAAA,EAAE;cAAI;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G,CAAC,eACNhJ,OAAA;cAAKoL,SAAS,EAAC,SAAS;cAAAV,QAAA,GAAC,yJAA0B,GAAArK,sBAAA,GAACoD,WAAW,CAACM,UAAU,cAAA1D,sBAAA,cAAAA,sBAAA,GAAE,EAAE,EAAC,oBAAG;YAAA;cAAAwI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxFhJ,OAAA;cAAKoL,SAAS,EAAC,SAAS;cAAAV,QAAA,EAAC;YAA8B;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC,EAEV3G,cAAc,iBAAIrC,OAAA,CAACnB,IAAI,CAACsN,IAAI;UAACrB,KAAK,EAAE,QAAS;UAAAJ,QAAA,gBAC3C1K,OAAA,CAACH,iBAAiB;YAAC6B,YAAY,EAAEA,YAAa;YAACC,eAAe,EAAEA,eAAgB;YAAC4B,aAAa,EAAEA,aAAc;YAACC,QAAQ,EAAEA,QAAS;YAACZ,gBAAgB,EAAEA;UAAiB;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzKhJ,OAAA;YAAGoL,SAAS,EAAC,aAAa;YAACQ,OAAO,EAAEA,CAAA,KAAMxK,wBAAwB,CAAC,IAAI,CAAE;YAAAsJ,QAAA,EAAC;UAAM;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpFhJ,OAAA;YAAKoL,SAAS,EAAC,SAAS;YAAAV,QAAA,GAAC,6CAAQ,EAACjH,WAAW,CAACwC,IAAI,EAAC,2QAA6C;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtGhJ,OAAA;YAAKoL,SAAS,EAAC,SAAS;YAAAV,QAAA,GAAC,mGAAiB,GAAApK,sBAAA,GAACmD,WAAW,CAACM,UAAU,cAAAzD,sBAAA,cAAAA,sBAAA,GAAE,EAAE,EAAC,8PAA0C;UAAA;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEPhJ,OAAA,CAACJ,gBAAgB;MAAC8B,YAAY,EAAEA,YAAa;MAC3BqB,QAAQ,EAAEH,gBAAiB;MAC3BiK,OAAO,EAAE1L,qBAAsB;MAC/BgG,cAAc,EAAEA,cAAe;MAC/BgE,QAAQ,EAAEA,CAAA,KAAM/J,wBAAwB,CAAC,KAAK;IAAE;MAAAyH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErEhJ,OAAA,CAACR,uBAAuB;MAAAqJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3BhJ,OAAA,CAACV,kBAAkB;MACfgM,IAAI,EAAErK,sBAAuB;MAC7BmI,IAAI,EAAEZ,4BAA6B;MACnC2C,QAAQ,EAAEzC,gCAAiC;MAC3CqC,QAAQ,EAAE1J,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0J,QAAS;MAC7B/I,aAAa,EAAEA,aAAc;MAC7BF,YAAY,EAAEA;IAAa;MAAA+G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AACtB;AAAC9I,EAAA,CA7ZuBD,oBAAoB;EAAA,QACrBZ,SAAS,EACbR,IAAI,CAAC4B,OAAO,EAkB0EhB,4BAA4B,EAEfE,sCAAsC,EAEGD,kCAAkC,EAExF/B,mCAAmC,EAEjED,kCAAkC,EAExEgB,QAAQ;AAAA;AAAAoO,EAAA,GA9BrB7M,oBAAoB;AAAA,IAAA6M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}