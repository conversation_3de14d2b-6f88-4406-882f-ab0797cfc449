import React, { useEffect, useState } from "react";
import { Button, Space, Layout, List, Pagination, Popover, Radio, Image, Modal } from "antd";
import * as httpCommon from "@common/api/http";
import { eConsoleNodeId, ePagination } from "@common/utils/enum";
import { useQueryClient } from "@tanstack/react-query";
import { Outlet, useLocation, useNavigate, useOutletContext, useParams, useSearchParams } from "react-router-dom";
import { ResizableBox } from "react-resizable";
import { useQuerySettin228_getTeamUserConfig } from "src/team/service/objNodeHooks";
import { useIssueSearchParams } from "src/issueTrack/service/issueSearchHooks";
import "./Issuelist.scss";
import { track008, track010 } from '@common/utils/ApiPath';
import IssueItem from "./IssueItem"
import { priPermission, getNodeNameByNodeId } from "@common/utils/logicUtils";
import jiangxu from "@common/assets/images/jiangxu.png";
import shengxu from "@common/assets/images/shengxu.png";
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { globalEventBus } from "@common/utils/eventBus";

const { Sider } = Layout;

//Issue短列表，中间第二列短列表
export default function Issuelist() {
  //totalCnt: issue总记录数, pageNo: 当前页码, issueList:当前页的至多30条的issue列表, currentIssueIdx:当前选中的issue在issueList中的序号
  const { totalCnt, issueList, pageNo, subclassAttrList, userList, spaceUserList, selectionList, objInfo,
    setCurrentIssueIdx, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, keywords, gotoPreviousIssue, gotoNextIssue,
    ascendingFlg, setAscendingFlg, orderBy, setOrderBy, searchQuery, projectInfo, setting320Result, selectedKeys, onMoreBtnClick, onShowMoreButtonClick,
    onPositioningClick, finalCreateFlg
  } = useOutletContext(); //从IssueHome获取到上下文数据*/

  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const createFlg = searchParams.get("createFlg"); //是否创建

  const { teamId, nodeId: issueListNodeId, } = useParams();
  const { issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams();// issue路由配置，页面刷新

  useEffect(()=>{
    if(createFlg){
      setIssueSearchParams({queryId: -1});
      setTimeout(()=>{
        // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑
        // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；
       openCreateIssueModal()
      }, 2000)
    } 
  },[createFlg])

  // 拖拽的句柄： <DraggableCore> not mounted on DragStart!
  // https://github.com/react-grid-layout/react-resizable/issues/200
  const handle = (resizeHandle, ref) => {
    return <div className="ia-splitter-handle" ref={ref}>
      <div className="ia-splitter-handle-highlight"></div>
    </div>
  };

  // null值不为空，无法设置默认值260
  const { data: { treeWidth: width /*, expandNodeIds*/ } = { width: 260, expandNodeIds: [] },
    /*isLoading: isLoadingTeamConfig,
    refetch: refetchTeamConfig */
  } = useQuerySettin228_getTeamUserConfig(teamId, issueListNodeId);

/*  //刷新issue列表
  function refreshIssueList() {
    queryClient.invalidateQueries([track008])
    setTimeout(() => {
      setRefreshingFlg(false);
    }, 500);
  }*/

/*  // 宽度拖动中...
  const onResize = (event, { element, size, handle }) => {

  }*/

  function openCreateIssueModal () {
    globalEventBus.emit("openCreateIssueModalEvent", "", {
      nodeId: issueListNodeId, projectInfo, callback: onPostIssueCreated
    })
  }

  // 调整宽度
  const onResizeStop = (event, { element, size, handle }) => {
    const request = {
      teamId,
      nodeId: issueListNodeId, //中间数展开节点
      treeWidth: size.width,
      // expandNodeIds:[],
    }
    httpCommon.setting_227_save_team_user_tree_width(request).then((res) => {
      if (res.resultCode === 200) {

      }
    })
  }

  // 加载更多 - 长短列表
  function gotoPageNo(page/*, pageSize*/) {
    // setPageNo(page); //触发 useQueryIssue017_getIssueList 再次调用
    queryClient.setQueryData([track010, teamId, issueListNodeId], (pageNo)=>(page));//触发 useQueryIssue017_getIssueList 再次调用
    setCurrentIssueIdx(0);
  }

  // issue创建完成后，页面刷新
  function onPostIssueCreated(createIssueNodeId) {
    // tmsbug-10387：issue列表(以及中树其它情形)， 在默认的第一个节点上，创建新节点，期望新建后的节点处于高亮状态
    let idx = issueList.findIndex(_issue => _issue.nodeId == issueNodeId);
    if(pageNo == 1 && idx == 0) setIssueSearchParams({issueNodeId: createIssueNodeId, queryId, needAdd: false});
  }

  // 点击事件
  const handleOnClick = (_issue) => {
    setIssueSearchParams({issueNodeId: _issue.nodeId, queryId});
  }

 async function addClick(){
    return Modal.confirm({
        title: "提示",
        icon: <ExclamationCircleOutlined />,
        centered: true,
        content: <p>{`当前列表页为"查询"页，将转至“${await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)??"全部"}”页创建问题。`}</p>,
        okText: " 好的，继续新建",
        cancelText: "取消",
        onOk: () => {
          if(!!projectInfo?.allIssueNodeId){
            navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}?createFlg=true`);
          }
        },
        onCancel: () => {
          console.log("Cancel");
        },
      });
  }

  // !!objInfo?.objId: 搜索条件
  // 是否有编辑权限 当权限privWrite大于0则可以编辑
  const editBtn = (priPermission(setting320Result.privWrite) && finalCreateFlg)  ?
    <Button className="defaultBtn"
            type="primary"
            onClick={() => !!objInfo?.objId ? addClick(): openCreateIssueModal()}>
      + {projectInfo.issueAlias}
    </Button>
    : <></>

  return <Layout>
    {/* 可拖拽 */}
    <ResizableBox
      width={width || 300} // 接口返回的width为null
      className="custom-box-right"
      height={Infinity}
      handle={handle}
      handleSize={[8, 8]}
      resizeHandles={['e']}
      axis="x"
      minConstraints={[300, Infinity]} //最小宽度
      maxConstraints={[650, Infinity]}
      //onResize={onResize}
      onResizeStop={onResizeStop}
      style={{ height: "100%" }}
    >
      <Sider className="issue-list-sider" width={"100%"}>
        <div className="issue-list-sider-search">
          <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
            {(searchQuery?.length > 0 || keywords) ?
              <a style={{  paddingLeft: 15, fontSize: 12, color: "#666" }} title='定位至#1页'
                 onClick={()=>pageNo > 1 ? gotoPageNo(1) : null}>结果({totalCnt}条)</a>
              :
              <a style={{   paddingLeft: 15,fontSize: 12, color: "#666" }} title='定位至#1页'
                   onClick={()=>pageNo > 1 ? gotoPageNo(1) : null}>全部({totalCnt}条)</a>
            }
            {/* <Button
              className={refreshingFlg && 'refresh-icon'}
              style={{ position: 'relative', color: '#999' }}
              type="link"
              icon={<span className="refresh-position fontsize-14 iconfont shuaxin1" />}
              onClick={() => {
                setRefreshingFlg(true);
                refreshIssueList()
              }}
            /> */}
          </div>
          <Space>
            {/* 排序功能 */}
            <Popover
              content={<div >
                <Radio.Group onChange={(e) => setOrderBy(e.target.value)} value={orderBy}>
                  <Space direction="vertical">
                    <Radio value={eConsoleNodeId.Nid_11118_Issue_CreateDate}>创建时间</Radio>
                    <Radio value={eConsoleNodeId.Nid_11119_Issue_UpdateDate}>修改时间</Radio>
                  </Space>
                </Radio.Group>
              </div>} title={<div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <div>排序方式</div>
                <a title={ascendingFlg ? "点击降序" : "点击升序"} onClick={() => setAscendingFlg(!ascendingFlg)}>
                  <Image src={ascendingFlg ? shengxu : jiangxu} preview={false} width={10} height={10} style={{marginBottom:3}}/>
                </a>
              </div>} trigger="click" placement="bottom">
              <a title="排序" style={{color:'inherit'}}>
                <Image src={ascendingFlg ? shengxu : jiangxu} preview={false} width={10} height={10} style={{marginBottom:3}}/> 
              </a>
            </Popover>
            { 
              <Space>
                {totalCnt ? <div className="fontsize-12" style={{ color: "#666" }}>#{pageNo}页</div> : ""}
                {editBtn}
              </Space>
            }
          </Space>
        </div>
        {issueList && issueList?.length > 0 ?
          <>
            {/*中间列，issue短列表*/}
            <List dataSource={issueList}
              className="issue-list"
              renderItem={(_issue, index) =>(
                <IssueItem 
                  onClick={handleOnClick}
                  _issue={_issue}
                  onShowMoreButtonClick={onShowMoreButtonClick}
                  onMoreBtnClick={onMoreBtnClick}
                  selectedKeys={selectedKeys}
                />
              )} />
            {/* 短列表 页码配置*/}
            <Pagination
              className={"issue-list-sider-pagination"}
              pageSize={ePagination.PageSize_30}
              current={pageNo}
              size="small"
              showSizeChanger={false}
              total={totalCnt}
              onChange={gotoPageNo}
            />
          </>
          :
          // 空白提示栏
          <div className="issue-list blank-page">
            <div className="blank-page-title">这里是空的</div>
            {
              priPermission(setting320Result.privWrite) ? <div className="blank-page-des fontsize-12 flexCenter">
              <span style={{ paddingRight: 5 }}>你可以</span>
              <a onClick={() =>openCreateIssueModal()}>新建{projectInfo.issueAlias}</a>
            </div> : <></>
            }
          </div>
        }
      </Sider>
    </ResizableBox>
    <Layout className="issue-detail">
      {/* 子路由，用于渲染 问题详情页 (IssueDetail.jsx) ，通过context传递数据 */}
      <Outlet context={{ teamId, issueNodeId, selectionList, spaceUserList, userList,
                        gotoPreviousIssue, gotoNextIssue, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg,
                        projectInfo, onMoreBtnClick, setCurrentIssueIdx, issueList,onPositioningClick }} />
    </Layout>
  </Layout >
}
