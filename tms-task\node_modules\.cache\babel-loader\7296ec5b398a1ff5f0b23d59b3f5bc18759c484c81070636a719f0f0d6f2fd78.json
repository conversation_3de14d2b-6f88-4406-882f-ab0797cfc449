{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var Iterable;\n(function (Iterable) {\n  function is(thing) {\n    return thing && typeof thing === 'object' && typeof thing[Symbol.iterator] === 'function';\n  }\n  Iterable.is = is;\n  const _empty = Object.freeze([]);\n  function empty() {\n    return _empty;\n  }\n  Iterable.empty = empty;\n  function* single(element) {\n    yield element;\n  }\n  Iterable.single = single;\n  function from(iterable) {\n    return iterable || _empty;\n  }\n  Iterable.from = from;\n  function isEmpty(iterable) {\n    return !iterable || iterable[Symbol.iterator]().next().done === true;\n  }\n  Iterable.isEmpty = isEmpty;\n  function first(iterable) {\n    return iterable[Symbol.iterator]().next().value;\n  }\n  Iterable.first = first;\n  function some(iterable, predicate) {\n    for (const element of iterable) {\n      if (predicate(element)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  Iterable.some = some;\n  function find(iterable, predicate) {\n    for (const element of iterable) {\n      if (predicate(element)) {\n        return element;\n      }\n    }\n    return undefined;\n  }\n  Iterable.find = find;\n  function* filter(iterable, predicate) {\n    for (const element of iterable) {\n      if (predicate(element)) {\n        yield element;\n      }\n    }\n  }\n  Iterable.filter = filter;\n  function* map(iterable, fn) {\n    let index = 0;\n    for (const element of iterable) {\n      yield fn(element, index++);\n    }\n  }\n  Iterable.map = map;\n  function* concat() {\n    for (var _len = arguments.length, iterables = new Array(_len), _key = 0; _key < _len; _key++) {\n      iterables[_key] = arguments[_key];\n    }\n    for (const iterable of iterables) {\n      for (const element of iterable) {\n        yield element;\n      }\n    }\n  }\n  Iterable.concat = concat;\n  function* concatNested(iterables) {\n    for (const iterable of iterables) {\n      for (const element of iterable) {\n        yield element;\n      }\n    }\n  }\n  Iterable.concatNested = concatNested;\n  function reduce(iterable, reducer, initialValue) {\n    let value = initialValue;\n    for (const element of iterable) {\n      value = reducer(value, element);\n    }\n    return value;\n  }\n  Iterable.reduce = reduce;\n  function forEach(iterable, fn) {\n    let index = 0;\n    for (const element of iterable) {\n      fn(element, index++);\n    }\n  }\n  Iterable.forEach = forEach;\n  /**\n   * Returns an iterable slice of the array, with the same semantics as `array.slice()`.\n   */\n  function slice(arr, from) {\n    let to = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : arr.length;\n    return function* () {\n      if (from < 0) {\n        from += arr.length;\n      }\n      if (to < 0) {\n        to += arr.length;\n      } else if (to > arr.length) {\n        to = arr.length;\n      }\n      for (; from < to; from++) {\n        yield arr[from];\n      }\n    }();\n  }\n  Iterable.slice = slice;\n  /**\n   * Consumes `atMost` elements from iterable and returns the consumed elements,\n   * and an iterable for the rest of the elements.\n   */\n  function consume(iterable) {\n    let atMost = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.POSITIVE_INFINITY;\n    const consumed = [];\n    if (atMost === 0) {\n      return [consumed, iterable];\n    }\n    const iterator = iterable[Symbol.iterator]();\n    for (let i = 0; i < atMost; i++) {\n      const next = iterator.next();\n      if (next.done) {\n        return [consumed, Iterable.empty()];\n      }\n      consumed.push(next.value);\n    }\n    return [consumed, {\n      [Symbol.iterator]() {\n        return iterator;\n      }\n    }];\n  }\n  Iterable.consume = consume;\n  /**\n   * Consumes `atMost` elements from iterable and returns the consumed elements,\n   * and an iterable for the rest of the elements.\n   */\n  function collect(iterable) {\n    return consume(iterable)[0];\n  }\n  Iterable.collect = collect;\n  /**\n   * Returns whether the iterables are the same length and all items are\n   * equal using the comparator function.\n   */\n  function equals(a, b) {\n    let comparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (at, bt) => at === bt;\n    const ai = a[Symbol.iterator]();\n    const bi = b[Symbol.iterator]();\n    while (true) {\n      const an = ai.next();\n      const bn = bi.next();\n      if (an.done !== bn.done) {\n        return false;\n      } else if (an.done) {\n        return true;\n      } else if (!comparator(an.value, bn.value)) {\n        return false;\n      }\n    }\n  }\n  Iterable.equals = equals;\n})(Iterable || (Iterable = {}));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}