{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\settings\\\\utils\\\\MemberInformation.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Input, Form, Avatar, Select, Upload } from \"antd\";\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport { CameraOutlined } from \"@ant-design/icons\";\nimport { QuestionsUploadIcon } from \"@components/IconUtil\";\nimport \"./MemberInformation.scss\";\nimport { useParams } from \"react-router-dom\";\nimport * as httpSettings from '@common/api/http';\nimport ImgCrop from \"antd-img-crop\";\nimport Draggable from 'react-draggable';\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { NoAvatarIcon } from '@components/IconUtil';\nimport { useQueryClient } from \"@tanstack/react-query\";\nimport { get_team_mbr_user_info } from \"@/settings/store/actionCreators\";\nimport { useDispatch } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  Dragger\n} = Upload;\n\n// 可修改项\nfunction MemberFromItem({\n  value,\n  onChange,\n  memberItem,\n  placeholderValue\n}) {\n  _s();\n  useEffect(() => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(value);\n  }, [memberItem]);\n  const modifyBlur = e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e.target.value);\n  };\n  const sexChange = e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"MemberFromItem\",\n    children: [memberItem.name == \"avatar\" && /*#__PURE__*/_jsxDEV(MemberLogo, {\n      value: value,\n      onChange: onChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 9\n    }, this), memberItem.name == 'sex' && /*#__PURE__*/_jsxDEV(Select, {\n      className: \"MemberFromItem-sex\",\n      defaultValue: value,\n      style: {\n        width: 100\n      },\n      placeholder: placeholderValue,\n      onChange: sexChange,\n      children: [/*#__PURE__*/_jsxDEV(Option, {\n        value: 0,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          children: \"\\u7537\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Option, {\n        value: 1,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          children: \"\\u5973\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 9\n    }, this), memberItem.name != \"sex\" && memberItem.name != \"avatar\" && /*#__PURE__*/_jsxDEV(Input, {\n      autoComplete: \"off\",\n      onBlur: modifyBlur,\n      defaultValue: value,\n      placeholder: placeholderValue\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n// memberPopVisible 弹窗状态\n// onClose 弹窗关闭事件\n// memberItem 成员数据对象(对象类型)\n// formItemData 封装好的form表单数据\n_s(MemberFromItem, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = MemberFromItem;\nexport default function MemberInformation({\n  memberPopVisible,\n  onClose,\n  memberItem,\n  formItemData,\n  refreshUserList\n}) {\n  _s2();\n  //,allUsers=[],setAllUsers\n  const draggleRef = useRef(null);\n  const queryClient = useQueryClient();\n  const dispatch = useDispatch();\n  const [disabled, setDisabled] = useState(false);\n  const {\n    teamId\n  } = useParams();\n  const [form] = Form.useForm();\n  const [formData, setFormData] = useState([]);\n  const [bounds, setBounds] = useState({\n    left: 0,\n    top: 0,\n    bottom: 0,\n    right: 0\n  });\n  const onStart = (_event, uiData) => {\n    var _draggleRef$current;\n    const {\n      clientWidth,\n      clientHeight\n    } = window.document.documentElement;\n    const targetRect = (_draggleRef$current = draggleRef.current) === null || _draggleRef$current === void 0 ? void 0 : _draggleRef$current.getBoundingClientRect();\n    if (!targetRect) {\n      return;\n    }\n    setBounds({\n      left: -targetRect.left + uiData.x,\n      right: clientWidth - (targetRect.right - uiData.x),\n      top: -targetRect.top + uiData.y,\n      bottom: clientHeight - (targetRect.bottom - uiData.y)\n    });\n  };\n  useEffect(() => {\n    if (memberPopVisible) {\n      let formData_ = {};\n      formItemData.forEach((item, index) => {\n        formData_[item.name] = item.value;\n      });\n      form.setFieldsValue({\n        ...formData_\n      });\n      setFormData([...formItemData]);\n    }\n  }, [formItemData]);\n  const onCancel = () => {\n    onClose();\n    form.resetFields();\n    setFormData([]);\n  };\n  const updateUserInfo = () => {\n    let formData = form.getFieldsValue(true);\n    let params = {\n      teamId: teamId,\n      userId: memberItem.userId,\n      gender: formData.sex,\n      ...formData\n    };\n    httpSettings.setting_225_update_user_info(params).then(res => {\n      if (res.resultCode === 200) {\n        globalUtil.success('修改成功');\n        onClose();\n        setFormData([]);\n        refreshUserList();\n        queryClient.invalidateQueries(['get_team_allusergrp']);\n        dispatch(get_team_mbr_user_info(teamId));\n      } else {\n        globalUtil.error(res.resultMessage || '保存失败');\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"MemberInformation\",\n    children: /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      className: \"MemberInformation-modal\",\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dragInformTitle\",\n        onMouseOver: () => {\n          if (disabled) setDisabled(false);\n        },\n        onMouseOut: () => setDisabled(true),\n        children: \"\\u6210\\u5458\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this),\n      width: 600,\n      centered: true,\n      destroyOnClose: true,\n      maskClosable: false,\n      keyboard: true,\n      open: memberPopVisible,\n      cancelText: \"\\u53D6\\u6D88\",\n      onCancel: onCancel,\n      onOk: updateUserInfo,\n      okText: \"\\u4FDD\\u5B58\",\n      modalRender: modal => /*#__PURE__*/_jsxDEV(Draggable, {\n        handle: \".dragInformTitle\",\n        disabled: disabled,\n        bounds: bounds,\n        onStart: (event, uiData) => onStart(event, uiData),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: draggleRef,\n          children: modal\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          labelAlign: \"left\",\n          colon: false,\n          labelCol: {\n            span: 5,\n            offset: 0\n          },\n          children: [formData.map((itemx, index) => /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: itemx.label,\n            style: {\n              margin: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: itemx.name,\n                children: /*#__PURE__*/_jsxDEV(MemberFromItem, {\n                  memberItem: itemx,\n                  placeholderValue: itemx.placeholder\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)\n              }, itemx.key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#999',\n                  marginLeft: 20,\n                  marginBottom: 5\n                },\n                children: itemx.desc\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              color: '#999'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"iconfont tishi\",\n              style: {\n                color: '#F59A23',\n                marginRight: 4,\n                fontSize: 18\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), \"\\u6210\\u5458\\u4FE1\\u606F\\u4FEE\\u6539\\u540E\\uFF0C\\u53EA\\u4F1A\\u5728\\u6B64\\u56E2\\u961F\\u5185\\u663E\\u793A\\uFF0C\\u56E2\\u961F\\u5185\\u6210\\u5458\\u90FD\\u53EF\\u4EE5\\u770B\\u89C1\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n}\n\n// 上传头像\n_s2(MemberInformation, \"7JpvfQ/WKZKjs2rgKQqSyuDwmy8=\", false, function () {\n  return [useQueryClient, useDispatch, useParams, Form.useForm];\n});\n_c2 = MemberInformation;\nfunction MemberLogo({\n  value,\n  onChange\n}) {\n  _s3();\n  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑状态\n\n  // 上传头像\n  const avatarUpload = link => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(link);\n  };\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"TeamLogo-form-avatar\",\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        style: {\n          width: 40,\n          height: 40\n        },\n        src: value,\n        icon: /*#__PURE__*/_jsxDEV(NoAvatarIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"TeamLogo-form-upload\",\n        children: /*#__PURE__*/_jsxDEV(CameraOutlined, {\n          title: \"\\u4E0A\\u4F20logo\",\n          className: \"TeamLogo-form-uploadIcon fontsize-14\",\n          onClick: event => setIsModalVisible(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      title: \"\\u4E0A\\u4F20logo\",\n      className: \"TeamLogo-modal\",\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      children: /*#__PURE__*/_jsxDEV(ImgUpload, {\n        avatarUpload: avatarUpload,\n        onCancel: () => setIsModalVisible(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 10\n  }, this);\n}\n\n// 图片上传\n_s3(MemberLogo, \"ZFwHEtl1ZQoNaflbPbQvGOHlSaM=\");\n_c3 = MemberLogo;\nfunction ImgUpload(props) {\n  const {\n    avatarUpload,\n    onCancel\n  } = props;\n  const dataSource = {\n    maxCount: 1,\n    name: \"file\",\n    multiple: false,\n    showUploadList: false,\n    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,\n    beforeUpload: file => {\n      const isPNG = file.type === \"image/png\" || file.type === 'image/jpeg';\n      if (!isPNG) {\n        globalUtil.error(`${file.name}不是图片格式`);\n      }\n      return isPNG || Upload.LIST_IGNORE;\n    },\n    onChange(info) {\n      onCancel();\n      const {\n        status,\n        response\n      } = info.file;\n      if (status == \"uploading\") {\n        console.log(info.file, info.fileList);\n      }\n      if (status === \"done\") {\n        if (response.resultCode == 200) {\n          avatarUpload(response.link);\n          globalUtil.success('上传成功');\n        } else {\n          globalUtil.error(\"上传失败\");\n        }\n      } else if (status === \"error\") {\n        globalUtil.error(`${info.file.name} file upload failed.`);\n      }\n    },\n    onDrop(e) {\n      console.log(\"Dropped files\", e.dataTransfer.files);\n    }\n  };\n\n  // 预览/裁剪图片\n  const onPreview = async file => {\n    let src = file.url;\n    if (!src) {\n      src = await new Promise(resolve => {\n        const reader = new FileReader();\n        reader.readAsDataURL(file.originFileObj);\n        reader.onload = () => resolve(reader.result);\n      });\n    }\n    const image = new Image();\n    image.src = src;\n    const imgWindow = window.open(src);\n    imgWindow.document.write(image.outerHTML);\n  };\n  return /*#__PURE__*/_jsxDEV(ImgCrop, {\n    modalClassName: \"clippingImgCrop\",\n    rotate: true,\n    modalTitle: \"编辑图片\",\n    modalOk: \"\\u786E\\u8BA4\",\n    modalCancel: \"\\u53D6\\u6D88\",\n    children: /*#__PURE__*/_jsxDEV(Dragger, {\n      ...dataSource,\n      onPreview: onPreview,\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"ant-upload-drag-icon\",\n        children: /*#__PURE__*/_jsxDEV(QuestionsUploadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"ant-upload-text\",\n        children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u52A8\\u56FE\\u7247\\u81F3\\u6B64\\u5904\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#999',\n          marginTop: 2\n        },\n        children: \"\\u56FE\\u7247\\u683C\\u5F0F\\uFF1Ajpg\\u3001png\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this);\n}\n_c4 = ImgUpload;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"MemberFromItem\");\n$RefreshReg$(_c2, \"MemberInformation\");\n$RefreshReg$(_c3, \"MemberLogo\");\n$RefreshReg$(_c4, \"ImgUpload\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Input", "Form", "Avatar", "Select", "Upload", "DraggablePopUp", "CameraOutlined", "QuestionsUploadIcon", "useParams", "httpSettings", "ImgCrop", "Draggable", "globalUtil", "NoAvatarIcon", "useQueryClient", "get_team_mbr_user_info", "useDispatch", "jsxDEV", "_jsxDEV", "Option", "<PERSON><PERSON>", "MemberFromItem", "value", "onChange", "memberItem", "placeholder<PERSON><PERSON><PERSON>", "_s", "modifyBlur", "e", "target", "sexChange", "className", "children", "name", "MemberLogo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultValue", "style", "width", "placeholder", "display", "justifyContent", "alignItems", "autoComplete", "onBlur", "_c", "MemberInformation", "memberPopVisible", "onClose", "formItemData", "refreshUserList", "_s2", "draggleRef", "queryClient", "dispatch", "disabled", "setDisabled", "teamId", "form", "useForm", "formData", "setFormData", "bounds", "setBounds", "left", "top", "bottom", "right", "onStart", "_event", "uiData", "_draggleRef$current", "clientWidth", "clientHeight", "window", "document", "documentElement", "targetRect", "current", "getBoundingClientRect", "x", "y", "formData_", "for<PERSON>ach", "item", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onCancel", "resetFields", "updateUserInfo", "getFieldsValue", "params", "userId", "gender", "sex", "setting_225_update_user_info", "then", "res", "resultCode", "success", "invalidateQueries", "error", "resultMessage", "title", "onMouseOver", "onMouseOut", "centered", "destroyOnClose", "maskClosable", "keyboard", "open", "cancelText", "onOk", "okText", "modalRender", "modal", "handle", "event", "ref", "labelAlign", "colon", "labelCol", "span", "offset", "map", "itemx", "<PERSON><PERSON>", "label", "margin", "key", "color", "marginLeft", "marginBottom", "desc", "marginRight", "fontSize", "_c2", "_s3", "isModalVisible", "setIsModalVisible", "avatarUpload", "link", "Fragment", "height", "src", "icon", "onClick", "footer", "ImgUpload", "_c3", "props", "dataSource", "maxCount", "multiple", "showUploadList", "action", "process", "env", "REACT_APP_BASE_URL", "beforeUpload", "file", "isPNG", "type", "LIST_IGNORE", "info", "status", "response", "console", "log", "fileList", "onDrop", "dataTransfer", "files", "onPreview", "url", "Promise", "resolve", "reader", "FileReader", "readAsDataURL", "originFileObj", "onload", "result", "image", "Image", "imgWindow", "write", "outerHTML", "modalClassName", "rotate", "modalTitle", "modalOk", "modalCancel", "marginTop", "_c4", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/settings/utils/MemberInformation.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Input, Form, Avatar, Select, Upload } from \"antd\";\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";\r\nimport { CameraOutlined } from \"@ant-design/icons\";\r\nimport { QuestionsUploadIcon } from \"@components/IconUtil\";\r\nimport \"./MemberInformation.scss\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport * as httpSettings from '@common/api/http';\r\nimport ImgCrop from \"antd-img-crop\";\r\nimport Draggable from 'react-draggable';\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { NoAvatarIcon } from '@components/IconUtil';\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { get_team_mbr_user_info } from \"@/settings/store/actionCreators\"\r\nimport { useDispatch } from \"react-redux\";\r\nconst { Option } = Select;\r\nconst { Dragger } = Upload;\r\n\r\n// 可修改项\r\nfunction MemberFromItem({ value, onChange, memberItem, placeholderValue }) {\r\n  useEffect(() => {\r\n    onChange?.(value)\r\n  },[memberItem])\r\n\r\n  const modifyBlur = (e) => {\r\n    onChange?.(e.target.value)\r\n  };\r\n  const sexChange = (e) => {\r\n    onChange?.(e)\r\n  }\r\n  return (\r\n    <div className=\"MemberFromItem\">\r\n      {\r\n        memberItem.name == \"avatar\" && \r\n        <MemberLogo value={value} onChange={onChange}/>\r\n      }\r\n\r\n      {\r\n        memberItem.name =='sex' &&\r\n        <Select className=\"MemberFromItem-sex\" defaultValue={value} style={{ width: 100 }} placeholder={placeholderValue} onChange={sexChange}>\r\n          <Option value={0}><div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>男</div></Option>\r\n          <Option value={1}><div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>女</div></Option>\r\n        </Select>\r\n      }\r\n\r\n      {\r\n        (memberItem.name != \"sex\" && memberItem.name != \"avatar\") &&\r\n        <Input\r\n          autoComplete=\"off\"\r\n          onBlur={modifyBlur}\r\n          defaultValue={value}\r\n          placeholder={placeholderValue}\r\n        />\r\n      }\r\n    </div>\r\n  );\r\n}\r\n// memberPopVisible 弹窗状态\r\n// onClose 弹窗关闭事件\r\n// memberItem 成员数据对象(对象类型)\r\n// formItemData 封装好的form表单数据\r\nexport default function MemberInformation({memberPopVisible,onClose,memberItem,formItemData,refreshUserList}) { //,allUsers=[],setAllUsers\r\n  const draggleRef = useRef(null);\r\n  const queryClient = useQueryClient();\r\n  const dispatch = useDispatch();\r\n  const [disabled, setDisabled] = useState(false);\r\n  const { teamId } = useParams();\r\n  const [form] = Form.useForm();\r\n  const [formData, setFormData] = useState([])\r\n  const [bounds, setBounds] = useState({\r\n    left: 0,\r\n    top: 0,\r\n    bottom: 0,\r\n    right: 0,\r\n  });\r\n  const onStart = (_event, uiData) => {\r\n    const { clientWidth, clientHeight } = window.document.documentElement;\r\n    const targetRect = draggleRef.current?.getBoundingClientRect();\r\n    if (!targetRect) {\r\n      return;\r\n    }\r\n    setBounds({\r\n      left: -targetRect.left + uiData.x,\r\n      right: clientWidth - (targetRect.right - uiData.x),\r\n      top: -targetRect.top + uiData.y,\r\n      bottom: clientHeight - (targetRect.bottom - uiData.y),\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    if(memberPopVisible) {\r\n      let formData_ = {}\r\n      formItemData.forEach((item, index) => {formData_[item.name] = item.value})\r\n      form.setFieldsValue({\r\n        ...formData_\r\n      })\r\n      setFormData([...formItemData])\r\n    }\r\n  }, [formItemData]);\r\n  const onCancel = () => {\r\n    onClose()\r\n    form.resetFields()\r\n    setFormData([])\r\n  }\r\n  const updateUserInfo = () => {\r\n    let formData = form.getFieldsValue(true)\r\n    let params = {\r\n      teamId: teamId,\r\n      userId: memberItem.userId,\r\n      gender: formData.sex,\r\n      ...formData,\r\n    };\r\n    httpSettings.setting_225_update_user_info(params).then((res) => {\r\n      if (res.resultCode === 200) {\r\n        globalUtil.success('修改成功')\r\n        onClose()\r\n        setFormData([])\r\n        refreshUserList();\r\n        queryClient.invalidateQueries(['get_team_allusergrp']);\r\n        dispatch(get_team_mbr_user_info(teamId))\r\n      }else{\r\n        globalUtil.error(res.resultMessage||'保存失败')\r\n      }\r\n    });\r\n  }\r\n  return (\r\n    <div className=\"MemberInformation\">\r\n      <DraggablePopUp\r\n        className=\"MemberInformation-modal\"\r\n        title={\r\n          <div\r\n          className=\"dragInformTitle\"\r\n          onMouseOver={() => {if (disabled) setDisabled(false)}}\r\n          onMouseOut={() => setDisabled(true)}>成员信息</div>\r\n        }\r\n        width={600}\r\n        centered\r\n        destroyOnClose\r\n        maskClosable={false}\r\n        keyboard={true}\r\n        open={memberPopVisible}\r\n        cancelText=\"取消\"\r\n        onCancel={onCancel}\r\n        onOk={updateUserInfo}\r\n        okText=\"保存\"\r\n        modalRender={(modal) => (\r\n          <Draggable\r\n            handle='.dragInformTitle'\r\n            disabled={disabled}\r\n            bounds={bounds}\r\n            onStart={(event, uiData) => onStart(event, uiData)}\r\n          >\r\n            <div ref={draggleRef}>{modal}</div>\r\n          </Draggable>\r\n        )}\r\n      >\r\n        <div>\r\n          <Form\r\n          form={form}\r\n            labelAlign=\"left\"\r\n            colon={false}\r\n            labelCol={{ span: 5, offset: 0 }}\r\n          >\r\n            {formData.map((itemx, index) => (\r\n              <Form.Item label={itemx.label} style={{margin:0}}> \r\n                <div style={{display:'flex',alignItems:'center'}}>\r\n                  <Form.Item name={itemx.name} key={itemx.key}>\r\n                    <MemberFromItem memberItem={itemx} placeholderValue={itemx.placeholder}/>\r\n                  </Form.Item>\r\n                  <span style={{color:'#999',marginLeft:20,marginBottom:5}}>{itemx.desc}</span>\r\n                </div>\r\n              </Form.Item>\r\n            ))}\r\n            <span style={{display:'flex',alignItems:'center',color:'#999'}}>\r\n              <span className=\"iconfont tishi\" style={{color:'#F59A23',marginRight: 4, fontSize: 18}}/>\r\n              成员信息修改后，只会在此团队内显示，团队内成员都可以看见\r\n            </span>\r\n          </Form>\r\n        </div>\r\n      </DraggablePopUp>\r\n    </div>\r\n  );\r\n}\r\n\r\n// 上传头像\r\nfunction MemberLogo({value, onChange}){\r\n  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑状态\r\n\r\n  // 上传头像\r\n  const avatarUpload = (link) => {\r\n    onChange?.(link)\r\n  }\r\n\r\n  return <React.Fragment>\r\n    <div className=\"TeamLogo-form-avatar\">\r\n      <Avatar\r\n        style={{ width: 40, height: 40 }}\r\n        src={value}\r\n        icon={<NoAvatarIcon/>}/>\r\n      <div className=\"TeamLogo-form-upload\">\r\n        <CameraOutlined\r\n          title=\"上传logo\"\r\n          className=\"TeamLogo-form-uploadIcon fontsize-14\"\r\n          onClick={(event) => setIsModalVisible(true)}/>\r\n      </div>\r\n    </div>\r\n    <DraggablePopUp\r\n      title=\"上传logo\"\r\n      className=\"TeamLogo-modal\"\r\n      open={isModalVisible}\r\n      onCancel={() => setIsModalVisible(false)}\r\n      footer={null}>\r\n      <ImgUpload avatarUpload={avatarUpload} onCancel={() => setIsModalVisible(false)}/>\r\n    </DraggablePopUp>\r\n  </React.Fragment>\r\n}\r\n\r\n// 图片上传\r\nfunction ImgUpload(props) {\r\n  const {avatarUpload,onCancel} = props\r\n  const dataSource = {\r\n    maxCount: 1,\r\n    name: \"file\",\r\n    multiple: false,\r\n    showUploadList: false,\r\n    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,\r\n    beforeUpload: (file) => {\r\n      const isPNG = file.type === \"image/png\" || file.type === 'image/jpeg';\r\n      if (!isPNG) {\r\n        globalUtil.error(`${file.name}不是图片格式`);\r\n      }\r\n      return isPNG || Upload.LIST_IGNORE;\r\n    },\r\n    onChange(info) {\r\n      onCancel()\r\n      const { status, response } = info.file;\r\n      if (status == \"uploading\") {\r\n        console.log(info.file, info.fileList);\r\n      }\r\n      if (status === \"done\") {\r\n        if(response.resultCode == 200) {\r\n          avatarUpload(response.link)\r\n          globalUtil.success('上传成功');\r\n        } else {\r\n          globalUtil.error(\"上传失败\")\r\n        }\r\n      } else if (status === \"error\") {\r\n        globalUtil.error(`${info.file.name} file upload failed.`);\r\n      }\r\n    },\r\n    onDrop(e) {\r\n      console.log(\"Dropped files\", e.dataTransfer.files);\r\n    },\r\n  };\r\n  \r\n  // 预览/裁剪图片\r\n  const onPreview = async (file) => {\r\n    let src = file.url;\r\n    if (!src) {\r\n      src = await new Promise((resolve) => {\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file.originFileObj);\r\n        reader.onload = () => resolve(reader.result);\r\n      });\r\n    }\r\n    const image = new Image();\r\n    image.src = src;\r\n    const imgWindow = window.open(src);\r\n    imgWindow.document.write(image.outerHTML);\r\n  };\r\n  \r\n  return (\r\n    <ImgCrop modalClassName=\"clippingImgCrop\" rotate modalTitle={\"编辑图片\"} modalOk=\"确认\" modalCancel=\"取消\">\r\n      <Dragger {...dataSource} onPreview={onPreview}>\r\n        <p className=\"ant-upload-drag-icon\">\r\n          <QuestionsUploadIcon />\r\n        </p>\r\n        <p className=\"ant-upload-text\">点击或拖动图片至此处</p>\r\n        <p style={{color:'#999',marginTop:2}}>图片格式：jpg、png</p>\r\n      </Dragger>\r\n    </ImgCrop>\r\n  );\r\n}"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAQ,MAAM;AAC1D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,OAAO,0BAA0B;AACjC,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAO,KAAKC,YAAY,MAAM,kBAAkB;AAChD,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC1C,MAAM;EAAEC;AAAO,CAAC,GAAGhB,MAAM;AACzB,MAAM;EAAEiB;AAAQ,CAAC,GAAGhB,MAAM;;AAE1B;AACA,SAASiB,cAAcA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,UAAU;EAAEC;AAAiB,CAAC,EAAE;EAAAC,EAAA;EACzE7B,SAAS,CAAC,MAAM;IACd0B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGD,KAAK,CAAC;EACnB,CAAC,EAAC,CAACE,UAAU,CAAC,CAAC;EAEf,MAAMG,UAAU,GAAIC,CAAC,IAAK;IACxBL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGK,CAAC,CAACC,MAAM,CAACP,KAAK,CAAC;EAC5B,CAAC;EACD,MAAMQ,SAAS,GAAIF,CAAC,IAAK;IACvBL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGK,CAAC,CAAC;EACf,CAAC;EACD,oBACEV,OAAA;IAAKa,SAAS,EAAC,gBAAgB;IAAAC,QAAA,GAE3BR,UAAU,CAACS,IAAI,IAAI,QAAQ,iBAC3Bf,OAAA,CAACgB,UAAU;MAACZ,KAAK,EAAEA,KAAM;MAACC,QAAQ,EAAEA;IAAS;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,EAI/Cd,UAAU,CAACS,IAAI,IAAG,KAAK,iBACvBf,OAAA,CAACf,MAAM;MAAC4B,SAAS,EAAC,oBAAoB;MAACQ,YAAY,EAAEjB,KAAM;MAACkB,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAACC,WAAW,EAAEjB,gBAAiB;MAACF,QAAQ,EAAEO,SAAU;MAAAE,QAAA,gBACpId,OAAA,CAACC,MAAM;QAACG,KAAK,EAAE,CAAE;QAAAU,QAAA,eAACd,OAAA;UAAKsB,KAAK,EAAE;YAACG,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,QAAQ;YAACC,UAAU,EAAC;UAAQ,CAAE;UAAAb,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5GpB,OAAA,CAACC,MAAM;QAACG,KAAK,EAAE,CAAE;QAAAU,QAAA,eAACd,OAAA;UAAKsB,KAAK,EAAE;YAACG,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,QAAQ;YAACC,UAAU,EAAC;UAAQ,CAAE;UAAAb,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtG,CAAC,EAIRd,UAAU,CAACS,IAAI,IAAI,KAAK,IAAIT,UAAU,CAACS,IAAI,IAAI,QAAQ,iBACxDf,OAAA,CAAClB,KAAK;MACJ8C,YAAY,EAAC,KAAK;MAClBC,MAAM,EAAEpB,UAAW;MACnBY,YAAY,EAAEjB,KAAM;MACpBoB,WAAW,EAAEjB;IAAiB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAED,CAAC;AAEV;AACA;AACA;AACA;AACA;AAAAZ,EAAA,CAzCSL,cAAc;AAAA2B,EAAA,GAAd3B,cAAc;AA0CvB,eAAe,SAAS4B,iBAAiBA,CAAC;EAACC,gBAAgB;EAACC,OAAO;EAAC3B,UAAU;EAAC4B,YAAY;EAACC;AAAe,CAAC,EAAE;EAAAC,GAAA;EAAE;EAC9G,MAAMC,UAAU,GAAGxD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMyD,WAAW,GAAG1C,cAAc,CAAC,CAAC;EACpC,MAAM2C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM;IAAE8D;EAAO,CAAC,GAAGpD,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACqD,IAAI,CAAC,GAAG5D,IAAI,CAAC6D,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmE,MAAM,EAAEC,SAAS,CAAC,GAAGpE,QAAQ,CAAC;IACnCqE,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAAA,IAAAC,mBAAA;IAClC,MAAM;MAAEC,WAAW;MAAEC;IAAa,CAAC,GAAGC,MAAM,CAACC,QAAQ,CAACC,eAAe;IACrE,MAAMC,UAAU,IAAAN,mBAAA,GAAGnB,UAAU,CAAC0B,OAAO,cAAAP,mBAAA,uBAAlBA,mBAAA,CAAoBQ,qBAAqB,CAAC,CAAC;IAC9D,IAAI,CAACF,UAAU,EAAE;MACf;IACF;IACAd,SAAS,CAAC;MACRC,IAAI,EAAE,CAACa,UAAU,CAACb,IAAI,GAAGM,MAAM,CAACU,CAAC;MACjCb,KAAK,EAAEK,WAAW,IAAIK,UAAU,CAACV,KAAK,GAAGG,MAAM,CAACU,CAAC,CAAC;MAClDf,GAAG,EAAE,CAACY,UAAU,CAACZ,GAAG,GAAGK,MAAM,CAACW,CAAC;MAC/Bf,MAAM,EAAEO,YAAY,IAAII,UAAU,CAACX,MAAM,GAAGI,MAAM,CAACW,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC;EAEDvF,SAAS,CAAC,MAAM;IACd,IAAGqD,gBAAgB,EAAE;MACnB,IAAImC,SAAS,GAAG,CAAC,CAAC;MAClBjC,YAAY,CAACkC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAACH,SAAS,CAACE,IAAI,CAACtD,IAAI,CAAC,GAAGsD,IAAI,CAACjE,KAAK;MAAA,CAAC,CAAC;MAC1EuC,IAAI,CAAC4B,cAAc,CAAC;QAClB,GAAGJ;MACL,CAAC,CAAC;MACFrB,WAAW,CAAC,CAAC,GAAGZ,YAAY,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAClB,MAAMsC,QAAQ,GAAGA,CAAA,KAAM;IACrBvC,OAAO,CAAC,CAAC;IACTU,IAAI,CAAC8B,WAAW,CAAC,CAAC;IAClB3B,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EACD,MAAM4B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI7B,QAAQ,GAAGF,IAAI,CAACgC,cAAc,CAAC,IAAI,CAAC;IACxC,IAAIC,MAAM,GAAG;MACXlC,MAAM,EAAEA,MAAM;MACdmC,MAAM,EAAEvE,UAAU,CAACuE,MAAM;MACzBC,MAAM,EAAEjC,QAAQ,CAACkC,GAAG;MACpB,GAAGlC;IACL,CAAC;IACDtD,YAAY,CAACyF,4BAA4B,CAACJ,MAAM,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;MAC9D,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1BzF,UAAU,CAAC0F,OAAO,CAAC,MAAM,CAAC;QAC1BnD,OAAO,CAAC,CAAC;QACTa,WAAW,CAAC,EAAE,CAAC;QACfX,eAAe,CAAC,CAAC;QACjBG,WAAW,CAAC+C,iBAAiB,CAAC,CAAC,qBAAqB,CAAC,CAAC;QACtD9C,QAAQ,CAAC1C,sBAAsB,CAAC6C,MAAM,CAAC,CAAC;MAC1C,CAAC,MAAI;QACHhD,UAAU,CAAC4F,KAAK,CAACJ,GAAG,CAACK,aAAa,IAAE,MAAM,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ,CAAC;EACD,oBACEvF,OAAA;IAAKa,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChCd,OAAA,CAACb,cAAc;MACb0B,SAAS,EAAC,yBAAyB;MACnC2E,KAAK,eACHxF,OAAA;QACAa,SAAS,EAAC,iBAAiB;QAC3B4E,WAAW,EAAEA,CAAA,KAAM;UAAC,IAAIjD,QAAQ,EAAEC,WAAW,CAAC,KAAK,CAAC;QAAA,CAAE;QACtDiD,UAAU,EAAEA,CAAA,KAAMjD,WAAW,CAAC,IAAI,CAAE;QAAA3B,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAC/C;MACDG,KAAK,EAAE,GAAI;MACXoE,QAAQ;MACRC,cAAc;MACdC,YAAY,EAAE,KAAM;MACpBC,QAAQ,EAAE,IAAK;MACfC,IAAI,EAAE/D,gBAAiB;MACvBgE,UAAU,EAAC,cAAI;MACfxB,QAAQ,EAAEA,QAAS;MACnByB,IAAI,EAAEvB,cAAe;MACrBwB,MAAM,EAAC,cAAI;MACXC,WAAW,EAAGC,KAAK,iBACjBpG,OAAA,CAACP,SAAS;QACR4G,MAAM,EAAC,kBAAkB;QACzB7D,QAAQ,EAAEA,QAAS;QACnBO,MAAM,EAAEA,MAAO;QACfM,OAAO,EAAEA,CAACiD,KAAK,EAAE/C,MAAM,KAAKF,OAAO,CAACiD,KAAK,EAAE/C,MAAM,CAAE;QAAAzC,QAAA,eAEnDd,OAAA;UAAKuG,GAAG,EAAElE,UAAW;UAAAvB,QAAA,EAAEsF;QAAK;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACX;MAAAN,QAAA,eAEFd,OAAA;QAAAc,QAAA,eACEd,OAAA,CAACjB,IAAI;UACL4D,IAAI,EAAEA,IAAK;UACT6D,UAAU,EAAC,MAAM;UACjBC,KAAK,EAAE,KAAM;UACbC,QAAQ,EAAE;YAAEC,IAAI,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAAA9F,QAAA,GAEhC+B,QAAQ,CAACgE,GAAG,CAAC,CAACC,KAAK,EAAExC,KAAK,kBACzBtE,OAAA,CAACjB,IAAI,CAACgI,IAAI;YAACC,KAAK,EAAEF,KAAK,CAACE,KAAM;YAAC1F,KAAK,EAAE;cAAC2F,MAAM,EAAC;YAAC,CAAE;YAAAnG,QAAA,eAC/Cd,OAAA;cAAKsB,KAAK,EAAE;gBAACG,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC;cAAQ,CAAE;cAAAb,QAAA,gBAC/Cd,OAAA,CAACjB,IAAI,CAACgI,IAAI;gBAAChG,IAAI,EAAE+F,KAAK,CAAC/F,IAAK;gBAAAD,QAAA,eAC1Bd,OAAA,CAACG,cAAc;kBAACG,UAAU,EAAEwG,KAAM;kBAACvG,gBAAgB,EAAEuG,KAAK,CAACtF;gBAAY;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC,GADzC0F,KAAK,CAACI,GAAG;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhC,CAAC,eACZpB,OAAA;gBAAMsB,KAAK,EAAE;kBAAC6F,KAAK,EAAC,MAAM;kBAACC,UAAU,EAAC,EAAE;kBAACC,YAAY,EAAC;gBAAC,CAAE;gBAAAvG,QAAA,EAAEgG,KAAK,CAACQ;cAAI;gBAAArG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACZ,CAAC,eACFpB,OAAA;YAAMsB,KAAK,EAAE;cAACG,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACwF,KAAK,EAAC;YAAM,CAAE;YAAArG,QAAA,gBAC7Dd,OAAA;cAAMa,SAAS,EAAC,gBAAgB;cAACS,KAAK,EAAE;gBAAC6F,KAAK,EAAC,SAAS;gBAACI,WAAW,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAE;YAAE;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,4KAE3F;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV;;AAEA;AAAAgB,GAAA,CA3HwBL,iBAAiB;EAAA,QAEnBnC,cAAc,EACjBE,WAAW,EAETR,SAAS,EACbP,IAAI,CAAC6D,OAAO;AAAA;AAAA6E,GAAA,GANL1F,iBAAiB;AA4HzC,SAASf,UAAUA,CAAC;EAACZ,KAAK;EAAEC;AAAQ,CAAC,EAAC;EAAAqH,GAAA;EACpC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhJ,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE7D;EACA,MAAMiJ,YAAY,GAAIC,IAAI,IAAK;IAC7BzH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGyH,IAAI,CAAC;EAClB,CAAC;EAED,oBAAO9H,OAAA,CAACtB,KAAK,CAACqJ,QAAQ;IAAAjH,QAAA,gBACpBd,OAAA;MAAKa,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCd,OAAA,CAAChB,MAAM;QACLsC,KAAK,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEyG,MAAM,EAAE;QAAG,CAAE;QACjCC,GAAG,EAAE7H,KAAM;QACX8H,IAAI,eAAElI,OAAA,CAACL,YAAY;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC1BpB,OAAA;QAAKa,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnCd,OAAA,CAACZ,cAAc;UACboG,KAAK,EAAC,kBAAQ;UACd3E,SAAS,EAAC,sCAAsC;UAChDsH,OAAO,EAAG7B,KAAK,IAAKsB,iBAAiB,CAAC,IAAI;QAAE;UAAA3G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNpB,OAAA,CAACb,cAAc;MACbqG,KAAK,EAAC,kBAAQ;MACd3E,SAAS,EAAC,gBAAgB;MAC1BkF,IAAI,EAAE4B,cAAe;MACrBnD,QAAQ,EAAEA,CAAA,KAAMoD,iBAAiB,CAAC,KAAK,CAAE;MACzCQ,MAAM,EAAE,IAAK;MAAAtH,QAAA,eACbd,OAAA,CAACqI,SAAS;QAACR,YAAY,EAAEA,YAAa;QAACrD,QAAQ,EAAEA,CAAA,KAAMoD,iBAAiB,CAAC,KAAK;MAAE;QAAA3G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AACnB;;AAEA;AAAAsG,GAAA,CAhCS1G,UAAU;AAAAsH,GAAA,GAAVtH,UAAU;AAiCnB,SAASqH,SAASA,CAACE,KAAK,EAAE;EACxB,MAAM;IAACV,YAAY;IAACrD;EAAQ,CAAC,GAAG+D,KAAK;EACrC,MAAMC,UAAU,GAAG;IACjBC,QAAQ,EAAE,CAAC;IACX1H,IAAI,EAAE,MAAM;IACZ2H,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,KAAK;IACrBC,MAAM,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,8BAA8B;IACvEC,YAAY,EAAGC,IAAI,IAAK;MACtB,MAAMC,KAAK,GAAGD,IAAI,CAACE,IAAI,KAAK,WAAW,IAAIF,IAAI,CAACE,IAAI,KAAK,YAAY;MACrE,IAAI,CAACD,KAAK,EAAE;QACVxJ,UAAU,CAAC4F,KAAK,CAAC,GAAG2D,IAAI,CAAClI,IAAI,QAAQ,CAAC;MACxC;MACA,OAAOmI,KAAK,IAAIhK,MAAM,CAACkK,WAAW;IACpC,CAAC;IACD/I,QAAQA,CAACgJ,IAAI,EAAE;MACb7E,QAAQ,CAAC,CAAC;MACV,MAAM;QAAE8E,MAAM;QAAEC;MAAS,CAAC,GAAGF,IAAI,CAACJ,IAAI;MACtC,IAAIK,MAAM,IAAI,WAAW,EAAE;QACzBE,OAAO,CAACC,GAAG,CAACJ,IAAI,CAACJ,IAAI,EAAEI,IAAI,CAACK,QAAQ,CAAC;MACvC;MACA,IAAIJ,MAAM,KAAK,MAAM,EAAE;QACrB,IAAGC,QAAQ,CAACpE,UAAU,IAAI,GAAG,EAAE;UAC7B0C,YAAY,CAAC0B,QAAQ,CAACzB,IAAI,CAAC;UAC3BpI,UAAU,CAAC0F,OAAO,CAAC,MAAM,CAAC;QAC5B,CAAC,MAAM;UACL1F,UAAU,CAAC4F,KAAK,CAAC,MAAM,CAAC;QAC1B;MACF,CAAC,MAAM,IAAIgE,MAAM,KAAK,OAAO,EAAE;QAC7B5J,UAAU,CAAC4F,KAAK,CAAC,GAAG+D,IAAI,CAACJ,IAAI,CAAClI,IAAI,sBAAsB,CAAC;MAC3D;IACF,CAAC;IACD4I,MAAMA,CAACjJ,CAAC,EAAE;MACR8I,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE/I,CAAC,CAACkJ,YAAY,CAACC,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,MAAOb,IAAI,IAAK;IAChC,IAAIhB,GAAG,GAAGgB,IAAI,CAACc,GAAG;IAClB,IAAI,CAAC9B,GAAG,EAAE;MACRA,GAAG,GAAG,MAAM,IAAI+B,OAAO,CAAEC,OAAO,IAAK;QACnC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,aAAa,CAACnB,IAAI,CAACoB,aAAa,CAAC;QACxCH,MAAM,CAACI,MAAM,GAAG,MAAML,OAAO,CAACC,MAAM,CAACK,MAAM,CAAC;MAC9C,CAAC,CAAC;IACJ;IACA,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;IACzBD,KAAK,CAACvC,GAAG,GAAGA,GAAG;IACf,MAAMyC,SAAS,GAAG/G,MAAM,CAACoC,IAAI,CAACkC,GAAG,CAAC;IAClCyC,SAAS,CAAC9G,QAAQ,CAAC+G,KAAK,CAACH,KAAK,CAACI,SAAS,CAAC;EAC3C,CAAC;EAED,oBACE5K,OAAA,CAACR,OAAO;IAACqL,cAAc,EAAC,iBAAiB;IAACC,MAAM;IAACC,UAAU,EAAE,MAAO;IAACC,OAAO,EAAC,cAAI;IAACC,WAAW,EAAC,cAAI;IAAAnK,QAAA,eAChGd,OAAA,CAACE,OAAO;MAAA,GAAKsI,UAAU;MAAEsB,SAAS,EAAEA,SAAU;MAAAhJ,QAAA,gBAC5Cd,OAAA;QAAGa,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACjCd,OAAA,CAACX,mBAAmB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACJpB,OAAA;QAAGa,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC7CpB,OAAA;QAAGsB,KAAK,EAAE;UAAC6F,KAAK,EAAC,MAAM;UAAC+D,SAAS,EAAC;QAAC,CAAE;QAAApK,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEd;AAAC+J,GAAA,GAhEQ9C,SAAS;AAAA,IAAAvG,EAAA,EAAA2F,GAAA,EAAAa,GAAA,EAAA6C,GAAA;AAAAC,YAAA,CAAAtJ,EAAA;AAAAsJ,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}