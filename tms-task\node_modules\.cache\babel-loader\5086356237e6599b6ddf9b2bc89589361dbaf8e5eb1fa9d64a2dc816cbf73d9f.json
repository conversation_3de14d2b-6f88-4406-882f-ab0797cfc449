{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Emitter } from '../../../base/common/event.js';\nimport { KeyChord } from '../../../base/common/keyCodes.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { Selection } from '../core/selection.js';\nimport { Token } from '../languages.js';\nimport * as standaloneEnums from '../standalone/standaloneEnums.js';\nexport class KeyMod {\n  static chord(firstPart, secondPart) {\n    return KeyChord(firstPart, secondPart);\n  }\n}\nKeyMod.CtrlCmd = 2048 /* ConstKeyMod.CtrlCmd */;\nKeyMod.Shift = 1024 /* ConstKeyMod.Shift */;\nKeyMod.Alt = 512 /* ConstKeyMod.Alt */;\nKeyMod.WinCtrl = 256 /* ConstKeyMod.WinCtrl */;\nexport function createMonacoBaseAPI() {\n  return {\n    editor: undefined,\n    languages: undefined,\n    CancellationTokenSource: CancellationTokenSource,\n    Emitter: Emitter,\n    KeyCode: standaloneEnums.KeyCode,\n    KeyMod: KeyMod,\n    Position: Position,\n    Range: Range,\n    Selection: Selection,\n    SelectionDirection: standaloneEnums.SelectionDirection,\n    MarkerSeverity: standaloneEnums.MarkerSeverity,\n    MarkerTag: standaloneEnums.MarkerTag,\n    Uri: URI,\n    Token: Token\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}