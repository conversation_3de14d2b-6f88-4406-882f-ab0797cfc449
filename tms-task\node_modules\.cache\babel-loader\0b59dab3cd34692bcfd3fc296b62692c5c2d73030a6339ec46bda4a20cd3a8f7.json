{"ast": null, "code": "/**\n * Uses a LRU cache to make a given parametrized function cached.\n * Caches just the last value.\n * The key must be JSON serializable.\n*/\nexport class LRUCachedFunction {\n  constructor(fn) {\n    this.fn = fn;\n    this.lastCache = undefined;\n    this.lastArgKey = undefined;\n  }\n  get(arg) {\n    const key = JSON.stringify(arg);\n    if (this.lastArgKey !== key) {\n      this.lastArgKey = key;\n      this.lastCache = this.fn(arg);\n    }\n    return this.lastCache;\n  }\n}\n/**\n * Uses an unbounded cache (referential equality) to memoize the results of the given function.\n*/\nexport class CachedFunction {\n  constructor(fn) {\n    this.fn = fn;\n    this._map = new Map();\n  }\n  get cachedValues() {\n    return this._map;\n  }\n  get(arg) {\n    if (this._map.has(arg)) {\n      return this._map.get(arg);\n    }\n    const value = this.fn(arg);\n    this._map.set(arg, value);\n    return value;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}