{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { DiffChange } from './diffChange.js';\nimport { stringHash } from '../hash.js';\nexport class StringDiffSequence {\n  constructor(source) {\n    this.source = source;\n  }\n  getElements() {\n    const source = this.source;\n    const characters = new Int32Array(source.length);\n    for (let i = 0, len = source.length; i < len; i++) {\n      characters[i] = source.charCodeAt(i);\n    }\n    return characters;\n  }\n}\nexport function stringDiff(original, modified, pretty) {\n  return new LcsDiff(new StringDiffSequence(original), new StringDiffSequence(modified)).ComputeDiff(pretty).changes;\n}\n//\n// The code below has been ported from a C# implementation in VS\n//\nexport class Debug {\n  static Assert(condition, message) {\n    if (!condition) {\n      throw new Error(message);\n    }\n  }\n}\nexport class MyArray {\n  /**\n   * Copies a range of elements from an Array starting at the specified source index and pastes\n   * them to another Array starting at the specified destination index. The length and the indexes\n   * are specified as 64-bit integers.\n   * sourceArray:\n   *\t\tThe Array that contains the data to copy.\n   * sourceIndex:\n   *\t\tA 64-bit integer that represents the index in the sourceArray at which copying begins.\n   * destinationArray:\n   *\t\tThe Array that receives the data.\n   * destinationIndex:\n   *\t\tA 64-bit integer that represents the index in the destinationArray at which storing begins.\n   * length:\n   *\t\tA 64-bit integer that represents the number of elements to copy.\n   */\n  static Copy(sourceArray, sourceIndex, destinationArray, destinationIndex, length) {\n    for (let i = 0; i < length; i++) {\n      destinationArray[destinationIndex + i] = sourceArray[sourceIndex + i];\n    }\n  }\n  static Copy2(sourceArray, sourceIndex, destinationArray, destinationIndex, length) {\n    for (let i = 0; i < length; i++) {\n      destinationArray[destinationIndex + i] = sourceArray[sourceIndex + i];\n    }\n  }\n}\n/**\n * A utility class which helps to create the set of DiffChanges from\n * a difference operation. This class accepts original DiffElements and\n * modified DiffElements that are involved in a particular change. The\n * MarkNextChange() method can be called to mark the separation between\n * distinct changes. At the end, the Changes property can be called to retrieve\n * the constructed changes.\n */\nclass DiffChangeHelper {\n  /**\n   * Constructs a new DiffChangeHelper for the given DiffSequences.\n   */\n  constructor() {\n    this.m_changes = [];\n    this.m_originalStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n    this.m_modifiedStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n    this.m_originalCount = 0;\n    this.m_modifiedCount = 0;\n  }\n  /**\n   * Marks the beginning of the next change in the set of differences.\n   */\n  MarkNextChange() {\n    // Only add to the list if there is something to add\n    if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n      // Add the new change to our list\n      this.m_changes.push(new DiffChange(this.m_originalStart, this.m_originalCount, this.m_modifiedStart, this.m_modifiedCount));\n    }\n    // Reset for the next change\n    this.m_originalCount = 0;\n    this.m_modifiedCount = 0;\n    this.m_originalStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n    this.m_modifiedStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n  }\n  /**\n   * Adds the original element at the given position to the elements\n   * affected by the current change. The modified index gives context\n   * to the change position with respect to the original sequence.\n   * @param originalIndex The index of the original element to add.\n   * @param modifiedIndex The index of the modified element that provides corresponding position in the modified sequence.\n   */\n  AddOriginalElement(originalIndex, modifiedIndex) {\n    // The 'true' start index is the smallest of the ones we've seen\n    this.m_originalStart = Math.min(this.m_originalStart, originalIndex);\n    this.m_modifiedStart = Math.min(this.m_modifiedStart, modifiedIndex);\n    this.m_originalCount++;\n  }\n  /**\n   * Adds the modified element at the given position to the elements\n   * affected by the current change. The original index gives context\n   * to the change position with respect to the modified sequence.\n   * @param originalIndex The index of the original element that provides corresponding position in the original sequence.\n   * @param modifiedIndex The index of the modified element to add.\n   */\n  AddModifiedElement(originalIndex, modifiedIndex) {\n    // The 'true' start index is the smallest of the ones we've seen\n    this.m_originalStart = Math.min(this.m_originalStart, originalIndex);\n    this.m_modifiedStart = Math.min(this.m_modifiedStart, modifiedIndex);\n    this.m_modifiedCount++;\n  }\n  /**\n   * Retrieves all of the changes marked by the class.\n   */\n  getChanges() {\n    if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n      // Finish up on whatever is left\n      this.MarkNextChange();\n    }\n    return this.m_changes;\n  }\n  /**\n   * Retrieves all of the changes marked by the class in the reverse order\n   */\n  getReverseChanges() {\n    if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n      // Finish up on whatever is left\n      this.MarkNextChange();\n    }\n    this.m_changes.reverse();\n    return this.m_changes;\n  }\n}\n/**\n * An implementation of the difference algorithm described in\n * \"An O(ND) Difference Algorithm and its variations\" by Eugene W. Myers\n */\nexport class LcsDiff {\n  /**\n   * Constructs the DiffFinder\n   */\n  constructor(originalSequence, modifiedSequence) {\n    let continueProcessingPredicate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    this.ContinueProcessingPredicate = continueProcessingPredicate;\n    this._originalSequence = originalSequence;\n    this._modifiedSequence = modifiedSequence;\n    const [originalStringElements, originalElementsOrHash, originalHasStrings] = LcsDiff._getElements(originalSequence);\n    const [modifiedStringElements, modifiedElementsOrHash, modifiedHasStrings] = LcsDiff._getElements(modifiedSequence);\n    this._hasStrings = originalHasStrings && modifiedHasStrings;\n    this._originalStringElements = originalStringElements;\n    this._originalElementsOrHash = originalElementsOrHash;\n    this._modifiedStringElements = modifiedStringElements;\n    this._modifiedElementsOrHash = modifiedElementsOrHash;\n    this.m_forwardHistory = [];\n    this.m_reverseHistory = [];\n  }\n  static _isStringArray(arr) {\n    return arr.length > 0 && typeof arr[0] === 'string';\n  }\n  static _getElements(sequence) {\n    const elements = sequence.getElements();\n    if (LcsDiff._isStringArray(elements)) {\n      const hashes = new Int32Array(elements.length);\n      for (let i = 0, len = elements.length; i < len; i++) {\n        hashes[i] = stringHash(elements[i], 0);\n      }\n      return [elements, hashes, true];\n    }\n    if (elements instanceof Int32Array) {\n      return [[], elements, false];\n    }\n    return [[], new Int32Array(elements), false];\n  }\n  ElementsAreEqual(originalIndex, newIndex) {\n    if (this._originalElementsOrHash[originalIndex] !== this._modifiedElementsOrHash[newIndex]) {\n      return false;\n    }\n    return this._hasStrings ? this._originalStringElements[originalIndex] === this._modifiedStringElements[newIndex] : true;\n  }\n  ElementsAreStrictEqual(originalIndex, newIndex) {\n    if (!this.ElementsAreEqual(originalIndex, newIndex)) {\n      return false;\n    }\n    const originalElement = LcsDiff._getStrictElement(this._originalSequence, originalIndex);\n    const modifiedElement = LcsDiff._getStrictElement(this._modifiedSequence, newIndex);\n    return originalElement === modifiedElement;\n  }\n  static _getStrictElement(sequence, index) {\n    if (typeof sequence.getStrictElement === 'function') {\n      return sequence.getStrictElement(index);\n    }\n    return null;\n  }\n  OriginalElementsAreEqual(index1, index2) {\n    if (this._originalElementsOrHash[index1] !== this._originalElementsOrHash[index2]) {\n      return false;\n    }\n    return this._hasStrings ? this._originalStringElements[index1] === this._originalStringElements[index2] : true;\n  }\n  ModifiedElementsAreEqual(index1, index2) {\n    if (this._modifiedElementsOrHash[index1] !== this._modifiedElementsOrHash[index2]) {\n      return false;\n    }\n    return this._hasStrings ? this._modifiedStringElements[index1] === this._modifiedStringElements[index2] : true;\n  }\n  ComputeDiff(pretty) {\n    return this._ComputeDiff(0, this._originalElementsOrHash.length - 1, 0, this._modifiedElementsOrHash.length - 1, pretty);\n  }\n  /**\n   * Computes the differences between the original and modified input\n   * sequences on the bounded range.\n   * @returns An array of the differences between the two input sequences.\n   */\n  _ComputeDiff(originalStart, originalEnd, modifiedStart, modifiedEnd, pretty) {\n    const quitEarlyArr = [false];\n    let changes = this.ComputeDiffRecursive(originalStart, originalEnd, modifiedStart, modifiedEnd, quitEarlyArr);\n    if (pretty) {\n      // We have to clean up the computed diff to be more intuitive\n      // but it turns out this cannot be done correctly until the entire set\n      // of diffs have been computed\n      changes = this.PrettifyChanges(changes);\n    }\n    return {\n      quitEarly: quitEarlyArr[0],\n      changes: changes\n    };\n  }\n  /**\n   * Private helper method which computes the differences on the bounded range\n   * recursively.\n   * @returns An array of the differences between the two input sequences.\n   */\n  ComputeDiffRecursive(originalStart, originalEnd, modifiedStart, modifiedEnd, quitEarlyArr) {\n    quitEarlyArr[0] = false;\n    // Find the start of the differences\n    while (originalStart <= originalEnd && modifiedStart <= modifiedEnd && this.ElementsAreEqual(originalStart, modifiedStart)) {\n      originalStart++;\n      modifiedStart++;\n    }\n    // Find the end of the differences\n    while (originalEnd >= originalStart && modifiedEnd >= modifiedStart && this.ElementsAreEqual(originalEnd, modifiedEnd)) {\n      originalEnd--;\n      modifiedEnd--;\n    }\n    // In the special case where we either have all insertions or all deletions or the sequences are identical\n    if (originalStart > originalEnd || modifiedStart > modifiedEnd) {\n      let changes;\n      if (modifiedStart <= modifiedEnd) {\n        Debug.Assert(originalStart === originalEnd + 1, 'originalStart should only be one more than originalEnd');\n        // All insertions\n        changes = [new DiffChange(originalStart, 0, modifiedStart, modifiedEnd - modifiedStart + 1)];\n      } else if (originalStart <= originalEnd) {\n        Debug.Assert(modifiedStart === modifiedEnd + 1, 'modifiedStart should only be one more than modifiedEnd');\n        // All deletions\n        changes = [new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, 0)];\n      } else {\n        Debug.Assert(originalStart === originalEnd + 1, 'originalStart should only be one more than originalEnd');\n        Debug.Assert(modifiedStart === modifiedEnd + 1, 'modifiedStart should only be one more than modifiedEnd');\n        // Identical sequences - No differences\n        changes = [];\n      }\n      return changes;\n    }\n    // This problem can be solved using the Divide-And-Conquer technique.\n    const midOriginalArr = [0];\n    const midModifiedArr = [0];\n    const result = this.ComputeRecursionPoint(originalStart, originalEnd, modifiedStart, modifiedEnd, midOriginalArr, midModifiedArr, quitEarlyArr);\n    const midOriginal = midOriginalArr[0];\n    const midModified = midModifiedArr[0];\n    if (result !== null) {\n      // Result is not-null when there was enough memory to compute the changes while\n      // searching for the recursion point\n      return result;\n    } else if (!quitEarlyArr[0]) {\n      // We can break the problem down recursively by finding the changes in the\n      // First Half:   (originalStart, modifiedStart) to (midOriginal, midModified)\n      // Second Half:  (midOriginal + 1, minModified + 1) to (originalEnd, modifiedEnd)\n      // NOTE: ComputeDiff() is inclusive, therefore the second range starts on the next point\n      const leftChanges = this.ComputeDiffRecursive(originalStart, midOriginal, modifiedStart, midModified, quitEarlyArr);\n      let rightChanges = [];\n      if (!quitEarlyArr[0]) {\n        rightChanges = this.ComputeDiffRecursive(midOriginal + 1, originalEnd, midModified + 1, modifiedEnd, quitEarlyArr);\n      } else {\n        // We didn't have time to finish the first half, so we don't have time to compute this half.\n        // Consider the entire rest of the sequence different.\n        rightChanges = [new DiffChange(midOriginal + 1, originalEnd - (midOriginal + 1) + 1, midModified + 1, modifiedEnd - (midModified + 1) + 1)];\n      }\n      return this.ConcatenateChanges(leftChanges, rightChanges);\n    }\n    // If we hit here, we quit early, and so can't return anything meaningful\n    return [new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, modifiedEnd - modifiedStart + 1)];\n  }\n  WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr) {\n    let forwardChanges = null;\n    let reverseChanges = null;\n    // First, walk backward through the forward diagonals history\n    let changeHelper = new DiffChangeHelper();\n    let diagonalMin = diagonalForwardStart;\n    let diagonalMax = diagonalForwardEnd;\n    let diagonalRelative = midOriginalArr[0] - midModifiedArr[0] - diagonalForwardOffset;\n    let lastOriginalIndex = -1073741824 /* Constants.MIN_SAFE_SMALL_INTEGER */;\n    let historyIndex = this.m_forwardHistory.length - 1;\n    do {\n      // Get the diagonal index from the relative diagonal number\n      const diagonal = diagonalRelative + diagonalForwardBase;\n      // Figure out where we came from\n      if (diagonal === diagonalMin || diagonal < diagonalMax && forwardPoints[diagonal - 1] < forwardPoints[diagonal + 1]) {\n        // Vertical line (the element is an insert)\n        originalIndex = forwardPoints[diagonal + 1];\n        modifiedIndex = originalIndex - diagonalRelative - diagonalForwardOffset;\n        if (originalIndex < lastOriginalIndex) {\n          changeHelper.MarkNextChange();\n        }\n        lastOriginalIndex = originalIndex;\n        changeHelper.AddModifiedElement(originalIndex + 1, modifiedIndex);\n        diagonalRelative = diagonal + 1 - diagonalForwardBase; //Setup for the next iteration\n      } else {\n        // Horizontal line (the element is a deletion)\n        originalIndex = forwardPoints[diagonal - 1] + 1;\n        modifiedIndex = originalIndex - diagonalRelative - diagonalForwardOffset;\n        if (originalIndex < lastOriginalIndex) {\n          changeHelper.MarkNextChange();\n        }\n        lastOriginalIndex = originalIndex - 1;\n        changeHelper.AddOriginalElement(originalIndex, modifiedIndex + 1);\n        diagonalRelative = diagonal - 1 - diagonalForwardBase; //Setup for the next iteration\n      }\n      if (historyIndex >= 0) {\n        forwardPoints = this.m_forwardHistory[historyIndex];\n        diagonalForwardBase = forwardPoints[0]; //We stored this in the first spot\n        diagonalMin = 1;\n        diagonalMax = forwardPoints.length - 1;\n      }\n    } while (--historyIndex >= -1);\n    // Ironically, we get the forward changes as the reverse of the\n    // order we added them since we technically added them backwards\n    forwardChanges = changeHelper.getReverseChanges();\n    if (quitEarlyArr[0]) {\n      // TODO: Calculate a partial from the reverse diagonals.\n      //       For now, just assume everything after the midOriginal/midModified point is a diff\n      let originalStartPoint = midOriginalArr[0] + 1;\n      let modifiedStartPoint = midModifiedArr[0] + 1;\n      if (forwardChanges !== null && forwardChanges.length > 0) {\n        const lastForwardChange = forwardChanges[forwardChanges.length - 1];\n        originalStartPoint = Math.max(originalStartPoint, lastForwardChange.getOriginalEnd());\n        modifiedStartPoint = Math.max(modifiedStartPoint, lastForwardChange.getModifiedEnd());\n      }\n      reverseChanges = [new DiffChange(originalStartPoint, originalEnd - originalStartPoint + 1, modifiedStartPoint, modifiedEnd - modifiedStartPoint + 1)];\n    } else {\n      // Now walk backward through the reverse diagonals history\n      changeHelper = new DiffChangeHelper();\n      diagonalMin = diagonalReverseStart;\n      diagonalMax = diagonalReverseEnd;\n      diagonalRelative = midOriginalArr[0] - midModifiedArr[0] - diagonalReverseOffset;\n      lastOriginalIndex = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n      historyIndex = deltaIsEven ? this.m_reverseHistory.length - 1 : this.m_reverseHistory.length - 2;\n      do {\n        // Get the diagonal index from the relative diagonal number\n        const diagonal = diagonalRelative + diagonalReverseBase;\n        // Figure out where we came from\n        if (diagonal === diagonalMin || diagonal < diagonalMax && reversePoints[diagonal - 1] >= reversePoints[diagonal + 1]) {\n          // Horizontal line (the element is a deletion))\n          originalIndex = reversePoints[diagonal + 1] - 1;\n          modifiedIndex = originalIndex - diagonalRelative - diagonalReverseOffset;\n          if (originalIndex > lastOriginalIndex) {\n            changeHelper.MarkNextChange();\n          }\n          lastOriginalIndex = originalIndex + 1;\n          changeHelper.AddOriginalElement(originalIndex + 1, modifiedIndex + 1);\n          diagonalRelative = diagonal + 1 - diagonalReverseBase; //Setup for the next iteration\n        } else {\n          // Vertical line (the element is an insertion)\n          originalIndex = reversePoints[diagonal - 1];\n          modifiedIndex = originalIndex - diagonalRelative - diagonalReverseOffset;\n          if (originalIndex > lastOriginalIndex) {\n            changeHelper.MarkNextChange();\n          }\n          lastOriginalIndex = originalIndex;\n          changeHelper.AddModifiedElement(originalIndex + 1, modifiedIndex + 1);\n          diagonalRelative = diagonal - 1 - diagonalReverseBase; //Setup for the next iteration\n        }\n        if (historyIndex >= 0) {\n          reversePoints = this.m_reverseHistory[historyIndex];\n          diagonalReverseBase = reversePoints[0]; //We stored this in the first spot\n          diagonalMin = 1;\n          diagonalMax = reversePoints.length - 1;\n        }\n      } while (--historyIndex >= -1);\n      // There are cases where the reverse history will find diffs that\n      // are correct, but not intuitive, so we need shift them.\n      reverseChanges = changeHelper.getChanges();\n    }\n    return this.ConcatenateChanges(forwardChanges, reverseChanges);\n  }\n  /**\n   * Given the range to compute the diff on, this method finds the point:\n   * (midOriginal, midModified)\n   * that exists in the middle of the LCS of the two sequences and\n   * is the point at which the LCS problem may be broken down recursively.\n   * This method will try to keep the LCS trace in memory. If the LCS recursion\n   * point is calculated and the full trace is available in memory, then this method\n   * will return the change list.\n   * @param originalStart The start bound of the original sequence range\n   * @param originalEnd The end bound of the original sequence range\n   * @param modifiedStart The start bound of the modified sequence range\n   * @param modifiedEnd The end bound of the modified sequence range\n   * @param midOriginal The middle point of the original sequence range\n   * @param midModified The middle point of the modified sequence range\n   * @returns The diff changes, if available, otherwise null\n   */\n  ComputeRecursionPoint(originalStart, originalEnd, modifiedStart, modifiedEnd, midOriginalArr, midModifiedArr, quitEarlyArr) {\n    let originalIndex = 0,\n      modifiedIndex = 0;\n    let diagonalForwardStart = 0,\n      diagonalForwardEnd = 0;\n    let diagonalReverseStart = 0,\n      diagonalReverseEnd = 0;\n    // To traverse the edit graph and produce the proper LCS, our actual\n    // start position is just outside the given boundary\n    originalStart--;\n    modifiedStart--;\n    // We set these up to make the compiler happy, but they will\n    // be replaced before we return with the actual recursion point\n    midOriginalArr[0] = 0;\n    midModifiedArr[0] = 0;\n    // Clear out the history\n    this.m_forwardHistory = [];\n    this.m_reverseHistory = [];\n    // Each cell in the two arrays corresponds to a diagonal in the edit graph.\n    // The integer value in the cell represents the originalIndex of the furthest\n    // reaching point found so far that ends in that diagonal.\n    // The modifiedIndex can be computed mathematically from the originalIndex and the diagonal number.\n    const maxDifferences = originalEnd - originalStart + (modifiedEnd - modifiedStart);\n    const numDiagonals = maxDifferences + 1;\n    const forwardPoints = new Int32Array(numDiagonals);\n    const reversePoints = new Int32Array(numDiagonals);\n    // diagonalForwardBase: Index into forwardPoints of the diagonal which passes through (originalStart, modifiedStart)\n    // diagonalReverseBase: Index into reversePoints of the diagonal which passes through (originalEnd, modifiedEnd)\n    const diagonalForwardBase = modifiedEnd - modifiedStart;\n    const diagonalReverseBase = originalEnd - originalStart;\n    // diagonalForwardOffset: Geometric offset which allows modifiedIndex to be computed from originalIndex and the\n    //    diagonal number (relative to diagonalForwardBase)\n    // diagonalReverseOffset: Geometric offset which allows modifiedIndex to be computed from originalIndex and the\n    //    diagonal number (relative to diagonalReverseBase)\n    const diagonalForwardOffset = originalStart - modifiedStart;\n    const diagonalReverseOffset = originalEnd - modifiedEnd;\n    // delta: The difference between the end diagonal and the start diagonal. This is used to relate diagonal numbers\n    //   relative to the start diagonal with diagonal numbers relative to the end diagonal.\n    // The Even/Oddn-ness of this delta is important for determining when we should check for overlap\n    const delta = diagonalReverseBase - diagonalForwardBase;\n    const deltaIsEven = delta % 2 === 0;\n    // Here we set up the start and end points as the furthest points found so far\n    // in both the forward and reverse directions, respectively\n    forwardPoints[diagonalForwardBase] = originalStart;\n    reversePoints[diagonalReverseBase] = originalEnd;\n    // Remember if we quit early, and thus need to do a best-effort result instead of a real result.\n    quitEarlyArr[0] = false;\n    // A couple of points:\n    // --With this method, we iterate on the number of differences between the two sequences.\n    //   The more differences there actually are, the longer this will take.\n    // --Also, as the number of differences increases, we have to search on diagonals further\n    //   away from the reference diagonal (which is diagonalForwardBase for forward, diagonalReverseBase for reverse).\n    // --We extend on even diagonals (relative to the reference diagonal) only when numDifferences\n    //   is even and odd diagonals only when numDifferences is odd.\n    for (let numDifferences = 1; numDifferences <= maxDifferences / 2 + 1; numDifferences++) {\n      let furthestOriginalIndex = 0;\n      let furthestModifiedIndex = 0;\n      // Run the algorithm in the forward direction\n      diagonalForwardStart = this.ClipDiagonalBound(diagonalForwardBase - numDifferences, numDifferences, diagonalForwardBase, numDiagonals);\n      diagonalForwardEnd = this.ClipDiagonalBound(diagonalForwardBase + numDifferences, numDifferences, diagonalForwardBase, numDiagonals);\n      for (let diagonal = diagonalForwardStart; diagonal <= diagonalForwardEnd; diagonal += 2) {\n        // STEP 1: We extend the furthest reaching point in the present diagonal\n        // by looking at the diagonals above and below and picking the one whose point\n        // is further away from the start point (originalStart, modifiedStart)\n        if (diagonal === diagonalForwardStart || diagonal < diagonalForwardEnd && forwardPoints[diagonal - 1] < forwardPoints[diagonal + 1]) {\n          originalIndex = forwardPoints[diagonal + 1];\n        } else {\n          originalIndex = forwardPoints[diagonal - 1] + 1;\n        }\n        modifiedIndex = originalIndex - (diagonal - diagonalForwardBase) - diagonalForwardOffset;\n        // Save the current originalIndex so we can test for false overlap in step 3\n        const tempOriginalIndex = originalIndex;\n        // STEP 2: We can continue to extend the furthest reaching point in the present diagonal\n        // so long as the elements are equal.\n        while (originalIndex < originalEnd && modifiedIndex < modifiedEnd && this.ElementsAreEqual(originalIndex + 1, modifiedIndex + 1)) {\n          originalIndex++;\n          modifiedIndex++;\n        }\n        forwardPoints[diagonal] = originalIndex;\n        if (originalIndex + modifiedIndex > furthestOriginalIndex + furthestModifiedIndex) {\n          furthestOriginalIndex = originalIndex;\n          furthestModifiedIndex = modifiedIndex;\n        }\n        // STEP 3: If delta is odd (overlap first happens on forward when delta is odd)\n        // and diagonal is in the range of reverse diagonals computed for numDifferences-1\n        // (the previous iteration; we haven't computed reverse diagonals for numDifferences yet)\n        // then check for overlap.\n        if (!deltaIsEven && Math.abs(diagonal - diagonalReverseBase) <= numDifferences - 1) {\n          if (originalIndex >= reversePoints[diagonal]) {\n            midOriginalArr[0] = originalIndex;\n            midModifiedArr[0] = modifiedIndex;\n            if (tempOriginalIndex <= reversePoints[diagonal] && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */ + 1) {\n              // BINGO! We overlapped, and we have the full trace in memory!\n              return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n            } else {\n              // Either false overlap, or we didn't have enough memory for the full trace\n              // Just return the recursion point\n              return null;\n            }\n          }\n        }\n      }\n      // Check to see if we should be quitting early, before moving on to the next iteration.\n      const matchLengthOfLongest = (furthestOriginalIndex - originalStart + (furthestModifiedIndex - modifiedStart) - numDifferences) / 2;\n      if (this.ContinueProcessingPredicate !== null && !this.ContinueProcessingPredicate(furthestOriginalIndex, matchLengthOfLongest)) {\n        // We can't finish, so skip ahead to generating a result from what we have.\n        quitEarlyArr[0] = true;\n        // Use the furthest distance we got in the forward direction.\n        midOriginalArr[0] = furthestOriginalIndex;\n        midModifiedArr[0] = furthestModifiedIndex;\n        if (matchLengthOfLongest > 0 && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */ + 1) {\n          // Enough of the history is in memory to walk it backwards\n          return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n        } else {\n          // We didn't actually remember enough of the history.\n          //Since we are quitting the diff early, we need to shift back the originalStart and modified start\n          //back into the boundary limits since we decremented their value above beyond the boundary limit.\n          originalStart++;\n          modifiedStart++;\n          return [new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, modifiedEnd - modifiedStart + 1)];\n        }\n      }\n      // Run the algorithm in the reverse direction\n      diagonalReverseStart = this.ClipDiagonalBound(diagonalReverseBase - numDifferences, numDifferences, diagonalReverseBase, numDiagonals);\n      diagonalReverseEnd = this.ClipDiagonalBound(diagonalReverseBase + numDifferences, numDifferences, diagonalReverseBase, numDiagonals);\n      for (let diagonal = diagonalReverseStart; diagonal <= diagonalReverseEnd; diagonal += 2) {\n        // STEP 1: We extend the furthest reaching point in the present diagonal\n        // by looking at the diagonals above and below and picking the one whose point\n        // is further away from the start point (originalEnd, modifiedEnd)\n        if (diagonal === diagonalReverseStart || diagonal < diagonalReverseEnd && reversePoints[diagonal - 1] >= reversePoints[diagonal + 1]) {\n          originalIndex = reversePoints[diagonal + 1] - 1;\n        } else {\n          originalIndex = reversePoints[diagonal - 1];\n        }\n        modifiedIndex = originalIndex - (diagonal - diagonalReverseBase) - diagonalReverseOffset;\n        // Save the current originalIndex so we can test for false overlap\n        const tempOriginalIndex = originalIndex;\n        // STEP 2: We can continue to extend the furthest reaching point in the present diagonal\n        // as long as the elements are equal.\n        while (originalIndex > originalStart && modifiedIndex > modifiedStart && this.ElementsAreEqual(originalIndex, modifiedIndex)) {\n          originalIndex--;\n          modifiedIndex--;\n        }\n        reversePoints[diagonal] = originalIndex;\n        // STEP 4: If delta is even (overlap first happens on reverse when delta is even)\n        // and diagonal is in the range of forward diagonals computed for numDifferences\n        // then check for overlap.\n        if (deltaIsEven && Math.abs(diagonal - diagonalForwardBase) <= numDifferences) {\n          if (originalIndex <= forwardPoints[diagonal]) {\n            midOriginalArr[0] = originalIndex;\n            midModifiedArr[0] = modifiedIndex;\n            if (tempOriginalIndex >= forwardPoints[diagonal] && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */ + 1) {\n              // BINGO! We overlapped, and we have the full trace in memory!\n              return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n            } else {\n              // Either false overlap, or we didn't have enough memory for the full trace\n              // Just return the recursion point\n              return null;\n            }\n          }\n        }\n      }\n      // Save current vectors to history before the next iteration\n      if (numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */) {\n        // We are allocating space for one extra int, which we fill with\n        // the index of the diagonal base index\n        let temp = new Int32Array(diagonalForwardEnd - diagonalForwardStart + 2);\n        temp[0] = diagonalForwardBase - diagonalForwardStart + 1;\n        MyArray.Copy2(forwardPoints, diagonalForwardStart, temp, 1, diagonalForwardEnd - diagonalForwardStart + 1);\n        this.m_forwardHistory.push(temp);\n        temp = new Int32Array(diagonalReverseEnd - diagonalReverseStart + 2);\n        temp[0] = diagonalReverseBase - diagonalReverseStart + 1;\n        MyArray.Copy2(reversePoints, diagonalReverseStart, temp, 1, diagonalReverseEnd - diagonalReverseStart + 1);\n        this.m_reverseHistory.push(temp);\n      }\n    }\n    // If we got here, then we have the full trace in history. We just have to convert it to a change list\n    // NOTE: This part is a bit messy\n    return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n  }\n  /**\n   * Shifts the given changes to provide a more intuitive diff.\n   * While the first element in a diff matches the first element after the diff,\n   * we shift the diff down.\n   *\n   * @param changes The list of changes to shift\n   * @returns The shifted changes\n   */\n  PrettifyChanges(changes) {\n    // Shift all the changes down first\n    for (let i = 0; i < changes.length; i++) {\n      const change = changes[i];\n      const originalStop = i < changes.length - 1 ? changes[i + 1].originalStart : this._originalElementsOrHash.length;\n      const modifiedStop = i < changes.length - 1 ? changes[i + 1].modifiedStart : this._modifiedElementsOrHash.length;\n      const checkOriginal = change.originalLength > 0;\n      const checkModified = change.modifiedLength > 0;\n      while (change.originalStart + change.originalLength < originalStop && change.modifiedStart + change.modifiedLength < modifiedStop && (!checkOriginal || this.OriginalElementsAreEqual(change.originalStart, change.originalStart + change.originalLength)) && (!checkModified || this.ModifiedElementsAreEqual(change.modifiedStart, change.modifiedStart + change.modifiedLength))) {\n        const startStrictEqual = this.ElementsAreStrictEqual(change.originalStart, change.modifiedStart);\n        const endStrictEqual = this.ElementsAreStrictEqual(change.originalStart + change.originalLength, change.modifiedStart + change.modifiedLength);\n        if (endStrictEqual && !startStrictEqual) {\n          // moving the change down would create an equal change, but the elements are not strict equal\n          break;\n        }\n        change.originalStart++;\n        change.modifiedStart++;\n      }\n      const mergedChangeArr = [null];\n      if (i < changes.length - 1 && this.ChangesOverlap(changes[i], changes[i + 1], mergedChangeArr)) {\n        changes[i] = mergedChangeArr[0];\n        changes.splice(i + 1, 1);\n        i--;\n        continue;\n      }\n    }\n    // Shift changes back up until we hit empty or whitespace-only lines\n    for (let i = changes.length - 1; i >= 0; i--) {\n      const change = changes[i];\n      let originalStop = 0;\n      let modifiedStop = 0;\n      if (i > 0) {\n        const prevChange = changes[i - 1];\n        originalStop = prevChange.originalStart + prevChange.originalLength;\n        modifiedStop = prevChange.modifiedStart + prevChange.modifiedLength;\n      }\n      const checkOriginal = change.originalLength > 0;\n      const checkModified = change.modifiedLength > 0;\n      let bestDelta = 0;\n      let bestScore = this._boundaryScore(change.originalStart, change.originalLength, change.modifiedStart, change.modifiedLength);\n      for (let delta = 1;; delta++) {\n        const originalStart = change.originalStart - delta;\n        const modifiedStart = change.modifiedStart - delta;\n        if (originalStart < originalStop || modifiedStart < modifiedStop) {\n          break;\n        }\n        if (checkOriginal && !this.OriginalElementsAreEqual(originalStart, originalStart + change.originalLength)) {\n          break;\n        }\n        if (checkModified && !this.ModifiedElementsAreEqual(modifiedStart, modifiedStart + change.modifiedLength)) {\n          break;\n        }\n        const touchingPreviousChange = originalStart === originalStop && modifiedStart === modifiedStop;\n        const score = (touchingPreviousChange ? 5 : 0) + this._boundaryScore(originalStart, change.originalLength, modifiedStart, change.modifiedLength);\n        if (score > bestScore) {\n          bestScore = score;\n          bestDelta = delta;\n        }\n      }\n      change.originalStart -= bestDelta;\n      change.modifiedStart -= bestDelta;\n      const mergedChangeArr = [null];\n      if (i > 0 && this.ChangesOverlap(changes[i - 1], changes[i], mergedChangeArr)) {\n        changes[i - 1] = mergedChangeArr[0];\n        changes.splice(i, 1);\n        i++;\n        continue;\n      }\n    }\n    // There could be multiple longest common substrings.\n    // Give preference to the ones containing longer lines\n    if (this._hasStrings) {\n      for (let i = 1, len = changes.length; i < len; i++) {\n        const aChange = changes[i - 1];\n        const bChange = changes[i];\n        const matchedLength = bChange.originalStart - aChange.originalStart - aChange.originalLength;\n        const aOriginalStart = aChange.originalStart;\n        const bOriginalEnd = bChange.originalStart + bChange.originalLength;\n        const abOriginalLength = bOriginalEnd - aOriginalStart;\n        const aModifiedStart = aChange.modifiedStart;\n        const bModifiedEnd = bChange.modifiedStart + bChange.modifiedLength;\n        const abModifiedLength = bModifiedEnd - aModifiedStart;\n        // Avoid wasting a lot of time with these searches\n        if (matchedLength < 5 && abOriginalLength < 20 && abModifiedLength < 20) {\n          const t = this._findBetterContiguousSequence(aOriginalStart, abOriginalLength, aModifiedStart, abModifiedLength, matchedLength);\n          if (t) {\n            const [originalMatchStart, modifiedMatchStart] = t;\n            if (originalMatchStart !== aChange.originalStart + aChange.originalLength || modifiedMatchStart !== aChange.modifiedStart + aChange.modifiedLength) {\n              // switch to another sequence that has a better score\n              aChange.originalLength = originalMatchStart - aChange.originalStart;\n              aChange.modifiedLength = modifiedMatchStart - aChange.modifiedStart;\n              bChange.originalStart = originalMatchStart + matchedLength;\n              bChange.modifiedStart = modifiedMatchStart + matchedLength;\n              bChange.originalLength = bOriginalEnd - bChange.originalStart;\n              bChange.modifiedLength = bModifiedEnd - bChange.modifiedStart;\n            }\n          }\n        }\n      }\n    }\n    return changes;\n  }\n  _findBetterContiguousSequence(originalStart, originalLength, modifiedStart, modifiedLength, desiredLength) {\n    if (originalLength < desiredLength || modifiedLength < desiredLength) {\n      return null;\n    }\n    const originalMax = originalStart + originalLength - desiredLength + 1;\n    const modifiedMax = modifiedStart + modifiedLength - desiredLength + 1;\n    let bestScore = 0;\n    let bestOriginalStart = 0;\n    let bestModifiedStart = 0;\n    for (let i = originalStart; i < originalMax; i++) {\n      for (let j = modifiedStart; j < modifiedMax; j++) {\n        const score = this._contiguousSequenceScore(i, j, desiredLength);\n        if (score > 0 && score > bestScore) {\n          bestScore = score;\n          bestOriginalStart = i;\n          bestModifiedStart = j;\n        }\n      }\n    }\n    if (bestScore > 0) {\n      return [bestOriginalStart, bestModifiedStart];\n    }\n    return null;\n  }\n  _contiguousSequenceScore(originalStart, modifiedStart, length) {\n    let score = 0;\n    for (let l = 0; l < length; l++) {\n      if (!this.ElementsAreEqual(originalStart + l, modifiedStart + l)) {\n        return 0;\n      }\n      score += this._originalStringElements[originalStart + l].length;\n    }\n    return score;\n  }\n  _OriginalIsBoundary(index) {\n    if (index <= 0 || index >= this._originalElementsOrHash.length - 1) {\n      return true;\n    }\n    return this._hasStrings && /^\\s*$/.test(this._originalStringElements[index]);\n  }\n  _OriginalRegionIsBoundary(originalStart, originalLength) {\n    if (this._OriginalIsBoundary(originalStart) || this._OriginalIsBoundary(originalStart - 1)) {\n      return true;\n    }\n    if (originalLength > 0) {\n      const originalEnd = originalStart + originalLength;\n      if (this._OriginalIsBoundary(originalEnd - 1) || this._OriginalIsBoundary(originalEnd)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  _ModifiedIsBoundary(index) {\n    if (index <= 0 || index >= this._modifiedElementsOrHash.length - 1) {\n      return true;\n    }\n    return this._hasStrings && /^\\s*$/.test(this._modifiedStringElements[index]);\n  }\n  _ModifiedRegionIsBoundary(modifiedStart, modifiedLength) {\n    if (this._ModifiedIsBoundary(modifiedStart) || this._ModifiedIsBoundary(modifiedStart - 1)) {\n      return true;\n    }\n    if (modifiedLength > 0) {\n      const modifiedEnd = modifiedStart + modifiedLength;\n      if (this._ModifiedIsBoundary(modifiedEnd - 1) || this._ModifiedIsBoundary(modifiedEnd)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  _boundaryScore(originalStart, originalLength, modifiedStart, modifiedLength) {\n    const originalScore = this._OriginalRegionIsBoundary(originalStart, originalLength) ? 1 : 0;\n    const modifiedScore = this._ModifiedRegionIsBoundary(modifiedStart, modifiedLength) ? 1 : 0;\n    return originalScore + modifiedScore;\n  }\n  /**\n   * Concatenates the two input DiffChange lists and returns the resulting\n   * list.\n   * @param The left changes\n   * @param The right changes\n   * @returns The concatenated list\n   */\n  ConcatenateChanges(left, right) {\n    const mergedChangeArr = [];\n    if (left.length === 0 || right.length === 0) {\n      return right.length > 0 ? right : left;\n    } else if (this.ChangesOverlap(left[left.length - 1], right[0], mergedChangeArr)) {\n      // Since we break the problem down recursively, it is possible that we\n      // might recurse in the middle of a change thereby splitting it into\n      // two changes. Here in the combining stage, we detect and fuse those\n      // changes back together\n      const result = new Array(left.length + right.length - 1);\n      MyArray.Copy(left, 0, result, 0, left.length - 1);\n      result[left.length - 1] = mergedChangeArr[0];\n      MyArray.Copy(right, 1, result, left.length, right.length - 1);\n      return result;\n    } else {\n      const result = new Array(left.length + right.length);\n      MyArray.Copy(left, 0, result, 0, left.length);\n      MyArray.Copy(right, 0, result, left.length, right.length);\n      return result;\n    }\n  }\n  /**\n   * Returns true if the two changes overlap and can be merged into a single\n   * change\n   * @param left The left change\n   * @param right The right change\n   * @param mergedChange The merged change if the two overlap, null otherwise\n   * @returns True if the two changes overlap\n   */\n  ChangesOverlap(left, right, mergedChangeArr) {\n    Debug.Assert(left.originalStart <= right.originalStart, 'Left change is not less than or equal to right change');\n    Debug.Assert(left.modifiedStart <= right.modifiedStart, 'Left change is not less than or equal to right change');\n    if (left.originalStart + left.originalLength >= right.originalStart || left.modifiedStart + left.modifiedLength >= right.modifiedStart) {\n      const originalStart = left.originalStart;\n      let originalLength = left.originalLength;\n      const modifiedStart = left.modifiedStart;\n      let modifiedLength = left.modifiedLength;\n      if (left.originalStart + left.originalLength >= right.originalStart) {\n        originalLength = right.originalStart + right.originalLength - left.originalStart;\n      }\n      if (left.modifiedStart + left.modifiedLength >= right.modifiedStart) {\n        modifiedLength = right.modifiedStart + right.modifiedLength - left.modifiedStart;\n      }\n      mergedChangeArr[0] = new DiffChange(originalStart, originalLength, modifiedStart, modifiedLength);\n      return true;\n    } else {\n      mergedChangeArr[0] = null;\n      return false;\n    }\n  }\n  /**\n   * Helper method used to clip a diagonal index to the range of valid\n   * diagonals. This also decides whether or not the diagonal index,\n   * if it exceeds the boundary, should be clipped to the boundary or clipped\n   * one inside the boundary depending on the Even/Odd status of the boundary\n   * and numDifferences.\n   * @param diagonal The index of the diagonal to clip.\n   * @param numDifferences The current number of differences being iterated upon.\n   * @param diagonalBaseIndex The base reference diagonal.\n   * @param numDiagonals The total number of diagonals.\n   * @returns The clipped diagonal index.\n   */\n  ClipDiagonalBound(diagonal, numDifferences, diagonalBaseIndex, numDiagonals) {\n    if (diagonal >= 0 && diagonal < numDiagonals) {\n      // Nothing to clip, its in range\n      return diagonal;\n    }\n    // diagonalsBelow: The number of diagonals below the reference diagonal\n    // diagonalsAbove: The number of diagonals above the reference diagonal\n    const diagonalsBelow = diagonalBaseIndex;\n    const diagonalsAbove = numDiagonals - diagonalBaseIndex - 1;\n    const diffEven = numDifferences % 2 === 0;\n    if (diagonal < 0) {\n      const lowerBoundEven = diagonalsBelow % 2 === 0;\n      return diffEven === lowerBoundEven ? 0 : 1;\n    } else {\n      const upperBoundEven = diagonalsAbove % 2 === 0;\n      return diffEven === upperBoundEven ? numDiagonals - 1 : numDiagonals - 2;\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}