// VersionPlugin.js
const fs = require('fs');
const path = require('path');

class VersionPlugin {
  apply(compiler) {
    compiler.hooks.emit.tapAsync('VersionPlugin', (compilation, callback) => {
      // 这里你可以定义你的版本号，或者从其他地方获取（如package.json）
      const version = process.env.REACT_APP_VERSION;

      // 读取更新日志文件
      let changelog = null;
      try {
        const changelogPath = path.resolve(process.cwd(), 'public/changelog.json');
        if (fs.existsSync(changelogPath)) {
          const changelogContent = fs.readFileSync(changelogPath, 'utf8');
          changelog = JSON.parse(changelogContent);
        }
      } catch (error) {
        console.warn('Failed to read changelog.json:', error.message);
      }

      // 创建一个新的资产（asset）
      compilation.assets['version.json'] = {
        source: function() {
          // 返回JSON字符串
          return JSON.stringify({
            version: version,
            resultCode: 200,
            resultMessage: null,
            changelog: changelog
          }, null, 2);
        },
        size: function() {
          // 返回文件大小
          return this.source().length;
        }
      };

      callback();
    });
  }
}

module.exports = VersionPlugin;