{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\utils\\\\ArrayUtils.js\";\nimport { eSelectionListId, eMoveType, eCtxOptionType, eInputMaxLength, eTemplateTeamId } from \"@common/utils/enum\";\nimport * as _ from 'lodash';\nimport { Avatar } from \"antd\";\nimport { NoAvatarIcon } from '@common/components/IconUtil';\nimport moment from \"moment\";\nimport $ from 'jquery';\n\n// 后端返回的默认值\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DEFAULT_VALUE = -999;\n\n// 判空\nexport function isEmpty(value) {\n  let flg = false;\n  if (Object.prototype.toString.call(value) === \"[object Array]\") {\n    flg = value.length === 0 ? true : false;\n  } else if (Object.prototype.toString.call(value) === \"[object Object]\") {\n    flg = Object.keys(value).length === 0 ? true : false;\n  } else if (Object.prototype.toString.call(value) === \"[object String]\") {\n    flg = value.replace(\"/s\", \"\").length === 0 ? true : false;\n  } else if (Object.prototype.toString.call(value) === \"[object Number]\") {\n    flg = isNaN(value) || value == DEFAULT_VALUE ? true : false; // 后端返回的默认值 -999 \n  } else if (Object.prototype.toString.call(value) === \"[object Null]\") {\n    flg = true;\n  } else if (Object.prototype.toString.call(value) === \"[object Undefined]\") {\n    flg = true;\n  } else if (Object.prototype.toString.call(value) === \"[object Boolean]\") {\n    flg = value ? false : true;\n  } else if (Object.prototype.toString.call(value) === \"[object Map]\") {\n    // 判断map是否为空\n    flg = value === null || value.size === 0;\n  }\n  return flg;\n}\n\n// 判断数字是否是初始值\nexport function isInitNum(value) {\n  return value === -999 || isEmpty(value);\n}\n\n// 判断是否是数组\nexport function isArray(obj) {\n  return Object.prototype.toString.call(obj) === \"[object Array]\";\n}\n\n// 判断是否是对象\nexport function isObject(obj) {\n  return Object.prototype.toString.call(obj) === \"[object Object]\";\n}\n\n// 俩个数组取交集，mainList是主数据\nexport const intersection = (mainList, subList, key) => {\n  if (isEmpty(mainList) || isEmpty(subList)) {\n    return [];\n  }\n  let arr = subList.map(item => item[key]);\n  let newArr3 = mainList.filter(info => {\n    return new Set(arr).has(info[key]);\n  });\n  return newArr3;\n};\n\n//数组去重，mainList中去除和subList的key相同的数据\nexport const deduplication = (mainList, subList, key) => {\n  let newArr3 = mainList.filter(function (item1) {\n    return subList.every(function (item2) {\n      return item2[key] !== item1[key];\n    });\n  });\n  return newArr3;\n};\n\n/**\r\n * 匹配407列表数据,获取propValue\r\n * @param list 数据\r\n * @param _id  selectionId\r\n * @param type propType\r\n */\nexport function getPropValueByIdType(list, _id, type) {\n  return ((list || []).find(item => {\n    return item.selectionId == _id && item.propType == type;\n  }) || {\n    propValue: \"\"\n  }).propValue;\n}\n/**\r\n * 匹配407列表数据,获取list\r\n * @param list 数据\r\n * @param code selectionId\r\n */\nexport function getCodeValueListByCode(list, code) {\n  return (list || []).filter(item => {\n    return item.selectionId == code;\n  });\n}\n/**\r\n * 匹配202列表数据,获取userName\r\n * @param list 数据\r\n * @param _id  userId\r\n */\nexport function getUserNameById(list, _id) {\n  return ((list || []).find(item => {\n    return item.userId == _id;\n  }) || {\n    userName: \"\"\n  }).userName;\n}\n\n/**\r\n * 获取用户数据数组\r\n * @param {*} userList\r\n * @returns\r\n */\nexport const getUserList = (userList = []) => {\n  return (userList || []).map((item, index) => {\n    if (item.userId) {\n      item.key = item.userId.toString();\n      item.propType = item.userId.toString();\n      // item.icon = <Avatar src={item.avatar} icon={<NoAvatarIcon/>} size={24}/>\n    }\n    item.propValue = item.userName;\n    return item;\n  });\n};\nfunction avatarFormat(item) {\n  return /*#__PURE__*/_jsxDEV(Avatar, {\n    size: 24,\n    src: item === null || item === void 0 ? void 0 : item.avatar,\n    icon: /*#__PURE__*/_jsxDEV(NoAvatarIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 53\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 10\n  }, this);\n}\n\n/**\r\n * 获取用户名称Select类型\r\n * @param {*} userList\r\n * @returns\r\n */\nexport const getUserListSelect = (userList = []) => {\n  return (userList || []).map((item, index) => {\n    if (item.userId) {\n      item.icon = avatarFormat(item);\n      item.value = item.userId.toString();\n      item.key = item.userId.toString();\n    }\n    item.label = item.userName;\n    return item;\n  });\n};\n\n/**\r\n * 对象列表\r\n * @param {*} data 407对象列表数据\r\n * @param {*} needAllType 是否需要全部的对象类型: 默认false\r\n * @param {*} otherList 额外附加的List: 消息模块\r\n * @returns\r\n */\nexport const getResTypeList = (data = [], needAllType = false, selectionId = eSelectionListId.Selection_1921_Res_ObjType, otherList, filterResTypeList, isBefore = true) => {\n  let selectionList = data.filter(item => item.selectionId == selectionId && (needAllType || item.propType != \"-1\"));\n  if (otherList) {\n    if (isBefore) {\n      selectionList = [...otherList, ...selectionList]; // 放前面\n    } else {\n      selectionList = [...selectionList, ...otherList]; // 放末尾\n    }\n  }\n  if (!!filterResTypeList) {\n    selectionList = selectionList.filter(selection => filterResTypeList.some(filterResType => filterResType == selection.propType));\n  }\n  selectionList = (selectionList || []).map(item => ({\n    key: item.propType.toString(),\n    value: item.propType.toString(),\n    label: item.propValue.toString()\n  }));\n  // console.log(\"getResTypeList-selectionList\", selectionList);\n  return selectionList;\n};\n\n/**\r\n * 协作群列表\r\n * @param {*} data 407对象列表数据\r\n * @returns\r\n */\nexport const getSpacesList = (spaceList = [], teamId) => {\n  let _spaceList = spaceList.filter(item => item.id != teamId);\n  return (_spaceList || []).map(item => {\n    var _item$id, _item$id2, _item$name;\n    return {\n      key: (_item$id = item.id) === null || _item$id === void 0 ? void 0 : _item$id.toString(),\n      value: (_item$id2 = item.id) === null || _item$id2 === void 0 ? void 0 : _item$id2.toString(),\n      label: (_item$name = item.name) === null || _item$name === void 0 ? void 0 : _item$name.toString()\n    };\n  });\n};\n\n//比较两个对象是否相等\nexport const compareObj = (obj, newObj) => {\n  return _.isEqual(obj, newObj);\n};\n\n//比较两个数组是否相等\nexport const compareArr = (arr, newArr) => {\n  return _.isEqual(arr, newArr);\n};\n\n// B对象只保留A对象中存在的属性, B∩A\nexport function filterObjProperty(A = {}, B = {}) {\n  // 获取 A 对象中存在的属性\n  const validKeys = Object.keys(A);\n  // 创建一个新的对象，只保留 A 对象中存在的属性\n  const filteredObj = Object.assign({}, ...validKeys.map(key => ({\n    [key]: B[key]\n  })));\n  return filteredObj;\n}\n\n// 编辑器@UserName\nexport const getEditorUserList = data => {\n  return data.map(item => ({\n    key: item.userName,\n    value: item.userName,\n    id: item.userId\n  }));\n};\n\n// 时间格式化 dhx-schduler 日期格式化，已废弃， 更改为 scheduler.templates.format_date\nexport function dateFormat(fmt) {\n  var o = {\n    \"M+\": this.getMonth() + 1,\n    //月份\n    \"D+\": this.getDate(),\n    //日\n    \"d+\": this.getDate(),\n    //日\n    \"H+\": this.getHours(),\n    //小时\n    \"m+\": this.getMinutes(),\n    //分\n    \"s+\": this.getSeconds(),\n    //秒\n    \"q+\": Math.floor((this.getMonth() + 3) / 3),\n    //季度\n    S: this.getMilliseconds() //毫秒\n  };\n  if (/(y+|Y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + \"\").substr(4 - RegExp.$1.length));\n  for (var k in o) if (new RegExp(\"(\" + k + \")\").test(fmt)) fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : (\"00\" + o[k]).substr((\"\" + o[k]).length));\n  return fmt;\n}\n\n/**\r\n * 复制文本到剪切板中\r\n *\r\n * @export\r\n * @param {*} value 需要复制的文本\r\n * @param {*} cb 复制成功后的回调\r\n */\nexport function copyToClip(value, cb) {\n  // 动态创建 textarea 标签\n  const textarea = document.createElement(\"textarea\");\n  // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域\n  textarea.readOnly = \"readonly\";\n  textarea.style.position = \"absolute\";\n  textarea.style.left = \"-9999px\";\n  // 将要 copy 的值赋给 textarea 标签的 value 属性\n  textarea.value = value;\n  // 将 textarea 插入到 body 中\n  document.body.appendChild(textarea);\n  // 选中值并复制\n  textarea.select();\n  textarea.setSelectionRange(0, textarea.value.length);\n  document.execCommand(\"Copy\");\n  document.body.removeChild(textarea);\n  if (cb && Object.prototype.toString.call(cb) === \"[object Function]\") {\n    cb();\n  }\n}\n\n/**\r\n * @description 根据子节点获取父节点key (Tree形式)\r\n * @param {String} rootNode Tree数组\r\n * @param {Array} key 选中的节点, 可能为objId或者nodeId\r\n * @param {Array} includeOwn 是否包含自身节点，默认false\r\n * @returns \r\n */\n\nexport function getParentsKeysBykeyInTree(rootNode = [], key, includeOwn = false) {\n  for (let i in rootNode) {\n    if (rootNode[i].key == key) {\n      return includeOwn ? [rootNode[i].key] : []; // 是否返回自身节点信息\n    }\n    if (rootNode[i].children) {\n      let node = getParentsKeysBykeyInTree(rootNode[i].children, key, includeOwn);\n      if (node !== undefined) {\n        return node.concat(rootNode[i].key); //查询到把父节点连起来\n      }\n    }\n  }\n}\n\n/**\r\n * @description 根据子节点获取父节点信息\r\n * @param {String} key 叶子节点\r\n * @param {Array} arr 需要展开的节点\r\n * @param {Array} list 数据源\r\n * @returns\r\n */\n\nexport function getParentsNodeTypeById(list, key) {\n  for (let i in list) {\n    if (list[i].key == key) {\n      //查询到就返回该数组对象,顶级节点，父节点为空\n      return [];\n    }\n    if (list[i].children) {\n      let node = getParentsNodeTypeById(list[i].children, key);\n      if (node !== undefined) {\n        //查询到把父节点连起来\n        return node.concat({\n          key: list[i].key,\n          nodeType: list[i].nodeType\n        });\n      }\n    }\n  }\n}\n\n// 是否是日期\nexport function isDate(str) {\n  return moment(str).isValid();\n}\n\n// 获取选中的值\nexport const getSelectKeys = pathname => {\n  try {\n    let currentSelectId = pathname.split(\"/\")[2].split(\"-\")[1];\n    return currentSelectId;\n  } catch (error) {\n    return null;\n  }\n};\n\n/**\r\n * 时间范围是否在多少天内\r\n * @param {Object} startTime 开始时间\r\n * @param {Object} endTime   结束时间\r\n * @param {Object} compDay   是否在x天内\r\n * \r\n * 使用方法\r\n * checkDate('2022-10-23 00:00:00', '2022-10-24 00:00:01', 1);\r\n * checkDate('2022-10-23', '2022-10-24', 1);\r\n */\nexport function checkDate(startTime, endTime, compDay) {\n  if (startTime == \"\" || startTime == null || startTime == undefined) {\n    return false;\n  }\n  if (endTime == \"\" || endTime == null || endTime == undefined) {\n    return false;\n  }\n  var data1 = moment(startTime).valueOf();\n  var data2 = moment(endTime).valueOf();\n  var datadiff = data2 - data1;\n  var time = parseInt(compDay) * (60 * 60 * 24 * 1000);\n  if (datadiff < 0) {\n    return false;\n  }\n  if (datadiff > time) {\n    return false;\n  }\n  return true;\n}\n\n/**\r\n * JS 计算两个时间间隔多久（时分秒）\r\n * @param startTime \"2022-07-18 14:40:52\"\r\n * @param endTime \"2022-07-18 10:55:37\"\r\n * @return 3时45分15秒\r\n */\nexport function twoTimeInterval(startTime, endTime) {\n  try {\n    if (!startTime || !endTime) {\n      return;\n    }\n\n    // 刚刚 1min前\n    // let diff = moment(endTime).diff(moment(startTime),\"minute\")\n    // if(diff < 1){\n    //   return \"刚刚\"\n    // }\n\n    // 开始时间\n    let date1 = moment(startTime).toDate();\n\n    // 结束时间\n    let date2 = moment(endTime).toDate();\n\n    // 时间相差秒数\n    let dateDiff = date2.getTime() - date1.getTime();\n\n    // 计算出相差天数\n    let days = Math.floor(dateDiff / (24 * 3600 * 1000));\n\n    // 计算出小时数\n    let residue1 = dateDiff % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数\n    let hours = Math.floor(residue1 / (3600 * 1000));\n\n    // 计算相差分钟数\n    let residue2 = residue1 % (3600 * 1000); // 计算小时数后剩余的毫秒数\n    let minutes = Math.floor(residue2 / (60 * 1000));\n\n    // 计算相差秒数\n    let residue3 = residue2 % (60 * 1000); // 计算分钟数后剩余的毫秒数\n    let seconds = Math.round(residue3 / 1000);\n    let returnVal = (days == 0 ? \"\" : days + \"天\") + (hours == 0 ? \"\" : hours + \"时\") + (minutes == 0 ? \"\" : minutes + \"分\") + (seconds == 0 ? \"\" : seconds + \"秒\");\n    if (!returnVal) {\n      // 0秒特殊处理，显示为1秒 ，避免为空\n      returnVal = \"1秒\";\n    }\n    return returnVal;\n  } catch (e) {\n    console.error(\"twoTimeInterval\", e);\n    return 0;\n  }\n}\n\n// 判断是否跨天\nexport const checkIsMultiDay = (beginDt, dueDt) => {\n  if (isEmpty(beginDt) || isEmpty(dueDt)) {\n    return false;\n  }\n  return moment(beginDt).toDate().getDate() !== moment(dueDt).toDate().getDate();\n};\n\n// tree扁平化 \nexport function treeToArray(tree = []) {\n  return tree.reduce((pre, cur) => {\n    const {\n      children,\n      ...i\n    } = cur;\n    return pre.concat(i, children && children.length ? treeToArray(children) : []);\n  }, []);\n}\n\n// 数组转tree \nexport function arrayToTree(data = []) {\n  const obj = {};\n  data.forEach(item => {\n    obj[item.id] = {\n      ...item,\n      children: []\n    }; // 创建新的对象，包含独立的 children 数组\n  });\n  const parentList = [];\n  data.forEach(item => {\n    const parent = obj[item.parentId];\n    if (parent) {\n      parent.children.push(obj[item.id]); // 将新对象添加到父节点的 children 数组中\n    } else {\n      parentList.push(obj[item.id]);\n    }\n  });\n  return parentList;\n}\n\n// treeFormat格式label value\nexport function treeFormat(list = []) {\n  return list.map(tree => {\n    let _tree = {\n      label: tree.name,\n      value: tree.id\n    };\n    if ((tree.children || []).length > 0) {\n      _tree.children = treeFormat(tree.children);\n    }\n    return _tree;\n  });\n}\nexport function getAttrPropByType(propList = [], type) {\n  return propList.find(itemx => itemx.propType.toString() == type.toString()) || {};\n}\n\n// 将searchQuery转换为params\nexport function transformSearhQueryParams(searchQuery) {\n  let params = {};\n  if (!isEmpty(searchQuery)) {\n    searchQuery.forEach(search => {\n      params[search.col] = search.begin;\n    });\n  }\n  return params;\n}\n\n// 将searchQuery转换为params string\nexport function transformSearhQueryParamsString(searchQuery) {\n  let params = {};\n  if (!isEmpty(searchQuery)) {\n    searchQuery.forEach(search => {\n      var _search$begin;\n      params[search.col] = (_search$begin = search.begin) === null || _search$begin === void 0 ? void 0 : _search$begin.toString();\n    });\n  }\n  return params;\n}\n\n/**\r\n * 单层List上移下移, 备注：第一个上移会移动到最后一个\r\n */\nexport const handleMoveAction = (data, record, moveType) => {\n  const _data = [...data];\n  const index = _data.findIndex(item => item.key == record.key);\n  if (moveType == eMoveType.eMoveUp) {\n    // 上移\n    if (index != 0) {\n      _data[index] = _data.splice(index - 1, 1, _data[index])[0];\n    } else {\n      _data.push(_data.shift());\n    }\n  } else if (moveType == eMoveType.eMoveDown) {\n    // 下移\n    if (index != _data.length - 1) {\n      _data[index] = _data.splice(index + 1, 1, _data[index])[0];\n    } else {\n      _data.unshift(_data.splice(index, 1)[0]);\n    }\n  }\n  return _data;\n};\n\n// 系统自定义图标\nexport const getSysIconList = (selectionList = []) => {\n  let iconOptionList = selectionList.filter(el => el.selectionId == eSelectionListId.Selection_1939_Icon && el.teamId == eTemplateTeamId) || [];\n  return iconOptionList;\n};\n\n// 团队图标\nexport const getTeamIconList = (selectionList = [], teamId) => {\n  let iconOptionList = selectionList.filter(el => el.selectionId == eSelectionListId.Selection_1939_Icon && el.teamId == teamId) || [];\n  return iconOptionList;\n};\n\n// 新建对象分组\nexport const assembleGroup = (list, _actionTypeFlg) => {\n  let _list = list.reduce((pre, current, index) => {\n    if (!!current.groupName) {\n      const groupIndex = pre.findIndex(_pre => _pre.name == current.groupName);\n      if (groupIndex == -1) {\n        const group = {\n          id: current.id,\n          parentId: current.parentId,\n          name: current.groupName,\n          nameSuffix: \"\",\n          groupName: \"\",\n          actionType: _actionTypeFlg ? current.actionType : \"\",\n          // 新建对象来说，actionType是nodeType，没有用处， 个性化设置的actionType有用\n          privType: \"\",\n          seqNo: \"\",\n          type: eCtxOptionType.eGroup,\n          disabled: true,\n          children: [current]\n        };\n        pre.push(group);\n      } else {\n        pre[groupIndex].children.push(current);\n      }\n    } else {\n      // type: eCtxOptionType.eGroup 本身该节点不是分组，但是为了ContextBoard中的marginLeft,因此赋值为分组\n      pre.push({\n        ...current,\n        type: eCtxOptionType.eGroup\n      }); // groupName为空，直接认为是主节点，没有分组\n    }\n    return pre;\n  }, []);\n  return _list;\n};\n\n// 题目摘要，截取题干前50个字符\nexport const getAbstractDesc = (str, len = eInputMaxLength.fifty) => {\n  try {\n    var _$$text;\n    return (_$$text = $(str).text()) === null || _$$text === void 0 ? void 0 : _$$text.substring(0, len);\n  } catch (e) {\n    console.error(\"getAbstractDesc\", e);\n    return str;\n  }\n};\n\n// 饼状图\nexport const isPie = selectCatKey => {\n  return selectCatKey == \"Pie\";\n};\n\n// 用js判断一个元素是否为另一个元素的子元素\nexport function isParent(obj, parentObj) {\n  while (obj != undefined && obj != null && obj.tagName.toUpperCase() != 'BODY') {\n    if (obj == parentObj) {\n      return true;\n    }\n    obj = obj.parentNode;\n  }\n  return false;\n}\n\n// 格式化序号\nexport function formatSeqNo(dataSource) {\n  const formatData = (data, parentCodeNo = '') => {\n    data.forEach((item, index) => {\n      const codeNo = parentCodeNo ? `${parentCodeNo}.${index + 1}` : `${index + 1}`;\n      if (!item.codeNo) item.codeNo = codeNo; // coceNo没有值时前端生成，后端有值则以后端为准\n      if (item.children && item.children.length > 0) {\n        formatData(item.children, codeNo);\n      }\n    });\n  };\n  let treeData = formatTreeData([], dataSource, \"id\", \"parentId\");\n  formatData(treeData);\n  return treeToArray(treeData);\n}\n\n// List 转 Tree\nexport const formatTreeData = (cur = [], arr = [], childNodeIdName = \"nodeId\", parentIdName = \"nodeParentId\") => {\n  // 生成根目录\n  if (cur.length == 0) {\n    // 格式化数据格式\n    cur = arr.filter(item => arr.every(itemx => itemx[childNodeIdName] != item[parentIdName]));\n  }\n  cur.forEach(item => {\n    // item.key = item[childNodeIdName];\n    let childs = arr.filter(itemx => itemx[parentIdName] == item[childNodeIdName]);\n    if (childs.length) {\n      let turnChilds = formatTreeData(childs, arr, childNodeIdName, parentIdName);\n      if (!isEmpty(turnChilds)) {\n        item.children = turnChilds;\n      }\n    }\n  });\n  return cur;\n};\n\n// 使用try-catch去捕捉错误\n// 直接使用JSON.parse去转译，并把转译结果判断一下是不是object类型，如果是的话就返回true,否则就返回false,这样就排除了转化后非object的类型，比如\"123456789\"\nexport function isJSON(str) {\n  try {\n    if (typeof JSON.parse(str) == \"object\") {\n      return true;\n    }\n  } catch (e) {}\n  return false;\n}\n\n// 根据某个字段去重\nexport function unique(arr, val) {\n  const res = new Map();\n  return arr.filter(item => !res.has(item[val]) && res.set(item[val], 1));\n}\n\n// 系统图标\nexport function getSysIconByIdType(list, _id, type) {\n  return ((list || []).find(item => {\n    return item.selectionId == _id && item.teamId == eTemplateTeamId && item.propType == type;\n  }) || {\n    propValue: \"\"\n  }).propValue;\n}\n\n// 查找指定id的节点\nexport function findNodeById(nodeMap, id) {\n  return nodeMap[id] || null;\n}\n\n// 搜索并返回包含关键词的所有父节点\nexport function findParentNodes(nodeMap, node, parentIdName = \"parentId\") {\n  let parents = [];\n  let parentId = node[parentIdName];\n  while (parentId !== null) {\n    let parent = findNodeById(nodeMap, parentId);\n    if (parent) {\n      parents.push(parent);\n      parentId = parent[parentIdName];\n    } else {\n      break;\n    }\n  }\n  return parents;\n}\nexport function add(a, b) {\n  const precision = Math.max(getPrecision(a), getPrecision(b));\n  return (a * 10 ** precision + b * 10 ** precision) / 10 ** precision;\n}\nexport function subtract(a, b) {\n  const precision = Math.max(getPrecision(a), getPrecision(b));\n  return (a * 10 ** precision - b * 10 ** precision) / 10 ** precision;\n}\nexport function multiply(a, b) {\n  const precision = getPrecision(a) + getPrecision(b);\n  const num1 = Number(a.toString().replace(\".\", \"\"));\n  const num2 = Number(b.toString().replace(\".\", \"\"));\n  return num1 * num2 / 10 ** precision;\n}\n\n// TODO: divide算法有问题，需要重构\nexport function divide(a, b) {\n  const precision = getPrecision(b) - getPrecision(a);\n  return a / b * 10 ** precision;\n}\nfunction getPrecision(num) {\n  const str = num.toString();\n  const index = str.indexOf('.');\n  return index === -1 ? 0 : str.length - index - 1;\n}\nexport function formatNumber(input) {\n  let num = Number(input);\n  if (!isNaN(num)) {\n    return Math.floor(num);\n  } else {\n    return input;\n  }\n}\n\n// 有小数则保留俩位小数，没有则不保留\nexport function formatNumberFixed(num) {\n  let roundedNum = Math.round(num * 100) / 100;\n  // 将数字转换为字符串并查找小数点\n  let [integerPart, decimalPart] = roundedNum.toString().split('.');\n  // 如果没有小数部分或者小数部分是零（包括尾随零），则返回整数部分\n  if (!decimalPart || decimalPart === '0') {\n    return parseInt(integerPart, 10); // 返回整数\n  } else {\n    // 否则返回保留两位小数的数字（这里已经是四舍五入后的了）\n    return roundedNum; // 或者使用 parseFloat(integerPart + '.' + decimalPart) 但这是多余的\n  }\n}\nexport function getDateNow() {\n  let currentDate = new Date();\n  let month = currentDate.getMonth() + 1; // 月份是从 0 开始计数的，所以要加 1\n  let day = currentDate.getDate();\n  let formattedDate = month + \"月\" + day + \"号\";\n  return formattedDate;\n}\nexport function findCascaderParentNodes(treeArray, targetValue, parentValues = []) {\n  if (!targetValue) {\n    return parentValues;\n  }\n  for (let treeData of treeArray) {\n    if (treeData.value === targetValue) {\n      return parentValues.concat(treeData.value);\n    }\n    if (treeData.children) {\n      let result = findCascaderParentNodes(treeData.children, targetValue, parentValues.concat(treeData.value));\n      if (result) {\n        return result;\n      }\n    }\n  }\n  return parentValues;\n}\n\n// 去除末尾空白行\nexport function removeTrailingEmptyLines(textareaValue) {\n  if (!textareaValue) {\n    return textareaValue;\n  }\n  // 首先，去除字符串末尾的所有空白字符（包括空格和制表符，但不包括换行符）\n  let trimmedValue = textareaValue.trimEnd();\n\n  // 然后，使用正则表达式去除末尾的空白行\n  // 这个正则表达式匹配一个或多个换行符后跟任意数量的空白字符（包括没有字符的情况），直到字符串的末尾\n  let result = trimmedValue.replace(/(\\r?\\n)\\s*$/, '');\n  return result;\n}\nexport function listToTree(data = []) {\n  const obj = {};\n  data.map(item => {\n    obj[item.id] = item;\n    item.children = [];\n    return item;\n  });\n  const parentList = [];\n  data.forEach(item => {\n    const parent = obj[item.parentId];\n    if (parent) {\n      parent.children = parent.children || [];\n      parent.children.push({\n        ...item\n      });\n    } else {\n      parentList.push({\n        ...item\n      });\n    }\n  });\n  return parentList;\n}\n\n// 即3306008 % power(10,5) = 6008 / 1000 = 6.008 取整 6 \n// 将传入num除以10的5次方取余，再除以1000取商，增加必要的校验\nexport function getConsoleUiControlId(num) {\n  if (_.isNaN(num)) {\n    return num;\n  }\n  return Math.floor(_.divide(_.multiply(num, 1) % 100000, 1000));\n}\n\n// 即3306008 % power(10,5) = 6008 % 1000 = 8\nexport function getConsoleUiControlEnd(num) {\n  if (_.isNaN(num)) {\n    return num;\n  }\n  return _.multiply(num, 1) % 100000 % 1000;\n}\n\n// 将传入nodeTypeId*100000 + uiTypeId*1000 + length(默认值为1) 兼容字符串和数字\nexport function getConsoleUiControlIdByNodeType(nodeTypeId, uiTypeId, length = 1) {\n  if (_.isNaN(nodeTypeId)) {\n    return nodeTypeId;\n  }\n  return _.multiply(nodeTypeId, 100000) + _.multiply(uiTypeId, 1000) + length;\n}\n\n// 3314001 -> 14001\nexport function getIdByInheritId(num) {\n  var _num$toString;\n  return num === null || num === void 0 ? void 0 : (_num$toString = num.toString()) === null || _num$toString === void 0 ? void 0 : _num$toString.slice(-5);\n}\n\n// 数值类型\nexport const isNumber = value => {\n  return typeof value === 'number' && !isNaN(value);\n};", "map": {"version": 3, "names": ["eSelectionListId", "eMoveType", "eCtxOptionType", "eInputMaxLength", "eTemplateTeamId", "_", "Avatar", "NoAvatarIcon", "moment", "$", "jsxDEV", "_jsxDEV", "DEFAULT_VALUE", "isEmpty", "value", "flg", "Object", "prototype", "toString", "call", "length", "keys", "replace", "isNaN", "size", "isInitNum", "isArray", "obj", "isObject", "intersection", "mainList", "subList", "key", "arr", "map", "item", "newArr3", "filter", "info", "Set", "has", "deduplication", "item1", "every", "item2", "getPropValueByIdType", "list", "_id", "type", "find", "selectionId", "propType", "propValue", "getCodeValueListByCode", "code", "getUserNameById", "userId", "userName", "getUserList", "userList", "index", "avatarFormat", "src", "avatar", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getUserListSelect", "label", "getResTypeList", "data", "needAllType", "Selection_1921_Res_ObjType", "otherList", "filterResTypeList", "isBefore", "selectionList", "selection", "some", "filterResType", "getSpacesList", "spaceList", "teamId", "_spaceList", "id", "_item$id", "_item$id2", "_item$name", "name", "compareObj", "newObj", "isEqual", "compareArr", "newArr", "filterObjProperty", "A", "B", "validKeys", "filteredObj", "assign", "getEditorUserList", "dateFormat", "fmt", "o", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "Math", "floor", "S", "getMilliseconds", "test", "RegExp", "$1", "getFullYear", "substr", "k", "copyToClip", "cb", "textarea", "document", "createElement", "readOnly", "style", "position", "left", "body", "append<PERSON><PERSON><PERSON>", "select", "setSelectionRange", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "getParentsKeysBykeyInTree", "rootNode", "includeOwn", "i", "children", "node", "undefined", "concat", "getParentsNodeTypeById", "nodeType", "isDate", "str", "<PERSON><PERSON><PERSON><PERSON>", "getSelectKeys", "pathname", "currentSelectId", "split", "error", "checkDate", "startTime", "endTime", "compDay", "data1", "valueOf", "data2", "datadiff", "time", "parseInt", "twoTimeInterval", "date1", "toDate", "date2", "dateDiff", "getTime", "days", "residue1", "hours", "residue2", "minutes", "residue3", "seconds", "round", "returnVal", "e", "console", "checkIsMultiDay", "beginDt", "dueDt", "treeToArray", "tree", "reduce", "pre", "cur", "arrayToTree", "for<PERSON>ach", "parentList", "parent", "parentId", "push", "treeFormat", "_tree", "getAttrPropByType", "propList", "itemx", "transformSearhQueryParams", "searchQuery", "params", "search", "col", "begin", "transformSearhQueryParamsString", "_search$begin", "handleMoveAction", "record", "moveType", "_data", "findIndex", "eMoveUp", "splice", "shift", "eMoveDown", "unshift", "getSysIconList", "iconOptionList", "el", "Selection_1939_Icon", "getTeamIconList", "assembleGroup", "_actionTypeFlg", "_list", "current", "groupName", "groupIndex", "_pre", "group", "nameSuffix", "actionType", "privType", "seqNo", "eGroup", "disabled", "getAbstractDesc", "len", "fifty", "_$$text", "text", "substring", "is<PERSON><PERSON>", "selectCatKey", "isParent", "parentObj", "tagName", "toUpperCase", "parentNode", "formatSeqNo", "dataSource", "formatData", "parentCodeNo", "codeNo", "treeData", "formatTreeData", "childNodeIdName", "parentIdName", "childs", "<PERSON><PERSON><PERSON><PERSON>", "isJSON", "JSON", "parse", "unique", "val", "res", "Map", "set", "getSysIconByIdType", "findNodeById", "nodeMap", "findParentNodes", "parents", "add", "a", "b", "precision", "max", "getPrecision", "subtract", "multiply", "num1", "Number", "num2", "divide", "num", "indexOf", "formatNumber", "input", "formatNumberFixed", "roundedNum", "integerPart", "decimalPart", "getDateNow", "currentDate", "Date", "month", "day", "formattedDate", "findCascaderParentNodes", "treeArray", "targetValue", "parentValues", "result", "removeTrailingEmptyLines", "textareaValue", "trimmedValue", "trimEnd", "listToTree", "getConsoleUiControlId", "getConsoleUiControlEnd", "getConsoleUiControlIdByNodeType", "nodeTypeId", "uiTypeId", "getIdByInheritId", "_num$toString", "slice", "isNumber"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/utils/ArrayUtils.js"], "sourcesContent": ["import {eSelectionListId, eMoveType, eCtxOptionType, eInputMaxLength, eTemplateTeamId} from \"@common/utils/enum\";\r\nimport * as _ from 'lodash';\r\nimport { Avatar } from \"antd\";\r\nimport { NoAvatarIcon } from '@common/components/IconUtil';\r\nimport moment from \"moment\";\r\nimport $ from 'jquery';\r\n\r\n// 后端返回的默认值\r\nexport const DEFAULT_VALUE = -999;\r\n\r\n// 判空\r\nexport function isEmpty(value) {\r\n  let flg = false;\r\n  if (Object.prototype.toString.call(value) === \"[object Array]\") {\r\n    flg = value.length === 0 ? true : false;\r\n  } else if (Object.prototype.toString.call(value) === \"[object Object]\") {\r\n    flg = Object.keys(value).length === 0 ? true : false;\r\n  } else if (Object.prototype.toString.call(value) === \"[object String]\") {\r\n    flg = value.replace(\"/s\", \"\").length === 0 ? true : false;\r\n  } else if (Object.prototype.toString.call(value) === \"[object Number]\") {\r\n    flg = (isNaN(value) || value == DEFAULT_VALUE) ? true : false; // 后端返回的默认值 -999 \r\n  } else if (Object.prototype.toString.call(value) === \"[object Null]\") {\r\n    flg = true;\r\n  } else if (Object.prototype.toString.call(value) === \"[object Undefined]\") {\r\n    flg = true;\r\n  } else if (Object.prototype.toString.call(value) === \"[object Boolean]\") {\r\n    flg = value ? false : true;\r\n  } else if (Object.prototype.toString.call(value) === \"[object Map]\") {\r\n    // 判断map是否为空\r\n    flg = value === null || value.size === 0 ;\r\n  }\r\n  return flg;\r\n}\r\n\r\n// 判断数字是否是初始值\r\nexport function isInitNum (value) {\r\n  return value === -999 || isEmpty(value);\r\n}\r\n\r\n// 判断是否是数组\r\nexport function isArray(obj) {\r\n  return Object.prototype.toString.call(obj) === \"[object Array]\";\r\n}\r\n\r\n// 判断是否是对象\r\nexport function isObject(obj) {\r\n  return Object.prototype.toString.call(obj) === \"[object Object]\";\r\n}\r\n\r\n// 俩个数组取交集，mainList是主数据\r\nexport const intersection = (mainList, subList, key) => {\r\n  if (isEmpty(mainList) || isEmpty(subList)) {\r\n    return [];\r\n  }\r\n  let arr = subList.map((item) => item[key]);\r\n  let newArr3 = mainList.filter((info) => {\r\n    return new Set(arr).has(info[key]);\r\n  });\r\n  return newArr3;\r\n};\r\n\r\n//数组去重，mainList中去除和subList的key相同的数据\r\nexport const deduplication = (mainList, subList, key) => {\r\n  let newArr3 = mainList.filter(function (item1) {\r\n    return subList.every(function (item2) {\r\n      return item2[key] !== item1[key];\r\n    });\r\n  });\r\n  return newArr3;\r\n};\r\n\r\n/**\r\n * 匹配407列表数据,获取propValue\r\n * @param list 数据\r\n * @param _id  selectionId\r\n * @param type propType\r\n */\r\nexport function getPropValueByIdType(list, _id, type) {\r\n  return (\r\n    (list || []).find((item) => {\r\n      return item.selectionId == _id && item.propType == type;\r\n    }) || { propValue: \"\" }\r\n  ).propValue;\r\n}\r\n/**\r\n * 匹配407列表数据,获取list\r\n * @param list 数据\r\n * @param code selectionId\r\n */\r\nexport function getCodeValueListByCode(list, code) {\r\n  return (list || []).filter((item) => {\r\n    return item.selectionId == code;\r\n  });\r\n}\r\n/**\r\n * 匹配202列表数据,获取userName\r\n * @param list 数据\r\n * @param _id  userId\r\n */\r\nexport function getUserNameById(list, _id) {\r\n  return (\r\n    (list || []).find((item) => {\r\n      return item.userId == _id;\r\n    }) || { userName: \"\" }\r\n  ).userName;\r\n}\r\n\r\n/**\r\n * 获取用户数据数组\r\n * @param {*} userList\r\n * @returns\r\n */\r\nexport const getUserList = (userList = []) => {\r\n  return (userList || []).map((item, index) => {\r\n    if (item.userId) {\r\n      item.key = item.userId.toString();\r\n      item.propType = item.userId.toString();\r\n      // item.icon = <Avatar src={item.avatar} icon={<NoAvatarIcon/>} size={24}/>\r\n    }\r\n    item.propValue = item.userName;\r\n   \r\n    return item;\r\n  });\r\n};\r\n\r\n\r\nfunction avatarFormat(item){\r\n  return <Avatar size={24} src={item?.avatar} icon={<NoAvatarIcon/>}/>\r\n}\r\n\r\n/**\r\n * 获取用户名称Select类型\r\n * @param {*} userList\r\n * @returns\r\n */\r\nexport const getUserListSelect = (userList = []) => {\r\n  return (userList || []).map((item, index) => {\r\n    if (item.userId) {\r\n      item.icon = avatarFormat(item);\r\n      item.value = item.userId.toString();\r\n      item.key = item.userId.toString();\r\n    }\r\n    item.label = item.userName;\r\n    return item;\r\n  });\r\n};\r\n\r\n/**\r\n * 对象列表\r\n * @param {*} data 407对象列表数据\r\n * @param {*} needAllType 是否需要全部的对象类型: 默认false\r\n * @param {*} otherList 额外附加的List: 消息模块\r\n * @returns\r\n */\r\nexport const getResTypeList = (data = [], needAllType = false, selectionId = eSelectionListId.Selection_1921_Res_ObjType, otherList, filterResTypeList, isBefore= true) => {\r\n  let selectionList = data.filter((item) => (item.selectionId == selectionId && (needAllType || item.propType != \"-1\" )) );\r\n  if(otherList){\r\n    if(isBefore){\r\n      selectionList = [...otherList, ...selectionList]; // 放前面\r\n    } else {\r\n      selectionList = [...selectionList, ...otherList]; // 放末尾\r\n    }\r\n  }\r\n  if(!!filterResTypeList){\r\n    selectionList = selectionList.filter(selection => filterResTypeList.some(filterResType => filterResType == selection.propType));\r\n  }\r\n  selectionList = (selectionList || []).map((item) => ({\r\n    key: item.propType.toString(),\r\n    value: item.propType.toString(),\r\n    label: item.propValue.toString(),\r\n  }));\r\n  // console.log(\"getResTypeList-selectionList\", selectionList);\r\n  return selectionList;\r\n};\r\n\r\n/**\r\n * 协作群列表\r\n * @param {*} data 407对象列表数据\r\n * @returns\r\n */\r\nexport const getSpacesList = (spaceList = [], teamId) => {\r\n  let _spaceList = spaceList.filter((item) => item.id != teamId);\r\n  return (_spaceList || []).map((item) => ({\r\n    key: item.id?.toString(),\r\n    value: item.id?.toString(),\r\n    label: item.name?.toString(),\r\n  }));\r\n};\r\n\r\n//比较两个对象是否相等\r\nexport const compareObj = (obj, newObj) => {\r\n  return _.isEqual(obj, newObj);\r\n};\r\n\r\n//比较两个数组是否相等\r\nexport const compareArr = (arr, newArr) => {\r\n  return _.isEqual(arr, newArr);\r\n};\r\n\r\n// B对象只保留A对象中存在的属性, B∩A\r\nexport function filterObjProperty(A={}, B={}) {\r\n  // 获取 A 对象中存在的属性\r\n  const validKeys = Object.keys(A);\r\n  // 创建一个新的对象，只保留 A 对象中存在的属性\r\n  const filteredObj = Object.assign({}, ...validKeys.map(key => ({ [key]: B[key] })));\r\n  return filteredObj;\r\n}\r\n\r\n// 编辑器@UserName\r\nexport const getEditorUserList = (data) => {\r\n  return data.map((item) => ({\r\n    key: item.userName,\r\n    value: item.userName,\r\n    id: item.userId,\r\n  }));\r\n};\r\n\r\n// 时间格式化 dhx-schduler 日期格式化，已废弃， 更改为 scheduler.templates.format_date\r\nexport function dateFormat(fmt) {\r\n  var o = {\r\n    \"M+\": this.getMonth() + 1, //月份\r\n    \"D+\": this.getDate(), //日\r\n    \"d+\": this.getDate(), //日\r\n    \"H+\": this.getHours(), //小时\r\n    \"m+\": this.getMinutes(), //分\r\n    \"s+\": this.getSeconds(), //秒\r\n    \"q+\": Math.floor((this.getMonth() + 3) / 3), //季度\r\n    S: this.getMilliseconds(), //毫秒\r\n  };\r\n  if (/(y+|Y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + \"\").substr(4 - RegExp.$1.length));\r\n  for (var k in o) if (new RegExp(\"(\" + k + \")\").test(fmt)) fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : (\"00\" + o[k]).substr((\"\" + o[k]).length));\r\n  return fmt;\r\n}\r\n\r\n\r\n\r\n/**\r\n * 复制文本到剪切板中\r\n *\r\n * @export\r\n * @param {*} value 需要复制的文本\r\n * @param {*} cb 复制成功后的回调\r\n */\r\nexport function copyToClip(value, cb) {\r\n  // 动态创建 textarea 标签\r\n  const textarea = document.createElement(\"textarea\");\r\n  // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域\r\n  textarea.readOnly = \"readonly\";\r\n  textarea.style.position = \"absolute\";\r\n  textarea.style.left = \"-9999px\";\r\n  // 将要 copy 的值赋给 textarea 标签的 value 属性\r\n  textarea.value = value;\r\n  // 将 textarea 插入到 body 中\r\n  document.body.appendChild(textarea);\r\n  // 选中值并复制\r\n  textarea.select();\r\n  textarea.setSelectionRange(0, textarea.value.length);\r\n  document.execCommand(\"Copy\");\r\n  document.body.removeChild(textarea);\r\n  if (cb && Object.prototype.toString.call(cb) === \"[object Function]\") {\r\n    cb();\r\n  }\r\n}\r\n\r\n/**\r\n * @description 根据子节点获取父节点key (Tree形式)\r\n * @param {String} rootNode Tree数组\r\n * @param {Array} key 选中的节点, 可能为objId或者nodeId\r\n * @param {Array} includeOwn 是否包含自身节点，默认false\r\n * @returns \r\n */\r\n\r\n export function getParentsKeysBykeyInTree(rootNode = [], key, includeOwn = false) {\r\n  for (let i in rootNode) {\r\n    if (rootNode[i].key == key) {\r\n      return includeOwn ? [rootNode[i].key] : [];  // 是否返回自身节点信息\r\n    }\r\n\r\n    if (rootNode[i].children) {\r\n      let node = getParentsKeysBykeyInTree(rootNode[i].children, key, includeOwn);\r\n      if (node !== undefined) {\r\n        return node.concat(rootNode[i].key);  //查询到把父节点连起来\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * @description 根据子节点获取父节点信息\r\n * @param {String} key 叶子节点\r\n * @param {Array} arr 需要展开的节点\r\n * @param {Array} list 数据源\r\n * @returns\r\n */\r\n\r\n export function getParentsNodeTypeById(list, key) {\r\n  for (let i in list) {\r\n    if (list[i].key == key) {\r\n      //查询到就返回该数组对象,顶级节点，父节点为空\r\n      return [];\r\n    }\r\n\r\n    if (list[i].children) {\r\n      let node = getParentsNodeTypeById(list[i].children, key);\r\n      if (node !== undefined) {\r\n        //查询到把父节点连起来\r\n        return node.concat({\r\n          key: list[i].key,\r\n          nodeType: list[i].nodeType,\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 是否是日期\r\nexport function isDate(str){\r\n   return moment(str).isValid()\r\n}\r\n\r\n// 获取选中的值\r\nexport const getSelectKeys = (pathname) => {\r\n  try {\r\n    let currentSelectId = pathname.split(\"/\")[2].split(\"-\")[1]\r\n    return currentSelectId;\r\n  } catch (error) {\r\n    return null;\r\n  }\r\n}\r\n\r\n\r\n/**\r\n * 时间范围是否在多少天内\r\n * @param {Object} startTime 开始时间\r\n * @param {Object} endTime   结束时间\r\n * @param {Object} compDay   是否在x天内\r\n * \r\n * 使用方法\r\n * checkDate('2022-10-23 00:00:00', '2022-10-24 00:00:01', 1);\r\n * checkDate('2022-10-23', '2022-10-24', 1);\r\n */\r\n export function checkDate(startTime, endTime, compDay) {\r\n  if (startTime == \"\" || startTime == null || startTime == undefined) {\r\n      return false;\r\n  }\r\n  if (endTime == \"\" || endTime == null || endTime == undefined) {\r\n      return false;\r\n  }\r\n  var data1 = moment(startTime).valueOf();\r\n  var data2 = moment(endTime).valueOf();\r\n  var datadiff = data2 - data1;\r\n  var time = parseInt(compDay) * (60 * 60 * 24 * 1000);\r\n  if (datadiff < 0) {\r\n      return false;\r\n  }\r\n  if (datadiff > time) {\r\n      return false;\r\n  }\r\n  return true;\r\n}\r\n\r\n\r\n/**\r\n * JS 计算两个时间间隔多久（时分秒）\r\n * @param startTime \"2022-07-18 14:40:52\"\r\n * @param endTime \"2022-07-18 10:55:37\"\r\n * @return 3时45分15秒\r\n */\r\n export function twoTimeInterval(startTime, endTime) {\r\n  try {\r\n    if(!startTime || !endTime){\r\n      return;\r\n    }\r\n    \r\n    // 刚刚 1min前\r\n    // let diff = moment(endTime).diff(moment(startTime),\"minute\")\r\n    // if(diff < 1){\r\n    //   return \"刚刚\"\r\n    // }\r\n\r\n    // 开始时间\r\n    let date1 = moment(startTime).toDate();\r\n\r\n    // 结束时间\r\n    let date2 = moment(endTime).toDate();\r\n\r\n    // 时间相差秒数\r\n    let dateDiff = date2.getTime() - date1.getTime();\r\n\r\n    // 计算出相差天数\r\n    let days = Math.floor(dateDiff / (24 * 3600 * 1000));\r\n\r\n    // 计算出小时数\r\n    let residue1 = dateDiff % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数\r\n    let hours = Math.floor(residue1 / (3600 * 1000));\r\n\r\n    // 计算相差分钟数\r\n    let residue2 = residue1 % (3600 * 1000); // 计算小时数后剩余的毫秒数\r\n    let minutes = Math.floor(residue2 / (60 * 1000));\r\n\r\n    // 计算相差秒数\r\n    let residue3 = residue2 % (60 * 1000); // 计算分钟数后剩余的毫秒数\r\n    let seconds = Math.round(residue3 / 1000);\r\n\r\n    let returnVal =\r\n      ((days == 0) ? \"\" : days + \"天\") +\r\n      ((hours == 0) ? \"\" : hours + \"时\") +\r\n      ((minutes == 0) ? \"\" : minutes + \"分\") +\r\n      ((seconds == 0) ?  \"\" : seconds + \"秒\"); \r\n\r\n    if(!returnVal){  // 0秒特殊处理，显示为1秒 ，避免为空\r\n      returnVal = \"1秒\"\r\n    }\r\n\r\n    return returnVal;\r\n  } catch (e) {\r\n    console.error(\"twoTimeInterval\", e);\r\n    return 0;\r\n  }\r\n}\r\n\r\n // 判断是否跨天\r\nexport const checkIsMultiDay = (beginDt, dueDt) => {\r\n  if(isEmpty(beginDt) || isEmpty(dueDt)){\r\n    return false\r\n  }\r\n  return moment(beginDt).toDate().getDate() !== moment(dueDt).toDate().getDate();\r\n}\r\n\r\n// tree扁平化 \r\nexport function treeToArray(tree=[]) {\r\n  return tree.reduce((pre, cur) => {\r\n    const { children, ...i } = cur\r\n    return pre.concat(i, children && children.length ? treeToArray(children) : [])\r\n  }, [])\r\n}\r\n\r\n// 数组转tree \r\nexport function arrayToTree(data = []) {\r\n  const obj = {};\r\n  data.forEach((item) => {\r\n    obj[item.id] = { ...item, children: [] }; // 创建新的对象，包含独立的 children 数组\r\n  });\r\n  const parentList = [];\r\n  data.forEach((item) => {\r\n    const parent = obj[item.parentId];\r\n    if (parent) {\r\n      parent.children.push(obj[item.id]); // 将新对象添加到父节点的 children 数组中\r\n    } else {\r\n      parentList.push(obj[item.id]);\r\n    }\r\n  });\r\n  return parentList;\r\n}\r\n\r\n// treeFormat格式label value\r\nexport function treeFormat(list = []){\r\n  return list.map(tree => {\r\n    let _tree = {label: tree.name, value: tree.id}\r\n    if((tree.children||[]).length > 0){\r\n      _tree.children = treeFormat(tree.children)\r\n    }\r\n    return _tree\r\n  })\r\n}\r\n\r\n\r\nexport function getAttrPropByType(propList=[],type){\r\n  return propList.find(itemx => itemx.propType.toString() == type.toString()) || {};\r\n}\r\n\r\n// 将searchQuery转换为params\r\nexport function transformSearhQueryParams (searchQuery){\r\n  let params = {};\r\n  if(!isEmpty(searchQuery)){\r\n    searchQuery.forEach(search => {\r\n      params[search.col] = search.begin;\r\n    });\r\n  }\r\n  return params;\r\n}\r\n\r\n// 将searchQuery转换为params string\r\nexport function transformSearhQueryParamsString (searchQuery){\r\n  let params = {};\r\n  if(!isEmpty(searchQuery)){\r\n    searchQuery.forEach(search => {\r\n      params[search.col] = search.begin?.toString();\r\n    });\r\n  }\r\n  return params;\r\n}\r\n\r\n\r\n/**\r\n * 单层List上移下移, 备注：第一个上移会移动到最后一个\r\n */\r\nexport const handleMoveAction = (data, record, moveType) => {\r\n  const _data = [...data];\r\n  const index = _data.findIndex((item) => item.key == record.key);\r\n  if (moveType == eMoveType.eMoveUp) { // 上移\r\n    if (index != 0) {\r\n      _data[index] = _data.splice(index - 1, 1, _data[index])[0];\r\n    } else {\r\n      _data.push(_data.shift());\r\n    }\r\n  } else if (moveType == eMoveType.eMoveDown) { // 下移\r\n    if (index != _data.length - 1) {\r\n      _data[index] = _data.splice(index + 1, 1, _data[index])[0];\r\n    } else {\r\n      _data.unshift(_data.splice(index, 1)[0]);\r\n    }\r\n  }\r\n  return _data;\r\n};\r\n\r\n// 系统自定义图标\r\nexport const getSysIconList = (selectionList = [])=>{\r\n  let iconOptionList = selectionList.filter(el => el.selectionId == eSelectionListId.Selection_1939_Icon && el.teamId == eTemplateTeamId) || []\r\n  return iconOptionList;\r\n }\r\n\r\n // 团队图标\r\nexport const getTeamIconList = (selectionList = [], teamId)=>{\r\n  let iconOptionList = selectionList.filter(el => el.selectionId == eSelectionListId.Selection_1939_Icon && el.teamId == teamId) || []\r\n  return iconOptionList;\r\n }\r\n\r\n// 新建对象分组\r\nexport const assembleGroup = (list, _actionTypeFlg) => {\r\n  let _list = list.reduce((pre, current, index) => {\r\n    if(!!current.groupName){ \r\n      const groupIndex = pre.findIndex(_pre => _pre.name == current.groupName)\r\n      if (groupIndex == -1) {\r\n        const group = {\r\n          id: current.id,\r\n          parentId: current.parentId,\r\n          name: current.groupName,\r\n          nameSuffix: \"\",\r\n          groupName: \"\",\r\n          actionType: _actionTypeFlg ? current.actionType : \"\", // 新建对象来说，actionType是nodeType，没有用处， 个性化设置的actionType有用\r\n          privType: \"\",\r\n          seqNo: \"\",\r\n          type: eCtxOptionType.eGroup,\r\n          disabled: true,\r\n          children : [current],\r\n        };\r\n        pre.push(group);\r\n      } else {\r\n        pre[groupIndex].children.push(current);\r\n      }\r\n    } else {\r\n      // type: eCtxOptionType.eGroup 本身该节点不是分组，但是为了ContextBoard中的marginLeft,因此赋值为分组\r\n      pre.push({...current, type: eCtxOptionType.eGroup,}); // groupName为空，直接认为是主节点，没有分组\r\n    }\r\n    return pre;\r\n  }, []);\r\n  return _list;\r\n}\r\n\r\n// 题目摘要，截取题干前50个字符\r\nexport const getAbstractDesc = (str, len = eInputMaxLength.fifty) => {\r\n  try {\r\n    return $(str).text()?.substring(0, len)\r\n  } catch (e) {\r\n    console.error(\"getAbstractDesc\", e)\r\n    return str\r\n  }\r\n}\r\n\r\n// 饼状图\r\nexport const isPie = (selectCatKey) =>{\r\n  return selectCatKey == \"Pie\" \r\n}\r\n\r\n// 用js判断一个元素是否为另一个元素的子元素\r\nexport function isParent (obj, parentObj){\r\n  while (obj != undefined && obj != null && obj.tagName.toUpperCase() != 'BODY'){\r\n      if (obj == parentObj){\r\n          return true;\r\n      }\r\n      obj = obj.parentNode;\r\n  }\r\n  return false;\r\n}\r\n\r\n// 格式化序号\r\nexport function formatSeqNo(dataSource){\r\n  const formatData = (data, parentCodeNo = '') => {\r\n    data.forEach((item, index) => {\r\n          const codeNo = parentCodeNo ? `${parentCodeNo}.${index + 1}` : `${index + 1}`;\r\n          if(!item.codeNo) item.codeNo = codeNo;  // coceNo没有值时前端生成，后端有值则以后端为准\r\n        if (item.children && item.children.length > 0) {\r\n            formatData(item.children, codeNo);\r\n        }\r\n    });\r\n  };\r\n  let treeData = formatTreeData([], dataSource, \"id\", \"parentId\")\r\n  formatData(treeData);\r\n  return treeToArray(treeData)\r\n}\r\n\r\n\r\n// List 转 Tree\r\nexport const formatTreeData = (cur = [], arr = [], childNodeIdName=\"nodeId\", parentIdName=\"nodeParentId\") => {\r\n  // 生成根目录\r\n  if (cur.length == 0) {\r\n    // 格式化数据格式\r\n    cur = arr.filter(item => arr.every(itemx => itemx[childNodeIdName] != item[parentIdName]));\r\n  }\r\n  cur.forEach(item => {\r\n    // item.key = item[childNodeIdName];\r\n    let childs = arr.filter(itemx => itemx[parentIdName] == item[childNodeIdName]);\r\n    if (childs.length) {\r\n      let turnChilds = formatTreeData(childs, arr, childNodeIdName, parentIdName);\r\n      if(!isEmpty(turnChilds)){\r\n        item.children = turnChilds;\r\n      }\r\n    }\r\n  });\r\n  return cur;\r\n}\r\n\r\n// 使用try-catch去捕捉错误\r\n// 直接使用JSON.parse去转译，并把转译结果判断一下是不是object类型，如果是的话就返回true,否则就返回false,这样就排除了转化后非object的类型，比如\"123456789\"\r\nexport function isJSON(str) {\r\n  try {\r\n      if (typeof JSON.parse(str) == \"object\") {\r\n          return true;\r\n      } \r\n  } catch(e) {\r\n\r\n  }\r\n  return false;\r\n}\r\n\r\n// 根据某个字段去重\r\nexport function  unique(arr, val) {\r\n  const res = new Map()\r\n  return arr.filter((item) => !res.has(item[val]) && res.set(item[val], 1))\r\n}\r\n\r\n// 系统图标\r\nexport function getSysIconByIdType(list, _id, type) {\r\n  return ((list || []).find((item) => {\r\n    return item.selectionId == _id && item.teamId == eTemplateTeamId && item.propType == type;\r\n  }) || { propValue: \"\" }).propValue\r\n}\r\n\r\n\r\n// 查找指定id的节点\r\nexport function findNodeById(nodeMap, id) {\r\n    return nodeMap[id] || null;\r\n}\r\n\r\n// 搜索并返回包含关键词的所有父节点\r\nexport function findParentNodes(nodeMap, node, parentIdName=\"parentId\") {\r\n    let parents = [];\r\n    let parentId = node[parentIdName];\r\n    while (parentId !== null) {\r\n      let parent = findNodeById(nodeMap, parentId);\r\n      if (parent) {\r\n        parents.push(parent);\r\n        parentId = parent[parentIdName];\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n    return parents;\r\n  }\r\n\r\n\r\nexport function add(a, b) {\r\n  const precision = Math.max(getPrecision(a), getPrecision(b));\r\n  return (a * 10 ** precision + b * 10 ** precision) / 10 ** precision;\r\n}\r\n\r\nexport function subtract(a, b) {\r\n  const precision = Math.max(getPrecision(a), getPrecision(b));\r\n  return (a * 10 ** precision - b * 10 ** precision) / 10 ** precision;\r\n}\r\n\r\nexport function multiply(a, b) {\r\n  const precision = getPrecision(a) + getPrecision(b);\r\n  const num1 = Number(a.toString().replace(\".\", \"\"));\r\n  const num2 = Number(b.toString().replace(\".\", \"\"));\r\n  return num1 * num2 / 10 ** precision;\r\n}\r\n\r\n// TODO: divide算法有问题，需要重构\r\nexport function divide(a, b) {\r\n  const precision = getPrecision(b) - getPrecision(a);\r\n  return a / b * 10 ** precision;\r\n}\r\n\r\nfunction getPrecision(num) {\r\n    const str = num.toString();\r\n    const index = str.indexOf('.');\r\n    return index === -1 ? 0 : str.length - index - 1;\r\n}\r\n\r\n\r\nexport function formatNumber(input) {  \r\n  let num = Number(input);  \r\n  if (!isNaN(num)) {  \r\n    return Math.floor(num);  \r\n  } else {  \r\n    return input;\r\n  }  \r\n}  \r\n\r\n// 有小数则保留俩位小数，没有则不保留\r\nexport function formatNumberFixed(num) {\r\n  let roundedNum = Math.round(num * 100) / 100;\r\n  // 将数字转换为字符串并查找小数点\r\n  let [integerPart, decimalPart] = roundedNum.toString().split('.');\r\n  // 如果没有小数部分或者小数部分是零（包括尾随零），则返回整数部分\r\n  if (!decimalPart || decimalPart === '0') {\r\n      return parseInt(integerPart, 10); // 返回整数\r\n  } else {\r\n      // 否则返回保留两位小数的数字（这里已经是四舍五入后的了）\r\n      return roundedNum; // 或者使用 parseFloat(integerPart + '.' + decimalPart) 但这是多余的\r\n  }\r\n}\r\n\r\nexport function getDateNow () {\r\n  let currentDate = new Date();\r\n\r\n  let month = currentDate.getMonth() + 1; // 月份是从 0 开始计数的，所以要加 1\r\n  let day = currentDate.getDate();\r\n  \r\n  let formattedDate = month + \"月\" + day + \"号\";\r\n  return formattedDate;\r\n}\r\n\r\nexport function findCascaderParentNodes(treeArray, targetValue, parentValues = []) {\r\n  if (!targetValue) {\r\n    return parentValues;\r\n  }\r\n  for (let treeData of treeArray) {\r\n    if (treeData.value === targetValue) {\r\n      return parentValues.concat(treeData.value);\r\n    }\r\n\r\n    if (treeData.children) {\r\n      let result = findCascaderParentNodes(treeData.children, targetValue, parentValues.concat(treeData.value));\r\n      if (result) {\r\n        return result;\r\n      }\r\n    }\r\n  }\r\n\r\n  return parentValues;\r\n}\r\n\r\n// 去除末尾空白行\r\nexport function removeTrailingEmptyLines(textareaValue) {\r\n  if(!textareaValue){\r\n    return textareaValue\r\n  }\r\n  // 首先，去除字符串末尾的所有空白字符（包括空格和制表符，但不包括换行符）\r\n  let trimmedValue = textareaValue.trimEnd();\r\n\r\n  // 然后，使用正则表达式去除末尾的空白行\r\n  // 这个正则表达式匹配一个或多个换行符后跟任意数量的空白字符（包括没有字符的情况），直到字符串的末尾\r\n  let result = trimmedValue.replace(/(\\r?\\n)\\s*$/, '');\r\n\r\n  return result;\r\n}\r\n\r\n\r\nexport function listToTree(data = []) {\r\n  const obj = {};\r\n  data.map((item) => {\r\n    obj[item.id] = item;\r\n    item.children = []\r\n    return item\r\n  });\r\n  const parentList = [];\r\n  data.forEach((item) => {\r\n    const parent = obj[item.parentId];\r\n    if (parent) {\r\n      parent.children = parent.children || [];\r\n      parent.children.push({...item});\r\n    } else {\r\n      parentList.push({...item});\r\n    }\r\n  });\r\n  return parentList;\r\n}\r\n\r\n// 即3306008 % power(10,5) = 6008 / 1000 = 6.008 取整 6 \r\n// 将传入num除以10的5次方取余，再除以1000取商，增加必要的校验\r\nexport function getConsoleUiControlId(num) {\r\n  if(_.isNaN(num)){\r\n    return num;\r\n  }\r\n  return Math.floor(_.divide(_.multiply(num, 1) % 100000, 1000));\r\n}\r\n\r\n// 即3306008 % power(10,5) = 6008 % 1000 = 8\r\nexport function getConsoleUiControlEnd(num) {\r\n  if(_.isNaN(num)){\r\n    return num;\r\n  }\r\n  return _.multiply(num, 1) % 100000 % 1000;\r\n}\r\n\r\n// 将传入nodeTypeId*100000 + uiTypeId*1000 + length(默认值为1) 兼容字符串和数字\r\nexport function getConsoleUiControlIdByNodeType(nodeTypeId, uiTypeId, length = 1) {\r\n  if(_.isNaN(nodeTypeId)){\r\n    return nodeTypeId;\r\n  }\r\n  return _.multiply(nodeTypeId, 100000) + _.multiply(uiTypeId, 1000) + length;\r\n}\r\n\r\n// 3314001 -> 14001\r\nexport function getIdByInheritId(num) {\r\n  return num?.toString()?.slice(-5);\r\n}\r\n\r\n// 数值类型\r\nexport const isNumber = (value) => {\r\n  return typeof value === 'number' && !isNaN(value);\r\n};\r\n"], "mappings": ";AAAA,SAAQA,gBAAgB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAO,oBAAoB;AAChH,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAC3B,SAASC,MAAM,QAAQ,MAAM;AAC7B,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,CAAC,MAAM,QAAQ;;AAEtB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,aAAa,GAAG,CAAC,GAAG;;AAEjC;AACA,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAIC,GAAG,GAAG,KAAK;EACf,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,gBAAgB,EAAE;IAC9DC,GAAG,GAAGD,KAAK,CAACM,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK;EACzC,CAAC,MAAM,IAAIJ,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,iBAAiB,EAAE;IACtEC,GAAG,GAAGC,MAAM,CAACK,IAAI,CAACP,KAAK,CAAC,CAACM,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK;EACtD,CAAC,MAAM,IAAIJ,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,iBAAiB,EAAE;IACtEC,GAAG,GAAGD,KAAK,CAACQ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACF,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK;EAC3D,CAAC,MAAM,IAAIJ,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,iBAAiB,EAAE;IACtEC,GAAG,GAAIQ,KAAK,CAACT,KAAK,CAAC,IAAIA,KAAK,IAAIF,aAAa,GAAI,IAAI,GAAG,KAAK,CAAC,CAAC;EACjE,CAAC,MAAM,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,eAAe,EAAE;IACpEC,GAAG,GAAG,IAAI;EACZ,CAAC,MAAM,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,oBAAoB,EAAE;IACzEC,GAAG,GAAG,IAAI;EACZ,CAAC,MAAM,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,kBAAkB,EAAE;IACvEC,GAAG,GAAGD,KAAK,GAAG,KAAK,GAAG,IAAI;EAC5B,CAAC,MAAM,IAAIE,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,cAAc,EAAE;IACnE;IACAC,GAAG,GAAGD,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACU,IAAI,KAAK,CAAC;EAC1C;EACA,OAAOT,GAAG;AACZ;;AAEA;AACA,OAAO,SAASU,SAASA,CAAEX,KAAK,EAAE;EAChC,OAAOA,KAAK,KAAK,CAAC,GAAG,IAAID,OAAO,CAACC,KAAK,CAAC;AACzC;;AAEA;AACA,OAAO,SAASY,OAAOA,CAACC,GAAG,EAAE;EAC3B,OAAOX,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACQ,GAAG,CAAC,KAAK,gBAAgB;AACjE;;AAEA;AACA,OAAO,SAASC,QAAQA,CAACD,GAAG,EAAE;EAC5B,OAAOX,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACQ,GAAG,CAAC,KAAK,iBAAiB;AAClE;;AAEA;AACA,OAAO,MAAME,YAAY,GAAGA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,KAAK;EACtD,IAAInB,OAAO,CAACiB,QAAQ,CAAC,IAAIjB,OAAO,CAACkB,OAAO,CAAC,EAAE;IACzC,OAAO,EAAE;EACX;EACA,IAAIE,GAAG,GAAGF,OAAO,CAACG,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACH,GAAG,CAAC,CAAC;EAC1C,IAAII,OAAO,GAAGN,QAAQ,CAACO,MAAM,CAAEC,IAAI,IAAK;IACtC,OAAO,IAAIC,GAAG,CAACN,GAAG,CAAC,CAACO,GAAG,CAACF,IAAI,CAACN,GAAG,CAAC,CAAC;EACpC,CAAC,CAAC;EACF,OAAOI,OAAO;AAChB,CAAC;;AAED;AACA,OAAO,MAAMK,aAAa,GAAGA,CAACX,QAAQ,EAAEC,OAAO,EAAEC,GAAG,KAAK;EACvD,IAAII,OAAO,GAAGN,QAAQ,CAACO,MAAM,CAAC,UAAUK,KAAK,EAAE;IAC7C,OAAOX,OAAO,CAACY,KAAK,CAAC,UAAUC,KAAK,EAAE;MACpC,OAAOA,KAAK,CAACZ,GAAG,CAAC,KAAKU,KAAK,CAACV,GAAG,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOI,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,oBAAoBA,CAACC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACpD,OAAO,CACL,CAACF,IAAI,IAAI,EAAE,EAAEG,IAAI,CAAEd,IAAI,IAAK;IAC1B,OAAOA,IAAI,CAACe,WAAW,IAAIH,GAAG,IAAIZ,IAAI,CAACgB,QAAQ,IAAIH,IAAI;EACzD,CAAC,CAAC,IAAI;IAAEI,SAAS,EAAE;EAAG,CAAC,EACvBA,SAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACP,IAAI,EAAEQ,IAAI,EAAE;EACjD,OAAO,CAACR,IAAI,IAAI,EAAE,EAAET,MAAM,CAAEF,IAAI,IAAK;IACnC,OAAOA,IAAI,CAACe,WAAW,IAAII,IAAI;EACjC,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACT,IAAI,EAAEC,GAAG,EAAE;EACzC,OAAO,CACL,CAACD,IAAI,IAAI,EAAE,EAAEG,IAAI,CAAEd,IAAI,IAAK;IAC1B,OAAOA,IAAI,CAACqB,MAAM,IAAIT,GAAG;EAC3B,CAAC,CAAC,IAAI;IAAEU,QAAQ,EAAE;EAAG,CAAC,EACtBA,QAAQ;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAGA,CAACC,QAAQ,GAAG,EAAE,KAAK;EAC5C,OAAO,CAACA,QAAQ,IAAI,EAAE,EAAEzB,GAAG,CAAC,CAACC,IAAI,EAAEyB,KAAK,KAAK;IAC3C,IAAIzB,IAAI,CAACqB,MAAM,EAAE;MACfrB,IAAI,CAACH,GAAG,GAAGG,IAAI,CAACqB,MAAM,CAACtC,QAAQ,CAAC,CAAC;MACjCiB,IAAI,CAACgB,QAAQ,GAAGhB,IAAI,CAACqB,MAAM,CAACtC,QAAQ,CAAC,CAAC;MACtC;IACF;IACAiB,IAAI,CAACiB,SAAS,GAAGjB,IAAI,CAACsB,QAAQ;IAE9B,OAAOtB,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;AAGD,SAAS0B,YAAYA,CAAC1B,IAAI,EAAC;EACzB,oBAAOxB,OAAA,CAACL,MAAM;IAACkB,IAAI,EAAE,EAAG;IAACsC,GAAG,EAAE3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,MAAO;IAACC,IAAI,eAAErD,OAAA,CAACJ,YAAY;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACV,QAAQ,GAAG,EAAE,KAAK;EAClD,OAAO,CAACA,QAAQ,IAAI,EAAE,EAAEzB,GAAG,CAAC,CAACC,IAAI,EAAEyB,KAAK,KAAK;IAC3C,IAAIzB,IAAI,CAACqB,MAAM,EAAE;MACfrB,IAAI,CAAC6B,IAAI,GAAGH,YAAY,CAAC1B,IAAI,CAAC;MAC9BA,IAAI,CAACrB,KAAK,GAAGqB,IAAI,CAACqB,MAAM,CAACtC,QAAQ,CAAC,CAAC;MACnCiB,IAAI,CAACH,GAAG,GAAGG,IAAI,CAACqB,MAAM,CAACtC,QAAQ,CAAC,CAAC;IACnC;IACAiB,IAAI,CAACmC,KAAK,GAAGnC,IAAI,CAACsB,QAAQ;IAC1B,OAAOtB,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoC,cAAc,GAAGA,CAACC,IAAI,GAAG,EAAE,EAAEC,WAAW,GAAG,KAAK,EAAEvB,WAAW,GAAGlD,gBAAgB,CAAC0E,0BAA0B,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ,GAAE,IAAI,KAAK;EACzK,IAAIC,aAAa,GAAGN,IAAI,CAACnC,MAAM,CAAEF,IAAI,IAAMA,IAAI,CAACe,WAAW,IAAIA,WAAW,KAAKuB,WAAW,IAAItC,IAAI,CAACgB,QAAQ,IAAI,IAAI,CAAI,CAAC;EACxH,IAAGwB,SAAS,EAAC;IACX,IAAGE,QAAQ,EAAC;MACVC,aAAa,GAAG,CAAC,GAAGH,SAAS,EAAE,GAAGG,aAAa,CAAC,CAAC,CAAC;IACpD,CAAC,MAAM;MACLA,aAAa,GAAG,CAAC,GAAGA,aAAa,EAAE,GAAGH,SAAS,CAAC,CAAC,CAAC;IACpD;EACF;EACA,IAAG,CAAC,CAACC,iBAAiB,EAAC;IACrBE,aAAa,GAAGA,aAAa,CAACzC,MAAM,CAAC0C,SAAS,IAAIH,iBAAiB,CAACI,IAAI,CAACC,aAAa,IAAIA,aAAa,IAAIF,SAAS,CAAC5B,QAAQ,CAAC,CAAC;EACjI;EACA2B,aAAa,GAAG,CAACA,aAAa,IAAI,EAAE,EAAE5C,GAAG,CAAEC,IAAI,KAAM;IACnDH,GAAG,EAAEG,IAAI,CAACgB,QAAQ,CAACjC,QAAQ,CAAC,CAAC;IAC7BJ,KAAK,EAAEqB,IAAI,CAACgB,QAAQ,CAACjC,QAAQ,CAAC,CAAC;IAC/BoD,KAAK,EAAEnC,IAAI,CAACiB,SAAS,CAAClC,QAAQ,CAAC;EACjC,CAAC,CAAC,CAAC;EACH;EACA,OAAO4D,aAAa;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,aAAa,GAAGA,CAACC,SAAS,GAAG,EAAE,EAAEC,MAAM,KAAK;EACvD,IAAIC,UAAU,GAAGF,SAAS,CAAC9C,MAAM,CAAEF,IAAI,IAAKA,IAAI,CAACmD,EAAE,IAAIF,MAAM,CAAC;EAC9D,OAAO,CAACC,UAAU,IAAI,EAAE,EAAEnD,GAAG,CAAEC,IAAI;IAAA,IAAAoD,QAAA,EAAAC,SAAA,EAAAC,UAAA;IAAA,OAAM;MACvCzD,GAAG,GAAAuD,QAAA,GAAEpD,IAAI,CAACmD,EAAE,cAAAC,QAAA,uBAAPA,QAAA,CAASrE,QAAQ,CAAC,CAAC;MACxBJ,KAAK,GAAA0E,SAAA,GAAErD,IAAI,CAACmD,EAAE,cAAAE,SAAA,uBAAPA,SAAA,CAAStE,QAAQ,CAAC,CAAC;MAC1BoD,KAAK,GAAAmB,UAAA,GAAEtD,IAAI,CAACuD,IAAI,cAAAD,UAAA,uBAATA,UAAA,CAAWvE,QAAQ,CAAC;IAC7B,CAAC;EAAA,CAAC,CAAC;AACL,CAAC;;AAED;AACA,OAAO,MAAMyE,UAAU,GAAGA,CAAChE,GAAG,EAAEiE,MAAM,KAAK;EACzC,OAAOvF,CAAC,CAACwF,OAAO,CAAClE,GAAG,EAAEiE,MAAM,CAAC;AAC/B,CAAC;;AAED;AACA,OAAO,MAAME,UAAU,GAAGA,CAAC7D,GAAG,EAAE8D,MAAM,KAAK;EACzC,OAAO1F,CAAC,CAACwF,OAAO,CAAC5D,GAAG,EAAE8D,MAAM,CAAC;AAC/B,CAAC;;AAED;AACA,OAAO,SAASC,iBAAiBA,CAACC,CAAC,GAAC,CAAC,CAAC,EAAEC,CAAC,GAAC,CAAC,CAAC,EAAE;EAC5C;EACA,MAAMC,SAAS,GAAGnF,MAAM,CAACK,IAAI,CAAC4E,CAAC,CAAC;EAChC;EACA,MAAMG,WAAW,GAAGpF,MAAM,CAACqF,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGF,SAAS,CAACjE,GAAG,CAACF,GAAG,KAAK;IAAE,CAACA,GAAG,GAAGkE,CAAC,CAAClE,GAAG;EAAE,CAAC,CAAC,CAAC,CAAC;EACnF,OAAOoE,WAAW;AACpB;;AAEA;AACA,OAAO,MAAME,iBAAiB,GAAI9B,IAAI,IAAK;EACzC,OAAOA,IAAI,CAACtC,GAAG,CAAEC,IAAI,KAAM;IACzBH,GAAG,EAAEG,IAAI,CAACsB,QAAQ;IAClB3C,KAAK,EAAEqB,IAAI,CAACsB,QAAQ;IACpB6B,EAAE,EAAEnD,IAAI,CAACqB;EACX,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,OAAO,SAAS+C,UAAUA,CAACC,GAAG,EAAE;EAC9B,IAAIC,CAAC,GAAG;IACN,IAAI,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;IAAE;IAC3B,IAAI,EAAE,IAAI,CAACC,OAAO,CAAC,CAAC;IAAE;IACtB,IAAI,EAAE,IAAI,CAACA,OAAO,CAAC,CAAC;IAAE;IACtB,IAAI,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;IAAE;IACvB,IAAI,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;IAAE;IACzB,IAAI,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;IAAE;IACzB,IAAI,EAAEC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAI,CAACN,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAAE;IAC7CO,CAAC,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC,CAAE;EAC7B,CAAC;EACD,IAAI,SAAS,CAACC,IAAI,CAACX,GAAG,CAAC,EAAEA,GAAG,GAAGA,GAAG,CAAClF,OAAO,CAAC8F,MAAM,CAACC,EAAE,EAAE,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAEC,MAAM,CAAC,CAAC,GAAGH,MAAM,CAACC,EAAE,CAACjG,MAAM,CAAC,CAAC;EAC7G,KAAK,IAAIoG,CAAC,IAAIf,CAAC,EAAE,IAAI,IAAIW,MAAM,CAAC,GAAG,GAAGI,CAAC,GAAG,GAAG,CAAC,CAACL,IAAI,CAACX,GAAG,CAAC,EAAEA,GAAG,GAAGA,GAAG,CAAClF,OAAO,CAAC8F,MAAM,CAACC,EAAE,EAAED,MAAM,CAACC,EAAE,CAACjG,MAAM,IAAI,CAAC,GAAGqF,CAAC,CAACe,CAAC,CAAC,GAAG,CAAC,IAAI,GAAGf,CAAC,CAACe,CAAC,CAAC,EAAED,MAAM,CAAC,CAAC,EAAE,GAAGd,CAAC,CAACe,CAAC,CAAC,EAAEpG,MAAM,CAAC,CAAC;EAC/J,OAAOoF,GAAG;AACZ;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,UAAUA,CAAC3G,KAAK,EAAE4G,EAAE,EAAE;EACpC;EACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;EACnD;EACAF,QAAQ,CAACG,QAAQ,GAAG,UAAU;EAC9BH,QAAQ,CAACI,KAAK,CAACC,QAAQ,GAAG,UAAU;EACpCL,QAAQ,CAACI,KAAK,CAACE,IAAI,GAAG,SAAS;EAC/B;EACAN,QAAQ,CAAC7G,KAAK,GAAGA,KAAK;EACtB;EACA8G,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,QAAQ,CAAC;EACnC;EACAA,QAAQ,CAACS,MAAM,CAAC,CAAC;EACjBT,QAAQ,CAACU,iBAAiB,CAAC,CAAC,EAAEV,QAAQ,CAAC7G,KAAK,CAACM,MAAM,CAAC;EACpDwG,QAAQ,CAACU,WAAW,CAAC,MAAM,CAAC;EAC5BV,QAAQ,CAACM,IAAI,CAACK,WAAW,CAACZ,QAAQ,CAAC;EACnC,IAAID,EAAE,IAAI1G,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACuG,EAAE,CAAC,KAAK,mBAAmB,EAAE;IACpEA,EAAE,CAAC,CAAC;EACN;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEC,OAAO,SAASc,yBAAyBA,CAACC,QAAQ,GAAG,EAAE,EAAEzG,GAAG,EAAE0G,UAAU,GAAG,KAAK,EAAE;EACjF,KAAK,IAAIC,CAAC,IAAIF,QAAQ,EAAE;IACtB,IAAIA,QAAQ,CAACE,CAAC,CAAC,CAAC3G,GAAG,IAAIA,GAAG,EAAE;MAC1B,OAAO0G,UAAU,GAAG,CAACD,QAAQ,CAACE,CAAC,CAAC,CAAC3G,GAAG,CAAC,GAAG,EAAE,CAAC,CAAE;IAC/C;IAEA,IAAIyG,QAAQ,CAACE,CAAC,CAAC,CAACC,QAAQ,EAAE;MACxB,IAAIC,IAAI,GAAGL,yBAAyB,CAACC,QAAQ,CAACE,CAAC,CAAC,CAACC,QAAQ,EAAE5G,GAAG,EAAE0G,UAAU,CAAC;MAC3E,IAAIG,IAAI,KAAKC,SAAS,EAAE;QACtB,OAAOD,IAAI,CAACE,MAAM,CAACN,QAAQ,CAACE,CAAC,CAAC,CAAC3G,GAAG,CAAC,CAAC,CAAE;MACxC;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEC,OAAO,SAASgH,sBAAsBA,CAAClG,IAAI,EAAEd,GAAG,EAAE;EACjD,KAAK,IAAI2G,CAAC,IAAI7F,IAAI,EAAE;IAClB,IAAIA,IAAI,CAAC6F,CAAC,CAAC,CAAC3G,GAAG,IAAIA,GAAG,EAAE;MACtB;MACA,OAAO,EAAE;IACX;IAEA,IAAIc,IAAI,CAAC6F,CAAC,CAAC,CAACC,QAAQ,EAAE;MACpB,IAAIC,IAAI,GAAGG,sBAAsB,CAAClG,IAAI,CAAC6F,CAAC,CAAC,CAACC,QAAQ,EAAE5G,GAAG,CAAC;MACxD,IAAI6G,IAAI,KAAKC,SAAS,EAAE;QACtB;QACA,OAAOD,IAAI,CAACE,MAAM,CAAC;UACjB/G,GAAG,EAAEc,IAAI,CAAC6F,CAAC,CAAC,CAAC3G,GAAG;UAChBiH,QAAQ,EAAEnG,IAAI,CAAC6F,CAAC,CAAC,CAACM;QACpB,CAAC,CAAC;MACJ;IACF;EACF;AACF;;AAEA;AACA,OAAO,SAASC,MAAMA,CAACC,GAAG,EAAC;EACxB,OAAO3I,MAAM,CAAC2I,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC;AAC/B;;AAEA;AACA,OAAO,MAAMC,aAAa,GAAIC,QAAQ,IAAK;EACzC,IAAI;IACF,IAAIC,eAAe,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1D,OAAOD,eAAe;EACxB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,OAAO,IAAI;EACb;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACC,OAAO,SAASC,SAASA,CAACC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACtD,IAAIF,SAAS,IAAI,EAAE,IAAIA,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAIb,SAAS,EAAE;IAChE,OAAO,KAAK;EAChB;EACA,IAAIc,OAAO,IAAI,EAAE,IAAIA,OAAO,IAAI,IAAI,IAAIA,OAAO,IAAId,SAAS,EAAE;IAC1D,OAAO,KAAK;EAChB;EACA,IAAIgB,KAAK,GAAGtJ,MAAM,CAACmJ,SAAS,CAAC,CAACI,OAAO,CAAC,CAAC;EACvC,IAAIC,KAAK,GAAGxJ,MAAM,CAACoJ,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC;EACrC,IAAIE,QAAQ,GAAGD,KAAK,GAAGF,KAAK;EAC5B,IAAII,IAAI,GAAGC,QAAQ,CAACN,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EACpD,IAAII,QAAQ,GAAG,CAAC,EAAE;IACd,OAAO,KAAK;EAChB;EACA,IAAIA,QAAQ,GAAGC,IAAI,EAAE;IACjB,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACb;;AAGA;AACA;AACA;AACA;AACA;AACA;AACC,OAAO,SAASE,eAAeA,CAACT,SAAS,EAAEC,OAAO,EAAE;EACnD,IAAI;IACF,IAAG,CAACD,SAAS,IAAI,CAACC,OAAO,EAAC;MACxB;IACF;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA,IAAIS,KAAK,GAAG7J,MAAM,CAACmJ,SAAS,CAAC,CAACW,MAAM,CAAC,CAAC;;IAEtC;IACA,IAAIC,KAAK,GAAG/J,MAAM,CAACoJ,OAAO,CAAC,CAACU,MAAM,CAAC,CAAC;;IAEpC;IACA,IAAIE,QAAQ,GAAGD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGJ,KAAK,CAACI,OAAO,CAAC,CAAC;;IAEhD;IACA,IAAIC,IAAI,GAAG3D,IAAI,CAACC,KAAK,CAACwD,QAAQ,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;IAEpD;IACA,IAAIG,QAAQ,GAAGH,QAAQ,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;IAC9C,IAAII,KAAK,GAAG7D,IAAI,CAACC,KAAK,CAAC2D,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;;IAEhD;IACA,IAAIE,QAAQ,GAAGF,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;IACzC,IAAIG,OAAO,GAAG/D,IAAI,CAACC,KAAK,CAAC6D,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;;IAEhD;IACA,IAAIE,QAAQ,GAAGF,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IACvC,IAAIG,OAAO,GAAGjE,IAAI,CAACkE,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC;IAEzC,IAAIG,SAAS,GACX,CAAER,IAAI,IAAI,CAAC,GAAI,EAAE,GAAGA,IAAI,GAAG,GAAG,KAC5BE,KAAK,IAAI,CAAC,GAAI,EAAE,GAAGA,KAAK,GAAG,GAAG,CAAC,IAC/BE,OAAO,IAAI,CAAC,GAAI,EAAE,GAAGA,OAAO,GAAG,GAAG,CAAC,IACnCE,OAAO,IAAI,CAAC,GAAK,EAAE,GAAGA,OAAO,GAAG,GAAG,CAAC;IAExC,IAAG,CAACE,SAAS,EAAC;MAAG;MACfA,SAAS,GAAG,IAAI;IAClB;IAEA,OAAOA,SAAS;EAClB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVC,OAAO,CAAC3B,KAAK,CAAC,iBAAiB,EAAE0B,CAAC,CAAC;IACnC,OAAO,CAAC;EACV;AACF;;AAEC;AACD,OAAO,MAAME,eAAe,GAAGA,CAACC,OAAO,EAAEC,KAAK,KAAK;EACjD,IAAG1K,OAAO,CAACyK,OAAO,CAAC,IAAIzK,OAAO,CAAC0K,KAAK,CAAC,EAAC;IACpC,OAAO,KAAK;EACd;EACA,OAAO/K,MAAM,CAAC8K,OAAO,CAAC,CAAChB,MAAM,CAAC,CAAC,CAAC3D,OAAO,CAAC,CAAC,KAAKnG,MAAM,CAAC+K,KAAK,CAAC,CAACjB,MAAM,CAAC,CAAC,CAAC3D,OAAO,CAAC,CAAC;AAChF,CAAC;;AAED;AACA,OAAO,SAAS6E,WAAWA,CAACC,IAAI,GAAC,EAAE,EAAE;EACnC,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC/B,MAAM;MAAEhD,QAAQ;MAAE,GAAGD;IAAE,CAAC,GAAGiD,GAAG;IAC9B,OAAOD,GAAG,CAAC5C,MAAM,CAACJ,CAAC,EAAEC,QAAQ,IAAIA,QAAQ,CAACxH,MAAM,GAAGoK,WAAW,CAAC5C,QAAQ,CAAC,GAAG,EAAE,CAAC;EAChF,CAAC,EAAE,EAAE,CAAC;AACR;;AAEA;AACA,OAAO,SAASiD,WAAWA,CAACrH,IAAI,GAAG,EAAE,EAAE;EACrC,MAAM7C,GAAG,GAAG,CAAC,CAAC;EACd6C,IAAI,CAACsH,OAAO,CAAE3J,IAAI,IAAK;IACrBR,GAAG,CAACQ,IAAI,CAACmD,EAAE,CAAC,GAAG;MAAE,GAAGnD,IAAI;MAAEyG,QAAQ,EAAE;IAAG,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF,MAAMmD,UAAU,GAAG,EAAE;EACrBvH,IAAI,CAACsH,OAAO,CAAE3J,IAAI,IAAK;IACrB,MAAM6J,MAAM,GAAGrK,GAAG,CAACQ,IAAI,CAAC8J,QAAQ,CAAC;IACjC,IAAID,MAAM,EAAE;MACVA,MAAM,CAACpD,QAAQ,CAACsD,IAAI,CAACvK,GAAG,CAACQ,IAAI,CAACmD,EAAE,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,MAAM;MACLyG,UAAU,CAACG,IAAI,CAACvK,GAAG,CAACQ,IAAI,CAACmD,EAAE,CAAC,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,OAAOyG,UAAU;AACnB;;AAEA;AACA,OAAO,SAASI,UAAUA,CAACrJ,IAAI,GAAG,EAAE,EAAC;EACnC,OAAOA,IAAI,CAACZ,GAAG,CAACuJ,IAAI,IAAI;IACtB,IAAIW,KAAK,GAAG;MAAC9H,KAAK,EAAEmH,IAAI,CAAC/F,IAAI;MAAE5E,KAAK,EAAE2K,IAAI,CAACnG;IAAE,CAAC;IAC9C,IAAG,CAACmG,IAAI,CAAC7C,QAAQ,IAAE,EAAE,EAAExH,MAAM,GAAG,CAAC,EAAC;MAChCgL,KAAK,CAACxD,QAAQ,GAAGuD,UAAU,CAACV,IAAI,CAAC7C,QAAQ,CAAC;IAC5C;IACA,OAAOwD,KAAK;EACd,CAAC,CAAC;AACJ;AAGA,OAAO,SAASC,iBAAiBA,CAACC,QAAQ,GAAC,EAAE,EAACtJ,IAAI,EAAC;EACjD,OAAOsJ,QAAQ,CAACrJ,IAAI,CAACsJ,KAAK,IAAIA,KAAK,CAACpJ,QAAQ,CAACjC,QAAQ,CAAC,CAAC,IAAI8B,IAAI,CAAC9B,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACnF;;AAEA;AACA,OAAO,SAASsL,yBAAyBA,CAAEC,WAAW,EAAC;EACrD,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAG,CAAC7L,OAAO,CAAC4L,WAAW,CAAC,EAAC;IACvBA,WAAW,CAACX,OAAO,CAACa,MAAM,IAAI;MAC5BD,MAAM,CAACC,MAAM,CAACC,GAAG,CAAC,GAAGD,MAAM,CAACE,KAAK;IACnC,CAAC,CAAC;EACJ;EACA,OAAOH,MAAM;AACf;;AAEA;AACA,OAAO,SAASI,+BAA+BA,CAAEL,WAAW,EAAC;EAC3D,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAG,CAAC7L,OAAO,CAAC4L,WAAW,CAAC,EAAC;IACvBA,WAAW,CAACX,OAAO,CAACa,MAAM,IAAI;MAAA,IAAAI,aAAA;MAC5BL,MAAM,CAACC,MAAM,CAACC,GAAG,CAAC,IAAAG,aAAA,GAAGJ,MAAM,CAACE,KAAK,cAAAE,aAAA,uBAAZA,aAAA,CAAc7L,QAAQ,CAAC,CAAC;IAC/C,CAAC,CAAC;EACJ;EACA,OAAOwL,MAAM;AACf;;AAGA;AACA;AACA;AACA,OAAO,MAAMM,gBAAgB,GAAGA,CAACxI,IAAI,EAAEyI,MAAM,EAAEC,QAAQ,KAAK;EAC1D,MAAMC,KAAK,GAAG,CAAC,GAAG3I,IAAI,CAAC;EACvB,MAAMZ,KAAK,GAAGuJ,KAAK,CAACC,SAAS,CAAEjL,IAAI,IAAKA,IAAI,CAACH,GAAG,IAAIiL,MAAM,CAACjL,GAAG,CAAC;EAC/D,IAAIkL,QAAQ,IAAIjN,SAAS,CAACoN,OAAO,EAAE;IAAE;IACnC,IAAIzJ,KAAK,IAAI,CAAC,EAAE;MACduJ,KAAK,CAACvJ,KAAK,CAAC,GAAGuJ,KAAK,CAACG,MAAM,CAAC1J,KAAK,GAAG,CAAC,EAAE,CAAC,EAAEuJ,KAAK,CAACvJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,MAAM;MACLuJ,KAAK,CAACjB,IAAI,CAACiB,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC,MAAM,IAAIL,QAAQ,IAAIjN,SAAS,CAACuN,SAAS,EAAE;IAAE;IAC5C,IAAI5J,KAAK,IAAIuJ,KAAK,CAAC/L,MAAM,GAAG,CAAC,EAAE;MAC7B+L,KAAK,CAACvJ,KAAK,CAAC,GAAGuJ,KAAK,CAACG,MAAM,CAAC1J,KAAK,GAAG,CAAC,EAAE,CAAC,EAAEuJ,KAAK,CAACvJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,MAAM;MACLuJ,KAAK,CAACM,OAAO,CAACN,KAAK,CAACG,MAAM,CAAC1J,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C;EACF;EACA,OAAOuJ,KAAK;AACd,CAAC;;AAED;AACA,OAAO,MAAMO,cAAc,GAAGA,CAAC5I,aAAa,GAAG,EAAE,KAAG;EAClD,IAAI6I,cAAc,GAAG7I,aAAa,CAACzC,MAAM,CAACuL,EAAE,IAAIA,EAAE,CAAC1K,WAAW,IAAIlD,gBAAgB,CAAC6N,mBAAmB,IAAID,EAAE,CAACxI,MAAM,IAAIhF,eAAe,CAAC,IAAI,EAAE;EAC7I,OAAOuN,cAAc;AACtB,CAAC;;AAED;AACD,OAAO,MAAMG,eAAe,GAAGA,CAAChJ,aAAa,GAAG,EAAE,EAAEM,MAAM,KAAG;EAC3D,IAAIuI,cAAc,GAAG7I,aAAa,CAACzC,MAAM,CAACuL,EAAE,IAAIA,EAAE,CAAC1K,WAAW,IAAIlD,gBAAgB,CAAC6N,mBAAmB,IAAID,EAAE,CAACxI,MAAM,IAAIA,MAAM,CAAC,IAAI,EAAE;EACpI,OAAOuI,cAAc;AACtB,CAAC;;AAEF;AACA,OAAO,MAAMI,aAAa,GAAGA,CAACjL,IAAI,EAAEkL,cAAc,KAAK;EACrD,IAAIC,KAAK,GAAGnL,IAAI,CAAC4I,MAAM,CAAC,CAACC,GAAG,EAAEuC,OAAO,EAAEtK,KAAK,KAAK;IAC/C,IAAG,CAAC,CAACsK,OAAO,CAACC,SAAS,EAAC;MACrB,MAAMC,UAAU,GAAGzC,GAAG,CAACyB,SAAS,CAACiB,IAAI,IAAIA,IAAI,CAAC3I,IAAI,IAAIwI,OAAO,CAACC,SAAS,CAAC;MACxE,IAAIC,UAAU,IAAI,CAAC,CAAC,EAAE;QACpB,MAAME,KAAK,GAAG;UACZhJ,EAAE,EAAE4I,OAAO,CAAC5I,EAAE;UACd2G,QAAQ,EAAEiC,OAAO,CAACjC,QAAQ;UAC1BvG,IAAI,EAAEwI,OAAO,CAACC,SAAS;UACvBI,UAAU,EAAE,EAAE;UACdJ,SAAS,EAAE,EAAE;UACbK,UAAU,EAAER,cAAc,GAAGE,OAAO,CAACM,UAAU,GAAG,EAAE;UAAE;UACtDC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACT1L,IAAI,EAAE9C,cAAc,CAACyO,MAAM;UAC3BC,QAAQ,EAAE,IAAI;UACdhG,QAAQ,EAAG,CAACsF,OAAO;QACrB,CAAC;QACDvC,GAAG,CAACO,IAAI,CAACoC,KAAK,CAAC;MACjB,CAAC,MAAM;QACL3C,GAAG,CAACyC,UAAU,CAAC,CAACxF,QAAQ,CAACsD,IAAI,CAACgC,OAAO,CAAC;MACxC;IACF,CAAC,MAAM;MACL;MACAvC,GAAG,CAACO,IAAI,CAAC;QAAC,GAAGgC,OAAO;QAAElL,IAAI,EAAE9C,cAAc,CAACyO;MAAO,CAAC,CAAC,CAAC,CAAC;IACxD;IACA,OAAOhD,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;EACN,OAAOsC,KAAK;AACd,CAAC;;AAED;AACA,OAAO,MAAMY,eAAe,GAAGA,CAAC1F,GAAG,EAAE2F,GAAG,GAAG3O,eAAe,CAAC4O,KAAK,KAAK;EACnE,IAAI;IAAA,IAAAC,OAAA;IACF,QAAAA,OAAA,GAAOvO,CAAC,CAAC0I,GAAG,CAAC,CAAC8F,IAAI,CAAC,CAAC,cAAAD,OAAA,uBAAbA,OAAA,CAAeE,SAAS,CAAC,CAAC,EAAEJ,GAAG,CAAC;EACzC,CAAC,CAAC,OAAO3D,CAAC,EAAE;IACVC,OAAO,CAAC3B,KAAK,CAAC,iBAAiB,EAAE0B,CAAC,CAAC;IACnC,OAAOhC,GAAG;EACZ;AACF,CAAC;;AAED;AACA,OAAO,MAAMgG,KAAK,GAAIC,YAAY,IAAI;EACpC,OAAOA,YAAY,IAAI,KAAK;AAC9B,CAAC;;AAED;AACA,OAAO,SAASC,QAAQA,CAAE1N,GAAG,EAAE2N,SAAS,EAAC;EACvC,OAAO3N,GAAG,IAAImH,SAAS,IAAInH,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC4N,OAAO,CAACC,WAAW,CAAC,CAAC,IAAI,MAAM,EAAC;IAC1E,IAAI7N,GAAG,IAAI2N,SAAS,EAAC;MACjB,OAAO,IAAI;IACf;IACA3N,GAAG,GAAGA,GAAG,CAAC8N,UAAU;EACxB;EACA,OAAO,KAAK;AACd;;AAEA;AACA,OAAO,SAASC,WAAWA,CAACC,UAAU,EAAC;EACrC,MAAMC,UAAU,GAAGA,CAACpL,IAAI,EAAEqL,YAAY,GAAG,EAAE,KAAK;IAC9CrL,IAAI,CAACsH,OAAO,CAAC,CAAC3J,IAAI,EAAEyB,KAAK,KAAK;MACxB,MAAMkM,MAAM,GAAGD,YAAY,GAAG,GAAGA,YAAY,IAAIjM,KAAK,GAAG,CAAC,EAAE,GAAG,GAAGA,KAAK,GAAG,CAAC,EAAE;MAC7E,IAAG,CAACzB,IAAI,CAAC2N,MAAM,EAAE3N,IAAI,CAAC2N,MAAM,GAAGA,MAAM,CAAC,CAAE;MAC1C,IAAI3N,IAAI,CAACyG,QAAQ,IAAIzG,IAAI,CAACyG,QAAQ,CAACxH,MAAM,GAAG,CAAC,EAAE;QAC3CwO,UAAU,CAACzN,IAAI,CAACyG,QAAQ,EAAEkH,MAAM,CAAC;MACrC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,IAAIC,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEL,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC;EAC/DC,UAAU,CAACG,QAAQ,CAAC;EACpB,OAAOvE,WAAW,CAACuE,QAAQ,CAAC;AAC9B;;AAGA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAACpE,GAAG,GAAG,EAAE,EAAE3J,GAAG,GAAG,EAAE,EAAEgO,eAAe,GAAC,QAAQ,EAAEC,YAAY,GAAC,cAAc,KAAK;EAC3G;EACA,IAAItE,GAAG,CAACxK,MAAM,IAAI,CAAC,EAAE;IACnB;IACAwK,GAAG,GAAG3J,GAAG,CAACI,MAAM,CAACF,IAAI,IAAIF,GAAG,CAACU,KAAK,CAAC4J,KAAK,IAAIA,KAAK,CAAC0D,eAAe,CAAC,IAAI9N,IAAI,CAAC+N,YAAY,CAAC,CAAC,CAAC;EAC5F;EACAtE,GAAG,CAACE,OAAO,CAAC3J,IAAI,IAAI;IAClB;IACA,IAAIgO,MAAM,GAAGlO,GAAG,CAACI,MAAM,CAACkK,KAAK,IAAIA,KAAK,CAAC2D,YAAY,CAAC,IAAI/N,IAAI,CAAC8N,eAAe,CAAC,CAAC;IAC9E,IAAIE,MAAM,CAAC/O,MAAM,EAAE;MACjB,IAAIgP,UAAU,GAAGJ,cAAc,CAACG,MAAM,EAAElO,GAAG,EAAEgO,eAAe,EAAEC,YAAY,CAAC;MAC3E,IAAG,CAACrP,OAAO,CAACuP,UAAU,CAAC,EAAC;QACtBjO,IAAI,CAACyG,QAAQ,GAAGwH,UAAU;MAC5B;IACF;EACF,CAAC,CAAC;EACF,OAAOxE,GAAG;AACZ,CAAC;;AAED;AACA;AACA,OAAO,SAASyE,MAAMA,CAAClH,GAAG,EAAE;EAC1B,IAAI;IACA,IAAI,OAAOmH,IAAI,CAACC,KAAK,CAACpH,GAAG,CAAC,IAAI,QAAQ,EAAE;MACpC,OAAO,IAAI;IACf;EACJ,CAAC,CAAC,OAAMgC,CAAC,EAAE,CAEX;EACA,OAAO,KAAK;AACd;;AAEA;AACA,OAAO,SAAUqF,MAAMA,CAACvO,GAAG,EAAEwO,GAAG,EAAE;EAChC,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,OAAO1O,GAAG,CAACI,MAAM,CAAEF,IAAI,IAAK,CAACuO,GAAG,CAAClO,GAAG,CAACL,IAAI,CAACsO,GAAG,CAAC,CAAC,IAAIC,GAAG,CAACE,GAAG,CAACzO,IAAI,CAACsO,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E;;AAEA;AACA,OAAO,SAASI,kBAAkBA,CAAC/N,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAE;EAClD,OAAO,CAAC,CAACF,IAAI,IAAI,EAAE,EAAEG,IAAI,CAAEd,IAAI,IAAK;IAClC,OAAOA,IAAI,CAACe,WAAW,IAAIH,GAAG,IAAIZ,IAAI,CAACiD,MAAM,IAAIhF,eAAe,IAAI+B,IAAI,CAACgB,QAAQ,IAAIH,IAAI;EAC3F,CAAC,CAAC,IAAI;IAAEI,SAAS,EAAE;EAAG,CAAC,EAAEA,SAAS;AACpC;;AAGA;AACA,OAAO,SAAS0N,YAAYA,CAACC,OAAO,EAAEzL,EAAE,EAAE;EACtC,OAAOyL,OAAO,CAACzL,EAAE,CAAC,IAAI,IAAI;AAC9B;;AAEA;AACA,OAAO,SAAS0L,eAAeA,CAACD,OAAO,EAAElI,IAAI,EAAEqH,YAAY,GAAC,UAAU,EAAE;EACpE,IAAIe,OAAO,GAAG,EAAE;EAChB,IAAIhF,QAAQ,GAAGpD,IAAI,CAACqH,YAAY,CAAC;EACjC,OAAOjE,QAAQ,KAAK,IAAI,EAAE;IACxB,IAAID,MAAM,GAAG8E,YAAY,CAACC,OAAO,EAAE9E,QAAQ,CAAC;IAC5C,IAAID,MAAM,EAAE;MACViF,OAAO,CAAC/E,IAAI,CAACF,MAAM,CAAC;MACpBC,QAAQ,GAAGD,MAAM,CAACkE,YAAY,CAAC;IACjC,CAAC,MAAM;MACL;IACF;EACF;EACA,OAAOe,OAAO;AAChB;AAGF,OAAO,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxB,MAAMC,SAAS,GAAGtK,IAAI,CAACuK,GAAG,CAACC,YAAY,CAACJ,CAAC,CAAC,EAAEI,YAAY,CAACH,CAAC,CAAC,CAAC;EAC5D,OAAO,CAACD,CAAC,GAAG,EAAE,IAAIE,SAAS,GAAGD,CAAC,GAAG,EAAE,IAAIC,SAAS,IAAI,EAAE,IAAIA,SAAS;AACtE;AAEA,OAAO,SAASG,QAAQA,CAACL,CAAC,EAAEC,CAAC,EAAE;EAC7B,MAAMC,SAAS,GAAGtK,IAAI,CAACuK,GAAG,CAACC,YAAY,CAACJ,CAAC,CAAC,EAAEI,YAAY,CAACH,CAAC,CAAC,CAAC;EAC5D,OAAO,CAACD,CAAC,GAAG,EAAE,IAAIE,SAAS,GAAGD,CAAC,GAAG,EAAE,IAAIC,SAAS,IAAI,EAAE,IAAIA,SAAS;AACtE;AAEA,OAAO,SAASI,QAAQA,CAACN,CAAC,EAAEC,CAAC,EAAE;EAC7B,MAAMC,SAAS,GAAGE,YAAY,CAACJ,CAAC,CAAC,GAAGI,YAAY,CAACH,CAAC,CAAC;EACnD,MAAMM,IAAI,GAAGC,MAAM,CAACR,CAAC,CAACjQ,QAAQ,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EAClD,MAAMsQ,IAAI,GAAGD,MAAM,CAACP,CAAC,CAAClQ,QAAQ,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EAClD,OAAOoQ,IAAI,GAAGE,IAAI,GAAG,EAAE,IAAIP,SAAS;AACtC;;AAEA;AACA,OAAO,SAASQ,MAAMA,CAACV,CAAC,EAAEC,CAAC,EAAE;EAC3B,MAAMC,SAAS,GAAGE,YAAY,CAACH,CAAC,CAAC,GAAGG,YAAY,CAACJ,CAAC,CAAC;EACnD,OAAOA,CAAC,GAAGC,CAAC,GAAG,EAAE,IAAIC,SAAS;AAChC;AAEA,SAASE,YAAYA,CAACO,GAAG,EAAE;EACvB,MAAM3I,GAAG,GAAG2I,GAAG,CAAC5Q,QAAQ,CAAC,CAAC;EAC1B,MAAM0C,KAAK,GAAGuF,GAAG,CAAC4I,OAAO,CAAC,GAAG,CAAC;EAC9B,OAAOnO,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGuF,GAAG,CAAC/H,MAAM,GAAGwC,KAAK,GAAG,CAAC;AACpD;AAGA,OAAO,SAASoO,YAAYA,CAACC,KAAK,EAAE;EAClC,IAAIH,GAAG,GAAGH,MAAM,CAACM,KAAK,CAAC;EACvB,IAAI,CAAC1Q,KAAK,CAACuQ,GAAG,CAAC,EAAE;IACf,OAAO/K,IAAI,CAACC,KAAK,CAAC8K,GAAG,CAAC;EACxB,CAAC,MAAM;IACL,OAAOG,KAAK;EACd;AACF;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAACJ,GAAG,EAAE;EACrC,IAAIK,UAAU,GAAGpL,IAAI,CAACkE,KAAK,CAAC6G,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;EAC5C;EACA,IAAI,CAACM,WAAW,EAAEC,WAAW,CAAC,GAAGF,UAAU,CAACjR,QAAQ,CAAC,CAAC,CAACsI,KAAK,CAAC,GAAG,CAAC;EACjE;EACA,IAAI,CAAC6I,WAAW,IAAIA,WAAW,KAAK,GAAG,EAAE;IACrC,OAAOlI,QAAQ,CAACiI,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;EACtC,CAAC,MAAM;IACH;IACA,OAAOD,UAAU,CAAC,CAAC;EACvB;AACF;AAEA,OAAO,SAASG,UAAUA,CAAA,EAAI;EAC5B,IAAIC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC;EAE5B,IAAIC,KAAK,GAAGF,WAAW,CAAC7L,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACxC,IAAIgM,GAAG,GAAGH,WAAW,CAAC5L,OAAO,CAAC,CAAC;EAE/B,IAAIgM,aAAa,GAAGF,KAAK,GAAG,GAAG,GAAGC,GAAG,GAAG,GAAG;EAC3C,OAAOC,aAAa;AACtB;AAEA,OAAO,SAASC,uBAAuBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,YAAY,GAAG,EAAE,EAAE;EACjF,IAAI,CAACD,WAAW,EAAE;IAChB,OAAOC,YAAY;EACrB;EACA,KAAK,IAAIhD,QAAQ,IAAI8C,SAAS,EAAE;IAC9B,IAAI9C,QAAQ,CAACjP,KAAK,KAAKgS,WAAW,EAAE;MAClC,OAAOC,YAAY,CAAChK,MAAM,CAACgH,QAAQ,CAACjP,KAAK,CAAC;IAC5C;IAEA,IAAIiP,QAAQ,CAACnH,QAAQ,EAAE;MACrB,IAAIoK,MAAM,GAAGJ,uBAAuB,CAAC7C,QAAQ,CAACnH,QAAQ,EAAEkK,WAAW,EAAEC,YAAY,CAAChK,MAAM,CAACgH,QAAQ,CAACjP,KAAK,CAAC,CAAC;MACzG,IAAIkS,MAAM,EAAE;QACV,OAAOA,MAAM;MACf;IACF;EACF;EAEA,OAAOD,YAAY;AACrB;;AAEA;AACA,OAAO,SAASE,wBAAwBA,CAACC,aAAa,EAAE;EACtD,IAAG,CAACA,aAAa,EAAC;IAChB,OAAOA,aAAa;EACtB;EACA;EACA,IAAIC,YAAY,GAAGD,aAAa,CAACE,OAAO,CAAC,CAAC;;EAE1C;EACA;EACA,IAAIJ,MAAM,GAAGG,YAAY,CAAC7R,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAEpD,OAAO0R,MAAM;AACf;AAGA,OAAO,SAASK,UAAUA,CAAC7O,IAAI,GAAG,EAAE,EAAE;EACpC,MAAM7C,GAAG,GAAG,CAAC,CAAC;EACd6C,IAAI,CAACtC,GAAG,CAAEC,IAAI,IAAK;IACjBR,GAAG,CAACQ,IAAI,CAACmD,EAAE,CAAC,GAAGnD,IAAI;IACnBA,IAAI,CAACyG,QAAQ,GAAG,EAAE;IAClB,OAAOzG,IAAI;EACb,CAAC,CAAC;EACF,MAAM4J,UAAU,GAAG,EAAE;EACrBvH,IAAI,CAACsH,OAAO,CAAE3J,IAAI,IAAK;IACrB,MAAM6J,MAAM,GAAGrK,GAAG,CAACQ,IAAI,CAAC8J,QAAQ,CAAC;IACjC,IAAID,MAAM,EAAE;MACVA,MAAM,CAACpD,QAAQ,GAAGoD,MAAM,CAACpD,QAAQ,IAAI,EAAE;MACvCoD,MAAM,CAACpD,QAAQ,CAACsD,IAAI,CAAC;QAAC,GAAG/J;MAAI,CAAC,CAAC;IACjC,CAAC,MAAM;MACL4J,UAAU,CAACG,IAAI,CAAC;QAAC,GAAG/J;MAAI,CAAC,CAAC;IAC5B;EACF,CAAC,CAAC;EACF,OAAO4J,UAAU;AACnB;;AAEA;AACA;AACA,OAAO,SAASuH,qBAAqBA,CAACxB,GAAG,EAAE;EACzC,IAAGzR,CAAC,CAACkB,KAAK,CAACuQ,GAAG,CAAC,EAAC;IACd,OAAOA,GAAG;EACZ;EACA,OAAO/K,IAAI,CAACC,KAAK,CAAC3G,CAAC,CAACwR,MAAM,CAACxR,CAAC,CAACoR,QAAQ,CAACK,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC;AAChE;;AAEA;AACA,OAAO,SAASyB,sBAAsBA,CAACzB,GAAG,EAAE;EAC1C,IAAGzR,CAAC,CAACkB,KAAK,CAACuQ,GAAG,CAAC,EAAC;IACd,OAAOA,GAAG;EACZ;EACA,OAAOzR,CAAC,CAACoR,QAAQ,CAACK,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI;AAC3C;;AAEA;AACA,OAAO,SAAS0B,+BAA+BA,CAACC,UAAU,EAAEC,QAAQ,EAAEtS,MAAM,GAAG,CAAC,EAAE;EAChF,IAAGf,CAAC,CAACkB,KAAK,CAACkS,UAAU,CAAC,EAAC;IACrB,OAAOA,UAAU;EACnB;EACA,OAAOpT,CAAC,CAACoR,QAAQ,CAACgC,UAAU,EAAE,MAAM,CAAC,GAAGpT,CAAC,CAACoR,QAAQ,CAACiC,QAAQ,EAAE,IAAI,CAAC,GAAGtS,MAAM;AAC7E;;AAEA;AACA,OAAO,SAASuS,gBAAgBA,CAAC7B,GAAG,EAAE;EAAA,IAAA8B,aAAA;EACpC,OAAO9B,GAAG,aAAHA,GAAG,wBAAA8B,aAAA,GAAH9B,GAAG,CAAE5Q,QAAQ,CAAC,CAAC,cAAA0S,aAAA,uBAAfA,aAAA,CAAiBC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnC;;AAEA;AACA,OAAO,MAAMC,QAAQ,GAAIhT,KAAK,IAAK;EACjC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACS,KAAK,CAACT,KAAK,CAAC;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}