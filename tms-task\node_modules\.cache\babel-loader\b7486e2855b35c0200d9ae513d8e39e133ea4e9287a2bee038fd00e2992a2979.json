{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\issueTrack\\\\views\\\\IssueHome\\\\CreateIssueContent.jsx\",\n  _s = $RefreshSig$();\nimport { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';\nimport TEditor from \"@common/components/TEditor/TEditor\";\nimport { useDebounce } from \"@common/hook/index\";\nimport { useQuerySetting407_getCodeValueList, useQueryTeam571_GetSpaceVaildUserList } from \"@common/service/commonHooks\";\nimport { compareObj, getAttrPropByType, getPropValueByIdType } from \"@common/utils/ArrayUtils\";\nimport { eConsoleNodeId, eConsoleNodeType, eConsolePropId, eConsoleUiControl, eDebounceTime, eEnableFlg, eFileObjId, eObjRelType } from \"@common/utils/enum\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { eNodeType, eNodeTypeId } from \"@common/utils/TsbConfig\";\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\nimport DefaultAvatar from \"@components/DefaultAvatar\";\nimport ObjRelDrawer from \"@components/ObjRelDrawer\";\nimport UploadLoading from \"@components/UploadLoading\";\nimport { Button, DatePicker, Form, Input, InputNumber, Modal, Select, Table, Upload } from \"antd\";\nimport { nanoid } from \"nanoid\";\nimport { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from \"react\";\nimport { shallowEqual, useSelector } from \"react-redux\";\nimport { unstable_usePrompt, useBeforeUnload, useParams } from \"react-router-dom\";\nimport { useMutationTrack006CreateIssue, useQuerySetting409_getTeamAttrgrpProps } from \"src/issueTrack/service/issueHooks\";\nimport { formatSvg } from \"src/issueTrack/utils/ArrayUtils\";\nimport \"./../IssueDetail/IssueDetail.scss\";\n\n// 新建issue选择人员更改为当前协作群有效人员 Walt 2023-03-27\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction CreateIssueContent({\n  nodeItem,\n  largeView,\n  createAnotherIssueFlg,\n  setOpen,\n  uploadLoading,\n  setUploadLoading,\n  drawerWidth\n}, ref) {\n  _s();\n  var _eNodeType$eNodeTypeI3, _eNodeType$eNodeTypeI4;\n  let editRef = useRef(null);\n  const [fromRef] = Form.useForm();\n  const issueSaveRef = useRef();\n  const {\n    teamId\n  } = useParams();\n  const [formId, setFormId] = useState(nanoid());\n  const [attrList, setAttrList] = useState([]);\n  // 默认初始数据\n  const [objList, setObjList] = useState([]); //附件列表\n  const [objRelList, setObjRelList] = useState([]); //对象关联列表\n  const [objRelModalVisibleFlg, setObjRelModalVisibleFlg] = useState(false); //是否显示对象关联对话框\n  const [objRelModalContentChangedFlg, setObjRelModalContentChangeFlg] = useState(true); //对象关联弹框是否有内容变更\n  const [initialValues, setInitialValues] = useState({});\n  // projectInfo：项目信息，用于显示别名\n  const {\n    nodeId: issueListNodeId,\n    projectInfo,\n    callback\n  } = nodeItem;\n  const {\n    subclassAttrList = [],\n    isLoading: isLoadingGetSubclassAttrs,\n    dataUpdatedAt: dataUpdatedAtSetting409\n  } = useQuerySetting409_getTeamAttrgrpProps(teamId, issueListNodeId, \"\", !!issueListNodeId);\n  const [titleObj, setTitleObj] = useState({\n    name: '',\n    title: ''\n  });\n  const isLoading = isLoadingGetSubclassAttrs;\n  const {\n    data: selectionList\n  } = useQuerySetting407_getCodeValueList(teamId); //字典数据\n  const {\n    data: spaceUserList\n  } = useQueryTeam571_GetSpaceVaildUserList(teamId, issueListNodeId, eEnableFlg.disable, !!issueListNodeId); //字典数据\n\n  const {\n    onIssue012CreateIssue\n  } = useMutationTrack006CreateIssue(teamId, issueListNodeId, 1);\n  const _load_issue012_create_issue_debounce = useDebounce(_load_issue012_create_issue, 500);\n  useImperativeHandle(ref, () => ({\n    onOk: handleSubmitButtonClick,\n    onCancel: handleCloseCreateIssueModal\n  }));\n  const {\n    userInfo\n  } = useSelector(state => ({\n    userInfo: state.getIn([\"login\", \"loginInfo\", \"userInfo\"])\n  }), shallowEqual);\n\n  // 是否过滤不可修改项且没有默认值\n  function isShowModifiable(attr) {\n    var _attr$propertyList$fi, _attr$propertyList$fi2;\n    const modifiable = (_attr$propertyList$fi = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_60_modifiable)) === null || _attr$propertyList$fi === void 0 ? void 0 : _attr$propertyList$fi.propValue;\n    const defaultValue = (_attr$propertyList$fi2 = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_5_default_value)) === null || _attr$propertyList$fi2 === void 0 ? void 0 : _attr$propertyList$fi2.propValue;\n    // 可修改的属性，或者不可修改但有默认值的属性\n    return (modifiable || 1) == 1 || modifiable == \"0\" && defaultValue != '';\n  }\n\n  // 判断是否使用两列布局\n  const shouldUseTwoColumns = drawerWidth >= 800; // 当drawer宽度大于等于800px时使用两列布局\n\n  // 渲染单个字段\n  const renderField = (attr, index) => {\n    var _eNodeType$eNodeTypeI;\n    const isFullWidth = attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title;\n    return /*#__PURE__*/_jsxDEV(Form.Item, {\n      name: attr.nodeId,\n      style: {\n        marginBottom: 15,\n        // 确保label右对齐\n        '.ant-form-item-label': {\n          textAlign: 'right',\n          paddingRight: '8px'\n        }\n      },\n      label: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: attr.nodeName\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 17\n      }, this)],\n      rules: [{\n        required: attr.requiredFlg == 1,\n        message: \"请填写\" + attr.nodeName\n      }]\n      // 使用Form的全局设置\n      ,\n      children: attr.uiControl == eConsoleUiControl.TextBox ? showTextBoxUI(attr, index) : attr.uiControl == eConsoleUiControl.MultiTextBox ? /*#__PURE__*/_jsxDEV(Input.TextArea, {\n        className: \"tms-multi-textbox\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 13\n      }, this) : attr.uiControl == eConsoleUiControl.RichTextBox ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '100%',\n          paddingRight: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(TEditor, {\n          ref: editRef,\n          placeholderText: \"\",\n          contentChanged: e => editContentChange(index, attr, e),\n          uploadParams: {\n            teamId,\n            nodeId: issueListNodeId,\n            moduleName: (_eNodeType$eNodeTypeI = eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]) === null || _eNodeType$eNodeTypeI === void 0 ? void 0 : _eNodeType$eNodeTypeI.nameEn,\n            objType: eNodeTypeId.nt_31704_objtype_issue_item\n          },\n          uploadCallback: uploadFileCallBack\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 15\n      }, this) : attr.uiControl == eConsoleUiControl.ListBox ? attr.selectionLid == \"-701\" ? /*#__PURE__*/_jsxDEV(Select, {\n        allowClear: true,\n        showSearch: true,\n        style: {\n          width: isFullWidth ? '100%' : 300,\n          borderRadius: 3\n        }\n        //select选择框搜索\n        ,\n        disabled: modifyFlg(attr),\n        filterOption: (input, option) => {\n          return (option.children[1].props.children || \"\").toLowerCase().indexOf(input.toLowerCase()) >= 0;\n        },\n        children: (spaceUserList || []).map((_user, index) => {\n          var _user$userId;\n          return /*#__PURE__*/_jsxDEV(Select.Option, {\n            value: (_user$userId = _user.userId) === null || _user$userId === void 0 ? void 0 : _user$userId.toString(),\n            style: _user.deleteFlg === 1 || _user.enableFlg === 0 ? {\n              color: \"#c1c1c1\"\n            } : {},\n            children: [/*#__PURE__*/_jsxDEV(DefaultAvatar, {\n              avatarSrc: _user.avatar\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `member-select-${_user.deleteFlg > 0 ? 'delete' : _user.enableFlg == 0 ? 'enable' : 'normal'}`,\n              style: {\n                marginLeft: 5\n              },\n              children: _user.userName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 23\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 28\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 17\n      }, this) : /*#__PURE__*/_jsxDEV(Select, {\n        disabled: modifyFlg(attr),\n        allowClear: true,\n        showSearch: true,\n        style: {\n          width: isFullWidth ? '100%' : 300,\n          borderRadius: 3\n        },\n        filterOption: (input, option) => {\n          // tmsbug-6469: issue在\"模块\"下拉时，使用键盘进行模糊匹配时崩溃  注意:option结构需保持一致\n          return (option.children.props.children[1].props.children || \"\").toLowerCase().indexOf(input.toLowerCase()) >= 0;\n        },\n        children: (selectionList || []).filter(item => item.selectionId == attr.selectionLid).map((item, index) => {\n          return /*#__PURE__*/_jsxDEV(Select.Option, {\n            value: item.propType,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\"\n              },\n              children: [attr.isSysUser ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  paddingRight: 5\n                },\n                children: /*#__PURE__*/_jsxDEV(DefaultAvatar, {\n                  avatarSrc: item.iconValue\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 79\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 46\n              }, this) : item.iconValue ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  paddingRight: 5\n                },\n                children: formatSvg(item.iconValue)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 47\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 30\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.propValue\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 27\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 25\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 30\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this) :\n      // 时间选择\n      attr.uiControl == eConsoleUiControl.Date ? showDateUI(attr, index) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 生成两列布局，保持字段顺序，富文本框跨越两列宽度\n  const generateTwoColumnLayout = () => {\n    const leftFields = [];\n    const rightFields = [];\n    const result = [];\n    attrList.forEach((attr, index) => {\n      // 标题字段单独占一行，但放在左侧容器中\n      if (attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title) {\n        result.push(/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1 1 calc(50% - 8px)',\n              minWidth: '300px'\n            },\n            children: renderField(attr, index)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1 1 calc(50% - 8px)',\n              minWidth: '300px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, `title-${index}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this));\n      } else if (attr.uiControl === eConsoleUiControl.RichTextBox) {\n        var _eNodeType$eNodeTypeI2;\n        // 富文本框使用与两列布局相同的Form.Item结构，确保label和内容区对齐\n        result.push(/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            width: '100%',\n            marginBottom: 15\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1 1 calc(50% - 8px)',\n              minWidth: '0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: attr.nodeId,\n              style: {\n                marginBottom: 0\n              },\n              label: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: attr.nodeName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 24\n              }, this),\n              rules: [{\n                required: attr.requiredFlg == 1,\n                message: \"请填写\" + attr.nodeName\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: 'calc(200% + 16px)',\n                  paddingRight: '12px'\n                },\n                children: /*#__PURE__*/_jsxDEV(TEditor, {\n                  ref: editRef,\n                  placeholderText: \"\",\n                  contentChanged: e => editContentChange(index, attr, e),\n                  uploadParams: {\n                    teamId,\n                    nodeId: issueListNodeId,\n                    moduleName: (_eNodeType$eNodeTypeI2 = eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]) === null || _eNodeType$eNodeTypeI2 === void 0 ? void 0 : _eNodeType$eNodeTypeI2.nameEn,\n                    objType: eNodeTypeId.nt_31704_objtype_issue_item\n                  },\n                  uploadCallback: uploadFileCallBack\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1 1 calc(50% - 8px)',\n              minWidth: '0'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, `rich-text-${index}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this));\n      } else {\n        // 其他字段按顺序分配到左右两列\n        if (leftFields.length <= rightFields.length) {\n          leftFields.push(renderField(attr, index));\n        } else {\n          rightFields.push(renderField(attr, index));\n        }\n\n        // 当左右两列都有内容时，渲染两列布局\n        if (leftFields.length > 0 && rightFields.length > 0) {\n          result.push(/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: '1 1 calc(50% - 8px)',\n                minWidth: '300px'\n              },\n              children: leftFields.splice(0, leftFields.length)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: '1 1 calc(50% - 8px)',\n                minWidth: '300px'\n              },\n              children: rightFields.splice(0, rightFields.length)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, `two-column-${index}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this));\n        }\n      }\n    });\n\n    // 处理剩余的单个字段\n    if (leftFields.length > 0 || rightFields.length > 0) {\n      result.push(/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: '1 1 calc(50% - 8px)',\n            minWidth: '300px'\n          },\n          children: leftFields\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: '1 1 calc(50% - 8px)',\n            minWidth: '300px'\n          },\n          children: rightFields\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, \"remaining-fields\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this));\n    }\n    return result;\n  };\n  useEffect(() => {\n    if (dataUpdatedAtSetting409) {\n      const attrList = subclassAttrList.filter(attr => {\n        var _attr$propertyList$fi3;\n        return attr.nodeType == eConsoleNodeType.NodeType_1_Attribute &&\n        // 匹配是否启用\n        (((_attr$propertyList$fi3 = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_9_visible)) === null || _attr$propertyList$fi3 === void 0 ? void 0 : _attr$propertyList$fi3.propValue) || 1) == 1 && isShowModifiable(attr);\n      }).map(attr => {\n        var _getAttrPropByType, _attr$propertyList$fi4, _attr$propertyList$fi5, _attr$propertyList$fi6, _attr$propertyList$fi7, _getAttrPropByType2;\n        const selectionLid = ((_getAttrPropByType = getAttrPropByType(attr.propertyList, eConsolePropId.Prop_12_selection)) === null || _getAttrPropByType === void 0 ? void 0 : _getAttrPropByType.propValue) || \"\";\n        return {\n          ...attr,\n          uiControl: (_attr$propertyList$fi4 = attr.propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control)) === null || _attr$propertyList$fi4 === void 0 ? void 0 : _attr$propertyList$fi4.propValue,\n          selectionLid: selectionLid,\n          requiredFlg: ((_attr$propertyList$fi5 = attr.propertyList.find(el => el.propType == eConsolePropId.Prop_239_required)) === null || _attr$propertyList$fi5 === void 0 ? void 0 : _attr$propertyList$fi5.propValue) || 0,\n          // 是否必填\n          itemValue: ((_attr$propertyList$fi6 = attr.propertyList.find(el => el.propType == eConsolePropId.Prop_5_default_value)) === null || _attr$propertyList$fi6 === void 0 ? void 0 : _attr$propertyList$fi6.propValue) || \"\",\n          // 默认值\n          itemValueBak: ((_attr$propertyList$fi7 = attr.propertyList.find(el => el.propType == eConsolePropId.Prop_5_default_value)) === null || _attr$propertyList$fi7 === void 0 ? void 0 : _attr$propertyList$fi7.propValue) || \"\",\n          // 原值,与itemValue相同\n          isSysUser: ((_getAttrPropByType2 = getAttrPropByType(attr.propertyList, eConsolePropId.Prop_27_list_type)) === null || _getAttrPropByType2 === void 0 ? void 0 : _getAttrPropByType2.propValue) == eEnableFlg.enable\n        };\n      }) || [];\n\n      // 初始数据赋值及存储\n      let fields = {};\n      attrList.map((item, index) => {\n        if (item.itemValue) {\n          return fields[item.nodeId] = item.itemValue;\n        }\n        // return fields[item.nodeId] = item.itemValue || null\n      });\n      setInitialValues(fields);\n      fromRef.setFieldsValue(fields);\n      setAttrList(attrList);\n    }\n  }, [dataUpdatedAtSetting409]);\n  useBeforeUnload(useCallback(event => {\n    if (isChange()) event.preventDefault();\n  }, []), {\n    capture: true\n  });\n  unstable_usePrompt({\n    when: useCallback(() => {\n      return !issueSaveRef.current && isChange();\n    }, []),\n    message: \"正在编辑问题，是否确定放弃编辑?\"\n  });\n\n  // 提交表单\n  const handleSubmitButtonClick = async () => {\n    fromRef.submit();\n  };\n  function isChange() {\n    let formData = fromRef.getFieldsValue(true);\n    //比较Form初始值和最终的值是否相等\n    let isNotChange = compareObj(formData, initialValues);\n    return !(isNotChange && objRelModalContentChangedFlg);\n  }\n\n  // 关闭新建弹窗\n  function handleCloseCreateIssueModal() {\n    if (!isChange()) {\n      setOpen(false); //表单没有改动，直接关闭表单\n    } else {\n      var _projectInfo$issueAli;\n      return Modal.confirm({\n        title: '提示',\n        centered: true,\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: \"center\",\n            color: \"#333333\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u6B63\\u5728\\u7F16\\u8F91\", (_projectInfo$issueAli = projectInfo === null || projectInfo === void 0 ? void 0 : projectInfo.issueAlias) !== null && _projectInfo$issueAli !== void 0 ? _projectInfo$issueAli : \"问题\", \"\\uFF0C\\u662F\\u5426\\u786E\\u5B9A\\u653E\\u5F03\\u7F16\\u8F91?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 20\n        }, this),\n        okText: \"确定\",\n        cancelText: \"取消\",\n        onOk: () => {\n          setOpen(false);\n        },\n        onCancel: () => {}\n      });\n    }\n  }\n\n  //对象关联表格columns\n  const columns = [{\n    title: '名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record, index) => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(AppNodeResourceIcon, {\n        nodeType: record.objType,\n        className: \"fontsize-16\",\n        style: {\n          color: \"#3279fe\",\n          opacity: 0.4\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), text]\n    }, void 0, true)\n  }, {\n    title: '类型',\n    dataIndex: 'nodeTypeName',\n    key: 'nodeTypeName'\n  }, {\n    title: '创建人',\n    dataIndex: 'userName',\n    key: 'userName'\n  }, {\n    title: '关联人',\n    dataIndex: 'refName',\n    key: 'refName'\n  }, {\n    title: '创建时间',\n    dataIndex: 'createDt',\n    key: 'createDt'\n  }, {\n    title: '操作',\n    dataIndex: 'operation',\n    key: 'operation',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"a\", {\n      onClick: () => deleteResource(record),\n      children: \"\\u5220\\u9664\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 88\n    }, this)\n  }];\n  function uploadFileCallBack(data) {\n    let link = data.link;\n    let name = link.substring(link.lastIndexOf(\"/\") + 1);\n    let fileObj = {\n      objId: data.id,\n      objType: eObjRelType.objrel_23_attachment,\n      objName: name,\n      seqNo: 1\n    };\n    setObjList([...objList, fileObj]);\n  }\n\n  // 编辑器\n  function editContentChange(index, attr, e) {\n    let nodeId = attr.nodeId;\n    fromRef.setFieldValue(nodeId, editRef.current.getContent());\n  }\n\n  // 文本textBox\n  function showTextBoxUI(attr, index) {\n    const isFullWidth = attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title || attr.uiControl === eConsoleUiControl.RichTextBox;\n    let format = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_13_format);\n    let value = getPropValueByIdType(selectionList, format.selectionLid, format.propValue);\n    switch (value) {\n      case \"数字\":\n        return /*#__PURE__*/_jsxDEV(InputNumber, {\n          type: \"number\",\n          style: {\n            width: isFullWidth ? '100%' : 300\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 16\n        }, this);\n      case \"整数\":\n        return /*#__PURE__*/_jsxDEV(InputNumber, {\n          type: \"number\",\n          precision: 0,\n          style: {\n            width: isFullWidth ? '100%' : 300\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            style: {\n              width: isFullWidth ? '100%' : 500,\n              borderRadius: 3\n            },\n            maxLength: 100,\n            autoComplete: \"off\",\n            onChange: e => setTitleObj({\n              name: attr.nodeId,\n              title: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#999',\n              marginLeft: 5\n            },\n            children: titleObj.name == attr.nodeId && titleObj.title.length == 100 ? '(至多100字)' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 16\n        }, this);\n    }\n  }\n  // 日期 date\n  function showDateUI(attr, index) {\n    let dateItem = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_13_format);\n    let format = dateItem ? getPropValueByIdType(selectionList, dateItem.selectionLid, dateItem.propValue) : \"YYYY-MM-DD\";\n    var time = \"HH:mm:ss\";\n    let showTime = format.indexOf(time) == -1 ? false : true;\n    return /*#__PURE__*/_jsxDEV(DatePicker, {\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u65F6\\u95F4\",\n      format: format,\n      showTime: showTime\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 12\n    }, this);\n  }\n\n  // 是否可修改\n  function modifyFlg(item) {\n    let modifiableProp = item.propertyList.find(el => el.propType == eConsolePropId.Prop_60_modifiable);\n    if ((modifiableProp === null || modifiableProp === void 0 ? void 0 : modifiableProp.propValue) == \"0\") {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  // 删除对象关联\n  function deleteResource(_objRel) {\n    setObjRelList(objRelList.filter(item => item.obj2Id != _objRel.obj2Id));\n  }\n\n  // 对象关联提交至新建issue\n  function submitObjRelForm(newObjRelList) {\n    //关闭弹窗\n    setObjRelModalVisibleFlg(false);\n    newObjRelList.forEach((item, index) => {\n      item.objId = item.obj2Id;\n      item.objType = item.nodeType;\n      item.objName = item.name;\n      item.seqNo = index;\n      item.refName = userInfo.userName;\n    });\n    setObjRelList([...objRelList, ...newObjRelList]);\n    setObjRelModalContentChangeFlg(false);\n  }\n  // 012接口 - 新建issue\n  async function _load_issue012_create_issue() {\n    var _ref;\n    setUploadLoading(true);\n    // TODO: 为何没有返回包含undefined的数据?\n    // tmsbug-6274:issue扩展字段，数据更新，没有从后端返回出应有的数据\n    let formData = fromRef.getFieldsValue(true);\n    let _attrListObj = {};\n    (_ref = (attrList === null || attrList === void 0 ? void 0 : attrList.filter(attr => {\n      var _Object$keys;\n      return !((_Object$keys = Object.keys(formData)) !== null && _Object$keys !== void 0 && _Object$keys.some(nodeId => nodeId == attr.nodeId));\n    })) || []) === null || _ref === void 0 ? void 0 : _ref.map(attr => {\n      _attrListObj[attr.nodeId] = null;\n    });\n    console.log(\"formData\", formData);\n    // 处理日期数据，避免影响数据源，产生报错date.clone is not a function\n    formData = {\n      ...formData\n    };\n    for (let attr in formData) {\n      attrList.filter(el => el.uiControl == eConsoleUiControl.Date).map((item, index) => {\n        if (attr == item.nodeId && formData[attr]) {\n          formData[attr] = formData[attr].format('YYYY-MM-DD');\n        }\n      });\n    }\n    let params = {\n      formId: formId,\n      issuegrpNodeId: issueListNodeId,\n      teamId: teamId,\n      objRelList: [...objRelList, ...objList],\n      ...formData,\n      ..._attrListObj\n    };\n    await new Promise((resolve, reject) => {\n      onIssue012CreateIssue(params, {\n        onSuccess: (data, vars) => {\n          if (data.resultCode == 200) {\n            globalUtil.success(\"提交成功！\");\n            issueSaveRef.current = true;\n            callback === null || callback === void 0 ? void 0 : callback(data.objNodeId); //列表更新\n            // 如果勾选继续新建\n            if (createAnotherIssueFlg) {\n              editRef.current.setContent(null); //清空编辑器数据\n              fromRef.setFieldsValue(initialValues); //恢复默认值\n              setObjList([]); //清空附件列表\n              setObjRelList([]); //清空对象关联列表\n            } else {\n              // fromRef.resetFields(); \n              setOpen(false);\n            }\n          } else {\n            console.log(data, vars);\n          }\n          setFormId(nanoid());\n          resolve();\n        },\n        onError: (err, params) => {}\n      });\n    });\n    setUploadLoading(false);\n  }\n\n  //上传文件\n  function uploadFile(info) {\n    if (info.file.status !== 'uploading') {\n      console.log('uploading', info.file, info.fileList);\n    }\n    if (info.file.status === 'done') {\n      if (info.file.response.resultCode == 200) {\n        let link = info.file.response.link;\n        let name = link.substring(link.lastIndexOf(\"/\") + 1);\n        let fileObj = {\n          objId: info.file.response.id,\n          objType: eObjRelType.objrel_23_attachment,\n          objName: name,\n          seqNo: 1\n        };\n        setObjList([...objList, fileObj]);\n        globalUtil.success(`${info.file.name} 文件上传成功！`);\n      }\n    } else if (info.file.status === 'error') {\n      console.log('error', info.file, info.fileList);\n      globalUtil.error(`${info.file.name} 文件上传失败！`);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(UploadLoading, {\n      spinning: isLoading || uploadLoading,\n      delay: eDebounceTime.fiveHundred,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"issue-detail\",\n        style: {\n          marginTop: \"24px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form, {\n          form: fromRef,\n          labelCol: {\n            span: 3\n          },\n          wrapperCol: {\n            span: 21\n          },\n          labelAlign: \"right\",\n          onFinish: _load_issue012_create_issue_debounce,\n          scrollToFirstError: true // 提交失败自动滚动到第一个错误字段 tmsbug-4081:issue必填项为空，无法提交\n          ,\n          children: [shouldUseTwoColumns ?\n          // 两列布局\n          generateTwoColumnLayout() :\n          // 单列布局\n          attrList.map((attr, index) => renderField(attr, index)), !isLoading && shouldUseTwoColumns && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '16px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: '1 1 calc(50% - 8px)',\n                  minWidth: '300px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"fujian\",\n                  label: \"\\u9644\\u4EF6\",\n                  style: {\n                    marginBottom: 15\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Upload, {\n                    name: 'file',\n                    multiple: true,\n                    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,\n                    data: {\n                      teamId: teamId,\n                      moduleName: (_eNodeType$eNodeTypeI3 = eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]) === null || _eNodeType$eNodeTypeI3 === void 0 ? void 0 : _eNodeType$eNodeTypeI3.nameEn,\n                      nodeId: eFileObjId,\n                      objType: eNodeTypeId.nt_31704_objtype_issue_item\n                    },\n                    onChange: uploadFile,\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 48\n                      }, this),\n                      children: \"\\u4E0A\\u4F20\\u9644\\u4EF6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 22\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 20\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 18\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: '1 1 calc(50% - 8px)',\n                  minWidth: '300px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 16\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '16px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: '1 1 calc(50% - 8px)',\n                  minWidth: '300px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"resource\",\n                  label: \"\\u5BF9\\u8C61\\u5173\\u8054\",\n                  style: {\n                    marginBottom: 0\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 46\n                    }, this),\n                    onClick: e => setObjRelModalVisibleFlg(true),\n                    children: \"\\u5BF9\\u8C61\\u5173\\u8054\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 20\n                  }, this), objRelList.length > 0 && /*#__PURE__*/_jsxDEV(Table, {\n                    className: \"custome-table\",\n                    columns: columns,\n                    dataSource: objRelList,\n                    size: \"small\",\n                    pagination: false\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 22\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 18\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: '1 1 calc(50% - 8px)',\n                  minWidth: '300px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 16\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 14\n            }, this)]\n          }, void 0, true), !isLoading && !shouldUseTwoColumns && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"fujian\",\n              label: \"\\u9644\\u4EF6\",\n              style: {\n                marginBottom: 15\n              },\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                name: 'file',\n                multiple: true,\n                action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,\n                data: {\n                  teamId: teamId,\n                  moduleName: (_eNodeType$eNodeTypeI4 = eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]) === null || _eNodeType$eNodeTypeI4 === void 0 ? void 0 : _eNodeType$eNodeTypeI4.nameEn,\n                  nodeId: eFileObjId,\n                  objType: eNodeTypeId.nt_31704_objtype_issue_item\n                },\n                onChange: uploadFile,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 44\n                  }, this),\n                  children: \"\\u4E0A\\u4F20\\u9644\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 18\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 16\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"resource\",\n              label: \"\\u5BF9\\u8C61\\u5173\\u8054\",\n              style: {\n                marginBottom: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 42\n                }, this),\n                onClick: e => setObjRelModalVisibleFlg(true),\n                children: \"\\u5BF9\\u8C61\\u5173\\u8054\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 16\n              }, this), objRelList.length > 0 && /*#__PURE__*/_jsxDEV(Table, {\n                className: \"custome-table\",\n                columns: columns,\n                dataSource: objRelList,\n                size: \"small\",\n                pagination: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 18\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 14\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ObjRelDrawer, {\n          teamId: teamId,\n          showResource: objRelModalVisibleFlg,\n          objId: \"2\",\n          objType: eNodeTypeId.nt_31704_objtype_issue_item,\n          onOk: submitObjRelForm,\n          onCancle: e => setObjRelModalVisibleFlg(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(CreateIssueContent, \"/XQ1lswACHbRcxQ3JT8aZE+CXJw=\", false, function () {\n  return [Form.useForm, useParams, useQuerySetting409_getTeamAttrgrpProps, useQuerySetting407_getCodeValueList, useQueryTeam571_GetSpaceVaildUserList, useMutationTrack006CreateIssue, useDebounce, useSelector, useBeforeUnload];\n});\n_c = CreateIssueContent;\nexport default _c2 = /*#__PURE__*/forwardRef(CreateIssueContent);\nvar _c, _c2;\n$RefreshReg$(_c, \"CreateIssueContent\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["ExclamationCircleOutlined", "PlusOutlined", "TEditor", "useDebounce", "useQuerySetting407_getCodeValueList", "useQueryTeam571_GetSpaceVaildUserList", "compareObj", "getAttrPropByType", "getPropValueByIdType", "eConsoleNodeId", "eConsoleNodeType", "eConsolePropId", "eConsoleUiControl", "eDebounceTime", "eEnableFlg", "eFileObjId", "eObjRelType", "globalUtil", "eNodeType", "eNodeTypeId", "AppNodeResourceIcon", "DefaultAvatar", "ObjRelDrawer", "UploadLoading", "<PERSON><PERSON>", "DatePicker", "Form", "Input", "InputNumber", "Modal", "Select", "Table", "Upload", "nanoid", "forwardRef", "useCallback", "useEffect", "useImperativeHandle", "useRef", "useState", "shallowEqual", "useSelector", "unstable_usePrompt", "useBeforeUnload", "useParams", "useMutationTrack006CreateIssue", "useQuerySetting409_getTeamAttrgrpProps", "formatSvg", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeItem", "largeView", "createAnotherIssueFlg", "<PERSON><PERSON><PERSON>", "uploadLoading", "setUploadLoading", "drawerWidth", "ref", "_s", "_eNodeType$eNodeTypeI3", "_eNodeType$eNodeTypeI4", "editRef", "fromRef", "useForm", "issueSaveRef", "teamId", "formId", "setFormId", "attrList", "setAttrList", "objList", "setObjList", "objRelList", "setObjRelList", "objRelModalVisibleFlg", "setObjRelModalVisibleFlg", "objRelModalContentChangedFlg", "setObjRelModalContentChangeFlg", "initialValues", "setInitialValues", "nodeId", "issueListNodeId", "projectInfo", "callback", "subclassAttrList", "isLoading", "isLoadingGetSubclassAttrs", "dataUpdatedAt", "dataUpdatedAtSetting409", "titleObj", "setTitleObj", "name", "title", "data", "selectionList", "spaceUserList", "disable", "onIssue012CreateIssue", "_load_issue012_create_issue_debounce", "_load_issue012_create_issue", "onOk", "handleSubmitButtonClick", "onCancel", "handleCloseCreateIssueModal", "userInfo", "state", "getIn", "isShowModifiable", "attr", "_attr$propertyList$fi", "_attr$propertyList$fi2", "modifiable", "propertyList", "find", "prop", "propType", "Prop_60_modifiable", "propValue", "defaultValue", "Prop_5_default_value", "shouldUseTwoColumns", "renderField", "index", "_eNodeType$eNodeTypeI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Nid_11102_Issue_Title", "<PERSON><PERSON>", "style", "marginBottom", "textAlign", "paddingRight", "label", "children", "nodeName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rules", "required", "requiredFlg", "message", "uiControl", "TextBox", "showTextBoxUI", "MultiTextBox", "TextArea", "className", "RichTextBox", "width", "placeholderText", "contentChanged", "e", "editContent<PERSON>hange", "uploadParams", "moduleName", "nt_31704_objtype_issue_item", "nameEn", "objType", "uploadCallback", "uploadFileCallBack", "ListBox", "selectionLid", "allowClear", "showSearch", "borderRadius", "disabled", "modifyFlg", "filterOption", "input", "option", "props", "toLowerCase", "indexOf", "map", "_user", "_user$userId", "Option", "value", "userId", "toString", "deleteFlg", "enableFlg", "color", "avatarSrc", "avatar", "marginLeft", "userName", "filter", "item", "selectionId", "display", "alignItems", "isSysUser", "iconValue", "Date", "showDateUI", "generateTwoColumnLayout", "leftFields", "rightFields", "result", "for<PERSON>ach", "push", "gap", "flex", "min<PERSON><PERSON><PERSON>", "_eNodeType$eNodeTypeI2", "length", "splice", "_attr$propertyList$fi3", "nodeType", "NodeType_1_Attribute", "Prop_9_visible", "_getAttrPropByType", "_attr$propertyList$fi4", "_attr$propertyList$fi5", "_attr$propertyList$fi6", "_attr$propertyList$fi7", "_getAttrPropByType2", "Prop_12_selection", "el", "Prop_14_ui_control", "Prop_239_required", "itemValue", "itemValueBak", "Prop_27_list_type", "enable", "fields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "isChange", "preventDefault", "capture", "when", "current", "submit", "formData", "getFieldsValue", "isNotChange", "_projectInfo$issueAli", "confirm", "centered", "content", "issueAlias", "okText", "cancelText", "columns", "dataIndex", "key", "render", "text", "record", "opacity", "onClick", "deleteResource", "link", "substring", "lastIndexOf", "fileObj", "objId", "id", "objrel_23_attachment", "objName", "seqNo", "setFieldValue", "get<PERSON>ontent", "format", "Prop_13_format", "type", "precision", "max<PERSON><PERSON><PERSON>", "autoComplete", "onChange", "target", "dateItem", "time", "showTime", "placeholder", "modifiableProp", "_objRel", "obj2Id", "submitObjRelForm", "newObjRelList", "refName", "_ref", "_attrListObj", "_Object$keys", "Object", "keys", "some", "console", "log", "params", "issuegrpNodeId", "Promise", "resolve", "reject", "onSuccess", "vars", "resultCode", "success", "objNodeId", "<PERSON><PERSON><PERSON><PERSON>", "onError", "err", "uploadFile", "info", "file", "status", "fileList", "response", "error", "spinning", "delay", "fiveHundred", "marginTop", "form", "labelCol", "span", "wrapperCol", "labelAlign", "onFinish", "scrollToFirstError", "multiple", "action", "process", "env", "REACT_APP_BASE_URL", "icon", "dataSource", "size", "pagination", "showResource", "onCancle", "_c", "_c2", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/issueTrack/views/IssueHome/CreateIssueContent.jsx"], "sourcesContent": ["import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';\r\nimport TEditor from \"@common/components/TEditor/TEditor\";\r\nimport { useDebounce } from \"@common/hook/index\";\r\nimport { useQuerySetting407_getCodeValueList, useQueryTeam571_GetSpaceVaildUserList } from \"@common/service/commonHooks\";\r\nimport { compareObj, getAttrPropByType, getPropValueByIdType } from \"@common/utils/ArrayUtils\";\r\nimport {\r\n  eConsoleNodeId,\r\n  eConsoleNodeType,\r\n  eConsolePropId,\r\n  eConsoleUiControl,\r\n  eDebounceTime,\r\n  eEnableFlg,\r\n  eFileObjId,\r\n  eObjRelType\r\n} from \"@common/utils/enum\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { eNodeType, eNodeTypeId } from \"@common/utils/TsbConfig\";\r\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\r\nimport DefaultAvatar from \"@components/DefaultAvatar\";\r\nimport ObjRelDrawer from \"@components/ObjRelDrawer\";\r\nimport UploadLoading from \"@components/UploadLoading\";\r\nimport { Button, DatePicker, Form, Input, InputNumber, Modal, Select, Table, Upload } from \"antd\";\r\nimport { nanoid } from \"nanoid\";\r\nimport { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from \"react\";\r\nimport { shallowEqual, useSelector } from \"react-redux\";\r\nimport { unstable_usePrompt, useBeforeUnload, useParams } from \"react-router-dom\";\r\nimport { useMutationTrack006CreateIssue, useQuerySetting409_getTeamAttrgrpProps } from \"src/issueTrack/service/issueHooks\";\r\nimport { formatSvg } from \"src/issueTrack/utils/ArrayUtils\";\r\nimport \"./../IssueDetail/IssueDetail.scss\";\r\n\r\n// 新建issue选择人员更改为当前协作群有效人员 Walt 2023-03-27\r\nfunction CreateIssueContent({\r\n  nodeItem,\r\n  largeView,\r\n  createAnotherIssueFlg,\r\n  setOpen,\r\n  uploadLoading,\r\n  setUploadLoading,\r\n  drawerWidth,\r\n}, ref) {\r\n  \r\n  let editRef = useRef(null);\r\n  const [fromRef] = Form.useForm();\r\n  const issueSaveRef = useRef();\r\n  const { teamId } = useParams();\r\n  const [formId, setFormId] = useState(nanoid());\r\n  const [attrList, setAttrList] = useState([]);\r\n  // 默认初始数据\r\n  const [objList, setObjList] = useState([]); //附件列表\r\n  const [objRelList, setObjRelList] = useState([]); //对象关联列表\r\n  const [objRelModalVisibleFlg, setObjRelModalVisibleFlg] = useState(false); //是否显示对象关联对话框\r\n  const [objRelModalContentChangedFlg, setObjRelModalContentChangeFlg] = useState(true); //对象关联弹框是否有内容变更\r\n  const [initialValues, setInitialValues] = useState({})\r\n  // projectInfo：项目信息，用于显示别名\r\n  const { nodeId:issueListNodeId, projectInfo, callback } = nodeItem;\r\n  const { subclassAttrList=[], isLoading: isLoadingGetSubclassAttrs, dataUpdatedAt: dataUpdatedAtSetting409 }\r\n    = useQuerySetting409_getTeamAttrgrpProps(teamId, issueListNodeId, \"\", !!issueListNodeId);\r\n\r\n  const [titleObj, setTitleObj] = useState({name: '', title: ''});\r\n  const isLoading = isLoadingGetSubclassAttrs;\r\n  const { data: selectionList,  } = useQuerySetting407_getCodeValueList(teamId); //字典数据\r\n  const { data: spaceUserList,  } = useQueryTeam571_GetSpaceVaildUserList(teamId, issueListNodeId, eEnableFlg.disable, !!issueListNodeId); //字典数据\r\n\r\n  const { onIssue012CreateIssue } = useMutationTrack006CreateIssue(teamId, issueListNodeId, 1)\r\n  const _load_issue012_create_issue_debounce = useDebounce(_load_issue012_create_issue, 500);\r\n\r\n  useImperativeHandle(ref, () => ({\r\n    onOk: handleSubmitButtonClick,\r\n    onCancel: handleCloseCreateIssueModal,\r\n  }));\r\n\r\n  const {  userInfo,  } = useSelector((state) => ({\r\n    userInfo: state.getIn([\"login\", \"loginInfo\", \"userInfo\"]),\r\n  }), shallowEqual);\r\n\r\n  // 是否过滤不可修改项且没有默认值\r\n  function isShowModifiable(attr) {\r\n    const modifiable = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_60_modifiable)?.propValue;\r\n    const defaultValue = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_5_default_value)?.propValue;\r\n    // 可修改的属性，或者不可修改但有默认值的属性\r\n    return (modifiable || 1) == 1 || (modifiable == \"0\" && defaultValue != '');\r\n  }\r\n\r\n  // 判断是否使用两列布局\r\n  const shouldUseTwoColumns = drawerWidth >= 800; // 当drawer宽度大于等于800px时使用两列布局\r\n\r\n  // 渲染单个字段\r\n  const renderField = (attr, index) => {\r\n    const isFullWidth = attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title;\r\n    \r\n    return (\r\n      <Form.Item name={attr.nodeId}\r\n        style={{\r\n          marginBottom: 15,\r\n          // 确保label右对齐\r\n          '.ant-form-item-label': {\r\n            textAlign: 'right',\r\n            paddingRight: '8px'\r\n          }\r\n        }}\r\n        key={index}\r\n        label={[<span key={index}>{attr.nodeName}</span>]}\r\n        rules={[\r\n          {\r\n            required: attr.requiredFlg == 1,\r\n            message: \"请填写\" + attr.nodeName\r\n          }\r\n        ]}\r\n        // 使用Form的全局设置\r\n      >\r\n              {\r\n          attr.uiControl == eConsoleUiControl.TextBox ?\r\n            showTextBoxUI(attr, index)\r\n            :\r\n            attr.uiControl == eConsoleUiControl.MultiTextBox ?\r\n            <Input.TextArea className=\"tms-multi-textbox\" />:\r\n            attr.uiControl == eConsoleUiControl.RichTextBox ?\r\n              <div style={{ width: '100%', paddingRight: '16px' }}>\r\n                <TEditor ref={editRef}\r\n                  placeholderText=\"\"\r\n                  contentChanged={(e) => editContentChange(index, attr, e)}\r\n                  uploadParams={{\r\n                    teamId,\r\n                    nodeId: issueListNodeId,\r\n                    moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]?.nameEn,\r\n                    objType: eNodeTypeId.nt_31704_objtype_issue_item\r\n                  }}\r\n                  uploadCallback={uploadFileCallBack}/>\r\n              </div> :\r\n            attr.uiControl == eConsoleUiControl.ListBox ?\r\n            attr.selectionLid == \"-701\" ?\r\n                <Select\r\n                  allowClear\r\n                  showSearch\r\n                  style={{ \r\n                    width: isFullWidth ? '100%' : 300, \r\n                    borderRadius: 3 \r\n                  }}\r\n                  //select选择框搜索\r\n                  disabled={modifyFlg(attr)}\r\n                  filterOption={(input, option) => {\r\n                    return (option.children[1].props.children || \"\").toLowerCase().indexOf(input.toLowerCase()) >= 0\r\n                  }}\r\n                >\r\n                  {(spaceUserList || []).map((_user, index) => {\r\n                    return <Select.Option \r\n                      key={index} \r\n                      value={_user.userId?.toString()} \r\n                      style={(_user.deleteFlg === 1 || _user.enableFlg === 0)?{color: \"#c1c1c1\"}:{}}>\r\n                      <DefaultAvatar avatarSrc={_user.avatar} />\r\n                      <span className={`member-select-${_user.deleteFlg > 0 ? 'delete' : _user.enableFlg == 0 ? 'enable' : 'normal'}`}\r\n                            style={{ marginLeft: 5 }}>{_user.userName}</span>\r\n                    </Select.Option>\r\n                  })}\r\n                </Select>\r\n                :\r\n                <Select disabled={modifyFlg(attr)}\r\n                  allowClear\r\n                  showSearch\r\n                  style={{ \r\n                    width: isFullWidth ? '100%' : 300, \r\n                    borderRadius: 3 \r\n                  }}\r\n                  filterOption={(input, option) => {\r\n                      // tmsbug-6469: issue在\"模块\"下拉时，使用键盘进行模糊匹配时崩溃  注意:option结构需保持一致\r\n                      return (option.children.props.children[1].props.children || \"\").toLowerCase().indexOf(input.toLowerCase()) >= 0\r\n                   }}\r\n                >\r\n                  {(selectionList || []).filter(item => item.selectionId == attr.selectionLid).map((item, index) => {\r\n                      return <Select.Option key={index} value={item.propType} >\r\n                        <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                         { attr.isSysUser ?  <div style={{ paddingRight: 5 }}><DefaultAvatar avatarSrc={item.iconValue} /></div> :\r\n                            item.iconValue ?  <div style={{ paddingRight: 5 }}>{formatSvg(item.iconValue)}</div> :\r\n                             <div></div>\r\n                          }\r\n                          <span>{item.propValue}</span>\r\n                        </div>\r\n                      </Select.Option>\r\n                  })}\r\n                </Select> :\r\n              // 时间选择\r\n              attr.uiControl == eConsoleUiControl.Date ?\r\n                showDateUI(attr, index) : <></>\r\n        }\r\n      </Form.Item>\r\n    );\r\n  };\r\n\r\n  // 生成两列布局，保持字段顺序，富文本框跨越两列宽度\r\n  const generateTwoColumnLayout = () => {\r\n    const leftFields = [];\r\n    const rightFields = [];\r\n    const result = [];\r\n    \r\n    attrList.forEach((attr, index) => {\r\n      // 标题字段单独占一行，但放在左侧容器中\r\n      if (attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title) {\r\n        result.push(\r\n          <div key={`title-${index}`} style={{\r\n            display: 'flex',\r\n            gap: '16px'\r\n          }}>\r\n            <div style={{\r\n              flex: '1 1 calc(50% - 8px)',\r\n              minWidth: '300px'\r\n            }}>\r\n              {renderField(attr, index)}\r\n            </div>\r\n            <div style={{\r\n              flex: '1 1 calc(50% - 8px)',\r\n              minWidth: '300px'\r\n            }}>\r\n              {/* 空的右列 */}\r\n            </div>\r\n          </div>\r\n        );\r\n      } else if (attr.uiControl === eConsoleUiControl.RichTextBox) {\r\n        // 富文本框使用与两列布局相同的Form.Item结构，确保label和内容区对齐\r\n        result.push(\r\n          <div key={`rich-text-${index}`} style={{ display: 'flex', width: '100%', marginBottom: 15 }}>\r\n            <div style={{ flex: '1 1 calc(50% - 8px)', minWidth: '0' }}>\r\n              <Form.Item\r\n                name={attr.nodeId}\r\n                style={{ marginBottom: 0 }}\r\n                label={<span>{attr.nodeName}</span>}\r\n                rules={[\r\n                  {\r\n                    required: attr.requiredFlg == 1,\r\n                    message: \"请填写\" + attr.nodeName\r\n                  }\r\n                ]}\r\n              >\r\n                <div style={{ width: 'calc(200% + 16px)', paddingRight: '12px' }}>\r\n                  <TEditor\r\n                    ref={editRef}\r\n                    placeholderText=\"\"\r\n                    contentChanged={(e) => editContentChange(index, attr, e)}\r\n                    uploadParams={{\r\n                      teamId,\r\n                      nodeId: issueListNodeId,\r\n                      moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]?.nameEn,\r\n                      objType: eNodeTypeId.nt_31704_objtype_issue_item\r\n                    }}\r\n                    uploadCallback={uploadFileCallBack}\r\n                  />\r\n                </div>\r\n              </Form.Item>\r\n            </div>\r\n            <div style={{ flex: '1 1 calc(50% - 8px)', minWidth: '0' }}>\r\n              {/* 空的右列，保持布局结构 */}\r\n            </div>\r\n          </div>\r\n        );\r\n      } else {\r\n        // 其他字段按顺序分配到左右两列\r\n        if (leftFields.length <= rightFields.length) {\r\n          leftFields.push(renderField(attr, index));\r\n        } else {\r\n          rightFields.push(renderField(attr, index));\r\n        }\r\n        \r\n        // 当左右两列都有内容时，渲染两列布局\r\n        if (leftFields.length > 0 && rightFields.length > 0) {\r\n          result.push(\r\n            <div key={`two-column-${index}`} style={{\r\n              display: 'flex',\r\n              gap: '16px'\r\n            }}>\r\n              <div style={{\r\n                flex: '1 1 calc(50% - 8px)',\r\n                minWidth: '300px'\r\n              }}>\r\n                {leftFields.splice(0, leftFields.length)}\r\n              </div>\r\n              <div style={{\r\n                flex: '1 1 calc(50% - 8px)',\r\n                minWidth: '300px'\r\n              }}>\r\n                {rightFields.splice(0, rightFields.length)}\r\n              </div>\r\n            </div>\r\n          );\r\n        }\r\n      }\r\n    });\r\n    \r\n    // 处理剩余的单个字段\r\n    if (leftFields.length > 0 || rightFields.length > 0) {\r\n      result.push(\r\n        <div key=\"remaining-fields\" style={{\r\n          display: 'flex',\r\n          gap: '16px'\r\n        }}>\r\n          <div style={{\r\n            flex: '1 1 calc(50% - 8px)',\r\n            minWidth: '300px'\r\n          }}>\r\n            {leftFields}\r\n          </div>\r\n          <div style={{\r\n            flex: '1 1 calc(50% - 8px)',\r\n            minWidth: '300px'\r\n          }}>\r\n            {rightFields}\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    return result;\r\n  };\r\n\r\n  useEffect(()=>{\r\n    if(dataUpdatedAtSetting409){\r\n      const attrList = subclassAttrList.filter(attr => attr.nodeType == eConsoleNodeType.NodeType_1_Attribute &&\r\n        // 匹配是否启用\r\n        (attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_9_visible)?.propValue || 1) == 1 &&\r\n        isShowModifiable(attr))\r\n        .map(attr => {\r\n          const selectionLid = getAttrPropByType(attr.propertyList, eConsolePropId.Prop_12_selection )?.propValue || \"\";\r\n          return {\r\n            ...attr,\r\n            uiControl: (attr.propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue,\r\n            selectionLid: selectionLid,\r\n            requiredFlg: (attr.propertyList.find(el => el.propType == eConsolePropId.Prop_239_required))?.propValue || 0,   // 是否必填\r\n            itemValue: attr.propertyList.find(el => el.propType == eConsolePropId.Prop_5_default_value)?.propValue || \"\",   // 默认值\r\n            itemValueBak: attr.propertyList.find(el => el.propType == eConsolePropId.Prop_5_default_value)?.propValue || \"\", // 原值,与itemValue相同\r\n            isSysUser: getAttrPropByType(attr.propertyList, eConsolePropId.Prop_27_list_type)?.propValue == eEnableFlg.enable\r\n          }\r\n        }) || [];\r\n\r\n        // 初始数据赋值及存储\r\n        let fields = {}\r\n        attrList.map((item, index) => {\r\n          if (item.itemValue) {\r\n            return fields[item.nodeId] = item.itemValue\r\n          }\r\n          // return fields[item.nodeId] = item.itemValue || null\r\n        })\r\n        setInitialValues(fields)\r\n        fromRef.setFieldsValue(fields)\r\n        setAttrList(attrList);\r\n    }\r\n  },[dataUpdatedAtSetting409])\r\n\r\n  useBeforeUnload(useCallback((event) => {\r\n    if(isChange()) event.preventDefault()\r\n  },[]), {capture: true})\r\n\r\n  unstable_usePrompt({\r\n    when: useCallback(() => {\r\n      return !issueSaveRef.current && isChange()\r\n    },[]),\r\n    message: \"正在编辑问题，是否确定放弃编辑?\"\r\n  })\r\n\r\n    // 提交表单\r\n    const handleSubmitButtonClick = async () => {\r\n      fromRef.submit();\r\n    };\r\n  \r\n    function isChange(){\r\n      let formData = fromRef.getFieldsValue(true);\r\n      //比较Form初始值和最终的值是否相等\r\n      let isNotChange = compareObj(formData, initialValues);\r\n      return !(isNotChange && objRelModalContentChangedFlg)\r\n    }\r\n  \r\n    // 关闭新建弹窗\r\n    function handleCloseCreateIssueModal() {\r\n      if (!isChange()) {\r\n        setOpen(false); //表单没有改动，直接关闭表单\r\n      } else {\r\n        return Modal.confirm({\r\n          title: '提示',\r\n          centered: true,\r\n          content: <div style={{ textAlign: \"center\", color: \"#333333\" }}>\r\n          <div>正在编辑{projectInfo?.issueAlias??\"问题\"}，是否确定放弃编辑?</div>\r\n        </div>,\r\n          okText: \"确定\",\r\n          cancelText: \"取消\",\r\n          onOk: () => {\r\n            setOpen(false);\r\n          },\r\n          onCancel: () => {\r\n          },\r\n        })\r\n      }\r\n  }\r\n\r\n  //对象关联表格columns\r\n  const columns = [\r\n    {\r\n      title: '名称', dataIndex: 'name', key: 'name',\r\n      render: (text, record, index) => <>\r\n        <AppNodeResourceIcon nodeType={record.objType} className=\"fontsize-16\" style={{ color: \"#3279fe\", opacity: 0.4 }}/>\r\n        {text}\r\n      </>\r\n    },\r\n    { title: '类型', dataIndex: 'nodeTypeName', key: 'nodeTypeName', },\r\n    { title: '创建人', dataIndex: 'userName', key: 'userName', },\r\n    { title: '关联人', dataIndex: 'refName', key: 'refName', },\r\n    { title: '创建时间', dataIndex: 'createDt', key: 'createDt', },\r\n    { title: '操作', dataIndex: 'operation', key: 'operation', render: (text, record) => <a onClick={() => deleteResource(record)}>删除</a> }\r\n  ];\r\n\r\n\r\n  function uploadFileCallBack(data) {\r\n    let link = data.link\r\n    let name = link.substring(link.lastIndexOf(\"/\") + 1)\r\n    let fileObj = { objId: data.id, objType: eObjRelType.objrel_23_attachment, objName: name, seqNo: 1 }\r\n    setObjList([...objList, fileObj]);\r\n  }\r\n\r\n  // 编辑器\r\n  function editContentChange(index, attr, e) {\r\n    let nodeId = attr.nodeId\r\n    fromRef.setFieldValue(nodeId, editRef.current.getContent())\r\n  }\r\n \r\n  // 文本textBox\r\n  function showTextBoxUI(attr, index) {\r\n    const isFullWidth = attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title || attr.uiControl === eConsoleUiControl.RichTextBox;\r\n    \r\n    let format = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_13_format)\r\n    let value = getPropValueByIdType(selectionList, format.selectionLid, format.propValue)\r\n    switch (value) {\r\n      case \"数字\":\r\n        return <InputNumber type={\"number\"} style={{ width: isFullWidth ? '100%' : 300 }} />\r\n      case \"整数\":\r\n        return <InputNumber type={\"number\"} precision={0} style={{ width: isFullWidth ? '100%' : 300 }} />\r\n      default:\r\n        return <div style={{display:'flex',alignItems:'center'}}>\r\n          <Input style={{ width: isFullWidth ? '100%' : 500, borderRadius: 3 }}\r\n                 maxLength={100} autoComplete=\"off\"\r\n                 onChange={(e)=>setTitleObj({name: attr.nodeId, title: e.target.value})}/>\r\n          <span style={{color:'#999',marginLeft:5}}>\r\n            {titleObj.name == attr.nodeId && titleObj.title.length == 100 ? '(至多100字)' : ''}\r\n          </span>\r\n        </div>\r\n    }\r\n  }\r\n  // 日期 date\r\n  function showDateUI(attr, index) {\r\n    let dateItem = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_13_format)\r\n    let format = dateItem ? getPropValueByIdType(selectionList, dateItem.selectionLid, dateItem.propValue) : \"YYYY-MM-DD\"\r\n    var time = \"HH:mm:ss\"\r\n    let showTime = format.indexOf(time) == -1 ? false : true\r\n    return <DatePicker placeholder=\"请选择时间\" format={format} showTime={showTime} />\r\n  }\r\n\r\n  // 是否可修改\r\n  function modifyFlg(item) {\r\n    let modifiableProp = item.propertyList.find(el => el.propType == eConsolePropId.Prop_60_modifiable)\r\n    if (modifiableProp?.propValue == \"0\") {\r\n      return true\r\n    } else {\r\n      return false\r\n    }\r\n  }\r\n\r\n  // 删除对象关联\r\n  function deleteResource(_objRel) {\r\n    setObjRelList(objRelList.filter(item => item.obj2Id != _objRel.obj2Id));\r\n  }\r\n\r\n  // 对象关联提交至新建issue\r\n  function submitObjRelForm(newObjRelList) {\r\n    //关闭弹窗\r\n    setObjRelModalVisibleFlg(false);\r\n    newObjRelList.forEach((item, index) => {\r\n      item.objId = item.obj2Id\r\n      item.objType = item.nodeType\r\n      item.objName = item.name\r\n      item.seqNo = index\r\n      item.refName = userInfo.userName\r\n    });\r\n    setObjRelList([...objRelList, ...newObjRelList]);\r\n    setObjRelModalContentChangeFlg(false);\r\n  }\r\n  // 012接口 - 新建issue\r\n  async  function _load_issue012_create_issue() {\r\n    setUploadLoading(true);\r\n    // TODO: 为何没有返回包含undefined的数据?\r\n    // tmsbug-6274:issue扩展字段，数据更新，没有从后端返回出应有的数据\r\n    let formData = fromRef.getFieldsValue(true);\r\n    let _attrListObj = {};\r\n    (attrList?.filter(attr=>(!Object.keys(formData)?.some(nodeId=>nodeId == attr.nodeId))) || [])?.map(attr=>{\r\n      _attrListObj[attr.nodeId] = null\r\n    });\r\n    console.log(\"formData\", formData);\r\n    // 处理日期数据，避免影响数据源，产生报错date.clone is not a function\r\n    formData = { ...formData }\r\n    for (let attr in formData) {\r\n      attrList.filter(el => el.uiControl == eConsoleUiControl.Date).map((item, index) => {\r\n        if (attr == item.nodeId && formData[attr]) {\r\n          formData[attr] = formData[attr].format('YYYY-MM-DD')\r\n        }\r\n      })\r\n    }\r\n    let params = { formId: formId, issuegrpNodeId: issueListNodeId, teamId: teamId, objRelList: [...objRelList, ...objList], ...formData, ..._attrListObj }\r\n    await new Promise((resolve, reject)=>  {\r\n      onIssue012CreateIssue(params, {\r\n        onSuccess: (data, vars) => {\r\n          if (data.resultCode == 200) {\r\n            globalUtil.success(\"提交成功！\");\r\n            issueSaveRef.current = true;\r\n            callback?.(data.objNodeId); //列表更新\r\n            // 如果勾选继续新建\r\n            if (createAnotherIssueFlg) {\r\n              editRef.current.setContent(null); //清空编辑器数据\r\n              fromRef.setFieldsValue(initialValues);//恢复默认值\r\n              setObjList([]); //清空附件列表\r\n              setObjRelList([]); //清空对象关联列表\r\n            } else {\r\n              // fromRef.resetFields(); \r\n              setOpen(false);\r\n            }\r\n          } else {\r\n            console.log(data, vars)\r\n          }\r\n          setFormId(nanoid());\r\n          resolve();\r\n        },\r\n        onError: (err, params) => {\r\n        }\r\n      })\r\n    })\r\n    setUploadLoading(false);\r\n  }\r\n\r\n  //上传文件\r\n  function uploadFile(info) {\r\n    if (info.file.status !== 'uploading') {\r\n      console.log('uploading', info.file, info.fileList);\r\n    }\r\n    if (info.file.status === 'done') {\r\n      if (info.file.response.resultCode == 200) {\r\n        let link = info.file.response.link\r\n        let name = link.substring(link.lastIndexOf(\"/\") + 1)\r\n        let fileObj = { objId: info.file.response.id, objType: eObjRelType.objrel_23_attachment, objName: name, seqNo: 1 }\r\n        setObjList([...objList, fileObj]);\r\n        globalUtil.success(`${info.file.name} 文件上传成功！`);\r\n      }\r\n    } else if (info.file.status === 'error') {\r\n      console.log('error', info.file, info.fileList);\r\n      globalUtil.error(`${info.file.name} 文件上传失败！`);\r\n    }\r\n  }\r\n\r\n  return <>\r\n      <UploadLoading spinning={isLoading || uploadLoading} delay={eDebounceTime.fiveHundred} >\r\n        <div className=\"issue-detail\" style={{marginTop:\"24px\"}}>\r\n          <Form\r\n            form={fromRef}\r\n            labelCol={{ span: 3 }}\r\n            wrapperCol={{ span: 21 }}\r\n            labelAlign=\"right\"\r\n            onFinish={_load_issue012_create_issue_debounce}\r\n            scrollToFirstError// 提交失败自动滚动到第一个错误字段 tmsbug-4081:issue必填项为空，无法提交\r\n          >\r\n            {shouldUseTwoColumns ? (\r\n              // 两列布局\r\n              generateTwoColumnLayout()\r\n            ) : (\r\n              // 单列布局\r\n              attrList.map((attr, index) => renderField(attr, index))\r\n            )}\r\n            {\r\n            !isLoading && shouldUseTwoColumns && <>\r\n             <div style={{\r\n               display: 'flex',\r\n               gap: '16px'\r\n             }}>\r\n               <div style={{\r\n                 flex: '1 1 calc(50% - 8px)',\r\n                 minWidth: '300px'\r\n               }}>\r\n                 <Form.Item name=\"fujian\" label=\"附件\" style={{marginBottom:15}}>\r\n                   <Upload name={'file'}\r\n                     multiple={true}\r\n                     action={`${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`}\r\n                     data={{\r\n                       teamId: teamId,\r\n                       moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]?.nameEn,\r\n                       nodeId: eFileObjId,\r\n                       objType: eNodeTypeId.nt_31704_objtype_issue_item\r\n                   }}\r\n                     onChange={uploadFile}\r\n                   >\r\n                     <Button type=\"link\" icon={<PlusOutlined />}>上传附件</Button>\r\n                   </Upload>\r\n                 </Form.Item>\r\n               </div>\r\n               <div style={{\r\n                 flex: '1 1 calc(50% - 8px)',\r\n                 minWidth: '300px'\r\n               }}>\r\n                 {/* 空的右列 */}\r\n               </div>\r\n             </div>\r\n             <div style={{\r\n               display: 'flex',\r\n               gap: '16px'\r\n             }}>\r\n               <div style={{\r\n                 flex: '1 1 calc(50% - 8px)',\r\n                 minWidth: '300px'\r\n               }}>\r\n                 <Form.Item name=\"resource\" label=\"对象关联\" style={{marginBottom:0}}>\r\n                   <Button type=\"link\" icon={<PlusOutlined />}\r\n                     onClick={(e) => setObjRelModalVisibleFlg(true)}>对象关联</Button>\r\n                   {objRelList.length > 0 &&\r\n                     <Table className=\"custome-table\"\r\n                       columns={columns}\r\n                       dataSource={objRelList}\r\n                       size={\"small\"}\r\n                       pagination={false} />\r\n                   }\r\n                 </Form.Item>\r\n               </div>\r\n               <div style={{\r\n                 flex: '1 1 calc(50% - 8px)',\r\n                 minWidth: '300px'\r\n               }}>\r\n                 {/* 空的右列 */}\r\n               </div>\r\n             </div>\r\n            </>\r\n            }\r\n            {\r\n            !isLoading && !shouldUseTwoColumns && <>\r\n             <Form.Item name=\"fujian\" label=\"附件\" style={{marginBottom:15}}>\r\n               <Upload name={'file'}\r\n                 multiple={true}\r\n                 action={`${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`}\r\n                 data={{\r\n                   teamId: teamId,\r\n                   moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]?.nameEn,\r\n                   nodeId: eFileObjId,\r\n                   objType: eNodeTypeId.nt_31704_objtype_issue_item\r\n               }}\r\n                 onChange={uploadFile}\r\n               >\r\n                 <Button type=\"link\" icon={<PlusOutlined />}>上传附件</Button>\r\n               </Upload>\r\n             </Form.Item>\r\n             <Form.Item name=\"resource\" label=\"对象关联\" style={{marginBottom:0}}>\r\n               <Button type=\"link\" icon={<PlusOutlined />}\r\n                 onClick={(e) => setObjRelModalVisibleFlg(true)}>对象关联</Button>\r\n               {objRelList.length > 0 &&\r\n                 <Table className=\"custome-table\"\r\n                   columns={columns}\r\n                   dataSource={objRelList}\r\n                   size={\"small\"}\r\n                   pagination={false} />\r\n               }\r\n             </Form.Item>\r\n            </>\r\n            }\r\n          </Form>\r\n          <ObjRelDrawer\r\n            teamId={teamId}\r\n            showResource={objRelModalVisibleFlg}\r\n            objId={\"2\"}\r\n            objType={eNodeTypeId.nt_31704_objtype_issue_item}\r\n            onOk={submitObjRelForm}\r\n            onCancle={(e) => setObjRelModalVisibleFlg(false)} \r\n          />\r\n        </div>\r\n      </UploadLoading>\r\n  </>;\r\n}\r\n\r\nexport default forwardRef(CreateIssueContent);"], "mappings": ";;AAAA,SAASA,yBAAyB,EAAEC,YAAY,QAAQ,mBAAmB;AAC3E,OAAOC,OAAO,MAAM,oCAAoC;AACxD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,mCAAmC,EAAEC,qCAAqC,QAAQ,6BAA6B;AACxH,SAASC,UAAU,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,0BAA0B;AAC9F,SACEC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,iBAAiB,EACjBC,aAAa,EACbC,UAAU,EACVC,UAAU,EACVC,WAAW,QACN,oBAAoB;AAC3B,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,SAAS,EAAEC,WAAW,QAAQ,yBAAyB;AAChE,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AACjG,SAASC,MAAM,QAAQ,QAAQ;AAC/B,SAASC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACjG,SAASC,YAAY,EAAEC,WAAW,QAAQ,aAAa;AACvD,SAASC,kBAAkB,EAAEC,eAAe,EAAEC,SAAS,QAAQ,kBAAkB;AACjF,SAASC,8BAA8B,EAAEC,sCAAsC,QAAQ,mCAAmC;AAC1H,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,OAAO,mCAAmC;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,kBAAkBA,CAAC;EAC1BC,QAAQ;EACRC,SAAS;EACTC,qBAAqB;EACrBC,OAAO;EACPC,aAAa;EACbC,gBAAgB;EAChBC;AACF,CAAC,EAAEC,GAAG,EAAE;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA;EAEN,IAAIC,OAAO,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAM,CAAC2B,OAAO,CAAC,GAAGvC,IAAI,CAACwC,OAAO,CAAC,CAAC;EAChC,MAAMC,YAAY,GAAG7B,MAAM,CAAC,CAAC;EAC7B,MAAM;IAAE8B;EAAO,CAAC,GAAGxB,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACyB,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAACN,MAAM,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5C;EACA,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAACsC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACwC,4BAA4B,EAAEC,8BAA8B,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACvF,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD;EACA,MAAM;IAAE4C,MAAM,EAACC,eAAe;IAAEC,WAAW;IAAEC;EAAS,CAAC,GAAGjC,QAAQ;EAClE,MAAM;IAAEkC,gBAAgB,GAAC,EAAE;IAAEC,SAAS,EAAEC,yBAAyB;IAAEC,aAAa,EAAEC;EAAwB,CAAC,GACvG7C,sCAAsC,CAACsB,MAAM,EAAEgB,eAAe,EAAE,EAAE,EAAE,CAAC,CAACA,eAAe,CAAC;EAE1F,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC;IAACuD,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC;EAC/D,MAAMP,SAAS,GAAGC,yBAAyB;EAC3C,MAAM;IAAEO,IAAI,EAAEC;EAAgB,CAAC,GAAG7F,mCAAmC,CAACgE,MAAM,CAAC,CAAC,CAAC;EAC/E,MAAM;IAAE4B,IAAI,EAAEE;EAAgB,CAAC,GAAG7F,qCAAqC,CAAC+D,MAAM,EAAEgB,eAAe,EAAEtE,UAAU,CAACqF,OAAO,EAAE,CAAC,CAACf,eAAe,CAAC,CAAC,CAAC;;EAEzI,MAAM;IAAEgB;EAAsB,CAAC,GAAGvD,8BAA8B,CAACuB,MAAM,EAAEgB,eAAe,EAAE,CAAC,CAAC;EAC5F,MAAMiB,oCAAoC,GAAGlG,WAAW,CAACmG,2BAA2B,EAAE,GAAG,CAAC;EAE1FjE,mBAAmB,CAACuB,GAAG,EAAE,OAAO;IAC9B2C,IAAI,EAAEC,uBAAuB;IAC7BC,QAAQ,EAAEC;EACZ,CAAC,CAAC,CAAC;EAEH,MAAM;IAAGC;EAAW,CAAC,GAAGlE,WAAW,CAAEmE,KAAK,KAAM;IAC9CD,QAAQ,EAAEC,KAAK,CAACC,KAAK,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC;EAC1D,CAAC,CAAC,EAAErE,YAAY,CAAC;;EAEjB;EACA,SAASsE,gBAAgBA,CAACC,IAAI,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC9B,MAAMC,UAAU,IAAAF,qBAAA,GAAGD,IAAI,CAACI,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAI3G,cAAc,CAAC4G,kBAAkB,CAAC,cAAAP,qBAAA,uBAAlFA,qBAAA,CAAoFQ,SAAS;IAChH,MAAMC,YAAY,IAAAR,sBAAA,GAAGF,IAAI,CAACI,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAI3G,cAAc,CAAC+G,oBAAoB,CAAC,cAAAT,sBAAA,uBAApFA,sBAAA,CAAsFO,SAAS;IACpH;IACA,OAAO,CAACN,UAAU,IAAI,CAAC,KAAK,CAAC,IAAKA,UAAU,IAAI,GAAG,IAAIO,YAAY,IAAI,EAAG;EAC5E;;EAEA;EACA,MAAME,mBAAmB,GAAGhE,WAAW,IAAI,GAAG,CAAC,CAAC;;EAEhD;EACA,MAAMiE,WAAW,GAAGA,CAACb,IAAI,EAAEc,KAAK,KAAK;IAAA,IAAAC,qBAAA;IACnC,MAAMC,WAAW,GAAGhB,IAAI,CAAC5B,MAAM,KAAK1E,cAAc,CAACuH,qBAAqB;IAExE,oBACE/E,OAAA,CAACvB,IAAI,CAACuG,IAAI;MAACnC,IAAI,EAAEiB,IAAI,CAAC5B,MAAO;MAC3B+C,KAAK,EAAE;QACLC,YAAY,EAAE,EAAE;QAChB;QACA,sBAAsB,EAAE;UACtBC,SAAS,EAAE,OAAO;UAClBC,YAAY,EAAE;QAChB;MACF,CAAE;MAEFC,KAAK,EAAE,cAACrF,OAAA;QAAAsF,QAAA,EAAmBxB,IAAI,CAACyB;MAAQ,GAArBX,KAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAuB,CAAC,CAAE;MAClDC,KAAK,EAAE,CACL;QACEC,QAAQ,EAAE/B,IAAI,CAACgC,WAAW,IAAI,CAAC;QAC/BC,OAAO,EAAE,KAAK,GAAGjC,IAAI,CAACyB;MACxB,CAAC;MAEH;MAAA;MAAAD,QAAA,EAGExB,IAAI,CAACkC,SAAS,IAAIrI,iBAAiB,CAACsI,OAAO,GACzCC,aAAa,CAACpC,IAAI,EAAEc,KAAK,CAAC,GAE1Bd,IAAI,CAACkC,SAAS,IAAIrI,iBAAiB,CAACwI,YAAY,gBAChDnG,OAAA,CAACtB,KAAK,CAAC0H,QAAQ;QAACC,SAAS,EAAC;MAAmB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAChD7B,IAAI,CAACkC,SAAS,IAAIrI,iBAAiB,CAAC2I,WAAW,gBAC7CtG,OAAA;QAAKiF,KAAK,EAAE;UAAEsB,KAAK,EAAE,MAAM;UAAEnB,YAAY,EAAE;QAAO,CAAE;QAAAE,QAAA,eAClDtF,OAAA,CAAC/C,OAAO;UAAC0D,GAAG,EAAEI,OAAQ;UACpByF,eAAe,EAAC,EAAE;UAClBC,cAAc,EAAGC,CAAC,IAAKC,iBAAiB,CAAC/B,KAAK,EAAEd,IAAI,EAAE4C,CAAC,CAAE;UACzDE,YAAY,EAAE;YACZzF,MAAM;YACNe,MAAM,EAAEC,eAAe;YACvB0E,UAAU,GAAAhC,qBAAA,GAAE5G,SAAS,CAACC,WAAW,CAAC4I,2BAA2B,CAAC,cAAAjC,qBAAA,uBAAlDA,qBAAA,CAAoDkC,MAAM;YACtEC,OAAO,EAAE9I,WAAW,CAAC4I;UACvB,CAAE;UACFG,cAAc,EAAEC;QAAmB;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,GACR7B,IAAI,CAACkC,SAAS,IAAIrI,iBAAiB,CAACwJ,OAAO,GAC3CrD,IAAI,CAACsD,YAAY,IAAI,MAAM,gBACvBpH,OAAA,CAACnB,MAAM;QACLwI,UAAU;QACVC,UAAU;QACVrC,KAAK,EAAE;UACLsB,KAAK,EAAEzB,WAAW,GAAG,MAAM,GAAG,GAAG;UACjCyC,YAAY,EAAE;QAChB;QACA;QAAA;QACAC,QAAQ,EAAEC,SAAS,CAAC3D,IAAI,CAAE;QAC1B4D,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;UAC/B,OAAO,CAACA,MAAM,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAACuC,KAAK,CAACvC,QAAQ,IAAI,EAAE,EAAEwC,WAAW,CAAC,CAAC,CAACC,OAAO,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;QAClG,CAAE;QAAAxC,QAAA,EAED,CAACrC,aAAa,IAAI,EAAE,EAAE+E,GAAG,CAAC,CAACC,KAAK,EAAErD,KAAK,KAAK;UAAA,IAAAsD,YAAA;UAC3C,oBAAOlI,OAAA,CAACnB,MAAM,CAACsJ,MAAM;YAEnBC,KAAK,GAAAF,YAAA,GAAED,KAAK,CAACI,MAAM,cAAAH,YAAA,uBAAZA,YAAA,CAAcI,QAAQ,CAAC,CAAE;YAChCrD,KAAK,EAAGgD,KAAK,CAACM,SAAS,KAAK,CAAC,IAAIN,KAAK,CAACO,SAAS,KAAK,CAAC,GAAE;cAACC,KAAK,EAAE;YAAS,CAAC,GAAC,CAAC,CAAE;YAAAnD,QAAA,gBAC9EtF,OAAA,CAAC5B,aAAa;cAACsK,SAAS,EAAET,KAAK,CAACU;YAAO;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1C3F,OAAA;cAAMqG,SAAS,EAAE,iBAAiB4B,KAAK,CAACM,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAGN,KAAK,CAACO,SAAS,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,EAAG;cAC1GvD,KAAK,EAAE;gBAAE2D,UAAU,EAAE;cAAE,CAAE;cAAAtD,QAAA,EAAE2C,KAAK,CAACY;YAAQ;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GALlDf,KAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMG,CAAC;QAClB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,gBAET3F,OAAA,CAACnB,MAAM;QAAC2I,QAAQ,EAAEC,SAAS,CAAC3D,IAAI,CAAE;QAChCuD,UAAU;QACVC,UAAU;QACVrC,KAAK,EAAE;UACLsB,KAAK,EAAEzB,WAAW,GAAG,MAAM,GAAG,GAAG;UACjCyC,YAAY,EAAE;QAChB,CAAE;QACFG,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;UAC7B;UACA,OAAO,CAACA,MAAM,CAACtC,QAAQ,CAACuC,KAAK,CAACvC,QAAQ,CAAC,CAAC,CAAC,CAACuC,KAAK,CAACvC,QAAQ,IAAI,EAAE,EAAEwC,WAAW,CAAC,CAAC,CAACC,OAAO,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;QAClH,CAAE;QAAAxC,QAAA,EAEF,CAACtC,aAAa,IAAI,EAAE,EAAE8F,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,IAAIlF,IAAI,CAACsD,YAAY,CAAC,CAACY,GAAG,CAAC,CAACe,IAAI,EAAEnE,KAAK,KAAK;UAC9F,oBAAO5E,OAAA,CAACnB,MAAM,CAACsJ,MAAM;YAAaC,KAAK,EAAEW,IAAI,CAAC1E,QAAS;YAAAiB,QAAA,eACrDtF,OAAA;cAAKiF,KAAK,EAAE;gBAAEgE,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAA5D,QAAA,GACnDxB,IAAI,CAACqF,SAAS,gBAAInJ,OAAA;gBAAKiF,KAAK,EAAE;kBAAEG,YAAY,EAAE;gBAAE,CAAE;gBAAAE,QAAA,eAACtF,OAAA,CAAC5B,aAAa;kBAACsK,SAAS,EAAEK,IAAI,CAACK;gBAAU;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GACpGoD,IAAI,CAACK,SAAS,gBAAIpJ,OAAA;gBAAKiF,KAAK,EAAE;kBAAEG,YAAY,EAAE;gBAAE,CAAE;gBAAAE,QAAA,EAAExF,SAAS,CAACiJ,IAAI,CAACK,SAAS;cAAC;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBACnF3F,OAAA;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAEd3F,OAAA;gBAAAsF,QAAA,EAAOyD,IAAI,CAACxE;cAAS;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC,GAPmBf,KAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQjB,CAAC;QACpB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;MACX;MACA7B,IAAI,CAACkC,SAAS,IAAIrI,iBAAiB,CAAC0L,IAAI,GACtCC,UAAU,CAACxF,IAAI,EAAEc,KAAK,CAAC,gBAAG5E,OAAA,CAAAE,SAAA,mBAAI;IAAC,GAlFlC0E,KAAK;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoFD,CAAC;EAEhB,CAAC;;EAED;EACA,MAAM4D,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,UAAU,GAAG,EAAE;IACrB,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMC,MAAM,GAAG,EAAE;IAEjBpI,QAAQ,CAACqI,OAAO,CAAC,CAAC7F,IAAI,EAAEc,KAAK,KAAK;MAChC;MACA,IAAId,IAAI,CAAC5B,MAAM,KAAK1E,cAAc,CAACuH,qBAAqB,EAAE;QACxD2E,MAAM,CAACE,IAAI,cACT5J,OAAA;UAA4BiF,KAAK,EAAE;YACjCgE,OAAO,EAAE,MAAM;YACfY,GAAG,EAAE;UACP,CAAE;UAAAvE,QAAA,gBACAtF,OAAA;YAAKiF,KAAK,EAAE;cACV6E,IAAI,EAAE,qBAAqB;cAC3BC,QAAQ,EAAE;YACZ,CAAE;YAAAzE,QAAA,EACCX,WAAW,CAACb,IAAI,EAAEc,KAAK;UAAC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACN3F,OAAA;YAAKiF,KAAK,EAAE;cACV6E,IAAI,EAAE,qBAAqB;cAC3BC,QAAQ,EAAE;YACZ;UAAE;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEG,CAAC;QAAA,GAfE,SAASf,KAAK,EAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBrB,CACP,CAAC;MACH,CAAC,MAAM,IAAI7B,IAAI,CAACkC,SAAS,KAAKrI,iBAAiB,CAAC2I,WAAW,EAAE;QAAA,IAAA0D,sBAAA;QAC3D;QACAN,MAAM,CAACE,IAAI,cACT5J,OAAA;UAAgCiF,KAAK,EAAE;YAAEgE,OAAO,EAAE,MAAM;YAAE1C,KAAK,EAAE,MAAM;YAAErB,YAAY,EAAE;UAAG,CAAE;UAAAI,QAAA,gBAC1FtF,OAAA;YAAKiF,KAAK,EAAE;cAAE6E,IAAI,EAAE,qBAAqB;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAzE,QAAA,eACzDtF,OAAA,CAACvB,IAAI,CAACuG,IAAI;cACRnC,IAAI,EAAEiB,IAAI,CAAC5B,MAAO;cAClB+C,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAC3BG,KAAK,eAAErF,OAAA;gBAAAsF,QAAA,EAAOxB,IAAI,CAACyB;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAE;cACpCC,KAAK,EAAE,CACL;gBACEC,QAAQ,EAAE/B,IAAI,CAACgC,WAAW,IAAI,CAAC;gBAC/BC,OAAO,EAAE,KAAK,GAAGjC,IAAI,CAACyB;cACxB,CAAC,CACD;cAAAD,QAAA,eAEFtF,OAAA;gBAAKiF,KAAK,EAAE;kBAAEsB,KAAK,EAAE,mBAAmB;kBAAEnB,YAAY,EAAE;gBAAO,CAAE;gBAAAE,QAAA,eAC/DtF,OAAA,CAAC/C,OAAO;kBACN0D,GAAG,EAAEI,OAAQ;kBACbyF,eAAe,EAAC,EAAE;kBAClBC,cAAc,EAAGC,CAAC,IAAKC,iBAAiB,CAAC/B,KAAK,EAAEd,IAAI,EAAE4C,CAAC,CAAE;kBACzDE,YAAY,EAAE;oBACZzF,MAAM;oBACNe,MAAM,EAAEC,eAAe;oBACvB0E,UAAU,GAAAmD,sBAAA,GAAE/L,SAAS,CAACC,WAAW,CAAC4I,2BAA2B,CAAC,cAAAkD,sBAAA,uBAAlDA,sBAAA,CAAoDjD,MAAM;oBACtEC,OAAO,EAAE9I,WAAW,CAAC4I;kBACvB,CAAE;kBACFG,cAAc,EAAEC;gBAAmB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN3F,OAAA;YAAKiF,KAAK,EAAE;cAAE6E,IAAI,EAAE,qBAAqB;cAAEC,QAAQ,EAAE;YAAI;UAAE;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEtD,CAAC;QAAA,GA/BE,aAAaf,KAAK,EAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCzB,CACP,CAAC;MACH,CAAC,MAAM;QACL;QACA,IAAI6D,UAAU,CAACS,MAAM,IAAIR,WAAW,CAACQ,MAAM,EAAE;UAC3CT,UAAU,CAACI,IAAI,CAACjF,WAAW,CAACb,IAAI,EAAEc,KAAK,CAAC,CAAC;QAC3C,CAAC,MAAM;UACL6E,WAAW,CAACG,IAAI,CAACjF,WAAW,CAACb,IAAI,EAAEc,KAAK,CAAC,CAAC;QAC5C;;QAEA;QACA,IAAI4E,UAAU,CAACS,MAAM,GAAG,CAAC,IAAIR,WAAW,CAACQ,MAAM,GAAG,CAAC,EAAE;UACnDP,MAAM,CAACE,IAAI,cACT5J,OAAA;YAAiCiF,KAAK,EAAE;cACtCgE,OAAO,EAAE,MAAM;cACfY,GAAG,EAAE;YACP,CAAE;YAAAvE,QAAA,gBACAtF,OAAA;cAAKiF,KAAK,EAAE;gBACV6E,IAAI,EAAE,qBAAqB;gBAC3BC,QAAQ,EAAE;cACZ,CAAE;cAAAzE,QAAA,EACCkE,UAAU,CAACU,MAAM,CAAC,CAAC,EAAEV,UAAU,CAACS,MAAM;YAAC;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACN3F,OAAA;cAAKiF,KAAK,EAAE;gBACV6E,IAAI,EAAE,qBAAqB;gBAC3BC,QAAQ,EAAE;cACZ,CAAE;cAAAzE,QAAA,EACCmE,WAAW,CAACS,MAAM,CAAC,CAAC,EAAET,WAAW,CAACQ,MAAM;YAAC;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA,GAfE,cAAcf,KAAK,EAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgB1B,CACP,CAAC;QACH;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAI6D,UAAU,CAACS,MAAM,GAAG,CAAC,IAAIR,WAAW,CAACQ,MAAM,GAAG,CAAC,EAAE;MACnDP,MAAM,CAACE,IAAI,cACT5J,OAAA;QAA4BiF,KAAK,EAAE;UACjCgE,OAAO,EAAE,MAAM;UACfY,GAAG,EAAE;QACP,CAAE;QAAAvE,QAAA,gBACAtF,OAAA;UAAKiF,KAAK,EAAE;YACV6E,IAAI,EAAE,qBAAqB;YAC3BC,QAAQ,EAAE;UACZ,CAAE;UAAAzE,QAAA,EACCkE;QAAU;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACN3F,OAAA;UAAKiF,KAAK,EAAE;YACV6E,IAAI,EAAE,qBAAqB;YAC3BC,QAAQ,EAAE;UACZ,CAAE;UAAAzE,QAAA,EACCmE;QAAW;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA,GAfC,kBAAkB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBtB,CACP,CAAC;IACH;IAEA,OAAO+D,MAAM;EACf,CAAC;EAEDvK,SAAS,CAAC,MAAI;IACZ,IAAGuD,uBAAuB,EAAC;MACzB,MAAMpB,QAAQ,GAAGgB,gBAAgB,CAACwG,MAAM,CAAChF,IAAI;QAAA,IAAAqG,sBAAA;QAAA,OAAIrG,IAAI,CAACsG,QAAQ,IAAI3M,gBAAgB,CAAC4M,oBAAoB;QACrG;QACA,CAAC,EAAAF,sBAAA,GAAArG,IAAI,CAACI,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAI3G,cAAc,CAAC4M,cAAc,CAAC,cAAAH,sBAAA,uBAA9EA,sBAAA,CAAgF5F,SAAS,KAAI,CAAC,KAAK,CAAC,IACrGV,gBAAgB,CAACC,IAAI,CAAC;MAAA,EAAC,CACtBkE,GAAG,CAAClE,IAAI,IAAI;QAAA,IAAAyG,kBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA;QACX,MAAMxD,YAAY,GAAG,EAAAmD,kBAAA,GAAAjN,iBAAiB,CAACwG,IAAI,CAACI,YAAY,EAAExG,cAAc,CAACmN,iBAAkB,CAAC,cAAAN,kBAAA,uBAAvEA,kBAAA,CAAyEhG,SAAS,KAAI,EAAE;QAC7G,OAAO;UACL,GAAGT,IAAI;UACPkC,SAAS,GAAAwE,sBAAA,GAAG1G,IAAI,CAACI,YAAY,CAACC,IAAI,CAAC2G,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAI3G,cAAc,CAACqN,kBAAkB,CAAC,cAAAP,sBAAA,uBAA/EA,sBAAA,CAAkFjG,SAAS;UACtG6C,YAAY,EAAEA,YAAY;UAC1BtB,WAAW,EAAE,EAAA2E,sBAAA,GAAC3G,IAAI,CAACI,YAAY,CAACC,IAAI,CAAC2G,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAI3G,cAAc,CAACsN,iBAAiB,CAAC,cAAAP,sBAAA,uBAA9EA,sBAAA,CAAiFlG,SAAS,KAAI,CAAC;UAAI;UAChH0G,SAAS,EAAE,EAAAP,sBAAA,GAAA5G,IAAI,CAACI,YAAY,CAACC,IAAI,CAAC2G,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAI3G,cAAc,CAAC+G,oBAAoB,CAAC,cAAAiG,sBAAA,uBAAhFA,sBAAA,CAAkFnG,SAAS,KAAI,EAAE;UAAI;UAChH2G,YAAY,EAAE,EAAAP,sBAAA,GAAA7G,IAAI,CAACI,YAAY,CAACC,IAAI,CAAC2G,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAI3G,cAAc,CAAC+G,oBAAoB,CAAC,cAAAkG,sBAAA,uBAAhFA,sBAAA,CAAkFpG,SAAS,KAAI,EAAE;UAAE;UACjH4E,SAAS,EAAE,EAAAyB,mBAAA,GAAAtN,iBAAiB,CAACwG,IAAI,CAACI,YAAY,EAAExG,cAAc,CAACyN,iBAAiB,CAAC,cAAAP,mBAAA,uBAAtEA,mBAAA,CAAwErG,SAAS,KAAI1G,UAAU,CAACuN;QAC7G,CAAC;MACH,CAAC,CAAC,IAAI,EAAE;;MAER;MACA,IAAIC,MAAM,GAAG,CAAC,CAAC;MACf/J,QAAQ,CAAC0G,GAAG,CAAC,CAACe,IAAI,EAAEnE,KAAK,KAAK;QAC5B,IAAImE,IAAI,CAACkC,SAAS,EAAE;UAClB,OAAOI,MAAM,CAACtC,IAAI,CAAC7G,MAAM,CAAC,GAAG6G,IAAI,CAACkC,SAAS;QAC7C;QACA;MACF,CAAC,CAAC;MACFhJ,gBAAgB,CAACoJ,MAAM,CAAC;MACxBrK,OAAO,CAACsK,cAAc,CAACD,MAAM,CAAC;MAC9B9J,WAAW,CAACD,QAAQ,CAAC;IACzB;EACF,CAAC,EAAC,CAACoB,uBAAuB,CAAC,CAAC;EAE5BhD,eAAe,CAACR,WAAW,CAAEqM,KAAK,IAAK;IACrC,IAAGC,QAAQ,CAAC,CAAC,EAAED,KAAK,CAACE,cAAc,CAAC,CAAC;EACvC,CAAC,EAAC,EAAE,CAAC,EAAE;IAACC,OAAO,EAAE;EAAI,CAAC,CAAC;EAEvBjM,kBAAkB,CAAC;IACjBkM,IAAI,EAAEzM,WAAW,CAAC,MAAM;MACtB,OAAO,CAACgC,YAAY,CAAC0K,OAAO,IAAIJ,QAAQ,CAAC,CAAC;IAC5C,CAAC,EAAC,EAAE,CAAC;IACLzF,OAAO,EAAE;EACX,CAAC,CAAC;;EAEA;EACA,MAAMxC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CvC,OAAO,CAAC6K,MAAM,CAAC,CAAC;EAClB,CAAC;EAED,SAASL,QAAQA,CAAA,EAAE;IACjB,IAAIM,QAAQ,GAAG9K,OAAO,CAAC+K,cAAc,CAAC,IAAI,CAAC;IAC3C;IACA,IAAIC,WAAW,GAAG3O,UAAU,CAACyO,QAAQ,EAAE9J,aAAa,CAAC;IACrD,OAAO,EAAEgK,WAAW,IAAIlK,4BAA4B,CAAC;EACvD;;EAEA;EACA,SAAS2B,2BAA2BA,CAAA,EAAG;IACrC,IAAI,CAAC+H,QAAQ,CAAC,CAAC,EAAE;MACfjL,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;IAClB,CAAC,MAAM;MAAA,IAAA0L,qBAAA;MACL,OAAOrN,KAAK,CAACsN,OAAO,CAAC;QACnBpJ,KAAK,EAAE,IAAI;QACXqJ,QAAQ,EAAE,IAAI;QACdC,OAAO,eAAEpM,OAAA;UAAKiF,KAAK,EAAE;YAAEE,SAAS,EAAE,QAAQ;YAAEsD,KAAK,EAAE;UAAU,CAAE;UAAAnD,QAAA,eAC/DtF,OAAA;YAAAsF,QAAA,GAAK,0BAAI,GAAA2G,qBAAA,GAAC7J,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiK,UAAU,cAAAJ,qBAAA,cAAAA,qBAAA,GAAE,IAAI,EAAC,yDAAU;UAAA;YAAAzG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;QACJ2G,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAI;QAChBjJ,IAAI,EAAEA,CAAA,KAAM;UACV/C,OAAO,CAAC,KAAK,CAAC;QAChB,CAAC;QACDiD,QAAQ,EAAEA,CAAA,KAAM,CAChB;MACF,CAAC,CAAC;IACJ;EACJ;;EAEA;EACA,MAAMgJ,OAAO,GAAG,CACd;IACE1J,KAAK,EAAE,IAAI;IAAE2J,SAAS,EAAE,MAAM;IAAEC,GAAG,EAAE,MAAM;IAC3CC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,EAAEjI,KAAK,kBAAK5E,OAAA,CAAAE,SAAA;MAAAoF,QAAA,gBAC/BtF,OAAA,CAAC7B,mBAAmB;QAACiM,QAAQ,EAAEyC,MAAM,CAAC7F,OAAQ;QAACX,SAAS,EAAC,aAAa;QAACpB,KAAK,EAAE;UAAEwD,KAAK,EAAE,SAAS;UAAEqE,OAAO,EAAE;QAAI;MAAE;QAAAtH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,EAClHiH,IAAI;IAAA,eACL;EACJ,CAAC,EACD;IAAE9J,KAAK,EAAE,IAAI;IAAE2J,SAAS,EAAE,cAAc;IAAEC,GAAG,EAAE;EAAgB,CAAC,EAChE;IAAE5J,KAAK,EAAE,KAAK;IAAE2J,SAAS,EAAE,UAAU;IAAEC,GAAG,EAAE;EAAY,CAAC,EACzD;IAAE5J,KAAK,EAAE,KAAK;IAAE2J,SAAS,EAAE,SAAS;IAAEC,GAAG,EAAE;EAAW,CAAC,EACvD;IAAE5J,KAAK,EAAE,MAAM;IAAE2J,SAAS,EAAE,UAAU;IAAEC,GAAG,EAAE;EAAY,CAAC,EAC1D;IAAE5J,KAAK,EAAE,IAAI;IAAE2J,SAAS,EAAE,WAAW;IAAEC,GAAG,EAAE,WAAW;IAAEC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBAAK7M,OAAA;MAAG+M,OAAO,EAAEA,CAAA,KAAMC,cAAc,CAACH,MAAM,CAAE;MAAAvH,QAAA,EAAC;IAAE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAE,CAAC,CACtI;EAGD,SAASuB,kBAAkBA,CAACnE,IAAI,EAAE;IAChC,IAAIkK,IAAI,GAAGlK,IAAI,CAACkK,IAAI;IACpB,IAAIpK,IAAI,GAAGoK,IAAI,CAACC,SAAS,CAACD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,IAAIC,OAAO,GAAG;MAAEC,KAAK,EAAEtK,IAAI,CAACuK,EAAE;MAAEtG,OAAO,EAAEjJ,WAAW,CAACwP,oBAAoB;MAAEC,OAAO,EAAE3K,IAAI;MAAE4K,KAAK,EAAE;IAAE,CAAC;IACpGhM,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE4L,OAAO,CAAC,CAAC;EACnC;;EAEA;EACA,SAASzG,iBAAiBA,CAAC/B,KAAK,EAAEd,IAAI,EAAE4C,CAAC,EAAE;IACzC,IAAIxE,MAAM,GAAG4B,IAAI,CAAC5B,MAAM;IACxBlB,OAAO,CAAC0M,aAAa,CAACxL,MAAM,EAAEnB,OAAO,CAAC6K,OAAO,CAAC+B,UAAU,CAAC,CAAC,CAAC;EAC7D;;EAEA;EACA,SAASzH,aAAaA,CAACpC,IAAI,EAAEc,KAAK,EAAE;IAClC,MAAME,WAAW,GAAGhB,IAAI,CAAC5B,MAAM,KAAK1E,cAAc,CAACuH,qBAAqB,IAAIjB,IAAI,CAACkC,SAAS,KAAKrI,iBAAiB,CAAC2I,WAAW;IAE5H,IAAIsH,MAAM,GAAG9J,IAAI,CAACI,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAI3G,cAAc,CAACmQ,cAAc,CAAC;IAC3F,IAAIzF,KAAK,GAAG7K,oBAAoB,CAACyF,aAAa,EAAE4K,MAAM,CAACxG,YAAY,EAAEwG,MAAM,CAACrJ,SAAS,CAAC;IACtF,QAAQ6D,KAAK;MACX,KAAK,IAAI;QACP,oBAAOpI,OAAA,CAACrB,WAAW;UAACmP,IAAI,EAAE,QAAS;UAAC7I,KAAK,EAAE;YAAEsB,KAAK,EAAEzB,WAAW,GAAG,MAAM,GAAG;UAAI;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtF,KAAK,IAAI;QACP,oBAAO3F,OAAA,CAACrB,WAAW;UAACmP,IAAI,EAAE,QAAS;UAACC,SAAS,EAAE,CAAE;UAAC9I,KAAK,EAAE;YAAEsB,KAAK,EAAEzB,WAAW,GAAG,MAAM,GAAG;UAAI;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpG;QACE,oBAAO3F,OAAA;UAAKiF,KAAK,EAAE;YAACgE,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC;UAAQ,CAAE;UAAA5D,QAAA,gBACtDtF,OAAA,CAACtB,KAAK;YAACuG,KAAK,EAAE;cAAEsB,KAAK,EAAEzB,WAAW,GAAG,MAAM,GAAG,GAAG;cAAEyC,YAAY,EAAE;YAAE,CAAE;YAC9DyG,SAAS,EAAE,GAAI;YAACC,YAAY,EAAC,KAAK;YAClCC,QAAQ,EAAGxH,CAAC,IAAG9D,WAAW,CAAC;cAACC,IAAI,EAAEiB,IAAI,CAAC5B,MAAM;cAAEY,KAAK,EAAE4D,CAAC,CAACyH,MAAM,CAAC/F;YAAK,CAAC;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAChF3F,OAAA;YAAMiF,KAAK,EAAE;cAACwD,KAAK,EAAC,MAAM;cAACG,UAAU,EAAC;YAAC,CAAE;YAAAtD,QAAA,EACtC3C,QAAQ,CAACE,IAAI,IAAIiB,IAAI,CAAC5B,MAAM,IAAIS,QAAQ,CAACG,KAAK,CAACmH,MAAM,IAAI,GAAG,GAAG,UAAU,GAAG;UAAE;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;IACV;EACF;EACA;EACA,SAAS2D,UAAUA,CAACxF,IAAI,EAAEc,KAAK,EAAE;IAC/B,IAAIwJ,QAAQ,GAAGtK,IAAI,CAACI,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAI3G,cAAc,CAACmQ,cAAc,CAAC;IAC7F,IAAID,MAAM,GAAGQ,QAAQ,GAAG7Q,oBAAoB,CAACyF,aAAa,EAAEoL,QAAQ,CAAChH,YAAY,EAAEgH,QAAQ,CAAC7J,SAAS,CAAC,GAAG,YAAY;IACrH,IAAI8J,IAAI,GAAG,UAAU;IACrB,IAAIC,QAAQ,GAAGV,MAAM,CAAC7F,OAAO,CAACsG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI;IACxD,oBAAOrO,OAAA,CAACxB,UAAU;MAAC+P,WAAW,EAAC,gCAAO;MAACX,MAAM,EAAEA,MAAO;MAACU,QAAQ,EAAEA;IAAS;MAAA9I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/E;;EAEA;EACA,SAAS8B,SAASA,CAACsB,IAAI,EAAE;IACvB,IAAIyF,cAAc,GAAGzF,IAAI,CAAC7E,YAAY,CAACC,IAAI,CAAC2G,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAI3G,cAAc,CAAC4G,kBAAkB,CAAC;IACnG,IAAI,CAAAkK,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjK,SAAS,KAAI,GAAG,EAAE;MACpC,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;;EAEA;EACA,SAASyI,cAAcA,CAACyB,OAAO,EAAE;IAC/B9M,aAAa,CAACD,UAAU,CAACoH,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC2F,MAAM,IAAID,OAAO,CAACC,MAAM,CAAC,CAAC;EACzE;;EAEA;EACA,SAASC,gBAAgBA,CAACC,aAAa,EAAE;IACvC;IACA/M,wBAAwB,CAAC,KAAK,CAAC;IAC/B+M,aAAa,CAACjF,OAAO,CAAC,CAACZ,IAAI,EAAEnE,KAAK,KAAK;MACrCmE,IAAI,CAACsE,KAAK,GAAGtE,IAAI,CAAC2F,MAAM;MACxB3F,IAAI,CAAC/B,OAAO,GAAG+B,IAAI,CAACqB,QAAQ;MAC5BrB,IAAI,CAACyE,OAAO,GAAGzE,IAAI,CAAClG,IAAI;MACxBkG,IAAI,CAAC0E,KAAK,GAAG7I,KAAK;MAClBmE,IAAI,CAAC8F,OAAO,GAAGnL,QAAQ,CAACmF,QAAQ;IAClC,CAAC,CAAC;IACFlH,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE,GAAGkN,aAAa,CAAC,CAAC;IAChD7M,8BAA8B,CAAC,KAAK,CAAC;EACvC;EACA;EACA,eAAgBsB,2BAA2BA,CAAA,EAAG;IAAA,IAAAyL,IAAA;IAC5CrO,gBAAgB,CAAC,IAAI,CAAC;IACtB;IACA;IACA,IAAIqL,QAAQ,GAAG9K,OAAO,CAAC+K,cAAc,CAAC,IAAI,CAAC;IAC3C,IAAIgD,YAAY,GAAG,CAAC,CAAC;IACrB,CAAAD,IAAA,GAAC,CAAAxN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwH,MAAM,CAAChF,IAAI;MAAA,IAAAkL,YAAA;MAAA,OAAG,GAAAA,YAAA,GAACC,MAAM,CAACC,IAAI,CAACpD,QAAQ,CAAC,cAAAkD,YAAA,eAArBA,YAAA,CAAuBG,IAAI,CAACjN,MAAM,IAAEA,MAAM,IAAI4B,IAAI,CAAC5B,MAAM,CAAC;IAAA,CAAC,CAAC,KAAI,EAAE,cAAA4M,IAAA,uBAA5FA,IAAA,CAA+F9G,GAAG,CAAClE,IAAI,IAAE;MACvGiL,YAAY,CAACjL,IAAI,CAAC5B,MAAM,CAAC,GAAG,IAAI;IAClC,CAAC,CAAC;IACFkN,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEvD,QAAQ,CAAC;IACjC;IACAA,QAAQ,GAAG;MAAE,GAAGA;IAAS,CAAC;IAC1B,KAAK,IAAIhI,IAAI,IAAIgI,QAAQ,EAAE;MACzBxK,QAAQ,CAACwH,MAAM,CAACgC,EAAE,IAAIA,EAAE,CAAC9E,SAAS,IAAIrI,iBAAiB,CAAC0L,IAAI,CAAC,CAACrB,GAAG,CAAC,CAACe,IAAI,EAAEnE,KAAK,KAAK;QACjF,IAAId,IAAI,IAAIiF,IAAI,CAAC7G,MAAM,IAAI4J,QAAQ,CAAChI,IAAI,CAAC,EAAE;UACzCgI,QAAQ,CAAChI,IAAI,CAAC,GAAGgI,QAAQ,CAAChI,IAAI,CAAC,CAAC8J,MAAM,CAAC,YAAY,CAAC;QACtD;MACF,CAAC,CAAC;IACJ;IACA,IAAI0B,MAAM,GAAG;MAAElO,MAAM,EAAEA,MAAM;MAAEmO,cAAc,EAAEpN,eAAe;MAAEhB,MAAM,EAAEA,MAAM;MAAEO,UAAU,EAAE,CAAC,GAAGA,UAAU,EAAE,GAAGF,OAAO,CAAC;MAAE,GAAGsK,QAAQ;MAAE,GAAGiD;IAAa,CAAC;IACvJ,MAAM,IAAIS,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACrCvM,qBAAqB,CAACmM,MAAM,EAAE;QAC5BK,SAAS,EAAEA,CAAC5M,IAAI,EAAE6M,IAAI,KAAK;UACzB,IAAI7M,IAAI,CAAC8M,UAAU,IAAI,GAAG,EAAE;YAC1B7R,UAAU,CAAC8R,OAAO,CAAC,OAAO,CAAC;YAC3B5O,YAAY,CAAC0K,OAAO,GAAG,IAAI;YAC3BvJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGU,IAAI,CAACgN,SAAS,CAAC,CAAC,CAAC;YAC5B;YACA,IAAIzP,qBAAqB,EAAE;cACzBS,OAAO,CAAC6K,OAAO,CAACoE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;cAClChP,OAAO,CAACsK,cAAc,CAACtJ,aAAa,CAAC,CAAC;cACtCP,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;cAChBE,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;YACrB,CAAC,MAAM;cACL;cACApB,OAAO,CAAC,KAAK,CAAC;YAChB;UACF,CAAC,MAAM;YACL6O,OAAO,CAACC,GAAG,CAACtM,IAAI,EAAE6M,IAAI,CAAC;UACzB;UACAvO,SAAS,CAACrC,MAAM,CAAC,CAAC,CAAC;UACnByQ,OAAO,CAAC,CAAC;QACX,CAAC;QACDQ,OAAO,EAAEA,CAACC,GAAG,EAAEZ,MAAM,KAAK,CAC1B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF7O,gBAAgB,CAAC,KAAK,CAAC;EACzB;;EAEA;EACA,SAAS0P,UAAUA,CAACC,IAAI,EAAE;IACxB,IAAIA,IAAI,CAACC,IAAI,CAACC,MAAM,KAAK,WAAW,EAAE;MACpClB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEe,IAAI,CAACC,IAAI,EAAED,IAAI,CAACG,QAAQ,CAAC;IACpD;IACA,IAAIH,IAAI,CAACC,IAAI,CAACC,MAAM,KAAK,MAAM,EAAE;MAC/B,IAAIF,IAAI,CAACC,IAAI,CAACG,QAAQ,CAACX,UAAU,IAAI,GAAG,EAAE;QACxC,IAAI5C,IAAI,GAAGmD,IAAI,CAACC,IAAI,CAACG,QAAQ,CAACvD,IAAI;QAClC,IAAIpK,IAAI,GAAGoK,IAAI,CAACC,SAAS,CAACD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpD,IAAIC,OAAO,GAAG;UAAEC,KAAK,EAAE+C,IAAI,CAACC,IAAI,CAACG,QAAQ,CAAClD,EAAE;UAAEtG,OAAO,EAAEjJ,WAAW,CAACwP,oBAAoB;UAAEC,OAAO,EAAE3K,IAAI;UAAE4K,KAAK,EAAE;QAAE,CAAC;QAClHhM,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE4L,OAAO,CAAC,CAAC;QACjCpP,UAAU,CAAC8R,OAAO,CAAC,GAAGM,IAAI,CAACC,IAAI,CAACxN,IAAI,UAAU,CAAC;MACjD;IACF,CAAC,MAAM,IAAIuN,IAAI,CAACC,IAAI,CAACC,MAAM,KAAK,OAAO,EAAE;MACvClB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEe,IAAI,CAACC,IAAI,EAAED,IAAI,CAACG,QAAQ,CAAC;MAC9CvS,UAAU,CAACyS,KAAK,CAAC,GAAGL,IAAI,CAACC,IAAI,CAACxN,IAAI,UAAU,CAAC;IAC/C;EACF;EAEA,oBAAO7C,OAAA,CAAAE,SAAA;IAAAoF,QAAA,eACHtF,OAAA,CAAC1B,aAAa;MAACoS,QAAQ,EAAEnO,SAAS,IAAI/B,aAAc;MAACmQ,KAAK,EAAE/S,aAAa,CAACgT,WAAY;MAAAtL,QAAA,eACpFtF,OAAA;QAAKqG,SAAS,EAAC,cAAc;QAACpB,KAAK,EAAE;UAAC4L,SAAS,EAAC;QAAM,CAAE;QAAAvL,QAAA,gBACtDtF,OAAA,CAACvB,IAAI;UACHqS,IAAI,EAAE9P,OAAQ;UACd+P,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAE,CAAE;UACtBC,UAAU,EAAE;YAAED,IAAI,EAAE;UAAG,CAAE;UACzBE,UAAU,EAAC,OAAO;UAClBC,QAAQ,EAAE/N,oCAAqC;UAC/CgO,kBAAkB;UAAA;UAAA9L,QAAA,GAEjBZ,mBAAmB;UAClB;UACA6E,uBAAuB,CAAC,CAAC;UAEzB;UACAjI,QAAQ,CAAC0G,GAAG,CAAC,CAAClE,IAAI,EAAEc,KAAK,KAAKD,WAAW,CAACb,IAAI,EAAEc,KAAK,CAAC,CACvD,EAED,CAACrC,SAAS,IAAImC,mBAAmB,iBAAI1E,OAAA,CAAAE,SAAA;YAAAoF,QAAA,gBACpCtF,OAAA;cAAKiF,KAAK,EAAE;gBACVgE,OAAO,EAAE,MAAM;gBACfY,GAAG,EAAE;cACP,CAAE;cAAAvE,QAAA,gBACAtF,OAAA;gBAAKiF,KAAK,EAAE;kBACV6E,IAAI,EAAE,qBAAqB;kBAC3BC,QAAQ,EAAE;gBACZ,CAAE;gBAAAzE,QAAA,eACAtF,OAAA,CAACvB,IAAI,CAACuG,IAAI;kBAACnC,IAAI,EAAC,QAAQ;kBAACwC,KAAK,EAAC,cAAI;kBAACJ,KAAK,EAAE;oBAACC,YAAY,EAAC;kBAAE,CAAE;kBAAAI,QAAA,eAC3DtF,OAAA,CAACjB,MAAM;oBAAC8D,IAAI,EAAE,MAAO;oBACnBwO,QAAQ,EAAE,IAAK;oBACfC,MAAM,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,2BAA4B;oBACrE1O,IAAI,EAAE;sBACJ5B,MAAM,EAAEA,MAAM;sBACd0F,UAAU,GAAAhG,sBAAA,GAAE5C,SAAS,CAACC,WAAW,CAAC4I,2BAA2B,CAAC,cAAAjG,sBAAA,uBAAlDA,sBAAA,CAAoDkG,MAAM;sBACtE7E,MAAM,EAAEpE,UAAU;sBAClBkJ,OAAO,EAAE9I,WAAW,CAAC4I;oBACzB,CAAE;oBACAoH,QAAQ,EAAEiC,UAAW;oBAAA7K,QAAA,eAErBtF,OAAA,CAACzB,MAAM;sBAACuP,IAAI,EAAC,MAAM;sBAAC4D,IAAI,eAAE1R,OAAA,CAAChD,YAAY;wBAAAwI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACN3F,OAAA;gBAAKiF,KAAK,EAAE;kBACV6E,IAAI,EAAE,qBAAqB;kBAC3BC,QAAQ,EAAE;gBACZ;cAAE;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3F,OAAA;cAAKiF,KAAK,EAAE;gBACVgE,OAAO,EAAE,MAAM;gBACfY,GAAG,EAAE;cACP,CAAE;cAAAvE,QAAA,gBACAtF,OAAA;gBAAKiF,KAAK,EAAE;kBACV6E,IAAI,EAAE,qBAAqB;kBAC3BC,QAAQ,EAAE;gBACZ,CAAE;gBAAAzE,QAAA,eACAtF,OAAA,CAACvB,IAAI,CAACuG,IAAI;kBAACnC,IAAI,EAAC,UAAU;kBAACwC,KAAK,EAAC,0BAAM;kBAACJ,KAAK,EAAE;oBAACC,YAAY,EAAC;kBAAC,CAAE;kBAAAI,QAAA,gBAC9DtF,OAAA,CAACzB,MAAM;oBAACuP,IAAI,EAAC,MAAM;oBAAC4D,IAAI,eAAE1R,OAAA,CAAChD,YAAY;sBAAAwI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzCoH,OAAO,EAAGrG,CAAC,IAAK7E,wBAAwB,CAAC,IAAI,CAAE;oBAAAyD,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC9DjE,UAAU,CAACuI,MAAM,GAAG,CAAC,iBACpBjK,OAAA,CAAClB,KAAK;oBAACuH,SAAS,EAAC,eAAe;oBAC9BmG,OAAO,EAAEA,OAAQ;oBACjBmF,UAAU,EAAEjQ,UAAW;oBACvBkQ,IAAI,EAAE,OAAQ;oBACdC,UAAU,EAAE;kBAAM;oBAAArM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACN3F,OAAA;gBAAKiF,KAAK,EAAE;kBACV6E,IAAI,EAAE,qBAAqB;kBAC3BC,QAAQ,EAAE;gBACZ;cAAE;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACL,CAAC,EAGH,CAACpD,SAAS,IAAI,CAACmC,mBAAmB,iBAAI1E,OAAA,CAAAE,SAAA;YAAAoF,QAAA,gBACrCtF,OAAA,CAACvB,IAAI,CAACuG,IAAI;cAACnC,IAAI,EAAC,QAAQ;cAACwC,KAAK,EAAC,cAAI;cAACJ,KAAK,EAAE;gBAACC,YAAY,EAAC;cAAE,CAAE;cAAAI,QAAA,eAC3DtF,OAAA,CAACjB,MAAM;gBAAC8D,IAAI,EAAE,MAAO;gBACnBwO,QAAQ,EAAE,IAAK;gBACfC,MAAM,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,2BAA4B;gBACrE1O,IAAI,EAAE;kBACJ5B,MAAM,EAAEA,MAAM;kBACd0F,UAAU,GAAA/F,sBAAA,GAAE7C,SAAS,CAACC,WAAW,CAAC4I,2BAA2B,CAAC,cAAAhG,sBAAA,uBAAlDA,sBAAA,CAAoDiG,MAAM;kBACtE7E,MAAM,EAAEpE,UAAU;kBAClBkJ,OAAO,EAAE9I,WAAW,CAAC4I;gBACzB,CAAE;gBACAoH,QAAQ,EAAEiC,UAAW;gBAAA7K,QAAA,eAErBtF,OAAA,CAACzB,MAAM;kBAACuP,IAAI,EAAC,MAAM;kBAAC4D,IAAI,eAAE1R,OAAA,CAAChD,YAAY;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAL,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACZ3F,OAAA,CAACvB,IAAI,CAACuG,IAAI;cAACnC,IAAI,EAAC,UAAU;cAACwC,KAAK,EAAC,0BAAM;cAACJ,KAAK,EAAE;gBAACC,YAAY,EAAC;cAAC,CAAE;cAAAI,QAAA,gBAC9DtF,OAAA,CAACzB,MAAM;gBAACuP,IAAI,EAAC,MAAM;gBAAC4D,IAAI,eAAE1R,OAAA,CAAChD,YAAY;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzCoH,OAAO,EAAGrG,CAAC,IAAK7E,wBAAwB,CAAC,IAAI,CAAE;gBAAAyD,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9DjE,UAAU,CAACuI,MAAM,GAAG,CAAC,iBACpBjK,OAAA,CAAClB,KAAK;gBAACuH,SAAS,EAAC,eAAe;gBAC9BmG,OAAO,EAAEA,OAAQ;gBACjBmF,UAAU,EAAEjQ,UAAW;gBACvBkQ,IAAI,EAAE,OAAQ;gBACdC,UAAU,EAAE;cAAM;gBAAArM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CAAC;UAAA,eACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEC,CAAC,eACP3F,OAAA,CAAC3B,YAAY;UACX8C,MAAM,EAAEA,MAAO;UACf2Q,YAAY,EAAElQ,qBAAsB;UACpCyL,KAAK,EAAE,GAAI;UACXrG,OAAO,EAAE9I,WAAW,CAAC4I,2BAA4B;UACjDxD,IAAI,EAAEqL,gBAAiB;UACvBoD,QAAQ,EAAGrL,CAAC,IAAK7E,wBAAwB,CAAC,KAAK;QAAE;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC,gBAClB,CAAC;AACL;AAAC/E,EAAA,CAjoBQT,kBAAkB;EAAA,QAWP1B,IAAI,CAACwC,OAAO,EAEXtB,SAAS,EAYxBE,sCAAsC,EAIR1C,mCAAmC,EACnCC,qCAAqC,EAErCwC,8BAA8B,EACnB1C,WAAW,EAOhCsC,WAAW,EAkRnCE,eAAe;AAAA;AAAAsS,EAAA,GA1TR7R,kBAAkB;AAmoB3B,eAAA8R,GAAA,gBAAehT,UAAU,CAACkB,kBAAkB,CAAC;AAAC,IAAA6R,EAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAF,EAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}