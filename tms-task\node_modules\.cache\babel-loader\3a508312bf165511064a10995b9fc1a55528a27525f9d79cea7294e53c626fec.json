{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\inspect\\\\views\\\\InspectProject\\\\CreatePartitionDrawer\\\\CreatePartitionDrawer.jsx\",\n  _s = $RefreshSig$();\n/*\r\n * @Author: Walt <EMAIL>\r\n * @Date: 2024-02-02 17:53:39\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2025-08-05 16:48:31\r\n * @Description: 采集口径\r\n */\n/* eslint-disable react-hooks/exhaustive-deps */\nimport { ExclamationCircleOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';\nimport * as http from \"@common/api/http\";\nimport { useQuerySetting202_getTeamAllUsers, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\nimport { compareArr, isEmpty } from '@common/utils/ArrayUtils';\nimport { eConsolePropId, eEditingMode, eEnableFlg, eOpType, eSelectionListId } from \"@common/utils/enum\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { getQueryableAttrNodeList, transformAttrNodeListForUI, transformCriteriaListForUI, getFlowList } from '@common/utils/logicUtils';\nimport * as toolUtil from \"@common/utils/toolUtil\";\nimport { eNodeTypeId, refreshTeamMenu, refreshSelectTeamMenu, inspCreatePartitionNodeTypeList } from \"@common/utils/TsbConfig\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { Button, Drawer, Form, Input, Modal, Space, Checkbox, Select } from \"antd\";\nimport { useEffect, useRef, useState, useMemo } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport SearchDisplayField from 'src/quickAcess/views/Search/SearchDetail/SearchDisplayField';\nimport SearchEditTable from \"src/quickAcess/views/Search/SearchDetail/SearchEditTable\";\nimport SearchReultPreviewModal from 'src/quickAcess/views/Search/SearchDetail/SearchReultPreviewModal';\nimport { useQueryTrack019GetPartitionDetail, useQuerySetting409_getTeamAttrgrpProps } from \"src/issueTrack/service/issueHooks\";\nimport AddPartitionAttr from \"./AddPartitionAttr\";\nimport CustomerFormTable from \"./CustomerFormTable\";\nimport { eRegionType } from \"src/inspect/utils/enum\";\nimport UploadLoading from \"@components/UploadLoading\";\nimport TEmpty from '@components/TEmpty';\nimport \"./CreatePartitionDrawer.scss\";\nimport { insp_091_get_insp_project_detail_query } from \"@common/api/query/inspect/query_insp_01_mgmt\";\nimport { useGetInspectRelatedNodes, useQueryCust008GetCustFormList, useQueryCust007GetCustObjList } from \"src/inspect/service/inspectHooks\";\nimport DraggableDrawer from \"@components/DraggableDrawer\";\n\n// 新建&编辑 采集口径\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CreatePartitionDrawer() {\n  _s();\n  var _projectInfo$issueAli, _projectInfo$issueAli2, _projectInfo$issueAli3;\n  const {\n    teamId\n  } = useParams();\n  const [form] = Form.useForm();\n  const tRef = useRef(Object.create(null));\n  // 弹窗显示配置\n  const [isModalVisible, setIsModalVisible] = useState(false); //采集口径弹窗\n  const [searchPreFlg, setSearchPreFlg] = useState(false); //是否搜索预览\n  const [searchDisplayFieldOpen, setSearchDisplayFieldOpen] = useState(false); //显示字段弹窗\n  const [isAddAttrModalVisible, setIsAddAttrModalVisible] = useState(false); //添加字段弹窗\n  // 数据配置\n  const [nodeItem, setNodeItem] = useState({}); //节点信息\n  // const [opType, setOpType] = useState(eEditingMode.Creating_0); //0:新建 1：编辑\n  const [selectFields, setSelectFields] = useState([]); //已选择的表单字段\n  const [initTableData, setInitTableData] = useState([]); // 初始数据\n  // attrNodeList 和 checkedValues 现在通过 useMemo 计算，不再需要 useState\n  const [userSelectedCheckedValues, setUserSelectedCheckedValues] = useState([]); // 用户手动选择的显示字段\n  const [modalKey, setModalKey] = useState(toolUtil.guid()); // 显示字段勾选\n\n  const [flowList, setFlowList] = useState([]); // 流程图列表\n  const [uploadLoading, setUploadLoading] = useState(false); // 提交加载中\n  const [showFormFields, setShowFormFields] = useState(true); // 控制表单字段设置的显示\n\n  const opType = useMemo(() => {\n    return inspCreatePartitionNodeTypeList.includes(nodeItem.nodeType) ? eEditingMode.Modifying_1 : eEditingMode.Creating_0; // 如果nodeType为分区节点，则为编辑采集口径\n  }, [nodeItem]);\n  const {\n    data: {\n      isCust,\n      isOA\n    },\n    isLoading: isLoadingInsp072\n  } = useGetInspectRelatedNodes({\n    teamId,\n    nodeId: nodeItem.nodeId,\n    enabled: !!nodeItem.nodeId\n  });\n\n  // 接口调用\n  //（1）获取项目信息和可发起流程\n  const {\n    data: projectInfo = {},\n    isLoading: isLoadingInsp091,\n    dataUpdatedAt: dataUpdatedAtInsp091\n  } = useQuery(insp_091_get_insp_project_detail_query(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, !!(nodeItem !== null && nodeItem !== void 0 && nodeItem.nodeId)));\n\n  // 获取自定义表单列表\n  const {\n    data: customFormData = {},\n    isLoading: isLoadingCust008\n  } = useQueryCust008GetCustFormList({\n    teamId,\n    nodeId: nodeItem.nodeId,\n    enabled: !!isCust && !!nodeItem.nodeId\n  });\n\n  // 定义模式相关配置\n  const modeConfig = {\n    custom: {\n      fieldName: 'nodeId',\n      label: '选择表单',\n      getSelectedItem: id => {\n        var _customFormData$formL;\n        return customFormData === null || customFormData === void 0 ? void 0 : (_customFormData$formL = customFormData.formList) === null || _customFormData$formL === void 0 ? void 0 : _customFormData$formL.find(form => form.nodeId == id);\n      },\n      getItemList: () => {\n        var _customFormData$formL2;\n        return (customFormData === null || customFormData === void 0 ? void 0 : (_customFormData$formL2 = customFormData.formList) === null || _customFormData$formL2 === void 0 ? void 0 : _customFormData$formL2.map(form => ({\n          key: form.nodeId,\n          value: form.nodeId,\n          label: form.formName\n          // icon: form.subclassIcon\n        }))) || [];\n      },\n      getDefaultKey: list => list.length > 0 ? list[0].key : undefined,\n      getRevertKey: item => item === null || item === void 0 ? void 0 : item.nodeId,\n      objType: eNodeTypeId.nt_871_cust_objlist,\n      paramKey: 'processdefNodeId'\n    },\n    normal: {\n      fieldName: 'processdefNodeId',\n      label: '选择流程',\n      getSelectedItem: id => {\n        var _projectInfo$flowList, _projectInfo$flowList2, _projectInfo$flowList3;\n        return projectInfo === null || projectInfo === void 0 ? void 0 : (_projectInfo$flowList = projectInfo.flowList) === null || _projectInfo$flowList === void 0 ? void 0 : (_projectInfo$flowList2 = _projectInfo$flowList.filter) === null || _projectInfo$flowList2 === void 0 ? void 0 : (_projectInfo$flowList3 = _projectInfo$flowList2.call(_projectInfo$flowList, flow => flow.enableFlg == eEnableFlg.enable)) === null || _projectInfo$flowList3 === void 0 ? void 0 : _projectInfo$flowList3.find(flow => flow.processdefNodeId == id);\n      },\n      getItemList: () => {\n        const [flowList] = getFlowList((projectInfo === null || projectInfo === void 0 ? void 0 : projectInfo.flowList) || [], selectionList);\n        return flowList;\n      },\n      getDefaultKey: (list, defaultFlow) => defaultFlow === null || defaultFlow === void 0 ? void 0 : defaultFlow.key,\n      getRevertKey: item => item === null || item === void 0 ? void 0 : item.processdefNodeId,\n      objType: isOA ? eNodeTypeId.nt_671_oa_processList : eNodeTypeId.nt_571_insp_processList,\n      paramKey: 'processdefNodeId'\n    }\n  };\n\n  // 当前模式配置\n  const currentMode = useMemo(() => {\n    return isCust ? modeConfig.custom : modeConfig.normal;\n  }, [isCust, modeConfig]);\n  const processdefNodeId = Form.useWatch(currentMode.fieldName, form);\n  const selecedtProcess = useMemo(() => currentMode.getSelectedItem(processdefNodeId), [projectInfo, customFormData, processdefNodeId, currentMode]);\n\n  //（2）获取自定义表单字段\n  const {\n    subclassAttrList = [],\n    isFetching: isFetchingSetting409,\n    dataUpdatedAt: dataUpdatedAtSetting409\n  } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, selecedtProcess === null || selecedtProcess === void 0 ? void 0 : selecedtProcess.subclassId, !!(selecedtProcess !== null && selecedtProcess !== void 0 && selecedtProcess.subclassId));\n\n  // (2.1) 编辑时获取采集口径详情 TODO: useQuery cacheTime 需要在相关key变化时才会清除缓存数据，所以需要一个变量key去触发再次打开的更新操作\n  const {\n    attrList,\n    criteriaList = [],\n    bizNodeId,\n    objType,\n    createFlg,\n    dataUpdatedAt: dataUpdatedAtTrack019,\n    refetch: refetchGetPartitionDetail\n  } = useQueryTrack019GetPartitionDetail(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, modalKey, opType == eEditingMode.Modifying_1 && !!(nodeItem !== null && nodeItem !== void 0 && nodeItem.nodeId));\n  //（3）字典数据\n  const {\n    data: selectionList\n  } = useQuerySetting407_getCodeValueList(teamId);\n  //（4）获取人员列表\n  const {\n    data: userList\n  } = useQuerySetting202_getTeamAllUsers(teamId);\n\n  // 选择样式\n  const selectStyle = {\n    width: 300,\n    borderRadius: 5\n  };\n  const isLoading = isLoadingInsp091 || isFetchingSetting409 || !!isCust && isLoadingCust008 || uploadLoading;\n\n  // 监听打开新建/编辑采集口径弹窗\n  useEffect(() => {\n    globalEventBus.on(\"openCreatePartitionDrawerEvent\", openCreatePartitionDrawerEvent);\n    return () => globalEventBus.off(\"openCreatePartitionDrawerEvent\", openCreatePartitionDrawerEvent);\n  }, []);\n\n  // 打开创建项目弹窗事件\n  const openCreatePartitionDrawerEvent = (target, args) => {\n    setModalKey(toolUtil.guid());\n    setNodeItem(args);\n    setIsModalVisible(true);\n  };\n\n  // 打开弹窗/关闭弹窗\n  useEffect(() => {\n    if (isModalVisible && opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtSetting409) {\n      // 加载数据\n      loadIssuePartitionDetail();\n    } else {\n      // 初始化数据\n      initialIssuePartitionData();\n    }\n  }, [isModalVisible, dataUpdatedAtTrack019, dataUpdatedAtSetting409]);\n\n  // 打开搜索预览弹窗\n  const openSearchReultPreviewModal = requst => {\n    globalEventBus.emit(\"openSearchReultPreviewModalEvent\", \"\", {\n      searchPreRequest: requst,\n      onDisplayClick: onDisplayClick.bind(this)\n    });\n  };\n\n  // 自定义表单字段现在通过 useMemo 自动更新，不再需要 useEffect\n  // 使用 useMemo 缓存 attrNodeList，避免不必要的重新计算\n  const attrNodeList = useMemo(() => {\n    if (!(subclassAttrList !== null && subclassAttrList !== void 0 && subclassAttrList.length) || !userList || !selectionList) {\n      return [];\n    }\n    let attrList = transformAttrNodeListForUI(subclassAttrList, userList, selectionList, true);\n    return getQueryableAttrNodeList(attrList); //过滤59类型\n  }, [subclassAttrList, userList, selectionList]);\n\n  // 缓存默认的 checkedValues\n  const defaultCheckedValues = useMemo(() => {\n    if (!(subclassAttrList !== null && subclassAttrList !== void 0 && subclassAttrList.length) || !userList || !selectionList) {\n      return [];\n    }\n    let attrList = transformAttrNodeListForUI(subclassAttrList, userList, selectionList, true);\n    return attrList.filter(item => item.checked).map(item => item.nodeId);\n  }, [subclassAttrList, userList, selectionList]);\n\n  // 实际使用的 checkedValues：优先使用用户选择的，否则使用默认值\n  const checkedValues = userSelectedCheckedValues.length > 0 ? userSelectedCheckedValues : defaultCheckedValues;\n  // 编辑 自定义表单\n  useEffect(() => {\n    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && !isEmpty(attrNodeList)) {\n      changeCriteriaList();\n    }\n  }, [opType, dataUpdatedAtTrack019, attrNodeList]);\n\n  // 编辑 回显流程 注意有先后依赖关系不可合并\n  useEffect(() => {\n    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtInsp091) {\n      form.setFieldValue(currentMode.fieldName, bizNodeId);\n    }\n  }, [opType, dataUpdatedAtTrack019, dataUpdatedAtInsp091, JSON.stringify(currentMode)]);\n  useEffect(() => {\n    if (isModalVisible) {\n      const itemList = currentMode.getItemList();\n      setFlowList(itemList);\n      if (opType == eEditingMode.Creating_0) {\n        const defaultFlow = isCust ? null : getFlowList((projectInfo === null || projectInfo === void 0 ? void 0 : projectInfo.flowList) || [], selectionList)[1];\n        const defaultKey = currentMode.getDefaultKey(itemList, defaultFlow);\n        if (defaultKey) {\n          form.setFieldValue(currentMode.fieldName, defaultKey);\n        }\n      }\n    }\n  }, [isModalVisible, projectInfo, customFormData, isCust, JSON.stringify(currentMode)]);\n\n  // 编辑搜索数据\n  const changeCriteriaList = () => {\n    let _criteriaList = (criteriaList || []).filter(criteria => attrNodeList.some(subclass => criteria.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17\n    _criteriaList = transformCriteriaListForUI(_criteriaList, attrNodeList, selectionList);\n    setInitTableData(_criteriaList);\n  };\n\n  // 初始化数据\n  function initialIssuePartitionData() {\n    // console.log(\"正在清空数据...\")\n    setInitTableData([]); //清空高级搜索数据\n    setSelectFields([]); //清空字段属性数据\n    setUserSelectedCheckedValues([]); //清空用户选择的显示字段\n    setShowFormFields(true); //重置表单字段设置显示状态\n  }\n\n  // 加载采集口径详情\n  function loadIssuePartitionDetail() {\n    const isCreateEnabled = createFlg == eEnableFlg.enable;\n    form.setFieldsValue({\n      name: nodeItem.name,\n      // name回显\n      createFlg: isCreateEnabled\n    });\n\n    // 根据createFlg设置表单字段设置的显示状态\n    setShowFormFields(isCreateEnabled);\n    const _attrList = attrList.filter(attr => subclassAttrList.some(subclass => attr.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17\n    _attrList.forEach(attr => {\n      var _propertyList$find, _propertyList$find2;\n      const propertyList = subclassAttrList.find(subclass => attr.attrNid == subclass.nodeId).propertyList;\n      attr.attrModifyableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\n      attr.attrQueryableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\n      attr.uiControl = (_propertyList$find = propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control)) === null || _propertyList$find === void 0 ? void 0 : _propertyList$find.propValue;\n      attr.selectionId = ((_propertyList$find2 = propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)) === null || _propertyList$find2 === void 0 ? void 0 : _propertyList$find2.propValue) || \"\";\n    });\n    setSelectFields(_attrList);\n  }\n\n  // 默认值配置\n  const getDefAttrPropValuepByType = (propList = [], type) => {\n    return (propList.find(item => item.propType == type) || {\n      propValue: \"0\"\n    }).propValue;\n  };\n\n  // 取消\n  const handleCancel = () => {\n    return Modal.confirm({\n      title: \"提示\",\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: opType == eOpType.add ? '正在新建采集口径，是否放弃编辑?' : '正在编辑采集口径，是否放弃编辑?'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 16\n      }, this),\n      okText: \"确定\",\n      cancelText: \"取消\",\n      onOk: () => {\n        setIsModalVisible(false);\n      },\n      onCancel: () => {\n        console.log(\"Cancel\");\n      }\n    });\n  };\n\n  // 选择字段数据处理\n  const onSelectFields = items => {\n    let fieldList = [];\n    selectFields.filter(el => {\n      items.map((_el, index) => {\n        if (el.attrNid == _el.nodeId) {\n          fieldList.push(el);\n          delete items[index];\n        }\n      });\n    });\n    items.map(item => {\n      var _propertyList$find3, _propertyList$find4, _propertyList$find5;\n      let propertyList = item.propertyList;\n      const _attrModifyableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\n      const _attrQueryableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\n      let field = {\n        attrNid: item.nodeId,\n        defaultVal: null,\n        attrVisibleFlg: '',\n        attrModifyableFlg: _attrModifyableFlg,\n        attrModifyableFlgBack: _attrModifyableFlg,\n        attrQueryableFlg: _attrQueryableFlg,\n        attrQueryableFlgBack: _attrQueryableFlg,\n        uiControl: (_propertyList$find3 = propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control)) === null || _propertyList$find3 === void 0 ? void 0 : _propertyList$find3.propValue,\n        selectionId: ((_propertyList$find4 = propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)) === null || _propertyList$find4 === void 0 ? void 0 : _propertyList$find4.propValue) || \"\",\n        isRegion: ((_propertyList$find5 = propertyList.find(el => el.propType == eConsolePropId.Prop_28_is_region)) === null || _propertyList$find5 === void 0 ? void 0 : _propertyList$find5.propValue) || \"\" // 是否是检查区域字段\n      };\n      fieldList.push(field);\n    });\n    setSelectFields(fieldList);\n    setIsAddAttrModalVisible(false);\n  };\n\n  // + 动态条件\n  const handleOnAddSearchCode = () => {\n    tRef.current.addSearchCode();\n  };\n\n  // 搜索预览 无需校验搜索名称\n  const handleSearchPreClick = () => {\n    setSearchPreFlg(true);\n    form.submit();\n  };\n\n  // 显示字段\n  const onDisplayClick = e => {\n    setSearchDisplayFieldOpen(true);\n  };\n\n  // 显示字段数据处理\n  const assembleQueryAttrList = () => {\n    return checkedValues.map(checkedValue => ({\n      attrNid: checkedValue\n    }));\n  };\n\n  // 保存显示字段\n  const handleSearchDisplayFieldOnOk = (e, values) => {\n    setSearchDisplayFieldOpen(false);\n    setUserSelectedCheckedValues(values); // 更新用户选择的字段\n    globalEventBus.emit(\"changeSearchReultPreviewEvent\", \"\", {\n      values\n    }); // 根据显示字段过滤\n  };\n\n  // 取消保存字段\n  const handleSearchDisplayFieldOnCancel = (e, values) => {\n    if (!compareArr(checkedValues, values)) {\n      Modal.confirm({\n        title: '提醒',\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 15\n        }, this),\n        content: \"放弃后将不会保存显示字段，确定要放弃？\",\n        okText: '确定',\n        cancelText: '取消',\n        onOk: () => {\n          setSearchDisplayFieldOpen(false);\n        }\n      });\n    } else {\n      setSearchDisplayFieldOpen(false);\n    }\n  };\n\n  // 点击 确定\n  const handleSaveClick = () => {\n    setSearchPreFlg(false);\n    form.submit();\n  };\n\n  // 表单提交 form.submit()\n  const onFinish = async values => {\n    var _tRef$current, _tRef$current$getCrit;\n    const {\n      name\n    } = values;\n    if (!searchPreFlg && !name) {\n      // 校验名称\n      return globalUtil.warning(\"请填写采集口径名称!\");\n    }\n    let criteriaList = (tRef === null || tRef === void 0 ? void 0 : (_tRef$current = tRef.current) === null || _tRef$current === void 0 ? void 0 : (_tRef$current$getCrit = _tRef$current.getCriteriaListForBackend) === null || _tRef$current$getCrit === void 0 ? void 0 : _tRef$current$getCrit.call(_tRef$current)) || [];\n    // 搜索预览\n    if (searchPreFlg) {\n      const queryAttrList = assembleQueryAttrList();\n      let requst = {\n        \"teamId\": teamId,\n        \"bizNodeId\": values[currentMode.fieldName],\n        \"name\": name,\n        \"objType\": currentMode.objType,\n        \"advanceQueryFlg\": \"1\",\n        \"criteriaList\": criteriaList,\n        \"queryAttrList\": queryAttrList\n      };\n      // 搜索预览\n      requst = {\n        ...requst,\n        pageNum: 1\n      }; //默认查询第一页\n      return openSearchReultPreviewModal(requst);\n    }\n    setUploadLoading(true);\n    // TODO: 检查项后端校验存在问题,先固定传null by walt from jack  2024-02-05\n    selectFields.forEach((el, index) => {\n      if (el.isRegion == eRegionType.check) {\n        el.defaultVal = null;\n      }\n    });\n    //  后端只需要这些参数，去除多余的不接受的参数\n    let _selectFields = selectFields.map((el, index) => ({\n      attrNid: el.attrNid,\n      defaultVal: el.defaultVal,\n      attrVisibleFlg: el.attrVisibleFlg,\n      attrModifyableFlg: el.attrModifyableFlg,\n      attrModifyableFlgBack: el.attrModifyableFlgBack,\n      attrQueryableFlg: el.attrQueryableFlg,\n      attrQueryableFlgBack: el.attrQueryableFlgBack,\n      seqNo: index + 1\n    }));\n\n    // 基础参数\n    let params = {\n      teamId,\n      name,\n      attrList: _selectFields,\n      criteriaList,\n      createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable\n    };\n\n    // 根据操作类型添加不同的参数\n    if (opType) {\n      // 编辑模式\n      params.nodeId = nodeItem.nodeId;\n      params[currentMode.paramKey] = values[currentMode.fieldName];\n      await editPartition(params);\n    } else {\n      // 新建模式\n      params.objNodeId = nodeItem.nodeId;\n      params[currentMode.paramKey] = values[currentMode.fieldName];\n      await createPartition(params);\n    }\n    setUploadLoading(false);\n  };\n\n  // 新建采集口径\n  async function createPartition(params) {\n    await http.track_018_create_issue_partition(params).then(result => {\n      if (result.resultCode == 200) {\n        var _result$nodeTree$;\n        refreshSelectTeamMenu({\n          treeNode: (result === null || result === void 0 ? void 0 : result.nodeTree[0]) || {}\n        });\n        setIsModalVisible(false);\n        nodeItem.callback && nodeItem.callback((result === null || result === void 0 ? void 0 : (_result$nodeTree$ = result.nodeTree[0]) === null || _result$nodeTree$ === void 0 ? void 0 : _result$nodeTree$.children[0]) || {});\n      }\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n\n  // 编辑采集口径\n  async function editPartition(params) {\n    await http.track_020_modify_issue_partition(params).then(result => {\n      if (result.resultCode == 200) {\n        refreshTeamMenu();\n        setIsModalVisible(false);\n        refetchGetPartitionDetail(); // 编辑采集口径后刷新数据\n      }\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n  const handleOnChange = value => {\n    var _tRef$current2, _tRef$current3, _tRef$current3$getCri;\n    let criteriaList = (tRef === null || tRef === void 0 ? void 0 : (_tRef$current2 = tRef.current) === null || _tRef$current2 === void 0 ? void 0 : _tRef$current2.getCriteriaListForBackend) && (tRef === null || tRef === void 0 ? void 0 : (_tRef$current3 = tRef.current) === null || _tRef$current3 === void 0 ? void 0 : (_tRef$current3$getCri = _tRef$current3.getCriteriaListForBackend) === null || _tRef$current3$getCri === void 0 ? void 0 : _tRef$current3$getCri.call(_tRef$current3));\n    if (isEmpty(criteriaList) && isEmpty(selectFields)) {\n      return;\n    }\n    return Modal.confirm({\n      title: \"提示\",\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: `重新选择${isCust ? '表单' : '流程'}，下方两个表格将清空，您需要重新设置。是否继续?`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 16\n      }, this),\n      okText: \"是\",\n      cancelText: \"否\",\n      zIndex: 1002,\n      // FIXME:不能超过10000，否则会导致Select下拉框被覆盖\n      onOk: () => {},\n      onCancel: () => {\n        console.log(\"Cancel\");\n        form.setFieldValue(currentMode.fieldName, currentMode.getRevertKey(selecedtProcess));\n      }\n    });\n  };\n\n  // 处理\"可新建\"复选框变化\n  const handleCreateFlgChange = e => {\n    const checked = e.target.checked;\n\n    // 如果取消勾选且表单字段设置有值，弹出确认对话框\n    if (!checked && !isEmpty(selectFields)) {\n      Modal.confirm({\n        title: \"提示\",\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 15\n        }, this),\n        content: \"取消可新建功能，则清除表单字段设置，是否取消？\",\n        okText: \"取消\",\n        cancelText: \"不取消\",\n        zIndex: 1002,\n        onOk: () => {\n          // 用户确认取消，清除表单字段设置并隐藏\n          setSelectFields([]);\n          setShowFormFields(false);\n          form.setFieldValue('createFlg', false);\n        },\n        onCancel: () => {\n          // 用户选择不取消，恢复勾选状态\n          form.setFieldValue('createFlg', true);\n        }\n      });\n    } else if (checked) {\n      // 如果重新勾选，显示表单字段设置\n      setShowFormFields(true);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n    className: \"tms-drawer IssuePartition\",\n    width: \"60%\",\n    title: opType ? `采集口径设置` : `新建采集口径`,\n    destroyOnClose: true,\n    open: isModalVisible,\n    onClose: handleCancel,\n    footer: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"right\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: 20,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            borderRadius: 5\n          },\n          onClick: handleCancel,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          style: {\n            borderRadius: 5\n          },\n          onClick: handleSaveClick,\n          children: \"\\u63D0\\u4EA4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 13\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(UploadLoading, {\n      spinning: isLoading,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        labelCol: {\n          span: 4\n        },\n        wrapperCol: {\n          span: 19\n        },\n        onFinish: onFinish,\n        preserve: false // Modal关闭后销毁form字段数据\n        ,\n        autoComplete: \"off\" // 取消自动补充功能\n        ,\n        initialValues: {\n          createFlg: true\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"采集口径名称\",\n          name: \"name\",\n          required: true,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            style: selectStyle,\n            autoComplete: \"off\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: currentMode.label,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            size: 20,\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: currentMode.fieldName,\n              required: true,\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                showSearch: true,\n                style: {\n                  width: 300,\n                  borderRadius: 3\n                }\n                //select选择框搜索\n                ,\n                filterOption: (input, option) => {\n                  return (option.children.props.children[1].props.children || \"\").toLowerCase().indexOf(input.toLowerCase()) >= 0;\n                },\n                onChange: handleOnChange,\n                children: flowList.map(flow => /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: flow.value,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    },\n                    children: [flow.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        paddingLeft: 5\n                      },\n                      children: flow.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 21\n                  }, this)\n                }, flow.key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"createFlg\",\n              valuePropName: \"checked\",\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                onChange: handleCreateFlgChange,\n                children: \"\\u53EF\\u65B0\\u5EFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"自定义搜索\",\n          children: isEmpty(attrNodeList) ? /*#__PURE__*/_jsxDEV(TEmpty, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 36\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(SearchEditTable, {\n              attrNodeList: attrNodeList,\n              exprs: [],\n              criteriaList: initTableData,\n              selectionList: selectionList,\n              queryType: 0,\n              ref: tRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-edit-btns\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {\n                  className: \"color-yellow\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this),\n                onClick: handleOnAddSearchCode,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"+ \\u52A8\\u6001\\u6761\\u4EF6\\uFF08\", /*#__PURE__*/_jsxDEV(FunctionOutlined, {\n                    style: {\n                      margin: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 32\n                  }, this), \"\\uFF09\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 31\n                }, this),\n                className: \"defaultBtn_light\",\n                onClick: handleSearchPreClick,\n                children: \"\\u7ED3\\u679C\\u9884\\u89C8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remarks\",\n              children: [\"\\u5907\\u6CE81\\uFF1A\\u6B64\\u5904\\u7684\\u81EA\\u5B9A\\u4E49\\u641C\\u7D22\\uFF0C\\u7528\\u4E8E\\u663E\\u793A\\u5F53\\u524D\\u91C7\\u96C6\\u53E3\\u5F84\\u5BF9\\u5E94\\u7684\", (_projectInfo$issueAli = projectInfo.issueAlias) !== null && _projectInfo$issueAli !== void 0 ? _projectInfo$issueAli : \"\", \"\\u5217\\u8868\\u3002\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remarks\",\n              children: \"\\u5907\\u6CE82\\uFF1A\\u70B9\\u51FB\\u786E\\u5B9A\\u6309\\u94AE\\u4EC5\\u4FDD\\u5B58\\u8868\\u5355(\\u5373\\u6761\\u4EF6)\\uFF0C\\u9884\\u89C8\\u7ED3\\u679C\\u4E0D\\u505A\\u4FDD\\u5B58\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 9\n        }, this), showFormFields && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"表单字段设置\",\n          children: [/*#__PURE__*/_jsxDEV(CustomerFormTable, {\n            selectFields: selectFields,\n            setSelectFields: setSelectFields,\n            selectionList: selectionList,\n            userList: userList,\n            subclassAttrList: subclassAttrList,\n            selecedtProcess: selecedtProcess\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            className: \"fontsize-12\",\n            onClick: () => setIsAddAttrModalVisible(true),\n            children: \"+ \\u6DFB\\u52A0\\u5B57\\u6BB5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"remarks\",\n            children: [\"\\u5907\\u6CE81\\uFF1A\\u5728\", (_projectInfo$issueAli2 = projectInfo.issueAlias) !== null && _projectInfo$issueAli2 !== void 0 ? _projectInfo$issueAli2 : \"\", \"\\u9879\\u76EE\\\"\", projectInfo.name, \"\\\"\\u7684\\u81EA\\u5B9A\\u4E49\\u5B57\\u6BB5\\u7684\\u57FA\\u7840\\u4E0A\\uFF0C\\u8FDB\\u4E00\\u6B65\\u9650\\u7F29\\u4E0A\\u9762\\u8868\\u683C\\u4E2D\\u5B57\\u6BB5\\u7684\\u662F\\u5426\\u663E\\u793A/\\u53EF\\u4FEE\\u6539/\\u53EF\\u641C\\u7D22\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"remarks\",\n            children: [\"\\u5907\\u6CE82\\uFF1A\\u5728\\u5F53\\u524D\\u91C7\\u96C6\\u53E3\\u5F84\\u4E2D\\u63D0\\u4EA4\\u7684\\u65B0\\u5EFA\", (_projectInfo$issueAli3 = projectInfo.issueAlias) !== null && _projectInfo$issueAli3 !== void 0 ? _projectInfo$issueAli3 : \"\", \"\\uFF0C\\u5176\\u5BF9\\u5E94\\u4E0A\\u9762\\u8868\\u683C\\u4E2D\\u7684\\u5B57\\u6BB5\\uFF0C\\u5982\\u679C\\u503C\\u4E3A\\u7A7A\\uFF0C\\u5219\\u4F7F\\u7528\\\"\\u7F3A\\u7701\\u503C\\\"\\u5217\\u4E2D\\u7684\\u503C\\uFF0C\\u8FDB\\u884C\\u6700\\u540E\\u7684\\u6570\\u636E\\u5B58\\u50A8\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(AddPartitionAttr, {\n      selectFields: selectFields,\n      attrList: subclassAttrList,\n      visible: isAddAttrModalVisible,\n      onSelectFields: onSelectFields,\n      onCancel: () => setIsAddAttrModalVisible(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(SearchReultPreviewModal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(SearchDisplayField, {\n      open: searchDisplayFieldOpen,\n      onOk: handleSearchDisplayFieldOnOk,\n      onCancel: handleSearchDisplayFieldOnCancel,\n      nodeName: nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeName,\n      checkedValues: checkedValues,\n      attrNodeList: attrNodeList\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 580,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 488,\n    columnNumber: 10\n  }, this);\n}\n_s(CreatePartitionDrawer, \"+2w0FSqkKI4cjDd6WxEnCjc0MNI=\", false, function () {\n  return [useParams, Form.useForm, useGetInspectRelatedNodes, useQuery, useQueryCust008GetCustFormList, Form.useWatch, useQuerySetting409_getTeamAttrgrpProps, useQueryTrack019GetPartitionDetail, useQuerySetting407_getCodeValueList, useQuerySetting202_getTeamAllUsers];\n});\n_c = CreatePartitionDrawer;\nvar _c;\n$RefreshReg$(_c, \"CreatePartitionDrawer\");", "map": {"version": 3, "names": ["ExclamationCircleOutlined", "FunctionOutlined", "QuestionCircleOutlined", "SearchOutlined", "http", "useQuerySetting202_getTeamAllUsers", "useQuerySetting407_getCodeValueList", "compareArr", "isEmpty", "eConsolePropId", "eEditingMode", "eEnableFlg", "eOpType", "eSelectionListId", "globalEventBus", "globalUtil", "getQueryableAttrNodeList", "transformAttrNodeListForUI", "transformCriteriaListForUI", "getFlowList", "toolUtil", "eNodeTypeId", "refreshTeamMenu", "refreshSelectTeamMenu", "inspCreatePartitionNodeTypeList", "useQuery", "<PERSON><PERSON>", "Drawer", "Form", "Input", "Modal", "Space", "Checkbox", "Select", "useEffect", "useRef", "useState", "useMemo", "useParams", "SearchDisplayField", "SearchEditTable", "SearchReultPreviewModal", "useQueryTrack019GetPartitionDetail", "useQuerySetting409_getTeamAttrgrpProps", "AddPartitionAttr", "CustomerFormTable", "eRegionType", "UploadLoading", "TEmpty", "insp_091_get_insp_project_detail_query", "useGetInspectRelatedNodes", "useQueryCust008GetCustFormList", "useQueryCust007GetCustObjList", "DraggableDrawer", "jsxDEV", "_jsxDEV", "CreatePartitionDrawer", "_s", "_projectInfo$issueAli", "_projectInfo$issueAli2", "_projectInfo$issueAli3", "teamId", "form", "useForm", "tRef", "Object", "create", "isModalVisible", "setIsModalVisible", "searchPreFlg", "setSearchPreFlg", "searchDisplayFieldOpen", "setSearchDisplayFieldOpen", "isAddAttrModalVisible", "setIsAddAttrModalVisible", "nodeItem", "setNodeItem", "selectFields", "setSelectFields", "initTableData", "setInitTableData", "userSelectedCheckedValues", "setUserSelectedCheckedValues", "modalKey", "setModalKey", "guid", "flowList", "setFlowList", "uploadLoading", "setUploadLoading", "showFormFields", "<PERSON>S<PERSON><PERSON><PERSON><PERSON><PERSON>s", "opType", "includes", "nodeType", "Modifying_1", "Creating_0", "data", "isCust", "isOA", "isLoading", "isLoadingInsp072", "nodeId", "enabled", "projectInfo", "isLoadingInsp091", "dataUpdatedAt", "dataUpdatedAtInsp091", "customFormData", "isLoadingCust008", "modeConfig", "custom", "fieldName", "label", "getSelectedItem", "id", "_customFormData$formL", "formList", "find", "getItemList", "_customFormData$formL2", "map", "key", "value", "formName", "getDefaultKey", "list", "length", "undefined", "getRevertKey", "item", "objType", "nt_871_cust_objlist", "<PERSON><PERSON><PERSON><PERSON>", "normal", "_projectInfo$flowList", "_projectInfo$flowList2", "_projectInfo$flowList3", "filter", "call", "flow", "enableFlg", "enable", "processdefNodeId", "selectionList", "defaultFlow", "nt_671_oa_processList", "nt_571_insp_processList", "currentMode", "useWatch", "selecedtProcess", "subclassAttrList", "isFetching", "isFetchingSetting409", "dataUpdatedAtSetting409", "subclassId", "attrList", "criteriaList", "bizNodeId", "createFlg", "dataUpdatedAtTrack019", "refetch", "refetchGetPartitionDetail", "userList", "selectStyle", "width", "borderRadius", "on", "openCreatePartitionDrawerEvent", "off", "target", "args", "loadIssuePartitionDetail", "initialIssuePartitionData", "openSearchReultPreviewModal", "requst", "emit", "searchPreRequest", "onDisplayClick", "bind", "attrNodeList", "defaultCheckedValues", "checked", "checkedValues", "changeCriteriaList", "setFieldValue", "JSON", "stringify", "itemList", "defaultKey", "_criteriaList", "criteria", "some", "subclass", "attrNid", "isCreateEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "_attrList", "attr", "for<PERSON>ach", "_propertyList$find", "_propertyList$find2", "propertyList", "attrModifyableFlgBack", "getDefAttrPropValuepByType", "Prop_60_modifiable", "attrQueryableFlgBack", "Prop_59_queryable", "uiControl", "el", "propType", "Prop_14_ui_control", "propValue", "selectionId", "_el", "Prop_12_selection", "propList", "type", "handleCancel", "confirm", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "children", "add", "okText", "cancelText", "onOk", "onCancel", "console", "log", "onSelectFields", "items", "fieldList", "index", "push", "_propertyList$find3", "_propertyList$find4", "_propertyList$find5", "_attrModifyableFlg", "_attrQueryableFlg", "field", "defaultVal", "attrVisibleFlg", "attrModifyableFlg", "attrQueryableFlg", "isRegion", "Prop_28_is_region", "handleOnAddSearchCode", "current", "addSearchCode", "handleSearchPreClick", "submit", "e", "assembleQueryAttrList", "checkedValue", "handleSearchDisplayFieldOnOk", "values", "handleSearchDisplayFieldOnCancel", "handleSaveClick", "onFinish", "_tRef$current", "_tRef$current$getCrit", "warning", "getCriteriaListForBackend", "queryAttrList", "pageNum", "check", "_selectFields", "seqNo", "params", "disable", "editPartition", "objNodeId", "createPartition", "track_018_create_issue_partition", "then", "result", "resultCode", "_result$nodeTree$", "treeNode", "nodeTree", "callback", "catch", "err", "track_020_modify_issue_partition", "handleOnChange", "_tRef$current2", "_tRef$current3", "_tRef$current3$getCri", "zIndex", "handleCreateFlgChange", "className", "destroyOnClose", "open", "onClose", "footer", "style", "textAlign", "size", "onClick", "spinning", "labelCol", "span", "wrapperCol", "preserve", "autoComplete", "initialValues", "<PERSON><PERSON>", "required", "noStyle", "showSearch", "filterOption", "input", "option", "props", "toLowerCase", "indexOf", "onChange", "Option", "display", "alignItems", "paddingLeft", "valuePropName", "exprs", "queryType", "ref", "margin", "issueAlias", "visible", "nodeName", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/inspect/views/InspectProject/CreatePartitionDrawer/CreatePartitionDrawer.jsx"], "sourcesContent": ["/*\r\n * @Author: <PERSON> <EMAIL>\r\n * @Date: 2024-02-02 17:53:39\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2025-08-05 16:48:31\r\n * @Description: 采集口径\r\n */\r\n/* eslint-disable react-hooks/exhaustive-deps */\r\nimport { ExclamationCircleOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';\r\nimport * as http from \"@common/api/http\";\r\nimport { useQuerySetting202_getTeamAllUsers, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\r\nimport { compareArr, isEmpty } from '@common/utils/ArrayUtils';\r\nimport { eConsolePropId, eEditingMode, eEnableFlg, eOpType, eSelectionListId, } from \"@common/utils/enum\";\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { getQueryableAttrNodeList, transformAttrNodeListForUI, transformCriteriaListForUI, getFlowList } from '@common/utils/logicUtils';\r\nimport * as toolUtil from \"@common/utils/toolUtil\";\r\nimport { eNodeTypeId, refreshTeamMenu, refreshSelectTeamMenu, inspCreatePartitionNodeTypeList } from \"@common/utils/TsbConfig\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { Button, Drawer, Form, Input, Modal, Space, Checkbox, Select } from \"antd\";\r\nimport { useEffect, useRef, useState, useMemo } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport SearchDisplayField from 'src/quickAcess/views/Search/SearchDetail/SearchDisplayField';\r\nimport SearchEditTable from \"src/quickAcess/views/Search/SearchDetail/SearchEditTable\";\r\nimport SearchReultPreviewModal from 'src/quickAcess/views/Search/SearchDetail/SearchReultPreviewModal';\r\nimport { useQueryTrack019GetPartitionDetail, useQuerySetting409_getTeamAttrgrpProps } from \"src/issueTrack/service/issueHooks\";\r\nimport AddPartitionAttr from \"./AddPartitionAttr\";\r\nimport CustomerFormTable from \"./CustomerFormTable\";\r\nimport { eRegionType } from \"src/inspect/utils/enum\";\r\nimport UploadLoading from \"@components/UploadLoading\";\r\nimport TEmpty from '@components/TEmpty';\r\nimport \"./CreatePartitionDrawer.scss\";\r\nimport {insp_091_get_insp_project_detail_query} from \"@common/api/query/inspect/query_insp_01_mgmt\";\r\nimport { useGetInspectRelatedNodes, useQueryCust008GetCustFormList, useQueryCust007GetCustObjList } from \"src/inspect/service/inspectHooks\";\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\n\r\n\r\n// 新建&编辑 采集口径\r\nexport default function CreatePartitionDrawer() {\r\n  const { teamId } = useParams();\r\n  const [form] = Form.useForm();\r\n  const tRef = useRef(Object.create(null));\r\n  // 弹窗显示配置\r\n  const [isModalVisible, setIsModalVisible] = useState(false); //采集口径弹窗\r\n  const [searchPreFlg, setSearchPreFlg] = useState(false); //是否搜索预览\r\n  const [searchDisplayFieldOpen, setSearchDisplayFieldOpen] = useState(false); //显示字段弹窗\r\n  const [isAddAttrModalVisible, setIsAddAttrModalVisible] = useState(false); //添加字段弹窗\r\n  // 数据配置\r\n  const [nodeItem, setNodeItem] = useState({}); //节点信息\r\n  // const [opType, setOpType] = useState(eEditingMode.Creating_0); //0:新建 1：编辑\r\n  const [selectFields, setSelectFields] = useState([]); //已选择的表单字段\r\n  const [initTableData, setInitTableData] = useState([]); // 初始数据\r\n  // attrNodeList 和 checkedValues 现在通过 useMemo 计算，不再需要 useState\r\n  const [userSelectedCheckedValues, setUserSelectedCheckedValues] = useState([]); // 用户手动选择的显示字段\r\n  const [modalKey, setModalKey] = useState(toolUtil.guid()); // 显示字段勾选\r\n\r\n  const [flowList, setFlowList] = useState([]);                 // 流程图列表\r\n  const [uploadLoading, setUploadLoading] = useState(false); // 提交加载中\r\n  const [showFormFields, setShowFormFields] = useState(true); // 控制表单字段设置的显示\r\n\r\n  const opType = useMemo(()=>{\r\n    return inspCreatePartitionNodeTypeList.includes(nodeItem.nodeType) ? eEditingMode.Modifying_1 : eEditingMode.Creating_0  // 如果nodeType为分区节点，则为编辑采集口径\r\n  },[nodeItem])\r\n\r\n  const { data: { isCust, isOA }, isLoading: isLoadingInsp072 } = useGetInspectRelatedNodes({teamId, nodeId: nodeItem.nodeId, enabled: !!nodeItem.nodeId,}) \r\n\r\n  // 接口调用\r\n  //（1）获取项目信息和可发起流程\r\n  const { data: projectInfo = {}, isLoading: isLoadingInsp091, dataUpdatedAt: dataUpdatedAtInsp091 } = useQuery(insp_091_get_insp_project_detail_query(teamId, nodeItem?.nodeId, !!nodeItem?.nodeId))\r\n  \r\n  // 获取自定义表单列表\r\n  const { data: customFormData = {}, isLoading: isLoadingCust008 } = useQueryCust008GetCustFormList({teamId, nodeId: nodeItem.nodeId, enabled: !!isCust && !!nodeItem.nodeId});\r\n  \r\n  // 定义模式相关配置\r\n  const modeConfig = {\r\n    custom: {\r\n      fieldName: 'nodeId',\r\n      label: '选择表单',\r\n      getSelectedItem: (id) => customFormData?.formList?.find(form => form.nodeId == id),\r\n      getItemList: () => customFormData?.formList?.map(form => ({\r\n        key: form.nodeId,\r\n        value: form.nodeId,\r\n        label: form.formName,\r\n        // icon: form.subclassIcon\r\n      })) || [],\r\n      getDefaultKey: (list) => list.length > 0 ? list[0].key : undefined,\r\n      getRevertKey: (item) => item?.nodeId,\r\n      objType: eNodeTypeId.nt_871_cust_objlist,\r\n      paramKey: 'processdefNodeId'\r\n    },\r\n    normal: {\r\n      fieldName: 'processdefNodeId',\r\n      label: '选择流程',\r\n      getSelectedItem: (id) => projectInfo?.flowList?.filter?.(flow => flow.enableFlg == eEnableFlg.enable)?.find(flow => flow.processdefNodeId == id),\r\n      getItemList: () => {\r\n        const [flowList] = getFlowList(projectInfo?.flowList || [], selectionList);\r\n        return flowList;\r\n      },\r\n      getDefaultKey: (list, defaultFlow) => defaultFlow?.key,\r\n      getRevertKey: (item) => item?.processdefNodeId,\r\n      objType: isOA ? eNodeTypeId.nt_671_oa_processList : eNodeTypeId.nt_571_insp_processList, \r\n      paramKey: 'processdefNodeId'\r\n    }\r\n  };\r\n  \r\n  // 当前模式配置\r\n  const currentMode = useMemo(()=>{\r\n    return isCust ? modeConfig.custom : modeConfig.normal\r\n  },[isCust, modeConfig]);\r\n\r\n  const processdefNodeId = Form.useWatch(currentMode.fieldName, form);\r\n  const selecedtProcess = useMemo(() => \r\n    currentMode.getSelectedItem(processdefNodeId)\r\n  ,[projectInfo, customFormData, processdefNodeId, currentMode]) \r\n  \r\n  //（2）获取自定义表单字段\r\n  const { subclassAttrList = [], isFetching: isFetchingSetting409, dataUpdatedAt: dataUpdatedAtSetting409 } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeItem?.nodeId, selecedtProcess?.subclassId, !!selecedtProcess?.subclassId);\r\n\r\n  // (2.1) 编辑时获取采集口径详情 TODO: useQuery cacheTime 需要在相关key变化时才会清除缓存数据，所以需要一个变量key去触发再次打开的更新操作\r\n  const { attrList, criteriaList=[], bizNodeId, objType, createFlg, dataUpdatedAt: dataUpdatedAtTrack019, refetch: refetchGetPartitionDetail } = useQueryTrack019GetPartitionDetail(teamId, nodeItem?.nodeId, modalKey, opType == eEditingMode.Modifying_1 && !!nodeItem?.nodeId);\r\n  //（3）字典数据\r\n  const { data: selectionList } = useQuerySetting407_getCodeValueList(teamId);\r\n  //（4）获取人员列表\r\n  const { data: userList } = useQuerySetting202_getTeamAllUsers(teamId);\r\n\r\n  // 选择样式\r\n  const selectStyle = { width: 300, borderRadius: 5 };\r\n\r\n  const isLoading  = isLoadingInsp091 || isFetchingSetting409 || (!!isCust && isLoadingCust008 ) || uploadLoading\r\n\r\n  // 监听打开新建/编辑采集口径弹窗\r\n  useEffect(() => {\r\n    globalEventBus.on(\"openCreatePartitionDrawerEvent\", openCreatePartitionDrawerEvent)\r\n    return () => globalEventBus.off(\"openCreatePartitionDrawerEvent\", openCreatePartitionDrawerEvent)\r\n  }, [])\r\n\r\n  // 打开创建项目弹窗事件\r\n  const openCreatePartitionDrawerEvent = (target, args) => {\r\n    setModalKey(toolUtil.guid());\r\n    setNodeItem(args);\r\n    setIsModalVisible(true);\r\n  }\r\n\r\n  // 打开弹窗/关闭弹窗\r\n  useEffect(() => {\r\n    if (isModalVisible && opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtSetting409) {\r\n      // 加载数据\r\n      loadIssuePartitionDetail()\r\n    } else {\r\n      // 初始化数据\r\n      initialIssuePartitionData()\r\n    }\r\n  }, [isModalVisible, dataUpdatedAtTrack019, dataUpdatedAtSetting409])\r\n\r\n  // 打开搜索预览弹窗\r\n  const openSearchReultPreviewModal = (requst) => {\r\n    globalEventBus.emit(\"openSearchReultPreviewModalEvent\", \"\",\r\n      { searchPreRequest: requst, onDisplayClick: onDisplayClick.bind(this) });\r\n  }\r\n\r\n  // 自定义表单字段现在通过 useMemo 自动更新，不再需要 useEffect\r\n  // 使用 useMemo 缓存 attrNodeList，避免不必要的重新计算\r\n  const attrNodeList = useMemo(() => {\r\n    if (!subclassAttrList?.length || !userList || !selectionList) {\r\n      return [];\r\n    }\r\n    let attrList = transformAttrNodeListForUI(subclassAttrList, userList, selectionList, true);\r\n    return getQueryableAttrNodeList(attrList); //过滤59类型\r\n  }, [subclassAttrList, userList, selectionList]);\r\n\r\n  // 缓存默认的 checkedValues\r\n  const defaultCheckedValues = useMemo(() => {\r\n    if (!subclassAttrList?.length || !userList || !selectionList) {\r\n      return [];\r\n    }\r\n    let attrList = transformAttrNodeListForUI(subclassAttrList, userList, selectionList, true);\r\n    return attrList.filter(item => item.checked).map(item => item.nodeId);\r\n  }, [subclassAttrList, userList, selectionList]);\r\n\r\n  // 实际使用的 checkedValues：优先使用用户选择的，否则使用默认值\r\n  const checkedValues = userSelectedCheckedValues.length > 0 ? userSelectedCheckedValues : defaultCheckedValues;\r\n  // 编辑 自定义表单\r\n  useEffect(() => {\r\n    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && !isEmpty(attrNodeList)) {\r\n      changeCriteriaList()\r\n    }\r\n  }, [opType, dataUpdatedAtTrack019, attrNodeList ])\r\n\r\n  // 编辑 回显流程 注意有先后依赖关系不可合并\r\n  useEffect(() => {\r\n    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtInsp091) {\r\n      form.setFieldValue(currentMode.fieldName, bizNodeId)\r\n    }\r\n  }, [opType, dataUpdatedAtTrack019, dataUpdatedAtInsp091, JSON.stringify(currentMode)])\r\n\r\n  useEffect(() => {\r\n    if (isModalVisible) {\r\n      const itemList = currentMode.getItemList();\r\n      setFlowList(itemList);\r\n      \r\n      if (opType == eEditingMode.Creating_0) {\r\n        const defaultFlow = isCust ? null : getFlowList(projectInfo?.flowList || [], selectionList)[1];\r\n        const defaultKey = currentMode.getDefaultKey(itemList, defaultFlow);\r\n        if (defaultKey) {\r\n          form.setFieldValue(currentMode.fieldName, defaultKey);\r\n        }\r\n      }\r\n    }\r\n  }, [isModalVisible, projectInfo, customFormData, isCust, JSON.stringify(currentMode)]);\r\n\r\n  // 编辑搜索数据\r\n  const changeCriteriaList = () => {\r\n    let _criteriaList = (criteriaList || []).filter(criteria => attrNodeList.some(subclass => criteria.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17\r\n     _criteriaList = transformCriteriaListForUI(_criteriaList, attrNodeList, selectionList);\r\n    setInitTableData(_criteriaList);\r\n  }\r\n\r\n  // 初始化数据\r\n  function initialIssuePartitionData() {\r\n    // console.log(\"正在清空数据...\")\r\n    setInitTableData([]); //清空高级搜索数据\r\n    setSelectFields([]); //清空字段属性数据\r\n    setUserSelectedCheckedValues([]); //清空用户选择的显示字段\r\n    setShowFormFields(true); //重置表单字段设置显示状态\r\n  }\r\n\r\n  // 加载采集口径详情\r\n  function loadIssuePartitionDetail() {\r\n    const isCreateEnabled = createFlg == eEnableFlg.enable;\r\n    form.setFieldsValue({\r\n      name: nodeItem.name, // name回显\r\n      createFlg: isCreateEnabled\r\n    });\r\n\r\n    // 根据createFlg设置表单字段设置的显示状态\r\n    setShowFormFields(isCreateEnabled);\r\n\r\n    const _attrList = attrList.filter(attr => subclassAttrList.some(subclass => attr.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17\r\n    _attrList.forEach((attr) => {\r\n      const propertyList = subclassAttrList.find(subclass => attr.attrNid == subclass.nodeId).propertyList;\r\n      attr.attrModifyableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\r\n      attr.attrQueryableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\r\n      attr.uiControl = (propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue;\r\n      attr.selectionId = propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)?.propValue || \"\";\r\n    });\r\n    setSelectFields(_attrList)\r\n  }\r\n\r\n  // 默认值配置\r\n  const getDefAttrPropValuepByType = (propList = [], type) => {\r\n    return (propList.find((item) => item.propType == type) || { propValue: \"0\" }).propValue;\r\n  }\r\n\r\n  // 取消\r\n  const handleCancel = () => {\r\n   return Modal.confirm({\r\n      title: \"提示\",\r\n      icon: <ExclamationCircleOutlined />,\r\n      content: <p>{ opType == eOpType.add ? '正在新建采集口径，是否放弃编辑?' : '正在编辑采集口径，是否放弃编辑?'}</p>,\r\n      okText: \"确定\",\r\n      cancelText: \"取消\",\r\n      onOk: () => {\r\n         setIsModalVisible(false)\r\n      },\r\n      onCancel: () => {\r\n        console.log(\"Cancel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  // 选择字段数据处理\r\n  const onSelectFields = (items) => {\r\n    let fieldList = []\r\n    selectFields.filter(el => {\r\n      items.map((_el, index) => {\r\n        if (el.attrNid == _el.nodeId) {\r\n          fieldList.push(el)\r\n          delete items[index]\r\n        }\r\n      })\r\n    })\r\n    items.map((item) => {\r\n      let propertyList = item.propertyList\r\n      const _attrModifyableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\r\n      const _attrQueryableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\r\n      let field = {\r\n        attrNid: item.nodeId,\r\n        defaultVal: null,\r\n        attrVisibleFlg: '',\r\n        attrModifyableFlg: _attrModifyableFlg,\r\n        attrModifyableFlgBack: _attrModifyableFlg,\r\n        attrQueryableFlg: _attrQueryableFlg,\r\n        attrQueryableFlgBack: _attrQueryableFlg,\r\n        uiControl: (propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue,\r\n        selectionId: propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)?.propValue || \"\",\r\n        isRegion: propertyList.find(el => el.propType == eConsolePropId.Prop_28_is_region)?.propValue || \"\",      // 是否是检查区域字段\r\n      }\r\n      fieldList.push(field)\r\n    })\r\n    setSelectFields(fieldList)\r\n    setIsAddAttrModalVisible(false)\r\n  }\r\n\r\n  // + 动态条件\r\n  const handleOnAddSearchCode = () => {\r\n    tRef.current.addSearchCode();\r\n  }\r\n\r\n  // 搜索预览 无需校验搜索名称\r\n  const handleSearchPreClick = () => {\r\n    setSearchPreFlg(true);\r\n    form.submit();\r\n  }\r\n\r\n  // 显示字段\r\n  const onDisplayClick = (e) => {\r\n    setSearchDisplayFieldOpen(true);\r\n  };\r\n\r\n  // 显示字段数据处理\r\n  const assembleQueryAttrList = () => {\r\n    return checkedValues.map(checkedValue => ({ attrNid: checkedValue }));\r\n  }\r\n\r\n  // 保存显示字段\r\n  const handleSearchDisplayFieldOnOk = (e, values) => {\r\n    setSearchDisplayFieldOpen(false);\r\n    setUserSelectedCheckedValues(values); // 更新用户选择的字段\r\n    globalEventBus.emit(\"changeSearchReultPreviewEvent\", \"\", { values }) // 根据显示字段过滤\r\n  }\r\n\r\n  // 取消保存字段\r\n  const handleSearchDisplayFieldOnCancel = (e, values) => {\r\n    if (!compareArr(checkedValues, values)) {\r\n      Modal.confirm({\r\n        title: '提醒',\r\n        icon: <ExclamationCircleOutlined />,\r\n        content: \"放弃后将不会保存显示字段，确定要放弃？\",\r\n        okText: '确定',\r\n        cancelText: '取消',\r\n        onOk: () => { setSearchDisplayFieldOpen(false); }\r\n      });\r\n    } else {\r\n      setSearchDisplayFieldOpen(false);\r\n    }\r\n  };\r\n\r\n  // 点击 确定\r\n  const handleSaveClick = () => {\r\n    setSearchPreFlg(false);\r\n    form.submit();\r\n  }\r\n\r\n  // 表单提交 form.submit()\r\n  const onFinish = async (values) => {\r\n    const { name } = values;\r\n    if (!searchPreFlg && !name) {\r\n      // 校验名称\r\n      return globalUtil.warning(\"请填写采集口径名称!\")\r\n    }\r\n    let criteriaList = tRef?.current?.getCriteriaListForBackend?.() || []\r\n    // 搜索预览\r\n    if (searchPreFlg) {\r\n      const queryAttrList = assembleQueryAttrList();\r\n      let requst = {\r\n        \"teamId\": teamId, \"bizNodeId\": values[currentMode.fieldName], \"name\": name, \"objType\": currentMode.objType,\r\n        \"advanceQueryFlg\": \"1\", \"criteriaList\": criteriaList, \"queryAttrList\": queryAttrList\r\n      }\r\n      // 搜索预览\r\n      requst = { ...requst, pageNum: 1 } //默认查询第一页\r\n      return openSearchReultPreviewModal(requst);\r\n    }\r\n    setUploadLoading(true);\r\n    // TODO: 检查项后端校验存在问题,先固定传null by walt from jack  2024-02-05\r\n    selectFields.forEach((el, index) => {\r\n      if (el.isRegion == eRegionType.check) {\r\n        el.defaultVal = null;\r\n      }\r\n    });\r\n    //  后端只需要这些参数，去除多余的不接受的参数\r\n    let _selectFields = selectFields.map((el, index) => ({\r\n      attrNid: el.attrNid,\r\n      defaultVal: el.defaultVal,\r\n      attrVisibleFlg: el.attrVisibleFlg,\r\n      attrModifyableFlg: el.attrModifyableFlg,\r\n      attrModifyableFlgBack: el.attrModifyableFlgBack,\r\n      attrQueryableFlg: el.attrQueryableFlg,\r\n      attrQueryableFlgBack: el.attrQueryableFlgBack,\r\n      seqNo: index + 1,\r\n    }))\r\n    \r\n    // 基础参数\r\n    let params = { teamId, name, attrList: _selectFields, criteriaList, createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable };\r\n    \r\n    // 根据操作类型添加不同的参数\r\n    if (opType) {\r\n      // 编辑模式\r\n      params.nodeId = nodeItem.nodeId;\r\n      params[currentMode.paramKey] = values[currentMode.fieldName];\r\n      await editPartition(params);\r\n    } else {\r\n      // 新建模式\r\n      params.objNodeId = nodeItem.nodeId;\r\n      params[currentMode.paramKey] = values[currentMode.fieldName];\r\n      await createPartition(params);\r\n    }\r\n    \r\n    setUploadLoading(false);\r\n  };\r\n\r\n  // 新建采集口径\r\n  async function createPartition(params) {\r\n    await http.track_018_create_issue_partition(params).then(result => {\r\n      if (result.resultCode == 200) {\r\n        refreshSelectTeamMenu({ treeNode: result?.nodeTree[0] || {}})\r\n        setIsModalVisible(false);\r\n        nodeItem.callback && nodeItem.callback(result?.nodeTree[0]?.children[0] || {});\r\n      }\r\n    }).catch(err => {\r\n      console.log(err)\r\n    })\r\n  }\r\n\r\n  // 编辑采集口径\r\n  async function editPartition(params) {\r\n    await http.track_020_modify_issue_partition(params).then(result => {\r\n      if (result.resultCode == 200) {\r\n        refreshTeamMenu();\r\n        setIsModalVisible(false);\r\n        refetchGetPartitionDetail(); // 编辑采集口径后刷新数据\r\n      }\r\n    }).catch(err => {\r\n      console.log(err)\r\n    })\r\n  }\r\n\r\n  const handleOnChange = (value) => {\r\n    let criteriaList = tRef?.current?.getCriteriaListForBackend && tRef?.current?.getCriteriaListForBackend?.()\r\n    if (isEmpty(criteriaList) && isEmpty(selectFields)) {\r\n      return\r\n    }\r\n    return Modal.confirm({\r\n      title: \"提示\",\r\n      icon: <ExclamationCircleOutlined />,\r\n      content: <p>{`重新选择${isCust ? '表单' : '流程'}，下方两个表格将清空，您需要重新设置。是否继续?`}</p>,\r\n      okText: \"是\",\r\n      cancelText: \"否\",\r\n      zIndex: 1002, // FIXME:不能超过10000，否则会导致Select下拉框被覆盖\r\n      onOk: () => {\r\n      },\r\n      onCancel: () => {\r\n        console.log(\"Cancel\");\r\n        form.setFieldValue(currentMode.fieldName, currentMode.getRevertKey(selecedtProcess))\r\n      },\r\n    });\r\n  }\r\n\r\n  // 处理\"可新建\"复选框变化\r\n  const handleCreateFlgChange = (e) => {\r\n    const checked = e.target.checked;\r\n\r\n    // 如果取消勾选且表单字段设置有值，弹出确认对话框\r\n    if (!checked && !isEmpty(selectFields)) {\r\n      Modal.confirm({\r\n        title: \"提示\",\r\n        icon: <ExclamationCircleOutlined />,\r\n        content: \"取消可新建功能，则清除表单字段设置，是否取消？\",\r\n        okText: \"取消\",\r\n        cancelText: \"不取消\",\r\n        zIndex: 1002,\r\n        onOk: () => {\r\n          // 用户确认取消，清除表单字段设置并隐藏\r\n          setSelectFields([]);\r\n          setShowFormFields(false);\r\n          form.setFieldValue('createFlg', false);\r\n        },\r\n        onCancel: () => {\r\n          // 用户选择不取消，恢复勾选状态\r\n          form.setFieldValue('createFlg', true);\r\n        },\r\n      });\r\n    } else if (checked) {\r\n      // 如果重新勾选，显示表单字段设置\r\n      setShowFormFields(true);\r\n    }\r\n  }\r\n\r\n  return <DraggableDrawer\r\n    className=\"tms-drawer IssuePartition\"\r\n    width={\"60%\"}\r\n    title={opType ? `采集口径设置` : `新建采集口径`}\r\n    destroyOnClose={true}\r\n    open={isModalVisible}\r\n    onClose={handleCancel}\r\n    footer={<div style={{ textAlign: \"right\" }} >\r\n      <Space size={20}>\r\n        <Button style={{ borderRadius: 5 }} onClick={handleCancel}>取消</Button>\r\n        <Button type=\"primary\" style={{ borderRadius: 5 }} onClick={handleSaveClick}>提交</Button>\r\n      </Space>\r\n    </div>}\r\n  >\r\n    <UploadLoading spinning={isLoading}>\r\n      <Form\r\n        form={form}\r\n        labelCol={{ span: 4 }}\r\n        wrapperCol={{ span: 19 }}\r\n        onFinish={onFinish}\r\n        preserve={false}// Modal关闭后销毁form字段数据\r\n        autoComplete={\"off\"} // 取消自动补充功能\r\n        initialValues={{\r\n          createFlg: true,\r\n        }}\r\n      >\r\n        <Form.Item label={\"采集口径名称\"} name=\"name\" required={true}>\r\n          <Input style={selectStyle} autoComplete=\"off\" />\r\n        </Form.Item>\r\n        <Form.Item label={currentMode.label}>\r\n          <Space size={20}>\r\n            <Form.Item name={currentMode.fieldName} required={true} noStyle>\r\n              <Select\r\n                showSearch\r\n                style={{ width: 300, borderRadius: 3 }}\r\n                //select选择框搜索\r\n                filterOption={(input, option) => {\r\n                  return (option.children.props.children[1].props.children || \"\").toLowerCase().indexOf(input.toLowerCase()) >= 0\r\n                }}\r\n                onChange={handleOnChange}\r\n              >\r\n                {\r\n                  flowList.map(flow => (<Select.Option key={flow.key} value={flow.value} >\r\n                    <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                      {flow.icon}\r\n                      <span style={{ paddingLeft: 5 }}>{flow.label}</span>\r\n                    </div>\r\n                  </Select.Option>))\r\n                }\r\n              </Select>\r\n            </Form.Item>\r\n            <Form.Item name=\"createFlg\" valuePropName=\"checked\" noStyle>\r\n              <Checkbox onChange={handleCreateFlgChange}>可新建</Checkbox>\r\n            </Form.Item>\r\n          </Space>\r\n        </Form.Item>\r\n        <Form.Item label={\"自定义搜索\"}>\r\n          {isEmpty(attrNodeList) ? <TEmpty/> :\r\n            <div>\r\n              <SearchEditTable attrNodeList={attrNodeList} exprs={[]} criteriaList={initTableData} selectionList={selectionList} queryType={0} ref={tRef} />\r\n              <div className=\"search-edit-btns\">\r\n                <Button\r\n                  type=\"link\"\r\n                  icon={\r\n                    <QuestionCircleOutlined className=\"color-yellow\" />\r\n                  }\r\n                  onClick={handleOnAddSearchCode}\r\n                >\r\n                  <span>+ 动态条件（<FunctionOutlined style={{ margin: 0 }} />）</span>\r\n                </Button>\r\n                <Button icon={<SearchOutlined />} className=\"defaultBtn_light\" onClick={handleSearchPreClick} >结果预览</Button>\r\n              </div>\r\n              <div className=\"remarks\">备注1：此处的自定义搜索，用于显示当前采集口径对应的{projectInfo.issueAlias ?? \"\"}列表。</div>\r\n              <div className=\"remarks\">备注2：点击确定按钮仅保存表单(即条件)，预览结果不做保存。</div>\r\n            </div>\r\n          }\r\n        </Form.Item>\r\n        {showFormFields && (\r\n          <Form.Item label={\"表单字段设置\"}>\r\n            <CustomerFormTable selectFields={selectFields} setSelectFields={setSelectFields} selectionList={selectionList} userList={userList} subclassAttrList={subclassAttrList} selecedtProcess={selecedtProcess} />\r\n            <a className=\"fontsize-12\" onClick={() => setIsAddAttrModalVisible(true)}>+ 添加字段</a>\r\n            <div className=\"remarks\">备注1：在{projectInfo.issueAlias ?? \"\"}项目\"{projectInfo.name}\"的自定义字段的基础上，进一步限缩上面表格中字段的是否显示/可修改/可搜索。</div>\r\n            <div className=\"remarks\">备注2：在当前采集口径中提交的新建{projectInfo.issueAlias ?? \"\"}，其对应上面表格中的字段，如果值为空，则使用\"缺省值\"列中的值，进行最后的数据存储。</div>\r\n          </Form.Item>\r\n        )}\r\n      </Form>\r\n    </UploadLoading>\r\n    {/* 添加字段 */}\r\n    <AddPartitionAttr selectFields={selectFields} attrList={subclassAttrList} visible={isAddAttrModalVisible} onSelectFields={onSelectFields} onCancel={() => setIsAddAttrModalVisible(false)} />\r\n    {/* 搜索结果预览 */}\r\n    <SearchReultPreviewModal />\r\n    {/* 显示字段 */}\r\n    <SearchDisplayField\r\n      open={searchDisplayFieldOpen}\r\n      onOk={handleSearchDisplayFieldOnOk}\r\n      onCancel={handleSearchDisplayFieldOnCancel}\r\n      nodeName={nodeItem?.nodeName}\r\n      checkedValues={checkedValues}\r\n      attrNodeList={attrNodeList}\r\n    />\r\n  </DraggableDrawer>\r\n}"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,cAAc,QAAQ,mBAAmB;AACvH,OAAO,KAAKC,IAAI,MAAM,kBAAkB;AACxC,SAASC,kCAAkC,EAAEC,mCAAmC,QAAQ,6BAA6B;AACrH,SAASC,UAAU,EAAEC,OAAO,QAAQ,0BAA0B;AAC9D,SAASC,cAAc,EAAEC,YAAY,EAAEC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,QAAS,oBAAoB;AACzG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,wBAAwB,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,WAAW,QAAQ,0BAA0B;AACxI,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,SAASC,WAAW,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,+BAA+B,QAAQ,yBAAyB;AAC9H,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,MAAM;AAClF,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAC5D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,kBAAkB,MAAM,6DAA6D;AAC5F,OAAOC,eAAe,MAAM,0DAA0D;AACtF,OAAOC,uBAAuB,MAAM,kEAAkE;AACtG,SAASC,kCAAkC,EAAEC,sCAAsC,QAAQ,mCAAmC;AAC9H,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAO,8BAA8B;AACrC,SAAQC,sCAAsC,QAAO,8CAA8C;AACnG,SAASC,yBAAyB,EAAEC,8BAA8B,EAAEC,6BAA6B,QAAQ,kCAAkC;AAC3I,OAAOC,eAAe,MAAM,6BAA6B;;AAGzD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,eAAe,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC9C,MAAM;IAAEC;EAAO,CAAC,GAAGvB,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACwB,IAAI,CAAC,GAAGlC,IAAI,CAACmC,OAAO,CAAC,CAAC;EAC7B,MAAMC,IAAI,GAAG7B,MAAM,CAAC8B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACxC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzD,MAAM,CAACmC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7E,MAAM,CAACqC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3E;EACA,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD;EACA,MAAM,CAAC6C,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChF,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAChB,QAAQ,CAACiE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAiB;EAC9D,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE5D,MAAMwD,MAAM,GAAGvD,OAAO,CAAC,MAAI;IACzB,OAAOb,+BAA+B,CAACqE,QAAQ,CAAClB,QAAQ,CAACmB,QAAQ,CAAC,GAAGpF,YAAY,CAACqF,WAAW,GAAGrF,YAAY,CAACsF,UAAU,EAAE;EAC3H,CAAC,EAAC,CAACrB,QAAQ,CAAC,CAAC;EAEb,MAAM;IAAEsB,IAAI,EAAE;MAAEC,MAAM;MAAEC;IAAK,CAAC;IAAEC,SAAS,EAAEC;EAAiB,CAAC,GAAGnD,yBAAyB,CAAC;IAACW,MAAM;IAAEyC,MAAM,EAAE3B,QAAQ,CAAC2B,MAAM;IAAEC,OAAO,EAAE,CAAC,CAAC5B,QAAQ,CAAC2B;EAAO,CAAC,CAAC;;EAEzJ;EACA;EACA,MAAM;IAAEL,IAAI,EAAEO,WAAW,GAAG,CAAC,CAAC;IAAEJ,SAAS,EAAEK,gBAAgB;IAAEC,aAAa,EAAEC;EAAqB,CAAC,GAAGlF,QAAQ,CAACwB,sCAAsC,CAACY,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,MAAM,EAAE,CAAC,EAAC3B,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE2B,MAAM,EAAC,CAAC;;EAEnM;EACA,MAAM;IAAEL,IAAI,EAAEW,cAAc,GAAG,CAAC,CAAC;IAAER,SAAS,EAAES;EAAiB,CAAC,GAAG1D,8BAA8B,CAAC;IAACU,MAAM;IAAEyC,MAAM,EAAE3B,QAAQ,CAAC2B,MAAM;IAAEC,OAAO,EAAE,CAAC,CAACL,MAAM,IAAI,CAAC,CAACvB,QAAQ,CAAC2B;EAAM,CAAC,CAAC;;EAE5K;EACA,MAAMQ,UAAU,GAAG;IACjBC,MAAM,EAAE;MACNC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,eAAe,EAAGC,EAAE;QAAA,IAAAC,qBAAA;QAAA,OAAKR,cAAc,aAAdA,cAAc,wBAAAQ,qBAAA,GAAdR,cAAc,CAAES,QAAQ,cAAAD,qBAAA,uBAAxBA,qBAAA,CAA0BE,IAAI,CAACxD,IAAI,IAAIA,IAAI,CAACwC,MAAM,IAAIa,EAAE,CAAC;MAAA;MAClFI,WAAW,EAAEA,CAAA;QAAA,IAAAC,sBAAA;QAAA,OAAM,CAAAZ,cAAc,aAAdA,cAAc,wBAAAY,sBAAA,GAAdZ,cAAc,CAAES,QAAQ,cAAAG,sBAAA,uBAAxBA,sBAAA,CAA0BC,GAAG,CAAC3D,IAAI,KAAK;UACxD4D,GAAG,EAAE5D,IAAI,CAACwC,MAAM;UAChBqB,KAAK,EAAE7D,IAAI,CAACwC,MAAM;UAClBW,KAAK,EAAEnD,IAAI,CAAC8D;UACZ;QACF,CAAC,CAAC,CAAC,KAAI,EAAE;MAAA;MACTC,aAAa,EAAGC,IAAI,IAAKA,IAAI,CAACC,MAAM,GAAG,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,GAAGM,SAAS;MAClEC,YAAY,EAAGC,IAAI,IAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE5B,MAAM;MACpC6B,OAAO,EAAE9G,WAAW,CAAC+G,mBAAmB;MACxCC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;MACNtB,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAE,MAAM;MACbC,eAAe,EAAGC,EAAE;QAAA,IAAAoB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAAA,OAAKjC,WAAW,aAAXA,WAAW,wBAAA+B,qBAAA,GAAX/B,WAAW,CAAElB,QAAQ,cAAAiD,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBG,MAAM,cAAAF,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAAAG,IAAA,CAAAJ,qBAAA,EAAgCK,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAIlI,UAAU,CAACmI,MAAM,CAAC,cAAAL,sBAAA,uBAA5EA,sBAAA,CAA8EnB,IAAI,CAACsB,IAAI,IAAIA,IAAI,CAACG,gBAAgB,IAAI5B,EAAE,CAAC;MAAA;MAChJI,WAAW,EAAEA,CAAA,KAAM;QACjB,MAAM,CAACjC,QAAQ,CAAC,GAAGnE,WAAW,CAAC,CAAAqF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAElB,QAAQ,KAAI,EAAE,EAAE0D,aAAa,CAAC;QAC1E,OAAO1D,QAAQ;MACjB,CAAC;MACDuC,aAAa,EAAEA,CAACC,IAAI,EAAEmB,WAAW,KAAKA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEvB,GAAG;MACtDO,YAAY,EAAGC,IAAI,IAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,gBAAgB;MAC9CZ,OAAO,EAAEhC,IAAI,GAAG9E,WAAW,CAAC6H,qBAAqB,GAAG7H,WAAW,CAAC8H,uBAAuB;MACvFd,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMe,WAAW,GAAG/G,OAAO,CAAC,MAAI;IAC9B,OAAO6D,MAAM,GAAGY,UAAU,CAACC,MAAM,GAAGD,UAAU,CAACwB,MAAM;EACvD,CAAC,EAAC,CAACpC,MAAM,EAAEY,UAAU,CAAC,CAAC;EAEvB,MAAMiC,gBAAgB,GAAGnH,IAAI,CAACyH,QAAQ,CAACD,WAAW,CAACpC,SAAS,EAAElD,IAAI,CAAC;EACnE,MAAMwF,eAAe,GAAGjH,OAAO,CAAC,MAC9B+G,WAAW,CAAClC,eAAe,CAAC6B,gBAAgB,CAAC,EAC9C,CAACvC,WAAW,EAAEI,cAAc,EAAEmC,gBAAgB,EAAEK,WAAW,CAAC,CAAC;;EAE9D;EACA,MAAM;IAAEG,gBAAgB,GAAG,EAAE;IAAEC,UAAU,EAAEC,oBAAoB;IAAE/C,aAAa,EAAEgD;EAAwB,CAAC,GAAG/G,sCAAsC,CAACkB,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,MAAM,EAAEgD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,UAAU,EAAE,CAAC,EAACL,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEK,UAAU,EAAC;;EAExO;EACA,MAAM;IAAEC,QAAQ;IAAEC,YAAY,GAAC,EAAE;IAAEC,SAAS;IAAE3B,OAAO;IAAE4B,SAAS;IAAErD,aAAa,EAAEsD,qBAAqB;IAAEC,OAAO,EAAEC;EAA0B,CAAC,GAAGxH,kCAAkC,CAACmB,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,MAAM,EAAEnB,QAAQ,EAAES,MAAM,IAAIlF,YAAY,CAACqF,WAAW,IAAI,CAAC,EAACpB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE2B,MAAM,EAAC;EAC/Q;EACA,MAAM;IAAEL,IAAI,EAAE+C;EAAc,CAAC,GAAG1I,mCAAmC,CAACuD,MAAM,CAAC;EAC3E;EACA,MAAM;IAAEoC,IAAI,EAAEkE;EAAS,CAAC,GAAG9J,kCAAkC,CAACwD,MAAM,CAAC;;EAErE;EACA,MAAMuG,WAAW,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAE,CAAC;EAEnD,MAAMlE,SAAS,GAAIK,gBAAgB,IAAIgD,oBAAoB,IAAK,CAAC,CAACvD,MAAM,IAAIW,gBAAkB,IAAIrB,aAAa;;EAE/G;EACAtD,SAAS,CAAC,MAAM;IACdpB,cAAc,CAACyJ,EAAE,CAAC,gCAAgC,EAAEC,8BAA8B,CAAC;IACnF,OAAO,MAAM1J,cAAc,CAAC2J,GAAG,CAAC,gCAAgC,EAAED,8BAA8B,CAAC;EACnG,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,8BAA8B,GAAGA,CAACE,MAAM,EAAEC,IAAI,KAAK;IACvDvF,WAAW,CAAChE,QAAQ,CAACiE,IAAI,CAAC,CAAC,CAAC;IAC5BT,WAAW,CAAC+F,IAAI,CAAC;IACjBvG,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,IAAIiC,cAAc,IAAIyB,MAAM,IAAIlF,YAAY,CAACqF,WAAW,IAAIiE,qBAAqB,IAAIN,uBAAuB,EAAE;MAC5G;MACAkB,wBAAwB,CAAC,CAAC;IAC5B,CAAC,MAAM;MACL;MACAC,yBAAyB,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC1G,cAAc,EAAE6F,qBAAqB,EAAEN,uBAAuB,CAAC,CAAC;;EAEpE;EACA,MAAMoB,2BAA2B,GAAIC,MAAM,IAAK;IAC9CjK,cAAc,CAACkK,IAAI,CAAC,kCAAkC,EAAE,EAAE,EACxD;MAAEC,gBAAgB,EAAEF,MAAM;MAAEG,cAAc,EAAEA,cAAc,CAACC,IAAI,CAAC,IAAI;IAAE,CAAC,CAAC;EAC5E,CAAC;;EAED;EACA;EACA,MAAMC,YAAY,GAAG/I,OAAO,CAAC,MAAM;IACjC,IAAI,EAACkH,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAExB,MAAM,KAAI,CAACoC,QAAQ,IAAI,CAACnB,aAAa,EAAE;MAC5D,OAAO,EAAE;IACX;IACA,IAAIY,QAAQ,GAAG3I,0BAA0B,CAACsI,gBAAgB,EAAEY,QAAQ,EAAEnB,aAAa,EAAE,IAAI,CAAC;IAC1F,OAAOhI,wBAAwB,CAAC4I,QAAQ,CAAC,CAAC,CAAC;EAC7C,CAAC,EAAE,CAACL,gBAAgB,EAAEY,QAAQ,EAAEnB,aAAa,CAAC,CAAC;;EAE/C;EACA,MAAMqC,oBAAoB,GAAGhJ,OAAO,CAAC,MAAM;IACzC,IAAI,EAACkH,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAExB,MAAM,KAAI,CAACoC,QAAQ,IAAI,CAACnB,aAAa,EAAE;MAC5D,OAAO,EAAE;IACX;IACA,IAAIY,QAAQ,GAAG3I,0BAA0B,CAACsI,gBAAgB,EAAEY,QAAQ,EAAEnB,aAAa,EAAE,IAAI,CAAC;IAC1F,OAAOY,QAAQ,CAAClB,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACoD,OAAO,CAAC,CAAC7D,GAAG,CAACS,IAAI,IAAIA,IAAI,CAAC5B,MAAM,CAAC;EACvE,CAAC,EAAE,CAACiD,gBAAgB,EAAEY,QAAQ,EAAEnB,aAAa,CAAC,CAAC;;EAE/C;EACA,MAAMuC,aAAa,GAAGtG,yBAAyB,CAAC8C,MAAM,GAAG,CAAC,GAAG9C,yBAAyB,GAAGoG,oBAAoB;EAC7G;EACAnJ,SAAS,CAAC,MAAM;IACd,IAAI0D,MAAM,IAAIlF,YAAY,CAACqF,WAAW,IAAIiE,qBAAqB,IAAI,CAACxJ,OAAO,CAAC4K,YAAY,CAAC,EAAE;MACzFI,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAAC5F,MAAM,EAAEoE,qBAAqB,EAAEoB,YAAY,CAAE,CAAC;;EAElD;EACAlJ,SAAS,CAAC,MAAM;IACd,IAAI0D,MAAM,IAAIlF,YAAY,CAACqF,WAAW,IAAIiE,qBAAqB,IAAIrD,oBAAoB,EAAE;MACvF7C,IAAI,CAAC2H,aAAa,CAACrC,WAAW,CAACpC,SAAS,EAAE8C,SAAS,CAAC;IACtD;EACF,CAAC,EAAE,CAAClE,MAAM,EAAEoE,qBAAqB,EAAErD,oBAAoB,EAAE+E,IAAI,CAACC,SAAS,CAACvC,WAAW,CAAC,CAAC,CAAC;EAEtFlH,SAAS,CAAC,MAAM;IACd,IAAIiC,cAAc,EAAE;MAClB,MAAMyH,QAAQ,GAAGxC,WAAW,CAAC7B,WAAW,CAAC,CAAC;MAC1ChC,WAAW,CAACqG,QAAQ,CAAC;MAErB,IAAIhG,MAAM,IAAIlF,YAAY,CAACsF,UAAU,EAAE;QACrC,MAAMiD,WAAW,GAAG/C,MAAM,GAAG,IAAI,GAAG/E,WAAW,CAAC,CAAAqF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAElB,QAAQ,KAAI,EAAE,EAAE0D,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9F,MAAM6C,UAAU,GAAGzC,WAAW,CAACvB,aAAa,CAAC+D,QAAQ,EAAE3C,WAAW,CAAC;QACnE,IAAI4C,UAAU,EAAE;UACd/H,IAAI,CAAC2H,aAAa,CAACrC,WAAW,CAACpC,SAAS,EAAE6E,UAAU,CAAC;QACvD;MACF;IACF;EACF,CAAC,EAAE,CAAC1H,cAAc,EAAEqC,WAAW,EAAEI,cAAc,EAAEV,MAAM,EAAEwF,IAAI,CAACC,SAAS,CAACvC,WAAW,CAAC,CAAC,CAAC;;EAEtF;EACA,MAAMoC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIM,aAAa,GAAG,CAACjC,YAAY,IAAI,EAAE,EAAEnB,MAAM,CAACqD,QAAQ,IAAIX,YAAY,CAACY,IAAI,CAACC,QAAQ,IAAIF,QAAQ,CAACG,OAAO,IAAID,QAAQ,CAAC3F,MAAM,CAAC,CAAC,CAAC,CAAC;IAChIwF,aAAa,GAAG5K,0BAA0B,CAAC4K,aAAa,EAAEV,YAAY,EAAEpC,aAAa,CAAC;IACvFhE,gBAAgB,CAAC8G,aAAa,CAAC;EACjC,CAAC;;EAED;EACA,SAASjB,yBAAyBA,CAAA,EAAG;IACnC;IACA7F,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBF,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IACrBI,4BAA4B,CAAC,EAAE,CAAC,CAAC,CAAC;IAClCS,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC3B;;EAEA;EACA,SAASiF,wBAAwBA,CAAA,EAAG;IAClC,MAAMuB,eAAe,GAAGpC,SAAS,IAAIpJ,UAAU,CAACmI,MAAM;IACtDhF,IAAI,CAACsI,cAAc,CAAC;MAClBC,IAAI,EAAE1H,QAAQ,CAAC0H,IAAI;MAAE;MACrBtC,SAAS,EAAEoC;IACb,CAAC,CAAC;;IAEF;IACAxG,iBAAiB,CAACwG,eAAe,CAAC;IAElC,MAAMG,SAAS,GAAG1C,QAAQ,CAAClB,MAAM,CAAC6D,IAAI,IAAIhD,gBAAgB,CAACyC,IAAI,CAACC,QAAQ,IAAIM,IAAI,CAACL,OAAO,IAAID,QAAQ,CAAC3F,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/GgG,SAAS,CAACE,OAAO,CAAED,IAAI,IAAK;MAAA,IAAAE,kBAAA,EAAAC,mBAAA;MAC1B,MAAMC,YAAY,GAAGpD,gBAAgB,CAACjC,IAAI,CAAC2E,QAAQ,IAAIM,IAAI,CAACL,OAAO,IAAID,QAAQ,CAAC3F,MAAM,CAAC,CAACqG,YAAY;MACpGJ,IAAI,CAACK,qBAAqB,GAAGC,0BAA0B,CAACF,YAAY,EAAElM,cAAc,CAACqM,kBAAkB,CAAC;MACxGP,IAAI,CAACQ,oBAAoB,GAAGF,0BAA0B,CAACF,YAAY,EAAElM,cAAc,CAACuM,iBAAiB,CAAC;MACtGT,IAAI,CAACU,SAAS,IAAAR,kBAAA,GAAIE,YAAY,CAACrF,IAAI,CAAC4F,EAAE,IAAIA,EAAE,CAACC,QAAQ,IAAI1M,cAAc,CAAC2M,kBAAkB,CAAC,cAAAX,kBAAA,uBAA1EA,kBAAA,CAA6EY,SAAS;MACvGd,IAAI,CAACe,WAAW,GAAG,EAAAZ,mBAAA,GAAAC,YAAY,CAACrF,IAAI,CAACiG,GAAG,IAAIA,GAAG,CAACJ,QAAQ,IAAI1M,cAAc,CAAC+M,iBAAiB,CAAC,cAAAd,mBAAA,uBAA1EA,mBAAA,CAA4EW,SAAS,KAAI,EAAE;IAChH,CAAC,CAAC;IACFvI,eAAe,CAACwH,SAAS,CAAC;EAC5B;;EAEA;EACA,MAAMO,0BAA0B,GAAGA,CAACY,QAAQ,GAAG,EAAE,EAAEC,IAAI,KAAK;IAC1D,OAAO,CAACD,QAAQ,CAACnG,IAAI,CAAEY,IAAI,IAAKA,IAAI,CAACiF,QAAQ,IAAIO,IAAI,CAAC,IAAI;MAAEL,SAAS,EAAE;IAAI,CAAC,EAAEA,SAAS;EACzF,CAAC;;EAED;EACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;IAC1B,OAAO7L,KAAK,CAAC8L,OAAO,CAAC;MAClBC,KAAK,EAAE,IAAI;MACXC,IAAI,eAAEvK,OAAA,CAACvD,yBAAyB;QAAA+N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,eAAE5K,OAAA;QAAA6K,QAAA,EAAKxI,MAAM,IAAIhF,OAAO,CAACyN,GAAG,GAAG,kBAAkB,GAAG;MAAkB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;MAClFI,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEA,CAAA,KAAM;QACTpK,iBAAiB,CAAC,KAAK,CAAC;MAC3B,CAAC;MACDqK,QAAQ,EAAEA,CAAA,KAAM;QACdC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIC,SAAS,GAAG,EAAE;IAClBjK,YAAY,CAAC6D,MAAM,CAACwE,EAAE,IAAI;MACxB2B,KAAK,CAACpH,GAAG,CAAC,CAAC8F,GAAG,EAAEwB,KAAK,KAAK;QACxB,IAAI7B,EAAE,CAAChB,OAAO,IAAIqB,GAAG,CAACjH,MAAM,EAAE;UAC5BwI,SAAS,CAACE,IAAI,CAAC9B,EAAE,CAAC;UAClB,OAAO2B,KAAK,CAACE,KAAK,CAAC;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFF,KAAK,CAACpH,GAAG,CAAES,IAAI,IAAK;MAAA,IAAA+G,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;MAClB,IAAIxC,YAAY,GAAGzE,IAAI,CAACyE,YAAY;MACpC,MAAMyC,kBAAkB,GAAGvC,0BAA0B,CAACF,YAAY,EAAElM,cAAc,CAACqM,kBAAkB,CAAC;MACtG,MAAMuC,iBAAiB,GAAGxC,0BAA0B,CAACF,YAAY,EAAElM,cAAc,CAACuM,iBAAiB,CAAC;MACpG,IAAIsC,KAAK,GAAG;QACVpD,OAAO,EAAEhE,IAAI,CAAC5B,MAAM;QACpBiJ,UAAU,EAAE,IAAI;QAChBC,cAAc,EAAE,EAAE;QAClBC,iBAAiB,EAAEL,kBAAkB;QACrCxC,qBAAqB,EAAEwC,kBAAkB;QACzCM,gBAAgB,EAAEL,iBAAiB;QACnCtC,oBAAoB,EAAEsC,iBAAiB;QACvCpC,SAAS,GAAAgC,mBAAA,GAAGtC,YAAY,CAACrF,IAAI,CAAC4F,EAAE,IAAIA,EAAE,CAACC,QAAQ,IAAI1M,cAAc,CAAC2M,kBAAkB,CAAC,cAAA6B,mBAAA,uBAA1EA,mBAAA,CAA6E5B,SAAS;QACjGC,WAAW,EAAE,EAAA4B,mBAAA,GAAAvC,YAAY,CAACrF,IAAI,CAACiG,GAAG,IAAIA,GAAG,CAACJ,QAAQ,IAAI1M,cAAc,CAAC+M,iBAAiB,CAAC,cAAA0B,mBAAA,uBAA1EA,mBAAA,CAA4E7B,SAAS,KAAI,EAAE;QACxGsC,QAAQ,EAAE,EAAAR,mBAAA,GAAAxC,YAAY,CAACrF,IAAI,CAAC4F,EAAE,IAAIA,EAAE,CAACC,QAAQ,IAAI1M,cAAc,CAACmP,iBAAiB,CAAC,cAAAT,mBAAA,uBAAxEA,mBAAA,CAA0E9B,SAAS,KAAI,EAAE,CAAO;MAC5G,CAAC;MACDyB,SAAS,CAACE,IAAI,CAACM,KAAK,CAAC;IACvB,CAAC,CAAC;IACFxK,eAAe,CAACgK,SAAS,CAAC;IAC1BpK,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAMmL,qBAAqB,GAAGA,CAAA,KAAM;IAClC7L,IAAI,CAAC8L,OAAO,CAACC,aAAa,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC1L,eAAe,CAAC,IAAI,CAAC;IACrBR,IAAI,CAACmM,MAAM,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAM/E,cAAc,GAAIgF,CAAC,IAAK;IAC5B1L,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAM2L,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAO5E,aAAa,CAAC9D,GAAG,CAAC2I,YAAY,KAAK;MAAElE,OAAO,EAAEkE;IAAa,CAAC,CAAC,CAAC;EACvE,CAAC;;EAED;EACA,MAAMC,4BAA4B,GAAGA,CAACH,CAAC,EAAEI,MAAM,KAAK;IAClD9L,yBAAyB,CAAC,KAAK,CAAC;IAChCU,4BAA4B,CAACoL,MAAM,CAAC,CAAC,CAAC;IACtCxP,cAAc,CAACkK,IAAI,CAAC,+BAA+B,EAAE,EAAE,EAAE;MAAEsF;IAAO,CAAC,CAAC,EAAC;EACvE,CAAC;;EAED;EACA,MAAMC,gCAAgC,GAAGA,CAACL,CAAC,EAAEI,MAAM,KAAK;IACtD,IAAI,CAAC/P,UAAU,CAACgL,aAAa,EAAE+E,MAAM,CAAC,EAAE;MACtCxO,KAAK,CAAC8L,OAAO,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,IAAI,eAAEvK,OAAA,CAACvD,yBAAyB;UAAA+N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACnCC,OAAO,EAAE,qBAAqB;QAC9BG,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAI;QAChBC,IAAI,EAAEA,CAAA,KAAM;UAAEhK,yBAAyB,CAAC,KAAK,CAAC;QAAE;MAClD,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,yBAAyB,CAAC,KAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAMgM,eAAe,GAAGA,CAAA,KAAM;IAC5BlM,eAAe,CAAC,KAAK,CAAC;IACtBR,IAAI,CAACmM,MAAM,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMQ,QAAQ,GAAG,MAAOH,MAAM,IAAK;IAAA,IAAAI,aAAA,EAAAC,qBAAA;IACjC,MAAM;MAAEtE;IAAK,CAAC,GAAGiE,MAAM;IACvB,IAAI,CAACjM,YAAY,IAAI,CAACgI,IAAI,EAAE;MAC1B;MACA,OAAOtL,UAAU,CAAC6P,OAAO,CAAC,YAAY,CAAC;IACzC;IACA,IAAI/G,YAAY,GAAG,CAAA7F,IAAI,aAAJA,IAAI,wBAAA0M,aAAA,GAAJ1M,IAAI,CAAE8L,OAAO,cAAAY,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAeG,yBAAyB,cAAAF,qBAAA,uBAAxCA,qBAAA,CAAAhI,IAAA,CAAA+H,aAA2C,CAAC,KAAI,EAAE;IACrE;IACA,IAAIrM,YAAY,EAAE;MAChB,MAAMyM,aAAa,GAAGX,qBAAqB,CAAC,CAAC;MAC7C,IAAIpF,MAAM,GAAG;QACX,QAAQ,EAAElH,MAAM;QAAE,WAAW,EAAEyM,MAAM,CAAClH,WAAW,CAACpC,SAAS,CAAC;QAAE,MAAM,EAAEqF,IAAI;QAAE,SAAS,EAAEjD,WAAW,CAACjB,OAAO;QAC1G,iBAAiB,EAAE,GAAG;QAAE,cAAc,EAAE0B,YAAY;QAAE,eAAe,EAAEiH;MACzE,CAAC;MACD;MACA/F,MAAM,GAAG;QAAE,GAAGA,MAAM;QAAEgG,OAAO,EAAE;MAAE,CAAC,EAAC;MACnC,OAAOjG,2BAA2B,CAACC,MAAM,CAAC;IAC5C;IACAtF,gBAAgB,CAAC,IAAI,CAAC;IACtB;IACAZ,YAAY,CAAC2H,OAAO,CAAC,CAACU,EAAE,EAAE6B,KAAK,KAAK;MAClC,IAAI7B,EAAE,CAACyC,QAAQ,IAAI7M,WAAW,CAACkO,KAAK,EAAE;QACpC9D,EAAE,CAACqC,UAAU,GAAG,IAAI;MACtB;IACF,CAAC,CAAC;IACF;IACA,IAAI0B,aAAa,GAAGpM,YAAY,CAAC4C,GAAG,CAAC,CAACyF,EAAE,EAAE6B,KAAK,MAAM;MACnD7C,OAAO,EAAEgB,EAAE,CAAChB,OAAO;MACnBqD,UAAU,EAAErC,EAAE,CAACqC,UAAU;MACzBC,cAAc,EAAEtC,EAAE,CAACsC,cAAc;MACjCC,iBAAiB,EAAEvC,EAAE,CAACuC,iBAAiB;MACvC7C,qBAAqB,EAAEM,EAAE,CAACN,qBAAqB;MAC/C8C,gBAAgB,EAAExC,EAAE,CAACwC,gBAAgB;MACrC3C,oBAAoB,EAAEG,EAAE,CAACH,oBAAoB;MAC7CmE,KAAK,EAAEnC,KAAK,GAAG;IACjB,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIoC,MAAM,GAAG;MAAEtN,MAAM;MAAEwI,IAAI;MAAEzC,QAAQ,EAAEqH,aAAa;MAAEpH,YAAY;MAAEE,SAAS,EAAEuG,MAAM,CAACvG,SAAS,GAAGpJ,UAAU,CAACmI,MAAM,GAAGnI,UAAU,CAACyQ;IAAQ,CAAC;;IAE1I;IACA,IAAIxL,MAAM,EAAE;MACV;MACAuL,MAAM,CAAC7K,MAAM,GAAG3B,QAAQ,CAAC2B,MAAM;MAC/B6K,MAAM,CAAC/H,WAAW,CAACf,QAAQ,CAAC,GAAGiI,MAAM,CAAClH,WAAW,CAACpC,SAAS,CAAC;MAC5D,MAAMqK,aAAa,CAACF,MAAM,CAAC;IAC7B,CAAC,MAAM;MACL;MACAA,MAAM,CAACG,SAAS,GAAG3M,QAAQ,CAAC2B,MAAM;MAClC6K,MAAM,CAAC/H,WAAW,CAACf,QAAQ,CAAC,GAAGiI,MAAM,CAAClH,WAAW,CAACpC,SAAS,CAAC;MAC5D,MAAMuK,eAAe,CAACJ,MAAM,CAAC;IAC/B;IAEA1L,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;;EAED;EACA,eAAe8L,eAAeA,CAACJ,MAAM,EAAE;IACrC,MAAM/Q,IAAI,CAACoR,gCAAgC,CAACL,MAAM,CAAC,CAACM,IAAI,CAACC,MAAM,IAAI;MACjE,IAAIA,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;QAAA,IAAAC,iBAAA;QAC5BrQ,qBAAqB,CAAC;UAAEsQ,QAAQ,EAAE,CAAAH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC;QAAC,CAAC,CAAC;QAC7D1N,iBAAiB,CAAC,KAAK,CAAC;QACxBO,QAAQ,CAACoN,QAAQ,IAAIpN,QAAQ,CAACoN,QAAQ,CAAC,CAAAL,MAAM,aAANA,MAAM,wBAAAE,iBAAA,GAANF,MAAM,CAAEI,QAAQ,CAAC,CAAC,CAAC,cAAAF,iBAAA,uBAAnBA,iBAAA,CAAqBxD,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC,CAAC;MAChF;IACF,CAAC,CAAC,CAAC4D,KAAK,CAACC,GAAG,IAAI;MACdvD,OAAO,CAACC,GAAG,CAACsD,GAAG,CAAC;IAClB,CAAC,CAAC;EACJ;;EAEA;EACA,eAAeZ,aAAaA,CAACF,MAAM,EAAE;IACnC,MAAM/Q,IAAI,CAAC8R,gCAAgC,CAACf,MAAM,CAAC,CAACM,IAAI,CAACC,MAAM,IAAI;MACjE,IAAIA,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;QAC5BrQ,eAAe,CAAC,CAAC;QACjB8C,iBAAiB,CAAC,KAAK,CAAC;QACxB8F,yBAAyB,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC,CAAC8H,KAAK,CAACC,GAAG,IAAI;MACdvD,OAAO,CAACC,GAAG,CAACsD,GAAG,CAAC;IAClB,CAAC,CAAC;EACJ;EAEA,MAAME,cAAc,GAAIxK,KAAK,IAAK;IAAA,IAAAyK,cAAA,EAAAC,cAAA,EAAAC,qBAAA;IAChC,IAAIzI,YAAY,GAAG,CAAA7F,IAAI,aAAJA,IAAI,wBAAAoO,cAAA,GAAJpO,IAAI,CAAE8L,OAAO,cAAAsC,cAAA,uBAAbA,cAAA,CAAevB,yBAAyB,MAAI7M,IAAI,aAAJA,IAAI,wBAAAqO,cAAA,GAAJrO,IAAI,CAAE8L,OAAO,cAAAuC,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAexB,yBAAyB,cAAAyB,qBAAA,uBAAxCA,qBAAA,CAAA3J,IAAA,CAAA0J,cAA2C,CAAC;IAC3G,IAAI7R,OAAO,CAACqJ,YAAY,CAAC,IAAIrJ,OAAO,CAACqE,YAAY,CAAC,EAAE;MAClD;IACF;IACA,OAAO/C,KAAK,CAAC8L,OAAO,CAAC;MACnBC,KAAK,EAAE,IAAI;MACXC,IAAI,eAAEvK,OAAA,CAACvD,yBAAyB;QAAA+N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,eAAE5K,OAAA;QAAA6K,QAAA,EAAI,OAAOlI,MAAM,GAAG,IAAI,GAAG,IAAI;MAA0B;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;MACvEI,MAAM,EAAE,GAAG;MACXC,UAAU,EAAE,GAAG;MACfgE,MAAM,EAAE,IAAI;MAAE;MACd/D,IAAI,EAAEA,CAAA,KAAM,CACZ,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAM;QACdC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QACrB7K,IAAI,CAAC2H,aAAa,CAACrC,WAAW,CAACpC,SAAS,EAAEoC,WAAW,CAACnB,YAAY,CAACqB,eAAe,CAAC,CAAC;MACtF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkJ,qBAAqB,GAAItC,CAAC,IAAK;IACnC,MAAM5E,OAAO,GAAG4E,CAAC,CAACxF,MAAM,CAACY,OAAO;;IAEhC;IACA,IAAI,CAACA,OAAO,IAAI,CAAC9K,OAAO,CAACqE,YAAY,CAAC,EAAE;MACtC/C,KAAK,CAAC8L,OAAO,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,IAAI,eAAEvK,OAAA,CAACvD,yBAAyB;UAAA+N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACnCC,OAAO,EAAE,yBAAyB;QAClCG,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,KAAK;QACjBgE,MAAM,EAAE,IAAI;QACZ/D,IAAI,EAAEA,CAAA,KAAM;UACV;UACA1J,eAAe,CAAC,EAAE,CAAC;UACnBa,iBAAiB,CAAC,KAAK,CAAC;UACxB7B,IAAI,CAAC2H,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC;QACxC,CAAC;QACDgD,QAAQ,EAAEA,CAAA,KAAM;UACd;UACA3K,IAAI,CAAC2H,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC;QACvC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIH,OAAO,EAAE;MAClB;MACA3F,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,oBAAOpC,OAAA,CAACF,eAAe;IACrBoP,SAAS,EAAC,2BAA2B;IACrCpI,KAAK,EAAE,KAAM;IACbwD,KAAK,EAAEjI,MAAM,GAAG,QAAQ,GAAG,QAAS;IACpC8M,cAAc,EAAE,IAAK;IACrBC,IAAI,EAAExO,cAAe;IACrByO,OAAO,EAAEjF,YAAa;IACtBkF,MAAM,eAAEtP,OAAA;MAAKuP,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAA3E,QAAA,eACzC7K,OAAA,CAACxB,KAAK;QAACiR,IAAI,EAAE,EAAG;QAAA5E,QAAA,gBACd7K,OAAA,CAAC7B,MAAM;UAACoR,KAAK,EAAE;YAAExI,YAAY,EAAE;UAAE,CAAE;UAAC2I,OAAO,EAAEtF,YAAa;UAAAS,QAAA,EAAC;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtE3K,OAAA,CAAC7B,MAAM;UAACgM,IAAI,EAAC,SAAS;UAACoF,KAAK,EAAE;YAAExI,YAAY,EAAE;UAAE,CAAE;UAAC2I,OAAO,EAAEzC,eAAgB;UAAApC,QAAA,EAAC;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAE;IAAAE,QAAA,gBAEP7K,OAAA,CAACR,aAAa;MAACmQ,QAAQ,EAAE9M,SAAU;MAAAgI,QAAA,eACjC7K,OAAA,CAAC3B,IAAI;QACHkC,IAAI,EAAEA,IAAK;QACXqP,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QACtBC,UAAU,EAAE;UAAED,IAAI,EAAE;QAAG,CAAE;QACzB3C,QAAQ,EAAEA,QAAS;QACnB6C,QAAQ,EAAE,KAAM;QAAA;QAChBC,YAAY,EAAE,KAAM,CAAC;QAAA;QACrBC,aAAa,EAAE;UACbzJ,SAAS,EAAE;QACb,CAAE;QAAAqE,QAAA,gBAEF7K,OAAA,CAAC3B,IAAI,CAAC6R,IAAI;UAACxM,KAAK,EAAE,QAAS;UAACoF,IAAI,EAAC,MAAM;UAACqH,QAAQ,EAAE,IAAK;UAAAtF,QAAA,eACrD7K,OAAA,CAAC1B,KAAK;YAACiR,KAAK,EAAE1I,WAAY;YAACmJ,YAAY,EAAC;UAAK;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACZ3K,OAAA,CAAC3B,IAAI,CAAC6R,IAAI;UAACxM,KAAK,EAAEmC,WAAW,CAACnC,KAAM;UAAAmH,QAAA,eAClC7K,OAAA,CAACxB,KAAK;YAACiR,IAAI,EAAE,EAAG;YAAA5E,QAAA,gBACd7K,OAAA,CAAC3B,IAAI,CAAC6R,IAAI;cAACpH,IAAI,EAAEjD,WAAW,CAACpC,SAAU;cAAC0M,QAAQ,EAAE,IAAK;cAACC,OAAO;cAAAvF,QAAA,eAC7D7K,OAAA,CAACtB,MAAM;gBACL2R,UAAU;gBACVd,KAAK,EAAE;kBAAEzI,KAAK,EAAE,GAAG;kBAAEC,YAAY,EAAE;gBAAE;gBACrC;gBAAA;gBACAuJ,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;kBAC/B,OAAO,CAACA,MAAM,CAAC3F,QAAQ,CAAC4F,KAAK,CAAC5F,QAAQ,CAAC,CAAC,CAAC,CAAC4F,KAAK,CAAC5F,QAAQ,IAAI,EAAE,EAAE6F,WAAW,CAAC,CAAC,CAACC,OAAO,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;gBACjH,CAAE;gBACFE,QAAQ,EAAEhC,cAAe;gBAAA/D,QAAA,EAGvB9I,QAAQ,CAACmC,GAAG,CAACmB,IAAI,iBAAKrF,OAAA,CAACtB,MAAM,CAACmS,MAAM;kBAAgBzM,KAAK,EAAEiB,IAAI,CAACjB,KAAM;kBAAAyG,QAAA,eACpE7K,OAAA;oBAAKuP,KAAK,EAAE;sBAAEuB,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAAlG,QAAA,GACnDxF,IAAI,CAACkF,IAAI,eACVvK,OAAA;sBAAMuP,KAAK,EAAE;wBAAEyB,WAAW,EAAE;sBAAE,CAAE;sBAAAnG,QAAA,EAAExF,IAAI,CAAC3B;oBAAK;sBAAA8G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC,GAJkCtF,IAAI,CAAClB,GAAG;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKnC,CAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACZ3K,OAAA,CAAC3B,IAAI,CAAC6R,IAAI;cAACpH,IAAI,EAAC,WAAW;cAACmI,aAAa,EAAC,SAAS;cAACb,OAAO;cAAAvF,QAAA,eACzD7K,OAAA,CAACvB,QAAQ;gBAACmS,QAAQ,EAAE3B,qBAAsB;gBAAApE,QAAA,EAAC;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACZ3K,OAAA,CAAC3B,IAAI,CAAC6R,IAAI;UAACxM,KAAK,EAAE,OAAQ;UAAAmH,QAAA,EACvB5N,OAAO,CAAC4K,YAAY,CAAC,gBAAG7H,OAAA,CAACP,MAAM;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,gBAChC3K,OAAA;YAAA6K,QAAA,gBACE7K,OAAA,CAACf,eAAe;cAAC4I,YAAY,EAAEA,YAAa;cAACqJ,KAAK,EAAE,EAAG;cAAC5K,YAAY,EAAE9E,aAAc;cAACiE,aAAa,EAAEA,aAAc;cAAC0L,SAAS,EAAE,CAAE;cAACC,GAAG,EAAE3Q;YAAK;cAAA+J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9I3K,OAAA;cAAKkP,SAAS,EAAC,kBAAkB;cAAArE,QAAA,gBAC/B7K,OAAA,CAAC7B,MAAM;gBACLgM,IAAI,EAAC,MAAM;gBACXI,IAAI,eACFvK,OAAA,CAACrD,sBAAsB;kBAACuS,SAAS,EAAC;gBAAc;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACnD;gBACD+E,OAAO,EAAEpD,qBAAsB;gBAAAzB,QAAA,eAE/B7K,OAAA;kBAAA6K,QAAA,GAAM,kCAAO,eAAA7K,OAAA,CAACtD,gBAAgB;oBAAC6S,KAAK,EAAE;sBAAE8B,MAAM,EAAE;oBAAE;kBAAE;oBAAA7G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACT3K,OAAA,CAAC7B,MAAM;gBAACoM,IAAI,eAAEvK,OAAA,CAACpD,cAAc;kBAAA4N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACuE,SAAS,EAAC,kBAAkB;gBAACQ,OAAO,EAAEjD,oBAAqB;gBAAA5B,QAAA,EAAE;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC,eACN3K,OAAA;cAAKkP,SAAS,EAAC,SAAS;cAAArE,QAAA,GAAC,yJAA0B,GAAA1K,qBAAA,GAAC8C,WAAW,CAACqO,UAAU,cAAAnR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAAC,oBAAG;YAAA;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1F3K,OAAA;cAAKkP,SAAS,EAAC,SAAS;cAAArE,QAAA,EAAC;YAA8B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEC,CAAC,EACXxI,cAAc,iBACbnC,OAAA,CAAC3B,IAAI,CAAC6R,IAAI;UAACxM,KAAK,EAAE,QAAS;UAAAmH,QAAA,gBACzB7K,OAAA,CAACV,iBAAiB;YAACgC,YAAY,EAAEA,YAAa;YAACC,eAAe,EAAEA,eAAgB;YAACkE,aAAa,EAAEA,aAAc;YAACmB,QAAQ,EAAEA,QAAS;YAACZ,gBAAgB,EAAEA,gBAAiB;YAACD,eAAe,EAAEA;UAAgB;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3M3K,OAAA;YAAGkP,SAAS,EAAC,aAAa;YAACQ,OAAO,EAAEA,CAAA,KAAMvO,wBAAwB,CAAC,IAAI,CAAE;YAAA0J,QAAA,EAAC;UAAM;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpF3K,OAAA;YAAKkP,SAAS,EAAC,SAAS;YAAArE,QAAA,GAAC,2BAAK,GAAAzK,sBAAA,GAAC6C,WAAW,CAACqO,UAAU,cAAAlR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,EAAC,gBAAG,EAAC6C,WAAW,CAAC6F,IAAI,EAAC,wNAAsC;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7H3K,OAAA;YAAKkP,SAAS,EAAC,SAAS;YAAArE,QAAA,GAAC,mGAAiB,GAAAxK,sBAAA,GAAC4C,WAAW,CAACqO,UAAU,cAAAjR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,EAAC,sPAA0C;UAAA;YAAAmK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEhB3K,OAAA,CAACX,gBAAgB;MAACiC,YAAY,EAAEA,YAAa;MAAC+E,QAAQ,EAAEL,gBAAiB;MAACuL,OAAO,EAAErQ,qBAAsB;MAACmK,cAAc,EAAEA,cAAe;MAACH,QAAQ,EAAEA,CAAA,KAAM/J,wBAAwB,CAAC,KAAK;IAAE;MAAAqJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE7L3K,OAAA,CAACd,uBAAuB;MAAAsL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3B3K,OAAA,CAAChB,kBAAkB;MACjBoQ,IAAI,EAAEpO,sBAAuB;MAC7BiK,IAAI,EAAE6B,4BAA6B;MACnC5B,QAAQ,EAAE8B,gCAAiC;MAC3CwE,QAAQ,EAAEpQ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoQ,QAAS;MAC7BxJ,aAAa,EAAEA,aAAc;MAC7BH,YAAY,EAAEA;IAAa;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AACpB;AAACzK,EAAA,CAtiBuBD,qBAAqB;EAAA,QACxBlB,SAAS,EACbV,IAAI,CAACmC,OAAO,EAwBqCb,yBAAyB,EAIYzB,QAAQ,EAG1C0B,8BAA8B,EAuCxEvB,IAAI,CAACyH,QAAQ,EAMsE1G,sCAAsC,EAGHD,kCAAkC,EAEjJpC,mCAAmC,EAExCD,kCAAkC;AAAA;AAAA2U,EAAA,GArFvCxR,qBAAqB;AAAA,IAAAwR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}