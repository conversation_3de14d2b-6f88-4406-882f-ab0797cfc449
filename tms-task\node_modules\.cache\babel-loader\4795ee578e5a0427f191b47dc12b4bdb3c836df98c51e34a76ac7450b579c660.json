{"ast": null, "code": "// 根据传入的property字段排序，默认升序\nexport const sortByProperty = (a, b, property, sortAsc = true) => {\n  let value1 = a[property];\n  let value2 = b[property];\n  if (Object.prototype.toString.call(value1) === \"[object String]\") {\n    // 字符串\n    if (sortAsc) {\n      return value1.localeCompare(value2);\n    } else {\n      return value2.localeCompare(value1);\n    }\n  } else if (Object.prototype.toString.call(value1) === \"[object Number]\") {\n    // 数值\n    if (sortAsc) {\n      return value1 - value2;\n    } else {\n      return value2 - value1;\n    }\n  } else if (Object.prototype.toString.call(value1) === \"[object Array]\") {\n    // 数组\n    if (sortAsc) {\n      return (value1 || []).length - (value2 || []).length;\n    } else {\n      return (value2 || []).length - (value1 || []).length;\n    }\n  }\n};", "map": {"version": 3, "names": ["sortByProperty", "a", "b", "property", "sortAsc", "value1", "value2", "Object", "prototype", "toString", "call", "localeCompare", "length"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/utils/Comparators.js"], "sourcesContent": ["\r\n// 根据传入的property字段排序，默认升序\r\nexport const sortByProperty = (a, b, property, sortAsc=true) => {\r\n  let value1 = a[property];\r\n  let value2 = b[property];\r\n  \r\n  if (Object.prototype.toString.call(value1) === \"[object String]\") {\r\n    // 字符串\r\n    if(sortAsc){\r\n      return value1.localeCompare(value2);\r\n    } else {\r\n      return value2.localeCompare(value1);\r\n    }\r\n  } else if (Object.prototype.toString.call(value1) === \"[object Number]\") {\r\n    // 数值\r\n    if(sortAsc){\r\n      return value1 - value2;\r\n    } else {\r\n      return value2 - value1;\r\n    }\r\n  } else if (Object.prototype.toString.call(value1) === \"[object Array]\") {\r\n    // 数组\r\n    if (sortAsc) {\r\n      return (value1||[]).length - (value2||[]).length\r\n    }else {\r\n      return (value2||[]).length - (value1||[]).length\r\n    }\r\n  }\r\n}"], "mappings": "AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAEC,OAAO,GAAC,IAAI,KAAK;EAC9D,IAAIC,MAAM,GAAGJ,CAAC,CAACE,QAAQ,CAAC;EACxB,IAAIG,MAAM,GAAGJ,CAAC,CAACC,QAAQ,CAAC;EAExB,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,MAAM,CAAC,KAAK,iBAAiB,EAAE;IAChE;IACA,IAAGD,OAAO,EAAC;MACT,OAAOC,MAAM,CAACM,aAAa,CAACL,MAAM,CAAC;IACrC,CAAC,MAAM;MACL,OAAOA,MAAM,CAACK,aAAa,CAACN,MAAM,CAAC;IACrC;EACF,CAAC,MAAM,IAAIE,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,MAAM,CAAC,KAAK,iBAAiB,EAAE;IACvE;IACA,IAAGD,OAAO,EAAC;MACT,OAAOC,MAAM,GAAGC,MAAM;IACxB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAGD,MAAM;IACxB;EACF,CAAC,MAAM,IAAIE,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,MAAM,CAAC,KAAK,gBAAgB,EAAE;IACtE;IACA,IAAID,OAAO,EAAE;MACX,OAAO,CAACC,MAAM,IAAE,EAAE,EAAEO,MAAM,GAAG,CAACN,MAAM,IAAE,EAAE,EAAEM,MAAM;IAClD,CAAC,MAAK;MACJ,OAAO,CAACN,MAAM,IAAE,EAAE,EAAEM,MAAM,GAAG,CAACP,MAAM,IAAE,EAAE,EAAEO,MAAM;IAClD;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}