{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n// NOTE: V<PERSON><PERSON>'s copy of nodejs path library to be usable in common (non-node) namespace\n// Copied from: https://github.com/nodejs/node/blob/v14.16.0/lib/path.js\n/**\n * Copyright Joyent, Inc. and other Node contributors.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a\n * copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to permit\n * persons to whom the Software is furnished to do so, subject to the\n * following conditions:\n *\n * The above copyright notice and this permission notice shall be included\n * in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n * NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n * DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n * USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\nimport * as process from './process.js';\nconst CHAR_UPPERCASE_A = 65; /* A */\nconst CHAR_LOWERCASE_A = 97; /* a */\nconst CHAR_UPPERCASE_Z = 90; /* Z */\nconst CHAR_LOWERCASE_Z = 122; /* z */\nconst CHAR_DOT = 46; /* . */\nconst CHAR_FORWARD_SLASH = 47; /* / */\nconst CHAR_BACKWARD_SLASH = 92; /* \\ */\nconst CHAR_COLON = 58; /* : */\nconst CHAR_QUESTION_MARK = 63; /* ? */\nclass ErrorInvalidArgType extends Error {\n  constructor(name, expected, actual) {\n    // determiner: 'must be' or 'must not be'\n    let determiner;\n    if (typeof expected === 'string' && expected.indexOf('not ') === 0) {\n      determiner = 'must not be';\n      expected = expected.replace(/^not /, '');\n    } else {\n      determiner = 'must be';\n    }\n    const type = name.indexOf('.') !== -1 ? 'property' : 'argument';\n    let msg = `The \"${name}\" ${type} ${determiner} of type ${expected}`;\n    msg += `. Received type ${typeof actual}`;\n    super(msg);\n    this.code = 'ERR_INVALID_ARG_TYPE';\n  }\n}\nfunction validateString(value, name) {\n  if (typeof value !== 'string') {\n    throw new ErrorInvalidArgType(name, 'string', value);\n  }\n}\nfunction isPathSeparator(code) {\n  return code === CHAR_FORWARD_SLASH || code === CHAR_BACKWARD_SLASH;\n}\nfunction isPosixPathSeparator(code) {\n  return code === CHAR_FORWARD_SLASH;\n}\nfunction isWindowsDeviceRoot(code) {\n  return code >= CHAR_UPPERCASE_A && code <= CHAR_UPPERCASE_Z || code >= CHAR_LOWERCASE_A && code <= CHAR_LOWERCASE_Z;\n}\n// Resolves . and .. elements in a path with directory names\nfunction normalizeString(path, allowAboveRoot, separator, isPathSeparator) {\n  let res = '';\n  let lastSegmentLength = 0;\n  let lastSlash = -1;\n  let dots = 0;\n  let code = 0;\n  for (let i = 0; i <= path.length; ++i) {\n    if (i < path.length) {\n      code = path.charCodeAt(i);\n    } else if (isPathSeparator(code)) {\n      break;\n    } else {\n      code = CHAR_FORWARD_SLASH;\n    }\n    if (isPathSeparator(code)) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== CHAR_DOT || res.charCodeAt(res.length - 2) !== CHAR_DOT) {\n          if (res.length > 2) {\n            const lastSlashIndex = res.lastIndexOf(separator);\n            if (lastSlashIndex === -1) {\n              res = '';\n              lastSegmentLength = 0;\n            } else {\n              res = res.slice(0, lastSlashIndex);\n              lastSegmentLength = res.length - 1 - res.lastIndexOf(separator);\n            }\n            lastSlash = i;\n            dots = 0;\n            continue;\n          } else if (res.length !== 0) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          res += res.length > 0 ? `${separator}..` : '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0) {\n          res += `${separator}${path.slice(lastSlash + 1, i)}`;\n        } else {\n          res = path.slice(lastSlash + 1, i);\n        }\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === CHAR_DOT && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\nfunction _format(sep, pathObject) {\n  if (pathObject === null || typeof pathObject !== 'object') {\n    throw new ErrorInvalidArgType('pathObject', 'Object', pathObject);\n  }\n  const dir = pathObject.dir || pathObject.root;\n  const base = pathObject.base || `${pathObject.name || ''}${pathObject.ext || ''}`;\n  if (!dir) {\n    return base;\n  }\n  return dir === pathObject.root ? `${dir}${base}` : `${dir}${sep}${base}`;\n}\nexport const win32 = {\n  // path.resolve([from ...], to)\n  resolve() {\n    let resolvedDevice = '';\n    let resolvedTail = '';\n    let resolvedAbsolute = false;\n    for (let i = arguments.length - 1; i >= -1; i--) {\n      let path;\n      if (i >= 0) {\n        path = i < 0 || arguments.length <= i ? undefined : arguments[i];\n        validateString(path, 'path');\n        // Skip empty entries\n        if (path.length === 0) {\n          continue;\n        }\n      } else if (resolvedDevice.length === 0) {\n        path = process.cwd();\n      } else {\n        // Windows has the concept of drive-specific current working\n        // directories. If we've resolved a drive letter but not yet an\n        // absolute path, get cwd for that drive, or the process cwd if\n        // the drive cwd is not available. We're sure the device is not\n        // a UNC path at this points, because UNC paths are always absolute.\n        path = process.env[`=${resolvedDevice}`] || process.cwd();\n        // Verify that a cwd was found and that it actually points\n        // to our drive. If not, default to the drive's root.\n        if (path === undefined || path.slice(0, 2).toLowerCase() !== resolvedDevice.toLowerCase() && path.charCodeAt(2) === CHAR_BACKWARD_SLASH) {\n          path = `${resolvedDevice}\\\\`;\n        }\n      }\n      const len = path.length;\n      let rootEnd = 0;\n      let device = '';\n      let isAbsolute = false;\n      const code = path.charCodeAt(0);\n      // Try to match a root\n      if (len === 1) {\n        if (isPathSeparator(code)) {\n          // `path` contains just a path separator\n          rootEnd = 1;\n          isAbsolute = true;\n        }\n      } else if (isPathSeparator(code)) {\n        // Possible UNC root\n        // If we started with a separator, we know we at least have an\n        // absolute path of some kind (UNC or otherwise)\n        isAbsolute = true;\n        if (isPathSeparator(path.charCodeAt(1))) {\n          // Matched double path separator at beginning\n          let j = 2;\n          let last = j;\n          // Match 1 or more non-path separators\n          while (j < len && !isPathSeparator(path.charCodeAt(j))) {\n            j++;\n          }\n          if (j < len && j !== last) {\n            const firstPart = path.slice(last, j);\n            // Matched!\n            last = j;\n            // Match 1 or more path separators\n            while (j < len && isPathSeparator(path.charCodeAt(j))) {\n              j++;\n            }\n            if (j < len && j !== last) {\n              // Matched!\n              last = j;\n              // Match 1 or more non-path separators\n              while (j < len && !isPathSeparator(path.charCodeAt(j))) {\n                j++;\n              }\n              if (j === len || j !== last) {\n                // We matched a UNC root\n                device = `\\\\\\\\${firstPart}\\\\${path.slice(last, j)}`;\n                rootEnd = j;\n              }\n            }\n          }\n        } else {\n          rootEnd = 1;\n        }\n      } else if (isWindowsDeviceRoot(code) && path.charCodeAt(1) === CHAR_COLON) {\n        // Possible device root\n        device = path.slice(0, 2);\n        rootEnd = 2;\n        if (len > 2 && isPathSeparator(path.charCodeAt(2))) {\n          // Treat separator following drive name as an absolute path\n          // indicator\n          isAbsolute = true;\n          rootEnd = 3;\n        }\n      }\n      if (device.length > 0) {\n        if (resolvedDevice.length > 0) {\n          if (device.toLowerCase() !== resolvedDevice.toLowerCase()) {\n            // This path points to another device so it is not applicable\n            continue;\n          }\n        } else {\n          resolvedDevice = device;\n        }\n      }\n      if (resolvedAbsolute) {\n        if (resolvedDevice.length > 0) {\n          break;\n        }\n      } else {\n        resolvedTail = `${path.slice(rootEnd)}\\\\${resolvedTail}`;\n        resolvedAbsolute = isAbsolute;\n        if (isAbsolute && resolvedDevice.length > 0) {\n          break;\n        }\n      }\n    }\n    // At this point the path should be resolved to a full absolute path,\n    // but handle relative paths to be safe (might happen when process.cwd()\n    // fails)\n    // Normalize the tail path\n    resolvedTail = normalizeString(resolvedTail, !resolvedAbsolute, '\\\\', isPathSeparator);\n    return resolvedAbsolute ? `${resolvedDevice}\\\\${resolvedTail}` : `${resolvedDevice}${resolvedTail}` || '.';\n  },\n  normalize(path) {\n    validateString(path, 'path');\n    const len = path.length;\n    if (len === 0) {\n      return '.';\n    }\n    let rootEnd = 0;\n    let device;\n    let isAbsolute = false;\n    const code = path.charCodeAt(0);\n    // Try to match a root\n    if (len === 1) {\n      // `path` contains just a single char, exit early to avoid\n      // unnecessary work\n      return isPosixPathSeparator(code) ? '\\\\' : path;\n    }\n    if (isPathSeparator(code)) {\n      // Possible UNC root\n      // If we started with a separator, we know we at least have an absolute\n      // path of some kind (UNC or otherwise)\n      isAbsolute = true;\n      if (isPathSeparator(path.charCodeAt(1))) {\n        // Matched double path separator at beginning\n        let j = 2;\n        let last = j;\n        // Match 1 or more non-path separators\n        while (j < len && !isPathSeparator(path.charCodeAt(j))) {\n          j++;\n        }\n        if (j < len && j !== last) {\n          const firstPart = path.slice(last, j);\n          // Matched!\n          last = j;\n          // Match 1 or more path separators\n          while (j < len && isPathSeparator(path.charCodeAt(j))) {\n            j++;\n          }\n          if (j < len && j !== last) {\n            // Matched!\n            last = j;\n            // Match 1 or more non-path separators\n            while (j < len && !isPathSeparator(path.charCodeAt(j))) {\n              j++;\n            }\n            if (j === len) {\n              // We matched a UNC root only\n              // Return the normalized version of the UNC root since there\n              // is nothing left to process\n              return `\\\\\\\\${firstPart}\\\\${path.slice(last)}\\\\`;\n            }\n            if (j !== last) {\n              // We matched a UNC root with leftovers\n              device = `\\\\\\\\${firstPart}\\\\${path.slice(last, j)}`;\n              rootEnd = j;\n            }\n          }\n        }\n      } else {\n        rootEnd = 1;\n      }\n    } else if (isWindowsDeviceRoot(code) && path.charCodeAt(1) === CHAR_COLON) {\n      // Possible device root\n      device = path.slice(0, 2);\n      rootEnd = 2;\n      if (len > 2 && isPathSeparator(path.charCodeAt(2))) {\n        // Treat separator following drive name as an absolute path\n        // indicator\n        isAbsolute = true;\n        rootEnd = 3;\n      }\n    }\n    let tail = rootEnd < len ? normalizeString(path.slice(rootEnd), !isAbsolute, '\\\\', isPathSeparator) : '';\n    if (tail.length === 0 && !isAbsolute) {\n      tail = '.';\n    }\n    if (tail.length > 0 && isPathSeparator(path.charCodeAt(len - 1))) {\n      tail += '\\\\';\n    }\n    if (device === undefined) {\n      return isAbsolute ? `\\\\${tail}` : tail;\n    }\n    return isAbsolute ? `${device}\\\\${tail}` : `${device}${tail}`;\n  },\n  isAbsolute(path) {\n    validateString(path, 'path');\n    const len = path.length;\n    if (len === 0) {\n      return false;\n    }\n    const code = path.charCodeAt(0);\n    return isPathSeparator(code) ||\n    // Possible device root\n    len > 2 && isWindowsDeviceRoot(code) && path.charCodeAt(1) === CHAR_COLON && isPathSeparator(path.charCodeAt(2));\n  },\n  join() {\n    if (arguments.length === 0) {\n      return '.';\n    }\n    let joined;\n    let firstPart;\n    for (let i = 0; i < arguments.length; ++i) {\n      const arg = i < 0 || arguments.length <= i ? undefined : arguments[i];\n      validateString(arg, 'path');\n      if (arg.length > 0) {\n        if (joined === undefined) {\n          joined = firstPart = arg;\n        } else {\n          joined += `\\\\${arg}`;\n        }\n      }\n    }\n    if (joined === undefined) {\n      return '.';\n    }\n    // Make sure that the joined path doesn't start with two slashes, because\n    // normalize() will mistake it for a UNC path then.\n    //\n    // This step is skipped when it is very clear that the user actually\n    // intended to point at a UNC path. This is assumed when the first\n    // non-empty string arguments starts with exactly two slashes followed by\n    // at least one more non-slash character.\n    //\n    // Note that for normalize() to treat a path as a UNC path it needs to\n    // have at least 2 components, so we don't filter for that here.\n    // This means that the user can use join to construct UNC paths from\n    // a server name and a share name; for example:\n    //   path.join('//server', 'share') -> '\\\\\\\\server\\\\share\\\\')\n    let needsReplace = true;\n    let slashCount = 0;\n    if (typeof firstPart === 'string' && isPathSeparator(firstPart.charCodeAt(0))) {\n      ++slashCount;\n      const firstLen = firstPart.length;\n      if (firstLen > 1 && isPathSeparator(firstPart.charCodeAt(1))) {\n        ++slashCount;\n        if (firstLen > 2) {\n          if (isPathSeparator(firstPart.charCodeAt(2))) {\n            ++slashCount;\n          } else {\n            // We matched a UNC path in the first part\n            needsReplace = false;\n          }\n        }\n      }\n    }\n    if (needsReplace) {\n      // Find any more consecutive slashes we need to replace\n      while (slashCount < joined.length && isPathSeparator(joined.charCodeAt(slashCount))) {\n        slashCount++;\n      }\n      // Replace the slashes if needed\n      if (slashCount >= 2) {\n        joined = `\\\\${joined.slice(slashCount)}`;\n      }\n    }\n    return win32.normalize(joined);\n  },\n  // It will solve the relative path from `from` to `to`, for instance:\n  //  from = 'C:\\\\orandea\\\\test\\\\aaa'\n  //  to = 'C:\\\\orandea\\\\impl\\\\bbb'\n  // The output of the function should be: '..\\\\..\\\\impl\\\\bbb'\n  relative(from, to) {\n    validateString(from, 'from');\n    validateString(to, 'to');\n    if (from === to) {\n      return '';\n    }\n    const fromOrig = win32.resolve(from);\n    const toOrig = win32.resolve(to);\n    if (fromOrig === toOrig) {\n      return '';\n    }\n    from = fromOrig.toLowerCase();\n    to = toOrig.toLowerCase();\n    if (from === to) {\n      return '';\n    }\n    // Trim any leading backslashes\n    let fromStart = 0;\n    while (fromStart < from.length && from.charCodeAt(fromStart) === CHAR_BACKWARD_SLASH) {\n      fromStart++;\n    }\n    // Trim trailing backslashes (applicable to UNC paths only)\n    let fromEnd = from.length;\n    while (fromEnd - 1 > fromStart && from.charCodeAt(fromEnd - 1) === CHAR_BACKWARD_SLASH) {\n      fromEnd--;\n    }\n    const fromLen = fromEnd - fromStart;\n    // Trim any leading backslashes\n    let toStart = 0;\n    while (toStart < to.length && to.charCodeAt(toStart) === CHAR_BACKWARD_SLASH) {\n      toStart++;\n    }\n    // Trim trailing backslashes (applicable to UNC paths only)\n    let toEnd = to.length;\n    while (toEnd - 1 > toStart && to.charCodeAt(toEnd - 1) === CHAR_BACKWARD_SLASH) {\n      toEnd--;\n    }\n    const toLen = toEnd - toStart;\n    // Compare paths to find the longest common path from root\n    const length = fromLen < toLen ? fromLen : toLen;\n    let lastCommonSep = -1;\n    let i = 0;\n    for (; i < length; i++) {\n      const fromCode = from.charCodeAt(fromStart + i);\n      if (fromCode !== to.charCodeAt(toStart + i)) {\n        break;\n      } else if (fromCode === CHAR_BACKWARD_SLASH) {\n        lastCommonSep = i;\n      }\n    }\n    // We found a mismatch before the first common path separator was seen, so\n    // return the original `to`.\n    if (i !== length) {\n      if (lastCommonSep === -1) {\n        return toOrig;\n      }\n    } else {\n      if (toLen > length) {\n        if (to.charCodeAt(toStart + i) === CHAR_BACKWARD_SLASH) {\n          // We get here if `from` is the exact base path for `to`.\n          // For example: from='C:\\\\foo\\\\bar'; to='C:\\\\foo\\\\bar\\\\baz'\n          return toOrig.slice(toStart + i + 1);\n        }\n        if (i === 2) {\n          // We get here if `from` is the device root.\n          // For example: from='C:\\\\'; to='C:\\\\foo'\n          return toOrig.slice(toStart + i);\n        }\n      }\n      if (fromLen > length) {\n        if (from.charCodeAt(fromStart + i) === CHAR_BACKWARD_SLASH) {\n          // We get here if `to` is the exact base path for `from`.\n          // For example: from='C:\\\\foo\\\\bar'; to='C:\\\\foo'\n          lastCommonSep = i;\n        } else if (i === 2) {\n          // We get here if `to` is the device root.\n          // For example: from='C:\\\\foo\\\\bar'; to='C:\\\\'\n          lastCommonSep = 3;\n        }\n      }\n      if (lastCommonSep === -1) {\n        lastCommonSep = 0;\n      }\n    }\n    let out = '';\n    // Generate the relative path based on the path difference between `to` and\n    // `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === CHAR_BACKWARD_SLASH) {\n        out += out.length === 0 ? '..' : '\\\\..';\n      }\n    }\n    toStart += lastCommonSep;\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0) {\n      return `${out}${toOrig.slice(toStart, toEnd)}`;\n    }\n    if (toOrig.charCodeAt(toStart) === CHAR_BACKWARD_SLASH) {\n      ++toStart;\n    }\n    return toOrig.slice(toStart, toEnd);\n  },\n  toNamespacedPath(path) {\n    // Note: this will *probably* throw somewhere.\n    if (typeof path !== 'string') {\n      return path;\n    }\n    if (path.length === 0) {\n      return '';\n    }\n    const resolvedPath = win32.resolve(path);\n    if (resolvedPath.length <= 2) {\n      return path;\n    }\n    if (resolvedPath.charCodeAt(0) === CHAR_BACKWARD_SLASH) {\n      // Possible UNC root\n      if (resolvedPath.charCodeAt(1) === CHAR_BACKWARD_SLASH) {\n        const code = resolvedPath.charCodeAt(2);\n        if (code !== CHAR_QUESTION_MARK && code !== CHAR_DOT) {\n          // Matched non-long UNC root, convert the path to a long UNC path\n          return `\\\\\\\\?\\\\UNC\\\\${resolvedPath.slice(2)}`;\n        }\n      }\n    } else if (isWindowsDeviceRoot(resolvedPath.charCodeAt(0)) && resolvedPath.charCodeAt(1) === CHAR_COLON && resolvedPath.charCodeAt(2) === CHAR_BACKWARD_SLASH) {\n      // Matched device root, convert the path to a long UNC path\n      return `\\\\\\\\?\\\\${resolvedPath}`;\n    }\n    return path;\n  },\n  dirname(path) {\n    validateString(path, 'path');\n    const len = path.length;\n    if (len === 0) {\n      return '.';\n    }\n    let rootEnd = -1;\n    let offset = 0;\n    const code = path.charCodeAt(0);\n    if (len === 1) {\n      // `path` contains just a path separator, exit early to avoid\n      // unnecessary work or a dot.\n      return isPathSeparator(code) ? path : '.';\n    }\n    // Try to match a root\n    if (isPathSeparator(code)) {\n      // Possible UNC root\n      rootEnd = offset = 1;\n      if (isPathSeparator(path.charCodeAt(1))) {\n        // Matched double path separator at beginning\n        let j = 2;\n        let last = j;\n        // Match 1 or more non-path separators\n        while (j < len && !isPathSeparator(path.charCodeAt(j))) {\n          j++;\n        }\n        if (j < len && j !== last) {\n          // Matched!\n          last = j;\n          // Match 1 or more path separators\n          while (j < len && isPathSeparator(path.charCodeAt(j))) {\n            j++;\n          }\n          if (j < len && j !== last) {\n            // Matched!\n            last = j;\n            // Match 1 or more non-path separators\n            while (j < len && !isPathSeparator(path.charCodeAt(j))) {\n              j++;\n            }\n            if (j === len) {\n              // We matched a UNC root only\n              return path;\n            }\n            if (j !== last) {\n              // We matched a UNC root with leftovers\n              // Offset by 1 to include the separator after the UNC root to\n              // treat it as a \"normal root\" on top of a (UNC) root\n              rootEnd = offset = j + 1;\n            }\n          }\n        }\n      }\n      // Possible device root\n    } else if (isWindowsDeviceRoot(code) && path.charCodeAt(1) === CHAR_COLON) {\n      rootEnd = len > 2 && isPathSeparator(path.charCodeAt(2)) ? 3 : 2;\n      offset = rootEnd;\n    }\n    let end = -1;\n    let matchedSlash = true;\n    for (let i = len - 1; i >= offset; --i) {\n      if (isPathSeparator(path.charCodeAt(i))) {\n        if (!matchedSlash) {\n          end = i;\n          break;\n        }\n      } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n    if (end === -1) {\n      if (rootEnd === -1) {\n        return '.';\n      }\n      end = rootEnd;\n    }\n    return path.slice(0, end);\n  },\n  basename(path, ext) {\n    if (ext !== undefined) {\n      validateString(ext, 'ext');\n    }\n    validateString(path, 'path');\n    let start = 0;\n    let end = -1;\n    let matchedSlash = true;\n    let i;\n    // Check for a drive letter prefix so as not to mistake the following\n    // path separator as an extra separator at the end of the path that can be\n    // disregarded\n    if (path.length >= 2 && isWindowsDeviceRoot(path.charCodeAt(0)) && path.charCodeAt(1) === CHAR_COLON) {\n      start = 2;\n    }\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext === path) {\n        return '';\n      }\n      let extIdx = ext.length - 1;\n      let firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= start; --i) {\n        const code = path.charCodeAt(i);\n        if (isPathSeparator(code)) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            start = i + 1;\n            break;\n          }\n        } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n      if (start === end) {\n        end = firstNonSlashEnd;\n      } else if (end === -1) {\n        end = path.length;\n      }\n      return path.slice(start, end);\n    }\n    for (i = path.length - 1; i >= start; --i) {\n      if (isPathSeparator(path.charCodeAt(i))) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now\n        if (!matchedSlash) {\n          start = i + 1;\n          break;\n        }\n      } else if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // path component\n        matchedSlash = false;\n        end = i + 1;\n      }\n    }\n    if (end === -1) {\n      return '';\n    }\n    return path.slice(start, end);\n  },\n  extname(path) {\n    validateString(path, 'path');\n    let start = 0;\n    let startDot = -1;\n    let startPart = 0;\n    let end = -1;\n    let matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    let preDotState = 0;\n    // Check for a drive letter prefix so as not to mistake the following\n    // path separator as an extra separator at the end of the path that can be\n    // disregarded\n    if (path.length >= 2 && path.charCodeAt(1) === CHAR_COLON && isWindowsDeviceRoot(path.charCodeAt(0))) {\n      start = startPart = 2;\n    }\n    for (let i = path.length - 1; i >= start; --i) {\n      const code = path.charCodeAt(i);\n      if (isPathSeparator(code)) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now\n        if (!matchedSlash) {\n          startPart = i + 1;\n          break;\n        }\n        continue;\n      }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === CHAR_DOT) {\n        // If this is our first dot, mark it as the start of our extension\n        if (startDot === -1) {\n          startDot = i;\n        } else if (preDotState !== 1) {\n          preDotState = 1;\n        }\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n  format: _format.bind(null, '\\\\'),\n  parse(path) {\n    validateString(path, 'path');\n    const ret = {\n      root: '',\n      dir: '',\n      base: '',\n      ext: '',\n      name: ''\n    };\n    if (path.length === 0) {\n      return ret;\n    }\n    const len = path.length;\n    let rootEnd = 0;\n    let code = path.charCodeAt(0);\n    if (len === 1) {\n      if (isPathSeparator(code)) {\n        // `path` contains just a path separator, exit early to avoid\n        // unnecessary work\n        ret.root = ret.dir = path;\n        return ret;\n      }\n      ret.base = ret.name = path;\n      return ret;\n    }\n    // Try to match a root\n    if (isPathSeparator(code)) {\n      // Possible UNC root\n      rootEnd = 1;\n      if (isPathSeparator(path.charCodeAt(1))) {\n        // Matched double path separator at beginning\n        let j = 2;\n        let last = j;\n        // Match 1 or more non-path separators\n        while (j < len && !isPathSeparator(path.charCodeAt(j))) {\n          j++;\n        }\n        if (j < len && j !== last) {\n          // Matched!\n          last = j;\n          // Match 1 or more path separators\n          while (j < len && isPathSeparator(path.charCodeAt(j))) {\n            j++;\n          }\n          if (j < len && j !== last) {\n            // Matched!\n            last = j;\n            // Match 1 or more non-path separators\n            while (j < len && !isPathSeparator(path.charCodeAt(j))) {\n              j++;\n            }\n            if (j === len) {\n              // We matched a UNC root only\n              rootEnd = j;\n            } else if (j !== last) {\n              // We matched a UNC root with leftovers\n              rootEnd = j + 1;\n            }\n          }\n        }\n      }\n    } else if (isWindowsDeviceRoot(code) && path.charCodeAt(1) === CHAR_COLON) {\n      // Possible device root\n      if (len <= 2) {\n        // `path` contains just a drive root, exit early to avoid\n        // unnecessary work\n        ret.root = ret.dir = path;\n        return ret;\n      }\n      rootEnd = 2;\n      if (isPathSeparator(path.charCodeAt(2))) {\n        if (len === 3) {\n          // `path` contains just a drive root, exit early to avoid\n          // unnecessary work\n          ret.root = ret.dir = path;\n          return ret;\n        }\n        rootEnd = 3;\n      }\n    }\n    if (rootEnd > 0) {\n      ret.root = path.slice(0, rootEnd);\n    }\n    let startDot = -1;\n    let startPart = rootEnd;\n    let end = -1;\n    let matchedSlash = true;\n    let i = path.length - 1;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    let preDotState = 0;\n    // Get non-dir info\n    for (; i >= rootEnd; --i) {\n      code = path.charCodeAt(i);\n      if (isPathSeparator(code)) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now\n        if (!matchedSlash) {\n          startPart = i + 1;\n          break;\n        }\n        continue;\n      }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === CHAR_DOT) {\n        // If this is our first dot, mark it as the start of our extension\n        if (startDot === -1) {\n          startDot = i;\n        } else if (preDotState !== 1) {\n          preDotState = 1;\n        }\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n    if (end !== -1) {\n      if (startDot === -1 ||\n      // We saw a non-dot character immediately before the dot\n      preDotState === 0 ||\n      // The (right-most) trimmed path component is exactly '..'\n      preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n        ret.base = ret.name = path.slice(startPart, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n        ret.ext = path.slice(startDot, end);\n      }\n    }\n    // If the directory is the root, use the entire root as the `dir` including\n    // the trailing slash if any (`C:\\abc` -> `C:\\`). Otherwise, strip out the\n    // trailing slash (`C:\\abc\\def` -> `C:\\abc`).\n    if (startPart > 0 && startPart !== rootEnd) {\n      ret.dir = path.slice(0, startPart - 1);\n    } else {\n      ret.dir = ret.root;\n    }\n    return ret;\n  },\n  sep: '\\\\',\n  delimiter: ';',\n  win32: null,\n  posix: null\n};\nexport const posix = {\n  // path.resolve([from ...], to)\n  resolve() {\n    let resolvedPath = '';\n    let resolvedAbsolute = false;\n    for (let i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      const path = i >= 0 ? i < 0 || arguments.length <= i ? undefined : arguments[i] : process.cwd();\n      validateString(path, 'path');\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n      resolvedPath = `${path}/${resolvedPath}`;\n      resolvedAbsolute = path.charCodeAt(0) === CHAR_FORWARD_SLASH;\n    }\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n    // Normalize the path\n    resolvedPath = normalizeString(resolvedPath, !resolvedAbsolute, '/', isPosixPathSeparator);\n    if (resolvedAbsolute) {\n      return `/${resolvedPath}`;\n    }\n    return resolvedPath.length > 0 ? resolvedPath : '.';\n  },\n  normalize(path) {\n    validateString(path, 'path');\n    if (path.length === 0) {\n      return '.';\n    }\n    const isAbsolute = path.charCodeAt(0) === CHAR_FORWARD_SLASH;\n    const trailingSeparator = path.charCodeAt(path.length - 1) === CHAR_FORWARD_SLASH;\n    // Normalize the path\n    path = normalizeString(path, !isAbsolute, '/', isPosixPathSeparator);\n    if (path.length === 0) {\n      if (isAbsolute) {\n        return '/';\n      }\n      return trailingSeparator ? './' : '.';\n    }\n    if (trailingSeparator) {\n      path += '/';\n    }\n    return isAbsolute ? `/${path}` : path;\n  },\n  isAbsolute(path) {\n    validateString(path, 'path');\n    return path.length > 0 && path.charCodeAt(0) === CHAR_FORWARD_SLASH;\n  },\n  join() {\n    if (arguments.length === 0) {\n      return '.';\n    }\n    let joined;\n    for (let i = 0; i < arguments.length; ++i) {\n      const arg = i < 0 || arguments.length <= i ? undefined : arguments[i];\n      validateString(arg, 'path');\n      if (arg.length > 0) {\n        if (joined === undefined) {\n          joined = arg;\n        } else {\n          joined += `/${arg}`;\n        }\n      }\n    }\n    if (joined === undefined) {\n      return '.';\n    }\n    return posix.normalize(joined);\n  },\n  relative(from, to) {\n    validateString(from, 'from');\n    validateString(to, 'to');\n    if (from === to) {\n      return '';\n    }\n    // Trim leading forward slashes.\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n    if (from === to) {\n      return '';\n    }\n    const fromStart = 1;\n    const fromEnd = from.length;\n    const fromLen = fromEnd - fromStart;\n    const toStart = 1;\n    const toLen = to.length - toStart;\n    // Compare paths to find the longest common path from root\n    const length = fromLen < toLen ? fromLen : toLen;\n    let lastCommonSep = -1;\n    let i = 0;\n    for (; i < length; i++) {\n      const fromCode = from.charCodeAt(fromStart + i);\n      if (fromCode !== to.charCodeAt(toStart + i)) {\n        break;\n      } else if (fromCode === CHAR_FORWARD_SLASH) {\n        lastCommonSep = i;\n      }\n    }\n    if (i === length) {\n      if (toLen > length) {\n        if (to.charCodeAt(toStart + i) === CHAR_FORWARD_SLASH) {\n          // We get here if `from` is the exact base path for `to`.\n          // For example: from='/foo/bar'; to='/foo/bar/baz'\n          return to.slice(toStart + i + 1);\n        }\n        if (i === 0) {\n          // We get here if `from` is the root\n          // For example: from='/'; to='/foo'\n          return to.slice(toStart + i);\n        }\n      } else if (fromLen > length) {\n        if (from.charCodeAt(fromStart + i) === CHAR_FORWARD_SLASH) {\n          // We get here if `to` is the exact base path for `from`.\n          // For example: from='/foo/bar/baz'; to='/foo/bar'\n          lastCommonSep = i;\n        } else if (i === 0) {\n          // We get here if `to` is the root.\n          // For example: from='/foo/bar'; to='/'\n          lastCommonSep = 0;\n        }\n      }\n    }\n    let out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`.\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === CHAR_FORWARD_SLASH) {\n        out += out.length === 0 ? '..' : '/..';\n      }\n    }\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts.\n    return `${out}${to.slice(toStart + lastCommonSep)}`;\n  },\n  toNamespacedPath(path) {\n    // Non-op on posix systems\n    return path;\n  },\n  dirname(path) {\n    validateString(path, 'path');\n    if (path.length === 0) {\n      return '.';\n    }\n    const hasRoot = path.charCodeAt(0) === CHAR_FORWARD_SLASH;\n    let end = -1;\n    let matchedSlash = true;\n    for (let i = path.length - 1; i >= 1; --i) {\n      if (path.charCodeAt(i) === CHAR_FORWARD_SLASH) {\n        if (!matchedSlash) {\n          end = i;\n          break;\n        }\n      } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n    if (end === -1) {\n      return hasRoot ? '/' : '.';\n    }\n    if (hasRoot && end === 1) {\n      return '//';\n    }\n    return path.slice(0, end);\n  },\n  basename(path, ext) {\n    if (ext !== undefined) {\n      validateString(ext, 'ext');\n    }\n    validateString(path, 'path');\n    let start = 0;\n    let end = -1;\n    let matchedSlash = true;\n    let i;\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext === path) {\n        return '';\n      }\n      let extIdx = ext.length - 1;\n      let firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        const code = path.charCodeAt(i);\n        if (code === CHAR_FORWARD_SLASH) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            start = i + 1;\n            break;\n          }\n        } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n      if (start === end) {\n        end = firstNonSlashEnd;\n      } else if (end === -1) {\n        end = path.length;\n      }\n      return path.slice(start, end);\n    }\n    for (i = path.length - 1; i >= 0; --i) {\n      if (path.charCodeAt(i) === CHAR_FORWARD_SLASH) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now\n        if (!matchedSlash) {\n          start = i + 1;\n          break;\n        }\n      } else if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // path component\n        matchedSlash = false;\n        end = i + 1;\n      }\n    }\n    if (end === -1) {\n      return '';\n    }\n    return path.slice(start, end);\n  },\n  extname(path) {\n    validateString(path, 'path');\n    let startDot = -1;\n    let startPart = 0;\n    let end = -1;\n    let matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    let preDotState = 0;\n    for (let i = path.length - 1; i >= 0; --i) {\n      const code = path.charCodeAt(i);\n      if (code === CHAR_FORWARD_SLASH) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now\n        if (!matchedSlash) {\n          startPart = i + 1;\n          break;\n        }\n        continue;\n      }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === CHAR_DOT) {\n        // If this is our first dot, mark it as the start of our extension\n        if (startDot === -1) {\n          startDot = i;\n        } else if (preDotState !== 1) {\n          preDotState = 1;\n        }\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n  format: _format.bind(null, '/'),\n  parse(path) {\n    validateString(path, 'path');\n    const ret = {\n      root: '',\n      dir: '',\n      base: '',\n      ext: '',\n      name: ''\n    };\n    if (path.length === 0) {\n      return ret;\n    }\n    const isAbsolute = path.charCodeAt(0) === CHAR_FORWARD_SLASH;\n    let start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    let startDot = -1;\n    let startPart = 0;\n    let end = -1;\n    let matchedSlash = true;\n    let i = path.length - 1;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    let preDotState = 0;\n    // Get non-dir info\n    for (; i >= start; --i) {\n      const code = path.charCodeAt(i);\n      if (code === CHAR_FORWARD_SLASH) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now\n        if (!matchedSlash) {\n          startPart = i + 1;\n          break;\n        }\n        continue;\n      }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === CHAR_DOT) {\n        // If this is our first dot, mark it as the start of our extension\n        if (startDot === -1) {\n          startDot = i;\n        } else if (preDotState !== 1) {\n          preDotState = 1;\n        }\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n    if (end !== -1) {\n      const start = startPart === 0 && isAbsolute ? 1 : startPart;\n      if (startDot === -1 ||\n      // We saw a non-dot character immediately before the dot\n      preDotState === 0 ||\n      // The (right-most) trimmed path component is exactly '..'\n      preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n        ret.base = ret.name = path.slice(start, end);\n      } else {\n        ret.name = path.slice(start, startDot);\n        ret.base = path.slice(start, end);\n        ret.ext = path.slice(startDot, end);\n      }\n    }\n    if (startPart > 0) {\n      ret.dir = path.slice(0, startPart - 1);\n    } else if (isAbsolute) {\n      ret.dir = '/';\n    }\n    return ret;\n  },\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\nposix.win32 = win32.win32 = win32;\nposix.posix = win32.posix = posix;\nexport const normalize = process.platform === 'win32' ? win32.normalize : posix.normalize;\nexport const resolve = process.platform === 'win32' ? win32.resolve : posix.resolve;\nexport const relative = process.platform === 'win32' ? win32.relative : posix.relative;\nexport const dirname = process.platform === 'win32' ? win32.dirname : posix.dirname;\nexport const basename = process.platform === 'win32' ? win32.basename : posix.basename;\nexport const extname = process.platform === 'win32' ? win32.extname : posix.extname;\nexport const sep = process.platform === 'win32' ? win32.sep : posix.sep;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}