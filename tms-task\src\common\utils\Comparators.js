
// 根据传入的property字段排序，默认升序
export const sortByProperty = (a, b, property, sortAsc=true) => {
  let value1 = a[property];
  let value2 = b[property];
  
  if (Object.prototype.toString.call(value1) === "[object String]") {
    // 字符串
    if(sortAsc){
      return value1.localeCompare(value2);
    } else {
      return value2.localeCompare(value1);
    }
  } else if (Object.prototype.toString.call(value1) === "[object Number]") {
    // 数值
    if(sortAsc){
      return value1 - value2;
    } else {
      return value2 - value1;
    }
  } else if (Object.prototype.toString.call(value1) === "[object Array]") {
    // 数组
    if (sortAsc) {
      return (value1||[]).length - (value2||[]).length
    }else {
      return (value2||[]).length - (value1||[]).length
    }
  }
}