{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\CheckAppUpdate.jsx\",\n  _s = $RefreshSig$();\nimport { local_check_version_update } from \"@common/api/http\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport React, { useEffect, useState } from \"react\";\nimport { Modal, Divider, Typography } from \"antd\";\nimport * as storageConstant from \"@common/utils/storageConstant\";\nimport { eEnableFlg } from \"@common/utils/enum\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Text\n} = Typography;\nexport default function CheckAppUpdate() {\n  _s();\n  const [systemUpdateTips, setSystemUpdateTips] = useState(true);\n\n  // 版本更新\n  const versionQuery = useQuery({\n    queryFn: async () => {\n      return local_check_version_update({\n        timestamp: Date.now()\n      }).then(result => {\n        return {\n          version: result.version || process.env.REACT_APP_VERSION,\n          changelog: result.changelog || null\n        };\n      });\n    },\n    enabled: process.env.NODE_ENV === \"production\" && localStorage.getItem(storageConstant.no_polling) != eEnableFlg.enable,\n    cacheTime: Infinity,\n    // 每15秒会刷新一次接口\n    refetchInterval: 15000,\n    refetchOnWindowFocus: true\n  });\n\n  // 渲染更新日志内容\n  const renderChangelogContent = (changelog, newVersion) => {\n    if (!changelog || !changelog.versions || !changelog.versions[newVersion]) {\n      return null;\n    }\n    const versionInfo = changelog.versions[newVersion];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        orientation: \"left\",\n        style: {\n          margin: '12px 0 8px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            fontSize: 14\n          },\n          children: \"\\u66F4\\u65B0\\u5185\\u5BB9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), versionInfo.features && versionInfo.features.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 12\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            color: '#52c41a',\n            fontSize: 13\n          },\n          children: \"\\u2728 \\u65B0\\u529F\\u80FD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: '4px 0 0 0',\n            paddingLeft: 20\n          },\n          children: versionInfo.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            style: {\n              fontSize: 12,\n              lineHeight: '18px',\n              marginBottom: 2\n            },\n            children: feature\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this), versionInfo.improvements && versionInfo.improvements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 12\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            color: '#1890ff',\n            fontSize: 13\n          },\n          children: \"\\uD83D\\uDE80 \\u4F18\\u5316\\u6539\\u8FDB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: '4px 0 0 0',\n            paddingLeft: 20\n          },\n          children: versionInfo.improvements.map((improvement, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            style: {\n              fontSize: 12,\n              lineHeight: '18px',\n              marginBottom: 2\n            },\n            children: improvement\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this), versionInfo.bugfixes && versionInfo.bugfixes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            color: '#ff4d4f',\n            fontSize: 13\n          },\n          children: \"\\uD83D\\uDC1B \\u95EE\\u9898\\u4FEE\\u590D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: '4px 0 0 0',\n            paddingLeft: 20\n          },\n          children: versionInfo.bugfixes.map((bugfix, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            style: {\n              fontSize: 12,\n              lineHeight: '18px',\n              marginBottom: 2\n            },\n            children: bugfix\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this), versionInfo.releaseDate && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 12,\n          textAlign: 'right'\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: 11\n          },\n          children: [\"\\u53D1\\u5E03\\u65F6\\u95F4: \", versionInfo.releaseDate]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this);\n  };\n  useEffect(() => {\n    if (versionQuery.dataUpdatedAt && systemUpdateTips && versionQuery.data && Number(versionQuery.data.version) !== Number(process.env.REACT_APP_VERSION)) {\n      setSystemUpdateTips(false);\n      Modal.confirm({\n        title: \"新版本更新\",\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u5F53\\u524D\\u7248\\u672C \", process.env.REACT_APP_VERSION]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u68C0\\u6D4B\\u5230\\u6700\\u65B0\\u7248\\u672C \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#fa86d9'\n              },\n              children: versionQuery.data\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u70B9\\u51FB\\u201C\\u66F4\\u65B0\\u201D\\u6309\\u94AE\\u6216\\u8005\\u5237\\u65B0\\u9875\\u9762\\uFF0C\\u8BBF\\u95EE\\u6700\\u65B0\\u7248\\u672C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 18\n        }, this),\n        okText: '更新',\n        cancelText: '暂不更新',\n        onOk: () => {\n          window.location.reload(true);\n        }\n      });\n    }\n  }, [versionQuery.dataUpdatedAt]);\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 10\n  }, this);\n}\n_s(CheckAppUpdate, \"hLIbqKfh0DdA5BjQkbCCXGtZ314=\", false, function () {\n  return [useQuery];\n});\n_c = CheckAppUpdate;\nvar _c;\n$RefreshReg$(_c, \"CheckAppUpdate\");", "map": {"version": 3, "names": ["local_check_version_update", "useQuery", "React", "useEffect", "useState", "Modal", "Divider", "Typography", "storageConstant", "eEnableFlg", "jsxDEV", "_jsxDEV", "Text", "CheckAppUpdate", "_s", "systemUpdateTips", "setSystemUpdateTips", "versionQuery", "queryFn", "timestamp", "Date", "now", "then", "result", "version", "process", "env", "REACT_APP_VERSION", "changelog", "enabled", "NODE_ENV", "localStorage", "getItem", "no_polling", "enable", "cacheTime", "Infinity", "refetchInterval", "refetchOnWindowFocus", "renderChangelogContent", "newVersion", "versions", "versionInfo", "style", "marginTop", "children", "orientation", "margin", "strong", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "features", "length", "marginBottom", "color", "paddingLeft", "map", "feature", "index", "lineHeight", "improvements", "improvement", "bugfixes", "bugfix", "releaseDate", "textAlign", "type", "dataUpdatedAt", "data", "Number", "confirm", "title", "content", "okText", "cancelText", "onOk", "window", "location", "reload", "Fragment", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/CheckAppUpdate.jsx"], "sourcesContent": ["import { local_check_version_update } from \"@common/api/http\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Modal, Divider, Typography } from \"antd\";\r\nimport * as storageConstant from \"@common/utils/storageConstant\";\r\nimport { eEnableFlg } from \"@common/utils/enum\";\r\n\r\nconst { Text } = Typography;\r\n\r\nexport default function CheckAppUpdate() {\r\n  const [systemUpdateTips,setSystemUpdateTips] = useState(true);\r\n\r\n  // 版本更新\r\n  const versionQuery = useQuery({\r\n    queryFn: async() => {\r\n      return local_check_version_update({timestamp: Date.now()}).then((result) => {\r\n        return {\r\n          version: result.version || process.env.REACT_APP_VERSION,\r\n          changelog: result.changelog || null\r\n        }\r\n      })\r\n    },\r\n    enabled: process.env.NODE_ENV === \"production\" && (localStorage.getItem(storageConstant.no_polling ) != eEnableFlg.enable),\r\n    cacheTime: Infinity,\r\n    // 每15秒会刷新一次接口\r\n    refetchInterval: 15000,\r\n    refetchOnWindowFocus: true\r\n  })\r\n\r\n  // 渲染更新日志内容\r\n  const renderChangelogContent = (changelog, newVersion) => {\r\n    if (!changelog || !changelog.versions || !changelog.versions[newVersion]) {\r\n      return null;\r\n    }\r\n\r\n    const versionInfo = changelog.versions[newVersion];\r\n\r\n    return (\r\n      <div style={{ marginTop: 16 }}>\r\n        <Divider orientation=\"left\" style={{ margin: '12px 0 8px 0' }}>\r\n          <Text strong style={{ fontSize: 14 }}>更新内容</Text>\r\n        </Divider>\r\n\r\n        {versionInfo.features && versionInfo.features.length > 0 && (\r\n          <div style={{ marginBottom: 12 }}>\r\n            <Text strong style={{ color: '#52c41a', fontSize: 13 }}>✨ 新功能</Text>\r\n            <ul style={{ margin: '4px 0 0 0', paddingLeft: 20 }}>\r\n              {versionInfo.features.map((feature, index) => (\r\n                <li key={index} style={{ fontSize: 12, lineHeight: '18px', marginBottom: 2 }}>\r\n                  {feature}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        )}\r\n\r\n        {versionInfo.improvements && versionInfo.improvements.length > 0 && (\r\n          <div style={{ marginBottom: 12 }}>\r\n            <Text strong style={{ color: '#1890ff', fontSize: 13 }}>🚀 优化改进</Text>\r\n            <ul style={{ margin: '4px 0 0 0', paddingLeft: 20 }}>\r\n              {versionInfo.improvements.map((improvement, index) => (\r\n                <li key={index} style={{ fontSize: 12, lineHeight: '18px', marginBottom: 2 }}>\r\n                  {improvement}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        )}\r\n\r\n        {versionInfo.bugfixes && versionInfo.bugfixes.length > 0 && (\r\n          <div style={{ marginBottom: 8 }}>\r\n            <Text strong style={{ color: '#ff4d4f', fontSize: 13 }}>🐛 问题修复</Text>\r\n            <ul style={{ margin: '4px 0 0 0', paddingLeft: 20 }}>\r\n              {versionInfo.bugfixes.map((bugfix, index) => (\r\n                <li key={index} style={{ fontSize: 12, lineHeight: '18px', marginBottom: 2 }}>\r\n                  {bugfix}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        )}\r\n\r\n        {versionInfo.releaseDate && (\r\n          <div style={{ marginTop: 12, textAlign: 'right' }}>\r\n            <Text type=\"secondary\" style={{ fontSize: 11 }}>\r\n              发布时间: {versionInfo.releaseDate}\r\n            </Text>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    if(versionQuery.dataUpdatedAt && systemUpdateTips && versionQuery.data &&\r\n       Number(versionQuery.data.version) !== Number(process.env.REACT_APP_VERSION)) {\r\n      setSystemUpdateTips(false);\r\n      Modal.confirm({\r\n        title: \"新版本更新\",\r\n        content: <div>\r\n          <div>当前版本 {process.env.REACT_APP_VERSION}</div>\r\n          <div>检测到最新版本 <span style={{color: '#fa86d9'}}>{versionQuery.data}</span></div>\r\n          <div>点击“更新”按钮或者刷新页面，访问最新版本</div>\r\n        </div>,\r\n        okText: '更新',\r\n        cancelText: '暂不更新',\r\n        onOk: () => {\r\n          window.location.reload(true);\r\n        }\r\n      })\r\n    }\r\n  },[versionQuery.dataUpdatedAt])\r\n\r\n  return <React.Fragment></React.Fragment>\r\n}"], "mappings": ";;AAAA,SAASA,0BAA0B,QAAQ,kBAAkB;AAC7D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AACjD,OAAO,KAAKC,eAAe,MAAM,+BAA+B;AAChE,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAM;EAAEC;AAAK,CAAC,GAAGL,UAAU;AAE3B,eAAe,SAASM,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM,CAACC,gBAAgB,EAACC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;;EAE7D;EACA,MAAMa,YAAY,GAAGhB,QAAQ,CAAC;IAC5BiB,OAAO,EAAE,MAAAA,CAAA,KAAW;MAClB,OAAOlB,0BAA0B,CAAC;QAACmB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MAAC,CAAC,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAK;QAC1E,OAAO;UACLC,OAAO,EAAED,MAAM,CAACC,OAAO,IAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB;UACxDC,SAAS,EAAEL,MAAM,CAACK,SAAS,IAAI;QACjC,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IACDC,OAAO,EAAEJ,OAAO,CAACC,GAAG,CAACI,QAAQ,KAAK,YAAY,IAAKC,YAAY,CAACC,OAAO,CAACxB,eAAe,CAACyB,UAAW,CAAC,IAAIxB,UAAU,CAACyB,MAAO;IAC1HC,SAAS,EAAEC,QAAQ;IACnB;IACAC,eAAe,EAAE,KAAK;IACtBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;;EAEF;EACA,MAAMC,sBAAsB,GAAGA,CAACX,SAAS,EAAEY,UAAU,KAAK;IACxD,IAAI,CAACZ,SAAS,IAAI,CAACA,SAAS,CAACa,QAAQ,IAAI,CAACb,SAAS,CAACa,QAAQ,CAACD,UAAU,CAAC,EAAE;MACxE,OAAO,IAAI;IACb;IAEA,MAAME,WAAW,GAAGd,SAAS,CAACa,QAAQ,CAACD,UAAU,CAAC;IAElD,oBACE7B,OAAA;MAAKgC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAC,QAAA,gBAC5BlC,OAAA,CAACL,OAAO;QAACwC,WAAW,EAAC,MAAM;QAACH,KAAK,EAAE;UAAEI,MAAM,EAAE;QAAe,CAAE;QAAAF,QAAA,eAC5DlC,OAAA,CAACC,IAAI;UAACoC,MAAM;UAACL,KAAK,EAAE;YAAEM,QAAQ,EAAE;UAAG,CAAE;UAAAJ,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,EAETX,WAAW,CAACY,QAAQ,IAAIZ,WAAW,CAACY,QAAQ,CAACC,MAAM,GAAG,CAAC,iBACtD5C,OAAA;QAAKgC,KAAK,EAAE;UAAEa,YAAY,EAAE;QAAG,CAAE;QAAAX,QAAA,gBAC/BlC,OAAA,CAACC,IAAI;UAACoC,MAAM;UAACL,KAAK,EAAE;YAAEc,KAAK,EAAE,SAAS;YAAER,QAAQ,EAAE;UAAG,CAAE;UAAAJ,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpE1C,OAAA;UAAIgC,KAAK,EAAE;YAAEI,MAAM,EAAE,WAAW;YAAEW,WAAW,EAAE;UAAG,CAAE;UAAAb,QAAA,EACjDH,WAAW,CAACY,QAAQ,CAACK,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACvClD,OAAA;YAAgBgC,KAAK,EAAE;cAAEM,QAAQ,EAAE,EAAE;cAAEa,UAAU,EAAE,MAAM;cAAEN,YAAY,EAAE;YAAE,CAAE;YAAAX,QAAA,EAC1Ee;UAAO,GADDC,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,EAEAX,WAAW,CAACqB,YAAY,IAAIrB,WAAW,CAACqB,YAAY,CAACR,MAAM,GAAG,CAAC,iBAC9D5C,OAAA;QAAKgC,KAAK,EAAE;UAAEa,YAAY,EAAE;QAAG,CAAE;QAAAX,QAAA,gBAC/BlC,OAAA,CAACC,IAAI;UAACoC,MAAM;UAACL,KAAK,EAAE;YAAEc,KAAK,EAAE,SAAS;YAAER,QAAQ,EAAE;UAAG,CAAE;UAAAJ,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtE1C,OAAA;UAAIgC,KAAK,EAAE;YAAEI,MAAM,EAAE,WAAW;YAAEW,WAAW,EAAE;UAAG,CAAE;UAAAb,QAAA,EACjDH,WAAW,CAACqB,YAAY,CAACJ,GAAG,CAAC,CAACK,WAAW,EAAEH,KAAK,kBAC/ClD,OAAA;YAAgBgC,KAAK,EAAE;cAAEM,QAAQ,EAAE,EAAE;cAAEa,UAAU,EAAE,MAAM;cAAEN,YAAY,EAAE;YAAE,CAAE;YAAAX,QAAA,EAC1EmB;UAAW,GADLH,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,EAEAX,WAAW,CAACuB,QAAQ,IAAIvB,WAAW,CAACuB,QAAQ,CAACV,MAAM,GAAG,CAAC,iBACtD5C,OAAA;QAAKgC,KAAK,EAAE;UAAEa,YAAY,EAAE;QAAE,CAAE;QAAAX,QAAA,gBAC9BlC,OAAA,CAACC,IAAI;UAACoC,MAAM;UAACL,KAAK,EAAE;YAAEc,KAAK,EAAE,SAAS;YAAER,QAAQ,EAAE;UAAG,CAAE;UAAAJ,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtE1C,OAAA;UAAIgC,KAAK,EAAE;YAAEI,MAAM,EAAE,WAAW;YAAEW,WAAW,EAAE;UAAG,CAAE;UAAAb,QAAA,EACjDH,WAAW,CAACuB,QAAQ,CAACN,GAAG,CAAC,CAACO,MAAM,EAAEL,KAAK,kBACtClD,OAAA;YAAgBgC,KAAK,EAAE;cAAEM,QAAQ,EAAE,EAAE;cAAEa,UAAU,EAAE,MAAM;cAAEN,YAAY,EAAE;YAAE,CAAE;YAAAX,QAAA,EAC1EqB;UAAM,GADAL,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,EAEAX,WAAW,CAACyB,WAAW,iBACtBxD,OAAA;QAAKgC,KAAK,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEwB,SAAS,EAAE;QAAQ,CAAE;QAAAvB,QAAA,eAChDlC,OAAA,CAACC,IAAI;UAACyD,IAAI,EAAC,WAAW;UAAC1B,KAAK,EAAE;YAAEM,QAAQ,EAAE;UAAG,CAAE;UAAAJ,QAAA,GAAC,4BACxC,EAACH,WAAW,CAACyB,WAAW;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAEDlD,SAAS,CAAC,MAAM;IACd,IAAGc,YAAY,CAACqD,aAAa,IAAIvD,gBAAgB,IAAIE,YAAY,CAACsD,IAAI,IACnEC,MAAM,CAACvD,YAAY,CAACsD,IAAI,CAAC/C,OAAO,CAAC,KAAKgD,MAAM,CAAC/C,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC,EAAE;MAC9EX,mBAAmB,CAAC,KAAK,CAAC;MAC1BX,KAAK,CAACoE,OAAO,CAAC;QACZC,KAAK,EAAE,OAAO;QACdC,OAAO,eAAEhE,OAAA;UAAAkC,QAAA,gBACPlC,OAAA;YAAAkC,QAAA,GAAK,2BAAK,EAACpB,OAAO,CAACC,GAAG,CAACC,iBAAiB;UAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/C1C,OAAA;YAAAkC,QAAA,GAAK,6CAAQ,eAAAlC,OAAA;cAAMgC,KAAK,EAAE;gBAACc,KAAK,EAAE;cAAS,CAAE;cAAAZ,QAAA,EAAE5B,YAAY,CAACsD;YAAI;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9E1C,OAAA;YAAAkC,QAAA,EAAK;UAAqB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;QACNuB,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,MAAM;QAClBC,IAAI,EAAEA,CAAA,KAAM;UACVC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAC,CAAChE,YAAY,CAACqD,aAAa,CAAC,CAAC;EAE/B,oBAAO3D,OAAA,CAACT,KAAK,CAACgF,QAAQ;IAAAhC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAiB,CAAC;AAC1C;AAACvC,EAAA,CAzGuBD,cAAc;EAAA,QAIfZ,QAAQ;AAAA;AAAAkF,EAAA,GAJPtE,cAAc;AAAA,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}