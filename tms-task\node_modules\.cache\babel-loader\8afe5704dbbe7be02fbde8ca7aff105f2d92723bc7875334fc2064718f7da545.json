{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { stringDiff } from '../../../base/common/diff/diff.js';\nimport { globals } from '../../../base/common/platform.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { DiffComputer } from '../diff/diffComputer.js';\nimport { MirrorTextModel as BaseMirrorModel } from '../model/mirrorTextModel.js';\nimport { ensureValidWordDefinition, getWordAtText } from '../core/wordHelper.js';\nimport { computeLinks } from '../languages/linkComputer.js';\nimport { BasicInplaceReplace } from '../languages/supports/inplaceReplaceSupport.js';\nimport { createMonacoBaseAPI } from './editorBaseApi.js';\nimport * as types from '../../../base/common/types.js';\nimport { StopWatch } from '../../../base/common/stopwatch.js';\nimport { UnicodeTextModelHighlighter } from './unicodeTextModelHighlighter.js';\n/**\n * @internal\n */\nexport class MirrorModel extends BaseMirrorModel {\n  get uri() {\n    return this._uri;\n  }\n  get eol() {\n    return this._eol;\n  }\n  getValue() {\n    return this.getText();\n  }\n  getLinesContent() {\n    return this._lines.slice(0);\n  }\n  getLineCount() {\n    return this._lines.length;\n  }\n  getLineContent(lineNumber) {\n    return this._lines[lineNumber - 1];\n  }\n  getWordAtPosition(position, wordDefinition) {\n    const wordAtText = getWordAtText(position.column, ensureValidWordDefinition(wordDefinition), this._lines[position.lineNumber - 1], 0);\n    if (wordAtText) {\n      return new Range(position.lineNumber, wordAtText.startColumn, position.lineNumber, wordAtText.endColumn);\n    }\n    return null;\n  }\n  words(wordDefinition) {\n    const lines = this._lines;\n    const wordenize = this._wordenize.bind(this);\n    let lineNumber = 0;\n    let lineText = '';\n    let wordRangesIdx = 0;\n    let wordRanges = [];\n    return {\n      *[Symbol.iterator]() {\n        while (true) {\n          if (wordRangesIdx < wordRanges.length) {\n            const value = lineText.substring(wordRanges[wordRangesIdx].start, wordRanges[wordRangesIdx].end);\n            wordRangesIdx += 1;\n            yield value;\n          } else {\n            if (lineNumber < lines.length) {\n              lineText = lines[lineNumber];\n              wordRanges = wordenize(lineText, wordDefinition);\n              wordRangesIdx = 0;\n              lineNumber += 1;\n            } else {\n              break;\n            }\n          }\n        }\n      }\n    };\n  }\n  getLineWords(lineNumber, wordDefinition) {\n    const content = this._lines[lineNumber - 1];\n    const ranges = this._wordenize(content, wordDefinition);\n    const words = [];\n    for (const range of ranges) {\n      words.push({\n        word: content.substring(range.start, range.end),\n        startColumn: range.start + 1,\n        endColumn: range.end + 1\n      });\n    }\n    return words;\n  }\n  _wordenize(content, wordDefinition) {\n    const result = [];\n    let match;\n    wordDefinition.lastIndex = 0; // reset lastIndex just to be sure\n    while (match = wordDefinition.exec(content)) {\n      if (match[0].length === 0) {\n        // it did match the empty string\n        break;\n      }\n      result.push({\n        start: match.index,\n        end: match.index + match[0].length\n      });\n    }\n    return result;\n  }\n  getValueInRange(range) {\n    range = this._validateRange(range);\n    if (range.startLineNumber === range.endLineNumber) {\n      return this._lines[range.startLineNumber - 1].substring(range.startColumn - 1, range.endColumn - 1);\n    }\n    const lineEnding = this._eol;\n    const startLineIndex = range.startLineNumber - 1;\n    const endLineIndex = range.endLineNumber - 1;\n    const resultLines = [];\n    resultLines.push(this._lines[startLineIndex].substring(range.startColumn - 1));\n    for (let i = startLineIndex + 1; i < endLineIndex; i++) {\n      resultLines.push(this._lines[i]);\n    }\n    resultLines.push(this._lines[endLineIndex].substring(0, range.endColumn - 1));\n    return resultLines.join(lineEnding);\n  }\n  offsetAt(position) {\n    position = this._validatePosition(position);\n    this._ensureLineStarts();\n    return this._lineStarts.getPrefixSum(position.lineNumber - 2) + (position.column - 1);\n  }\n  positionAt(offset) {\n    offset = Math.floor(offset);\n    offset = Math.max(0, offset);\n    this._ensureLineStarts();\n    const out = this._lineStarts.getIndexOf(offset);\n    const lineLength = this._lines[out.index].length;\n    // Ensure we return a valid position\n    return {\n      lineNumber: 1 + out.index,\n      column: 1 + Math.min(out.remainder, lineLength)\n    };\n  }\n  _validateRange(range) {\n    const start = this._validatePosition({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = this._validatePosition({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    if (start.lineNumber !== range.startLineNumber || start.column !== range.startColumn || end.lineNumber !== range.endLineNumber || end.column !== range.endColumn) {\n      return {\n        startLineNumber: start.lineNumber,\n        startColumn: start.column,\n        endLineNumber: end.lineNumber,\n        endColumn: end.column\n      };\n    }\n    return range;\n  }\n  _validatePosition(position) {\n    if (!Position.isIPosition(position)) {\n      throw new Error('bad position');\n    }\n    let {\n      lineNumber,\n      column\n    } = position;\n    let hasChanged = false;\n    if (lineNumber < 1) {\n      lineNumber = 1;\n      column = 1;\n      hasChanged = true;\n    } else if (lineNumber > this._lines.length) {\n      lineNumber = this._lines.length;\n      column = this._lines[lineNumber - 1].length + 1;\n      hasChanged = true;\n    } else {\n      const maxCharacter = this._lines[lineNumber - 1].length + 1;\n      if (column < 1) {\n        column = 1;\n        hasChanged = true;\n      } else if (column > maxCharacter) {\n        column = maxCharacter;\n        hasChanged = true;\n      }\n    }\n    if (!hasChanged) {\n      return position;\n    } else {\n      return {\n        lineNumber,\n        column\n      };\n    }\n  }\n}\n/**\n * @internal\n */\nexport class EditorSimpleWorker {\n  constructor(host, foreignModuleFactory) {\n    this._host = host;\n    this._models = Object.create(null);\n    this._foreignModuleFactory = foreignModuleFactory;\n    this._foreignModule = null;\n  }\n  dispose() {\n    this._models = Object.create(null);\n  }\n  _getModel(uri) {\n    return this._models[uri];\n  }\n  _getModels() {\n    const all = [];\n    Object.keys(this._models).forEach(key => all.push(this._models[key]));\n    return all;\n  }\n  acceptNewModel(data) {\n    this._models[data.url] = new MirrorModel(URI.parse(data.url), data.lines, data.EOL, data.versionId);\n  }\n  acceptModelChanged(strURL, e) {\n    if (!this._models[strURL]) {\n      return;\n    }\n    const model = this._models[strURL];\n    model.onEvents(e);\n  }\n  acceptRemovedModel(strURL) {\n    if (!this._models[strURL]) {\n      return;\n    }\n    delete this._models[strURL];\n  }\n  computeUnicodeHighlights(url, options, range) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const model = this._getModel(url);\n      if (!model) {\n        return {\n          ranges: [],\n          hasMore: false,\n          ambiguousCharacterCount: 0,\n          invisibleCharacterCount: 0,\n          nonBasicAsciiCharacterCount: 0\n        };\n      }\n      return UnicodeTextModelHighlighter.computeUnicodeHighlights(model, options, range);\n    });\n  }\n  // ---- BEGIN diff --------------------------------------------------------------------------\n  computeDiff(originalUrl, modifiedUrl, ignoreTrimWhitespace, maxComputationTime) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const original = this._getModel(originalUrl);\n      const modified = this._getModel(modifiedUrl);\n      if (!original || !modified) {\n        return null;\n      }\n      return EditorSimpleWorker.computeDiff(original, modified, ignoreTrimWhitespace, maxComputationTime);\n    });\n  }\n  static computeDiff(originalTextModel, modifiedTextModel, ignoreTrimWhitespace, maxComputationTime) {\n    const originalLines = originalTextModel.getLinesContent();\n    const modifiedLines = modifiedTextModel.getLinesContent();\n    const diffComputer = new DiffComputer(originalLines, modifiedLines, {\n      shouldComputeCharChanges: true,\n      shouldPostProcessCharChanges: true,\n      shouldIgnoreTrimWhitespace: ignoreTrimWhitespace,\n      shouldMakePrettyDiff: true,\n      maxComputationTime: maxComputationTime\n    });\n    const diffResult = diffComputer.computeDiff();\n    const identical = diffResult.changes.length > 0 ? false : this._modelsAreIdentical(originalTextModel, modifiedTextModel);\n    return {\n      quitEarly: diffResult.quitEarly,\n      identical: identical,\n      changes: diffResult.changes\n    };\n  }\n  static _modelsAreIdentical(original, modified) {\n    const originalLineCount = original.getLineCount();\n    const modifiedLineCount = modified.getLineCount();\n    if (originalLineCount !== modifiedLineCount) {\n      return false;\n    }\n    for (let line = 1; line <= originalLineCount; line++) {\n      const originalLine = original.getLineContent(line);\n      const modifiedLine = modified.getLineContent(line);\n      if (originalLine !== modifiedLine) {\n        return false;\n      }\n    }\n    return true;\n  }\n  computeMoreMinimalEdits(modelUrl, edits) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const model = this._getModel(modelUrl);\n      if (!model) {\n        return edits;\n      }\n      const result = [];\n      let lastEol = undefined;\n      edits = edits.slice(0).sort((a, b) => {\n        if (a.range && b.range) {\n          return Range.compareRangesUsingStarts(a.range, b.range);\n        }\n        // eol only changes should go to the end\n        const aRng = a.range ? 0 : 1;\n        const bRng = b.range ? 0 : 1;\n        return aRng - bRng;\n      });\n      for (let {\n        range,\n        text,\n        eol\n      } of edits) {\n        if (typeof eol === 'number') {\n          lastEol = eol;\n        }\n        if (Range.isEmpty(range) && !text) {\n          // empty change\n          continue;\n        }\n        const original = model.getValueInRange(range);\n        text = text.replace(/\\r\\n|\\n|\\r/g, model.eol);\n        if (original === text) {\n          // noop\n          continue;\n        }\n        // make sure diff won't take too long\n        if (Math.max(text.length, original.length) > EditorSimpleWorker._diffLimit) {\n          result.push({\n            range,\n            text\n          });\n          continue;\n        }\n        // compute diff between original and edit.text\n        const changes = stringDiff(original, text, false);\n        const editOffset = model.offsetAt(Range.lift(range).getStartPosition());\n        for (const change of changes) {\n          const start = model.positionAt(editOffset + change.originalStart);\n          const end = model.positionAt(editOffset + change.originalStart + change.originalLength);\n          const newEdit = {\n            text: text.substr(change.modifiedStart, change.modifiedLength),\n            range: {\n              startLineNumber: start.lineNumber,\n              startColumn: start.column,\n              endLineNumber: end.lineNumber,\n              endColumn: end.column\n            }\n          };\n          if (model.getValueInRange(newEdit.range) !== newEdit.text) {\n            result.push(newEdit);\n          }\n        }\n      }\n      if (typeof lastEol === 'number') {\n        result.push({\n          eol: lastEol,\n          text: '',\n          range: {\n            startLineNumber: 0,\n            startColumn: 0,\n            endLineNumber: 0,\n            endColumn: 0\n          }\n        });\n      }\n      return result;\n    });\n  }\n  // ---- END minimal edits ---------------------------------------------------------------\n  computeLinks(modelUrl) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const model = this._getModel(modelUrl);\n      if (!model) {\n        return null;\n      }\n      return computeLinks(model);\n    });\n  }\n  textualSuggest(modelUrls, leadingWord, wordDef, wordDefFlags) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const sw = new StopWatch(true);\n      const wordDefRegExp = new RegExp(wordDef, wordDefFlags);\n      const seen = new Set();\n      outer: for (const url of modelUrls) {\n        const model = this._getModel(url);\n        if (!model) {\n          continue;\n        }\n        for (const word of model.words(wordDefRegExp)) {\n          if (word === leadingWord || !isNaN(Number(word))) {\n            continue;\n          }\n          seen.add(word);\n          if (seen.size > EditorSimpleWorker._suggestionsLimit) {\n            break outer;\n          }\n        }\n      }\n      return {\n        words: Array.from(seen),\n        duration: sw.elapsed()\n      };\n    });\n  }\n  // ---- END suggest --------------------------------------------------------------------------\n  //#region -- word ranges --\n  computeWordRanges(modelUrl, range, wordDef, wordDefFlags) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const model = this._getModel(modelUrl);\n      if (!model) {\n        return Object.create(null);\n      }\n      const wordDefRegExp = new RegExp(wordDef, wordDefFlags);\n      const result = Object.create(null);\n      for (let line = range.startLineNumber; line < range.endLineNumber; line++) {\n        const words = model.getLineWords(line, wordDefRegExp);\n        for (const word of words) {\n          if (!isNaN(Number(word.word))) {\n            continue;\n          }\n          let array = result[word.word];\n          if (!array) {\n            array = [];\n            result[word.word] = array;\n          }\n          array.push({\n            startLineNumber: line,\n            startColumn: word.startColumn,\n            endLineNumber: line,\n            endColumn: word.endColumn\n          });\n        }\n      }\n      return result;\n    });\n  }\n  //#endregion\n  navigateValueSet(modelUrl, range, up, wordDef, wordDefFlags) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const model = this._getModel(modelUrl);\n      if (!model) {\n        return null;\n      }\n      const wordDefRegExp = new RegExp(wordDef, wordDefFlags);\n      if (range.startColumn === range.endColumn) {\n        range = {\n          startLineNumber: range.startLineNumber,\n          startColumn: range.startColumn,\n          endLineNumber: range.endLineNumber,\n          endColumn: range.endColumn + 1\n        };\n      }\n      const selectionText = model.getValueInRange(range);\n      const wordRange = model.getWordAtPosition({\n        lineNumber: range.startLineNumber,\n        column: range.startColumn\n      }, wordDefRegExp);\n      if (!wordRange) {\n        return null;\n      }\n      const word = model.getValueInRange(wordRange);\n      const result = BasicInplaceReplace.INSTANCE.navigateValueSet(range, selectionText, wordRange, word, up);\n      return result;\n    });\n  }\n  // ---- BEGIN foreign module support --------------------------------------------------------------------------\n  loadForeignModule(moduleId, createData, foreignHostMethods) {\n    const proxyMethodRequest = (method, args) => {\n      return this._host.fhr(method, args);\n    };\n    const foreignHost = types.createProxyObject(foreignHostMethods, proxyMethodRequest);\n    const ctx = {\n      host: foreignHost,\n      getMirrorModels: () => {\n        return this._getModels();\n      }\n    };\n    if (this._foreignModuleFactory) {\n      this._foreignModule = this._foreignModuleFactory(ctx, createData);\n      // static foreing module\n      return Promise.resolve(types.getAllMethodNames(this._foreignModule));\n    }\n    // ESM-comment-begin\n    // \t\treturn new Promise<any>((resolve, reject) => {\n    // \t\t\trequire([moduleId], (foreignModule: { create: IForeignModuleFactory }) => {\n    // \t\t\t\tthis._foreignModule = foreignModule.create(ctx, createData);\n    // \n    // \t\t\t\tresolve(types.getAllMethodNames(this._foreignModule));\n    // \n    // \t\t\t}, reject);\n    // \t\t});\n    // ESM-comment-end\n    // ESM-uncomment-begin\n    return Promise.reject(new Error(`Unexpected usage`));\n    // ESM-uncomment-end\n  }\n  // foreign method request\n  fmr(method, args) {\n    if (!this._foreignModule || typeof this._foreignModule[method] !== 'function') {\n      return Promise.reject(new Error('Missing requestHandler or method: ' + method));\n    }\n    try {\n      return Promise.resolve(this._foreignModule[method].apply(this._foreignModule, args));\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  }\n}\n// ---- END diff --------------------------------------------------------------------------\n// ---- BEGIN minimal edits ---------------------------------------------------------------\nEditorSimpleWorker._diffLimit = 100000;\n// ---- BEGIN suggest --------------------------------------------------------------------------\nEditorSimpleWorker._suggestionsLimit = 10000;\n/**\n * Called on the worker side\n * @internal\n */\nexport function create(host) {\n  return new EditorSimpleWorker(host, null);\n}\nif (typeof importScripts === 'function') {\n  // Running in a web worker\n  globals.monaco = createMonacoBaseAPI();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}