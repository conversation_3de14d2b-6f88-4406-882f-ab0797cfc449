{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { Emitter } from '../../base/common/event.js';\nimport { Disposable, toDisposable } from '../../base/common/lifecycle.js';\nexport class TokenizationRegistry {\n  constructor() {\n    this._map = new Map();\n    this._factories = new Map();\n    this._onDidChange = new Emitter();\n    this.onDidChange = this._onDidChange.event;\n    this._colorMap = null;\n  }\n  fire(languages) {\n    this._onDidChange.fire({\n      changedLanguages: languages,\n      changedColorMap: false\n    });\n  }\n  register(language, support) {\n    this._map.set(language, support);\n    this.fire([language]);\n    return toDisposable(() => {\n      if (this._map.get(language) !== support) {\n        return;\n      }\n      this._map.delete(language);\n      this.fire([language]);\n    });\n  }\n  registerFactory(languageId, factory) {\n    var _a;\n    (_a = this._factories.get(languageId)) === null || _a === void 0 ? void 0 : _a.dispose();\n    const myData = new TokenizationSupportFactoryData(this, languageId, factory);\n    this._factories.set(languageId, myData);\n    return toDisposable(() => {\n      const v = this._factories.get(languageId);\n      if (!v || v !== myData) {\n        return;\n      }\n      this._factories.delete(languageId);\n      v.dispose();\n    });\n  }\n  getOrCreate(languageId) {\n    return __awaiter(this, void 0, void 0, function* () {\n      // check first if the support is already set\n      const tokenizationSupport = this.get(languageId);\n      if (tokenizationSupport) {\n        return tokenizationSupport;\n      }\n      const factory = this._factories.get(languageId);\n      if (!factory || factory.isResolved) {\n        // no factory or factory.resolve already finished\n        return null;\n      }\n      yield factory.resolve();\n      return this.get(languageId);\n    });\n  }\n  get(language) {\n    return this._map.get(language) || null;\n  }\n  isResolved(languageId) {\n    const tokenizationSupport = this.get(languageId);\n    if (tokenizationSupport) {\n      return true;\n    }\n    const factory = this._factories.get(languageId);\n    if (!factory || factory.isResolved) {\n      return true;\n    }\n    return false;\n  }\n  setColorMap(colorMap) {\n    this._colorMap = colorMap;\n    this._onDidChange.fire({\n      changedLanguages: Array.from(this._map.keys()),\n      changedColorMap: true\n    });\n  }\n  getColorMap() {\n    return this._colorMap;\n  }\n  getDefaultBackground() {\n    if (this._colorMap && this._colorMap.length > 2 /* ColorId.DefaultBackground */) {\n      return this._colorMap[2 /* ColorId.DefaultBackground */];\n    }\n    return null;\n  }\n}\nclass TokenizationSupportFactoryData extends Disposable {\n  constructor(_registry, _languageId, _factory) {\n    super();\n    this._registry = _registry;\n    this._languageId = _languageId;\n    this._factory = _factory;\n    this._isDisposed = false;\n    this._resolvePromise = null;\n    this._isResolved = false;\n  }\n  get isResolved() {\n    return this._isResolved;\n  }\n  dispose() {\n    this._isDisposed = true;\n    super.dispose();\n  }\n  resolve() {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (!this._resolvePromise) {\n        this._resolvePromise = this._create();\n      }\n      return this._resolvePromise;\n    });\n  }\n  _create() {\n    return __awaiter(this, void 0, void 0, function* () {\n      const value = yield Promise.resolve(this._factory.createTokenizationSupport());\n      this._isResolved = true;\n      if (value && !this._isDisposed) {\n        this._register(this._registry.register(this._languageId, value));\n      }\n    });\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}