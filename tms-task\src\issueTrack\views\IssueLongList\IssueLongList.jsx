/* eslint-disable jsx-a11y/anchor-has-content */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useState } from "react";
import { But<PERSON>, Checkbox, ConfigProvider, Drawer, Modal, Space } from 'antd';
import DraggablePopUp from "@components/DraggablePopUp";
import { getPropValueByIdType, getUserNameById, getAvatarById, getIconValueByIdType, formatSvg } from "src/issueTrack/utils/ArrayUtils";
import * as httpCommon from "@common/api/http";
import IssueDetail from "../IssueDetail/IssueDetail";
import DefaultAvatar from "@components/DefaultAvatar"
import { eConsoleNodeId, eConsoleUiControl, ePagination } from "@common/utils/enum";
import { eNodeTypeId } from "@common/utils/TsbConfig";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useIssueSearchParams } from "src/issueTrack/service/issueSearchHooks";
import { Outlet, useAsyncValue, useLocation, useNavigate, useOutletContext, useParams, useSearchParams } from "react-router-dom";
import { globalEventBus } from "@common/utils/eventBus";
import { isListBoxType } from "@common/utils/logicUtils";
import { track008, track010 } from '@common/utils/ApiPath';
import "./IssueLongList.scss";
import { setting_234_get_team_mbr_user_info_query } from "@common/api/query/query";
import { useMutationSetting235SetTeamMbrUser, useMutationTeam545SelectTableFields } from "@common/service/commonHooks";
import { globalUtil } from "@common/utils/globalUtil";
import ObjExplorerTitle from "@components/ObjExplorer/ObjExplorerTitle";
import cloneDeep from "lodash/cloneDeep";
import { isEmpty } from "@common/utils/ArrayUtils";
import ResizableTable from "@components/ResizableTable";
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { priPermission, getNodeNameByNodeId } from "@common/utils/logicUtils";
import DraggableDrawer from '@common/components/DraggableDrawer';

//备注: column == field == attr
export default function IssueLongList() {
    const { teamId, nodeId: issueListNodeId } = useParams();
    const { totalCnt, issueList, attrList, issue506Result, subclassAttrList, userList, spaceUserList, selectionList, objInfo, pageNo,
         keywords,  previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg,
        gotoPreviousIssue, gotoNextIssue, setCurrentIssueIdx, viewMode,refetchIssueList, projectInfo, 
        onShowMoreButtonClick, onMoreBtnClick, searchQuery, setting320Result, finalCreateFlg } = useOutletContext(); //setPageNo, iconSelectionLid,
    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const createFlg = searchParams.get("createFlg"); //是否创建
    const open = searchParams.get("open"); //是否打开详情
    const [issueDetailVisibleFlg, setIssueDetailVisibleFlg] = useState(false);
    const {issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams();// issue路由配置，页面刷新
    const [issueListLong, setIssueListLong] = useState([]);// 深拷贝issueList

    const [attrColumns, setAttrColumns] = useState([]);
    //const [refreshingFlg, setRefreshingFlg] = useState(false); //是否正在"刷新中"
    const [selectedAttrNidList, setSelectedAttrList] = useState([]); // 显示字段
    //const [createIssueModalVisibleFlg, setCreateIssueModalVisibleFlg] = useState(false); //是否显示新建issue对话框
    const [selectedAttrsModalVisibleFlg, setSelectedAttrsModalVisibleFlg] = useState(false); //是否显示 "显示字段" 弹出框
    const { mutate: mutationSaveFieldList } = useMutationTeam545SelectTableFields();  //保存issue查询显示字段
    // const { fieldList, configFlg, isLoading } = useQueryTeam544GetTableFieldList(teamId, eNodeTypeId.nt_317_objtype_issue_project, projectId); // 获取显示字段

    const {data: { userId, issueZoomFlg } = { userId: null, issueZoomFlg: 0 } }
      = useQuery({
        ...setting_234_get_team_mbr_user_info_query(teamId)
    });

    // 移除 largeView、drawerWidth、handleZoom、setDrawerWidth 相关 state 和逻辑

    useEffect(()=>{
      if(createFlg){
        setIssueSearchParams({queryId: -1});
        setTimeout(()=>{
          // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑
          // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；
         openCreateIssueModal()
        }, 2000)
      } 
    },[createFlg])

    useEffect(()=>{
      if(open){
        setIssueSearchParams({queryId: -1});
        setIssueDetailVisibleFlg(true)
      } 
    },[open])

    //issuelist变动后重新处理数据
    useEffect(() => {
      if(issueList){
        _transformIssueTableColumns(issueList);
        _load_team544_get_table_field_list();
      }
    }, [JSON.stringify(issueList)]); // 直接依赖数组会二次调用

    useEffect(() => {
        refetchIssueList()
    },[subclassAttrList])

    useEffect(() => {
        // setLargeView(issueZoomFlg); // 移除此行
    }, [issueZoomFlg]);

    function openCreateIssueModal () {
        globalEventBus.emit("openCreateIssueModalEvent", "", {
          nodeId: issueListNodeId, projectInfo, callback: onPostIssueCreated
        })
      }
    // 数据处理 长列表
    //https://confluence.ficent.com/pages/viewpage.action?pageId=78362226
    //构建表格渲染所需要的 attrList(即:其它表格页面所需要的columns数据结构)
    function _transformIssueTableColumns(issueList = []) {
        const issueListLong = cloneDeep(issueList); // 需要注意JSON.parse(JSON.stringify())后icon无法识别为jsx对象
        let attrList1 = attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox)
        attrList1.forEach((attr) => {
            attr.key = attr.dataIndex = attr.attrNid;
            attr.title = attr.name;
            // 为所有列添加 ellipsis: true，防止文本折行
            attr.ellipsis = true;
            
            // 通过点击编号跳转详情
            if (attr.attrNid == eConsoleNodeId.Nid_11101_Issue_IssueNo) {
                // 不设置固定宽度，让auto layout自动调整
                attr.render = (text, record) => {
                    return <a style={{ color: "#3279fe" }}
                        onClick={() => {
                            setIssueDetailVisibleFlg(true);
                            setIssueSearchParams({ viewMode, issueNodeId: record.nodeId, queryId });
                        }}>
                        {text}
                    </a>
                }
            }
            if (attr.attrNid == eConsoleNodeId.Nid_11102_Issue_Title) {
                // 不设置固定宽度，让auto layout自动调整
                attr.render = (text, nodeData) => {
                  return <ObjExplorerTitle nodeData={nodeData}
                                           onShowMoreButtonClick={onShowMoreButtonClick}
                                           onMoreBtnClick={onMoreBtnClick}
                                           searchQuery={searchQuery}
                                           issueFlg={true}/>
                }
            }
            if (isListBoxType(attr.uiControl)) {  //下拉框类型的表单字段
                issueListLong.forEach((issue, _index) => {
                    for (var attrId in issue) {
                        if (attr.attrNid == attrId) {
                            if (attr.selectionLid == "-701") {
                                issue[attrId] = {
                                    avatar: getAvatarById(userList, issue[attrId]),
                                    username: getUserNameById(userList, issue[attrId])
                                }
                            } else if (attrId == eConsoleNodeId.Nid_11109_Issue_Type) {
                                issue[attrId] = getPropValueByIdType(selectionList, attr.selectionLid, issue[attrId]) //从下拉code找到其value
                            } else {
                                issue[attrId] = getPropValueByIdType(selectionList, attr.selectionLid, issue[attrId])
                            }
                        }
                    }
                })
            }
            // 添加icon 问题类别和标题的icon名称重复了，先隐藏 by walt 2023-06-15
            /* if (attr.attrNid == eConsoleNodeId.Nid_11109_Issue_Type) {
                attr.width = 100
                attr.render = (text, _issue) => {
                    return <div style={{ display: "flex", alignItems: "center" }} className="overHidden">
                        { _issue.icon}
                        <span style={{ paddingLeft: 2 }}>{text}</span>
                    </div>
                }
            } */
            // 添加头像
            if (attr.selectionLid == "-701") {
                attr.render = (text, record) => {
                    return <div className="overHidden">
                        {text?.username ? <DefaultAvatar avatarSrc={text?.avatar} avatarFlg={1} /> : ""}
                        <span style={{ marginLeft: 5 }}>{text?.username}</span>
                    </div>
                }
            }
            if (attr.uiControl == eConsoleUiControl.Date) {
                // 不设置固定宽度，让auto layout自动调整
               /*  attr.render = (text, record) => {
                    return <div style={{ color: "#666" }}>{text}</div>
                } */
            }
        })
        setIssueListLong(issueListLong)
        setAttrColumns(cloneDeep(attrList1));
        // 保存后重新渲染
    }

    //获取查询显示字段
    function _load_team544_get_table_field_list() {
        let params = { teamId: teamId, objType: eNodeTypeId.nt_317_objtype_issue_project, tableType: 1, projectId: issue506Result?.projectId, }
        httpCommon.team_544_get_table_field_list(params).then((res) => {
            if (res.resultCode === 200) {
                if (res.configFlg == 1) {
                    selectedAttrNidList.length = 0; //清空数组，用[]进行赋值会受到const的语法限制
                    attrList.forEach(attr => {
                        if (attr.uiControl != eConsoleUiControl.RichTextBox) {
                            attr.hiddenFlg = false
                        }
                        selectedAttrNidList.push(attr.attrNid)
                        attr.hiddenFlg = false
                    })
                    // attrList.forEach(attr => attr.hiddenFlg = false);
                } else {
                    selectedAttrNidList.length = 0;
                    selectedAttrNidList.push(...(res.fieldList || []).map(field => Number(field.fieldName))); //fileName为attrNid, 11105, 11104等
                    attrList.forEach(attr => {
                        if (attr.uiControl != eConsoleUiControl.RichTextBox) {
                            attr.hiddenFlg = false
                        }
                        attr.hiddenFlg = !selectedAttrNidList.includes(attr.attrNid)
                    });
                }
            }
            setAttrColumns(attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox));
        });
    }
 /*   //刷新issue列表
    function refreshIssueList() {
        queryClient.invalidateQueries([track008])
        setTimeout(() => {
            setRefreshingFlg(false);
        }, 500);
    }*/
    // 加载更多 - 长短列表
    function gotoPageNo(page, pageSize) {
        queryClient.setQueryData([track010, teamId, issueListNodeId], (pageNo)=>(page));
        document.querySelector(`.issueLong-List .ant-table-tbody`).scrollTop = 0
    }
    // 显示字段 勾选项
    function setSelectedAttrList2(checkedValue) {
        setSelectedAttrList(checkedValue);
    }

    //显示字段 选择字段, 点击保存按钮
    function handleSelectedAttrsSaved() {
        attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox).forEach(attr => attr.hiddenFlg = !selectedAttrNidList.includes(attr.attrNid));
        let request = {
            teamId,
            objType: eNodeTypeId.nt_317_objtype_issue_project,
            tableType: 1,
            projectId: issue506Result?.projectId,
            fields: selectedAttrNidList.map(item => ({fieldName: item}))
        }
        mutationSaveFieldList(request, {
            onSuccess: (data, vars) => {
                setSelectedAttrsModalVisibleFlg(false);
                setAttrColumns(origin => {
                    return origin.map(item => {
                        return {
                            ...item,
                            hiddenFlg: !selectedAttrNidList.includes(item.attrNid)
                        }
                    });
                })
            }
        })
    }

    // issue创建完成后，页面刷新
    function onPostIssueCreated(issueNodeId) {
        setIssueSearchParams({viewMode, issueNodeId, queryId, needAdd: false})
    }

    const customizeRenderEmpty = () => (
        <div
            style={{
                // textAlign: 'center',
            }}
        >
            <div className=" issueLong-blank-page">
                <div className="blank-page-title">这里是空的</div>
                {
                  priPermission(setting320Result.privWrite) &&  
                  <div className="blank-page-des fontsize-12 flexCenter">
                      <span style={{ paddingRight: 5 }}>你可以</span>
                      <a onClick={() =>openCreateIssueModal()}>新建{projectInfo.issueAlias}</a>
                  </div>
                }
            </div>
        </div>
    );

    function setTeamMbrUser(){
        if(!teamId && !userId){
          return globalUtil.error('数据获取错误');
        }
        let zoomFlg = issueZoomFlg // 使用 issueZoomFlg 作为 zoom 状态
        // setLargeView(zoomFlg); // 移除此行
        let params = { teamId, userId, issueZoomFlg: zoomFlg }
        // setTeamMbrUserMutation(params); // 删除 setTeamMbrUserMutation 相关调用
      }

  async  function addClick(){
    return Modal.confirm({
        title: "提示",
        icon: <ExclamationCircleOutlined />,
        centered: true,
        content: <p>{`当前列表页为"查询"页，将转至“${await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)??"全部"}”页创建问题。`}</p>,
        okText: " 好的，继续新建",
        cancelText: "取消",
        onOk: () => {
            if(!!projectInfo?.allIssueNodeId){
                navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}/list?createFlg=true`)
            }
        },
        onCancel: () => {
            console.log("Cancel");
        },
        });
    }

    // 移除此函数
    // const handleZoom = () => {
    //   if (largeView) {
    //     setDrawerWidth('50%');
    //     setLargeView(false);
    //   } else {
    //     setDrawerWidth('90%');
    //     setLargeView(true);
    //   }
    // };

    return <div className="issueLong-List">
        <div className="flexBetween" style={{ height: 45 }}>
            <Space size={10} >
                {searchQuery?.length > 0 || keywords ?
                    <a style={{ fontSize: 12, color: "#999" }} title='定位至#1页'
                       onClick={()=>pageNo>1?gotoPageNo(1):null}>结果(<span className="color333">{totalCnt}</span>条)</a>
                    : <a style={{ fontSize: 12, color: "#999" }} title='定位至#1页'
                         onClick={()=>pageNo>1?gotoPageNo(1):null}>全部(<span className="color333">{totalCnt}</span>条)</a>
                }
                {/* <Button
                    className={refreshingFlg && 'refresh-icon'}
                    style={{ position: 'relative', color: '#999' }}
                    type="link"
                    icon={<span className="refresh-position fontsize-14 iconfont shuaxin1" />}
                    onClick={() => {
                        setRefreshingFlg(true);
                        refreshIssueList()
                    }}
                /> */}
                {
                  ( priPermission(setting320Result.privWrite) && finalCreateFlg) &&
                  (<Button className="defaultBtn" type="primary"
                           onClick={() => objInfo?.objId? addClick() :openCreateIssueModal()}>
                      + {projectInfo.issueAlias}
                  </Button>)
                }
            </Space>
            <a onClick={() => setSelectedAttrsModalVisibleFlg(true)}
                className="fontsize-12"
                style={{ float: "right", marginRight: 20, color:'#999' }}>
                <span className="fontsize-14 iconfont filter2" />
                显示字段
            </a>
        </div>
        {
          isEmpty(issueListLong) ?
           <div
              style={{
                  // textAlign: 'center',
              }}
            >
            <div className="issueLong-blank-page">
                <div className="blank-page-title">这里是空的</div>
                {
                  priPermission(setting320Result.privWrite) &&  
                  <div className="blank-page-des fontsize-12 flexCenter">
                      <span style={{ paddingRight: 5 }}>你可以</span>
                      <a onClick={() =>openCreateIssueModal()}>新建{projectInfo.issueAlias}</a>
                  </div>
                }
            </div>
          </div> :
          <ConfigProvider renderEmpty={customizeRenderEmpty}>
            <ResizableTable 
                className="longlist"
                bordered={false}
                style={{ height: "100%" }}
                scroll={{ scrollToFirstRowOnChange: true, y: 0, x: 0 }}//y无意义设置，css设为unset
                columns={attrColumns.filter(_attr => !_attr.hiddenFlg)}
                dataSource={issueListLong}
                size={"small"}
                rowClassName={(_issue) => _issue.nodeId == issueNodeId ? "currentRow" : ""}
                // 通过table行点击事件，来使用户可以点击一行的任意位置，就可以弹出详情
                onRow={(_issue) => ({
                    onClick: (e) => {
                        setIssueSearchParams({viewMode, issueNodeId: _issue.nodeId, queryId}) // fixbug 修复切换issue url未变更的bug
                        // setIssueDetailVisibleFlg(true);
                    }
                })}
                pagination={
                    {
                        position: ['bottomCenter'],
                        size: 'small',
                        showQuickJumper: true,
                        showSizeChanger: false,
                        showTitle: true,
                        pageSize: 30,
                        current: pageNo,
                        defaultCurrent: pageNo,
                        total: totalCnt,
                        showTotal: (total) => `总共 ${total} 条`,
                        onChange: gotoPageNo,
                    }
                }
            />
        </ConfigProvider> }

        <DraggableDrawer
            destroyOnClose
            className="longlist-detail"
            minWidth="30%"
            maxWidth="95%"
            draggableFlag={true}
            fixedMinWidth="50%"
            fixedMaxWidth="90%"
            onClose={() => setIssueDetailVisibleFlg(false)}
            open={issueDetailVisibleFlg}
            closable={true}
            title={
                <span>{`${projectInfo.issueAlias}详情`}</span>
            }
        >
            <IssueDetail
                viewMode={viewMode}
                issueNodeId={issueNodeId}
                selectionList={selectionList}
                userList={userList}
                spaceUserList={spaceUserList}
                gotoPreviousIssue={gotoPreviousIssue}
                gotoNextIssue={gotoNextIssue}
                previousIssueLinkDisabledFlg={previousIssueLinkDisabledFlg}
                nextIssueLinkDisabledFlg={nextIssueLinkDisabledFlg}
                projectInfo={projectInfo}
                onMoreBtnClick={onMoreBtnClick}
                setCurrentIssueIdx={setCurrentIssueIdx}
                issueList={issueList}
            />
        </DraggableDrawer>
        {/*显示字段*/}
        {selectedAttrsModalVisibleFlg &&
            <DraggablePopUp className="tms-modal" destroyOnClose
                title={<div className="fontsize-16">显示字段</div>}
                open={true}
                onCancel={() => setSelectedAttrsModalVisibleFlg(false)}
                width={600}
                footer={[
                    <Button style={{ textAlign: "center" }} type="primary" onClick={handleSelectedAttrsSaved}>确定</Button>
                ]}
            >
                <div style={{ display: 'flex', justifyContent: 'flex-start', margin: 30 }}>
                    <div style={{ marginLeft: '30px' }}>
                        <div>
                            <Checkbox.Group onChange={(e) => setSelectedAttrList2(e)} defaultValue={selectedAttrNidList}>
                                {attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox)
                                          .map(_attr => (
                                            <Checkbox key={_attr.attrNid}
                                                      value={_attr.attrNid}
                                                      disabled={_attr.name == '问题编号' || _attr.name == '标题' || _attr.name == '负责人'}
                                                      style={{ margin: 10, width: 130 }} >
                                                {_attr.name}
                                            </Checkbox>
                                        ))
                                }
                            </Checkbox.Group>
                        </div>
                    </div>
                </div>
            </DraggablePopUp>
        }
    </div>
}
