{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\PersonalBasicTab.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Avatar, Button, Input, Form, Upload } from \"antd\";\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport { CloseCircleFilled, EditOutlined, CameraOutlined } from \"@ant-design/icons\";\nimport { useParams } from \"react-router-dom\";\nimport { shallowEqual, useDispatch, useSelector } from \"react-redux\";\nimport { NoAvatarIcon } from '@components/IconUtil';\nimport { QuestionsUploadIcon } from \"@components/IconUtil\";\nimport ImgCrop from \"antd-img-crop\";\nimport { get_team_mbr_user_info } from \"@/settings/store/actionCreators\";\nimport { setting_225_update_user_info } from '@common/api/http';\nimport \"./PersonalBasicTab.scss\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { getRootUrl } from \"@common/router/RouterRegister\";\nimport { setting202 } from \"@common/utils/ApiPath\";\nimport { useQueryClient } from \"@tanstack/react-query\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Dragger\n} = Upload;\n\n// 基本信息\nexport default function PersonalBasicTab({\n  flag\n}) {\n  _s();\n  var _state$teamMbrUserInf7, _state$teamMbrUserInf8, _state$teamMbrUserInf9, _state$teamMbrUserInf10;\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const queryClient = useQueryClient();\n  const {\n    teamId\n  } = useParams();\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [customFormData, setCustomFormData] = useState([]);\n  const state = useSelector(state => ({\n    teamMbrUserInfo: state.getIn([\"workSetUp\", \"teamMbrUserInfo\"])\n  }), shallowEqual);\n  useEffect(() => {\n    if (!!state.teamMbrUserInfo.mbrUserInfo) {\n      var _state$teamMbrUserInf, _state$teamMbrUserInf2, _state$teamMbrUserInf3, _state$teamMbrUserInf4, _state$teamMbrUserInf5, _state$teamMbrUserInf6;\n      let customFormData_ = [{\n        key: 'userName',\n        label: '成员名',\n        name: 'userName',\n        value: (_state$teamMbrUserInf = state.teamMbrUserInfo) === null || _state$teamMbrUserInf === void 0 ? void 0 : (_state$teamMbrUserInf2 = _state$teamMbrUserInf.mbrUserInfo) === null || _state$teamMbrUserInf2 === void 0 ? void 0 : _state$teamMbrUserInf2.userName,\n        tipsFlag: 1,\n        tips: '我在团队中的昵称'\n      }, {\n        key: 'mobileNo',\n        label: '手机号',\n        name: 'mobileNo',\n        value: (_state$teamMbrUserInf3 = state.teamMbrUserInfo) === null || _state$teamMbrUserInf3 === void 0 ? void 0 : (_state$teamMbrUserInf4 = _state$teamMbrUserInf3.mbrUserInfo) === null || _state$teamMbrUserInf4 === void 0 ? void 0 : _state$teamMbrUserInf4.mobileNo,\n        tipsFlag: 1,\n        tips: '此号码仅在本团队可见'\n      }, {\n        key: 'email',\n        label: '邮箱',\n        name: 'email',\n        value: (_state$teamMbrUserInf5 = state.teamMbrUserInfo) === null || _state$teamMbrUserInf5 === void 0 ? void 0 : (_state$teamMbrUserInf6 = _state$teamMbrUserInf5.mbrUserInfo) === null || _state$teamMbrUserInf6 === void 0 ? void 0 : _state$teamMbrUserInf6.email,\n        tipsFlag: 1,\n        tips: '接收团队邮件通知'\n      }];\n      setCustomFormData([...customFormData_]);\n    }\n  }, [state.teamMbrUserInfo.mbrUserInfo]);\n\n  // 修改图片\n  const modifyAvater = () => {\n    setIsModalVisible(true);\n  };\n\n  // 上传头像\n  const avatarUpload = link => {\n    let formData = form.getFieldsValue(true);\n    let params = {\n      teamId: teamId,\n      avatar: link,\n      userName: formData.userName.formItemValue,\n      userId: state.teamMbrUserInfo.mbrUserInfo.userId,\n      email: formData.email.formItemValue,\n      mobileNo: formData.mobileNo.formItemValue,\n      emailSubscribeMyChanges: state.teamMbrUserInfo.subscribeInfo.emailSubscribeMyChanges,\n      watchMyChangesFlg: state.teamMbrUserInfo.subscribeInfo.watchMyChangesFlg\n    };\n    setting_225_update_user_info(params).then(res => {\n      if (res.resultCode === 200) {\n        dispatch(get_team_mbr_user_info(teamId));\n      }\n    });\n  };\n  const updateUserInfo = label => {\n    let formData = form.getFieldsValue(true);\n    let params = {\n      teamId: teamId,\n      userName: formData.userName.formItemValue,\n      userId: state.teamMbrUserInfo.mbrUserInfo.userId,\n      email: formData.email.formItemValue,\n      mobileNo: formData.mobileNo.formItemValue\n    };\n    setting_225_update_user_info(params).then(res => {\n      if (res.resultCode === 200) {\n        dispatch(get_team_mbr_user_info(teamId));\n        queryClient.invalidateQueries(['get_team_allusergrp']);\n        globalUtil.success(`${label}更新成功`);\n        //tmsbug-4221:新建团队，新建文档，期望“创建者”信息显示成员名，而不是用户名(手机号码)  原因：用户名修改后需要刷新用户数据 \n        globalUtil.getQueryClient().invalidateQueries([setting202, teamId]);\n      }\n    });\n  };\n  const navigateTo = () => {\n    let url = getRootUrl() + '/' + teamId + '/settings/personal';\n    window.open(url);\n  };\n\n  // 删除头像\n  const deleteLogo = () => {\n    avatarUpload(''); //20230312 null -> ''\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Basic\",\n    children: [flag == 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"Basic-tips\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"iconfont tishi\",\n        style: {\n          color: '#F59A23',\n          marginRight: 4,\n          fontSize: 18\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 15\n      }, this), \"\\u4E3A\\u8BA9\\u5176\\u4ED6\\u56E2\\u961F\\u6210\\u5458\\u5F88\\u5BB9\\u6613\\u8FA8\\u8BC6\\u60A8\\uFF0C\\u8BF7\\u4FEE\\u6539\\u5982\\u4E0B\\u6210\\u5458\\u4FE1\\u606F\\u3002\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 11\n    }, this), flag == 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: '#999',\n        marginLeft: 23\n      },\n      children: [\"\\u540E\\u7EED\\u60A8\\u4E5F\\u53EF\\u4EE5\\u5728\", /*#__PURE__*/_jsxDEV(\"a\", {\n        style: {\n          marginLeft: 8,\n          marginRight: 8\n        },\n        onClick: navigateTo,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"iconfont shezhixitongshezhigongnengshezhishuxing fontsize-14\",\n          style: {\n            color: '#999'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this), \"\\u8BBE\\u7F6E\\xA0->\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"fontsize-10 iconfont chengyuan treeIconStyle\",\n          style: {\n            color: '#999'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), \"\\u4E2A\\u4EBA\\u8BBE\\u7F6E(\\u56E2\\u961F\\u5185)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this), \"\\u9875\\u9762\\u4E2D\\u518D\\u6B21\\u4FEE\\u6539\\u3002\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      className: \"Basic-form\",\n      colon: false,\n      form: form,\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u5934\\u50CF\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"Basic-form-avatar\",\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            className: \"Basic-avatar\",\n            src: (_state$teamMbrUserInf7 = state.teamMbrUserInfo) === null || _state$teamMbrUserInf7 === void 0 ? void 0 : (_state$teamMbrUserInf8 = _state$teamMbrUserInf7.mbrUserInfo) === null || _state$teamMbrUserInf8 === void 0 ? void 0 : _state$teamMbrUserInf8.avatar,\n            icon: /*#__PURE__*/_jsxDEV(NoAvatarIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"Basic-form-upload\",\n            children: /*#__PURE__*/_jsxDEV(CameraOutlined, {\n              className: \"Basic-form-uploadIcon\",\n              onClick: event => modifyAvater(true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 21\n          }, this), !!((_state$teamMbrUserInf9 = state.teamMbrUserInfo) !== null && _state$teamMbrUserInf9 !== void 0 && (_state$teamMbrUserInf10 = _state$teamMbrUserInf9.mbrUserInfo) !== null && _state$teamMbrUserInf10 !== void 0 && _state$teamMbrUserInf10.avatar) && /*#__PURE__*/_jsxDEV(CloseCircleFilled, {\n            title: \"\\u5220\\u9664\\u5934\\u50CF\",\n            className: \"delete-Basic-avatar\",\n            onClick: deleteLogo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 70\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n          title: \"\\u4E0A\\u4F20logo\",\n          centered: true,\n          className: \"avatarUpload-modal\",\n          open: isModalVisible,\n          onCancel: () => setIsModalVisible(false),\n          footer: null,\n          children: /*#__PURE__*/_jsxDEV(PersonalBasicTab.ImgUpload, {\n            avatarUpload: avatarUpload,\n            onCancel: () => setIsModalVisible(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 13\n      }, this), customFormData.map((memberInfo, index) => {\n        if (memberInfo.tipsFlag) {\n          return /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: memberInfo.label,\n            className: \"customFormItem\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: memberInfo.name,\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(PersonalBasicTab.MemberFromItem, {\n                memberItem: memberInfo,\n                updateUserInfo: updateUserInfo\n              }, memberInfo.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"formItem-tips\",\n              children: memberInfo.tips\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 19\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: memberInfo.label,\n            name: memberInfo.name,\n            children: /*#__PURE__*/_jsxDEV(PersonalBasicTab.MemberFromItem, {\n              memberItem: memberInfo,\n              updateUserInfo: updateUserInfo\n            }, memberInfo.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 19\n          }, this);\n        }\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this), flag == 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"Basic-tips\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"iconfont tishi\",\n        style: {\n          color: '#F59A23',\n          marginRight: 4,\n          fontSize: 18\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 13\n      }, this), \"\\u4E2A\\u4EBA\\u4FE1\\u606F\\u4FEE\\u6539\\u540E\\uFF0C\\u53EA\\u4F1A\\u5728\\u6B64\\u56E2\\u961F\\u5185\\u663E\\u793A\\uFF0C\\u56E2\\u961F\\u5185\\u6210\\u5458\\u90FD\\u53EF\\u4EE5\\u770B\\u89C1\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n}\n\n// 图片上传\n_s(PersonalBasicTab, \"TS7pNdl52z9SY5RIGQlWna5Prxs=\", false, function () {\n  return [Form.useForm, useDispatch, useQueryClient, useParams, useSelector];\n});\n_c = PersonalBasicTab;\nPersonalBasicTab.ImgUpload = function _(props) {\n  const {\n    avatarUpload,\n    onCancel\n  } = props;\n  const dataSource = {\n    maxCount: 1,\n    name: \"file\",\n    multiple: false,\n    showUploadList: false,\n    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,\n    beforeUpload: file => {\n      const isPNG = file.type === \"image/png\" || file.type === 'image/jpeg';\n      if (!isPNG) {\n        globalUtil.error(`${file.name}不是图片格式`);\n      }\n      return isPNG || Upload.LIST_IGNORE;\n    },\n    onChange(info) {\n      onCancel();\n      const {\n        status,\n        response\n      } = info.file;\n      if (status == \"uploading\") {\n        console.log(info.file, info.fileList);\n      }\n      if (status === \"done\") {\n        if (response.resultCode == 200) {\n          avatarUpload(response.link);\n          globalUtil.success('上传成功');\n        } else {\n          globalUtil.error(\"上传失败\");\n        }\n      } else if (status === \"error\") {\n        globalUtil.error(`${info.file.name} file upload failed.`);\n      }\n    }\n  };\n\n  // 预览/裁剪图片\n  const onPreview = async file => {\n    let src = file.url;\n    if (!src) {\n      src = await new Promise(resolve => {\n        const reader = new FileReader();\n        reader.readAsDataURL(file.originFileObj);\n        reader.onload = () => resolve(reader.result);\n      });\n    }\n    const image = new Image();\n    image.src = src;\n    const imgWindow = window.open(src);\n    imgWindow === null || imgWindow === void 0 ? void 0 : imgWindow.document.write(image.outerHTML);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(ImgCrop, {\n      modalClassName: \"clippingImgCrop\",\n      rotate: true,\n      modalTitle: \"编辑图片\",\n      modalOk: \"\\u786E\\u8BA4\",\n      modalCancel: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Dragger, {\n        ...dataSource,\n        onPreview: onPreview,\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"ant-upload-drag-icon\",\n          children: /*#__PURE__*/_jsxDEV(QuestionsUploadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"ant-upload-text\",\n          children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u52A8\\u56FE\\u7247\\u81F3\\u6B64\\u5904\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#999',\n            marginTop: 2\n          },\n          children: \"\\u56FE\\u7247\\u683C\\u5F0F\\uFF1Ajpg\\u3001png\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n\n// 自定义form表单item\nPersonalBasicTab.MemberFromItem = _s2(function _({\n  memberItem,\n  updateUserInfo,\n  value = '',\n  onChange\n}) {\n  _s2();\n  const [modifyFlag, setModifyFlag] = useState(false);\n  const [formItemValue, setFormItemValue] = useState('');\n  const modifyRef = useRef();\n  const triggerChange = changedValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange({\n      formItemValue,\n      ...value,\n      ...changedValue\n    });\n  };\n  useEffect(() => {\n    setFormItemValue(memberItem.value);\n    triggerChange({\n      formItemValue: memberItem.value\n    });\n  }, []);\n  useEffect(() => {\n    if (modifyFlag && memberItem.name != '') {\n      modifyRef.current.focus({\n        cursor: \"end\"\n      });\n    }\n  }, [modifyFlag]);\n  const modifyClick = () => {\n    setModifyFlag(true);\n  };\n  const modifyBlur = e => {\n    if (!e.target.value && memberItem.key != 'email') {\n      modifyRef.current.focus({\n        cursor: \"end\"\n      });\n      globalUtil.warning('不能为空');\n      return;\n    }\n    var str = e.target.value.replace(/\\s/g, \"\"); //去除空格\n    if (memberItem.key == 'mobileNo') {\n      let reg = /^1[3-9][0-9]{9}$/;\n      if (!reg.test(str)) {\n        globalUtil.warning('请输入正确的手机号码');\n        return;\n      }\n    }\n    if (memberItem.key == 'email' && !!str) {\n      let regs = /^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$/;\n      if (!regs.test(str)) {\n        globalUtil.warning('请输入正确的邮箱');\n        return;\n      }\n    }\n    setModifyFlag(false);\n    setFormItemValue(e.target.value);\n    triggerChange({\n      formItemValue: e.target.value\n    });\n    if (memberItem.value === e.target.value) return;\n    updateUserInfo(memberItem.label);\n  };\n\n  // 获取form item编辑样式\n  const getEditFormItemValue = () => /*#__PURE__*/_jsxDEV(Input, {\n    className: \"MemberFromItem-inpvalue\",\n    autoComplete: \"off\",\n    style: {\n      width: 240\n    },\n    defaultValue: formItemValue,\n    ref: modifyRef,\n    onBlur: modifyBlur,\n    onPressEnter: modifyBlur\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 323,\n    columnNumber: 5\n  }, this);\n  // 格式化form item只读状态样式\n  const getReadFormItemValue = () => formItemValue;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Basic-MemberFromItem\",\n    style: {\n      width: 240\n    },\n    children: [modifyFlag ? getEditFormItemValue(memberItem) : /*#__PURE__*/_jsxDEV(\"span\", {\n      children: getReadFormItemValue()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 13\n    }, this), !modifyFlag && /*#__PURE__*/_jsxDEV(Button, {\n      className: \"Basic-MemberFromItem-editIcon\",\n      size: \"small\",\n      type: \"link\",\n      icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 19\n      }, this),\n      onClick: modifyClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 336,\n    columnNumber: 5\n  }, this);\n}, \"i6KSyaAotEHw0EfyBA+aOIsFd5E=\");\nvar _c;\n$RefreshReg$(_c, \"PersonalBasicTab\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Avatar", "<PERSON><PERSON>", "Input", "Form", "Upload", "DraggablePopUp", "CloseCircleFilled", "EditOutlined", "CameraOutlined", "useParams", "shallowEqual", "useDispatch", "useSelector", "NoAvatarIcon", "QuestionsUploadIcon", "ImgCrop", "get_team_mbr_user_info", "setting_225_update_user_info", "globalUtil", "getRootUrl", "setting202", "useQueryClient", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "PersonalBasicTab", "flag", "_s", "_state$teamMbrUserInf7", "_state$teamMbrUserInf8", "_state$teamMbrUserInf9", "_state$teamMbrUserInf10", "form", "useForm", "dispatch", "queryClient", "teamId", "isModalVisible", "setIsModalVisible", "customFormData", "setCustomFormData", "state", "teamMbrUserInfo", "getIn", "mbrUserInfo", "_state$teamMbrUserInf", "_state$teamMbrUserInf2", "_state$teamMbrUserInf3", "_state$teamMbrUserInf4", "_state$teamMbrUserInf5", "_state$teamMbrUserInf6", "customFormData_", "key", "label", "name", "value", "userName", "tipsFlag", "tips", "mobileNo", "email", "modifyAvater", "avatarUpload", "link", "formData", "getFieldsValue", "params", "avatar", "formItemValue", "userId", "emailSubscribeMyChanges", "subscribeInfo", "watchMyChangesFlg", "then", "res", "resultCode", "updateUserInfo", "invalidateQueries", "success", "getQueryClient", "navigateTo", "url", "window", "open", "deleteLogo", "className", "children", "style", "color", "marginRight", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "onClick", "colon", "<PERSON><PERSON>", "src", "icon", "event", "title", "centered", "onCancel", "footer", "ImgUpload", "map", "memberInfo", "index", "noStyle", "MemberFromItem", "memberItem", "_c", "_", "props", "dataSource", "maxCount", "multiple", "showUploadList", "action", "process", "env", "REACT_APP_BASE_URL", "beforeUpload", "file", "isPNG", "type", "error", "LIST_IGNORE", "onChange", "info", "status", "response", "console", "log", "fileList", "onPreview", "Promise", "resolve", "reader", "FileReader", "readAsDataURL", "originFileObj", "onload", "result", "image", "Image", "imgWindow", "document", "write", "outerHTML", "modalClassName", "rotate", "modalTitle", "modalOk", "modalCancel", "marginTop", "_s2", "modifyFlag", "setModifyFlag", "setFormItemValue", "modifyRef", "trigger<PERSON>hange", "changedValue", "current", "focus", "cursor", "modifyClick", "modifyBlur", "e", "target", "warning", "str", "replace", "reg", "test", "regs", "getEditFormItemValue", "autoComplete", "width", "defaultValue", "ref", "onBlur", "onPressEnter", "getReadFormItemValue", "size", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/PersonalBasicTab.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Avatar,Button,Input,Form,Upload } from \"antd\";\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";\r\nimport { CloseCircleFilled, EditOutlined, CameraOutlined } from \"@ant-design/icons\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { shallowEqual, useDispatch, useSelector } from \"react-redux\";\r\nimport { NoAvatarIcon } from '@components/IconUtil';\r\nimport { QuestionsUploadIcon } from \"@components/IconUtil\";\r\nimport ImgCrop from \"antd-img-crop\";\r\nimport { get_team_mbr_user_info } from \"@/settings/store/actionCreators\"\r\nimport {\r\n  setting_225_update_user_info,\r\n} from '@common/api/http';\r\nimport \"./PersonalBasicTab.scss\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { getRootUrl } from \"@common/router/RouterRegister\";\r\nimport { setting202 } from \"@common/utils/ApiPath\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\n\r\nconst { Dragger } = Upload;\r\n\r\n\r\n// 基本信息\r\nexport default function PersonalBasicTab({flag}) {\r\n  const [form] = Form.useForm();\r\n  const dispatch = useDispatch();\r\n  const queryClient = useQueryClient();\r\n  const { teamId } = useParams();\r\n  const [isModalVisible, setIsModalVisible] = useState(false);\r\n  const [customFormData, setCustomFormData] = useState([]);\r\n  const state = useSelector((state) => ({\r\n    teamMbrUserInfo: state.getIn([\"workSetUp\", \"teamMbrUserInfo\"])\r\n  }), shallowEqual);\r\n\r\n  useEffect(() => {\r\n    if(!!state.teamMbrUserInfo.mbrUserInfo) {\r\n      let customFormData_ = [\r\n        {\r\n          key: 'userName',\r\n          label: '成员名',\r\n          name: 'userName',\r\n          value: state.teamMbrUserInfo?.mbrUserInfo?.userName,\r\n          tipsFlag: 1,\r\n          tips: '我在团队中的昵称'\r\n        },\r\n        {\r\n          key: 'mobileNo',\r\n          label: '手机号',\r\n          name: 'mobileNo',\r\n          value: state.teamMbrUserInfo?.mbrUserInfo?.mobileNo,\r\n          tipsFlag: 1,\r\n          tips: '此号码仅在本团队可见'\r\n        },\r\n        {\r\n          key: 'email',\r\n          label: '邮箱',\r\n          name: 'email',\r\n          value: state.teamMbrUserInfo?.mbrUserInfo?.email,\r\n          tipsFlag: 1,\r\n          tips: '接收团队邮件通知'\r\n        },\r\n      ]\r\n      setCustomFormData([...customFormData_])\r\n    }\r\n  },[state.teamMbrUserInfo.mbrUserInfo])\r\n\r\n  // 修改图片\r\n  const modifyAvater = () => {\r\n    setIsModalVisible(true);\r\n  }\r\n\r\n  // 上传头像\r\n  const avatarUpload = (link) => {\r\n    let formData = form.getFieldsValue(true)\r\n    let params = {\r\n      teamId: teamId,\r\n      avatar: link,\r\n      userName: formData.userName.formItemValue,\r\n      userId: state.teamMbrUserInfo.mbrUserInfo.userId,\r\n      email: formData.email.formItemValue,\r\n      mobileNo: formData.mobileNo.formItemValue,\r\n      emailSubscribeMyChanges: state.teamMbrUserInfo.subscribeInfo.emailSubscribeMyChanges,\r\n      watchMyChangesFlg: state.teamMbrUserInfo.subscribeInfo.watchMyChangesFlg\r\n    };\r\n    setting_225_update_user_info(params).then((res) => {\r\n      if (res.resultCode === 200) {\r\n        dispatch(get_team_mbr_user_info(teamId))\r\n      }\r\n    });\r\n  }\r\n\r\n  const updateUserInfo = (label) => {\r\n    let formData = form.getFieldsValue(true)\r\n    let params = {\r\n      teamId: teamId,\r\n      userName: formData.userName.formItemValue,\r\n      userId: state.teamMbrUserInfo.mbrUserInfo.userId,\r\n      email: formData.email.formItemValue,\r\n      mobileNo: formData.mobileNo.formItemValue,\r\n    };\r\n    setting_225_update_user_info(params).then((res) => {\r\n      if (res.resultCode === 200) {\r\n        dispatch(get_team_mbr_user_info(teamId));\r\n        queryClient.invalidateQueries(['get_team_allusergrp']);\r\n        globalUtil.success(`${label}更新成功`);\r\n        //tmsbug-4221:新建团队，新建文档，期望“创建者”信息显示成员名，而不是用户名(手机号码)  原因：用户名修改后需要刷新用户数据 \r\n        globalUtil.getQueryClient().invalidateQueries( [setting202, teamId]);\r\n      }\r\n    });\r\n  }\r\n\r\n  const navigateTo = () => {\r\n    let url = getRootUrl() + '/' + teamId + '/settings/personal';\r\n    window.open(url);\r\n  }\r\n\r\n  // 删除头像\r\n  const deleteLogo = () => {\r\n    avatarUpload('') //20230312 null -> ''\r\n  }\r\n  return (\r\n    <div className=\"Basic\">\r\n        {flag == 1 &&\r\n          <span className=\"Basic-tips\">\r\n              <span className=\"iconfont tishi\" style={{color:'#F59A23',marginRight: 4, fontSize: 18}}/>\r\n              为让其他团队成员很容易辨识您，请修改如下成员信息。\r\n          </span>\r\n        }\r\n        {flag == 1 && \r\n        <div style={{color:'#999',marginLeft:23}}>\r\n          后续您也可以在\r\n          <a style={{marginLeft:8,marginRight:8}} onClick={navigateTo}>\r\n            <span className=\"iconfont shezhixitongshezhigongnengshezhishuxing fontsize-14\" style={{color:'#999'}}/>设置&nbsp;-&gt;&nbsp;\r\n            <span className=\"fontsize-10 iconfont chengyuan treeIconStyle\" style={{color:'#999'}}/>个人设置(团队内)\r\n          </a>\r\n          页面中再次修改。\r\n        </div>\r\n        }\r\n        <Form className=\"Basic-form\" colon={false} form={form}>\r\n            <Form.Item label=\"头像\">\r\n                <div className=\"Basic-form-avatar\">\r\n                    <Avatar\r\n                      className=\"Basic-avatar\"\r\n                      src={state.teamMbrUserInfo?.mbrUserInfo?.avatar}\r\n                      icon={<NoAvatarIcon/>}\r\n                    />\r\n                    <div className=\"Basic-form-upload\">\r\n                    <CameraOutlined\r\n                      className=\"Basic-form-uploadIcon\"\r\n                      onClick={(event) => modifyAvater(true)}/>\r\n                    </div>\r\n                    {!!state.teamMbrUserInfo?.mbrUserInfo?.avatar && <CloseCircleFilled title=\"删除头像\" className=\"delete-Basic-avatar\" onClick={deleteLogo}/>}\r\n                </div>\r\n                <DraggablePopUp\r\n                title=\"上传logo\"\r\n                centered\r\n                className=\"avatarUpload-modal\"\r\n                open={isModalVisible}\r\n                onCancel={() => setIsModalVisible(false)}\r\n                footer={null}\r\n                >\r\n                  <PersonalBasicTab.ImgUpload avatarUpload={avatarUpload} onCancel={() => setIsModalVisible(false)}/>\r\n                </DraggablePopUp>\r\n            </Form.Item>\r\n            {customFormData.map((memberInfo, index) => {\r\n              if(memberInfo.tipsFlag) {\r\n                return (\r\n                  <Form.Item label={memberInfo.label} className=\"customFormItem\">\r\n                    <Form.Item name={memberInfo.name} noStyle>\r\n                      <PersonalBasicTab.MemberFromItem key={memberInfo.name} memberItem={memberInfo} updateUserInfo={updateUserInfo}/>\r\n                    </Form.Item>\r\n                    <span className=\"formItem-tips\">{memberInfo.tips}</span>\r\n                  </Form.Item>\r\n                )\r\n              } else {\r\n                return (\r\n                  <Form.Item label={memberInfo.label} name={memberInfo.name}>\r\n                    <PersonalBasicTab.MemberFromItem key={memberInfo.name} memberItem={memberInfo} updateUserInfo={updateUserInfo}/>\r\n                  </Form.Item>\r\n                )\r\n              }\r\n            })}\r\n        </Form>\r\n        {flag == 0 && \r\n          <span className=\"Basic-tips\">\r\n            <span className=\"iconfont tishi\" style={{color:'#F59A23',marginRight: 4, fontSize: 18}}/>\r\n            个人信息修改后，只会在此团队内显示，团队内成员都可以看见\r\n          </span>\r\n        }\r\n    </div>\r\n  );\r\n}\r\n\r\n// 图片上传\r\nPersonalBasicTab.ImgUpload = function _(props) {\r\n  const {avatarUpload,onCancel} = props\r\n  const dataSource = {\r\n    maxCount: 1,\r\n    name: \"file\",\r\n    multiple: false,\r\n    showUploadList: false,\r\n    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,\r\n    beforeUpload: (file) => {\r\n      const isPNG = file.type === \"image/png\" || file.type === 'image/jpeg';\r\n      if (!isPNG) {\r\n        globalUtil.error(`${file.name}不是图片格式`);\r\n      }\r\n      return isPNG || Upload.LIST_IGNORE;\r\n    },\r\n    onChange(info) {\r\n      onCancel()\r\n      const { status, response } = info.file;\r\n      if (status == \"uploading\") {\r\n        console.log(info.file, info.fileList);\r\n      }\r\n      if (status === \"done\") {\r\n        if(response.resultCode == 200) {\r\n          avatarUpload(response.link)\r\n          globalUtil.success('上传成功');\r\n        } else {\r\n          globalUtil.error(\"上传失败\")\r\n        }\r\n      } else if (status === \"error\") {\r\n        globalUtil.error(`${info.file.name} file upload failed.`);\r\n      }\r\n    },\r\n  };\r\n  \r\n  // 预览/裁剪图片\r\n  const onPreview = async (file) => {\r\n    let src = file.url;\r\n    if (!src) {\r\n      src = await new Promise((resolve) => {\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file.originFileObj);\r\n        reader.onload = () => resolve(reader.result);\r\n      });\r\n    }\r\n    const image = new Image();\r\n    image.src = src;\r\n    const imgWindow = window.open(src);\r\n    imgWindow?.document.write(image.outerHTML);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <ImgCrop \r\n      modalClassName=\"clippingImgCrop\" \r\n      rotate modalTitle={\"编辑图片\"} \r\n      modalOk=\"确认\" \r\n      modalCancel=\"取消\"\r\n      >\r\n        <Dragger {...dataSource} onPreview={onPreview}>\r\n          <p className=\"ant-upload-drag-icon\">\r\n            <QuestionsUploadIcon />\r\n          </p>\r\n          <p className=\"ant-upload-text\">点击或拖动图片至此处</p>\r\n          <p style={{color:'#999',marginTop:2}}>图片格式：jpg、png</p>\r\n        </Dragger>\r\n      </ImgCrop>\r\n    </>\r\n  );\r\n}\r\n\r\n// 自定义form表单item\r\nPersonalBasicTab.MemberFromItem = function _({ memberItem, updateUserInfo, value='', onChange }) {\r\n  const [modifyFlag, setModifyFlag] = useState(false);\r\n  const [formItemValue, setFormItemValue] = useState('')\r\n  const modifyRef = useRef();\r\n  const triggerChange = (changedValue) => {\r\n    onChange?.({\r\n      formItemValue,\r\n      ...value,\r\n      ...changedValue,\r\n    });\r\n  };\r\n  useEffect(() => {\r\n    setFormItemValue(memberItem.value)\r\n    triggerChange({\r\n      formItemValue: memberItem.value,\r\n    });\r\n  },[])\r\n  useEffect(() => {\r\n    if (modifyFlag && memberItem.name != '') {\r\n      modifyRef.current.focus({ cursor: \"end\" });\r\n    }\r\n  }, [modifyFlag]);\r\n  const modifyClick = () => {\r\n    setModifyFlag(true);\r\n  };\r\n  const modifyBlur = (e) => {\r\n    if(!e.target.value && memberItem.key != 'email') {\r\n      modifyRef.current.focus({ cursor: \"end\" });\r\n      globalUtil.warning('不能为空')\r\n      return\r\n    }\r\n    var str = e.target.value.replace(/\\s/g, \"\"); //去除空格\r\n    if(memberItem.key == 'mobileNo'){\r\n      let reg = /^1[3-9][0-9]{9}$/\r\n      if(!reg.test(str)){\r\n        globalUtil.warning('请输入正确的手机号码')\r\n        return\r\n      }\r\n    }\r\n    if(memberItem.key == 'email' && !!str){\r\n      let regs = /^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$/;\r\n      if(!regs.test(str)){\r\n        globalUtil.warning('请输入正确的邮箱')\r\n        return\r\n      }\r\n    }\r\n    setModifyFlag(false);\r\n    setFormItemValue(e.target.value)\r\n    triggerChange({\r\n      formItemValue: e.target.value,\r\n    });\r\n    if(memberItem.value === e.target.value) return\r\n    updateUserInfo(memberItem.label);\r\n  };\r\n\r\n  // 获取form item编辑样式\r\n  const getEditFormItemValue = () => (\r\n    <Input\r\n    className=\"MemberFromItem-inpvalue\"\r\n    autoComplete=\"off\"\r\n    style={{ width: 240 }}\r\n    defaultValue={formItemValue}\r\n    ref={modifyRef}\r\n    onBlur={modifyBlur}\r\n    onPressEnter={modifyBlur}\r\n    />\r\n  )\r\n  // 格式化form item只读状态样式\r\n  const getReadFormItemValue = () => formItemValue\r\n  return (\r\n    <div className=\"Basic-MemberFromItem\" style={{ width: 240 }}>\r\n        {modifyFlag\r\n         ? getEditFormItemValue(memberItem)\r\n          : <span>{getReadFormItemValue()}</span>}\r\n        {!modifyFlag && (\r\n          <Button\r\n            className=\"Basic-MemberFromItem-editIcon\"\r\n            size=\"small\"\r\n            type=\"link\"\r\n            icon={<EditOutlined />}\r\n            onClick={modifyClick}\r\n          />\r\n        )}\r\n    </div>\r\n  );\r\n}"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAACC,MAAM,EAACC,KAAK,EAACC,IAAI,EAACC,MAAM,QAAQ,MAAM;AACtD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,iBAAiB,EAAEC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AACnF,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SACEC,4BAA4B,QACvB,kBAAkB;AACzB,OAAO,yBAAyB;AAChC,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,cAAc,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAM;EAAEC;AAAQ,CAAC,GAAGtB,MAAM;;AAG1B;AACA,eAAe,SAASuB,gBAAgBA,CAAC;EAACC;AAAI,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;EAC/C,MAAM,CAACC,IAAI,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,WAAW,GAAGhB,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEiB;EAAO,CAAC,GAAG7B,SAAS,CAAC,CAAC;EAC9B,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM6C,KAAK,GAAG/B,WAAW,CAAE+B,KAAK,KAAM;IACpCC,eAAe,EAAED,KAAK,CAACE,KAAK,CAAC,CAAC,WAAW,EAAE,iBAAiB,CAAC;EAC/D,CAAC,CAAC,EAAEnC,YAAY,CAAC;EAEjBb,SAAS,CAAC,MAAM;IACd,IAAG,CAAC,CAAC8C,KAAK,CAACC,eAAe,CAACE,WAAW,EAAE;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACtC,IAAIC,eAAe,GAAG,CACpB;QACEC,GAAG,EAAE,UAAU;QACfC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE,UAAU;QAChBC,KAAK,GAAAV,qBAAA,GAAEJ,KAAK,CAACC,eAAe,cAAAG,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBD,WAAW,cAAAE,sBAAA,uBAAlCA,sBAAA,CAAoCU,QAAQ;QACnDC,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE;MACR,CAAC,EACD;QACEN,GAAG,EAAE,UAAU;QACfC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE,UAAU;QAChBC,KAAK,GAAAR,sBAAA,GAAEN,KAAK,CAACC,eAAe,cAAAK,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBH,WAAW,cAAAI,sBAAA,uBAAlCA,sBAAA,CAAoCW,QAAQ;QACnDF,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE;MACR,CAAC,EACD;QACEN,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,OAAO;QACbC,KAAK,GAAAN,sBAAA,GAAER,KAAK,CAACC,eAAe,cAAAO,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBL,WAAW,cAAAM,sBAAA,uBAAlCA,sBAAA,CAAoCU,KAAK;QAChDH,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE;MACR,CAAC,CACF;MACDlB,iBAAiB,CAAC,CAAC,GAAGW,eAAe,CAAC,CAAC;IACzC;EACF,CAAC,EAAC,CAACV,KAAK,CAACC,eAAe,CAACE,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzBvB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMwB,YAAY,GAAIC,IAAI,IAAK;IAC7B,IAAIC,QAAQ,GAAGhC,IAAI,CAACiC,cAAc,CAAC,IAAI,CAAC;IACxC,IAAIC,MAAM,GAAG;MACX9B,MAAM,EAAEA,MAAM;MACd+B,MAAM,EAAEJ,IAAI;MACZP,QAAQ,EAAEQ,QAAQ,CAACR,QAAQ,CAACY,aAAa;MACzCC,MAAM,EAAE5B,KAAK,CAACC,eAAe,CAACE,WAAW,CAACyB,MAAM;MAChDT,KAAK,EAAEI,QAAQ,CAACJ,KAAK,CAACQ,aAAa;MACnCT,QAAQ,EAAEK,QAAQ,CAACL,QAAQ,CAACS,aAAa;MACzCE,uBAAuB,EAAE7B,KAAK,CAACC,eAAe,CAAC6B,aAAa,CAACD,uBAAuB;MACpFE,iBAAiB,EAAE/B,KAAK,CAACC,eAAe,CAAC6B,aAAa,CAACC;IACzD,CAAC;IACDzD,4BAA4B,CAACmD,MAAM,CAAC,CAACO,IAAI,CAAEC,GAAG,IAAK;MACjD,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1BzC,QAAQ,CAACpB,sBAAsB,CAACsB,MAAM,CAAC,CAAC;MAC1C;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwC,cAAc,GAAIvB,KAAK,IAAK;IAChC,IAAIW,QAAQ,GAAGhC,IAAI,CAACiC,cAAc,CAAC,IAAI,CAAC;IACxC,IAAIC,MAAM,GAAG;MACX9B,MAAM,EAAEA,MAAM;MACdoB,QAAQ,EAAEQ,QAAQ,CAACR,QAAQ,CAACY,aAAa;MACzCC,MAAM,EAAE5B,KAAK,CAACC,eAAe,CAACE,WAAW,CAACyB,MAAM;MAChDT,KAAK,EAAEI,QAAQ,CAACJ,KAAK,CAACQ,aAAa;MACnCT,QAAQ,EAAEK,QAAQ,CAACL,QAAQ,CAACS;IAC9B,CAAC;IACDrD,4BAA4B,CAACmD,MAAM,CAAC,CAACO,IAAI,CAAEC,GAAG,IAAK;MACjD,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1BzC,QAAQ,CAACpB,sBAAsB,CAACsB,MAAM,CAAC,CAAC;QACxCD,WAAW,CAAC0C,iBAAiB,CAAC,CAAC,qBAAqB,CAAC,CAAC;QACtD7D,UAAU,CAAC8D,OAAO,CAAC,GAAGzB,KAAK,MAAM,CAAC;QAClC;QACArC,UAAU,CAAC+D,cAAc,CAAC,CAAC,CAACF,iBAAiB,CAAE,CAAC3D,UAAU,EAAEkB,MAAM,CAAC,CAAC;MACtE;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM4C,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIC,GAAG,GAAGhE,UAAU,CAAC,CAAC,GAAG,GAAG,GAAGmB,MAAM,GAAG,oBAAoB;IAC5D8C,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC;EAClB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvBtB,YAAY,CAAC,EAAE,CAAC,EAAC;EACnB,CAAC;EACD,oBACEzC,OAAA;IAAKgE,SAAS,EAAC,OAAO;IAAAC,QAAA,GACjB5D,IAAI,IAAI,CAAC,iBACRL,OAAA;MAAMgE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACxBjE,OAAA;QAAMgE,SAAS,EAAC,gBAAgB;QAACE,KAAK,EAAE;UAACC,KAAK,EAAC,SAAS;UAACC,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,0JAE7F;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAERpE,IAAI,IAAI,CAAC,iBACVL,OAAA;MAAKkE,KAAK,EAAE;QAACC,KAAK,EAAC,MAAM;QAACO,UAAU,EAAC;MAAE,CAAE;MAAAT,QAAA,GAAC,4CAExC,eAAAjE,OAAA;QAAGkE,KAAK,EAAE;UAACQ,UAAU,EAAC,CAAC;UAACN,WAAW,EAAC;QAAC,CAAE;QAACO,OAAO,EAAEhB,UAAW;QAAAM,QAAA,gBAC1DjE,OAAA;UAAMgE,SAAS,EAAC,8DAA8D;UAACE,KAAK,EAAE;YAACC,KAAK,EAAC;UAAM;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,0BACvG,eAAAzE,OAAA;UAAMgE,SAAS,EAAC,8CAA8C;UAACE,KAAK,EAAE;YAACC,KAAK,EAAC;UAAM;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,gDACzF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,oDAEN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAENzE,OAAA,CAACpB,IAAI;MAACoF,SAAS,EAAC,YAAY;MAACY,KAAK,EAAE,KAAM;MAACjE,IAAI,EAAEA,IAAK;MAAAsD,QAAA,gBAClDjE,OAAA,CAACpB,IAAI,CAACiG,IAAI;QAAC7C,KAAK,EAAC,cAAI;QAAAiC,QAAA,gBACjBjE,OAAA;UAAKgE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BjE,OAAA,CAACvB,MAAM;YACLuF,SAAS,EAAC,cAAc;YACxBc,GAAG,GAAAvE,sBAAA,GAAEa,KAAK,CAACC,eAAe,cAAAd,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBgB,WAAW,cAAAf,sBAAA,uBAAlCA,sBAAA,CAAoCsC,MAAO;YAChDiC,IAAI,eAAE/E,OAAA,CAACV,YAAY;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACFzE,OAAA;YAAKgE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAClCjE,OAAA,CAACf,cAAc;cACb+E,SAAS,EAAC,uBAAuB;cACjCW,OAAO,EAAGK,KAAK,IAAKxC,YAAY,CAAC,IAAI;YAAE;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,EACL,CAAC,GAAAhE,sBAAA,GAACW,KAAK,CAACC,eAAe,cAAAZ,sBAAA,gBAAAC,uBAAA,GAArBD,sBAAA,CAAuBc,WAAW,cAAAb,uBAAA,eAAlCA,uBAAA,CAAoCoC,MAAM,kBAAI9C,OAAA,CAACjB,iBAAiB;YAACkG,KAAK,EAAC,0BAAM;YAACjB,SAAS,EAAC,qBAAqB;YAACW,OAAO,EAAEZ;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtI,CAAC,eACNzE,OAAA,CAAClB,cAAc;UACfmG,KAAK,EAAC,kBAAQ;UACdC,QAAQ;UACRlB,SAAS,EAAC,oBAAoB;UAC9BF,IAAI,EAAE9C,cAAe;UACrBmE,QAAQ,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,KAAK,CAAE;UACzCmE,MAAM,EAAE,IAAK;UAAAnB,QAAA,eAEXjE,OAAA,CAACI,gBAAgB,CAACiF,SAAS;YAAC5C,YAAY,EAAEA,YAAa;YAAC0C,QAAQ,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,KAAK;UAAE;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACXvD,cAAc,CAACoE,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACzC,IAAGD,UAAU,CAACnD,QAAQ,EAAE;UACtB,oBACEpC,OAAA,CAACpB,IAAI,CAACiG,IAAI;YAAC7C,KAAK,EAAEuD,UAAU,CAACvD,KAAM;YAACgC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC5DjE,OAAA,CAACpB,IAAI,CAACiG,IAAI;cAAC5C,IAAI,EAAEsD,UAAU,CAACtD,IAAK;cAACwD,OAAO;cAAAxB,QAAA,eACvCjE,OAAA,CAACI,gBAAgB,CAACsF,cAAc;gBAAuBC,UAAU,EAAEJ,UAAW;gBAAChC,cAAc,EAAEA;cAAe,GAAxEgC,UAAU,CAACtD,IAAI;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA0D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC,eACZzE,OAAA;cAAMgE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEsB,UAAU,CAAClD;YAAI;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAEhB,CAAC,MAAM;UACL,oBACEzE,OAAA,CAACpB,IAAI,CAACiG,IAAI;YAAC7C,KAAK,EAAEuD,UAAU,CAACvD,KAAM;YAACC,IAAI,EAAEsD,UAAU,CAACtD,IAAK;YAAAgC,QAAA,eACxDjE,OAAA,CAACI,gBAAgB,CAACsF,cAAc;cAAuBC,UAAU,EAAEJ,UAAW;cAAChC,cAAc,EAAEA;YAAe,GAAxEgC,UAAU,CAACtD,IAAI;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA0D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC;QAEhB;MACF,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EACNpE,IAAI,IAAI,CAAC,iBACRL,OAAA;MAAMgE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC1BjE,OAAA;QAAMgE,SAAS,EAAC,gBAAgB;QAACE,KAAK,EAAE;UAACC,KAAK,EAAC,SAAS;UAACC,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,4KAE3F;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAER,CAAC;AAEV;;AAEA;AAAAnE,EAAA,CA1KwBF,gBAAgB;EAAA,QACvBxB,IAAI,CAACgC,OAAO,EACVxB,WAAW,EACRU,cAAc,EACfZ,SAAS,EAGdG,WAAW;AAAA;AAAAuG,EAAA,GAPHxF,gBAAgB;AA2KxCA,gBAAgB,CAACiF,SAAS,GAAG,SAASQ,CAACA,CAACC,KAAK,EAAE;EAC7C,MAAM;IAACrD,YAAY;IAAC0C;EAAQ,CAAC,GAAGW,KAAK;EACrC,MAAMC,UAAU,GAAG;IACjBC,QAAQ,EAAE,CAAC;IACX/D,IAAI,EAAE,MAAM;IACZgE,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,KAAK;IACrBC,MAAM,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,8BAA8B;IACvEC,YAAY,EAAGC,IAAI,IAAK;MACtB,MAAMC,KAAK,GAAGD,IAAI,CAACE,IAAI,KAAK,WAAW,IAAIF,IAAI,CAACE,IAAI,KAAK,YAAY;MACrE,IAAI,CAACD,KAAK,EAAE;QACV9G,UAAU,CAACgH,KAAK,CAAC,GAAGH,IAAI,CAACvE,IAAI,QAAQ,CAAC;MACxC;MACA,OAAOwE,KAAK,IAAI5H,MAAM,CAAC+H,WAAW;IACpC,CAAC;IACDC,QAAQA,CAACC,IAAI,EAAE;MACb3B,QAAQ,CAAC,CAAC;MACV,MAAM;QAAE4B,MAAM;QAAEC;MAAS,CAAC,GAAGF,IAAI,CAACN,IAAI;MACtC,IAAIO,MAAM,IAAI,WAAW,EAAE;QACzBE,OAAO,CAACC,GAAG,CAACJ,IAAI,CAACN,IAAI,EAAEM,IAAI,CAACK,QAAQ,CAAC;MACvC;MACA,IAAIJ,MAAM,KAAK,MAAM,EAAE;QACrB,IAAGC,QAAQ,CAAC1D,UAAU,IAAI,GAAG,EAAE;UAC7Bb,YAAY,CAACuE,QAAQ,CAACtE,IAAI,CAAC;UAC3B/C,UAAU,CAAC8D,OAAO,CAAC,MAAM,CAAC;QAC5B,CAAC,MAAM;UACL9D,UAAU,CAACgH,KAAK,CAAC,MAAM,CAAC;QAC1B;MACF,CAAC,MAAM,IAAII,MAAM,KAAK,OAAO,EAAE;QAC7BpH,UAAU,CAACgH,KAAK,CAAC,GAAGG,IAAI,CAACN,IAAI,CAACvE,IAAI,sBAAsB,CAAC;MAC3D;IACF;EACF,CAAC;;EAED;EACA,MAAMmF,SAAS,GAAG,MAAOZ,IAAI,IAAK;IAChC,IAAI1B,GAAG,GAAG0B,IAAI,CAAC5C,GAAG;IAClB,IAAI,CAACkB,GAAG,EAAE;MACRA,GAAG,GAAG,MAAM,IAAIuC,OAAO,CAAEC,OAAO,IAAK;QACnC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,aAAa,CAACjB,IAAI,CAACkB,aAAa,CAAC;QACxCH,MAAM,CAACI,MAAM,GAAG,MAAML,OAAO,CAACC,MAAM,CAACK,MAAM,CAAC;MAC9C,CAAC,CAAC;IACJ;IACA,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;IACzBD,KAAK,CAAC/C,GAAG,GAAGA,GAAG;IACf,MAAMiD,SAAS,GAAGlE,MAAM,CAACC,IAAI,CAACgB,GAAG,CAAC;IAClCiD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEC,QAAQ,CAACC,KAAK,CAACJ,KAAK,CAACK,SAAS,CAAC;EAC5C,CAAC;EAED,oBACElI,OAAA,CAAAE,SAAA;IAAA+D,QAAA,eACEjE,OAAA,CAACR,OAAO;MACR2I,cAAc,EAAC,iBAAiB;MAChCC,MAAM;MAACC,UAAU,EAAE,MAAO;MAC1BC,OAAO,EAAC,cAAI;MACZC,WAAW,EAAC,cAAI;MAAAtE,QAAA,eAEdjE,OAAA,CAACG,OAAO;QAAA,GAAK4F,UAAU;QAAEqB,SAAS,EAAEA,SAAU;QAAAnD,QAAA,gBAC5CjE,OAAA;UAAGgE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACjCjE,OAAA,CAACT,mBAAmB;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACJzE,OAAA;UAAGgE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7CzE,OAAA;UAAGkE,KAAK,EAAE;YAACC,KAAK,EAAC,MAAM;YAACqE,SAAS,EAAC;UAAC,CAAE;UAAAvE,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACV,CAAC;AAEP,CAAC;;AAED;AACArE,gBAAgB,CAACsF,cAAc,GAAA+C,GAAA,CAAG,SAAS5C,CAACA,CAAC;EAAEF,UAAU;EAAEpC,cAAc;EAAErB,KAAK,GAAC,EAAE;EAAE2E;AAAS,CAAC,EAAE;EAAA4B,GAAA;EAC/F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpK,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwE,aAAa,EAAE6F,gBAAgB,CAAC,GAAGrK,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAMsK,SAAS,GAAGrK,MAAM,CAAC,CAAC;EAC1B,MAAMsK,aAAa,GAAIC,YAAY,IAAK;IACtClC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG;MACT9D,aAAa;MACb,GAAGb,KAAK;MACR,GAAG6G;IACL,CAAC,CAAC;EACJ,CAAC;EACDzK,SAAS,CAAC,MAAM;IACdsK,gBAAgB,CAACjD,UAAU,CAACzD,KAAK,CAAC;IAClC4G,aAAa,CAAC;MACZ/F,aAAa,EAAE4C,UAAU,CAACzD;IAC5B,CAAC,CAAC;EACJ,CAAC,EAAC,EAAE,CAAC;EACL5D,SAAS,CAAC,MAAM;IACd,IAAIoK,UAAU,IAAI/C,UAAU,CAAC1D,IAAI,IAAI,EAAE,EAAE;MACvC4G,SAAS,CAACG,OAAO,CAACC,KAAK,CAAC;QAAEC,MAAM,EAAE;MAAM,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACR,UAAU,CAAC,CAAC;EAChB,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxBR,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EACD,MAAMS,UAAU,GAAIC,CAAC,IAAK;IACxB,IAAG,CAACA,CAAC,CAACC,MAAM,CAACpH,KAAK,IAAIyD,UAAU,CAAC5D,GAAG,IAAI,OAAO,EAAE;MAC/C8G,SAAS,CAACG,OAAO,CAACC,KAAK,CAAC;QAAEC,MAAM,EAAE;MAAM,CAAC,CAAC;MAC1CvJ,UAAU,CAAC4J,OAAO,CAAC,MAAM,CAAC;MAC1B;IACF;IACA,IAAIC,GAAG,GAAGH,CAAC,CAACC,MAAM,CAACpH,KAAK,CAACuH,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7C,IAAG9D,UAAU,CAAC5D,GAAG,IAAI,UAAU,EAAC;MAC9B,IAAI2H,GAAG,GAAG,kBAAkB;MAC5B,IAAG,CAACA,GAAG,CAACC,IAAI,CAACH,GAAG,CAAC,EAAC;QAChB7J,UAAU,CAAC4J,OAAO,CAAC,YAAY,CAAC;QAChC;MACF;IACF;IACA,IAAG5D,UAAU,CAAC5D,GAAG,IAAI,OAAO,IAAI,CAAC,CAACyH,GAAG,EAAC;MACpC,IAAII,IAAI,GAAG,+CAA+C;MAC1D,IAAG,CAACA,IAAI,CAACD,IAAI,CAACH,GAAG,CAAC,EAAC;QACjB7J,UAAU,CAAC4J,OAAO,CAAC,UAAU,CAAC;QAC9B;MACF;IACF;IACAZ,aAAa,CAAC,KAAK,CAAC;IACpBC,gBAAgB,CAACS,CAAC,CAACC,MAAM,CAACpH,KAAK,CAAC;IAChC4G,aAAa,CAAC;MACZ/F,aAAa,EAAEsG,CAAC,CAACC,MAAM,CAACpH;IAC1B,CAAC,CAAC;IACF,IAAGyD,UAAU,CAACzD,KAAK,KAAKmH,CAAC,CAACC,MAAM,CAACpH,KAAK,EAAE;IACxCqB,cAAc,CAACoC,UAAU,CAAC3D,KAAK,CAAC;EAClC,CAAC;;EAED;EACA,MAAM6H,oBAAoB,GAAGA,CAAA,kBAC3B7J,OAAA,CAACrB,KAAK;IACNqF,SAAS,EAAC,yBAAyB;IACnC8F,YAAY,EAAC,KAAK;IAClB5F,KAAK,EAAE;MAAE6F,KAAK,EAAE;IAAI,CAAE;IACtBC,YAAY,EAAEjH,aAAc;IAC5BkH,GAAG,EAAEpB,SAAU;IACfqB,MAAM,EAAEd,UAAW;IACnBe,YAAY,EAAEf;EAAW;IAAA9E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxB,CACF;EACD;EACA,MAAM2F,oBAAoB,GAAGA,CAAA,KAAMrH,aAAa;EAChD,oBACE/C,OAAA;IAAKgE,SAAS,EAAC,sBAAsB;IAACE,KAAK,EAAE;MAAE6F,KAAK,EAAE;IAAI,CAAE;IAAA9F,QAAA,GACvDyE,UAAU,GACRmB,oBAAoB,CAAClE,UAAU,CAAC,gBAC/B3F,OAAA;MAAAiE,QAAA,EAAOmG,oBAAoB,CAAC;IAAC;MAAA9F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,EACxC,CAACiE,UAAU,iBACV1I,OAAA,CAACtB,MAAM;MACLsF,SAAS,EAAC,+BAA+B;MACzCqG,IAAI,EAAC,OAAO;MACZ3D,IAAI,EAAC,MAAM;MACX3B,IAAI,eAAE/E,OAAA,CAAChB,YAAY;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACvBE,OAAO,EAAEwE;IAAY;MAAA7E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEV,CAAC;AAAA,IAAAmB,EAAA;AAAA0E,YAAA,CAAA1E,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}