{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { globals } from './platform.js';\nconst hasPerformanceNow = globals.performance && typeof globals.performance.now === 'function';\nexport class StopWatch {\n  constructor(highResolution) {\n    this._highResolution = hasPerformanceNow && highResolution;\n    this._startTime = this._now();\n    this._stopTime = -1;\n  }\n  static create() {\n    let highResolution = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    return new StopWatch(highResolution);\n  }\n  stop() {\n    this._stopTime = this._now();\n  }\n  elapsed() {\n    if (this._stopTime !== -1) {\n      return this._stopTime - this._startTime;\n    }\n    return this._now() - this._startTime;\n  }\n  _now() {\n    return this._highResolution ? globals.performance.now() : Date.now();\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}