{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n// Avoid circular dependency on EventEmitter by implementing a subset of the interface.\nexport class ErrorHandler {\n  constructor() {\n    this.listeners = [];\n    this.unexpectedErrorHandler = function (e) {\n      setTimeout(() => {\n        if (e.stack) {\n          if (ErrorNoTelemetry.isErrorNoTelemetry(e)) {\n            throw new ErrorNoTelemetry(e.message + '\\n\\n' + e.stack);\n          }\n          throw new Error(e.message + '\\n\\n' + e.stack);\n        }\n        throw e;\n      }, 0);\n    };\n  }\n  emit(e) {\n    this.listeners.forEach(listener => {\n      listener(e);\n    });\n  }\n  onUnexpectedError(e) {\n    this.unexpectedErrorHandler(e);\n    this.emit(e);\n  }\n  // For external errors, we don't want the listeners to be called\n  onUnexpectedExternalError(e) {\n    this.unexpectedErrorHandler(e);\n  }\n}\nexport const errorHandler = new ErrorHandler();\nexport function onUnexpectedError(e) {\n  // ignore errors from cancelled promises\n  if (!isCancellationError(e)) {\n    errorHandler.onUnexpectedError(e);\n  }\n  return undefined;\n}\nexport function onUnexpectedExternalError(e) {\n  // ignore errors from cancelled promises\n  if (!isCancellationError(e)) {\n    errorHandler.onUnexpectedExternalError(e);\n  }\n  return undefined;\n}\nexport function transformErrorForSerialization(error) {\n  if (error instanceof Error) {\n    const {\n      name,\n      message\n    } = error;\n    const stack = error.stacktrace || error.stack;\n    return {\n      $isError: true,\n      name,\n      message,\n      stack,\n      noTelemetry: ErrorNoTelemetry.isErrorNoTelemetry(error)\n    };\n  }\n  // return as is\n  return error;\n}\nconst canceledName = 'Canceled';\n/**\n * Checks if the given error is a promise in canceled state\n */\nexport function isCancellationError(error) {\n  if (error instanceof CancellationError) {\n    return true;\n  }\n  return error instanceof Error && error.name === canceledName && error.message === canceledName;\n}\n// !!!IMPORTANT!!!\n// Do NOT change this class because it is also used as an API-type.\nexport class CancellationError extends Error {\n  constructor() {\n    super(canceledName);\n    this.name = this.message;\n  }\n}\n/**\n * @deprecated use {@link CancellationError `new CancellationError()`} instead\n */\nexport function canceled() {\n  const error = new Error(canceledName);\n  error.name = error.message;\n  return error;\n}\nexport function illegalArgument(name) {\n  if (name) {\n    return new Error(`Illegal argument: ${name}`);\n  } else {\n    return new Error('Illegal argument');\n  }\n}\nexport function illegalState(name) {\n  if (name) {\n    return new Error(`Illegal state: ${name}`);\n  } else {\n    return new Error('Illegal state');\n  }\n}\nexport class NotSupportedError extends Error {\n  constructor(message) {\n    super('NotSupported');\n    if (message) {\n      this.message = message;\n    }\n  }\n}\n/**\n * Error that when thrown won't be logged in telemetry as an unhandled error.\n */\nexport class ErrorNoTelemetry extends Error {\n  constructor(msg) {\n    super(msg);\n    this.name = 'ErrorNoTelemetry';\n  }\n  static fromError(err) {\n    if (err instanceof ErrorNoTelemetry) {\n      return err;\n    }\n    const result = new ErrorNoTelemetry();\n    result.message = err.message;\n    result.stack = err.stack;\n    return result;\n  }\n  static isErrorNoTelemetry(err) {\n    return err.name === 'ErrorNoTelemetry';\n  }\n}\n/**\n * This error indicates a bug.\n * Do not throw this for invalid user input.\n * Only catch this error to recover gracefully from bugs.\n */\nexport class BugIndicatingError extends Error {\n  constructor(message) {\n    super(message || 'An unexpected bug occurred.');\n    Object.setPrototypeOf(this, BugIndicatingError.prototype);\n    // Because we know for sure only buggy code throws this,\n    // we definitely want to break here and fix the bug.\n    // eslint-disable-next-line no-debugger\n    debugger;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}