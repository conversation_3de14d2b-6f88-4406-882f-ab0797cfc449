{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../base/common/strings.js';\nimport { getMapForWordSeparators } from '../core/wordCharacterClassifier.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { FindMatch, SearchData } from '../model.js';\nconst LIMIT_FIND_COUNT = 999;\nexport class SearchParams {\n  constructor(searchString, isRegex, matchCase, wordSeparators) {\n    this.searchString = searchString;\n    this.isRegex = isRegex;\n    this.matchCase = matchCase;\n    this.wordSeparators = wordSeparators;\n  }\n  parseSearchRequest() {\n    if (this.searchString === '') {\n      return null;\n    }\n    // Try to create a RegExp out of the params\n    let multiline;\n    if (this.isRegex) {\n      multiline = isMultilineRegexSource(this.searchString);\n    } else {\n      multiline = this.searchString.indexOf('\\n') >= 0;\n    }\n    let regex = null;\n    try {\n      regex = strings.createRegExp(this.searchString, this.isRegex, {\n        matchCase: this.matchCase,\n        wholeWord: false,\n        multiline: multiline,\n        global: true,\n        unicode: true\n      });\n    } catch (err) {\n      return null;\n    }\n    if (!regex) {\n      return null;\n    }\n    let canUseSimpleSearch = !this.isRegex && !multiline;\n    if (canUseSimpleSearch && this.searchString.toLowerCase() !== this.searchString.toUpperCase()) {\n      // casing might make a difference\n      canUseSimpleSearch = this.matchCase;\n    }\n    return new SearchData(regex, this.wordSeparators ? getMapForWordSeparators(this.wordSeparators) : null, canUseSimpleSearch ? this.searchString : null);\n  }\n}\nexport function isMultilineRegexSource(searchString) {\n  if (!searchString || searchString.length === 0) {\n    return false;\n  }\n  for (let i = 0, len = searchString.length; i < len; i++) {\n    const chCode = searchString.charCodeAt(i);\n    if (chCode === 10 /* CharCode.LineFeed */) {\n      return true;\n    }\n    if (chCode === 92 /* CharCode.Backslash */) {\n      // move to next char\n      i++;\n      if (i >= len) {\n        // string ends with a \\\n        break;\n      }\n      const nextChCode = searchString.charCodeAt(i);\n      if (nextChCode === 110 /* CharCode.n */ || nextChCode === 114 /* CharCode.r */ || nextChCode === 87 /* CharCode.W */) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\nexport function createFindMatch(range, rawMatches, captureMatches) {\n  if (!captureMatches) {\n    return new FindMatch(range, null);\n  }\n  const matches = [];\n  for (let i = 0, len = rawMatches.length; i < len; i++) {\n    matches[i] = rawMatches[i];\n  }\n  return new FindMatch(range, matches);\n}\nclass LineFeedCounter {\n  constructor(text) {\n    const lineFeedsOffsets = [];\n    let lineFeedsOffsetsLen = 0;\n    for (let i = 0, textLen = text.length; i < textLen; i++) {\n      if (text.charCodeAt(i) === 10 /* CharCode.LineFeed */) {\n        lineFeedsOffsets[lineFeedsOffsetsLen++] = i;\n      }\n    }\n    this._lineFeedsOffsets = lineFeedsOffsets;\n  }\n  findLineFeedCountBeforeOffset(offset) {\n    const lineFeedsOffsets = this._lineFeedsOffsets;\n    let min = 0;\n    let max = lineFeedsOffsets.length - 1;\n    if (max === -1) {\n      // no line feeds\n      return 0;\n    }\n    if (offset <= lineFeedsOffsets[0]) {\n      // before first line feed\n      return 0;\n    }\n    while (min < max) {\n      const mid = min + ((max - min) / 2 >> 0);\n      if (lineFeedsOffsets[mid] >= offset) {\n        max = mid - 1;\n      } else {\n        if (lineFeedsOffsets[mid + 1] >= offset) {\n          // bingo!\n          min = mid;\n          max = mid;\n        } else {\n          min = mid + 1;\n        }\n      }\n    }\n    return min + 1;\n  }\n}\nexport class TextModelSearch {\n  static findMatches(model, searchParams, searchRange, captureMatches, limitResultCount) {\n    const searchData = searchParams.parseSearchRequest();\n    if (!searchData) {\n      return [];\n    }\n    if (searchData.regex.multiline) {\n      return this._doFindMatchesMultiline(model, searchRange, new Searcher(searchData.wordSeparators, searchData.regex), captureMatches, limitResultCount);\n    }\n    return this._doFindMatchesLineByLine(model, searchRange, searchData, captureMatches, limitResultCount);\n  }\n  /**\n   * Multiline search always executes on the lines concatenated with \\n.\n   * We must therefore compensate for the count of \\n in case the model is CRLF\n   */\n  static _getMultilineMatchRange(model, deltaOffset, text, lfCounter, matchIndex, match0) {\n    let startOffset;\n    let lineFeedCountBeforeMatch = 0;\n    if (lfCounter) {\n      lineFeedCountBeforeMatch = lfCounter.findLineFeedCountBeforeOffset(matchIndex);\n      startOffset = deltaOffset + matchIndex + lineFeedCountBeforeMatch /* add as many \\r as there were \\n */;\n    } else {\n      startOffset = deltaOffset + matchIndex;\n    }\n    let endOffset;\n    if (lfCounter) {\n      const lineFeedCountBeforeEndOfMatch = lfCounter.findLineFeedCountBeforeOffset(matchIndex + match0.length);\n      const lineFeedCountInMatch = lineFeedCountBeforeEndOfMatch - lineFeedCountBeforeMatch;\n      endOffset = startOffset + match0.length + lineFeedCountInMatch /* add as many \\r as there were \\n */;\n    } else {\n      endOffset = startOffset + match0.length;\n    }\n    const startPosition = model.getPositionAt(startOffset);\n    const endPosition = model.getPositionAt(endOffset);\n    return new Range(startPosition.lineNumber, startPosition.column, endPosition.lineNumber, endPosition.column);\n  }\n  static _doFindMatchesMultiline(model, searchRange, searcher, captureMatches, limitResultCount) {\n    const deltaOffset = model.getOffsetAt(searchRange.getStartPosition());\n    // We always execute multiline search over the lines joined with \\n\n    // This makes it that \\n will match the EOL for both CRLF and LF models\n    // We compensate for offset errors in `_getMultilineMatchRange`\n    const text = model.getValueInRange(searchRange, 1 /* EndOfLinePreference.LF */);\n    const lfCounter = model.getEOL() === '\\r\\n' ? new LineFeedCounter(text) : null;\n    const result = [];\n    let counter = 0;\n    let m;\n    searcher.reset(0);\n    while (m = searcher.next(text)) {\n      result[counter++] = createFindMatch(this._getMultilineMatchRange(model, deltaOffset, text, lfCounter, m.index, m[0]), m, captureMatches);\n      if (counter >= limitResultCount) {\n        return result;\n      }\n    }\n    return result;\n  }\n  static _doFindMatchesLineByLine(model, searchRange, searchData, captureMatches, limitResultCount) {\n    const result = [];\n    let resultLen = 0;\n    // Early case for a search range that starts & stops on the same line number\n    if (searchRange.startLineNumber === searchRange.endLineNumber) {\n      const text = model.getLineContent(searchRange.startLineNumber).substring(searchRange.startColumn - 1, searchRange.endColumn - 1);\n      resultLen = this._findMatchesInLine(searchData, text, searchRange.startLineNumber, searchRange.startColumn - 1, resultLen, result, captureMatches, limitResultCount);\n      return result;\n    }\n    // Collect results from first line\n    const text = model.getLineContent(searchRange.startLineNumber).substring(searchRange.startColumn - 1);\n    resultLen = this._findMatchesInLine(searchData, text, searchRange.startLineNumber, searchRange.startColumn - 1, resultLen, result, captureMatches, limitResultCount);\n    // Collect results from middle lines\n    for (let lineNumber = searchRange.startLineNumber + 1; lineNumber < searchRange.endLineNumber && resultLen < limitResultCount; lineNumber++) {\n      resultLen = this._findMatchesInLine(searchData, model.getLineContent(lineNumber), lineNumber, 0, resultLen, result, captureMatches, limitResultCount);\n    }\n    // Collect results from last line\n    if (resultLen < limitResultCount) {\n      const text = model.getLineContent(searchRange.endLineNumber).substring(0, searchRange.endColumn - 1);\n      resultLen = this._findMatchesInLine(searchData, text, searchRange.endLineNumber, 0, resultLen, result, captureMatches, limitResultCount);\n    }\n    return result;\n  }\n  static _findMatchesInLine(searchData, text, lineNumber, deltaOffset, resultLen, result, captureMatches, limitResultCount) {\n    const wordSeparators = searchData.wordSeparators;\n    if (!captureMatches && searchData.simpleSearch) {\n      const searchString = searchData.simpleSearch;\n      const searchStringLen = searchString.length;\n      const textLength = text.length;\n      let lastMatchIndex = -searchStringLen;\n      while ((lastMatchIndex = text.indexOf(searchString, lastMatchIndex + searchStringLen)) !== -1) {\n        if (!wordSeparators || isValidMatch(wordSeparators, text, textLength, lastMatchIndex, searchStringLen)) {\n          result[resultLen++] = new FindMatch(new Range(lineNumber, lastMatchIndex + 1 + deltaOffset, lineNumber, lastMatchIndex + 1 + searchStringLen + deltaOffset), null);\n          if (resultLen >= limitResultCount) {\n            return resultLen;\n          }\n        }\n      }\n      return resultLen;\n    }\n    const searcher = new Searcher(searchData.wordSeparators, searchData.regex);\n    let m;\n    // Reset regex to search from the beginning\n    searcher.reset(0);\n    do {\n      m = searcher.next(text);\n      if (m) {\n        result[resultLen++] = createFindMatch(new Range(lineNumber, m.index + 1 + deltaOffset, lineNumber, m.index + 1 + m[0].length + deltaOffset), m, captureMatches);\n        if (resultLen >= limitResultCount) {\n          return resultLen;\n        }\n      }\n    } while (m);\n    return resultLen;\n  }\n  static findNextMatch(model, searchParams, searchStart, captureMatches) {\n    const searchData = searchParams.parseSearchRequest();\n    if (!searchData) {\n      return null;\n    }\n    const searcher = new Searcher(searchData.wordSeparators, searchData.regex);\n    if (searchData.regex.multiline) {\n      return this._doFindNextMatchMultiline(model, searchStart, searcher, captureMatches);\n    }\n    return this._doFindNextMatchLineByLine(model, searchStart, searcher, captureMatches);\n  }\n  static _doFindNextMatchMultiline(model, searchStart, searcher, captureMatches) {\n    const searchTextStart = new Position(searchStart.lineNumber, 1);\n    const deltaOffset = model.getOffsetAt(searchTextStart);\n    const lineCount = model.getLineCount();\n    // We always execute multiline search over the lines joined with \\n\n    // This makes it that \\n will match the EOL for both CRLF and LF models\n    // We compensate for offset errors in `_getMultilineMatchRange`\n    const text = model.getValueInRange(new Range(searchTextStart.lineNumber, searchTextStart.column, lineCount, model.getLineMaxColumn(lineCount)), 1 /* EndOfLinePreference.LF */);\n    const lfCounter = model.getEOL() === '\\r\\n' ? new LineFeedCounter(text) : null;\n    searcher.reset(searchStart.column - 1);\n    const m = searcher.next(text);\n    if (m) {\n      return createFindMatch(this._getMultilineMatchRange(model, deltaOffset, text, lfCounter, m.index, m[0]), m, captureMatches);\n    }\n    if (searchStart.lineNumber !== 1 || searchStart.column !== 1) {\n      // Try again from the top\n      return this._doFindNextMatchMultiline(model, new Position(1, 1), searcher, captureMatches);\n    }\n    return null;\n  }\n  static _doFindNextMatchLineByLine(model, searchStart, searcher, captureMatches) {\n    const lineCount = model.getLineCount();\n    const startLineNumber = searchStart.lineNumber;\n    // Look in first line\n    const text = model.getLineContent(startLineNumber);\n    const r = this._findFirstMatchInLine(searcher, text, startLineNumber, searchStart.column, captureMatches);\n    if (r) {\n      return r;\n    }\n    for (let i = 1; i <= lineCount; i++) {\n      const lineIndex = (startLineNumber + i - 1) % lineCount;\n      const text = model.getLineContent(lineIndex + 1);\n      const r = this._findFirstMatchInLine(searcher, text, lineIndex + 1, 1, captureMatches);\n      if (r) {\n        return r;\n      }\n    }\n    return null;\n  }\n  static _findFirstMatchInLine(searcher, text, lineNumber, fromColumn, captureMatches) {\n    // Set regex to search from column\n    searcher.reset(fromColumn - 1);\n    const m = searcher.next(text);\n    if (m) {\n      return createFindMatch(new Range(lineNumber, m.index + 1, lineNumber, m.index + 1 + m[0].length), m, captureMatches);\n    }\n    return null;\n  }\n  static findPreviousMatch(model, searchParams, searchStart, captureMatches) {\n    const searchData = searchParams.parseSearchRequest();\n    if (!searchData) {\n      return null;\n    }\n    const searcher = new Searcher(searchData.wordSeparators, searchData.regex);\n    if (searchData.regex.multiline) {\n      return this._doFindPreviousMatchMultiline(model, searchStart, searcher, captureMatches);\n    }\n    return this._doFindPreviousMatchLineByLine(model, searchStart, searcher, captureMatches);\n  }\n  static _doFindPreviousMatchMultiline(model, searchStart, searcher, captureMatches) {\n    const matches = this._doFindMatchesMultiline(model, new Range(1, 1, searchStart.lineNumber, searchStart.column), searcher, captureMatches, 10 * LIMIT_FIND_COUNT);\n    if (matches.length > 0) {\n      return matches[matches.length - 1];\n    }\n    const lineCount = model.getLineCount();\n    if (searchStart.lineNumber !== lineCount || searchStart.column !== model.getLineMaxColumn(lineCount)) {\n      // Try again with all content\n      return this._doFindPreviousMatchMultiline(model, new Position(lineCount, model.getLineMaxColumn(lineCount)), searcher, captureMatches);\n    }\n    return null;\n  }\n  static _doFindPreviousMatchLineByLine(model, searchStart, searcher, captureMatches) {\n    const lineCount = model.getLineCount();\n    const startLineNumber = searchStart.lineNumber;\n    // Look in first line\n    const text = model.getLineContent(startLineNumber).substring(0, searchStart.column - 1);\n    const r = this._findLastMatchInLine(searcher, text, startLineNumber, captureMatches);\n    if (r) {\n      return r;\n    }\n    for (let i = 1; i <= lineCount; i++) {\n      const lineIndex = (lineCount + startLineNumber - i - 1) % lineCount;\n      const text = model.getLineContent(lineIndex + 1);\n      const r = this._findLastMatchInLine(searcher, text, lineIndex + 1, captureMatches);\n      if (r) {\n        return r;\n      }\n    }\n    return null;\n  }\n  static _findLastMatchInLine(searcher, text, lineNumber, captureMatches) {\n    let bestResult = null;\n    let m;\n    searcher.reset(0);\n    while (m = searcher.next(text)) {\n      bestResult = createFindMatch(new Range(lineNumber, m.index + 1, lineNumber, m.index + 1 + m[0].length), m, captureMatches);\n    }\n    return bestResult;\n  }\n}\nfunction leftIsWordBounday(wordSeparators, text, textLength, matchStartIndex, matchLength) {\n  if (matchStartIndex === 0) {\n    // Match starts at start of string\n    return true;\n  }\n  const charBefore = text.charCodeAt(matchStartIndex - 1);\n  if (wordSeparators.get(charBefore) !== 0 /* WordCharacterClass.Regular */) {\n    // The character before the match is a word separator\n    return true;\n  }\n  if (charBefore === 13 /* CharCode.CarriageReturn */ || charBefore === 10 /* CharCode.LineFeed */) {\n    // The character before the match is line break or carriage return.\n    return true;\n  }\n  if (matchLength > 0) {\n    const firstCharInMatch = text.charCodeAt(matchStartIndex);\n    if (wordSeparators.get(firstCharInMatch) !== 0 /* WordCharacterClass.Regular */) {\n      // The first character inside the match is a word separator\n      return true;\n    }\n  }\n  return false;\n}\nfunction rightIsWordBounday(wordSeparators, text, textLength, matchStartIndex, matchLength) {\n  if (matchStartIndex + matchLength === textLength) {\n    // Match ends at end of string\n    return true;\n  }\n  const charAfter = text.charCodeAt(matchStartIndex + matchLength);\n  if (wordSeparators.get(charAfter) !== 0 /* WordCharacterClass.Regular */) {\n    // The character after the match is a word separator\n    return true;\n  }\n  if (charAfter === 13 /* CharCode.CarriageReturn */ || charAfter === 10 /* CharCode.LineFeed */) {\n    // The character after the match is line break or carriage return.\n    return true;\n  }\n  if (matchLength > 0) {\n    const lastCharInMatch = text.charCodeAt(matchStartIndex + matchLength - 1);\n    if (wordSeparators.get(lastCharInMatch) !== 0 /* WordCharacterClass.Regular */) {\n      // The last character in the match is a word separator\n      return true;\n    }\n  }\n  return false;\n}\nexport function isValidMatch(wordSeparators, text, textLength, matchStartIndex, matchLength) {\n  return leftIsWordBounday(wordSeparators, text, textLength, matchStartIndex, matchLength) && rightIsWordBounday(wordSeparators, text, textLength, matchStartIndex, matchLength);\n}\nexport class Searcher {\n  constructor(wordSeparators, searchRegex) {\n    this._wordSeparators = wordSeparators;\n    this._searchRegex = searchRegex;\n    this._prevMatchStartIndex = -1;\n    this._prevMatchLength = 0;\n  }\n  reset(lastIndex) {\n    this._searchRegex.lastIndex = lastIndex;\n    this._prevMatchStartIndex = -1;\n    this._prevMatchLength = 0;\n  }\n  next(text) {\n    const textLength = text.length;\n    let m;\n    do {\n      if (this._prevMatchStartIndex + this._prevMatchLength === textLength) {\n        // Reached the end of the line\n        return null;\n      }\n      m = this._searchRegex.exec(text);\n      if (!m) {\n        return null;\n      }\n      const matchStartIndex = m.index;\n      const matchLength = m[0].length;\n      if (matchStartIndex === this._prevMatchStartIndex && matchLength === this._prevMatchLength) {\n        if (matchLength === 0) {\n          // the search result is an empty string and won't advance `regex.lastIndex`, so `regex.exec` will stuck here\n          // we attempt to recover from that by advancing by two if surrogate pair found and by one otherwise\n          if (strings.getNextCodePoint(text, textLength, this._searchRegex.lastIndex) > 0xFFFF) {\n            this._searchRegex.lastIndex += 2;\n          } else {\n            this._searchRegex.lastIndex += 1;\n          }\n          continue;\n        }\n        // Exit early if the regex matches the same range twice\n        return null;\n      }\n      this._prevMatchStartIndex = matchStartIndex;\n      this._prevMatchLength = matchLength;\n      if (!this._wordSeparators || isValidMatch(this._wordSeparators, text, textLength, matchStartIndex, matchLength)) {\n        return m;\n      }\n    } while (m);\n    return null;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}