import React, { useEffect, useState, useRef } from "react";
import { Input, Form, Avatar, Select, Upload } from "antd";
import DraggablePopUp from "@components/DraggablePopUp";
import { CameraOutlined } from "@ant-design/icons";
import { QuestionsUploadIcon } from "@components/IconUtil";
import "./MemberInformation.scss";
import { useParams } from "react-router-dom";
import * as httpSettings from '@common/api/http';
import ImgCrop from "antd-img-crop";
import Draggable from 'react-draggable';
import { globalUtil } from "@common/utils/globalUtil";
import { NoAvatarIcon } from '@components/IconUtil';
import { useQueryClient } from "@tanstack/react-query";
import { get_team_mbr_user_info } from "@/settings/store/actionCreators"
import { useDispatch } from "react-redux";
const { Option } = Select;
const { Dragger } = Upload;

// 可修改项
function MemberFromItem({ value, onChange, memberItem, placeholderValue }) {
  useEffect(() => {
    onChange?.(value)
  },[memberItem])

  const modifyBlur = (e) => {
    onChange?.(e.target.value)
  };
  const sexChange = (e) => {
    onChange?.(e)
  }
  return (
    <div className="MemberFromItem">
      {
        memberItem.name == "avatar" && 
        <MemberLogo value={value} onChange={onChange}/>
      }

      {
        memberItem.name =='sex' &&
        <Select className="MemberFromItem-sex" defaultValue={value} style={{ width: 100 }} placeholder={placeholderValue} onChange={sexChange}>
          <Option value={0}><div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>男</div></Option>
          <Option value={1}><div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>女</div></Option>
        </Select>
      }

      {
        (memberItem.name != "sex" && memberItem.name != "avatar") &&
        <Input
          autoComplete="off"
          onBlur={modifyBlur}
          defaultValue={value}
          placeholder={placeholderValue}
        />
      }
    </div>
  );
}
// memberPopVisible 弹窗状态
// onClose 弹窗关闭事件
// memberItem 成员数据对象(对象类型)
// formItemData 封装好的form表单数据
export default function MemberInformation({memberPopVisible,onClose,memberItem,formItemData,refreshUserList}) { //,allUsers=[],setAllUsers
  const draggleRef = useRef(null);
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [disabled, setDisabled] = useState(false);
  const { teamId } = useParams();
  const [form] = Form.useForm();
  const [formData, setFormData] = useState([])
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  useEffect(() => {
    if(memberPopVisible) {
      let formData_ = {}
      formItemData.forEach((item, index) => {formData_[item.name] = item.value})
      form.setFieldsValue({
        ...formData_
      })
      setFormData([...formItemData])
    }
  }, [formItemData]);
  const onCancel = () => {
    onClose()
    form.resetFields()
    setFormData([])
  }
  const updateUserInfo = () => {
    let formData = form.getFieldsValue(true)
    let params = {
      teamId: teamId,
      userId: memberItem.userId,
      gender: formData.sex,
      ...formData,
    };
    httpSettings.setting_225_update_user_info(params).then((res) => {
      if (res.resultCode === 200) {
        globalUtil.success('修改成功')
        onClose()
        setFormData([])
        refreshUserList();
        queryClient.invalidateQueries(['get_team_allusergrp']);
        dispatch(get_team_mbr_user_info(teamId))
      }else{
        globalUtil.error(res.resultMessage||'保存失败')
      }
    });
  }
  return (
    <div className="MemberInformation">
      <DraggablePopUp
        className="MemberInformation-modal"
        title={
          <div
          className="dragInformTitle"
          onMouseOver={() => {if (disabled) setDisabled(false)}}
          onMouseOut={() => setDisabled(true)}>成员信息</div>
        }
        width={600}
        centered
        destroyOnClose
        maskClosable={false}
        keyboard={true}
        open={memberPopVisible}
        cancelText="取消"
        onCancel={onCancel}
        onOk={updateUserInfo}
        okText="保存"
        modalRender={(modal) => (
          <Draggable
            handle='.dragInformTitle'
            disabled={disabled}
            bounds={bounds}
            onStart={(event, uiData) => onStart(event, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        )}
      >
        <div>
          <Form
          form={form}
            labelAlign="left"
            colon={false}
            labelCol={{ span: 5, offset: 0 }}
          >
            {formData.map((itemx, index) => (
              <Form.Item label={itemx.label} style={{margin:0}}> 
                <div style={{display:'flex',alignItems:'center'}}>
                  <Form.Item name={itemx.name} key={itemx.key}>
                    <MemberFromItem memberItem={itemx} placeholderValue={itemx.placeholder}/>
                  </Form.Item>
                  <span style={{color:'#999',marginLeft:20,marginBottom:5}}>{itemx.desc}</span>
                </div>
              </Form.Item>
            ))}
            <span style={{display:'flex',alignItems:'center',color:'#999'}}>
              <span className="iconfont tishi" style={{color:'#F59A23',marginRight: 4, fontSize: 18}}/>
              成员信息修改后，只会在此团队内显示，团队内成员都可以看见
            </span>
          </Form>
        </div>
      </DraggablePopUp>
    </div>
  );
}

// 上传头像
function MemberLogo({value, onChange}){
  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑状态

  // 上传头像
  const avatarUpload = (link) => {
    onChange?.(link)
  }

  return <React.Fragment>
    <div className="TeamLogo-form-avatar">
      <Avatar
        style={{ width: 40, height: 40 }}
        src={value}
        icon={<NoAvatarIcon/>}/>
      <div className="TeamLogo-form-upload">
        <CameraOutlined
          title="上传logo"
          className="TeamLogo-form-uploadIcon fontsize-14"
          onClick={(event) => setIsModalVisible(true)}/>
      </div>
    </div>
    <DraggablePopUp
      title="上传logo"
      className="TeamLogo-modal"
      open={isModalVisible}
      onCancel={() => setIsModalVisible(false)}
      footer={null}>
      <ImgUpload avatarUpload={avatarUpload} onCancel={() => setIsModalVisible(false)}/>
    </DraggablePopUp>
  </React.Fragment>
}

// 图片上传
function ImgUpload(props) {
  const {avatarUpload,onCancel} = props
  const dataSource = {
    maxCount: 1,
    name: "file",
    multiple: false,
    showUploadList: false,
    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,
    beforeUpload: (file) => {
      const isPNG = file.type === "image/png" || file.type === 'image/jpeg';
      if (!isPNG) {
        globalUtil.error(`${file.name}不是图片格式`);
      }
      return isPNG || Upload.LIST_IGNORE;
    },
    onChange(info) {
      onCancel()
      const { status, response } = info.file;
      if (status == "uploading") {
        console.log(info.file, info.fileList);
      }
      if (status === "done") {
        if(response.resultCode == 200) {
          avatarUpload(response.link)
          globalUtil.success('上传成功');
        } else {
          globalUtil.error("上传失败")
        }
      } else if (status === "error") {
        globalUtil.error(`${info.file.name} file upload failed.`);
      }
    },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };
  
  // 预览/裁剪图片
  const onPreview = async (file) => {
    let src = file.url;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj);
        reader.onload = () => resolve(reader.result);
      });
    }
    const image = new Image();
    image.src = src;
    const imgWindow = window.open(src);
    imgWindow.document.write(image.outerHTML);
  };
  
  return (
    <ImgCrop modalClassName="clippingImgCrop" rotate modalTitle={"编辑图片"} modalOk="确认" modalCancel="取消">
      <Dragger {...dataSource} onPreview={onPreview}>
        <p className="ant-upload-drag-icon">
          <QuestionsUploadIcon />
        </p>
        <p className="ant-upload-text">点击或拖动图片至此处</p>
        <p style={{color:'#999',marginTop:2}}>图片格式：jpg、png</p>
      </Dragger>
    </ImgCrop>
  );
}