{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\issueTrack\\\\views\\\\Issuelist\\\\Issuelist.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Button, Space, Layout, List, Pagination, Popover, Radio, Image, Modal } from \"antd\";\nimport * as httpCommon from \"@common/api/http\";\nimport { eConsoleNodeId, ePagination } from \"@common/utils/enum\";\nimport { useQueryClient } from \"@tanstack/react-query\";\nimport { Outlet, useLocation, useNavigate, useOutletContext, useParams, useSearchParams } from \"react-router-dom\";\nimport { ResizableBox } from \"react-resizable\";\nimport { useQuerySettin228_getTeamUserConfig } from \"src/team/service/objNodeHooks\";\nimport { useIssueSearchParams } from \"src/issueTrack/service/issueSearchHooks\";\nimport \"./Issuelist.scss\";\nimport { track008, track010 } from '@common/utils/ApiPath';\nimport IssueItem from \"./IssueItem\";\nimport { priPermission, getNodeNameByNodeId } from \"@common/utils/logicUtils\";\nimport jiangxu from \"@common/assets/images/jiangxu.png\";\nimport shengxu from \"@common/assets/images/shengxu.png\";\nimport { ExclamationCircleOutlined } from '@ant-design/icons';\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\n\n//Issue短列表，中间第二列短列表\nexport default function Issuelist() {\n  _s();\n  //totalCnt: issue总记录数, pageNo: 当前页码, issueList:当前页的至多30条的issue列表, currentIssueIdx:当前选中的issue在issueList中的序号\n  const {\n    totalCnt,\n    issueList,\n    pageNo,\n    subclassAttrList,\n    userList,\n    spaceUserList,\n    selectionList,\n    objInfo,\n    setCurrentIssueIdx,\n    previousIssueLinkDisabledFlg,\n    nextIssueLinkDisabledFlg,\n    keywords,\n    gotoPreviousIssue,\n    gotoNextIssue,\n    ascendingFlg,\n    setAscendingFlg,\n    orderBy,\n    setOrderBy,\n    searchQuery,\n    projectInfo,\n    setting320Result,\n    selectedKeys,\n    onMoreBtnClick,\n    onShowMoreButtonClick,\n    onPositioningClick\n  } = useOutletContext(); //从IssueHome获取到上下文数据*/\n\n  const queryClient = useQueryClient();\n  const navigate = useNavigate();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const createFlg = searchParams.get(\"createFlg\"); //是否创建\n\n  const {\n    teamId,\n    nodeId: issueListNodeId\n  } = useParams();\n  const {\n    issueNodeId,\n    queryId,\n    setIssueSearchParams\n  } = useIssueSearchParams(); // issue路由配置，页面刷新\n\n  useEffect(() => {\n    if (createFlg) {\n      setIssueSearchParams({\n        queryId: -1\n      });\n      setTimeout(() => {\n        // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑\n        // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；\n        openCreateIssueModal();\n      }, 2000);\n    }\n  }, [createFlg]);\n\n  // 拖拽的句柄： <DraggableCore> not mounted on DragStart!\n  // https://github.com/react-grid-layout/react-resizable/issues/200\n  const handle = (resizeHandle, ref) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ia-splitter-handle\",\n      ref: ref,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ia-splitter-handle-highlight\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 12\n    }, this);\n  };\n\n  // null值不为空，无法设置默认值260\n  const {\n    data: {\n      treeWidth: width /*, expandNodeIds*/\n    } = {\n      width: 260,\n      expandNodeIds: []\n    }\n    /*isLoading: isLoadingTeamConfig,\r\n    refetch: refetchTeamConfig */\n  } = useQuerySettin228_getTeamUserConfig(teamId, issueListNodeId);\n\n  /*  //刷新issue列表\r\n    function refreshIssueList() {\r\n      queryClient.invalidateQueries([track008])\r\n      setTimeout(() => {\r\n        setRefreshingFlg(false);\r\n      }, 500);\r\n    }*/\n\n  /*  // 宽度拖动中...\r\n    const onResize = (event, { element, size, handle }) => {\r\n  \r\n    }*/\n\n  function openCreateIssueModal() {\n    globalEventBus.emit(\"openCreateIssueModalEvent\", \"\", {\n      nodeId: issueListNodeId,\n      projectInfo,\n      callback: onPostIssueCreated\n    });\n  }\n\n  // 调整宽度\n  const onResizeStop = (event, {\n    element,\n    size,\n    handle\n  }) => {\n    const request = {\n      teamId,\n      nodeId: issueListNodeId,\n      //中间数展开节点\n      treeWidth: size.width\n      // expandNodeIds:[],\n    };\n    httpCommon.setting_227_save_team_user_tree_width(request).then(res => {\n      if (res.resultCode === 200) {}\n    });\n  };\n\n  // 加载更多 - 长短列表\n  function gotoPageNo(page /*, pageSize*/) {\n    // setPageNo(page); //触发 useQueryIssue017_getIssueList 再次调用\n    queryClient.setQueryData([track010, teamId, issueListNodeId], pageNo => page); //触发 useQueryIssue017_getIssueList 再次调用\n    setCurrentIssueIdx(0);\n  }\n\n  // issue创建完成后，页面刷新\n  function onPostIssueCreated(createIssueNodeId) {\n    // tmsbug-10387：issue列表(以及中树其它情形)， 在默认的第一个节点上，创建新节点，期望新建后的节点处于高亮状态\n    let idx = issueList.findIndex(_issue => _issue.nodeId == issueNodeId);\n    if (pageNo == 1 && idx == 0) setIssueSearchParams({\n      issueNodeId: createIssueNodeId,\n      queryId,\n      needAdd: false\n    });\n  }\n\n  // 点击事件\n  const handleOnClick = _issue => {\n    setIssueSearchParams({\n      issueNodeId: _issue.nodeId,\n      queryId\n    });\n  };\n  async function addClick() {\n    var _await$getNodeNameByN;\n    return Modal.confirm({\n      title: \"提示\",\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 15\n      }, this),\n      centered: true,\n      content: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: `当前列表页为\"查询\"页，将转至“${(_await$getNodeNameByN = await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)) !== null && _await$getNodeNameByN !== void 0 ? _await$getNodeNameByN : \"全部\"}”页创建问题。`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 18\n      }, this),\n      okText: \" 好的，继续新建\",\n      cancelText: \"取消\",\n      onOk: () => {\n        if (!!(projectInfo !== null && projectInfo !== void 0 && projectInfo.allIssueNodeId)) {\n          navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}?createFlg=true`);\n        }\n      },\n      onCancel: () => {\n        console.log(\"Cancel\");\n      }\n    });\n  }\n\n  // !!objInfo?.objId: 搜索条件\n  // 是否有编辑权限 当权限privWrite大于0则可以编辑\n  const editBtn = priPermission(setting320Result.privWrite) ? /*#__PURE__*/_jsxDEV(Button, {\n    className: \"defaultBtn\",\n    type: \"primary\",\n    onClick: () => !!(objInfo !== null && objInfo !== void 0 && objInfo.objId) ? addClick() : openCreateIssueModal(),\n    children: [\"+ \", projectInfo.issueAlias]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: [/*#__PURE__*/_jsxDEV(ResizableBox, {\n      width: width || 300 // 接口返回的width为null\n      ,\n      className: \"custom-box-right\",\n      height: Infinity,\n      handle: handle,\n      handleSize: [8, 8],\n      resizeHandles: ['e'],\n      axis: \"x\",\n      minConstraints: [300, Infinity] //最小宽度\n      ,\n      maxConstraints: [650, Infinity]\n      //onResize={onResize}\n      ,\n      onResizeStop: onResizeStop,\n      style: {\n        height: \"100%\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Sider, {\n        className: \"issue-list-sider\",\n        width: \"100%\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"issue-list-sider-search\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: (searchQuery === null || searchQuery === void 0 ? void 0 : searchQuery.length) > 0 || keywords ? /*#__PURE__*/_jsxDEV(\"a\", {\n              style: {\n                paddingLeft: 15,\n                fontSize: 12,\n                color: \"#666\"\n              },\n              title: \"\\u5B9A\\u4F4D\\u81F3#1\\u9875\",\n              onClick: () => pageNo > 1 ? gotoPageNo(1) : null,\n              children: [\"\\u7ED3\\u679C(\", totalCnt, \"\\u6761)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n              style: {\n                paddingLeft: 15,\n                fontSize: 12,\n                color: \"#666\"\n              },\n              title: \"\\u5B9A\\u4F4D\\u81F3#1\\u9875\",\n              onClick: () => pageNo > 1 ? gotoPageNo(1) : null,\n              children: [\"\\u5168\\u90E8(\", totalCnt, \"\\u6761)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Popover, {\n              content: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n                  onChange: e => setOrderBy(e.target.value),\n                  value: orderBy,\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    direction: \"vertical\",\n                    children: [/*#__PURE__*/_jsxDEV(Radio, {\n                      value: eConsoleNodeId.Nid_11118_Issue_CreateDate,\n                      children: \"\\u521B\\u5EFA\\u65F6\\u95F4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Radio, {\n                      value: eConsoleNodeId.Nid_11119_Issue_UpdateDate,\n                      children: \"\\u4FEE\\u6539\\u65F6\\u95F4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 24\n              }, this),\n              title: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  justifyContent: \"space-between\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u6392\\u5E8F\\u65B9\\u5F0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  title: ascendingFlg ? \"点击降序\" : \"点击升序\",\n                  onClick: () => setAscendingFlg(!ascendingFlg),\n                  children: /*#__PURE__*/_jsxDEV(Image, {\n                    src: ascendingFlg ? shengxu : jiangxu,\n                    preview: false,\n                    width: 10,\n                    height: 10,\n                    style: {\n                      marginBottom: 3\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 30\n              }, this),\n              trigger: \"click\",\n              placement: \"bottom\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                title: \"\\u6392\\u5E8F\",\n                style: {\n                  color: 'inherit'\n                },\n                children: /*#__PURE__*/_jsxDEV(Image, {\n                  src: ascendingFlg ? shengxu : jiangxu,\n                  preview: false,\n                  width: 10,\n                  height: 10,\n                  style: {\n                    marginBottom: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [totalCnt ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fontsize-12\",\n                style: {\n                  color: \"#666\"\n                },\n                children: [\"#\", pageNo, \"\\u9875\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 29\n              }, this) : \"\", editBtn]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 9\n        }, this), issueList && (issueList === null || issueList === void 0 ? void 0 : issueList.length) > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(List, {\n            dataSource: issueList,\n            className: \"issue-list\",\n            renderItem: (_issue, index) => /*#__PURE__*/_jsxDEV(IssueItem, {\n              onClick: handleOnClick,\n              _issue: _issue,\n              onShowMoreButtonClick: onShowMoreButtonClick,\n              onMoreBtnClick: onMoreBtnClick,\n              selectedKeys: selectedKeys\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n            className: \"issue-list-sider-pagination\",\n            pageSize: ePagination.PageSize_30,\n            current: pageNo,\n            size: \"small\",\n            showSizeChanger: false,\n            total: totalCnt,\n            onChange: gotoPageNo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true) :\n        /*#__PURE__*/\n        // 空白提示栏\n        _jsxDEV(\"div\", {\n          className: \"issue-list blank-page\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"blank-page-title\",\n            children: \"\\u8FD9\\u91CC\\u662F\\u7A7A\\u7684\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), priPermission(setting320Result.privWrite) ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"blank-page-des fontsize-12 flexCenter\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                paddingRight: 5\n              },\n              children: \"\\u4F60\\u53EF\\u4EE5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              onClick: () => openCreateIssueModal(),\n              children: [\"\\u65B0\\u5EFA\", projectInfo.issueAlias]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 59\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      className: \"issue-detail\",\n      children: /*#__PURE__*/_jsxDEV(Outlet, {\n        context: {\n          teamId,\n          issueNodeId,\n          selectionList,\n          spaceUserList,\n          userList,\n          gotoPreviousIssue,\n          gotoNextIssue,\n          previousIssueLinkDisabledFlg,\n          nextIssueLinkDisabledFlg,\n          projectInfo,\n          onMoreBtnClick,\n          setCurrentIssueIdx,\n          issueList,\n          onPositioningClick\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 10\n  }, this);\n}\n_s(Issuelist, \"v8Qy2iwF6obMiy1nNf+hT9elRKY=\", false, function () {\n  return [useOutletContext, useQueryClient, useNavigate, useSearchParams, useParams, useIssueSearchParams, useQuerySettin228_getTeamUserConfig];\n});\n_c = Issuelist;\nvar _c;\n$RefreshReg$(_c, \"Issuelist\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Space", "Layout", "List", "Pagination", "Popover", "Radio", "Image", "Modal", "httpCommon", "eConsoleNodeId", "ePagination", "useQueryClient", "Outlet", "useLocation", "useNavigate", "useOutletContext", "useParams", "useSearchParams", "ResizableBox", "useQuerySettin228_getTeamUserConfig", "useIssueSearchParams", "track008", "track010", "IssueItem", "priPermission", "getNodeNameByNodeId", "jiangxu", "<PERSON><PERSON><PERSON>u", "ExclamationCircleOutlined", "globalEventBus", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Issuelist", "_s", "totalCnt", "issueList", "pageNo", "subclassAttrList", "userList", "spaceUserList", "selectionList", "objInfo", "setCurrentIssueIdx", "previousIssueLinkDisabledFlg", "nextIssueLinkDisabledFlg", "keywords", "gotoPreviousIssue", "gotoNextIssue", "ascendingFlg", "setAscendingFlg", "orderBy", "setOrderBy", "searchQuery", "projectInfo", "setting320Result", "<PERSON><PERSON><PERSON><PERSON>", "onMoreBtnClick", "onShowMoreButtonClick", "onPositioningClick", "queryClient", "navigate", "searchParams", "setSearchParams", "createFlg", "get", "teamId", "nodeId", "issueListNodeId", "issueNodeId", "queryId", "setIssueSearchParams", "setTimeout", "openCreateIssueModal", "handle", "resizeHandle", "ref", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "data", "treeWidth", "width", "expandNodeIds", "emit", "callback", "onPostIssueCreated", "onResizeStop", "event", "element", "size", "request", "setting_227_save_team_user_tree_width", "then", "res", "resultCode", "gotoPageNo", "page", "setQueryData", "createIssueNodeId", "idx", "findIndex", "_issue", "needAdd", "handleOnClick", "addClick", "_await$getNodeNameByN", "confirm", "title", "icon", "centered", "content", "allIssueNodeId", "okText", "cancelText", "onOk", "onCancel", "console", "log", "editBtn", "privWrite", "type", "onClick", "objId", "issueAlias", "height", "Infinity", "handleSize", "resize<PERSON><PERSON>les", "axis", "minConstraints", "maxConstraints", "style", "display", "alignItems", "justifyContent", "length", "paddingLeft", "fontSize", "color", "Group", "onChange", "e", "target", "value", "direction", "Nid_11118_Issue_CreateDate", "Nid_11119_Issue_UpdateDate", "src", "preview", "marginBottom", "trigger", "placement", "dataSource", "renderItem", "index", "pageSize", "PageSize_30", "current", "showSizeChanger", "total", "paddingRight", "context", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/issueTrack/views/Issuelist/Issuelist.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Button, Space, Layout, List, Pagination, Popover, Radio, Image, Modal } from \"antd\";\r\nimport * as httpCommon from \"@common/api/http\";\r\nimport { eConsoleNodeId, ePagination } from \"@common/utils/enum\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { Outlet, useLocation, useNavigate, useOutletContext, useParams, useSearchParams } from \"react-router-dom\";\r\nimport { ResizableBox } from \"react-resizable\";\r\nimport { useQuerySettin228_getTeamUserConfig } from \"src/team/service/objNodeHooks\";\r\nimport { useIssueSearchParams } from \"src/issueTrack/service/issueSearchHooks\";\r\nimport \"./Issuelist.scss\";\r\nimport { track008, track010 } from '@common/utils/ApiPath';\r\nimport IssueItem from \"./IssueItem\"\r\nimport { priPermission, getNodeNameByNodeId } from \"@common/utils/logicUtils\";\r\nimport jiangxu from \"@common/assets/images/jiangxu.png\";\r\nimport shengxu from \"@common/assets/images/shengxu.png\";\r\nimport { ExclamationCircleOutlined } from '@ant-design/icons';\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\n\r\nconst { Sider } = Layout;\r\n\r\n//Issue短列表，中间第二列短列表\r\nexport default function Issuelist() {\r\n  //totalCnt: issue总记录数, pageNo: 当前页码, issueList:当前页的至多30条的issue列表, currentIssueIdx:当前选中的issue在issueList中的序号\r\n  const { totalCnt, issueList, pageNo, subclassAttrList, userList, spaceUserList, selectionList, objInfo,\r\n    setCurrentIssueIdx, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, keywords, gotoPreviousIssue, gotoNextIssue,\r\n    ascendingFlg, setAscendingFlg, orderBy, setOrderBy, searchQuery, projectInfo, setting320Result, selectedKeys, onMoreBtnClick, onShowMoreButtonClick,\r\n    onPositioningClick\r\n  } = useOutletContext(); //从IssueHome获取到上下文数据*/\r\n\r\n  const queryClient = useQueryClient();\r\n  const navigate = useNavigate();\r\n  const [searchParams, setSearchParams] = useSearchParams();\r\n  const createFlg = searchParams.get(\"createFlg\"); //是否创建\r\n\r\n  const { teamId, nodeId: issueListNodeId, } = useParams();\r\n  const { issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams();// issue路由配置，页面刷新\r\n\r\n  useEffect(()=>{\r\n    if(createFlg){\r\n      setIssueSearchParams({queryId: -1});\r\n      setTimeout(()=>{\r\n        // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑\r\n        // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；\r\n       openCreateIssueModal()\r\n      }, 2000)\r\n    } \r\n  },[createFlg])\r\n\r\n  // 拖拽的句柄： <DraggableCore> not mounted on DragStart!\r\n  // https://github.com/react-grid-layout/react-resizable/issues/200\r\n  const handle = (resizeHandle, ref) => {\r\n    return <div className=\"ia-splitter-handle\" ref={ref}>\r\n      <div className=\"ia-splitter-handle-highlight\"></div>\r\n    </div>\r\n  };\r\n\r\n  // null值不为空，无法设置默认值260\r\n  const { data: { treeWidth: width /*, expandNodeIds*/ } = { width: 260, expandNodeIds: [] },\r\n    /*isLoading: isLoadingTeamConfig,\r\n    refetch: refetchTeamConfig */\r\n  } = useQuerySettin228_getTeamUserConfig(teamId, issueListNodeId);\r\n\r\n/*  //刷新issue列表\r\n  function refreshIssueList() {\r\n    queryClient.invalidateQueries([track008])\r\n    setTimeout(() => {\r\n      setRefreshingFlg(false);\r\n    }, 500);\r\n  }*/\r\n\r\n/*  // 宽度拖动中...\r\n  const onResize = (event, { element, size, handle }) => {\r\n\r\n  }*/\r\n\r\n  function openCreateIssueModal () {\r\n    globalEventBus.emit(\"openCreateIssueModalEvent\", \"\", {\r\n      nodeId: issueListNodeId, projectInfo, callback: onPostIssueCreated\r\n    })\r\n  }\r\n\r\n  // 调整宽度\r\n  const onResizeStop = (event, { element, size, handle }) => {\r\n    const request = {\r\n      teamId,\r\n      nodeId: issueListNodeId, //中间数展开节点\r\n      treeWidth: size.width,\r\n      // expandNodeIds:[],\r\n    }\r\n    httpCommon.setting_227_save_team_user_tree_width(request).then((res) => {\r\n      if (res.resultCode === 200) {\r\n\r\n      }\r\n    })\r\n  }\r\n\r\n  // 加载更多 - 长短列表\r\n  function gotoPageNo(page/*, pageSize*/) {\r\n    // setPageNo(page); //触发 useQueryIssue017_getIssueList 再次调用\r\n    queryClient.setQueryData([track010, teamId, issueListNodeId], (pageNo)=>(page));//触发 useQueryIssue017_getIssueList 再次调用\r\n    setCurrentIssueIdx(0);\r\n  }\r\n\r\n  // issue创建完成后，页面刷新\r\n  function onPostIssueCreated(createIssueNodeId) {\r\n    // tmsbug-10387：issue列表(以及中树其它情形)， 在默认的第一个节点上，创建新节点，期望新建后的节点处于高亮状态\r\n    let idx = issueList.findIndex(_issue => _issue.nodeId == issueNodeId);\r\n    if(pageNo == 1 && idx == 0) setIssueSearchParams({issueNodeId: createIssueNodeId, queryId, needAdd: false});\r\n  }\r\n\r\n  // 点击事件\r\n  const handleOnClick = (_issue) => {\r\n    setIssueSearchParams({issueNodeId: _issue.nodeId, queryId});\r\n  }\r\n\r\n async function addClick(){\r\n    return Modal.confirm({\r\n        title: \"提示\",\r\n        icon: <ExclamationCircleOutlined />,\r\n        centered: true,\r\n        content: <p>{`当前列表页为\"查询\"页，将转至“${await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)??\"全部\"}”页创建问题。`}</p>,\r\n        okText: \" 好的，继续新建\",\r\n        cancelText: \"取消\",\r\n        onOk: () => {\r\n          if(!!projectInfo?.allIssueNodeId){\r\n            navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}?createFlg=true`);\r\n          }\r\n        },\r\n        onCancel: () => {\r\n          console.log(\"Cancel\");\r\n        },\r\n      });\r\n  }\r\n\r\n  // !!objInfo?.objId: 搜索条件\r\n  // 是否有编辑权限 当权限privWrite大于0则可以编辑\r\n  const editBtn = priPermission(setting320Result.privWrite) ?\r\n    <Button className=\"defaultBtn\"\r\n            type=\"primary\"\r\n            onClick={() => !!objInfo?.objId ? addClick(): openCreateIssueModal()}>\r\n      + {projectInfo.issueAlias}\r\n    </Button>\r\n    : <></>\r\n\r\n  return <Layout>\r\n    {/* 可拖拽 */}\r\n    <ResizableBox\r\n      width={width || 300} // 接口返回的width为null\r\n      className=\"custom-box-right\"\r\n      height={Infinity}\r\n      handle={handle}\r\n      handleSize={[8, 8]}\r\n      resizeHandles={['e']}\r\n      axis=\"x\"\r\n      minConstraints={[300, Infinity]} //最小宽度\r\n      maxConstraints={[650, Infinity]}\r\n      //onResize={onResize}\r\n      onResizeStop={onResizeStop}\r\n      style={{ height: \"100%\" }}\r\n    >\r\n      <Sider className=\"issue-list-sider\" width={\"100%\"}>\r\n        <div className=\"issue-list-sider-search\">\r\n          <div style={{ display: \"flex\", alignItems: \"center\", justifyContent: \"center\" }}>\r\n            {(searchQuery?.length > 0 || keywords) ?\r\n              <a style={{  paddingLeft: 15, fontSize: 12, color: \"#666\" }} title='定位至#1页'\r\n                 onClick={()=>pageNo > 1 ? gotoPageNo(1) : null}>结果({totalCnt}条)</a>\r\n              :\r\n              <a style={{   paddingLeft: 15,fontSize: 12, color: \"#666\" }} title='定位至#1页'\r\n                   onClick={()=>pageNo > 1 ? gotoPageNo(1) : null}>全部({totalCnt}条)</a>\r\n            }\r\n            {/* <Button\r\n              className={refreshingFlg && 'refresh-icon'}\r\n              style={{ position: 'relative', color: '#999' }}\r\n              type=\"link\"\r\n              icon={<span className=\"refresh-position fontsize-14 iconfont shuaxin1\" />}\r\n              onClick={() => {\r\n                setRefreshingFlg(true);\r\n                refreshIssueList()\r\n              }}\r\n            /> */}\r\n          </div>\r\n          <Space>\r\n            {/* 排序功能 */}\r\n            <Popover\r\n              content={<div >\r\n                <Radio.Group onChange={(e) => setOrderBy(e.target.value)} value={orderBy}>\r\n                  <Space direction=\"vertical\">\r\n                    <Radio value={eConsoleNodeId.Nid_11118_Issue_CreateDate}>创建时间</Radio>\r\n                    <Radio value={eConsoleNodeId.Nid_11119_Issue_UpdateDate}>修改时间</Radio>\r\n                  </Space>\r\n                </Radio.Group>\r\n              </div>} title={<div style={{ display: \"flex\", alignItems: \"center\", justifyContent: \"space-between\" }}>\r\n                <div>排序方式</div>\r\n                <a title={ascendingFlg ? \"点击降序\" : \"点击升序\"} onClick={() => setAscendingFlg(!ascendingFlg)}>\r\n                  <Image src={ascendingFlg ? shengxu : jiangxu} preview={false} width={10} height={10} style={{marginBottom:3}}/>\r\n                </a>\r\n              </div>} trigger=\"click\" placement=\"bottom\">\r\n              <a title=\"排序\" style={{color:'inherit'}}>\r\n                <Image src={ascendingFlg ? shengxu : jiangxu} preview={false} width={10} height={10} style={{marginBottom:3}}/> \r\n              </a>\r\n            </Popover>\r\n            { \r\n              <Space>\r\n                {totalCnt ? <div className=\"fontsize-12\" style={{ color: \"#666\" }}>#{pageNo}页</div> : \"\"}\r\n                {editBtn}\r\n              </Space>\r\n            }\r\n          </Space>\r\n        </div>\r\n        {issueList && issueList?.length > 0 ?\r\n          <>\r\n            {/*中间列，issue短列表*/}\r\n            <List dataSource={issueList}\r\n              className=\"issue-list\"\r\n              renderItem={(_issue, index) =>(\r\n                <IssueItem \r\n                  onClick={handleOnClick}\r\n                  _issue={_issue}\r\n                  onShowMoreButtonClick={onShowMoreButtonClick}\r\n                  onMoreBtnClick={onMoreBtnClick}\r\n                  selectedKeys={selectedKeys}\r\n                />\r\n              )} />\r\n            {/* 短列表 页码配置*/}\r\n            <Pagination\r\n              className={\"issue-list-sider-pagination\"}\r\n              pageSize={ePagination.PageSize_30}\r\n              current={pageNo}\r\n              size=\"small\"\r\n              showSizeChanger={false}\r\n              total={totalCnt}\r\n              onChange={gotoPageNo}\r\n            />\r\n          </>\r\n          :\r\n          // 空白提示栏\r\n          <div className=\"issue-list blank-page\">\r\n            <div className=\"blank-page-title\">这里是空的</div>\r\n            {\r\n              priPermission(setting320Result.privWrite) ? <div className=\"blank-page-des fontsize-12 flexCenter\">\r\n              <span style={{ paddingRight: 5 }}>你可以</span>\r\n              <a onClick={() =>openCreateIssueModal()}>新建{projectInfo.issueAlias}</a>\r\n            </div> : <></>\r\n            }\r\n          </div>\r\n        }\r\n      </Sider>\r\n    </ResizableBox>\r\n    <Layout className=\"issue-detail\">\r\n      {/* 子路由，用于渲染 问题详情页 (IssueDetail.jsx) ，通过context传递数据 */}\r\n      <Outlet context={{ teamId, issueNodeId, selectionList, spaceUserList, userList,\r\n                        gotoPreviousIssue, gotoNextIssue, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg,\r\n                        projectInfo, onMoreBtnClick, setCurrentIssueIdx, issueList,onPositioningClick }} />\r\n    </Layout>\r\n  </Layout >\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5F,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,SAASC,cAAc,EAAEC,WAAW,QAAQ,oBAAoB;AAChE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,QAAQ,kBAAkB;AACjH,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mCAAmC,QAAQ,+BAA+B;AACnF,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,OAAO,kBAAkB;AACzB,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,uBAAuB;AAC1D,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,0BAA0B;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,SAASC,yBAAyB,QAAQ,mBAAmB;AAC7D,SAASC,cAAc,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAM;EAAEC;AAAM,CAAC,GAAGjC,MAAM;;AAExB;AACA,eAAe,SAASkC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAClC;EACA,MAAM;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,MAAM;IAAEC,gBAAgB;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,aAAa;IAAEC,OAAO;IACpGC,kBAAkB;IAAEC,4BAA4B;IAAEC,wBAAwB;IAAEC,QAAQ;IAAEC,iBAAiB;IAAEC,aAAa;IACtHC,YAAY;IAAEC,eAAe;IAAEC,OAAO;IAAEC,UAAU;IAAEC,WAAW;IAAEC,WAAW;IAAEC,gBAAgB;IAAEC,YAAY;IAAEC,cAAc;IAAEC,qBAAqB;IACnJC;EACF,CAAC,GAAG9C,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAExB,MAAM+C,WAAW,GAAGnD,cAAc,CAAC,CAAC;EACpC,MAAMoD,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGhD,eAAe,CAAC,CAAC;EACzD,MAAMiD,SAAS,GAAGF,YAAY,CAACG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;;EAEjD,MAAM;IAAEC,MAAM;IAAEC,MAAM,EAAEC;EAAiB,CAAC,GAAGtD,SAAS,CAAC,CAAC;EACxD,MAAM;IAAEuD,WAAW;IAAEC,OAAO;IAAEC;EAAqB,CAAC,GAAGrD,oBAAoB,CAAC,CAAC,CAAC;;EAE9EvB,SAAS,CAAC,MAAI;IACZ,IAAGqE,SAAS,EAAC;MACXO,oBAAoB,CAAC;QAACD,OAAO,EAAE,CAAC;MAAC,CAAC,CAAC;MACnCE,UAAU,CAAC,MAAI;QACb;QACA;QACDC,oBAAoB,CAAC,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAC,CAACT,SAAS,CAAC,CAAC;;EAEd;EACA;EACA,MAAMU,MAAM,GAAGA,CAACC,YAAY,EAAEC,GAAG,KAAK;IACpC,oBAAO/C,OAAA;MAAKgD,SAAS,EAAC,oBAAoB;MAACD,GAAG,EAAEA,GAAI;MAAAE,QAAA,eAClDjD,OAAA;QAAKgD,SAAS,EAAC;MAA8B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EACR,CAAC;;EAED;EACA,MAAM;IAAEC,IAAI,EAAE;MAAEC,SAAS,EAAEC,KAAK,CAAC;IAAoB,CAAC,GAAG;MAAEA,KAAK,EAAE,GAAG;MAAEC,aAAa,EAAE;IAAG;IACvF;AACJ;EACE,CAAC,GAAGrE,mCAAmC,CAACiD,MAAM,EAAEE,eAAe,CAAC;;EAElE;AACA;AACA;AACA;AACA;AACA;AACA;;EAEA;AACA;AACA;AACA;;EAEE,SAASK,oBAAoBA,CAAA,EAAI;IAC/B9C,cAAc,CAAC4D,IAAI,CAAC,2BAA2B,EAAE,EAAE,EAAE;MACnDpB,MAAM,EAAEC,eAAe;MAAEd,WAAW;MAAEkC,QAAQ,EAAEC;IAClD,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,YAAY,GAAGA,CAACC,KAAK,EAAE;IAAEC,OAAO;IAAEC,IAAI;IAAEnB;EAAO,CAAC,KAAK;IACzD,MAAMoB,OAAO,GAAG;MACd5B,MAAM;MACNC,MAAM,EAAEC,eAAe;MAAE;MACzBgB,SAAS,EAAES,IAAI,CAACR;MAChB;IACF,CAAC;IACD/E,UAAU,CAACyF,qCAAqC,CAACD,OAAO,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;MACtE,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE,CAE5B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,SAASC,UAAUA,CAACC,IAAI,iBAAgB;IACtC;IACAxC,WAAW,CAACyC,YAAY,CAAC,CAACjF,QAAQ,EAAE8C,MAAM,EAAEE,eAAe,CAAC,EAAG/B,MAAM,IAAI+D,IAAK,CAAC,CAAC;IAChFzD,kBAAkB,CAAC,CAAC,CAAC;EACvB;;EAEA;EACA,SAAS8C,kBAAkBA,CAACa,iBAAiB,EAAE;IAC7C;IACA,IAAIC,GAAG,GAAGnE,SAAS,CAACoE,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACtC,MAAM,IAAIE,WAAW,CAAC;IACrE,IAAGhC,MAAM,IAAI,CAAC,IAAIkE,GAAG,IAAI,CAAC,EAAEhC,oBAAoB,CAAC;MAACF,WAAW,EAAEiC,iBAAiB;MAAEhC,OAAO;MAAEoC,OAAO,EAAE;IAAK,CAAC,CAAC;EAC7G;;EAEA;EACA,MAAMC,aAAa,GAAIF,MAAM,IAAK;IAChClC,oBAAoB,CAAC;MAACF,WAAW,EAAEoC,MAAM,CAACtC,MAAM;MAAEG;IAAO,CAAC,CAAC;EAC7D,CAAC;EAEF,eAAesC,QAAQA,CAAA,EAAE;IAAA,IAAAC,qBAAA;IACtB,OAAOxG,KAAK,CAACyG,OAAO,CAAC;MACjBC,KAAK,EAAE,IAAI;MACXC,IAAI,eAAEnF,OAAA,CAACH,yBAAyB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnC+B,QAAQ,EAAE,IAAI;MACdC,OAAO,eAAErF,OAAA;QAAAiD,QAAA,EAAI,oBAAA+B,qBAAA,GAAmB,MAAMtF,mBAAmB,CAAC2C,MAAM,EAAEZ,WAAW,CAAC6D,cAAc,CAAC,cAAAN,qBAAA,cAAAA,qBAAA,GAAE,IAAI;MAAS;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;MACjHkC,MAAM,EAAE,UAAU;MAClBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEA,CAAA,KAAM;QACV,IAAG,CAAC,EAAChE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE6D,cAAc,GAAC;UAC/BtD,QAAQ,CAAC,SAASK,MAAM,WAAWZ,WAAW,CAAC6D,cAAc,iBAAiB,CAAC;QACjF;MACF,CAAC;MACDI,QAAQ,EAAEA,CAAA,KAAM;QACdC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC,CAAC;EACN;;EAEA;EACA;EACA,MAAMC,OAAO,GAAGpG,aAAa,CAACiC,gBAAgB,CAACoE,SAAS,CAAC,gBACvD9F,OAAA,CAAChC,MAAM;IAACgF,SAAS,EAAC,YAAY;IACtB+C,IAAI,EAAC,SAAS;IACdC,OAAO,EAAEA,CAAA,KAAM,CAAC,EAACnF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEoF,KAAK,IAAGlB,QAAQ,CAAC,CAAC,GAAEnC,oBAAoB,CAAC,CAAE;IAAAK,QAAA,GAAC,IAC1E,EAACxB,WAAW,CAACyE,UAAU;EAAA;IAAAhD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC,gBACPrD,OAAA,CAAAE,SAAA,mBAAI,CAAC;EAET,oBAAOF,OAAA,CAAC9B,MAAM;IAAA+E,QAAA,gBAEZjD,OAAA,CAACb,YAAY;MACXqE,KAAK,EAAEA,KAAK,IAAI,GAAI,CAAC;MAAA;MACrBR,SAAS,EAAC,kBAAkB;MAC5BmD,MAAM,EAAEC,QAAS;MACjBvD,MAAM,EAAEA,MAAO;MACfwD,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;MACnBC,aAAa,EAAE,CAAC,GAAG,CAAE;MACrBC,IAAI,EAAC,GAAG;MACRC,cAAc,EAAE,CAAC,GAAG,EAAEJ,QAAQ,CAAE,CAAC;MAAA;MACjCK,cAAc,EAAE,CAAC,GAAG,EAAEL,QAAQ;MAC9B;MAAA;MACAvC,YAAY,EAAEA,YAAa;MAC3B6C,KAAK,EAAE;QAAEP,MAAM,EAAE;MAAO,CAAE;MAAAlD,QAAA,eAE1BjD,OAAA,CAACG,KAAK;QAAC6C,SAAS,EAAC,kBAAkB;QAACQ,KAAK,EAAE,MAAO;QAAAP,QAAA,gBAChDjD,OAAA;UAAKgD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCjD,OAAA;YAAK0G,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,cAAc,EAAE;YAAS,CAAE;YAAA5D,QAAA,EAC5E,CAAAzB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsF,MAAM,IAAG,CAAC,IAAI7F,QAAQ,gBACnCjB,OAAA;cAAG0G,KAAK,EAAE;gBAAGK,WAAW,EAAE,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAC/B,KAAK,EAAC,4BAAQ;cACxEc,OAAO,EAAEA,CAAA,KAAIxF,MAAM,GAAG,CAAC,GAAG8D,UAAU,CAAC,CAAC,CAAC,GAAG,IAAK;cAAArB,QAAA,GAAC,eAAG,EAAC3C,QAAQ,EAAC,SAAE;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAEtErD,OAAA;cAAG0G,KAAK,EAAE;gBAAIK,WAAW,EAAE,EAAE;gBAACC,QAAQ,EAAE,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAC/B,KAAK,EAAC,4BAAQ;cACtEc,OAAO,EAAEA,CAAA,KAAIxF,MAAM,GAAG,CAAC,GAAG8D,UAAU,CAAC,CAAC,CAAC,GAAG,IAAK;cAAArB,QAAA,GAAC,eAAG,EAAC3C,QAAQ,EAAC,SAAE;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYvE,CAAC,eACNrD,OAAA,CAAC/B,KAAK;YAAAgF,QAAA,gBAEJjD,OAAA,CAAC3B,OAAO;cACNgH,OAAO,eAAErF,OAAA;gBAAAiD,QAAA,eACPjD,OAAA,CAAC1B,KAAK,CAAC4I,KAAK;kBAACC,QAAQ,EAAGC,CAAC,IAAK7F,UAAU,CAAC6F,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBAACA,KAAK,EAAEhG,OAAQ;kBAAA2B,QAAA,eACvEjD,OAAA,CAAC/B,KAAK;oBAACsJ,SAAS,EAAC,UAAU;oBAAAtE,QAAA,gBACzBjD,OAAA,CAAC1B,KAAK;sBAACgJ,KAAK,EAAE5I,cAAc,CAAC8I,0BAA2B;sBAAAvE,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACrErD,OAAA,CAAC1B,KAAK;sBAACgJ,KAAK,EAAE5I,cAAc,CAAC+I,0BAA2B;sBAAAxE,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAE;cAAC6B,KAAK,eAAElF,OAAA;gBAAK0G,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,cAAc,EAAE;gBAAgB,CAAE;gBAAA5D,QAAA,gBACpGjD,OAAA;kBAAAiD,QAAA,EAAK;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACfrD,OAAA;kBAAGkF,KAAK,EAAE9D,YAAY,GAAG,MAAM,GAAG,MAAO;kBAAC4E,OAAO,EAAEA,CAAA,KAAM3E,eAAe,CAAC,CAACD,YAAY,CAAE;kBAAA6B,QAAA,eACtFjD,OAAA,CAACzB,KAAK;oBAACmJ,GAAG,EAAEtG,YAAY,GAAGxB,OAAO,GAAGD,OAAQ;oBAACgI,OAAO,EAAE,KAAM;oBAACnE,KAAK,EAAE,EAAG;oBAAC2C,MAAM,EAAE,EAAG;oBAACO,KAAK,EAAE;sBAACkB,YAAY,EAAC;oBAAC;kBAAE;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAE;cAACwE,OAAO,EAAC,OAAO;cAACC,SAAS,EAAC,QAAQ;cAAA7E,QAAA,eAC1CjD,OAAA;gBAAGkF,KAAK,EAAC,cAAI;gBAACwB,KAAK,EAAE;kBAACO,KAAK,EAAC;gBAAS,CAAE;gBAAAhE,QAAA,eACrCjD,OAAA,CAACzB,KAAK;kBAACmJ,GAAG,EAAEtG,YAAY,GAAGxB,OAAO,GAAGD,OAAQ;kBAACgI,OAAO,EAAE,KAAM;kBAACnE,KAAK,EAAE,EAAG;kBAAC2C,MAAM,EAAE,EAAG;kBAACO,KAAK,EAAE;oBAACkB,YAAY,EAAC;kBAAC;gBAAE;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAERrD,OAAA,CAAC/B,KAAK;cAAAgF,QAAA,GACH3C,QAAQ,gBAAGN,OAAA;gBAAKgD,SAAS,EAAC,aAAa;gBAAC0D,KAAK,EAAE;kBAAEO,KAAK,EAAE;gBAAO,CAAE;gBAAAhE,QAAA,GAAC,GAAC,EAACzC,MAAM,EAAC,QAAC;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAAG,EAAE,EACvFwC,OAAO;YAAA;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACL9C,SAAS,IAAI,CAAAA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuG,MAAM,IAAG,CAAC,gBACjC9G,OAAA,CAAAE,SAAA;UAAA+C,QAAA,gBAEEjD,OAAA,CAAC7B,IAAI;YAAC4J,UAAU,EAAExH,SAAU;YAC1ByC,SAAS,EAAC,YAAY;YACtBgF,UAAU,EAAEA,CAACpD,MAAM,EAAEqD,KAAK,kBACxBjI,OAAA,CAACR,SAAS;cACRwG,OAAO,EAAElB,aAAc;cACvBF,MAAM,EAAEA,MAAO;cACf/C,qBAAqB,EAAEA,qBAAsB;cAC7CD,cAAc,EAAEA,cAAe;cAC/BD,YAAY,EAAEA;YAAa;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEPrD,OAAA,CAAC5B,UAAU;YACT4E,SAAS,EAAE,6BAA8B;YACzCkF,QAAQ,EAAEvJ,WAAW,CAACwJ,WAAY;YAClCC,OAAO,EAAE5H,MAAO;YAChBwD,IAAI,EAAC,OAAO;YACZqE,eAAe,EAAE,KAAM;YACvBC,KAAK,EAAEhI,QAAS;YAChB6G,QAAQ,EAAE7C;UAAW;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA,eACF,CAAC;QAAA;QAEH;QACArD,OAAA;UAAKgD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCjD,OAAA;YAAKgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAE3C5D,aAAa,CAACiC,gBAAgB,CAACoE,SAAS,CAAC,gBAAG9F,OAAA;YAAKgD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClGjD,OAAA;cAAM0G,KAAK,EAAE;gBAAE6B,YAAY,EAAE;cAAE,CAAE;cAAAtF,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CrD,OAAA;cAAGgG,OAAO,EAAEA,CAAA,KAAKpD,oBAAoB,CAAC,CAAE;cAAAK,QAAA,GAAC,cAAE,EAACxB,WAAW,CAACyE,UAAU;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,gBAAGrD,OAAA,CAAAE,SAAA,mBAAI,CAAC;QAAA;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACfrD,OAAA,CAAC9B,MAAM;MAAC8E,SAAS,EAAC,cAAc;MAAAC,QAAA,eAE9BjD,OAAA,CAACnB,MAAM;QAAC2J,OAAO,EAAE;UAAEnG,MAAM;UAAEG,WAAW;UAAE5B,aAAa;UAAED,aAAa;UAAED,QAAQ;UAC5DQ,iBAAiB;UAAEC,aAAa;UAAEJ,4BAA4B;UAAEC,wBAAwB;UACxFS,WAAW;UAAEG,cAAc;UAAEd,kBAAkB;UAAEP,SAAS;UAACuB;QAAmB;MAAE;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/F,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AACZ;AAAChD,EAAA,CA1OuBD,SAAS;EAAA,QAM3BpB,gBAAgB,EAEAJ,cAAc,EACjBG,WAAW,EACYG,eAAe,EAGVD,SAAS,EACCI,oBAAoB,EAyBvED,mCAAmC;AAAA;AAAAqJ,EAAA,GAvCjBrI,SAAS;AAAA,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}