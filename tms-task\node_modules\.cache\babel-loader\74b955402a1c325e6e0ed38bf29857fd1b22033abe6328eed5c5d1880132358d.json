{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function once(fn) {\n  const _this = this;\n  let didCall = false;\n  let result;\n  return function () {\n    if (didCall) {\n      return result;\n    }\n    didCall = true;\n    result = fn.apply(_this, arguments);\n    return result;\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}