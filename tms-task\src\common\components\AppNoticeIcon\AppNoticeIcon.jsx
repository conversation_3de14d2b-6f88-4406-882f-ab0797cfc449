
import React, { useState, useEffect, useMemo } from "react";
import { Bad<PERSON>, Button, Checkbox, Drawer, Dropdown, List, Menu, Popover, Space, Modal, Avatar, Popconfirm } from "antd";
import { useNavigate, useParams, Link } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getOptionByType } from "@common/utils/TsbConfig";
import * as httpCommon from "@common/api/http";
import "./AppNoticeIcon.scss";
import AppNoticeIconAllModal from "./AppNoticeIconAllModal";
import { globalUtil } from "@common/utils/globalUtil";
import { useQuerySetting407_getCodeValueList } from "@common/service/commonHooks";
import CreateTeamModal, { CREATETYPE_UPGRADE, } from "@components/CreateTeam";
import moment from "moment";
import { getUrlByModule } from '@common/utils/logicUtils';
import { useDispatch } from "react-redux";
import { getTeamList } from "src/team/store/actionCreators";
import { CheckCircleOutlined } from '@ant-design/icons';
import { team_578_get_notify_query, /* setting_125_refresh_team_query */useQuery_setting_126_get_setting_teams } from "@common/api/query/query";
import { useThrottle } from "@common/hook/useThrottle";
import { eProductGroupId, eProductStatus, eSysNotifierOpType } from "@common/utils/enum";
import DraggableDrawer from "@components/DraggableDrawer";
import SettingsDrawer from "@/settings/views/SettingsDrawer";
//系统通知 
function AppNoticeIcon(props) {
  const {noticeVisible, setNoticeVisible} = props
  //const queryClient = useQueryClient();
  const navigate = useNavigate()
  const dispatch = useDispatch();
  const { teamId } = useParams();
  const [showUnReadView, setShowUnReadView] = useState(true);
  // const {data: selectionList, isLoading: isLoadingCodeValueList} = useQuerySetting407_getCodeValueList(teamId); // 字典数据
  //const { data, isLoading } = useQueryTeam557GetSysNotification()
  const { data: teamData } = useQuery({
    ...useQuery_setting_126_get_setting_teams()
  });
  const teamList = teamData?.teams||[]
  const {data: notifyData, refetch: refetchNotify, dataUpdatedAt } = useQuery({ //isLoading,
    ...team_578_get_notify_query(teamId)
  });
  // const { data: setting125Data } = useQuery({
  //   ...setting_125_refresh_team_query(teamId),
  //   enabled: false
  // })
  const [appNoticeIconVisible, setAppNoticeIconVisible] = useState(false);
  const [showMessage,_setShowMessage] = useState(false);
  const [dropDownOpenId,setDropDownOpenId] = useState(null);
  const [createTeamModalVisible,setCreateTeamModalVisible] = useState(false);
  // const [systemUpdateTips,setSystemUpdateTips] = useState(true);
  const [productListFinal,setProductListFinal] = useState([]);
  //const autoClosePopRef = useRef(false);

  const [settingsDrawerVisible, setSettingsDrawerVisible] = useState(false); //打开团队设置->应用管理
  const [settingsDrawerTab, setSettingsDrawerTab] = useState('product'); //默认打开 应用管理
  const [productId, setProductId] = useState(); //打开应用授权，具体的某一个应用


  const setShowMessage = useThrottle((value) => {
    _setShowMessage(value)
  }, 200)

  useEffect(()=>{
    getProductsList()
  },[]);

  useEffect(()=>{
    if(noticeVisible){
      setShowMessage(true);
    }
  },[noticeVisible]);

  useEffect(()=>{
    // 通知自动弹出的条件
    // 1、系统身份信息已填写
    // 2、团队个人信息已填写
    // 3、第一次的新手向导已做
    if(notifyData?.needPopupFlg == 1 && notifyData?.autoPopupFlg == 1 /* && !setting125Data?.firstEntryFlg && setting125Data?.bindMobileFlg && setting125Data?.trialReminderFlg */){
      if(!showMessage){
        setShowMessage(true)
      }
    }
  },[dataUpdatedAt]);

  function getProductsList() {
    httpCommon.team_711_get_team_product_list({ teamId: teamId }).then(res => {
      if (res.resultCode == 200) {
        let allProductsList = (res.productList || [])
          .filter(product => product.freeFlg == 0 && product.groupId != eProductGroupId.Pgid_1_OS && product.statusType != eProductStatus.Status_3_Unreleased)
          .map(product => {
            product.key = product.productId
            return product
          });
        productListFormat(allProductsList);
      }
    });
  }

  function productListFormat(productList=[]){
    let packageList = [];
    productList.forEach(product => {
      let item = packageList.find(_product => product.groupId == _product.groupId)
      if(!item){
        packageList.push({groupName: product.groupName, groupId: product.groupId, groupList: [product]});
      }else{
        item.groupList.push(product)
      }
    });
    let allProductsList = [];
    packageList.forEach(group => {
      let groupList = group.groupList.map((config,index) => ({
        ...config,
        key: config.productId,
        isRowSpan: index == 0 ? true : false,
        groupListLength: index == 0 ? group.groupList.length : 0,
        authCntDesc: !!config.authCntDesc ? config.authCntDesc : (productListFinal.find(product => product.productId == config.productId)?.authCntDesc||''),
        objCntDesc: !!config.objCntDesc ? config.objCntDesc : (productListFinal.find(product => product.productId == config.productId)?.objCntDesc||''),
      }));
      allProductsList = allProductsList.concat(groupList)
    });
    setProductListFinal([...allProductsList]);
  }

  // 删除单条通知
  function deleteNotic(item,refetch557) {
    let params = { teamId: teamId, opType: "phy_delete", notifyIds: [item.id], }
    httpCommon.team_558_update_notification_status(params)
      .then((res) => {
        if (res.resultCode === 200) {
          globalUtil.success('删除成功')
          refetch557 && refetch557();
        }
      });
  }

  //通知设置为已读
  function readNotics(id,opType,refetch557){
    let params = { teamId: teamId, opType: "update", notifyIds: [id], }
    if(!!opType){
      params.notifyType = opType
    }
    httpCommon.team_558_update_notification_status(params)
      .then((res) => {
        if (res.resultCode === 200) {
          globalUtil.success('本消息已读');
          refetch557 && refetch557()
          refetchNotify()
        }
      });
  }

  function isLink(item){
    if(item.opType == eSysNotifierOpType.op_1_comment || item.opType == eSysNotifierOpType.op_2_at_me || item.opType == eSysNotifierOpType.op_6_like){
      return true;
    }else if(item.opType == eSysNotifierOpType.op_11_approve_mbr_join && item?.supplierMap?.groupId){ //申请加入
      return true;
    }else if(item.opType == eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited && item?.supplierMap?.teamId){ //手机/邮箱邀请
      return true;
    } else if(item.opType == eSysNotifierOpType.op_25_product_auth){ //应用授权
      if(item?.teamId == teamId)
        return false
      else
        return true
    } else if(item.opType == eSysNotifierOpType.op_27_set_kpi_stop_bpmn && item?.supplierMap?.teamId){ // 任务停止流程尚未配置完全
      return true;
    } else {
      return false;
    }
  }

  function goToNewPage(item,refetch557){
    setDropDownOpenId(null);
    switch(item.opType){
      case eSysNotifierOpType.op_1_comment://评论
        // let url = getLinkUrl(item);
        // navigate(url,{state:{refresh: ["issues", "questions"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */
        btnClick(item,item.opType,refetch557);
        break;
      case eSysNotifierOpType.op_2_at_me://去到被@的节点
        // let url = getLinkUrl(item);
        // navigate(url,{state:{refresh: ["issues", "questions"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */
        btnClick(item,item.opType,refetch557);
        break;
      case eSysNotifierOpType.op_6_like://点赞
        // let url = getLinkUrl(item);
        // navigate(url,{state:{refresh: ["issues", "questions"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */
        btnClick(item,item.opType,refetch557);
        break;
      case eSysNotifierOpType.op_11_approve_mbr_join://去审批
        // item?.supplierMap?.spaceId && item?.supplierMap?.groupId && navigate(`/${teamId}/settings/space/${item.supplierMap.spaceId}/member/approval/${item.supplierMap.groupId}/2`);
        !!item?.supplierMap?.groupId && btnClick(item,item.opType,refetch557);
        break;
      case eSysNotifierOpType.op_13_add_mbr_user_quota://团队成员扩容
        setCreateTeamModalVisible(true);
        btnClick(item,item.opType,refetch557);
        break;
      case eSysNotifierOpType.op_14_buy_vip_expiration_dt://续费
       setCreateTeamModalVisible(true);
       btnClick(item,item.opType,refetch557);
       break;
      case eSysNotifierOpType.op_15_buy_cdisk_traffic://云盘扩容
        globalUtil.error('待开放');
        break;
      case eSysNotifierOpType.op_17_accept_invite://接受邀请
        item?.objId && item?.supplierMap?.inviteTeamId && acceptInvitation(item,refetch557);
        break;
      case eSysNotifierOpType.op_18_buy://购买
        setCreateTeamModalVisible(true);
        btnClick(item,item.opType,refetch557);
        break;
      case eSysNotifierOpType.op_22_upgrade_to_vip://升级企业版
        setCreateTeamModalVisible(true);
        btnClick(item,item.opType,refetch557);
        break;
      case eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited://进入团队
        item?.supplierMap?.teamId && btnClick(item,item.opType,refetch557);
        break;
      case eSysNotifierOpType.op_25_product_auth: //产品授权
          if(teamId == item.teamId) { //授权的请求，正好是本团队的请求，直接打开 SettingsDrawer的应用管理
            if(appNoticeIconVisible){
              setAppNoticeIconVisible(false)
            }
            if(noticeVisible){
              setNoticeVisible(false) //关闭本身(系统通知)Drawer
            }
           // setSettingsDrawerTab('product')
            setProductId(item.objId); //objId存储的就是productId,如21为文档/文档库
            setSettingsDrawerVisible(true)
          }else {
            btnClick(item,0,refetch557); //继续走default路径
          }
          break;
      default:
        btnClick(item,0,refetch557);
        break; 
    }
  }

  function btnClick(item,opType,refetch557){
    // let closeList = [2,11,13,14,15,17,18,22,23]
    // if(closeList.find(objType => objType == item.opType)){
    //   setAppNoticeIconVisible(false)
    // }
    if(notifyData.notificationReminders.find(remind => remind.id == item.id && remind.opStatus == 1)){
      readNotics(item.id,opType,refetch557);
    }
  }

  function acceptInvitation(item,refetch557){
    httpCommon.setting_217_add_user_by_invite_url({teamId: item.supplierMap.inviteTeamId,inviteId: item.objId}).then(res=>{
      if(res.resultCode == 200){
        //刷新团队列表
        btnClick(item,eSysNotifierOpType.op_17_accept_invite,refetch557);
        dispatch(getTeamList());
        joinTeamSuccess(item.supplierMap.inviteTeamId,item.objTitle)
      }
    });
  }

  function joinTeamSuccess(_teamId,newTeamName){
    Modal.confirm({
      title: '提示',
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }}/>,
      content: <div style={{ display: 'flex', flexDirection: 'column' }}>
        <span>团队加入成功，是否切换至 {newTeamName.substring(0,newTeamName.indexOf('团队的'))} ？</span>
        <span>点击"否"，停留在当前页面；</span>
        <span>点击"是"，切换至新的团队。</span>
      </div>,
      okText: '是，切换团队',
      cancelText: '否',
      onOk:() =>{
        navigate(`/team/${_teamId}`)
      },
      onCancel:()=>{}
    });
  }

  const getLinkUrl = (item) => {
    if(item.opType == eSysNotifierOpType.op_11_approve_mbr_join){ // 申请加入 去审批
      if(!!item?.supplierMap?.spaceId){
        return `/${item.teamId}/settings/space/${item.supplierMap.spaceId}/member/approval/${item.supplierMap.groupId}/2`
      }
      return `/${item.teamId}/settings/user/member/approval/${item.supplierMap.groupId}/2`
    }
    if(item.opType == eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited){ //手机/邮箱邀请 进入团队
      if(!!item.supplierMap?.teamId){
        return `/team/${item.supplierMap.teamId}`
      }
      return ``
    }
    if(item.opType == eSysNotifierOpType.op_25_product_auth){ //去授权
      //item.objId 存放 productId
      return `/${item.teamId}/settings/product/${item.objId}`
    }
    if(item.opType == eSysNotifierOpType.op_27_set_kpi_stop_bpmn){ // 任务停止流程尚未配置完全
      if(!!item.teamId){
        return `/team/${item.supplierMap.teamId}/stopTasks/${item.supplierMap.nodeId}`
      }
      return ``
    }
    let obj = {
      nodeType: item.supplierMap?.objType, 
      anchorNodeId: item.supplierMap?.anchorNodeId, 
      nodeId: item.supplierMap?.nodeId
    }
    let no_hash_url = getUrlByModule(item.teamId, { ...obj });
    let hash_url = ((item.objType == 1000 && item.supplierMap?.commentId)?`#comment-${item.supplierMap.commentId}`:'');
    return no_hash_url + hash_url
  }

  const getFirstName = (teamName) => {
    try {
      return teamName.substring(0, 1);
    } catch (e) {
      return "";
    }
  };

  const onOpenChange = (open) => {
    if(!open) {
      setDropDownOpenId(null);
      if(open != showMessage) {
        setTimeout(() => {
          setShowMessage(false);
          setNoticeVisible(false);
        }, 300) //20250801 2000 -> 300, 2秒太久
      }
    }
  }

  return (
    <React.Fragment>
      <Popover 
        open={showMessage}
        placement="bottom"
        overlayClassName={"noticPopover " + (appNoticeIconVisible ? "noticPopover-999" : "")}
        trigger={'click'}
        onOpenChange={onOpenChange}
        title={<NoticeTitle
          teamId={teamId}
          setAppNoticeIconVisible={setAppNoticeIconVisible}
          setNoticeVisible = {setNoticeVisible}
          closePopover={() => {setShowMessage(false);setNoticeVisible(false);setDropDownOpenId(null);}}
          showUnReadView={showUnReadView} 
          setShowUnReadView={setShowUnReadView}
        />}
        content={showMessage?
            <NoticeContent
              teamId={teamId}
              goToNewPage={goToNewPage}
              isLink={isLink}
              dropDownOpenId={dropDownOpenId}
              setDropDownOpenId={setDropDownOpenId}
              getLinkUrl={getLinkUrl}
              getFirstName={getFirstName}
              teamList={teamList}
              showUnReadView={showUnReadView} />
            :
            <></>
        }>
        <Badge offset={[-5, 5]} size="small" count={(notifyData?.notificationReminders || []).filter(reminder => reminder.opStatus == 1).length}
               showZero={false} {...props}>
          <Button type="link" shape="circle" icon={<span className={`iconfont ${notifyData?.autoPopupFlg == 1 ? "tongzhi1" : "tongzhibutixin"} `} />}
                  onClick={()=>{setShowMessage(!showMessage);setNoticeVisible(false);}}
                  className="header-icon" />
        </Badge>
      </Popover>
      <DraggableDrawer
        title={
          <span style={{ color: "orange" }} className="iconfont tongzhi1">
            <span style={{ color: "#666",marginLeft:10 }}>系统通知 - 全部</span>
          </span>
        }
        className="AppNoticeIconAllModl-drawer"
        centered
        open={appNoticeIconVisible}
        destroyOnClose
        onClose={() => {
          setShowMessage(false);
          setNoticeVisible(false);
          setDropDownOpenId(null);
          setTimeout(() => {
            setAppNoticeIconVisible(false);
          }, 300);
        }}
        width={800}
        footer={false}>
        <AppNoticeIconAllModal
          teamId={teamId}
          isLink={isLink}
          getLinkUrl={getLinkUrl}
          deleteNotic={deleteNotic}
          goToNewPage={goToNewPage}
          getFirstName={getFirstName}
          refetchNotify={refetchNotify}
          teamList={teamList}
        />
      </DraggableDrawer>
      <CreateTeamModal
        teamId={teamId}
        type={CREATETYPE_UPGRADE}
        visible={createTeamModalVisible}
        onCancel={() => setCreateTeamModalVisible(false)}
        onOk={() => setCreateTeamModalVisible(false)}
        productList={productListFinal}
      />
      {/* 团队设置/成员管理/应用管理 Drawer */}
      <SettingsDrawer
        visible={settingsDrawerVisible}
        onClose={()=>setSettingsDrawerVisible(false)}
        teamId={teamId}
        defaultTab={settingsDrawerTab}
        productId={productId}
      />
      {/* <BuyTeamModal 
        teamId={teamId} 
        type={buyTeamModalVisible.type} 
        visible={buyTeamModalVisible.visible}
        defaultPackageList={buyTeamModalVisible.defaultPackageList} 
        onCancel={()=> setBuyTeamModalVisible({visible:false,type:"",defaultPackageList:[]})} 
        onOk={()=> setBuyTeamModalVisible({visible:false,type:"",defaultPackageList:[]})}
      /> */}
    </React.Fragment>
  )
}

// the content of notice
function NoticeTitle({teamId,setAppNoticeIconVisible,setNoticeVisible,closePopover,showUnReadView,setShowUnReadView}) {
  const queryClient = useQueryClient();

  const {data, refetch: refetchNotify, dataUpdatedAt: team578DataUpdatedAt } = useQuery({
    ...team_578_get_notify_query(teamId),
    enabled: false,
    refetchInterval: false
  });

  const [ignorePopoverVisible,setIgnorePopoverVisible] = useState(false);

  const onAutoEjectChange = (e) => {
    let autoPopupFlg = e.target.checked ? 1 : 0;
    httpCommon.team_563_set_notify_auto_popup({teamId,autoPopupFlg})
      .then(result=>{
        if(result.resultCode == 200){
          queryClient.setQueryData(team_578_get_notify_query(teamId).queryKey, oldData => {
            return {
              ...oldData,
              autoPopupFlg
            }
          })
        }
      });
  }

  const onShowUnReadChange = (e) => {
    setShowUnReadView(e.target.checked);
  }

  // 忽略全部通知
  const ignoreNotic = () => {
    let params = {
      teamId: teamId,
      opType: "delete",
      notifyIds: []
    }

    httpCommon.team_558_update_notification_status(params)
      .then(result => {
        if(result.resultCode == 200){
          refetchNotify();
          setIgnorePopoverVisible(false);
        }
      })
  }

  const notifyCount = useMemo(() => {
    return (data?.notificationReminders??[]).filter(item => {
      if(showUnReadView) {
        return item.opStatus == 1
      } else {
        return true
      }
    }).length
  }, [showUnReadView, team578DataUpdatedAt])

  return (<div className="noticPopover-title">
    <Space size={10}>
      <div className="noticPopover-title-left">
        <span className="iconfont tongzhi1" />
        <div className="noticPopover-title-text">系统通知{notifyCount ? `(${notifyCount})` : ''}</div>
      </div>
      <Checkbox className="noticPopover-title-check" checked={data?.autoPopupFlg} onChange={onAutoEjectChange}>自动弹出</Checkbox>
      <Checkbox className="noticPopover-title-check" checked={showUnReadView} onChange={onShowUnReadChange}>只看未读</Checkbox>
      <Popconfirm
        overlayClassName="ignorePopover"
        title={`忽略全部(${notifyCount}条)通知?`}
        placement={'right'}
        trigger={'click'}
        open={ignorePopoverVisible}
        onConfirm={ignoreNotic}
        onCancel={()=>{setIgnorePopoverVisible(false)}}
      >
        <a
          className="noticPopover-title-option"
          style={!notifyCount ? {color:'#999'} : {}}
          onClick={()=>{notifyCount && setIgnorePopoverVisible(!ignorePopoverVisible)}}
        >
          <span title="忽略全部通知" className="iconfont butixing fontsize-14"></span>
        </a>
      </Popconfirm>
    </Space>
    <div style={{display:'flex',alignItems:'center'}}>
      <a className="fontsize-12" onClick={() => {
        setAppNoticeIconVisible(true)
        setNoticeVisible(false); //20250801 Jim Song, 系统通知，点击全部后，将自身小弹窗关闭
      }}>
        全部{data?.allCount ? ('(' + (data.allCount > 99 ? '99+' : data.allCount) + ')') : ''}
      </a>
      <a style={{marginLeft:20,color:'#666'}} onClick={()=>closePopover()}><span className="iconfont guanbi" style={{fontSize:14}}/></a>
    </div>
  </div>)
}

function NoticeContent({teamId, goToNewPage, isLink, dropDownOpenId, setDropDownOpenId, getLinkUrl, getFirstName, teamList, showUnReadView}) {
  const {data, refetch: refetchNotify, dataUpdatedAt: team578DataUpdatedAt } = useQuery({
    ...team_578_get_notify_query(teamId),
    enabled: false,
    refetchInterval: false
  });

  const {data: selectionList } = useQuerySetting407_getCodeValueList(teamId); // 字典数据

  function ignoreNotic(item){
    let params = {
      teamId: teamId,
      opType: "delete",
      notifyIds: [item.id]
    }

    httpCommon.team_558_update_notification_status(params)
      .then(result => {
        if(result.resultCode == 200){
          refetchNotify();
        }
      })
  }

  function remindLater(key,item){
    let params = {
      teamId: teamId,
      notifyIds: [item.id],
      remindType: key
    }
    httpCommon.team_559_set_notification_remind_later(params).then(res => {
      if(res.resultCode == 200){
        ignoreNotic(item);
      }
    });
  }

  function menuList(){
    let list = [
      {
        key:'ignore',
        label: (<div style={{display:'flex',alignItems:'center',fontSize:12,justifyContent:'center'}}>
          <span className="iconfont butixing" style={{fontSize:12,marginRight:5}}/>
          <span>忽略</span>
        </div>)
      }
    ];
    let dataList = (selectionList||[])
      .filter(selection => selection.selectionId == 1947)
      .map(selection => ({
        key: selection.propType,
        label: <div style={{fontSize:12,display:'flex',alignItems:'center',justifyContent:'space-between',width:60}}>{selection.propValue}<div>后</div></div>
      }));
    return list.concat(dataList)
  }

  function menuClick(e,item){
    setDropDownOpenId(null);
    if(e.key == 'ignore'){
      ignoreNotic(item)
    }else{
      remindLater(e.key,item)
    }
  }

  const memberMenu = item => (<div className="flex-column-parent">
    <Menu
      items={menuList()}
      className='flex-column-child section'
      style={{height:150}}
      onClick={(e) => menuClick(e,item)}/>
  </div>);

  function getWidth(item,getOptionByType){
    let width = 400
    if(!getOptionByType(item.opType) && !!item.opInfo){
      width = 400
    }else{
      width = 350
    }
    if(item.opTypeName.length > 2){
      width = width - 20
    }
    return width
  }

  function buttonUi(item,goToNewPage,getOptionByType){
    if(!getOptionByType(item.opType)){
      return null
    }
    return (
      <Button disabled={!!(teamList||[]).find(team => team.teamId == item?.supplierMap?.inviteTeamId)}
              className="noticPopover-list-btn"
              onClick={() => goToNewPage(item)}>
        {getOptionByType(item.opType)}
      </Button>
    )
  }

  function dropDownUi(item,memberMenu){
    return (
      <Dropdown trigger={['click']} overlay={memberMenu(item)} open={dropDownOpenId == item.id}>
        <a style={{color:'#999',fontSize:12,display:'flex',alignItems:'center'}}
           onClick={(e)=>{
             e.preventDefault();
             e.stopPropagation();
             setDropDownOpenId(item.id == dropDownOpenId ? null : item.id)
           }}
        >
          稍后提醒<span className="iconfont xiala1" style={{fontSize:14,color:'#999'}}/>
        </a>
      </Dropdown>
    )
  }

  function contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName){
    return (
      <div style={{width:'100%'}}>
        <div style={{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'3px 5px 0px 20px'}}>
          <div style={{display:'flex',alignItems:'center'}}>
            {!item.logoUrl ?
              <div className="notic-team-logo">
                {getFirstName(item.teamName)}
              </div>
              :
              <Avatar
                style={{
                  marginRight: 5,
                  width: 28,
                  minWidth: 28,
                  height: 28,
                }}
                src={item.logoUrl}
              />
            }
            <div className="fontsize-12 text-overflow" style={{color:'#333', flex: "auto"}}>{item.teamName}</div>
            <div style={{fontSize: 12, color: '#333', marginLeft: 10}}>{item.opTypeName}</div>
          </div>
          <Space size={5}>
            {!item.opInfo &&
              buttonUi(item,goToNewPage,getOptionByType)}
            <div style={{fontSize:12,color:'#999'}}>
              {item.notifyDt ? moment(item.notifyDt).format('YY-MM-DD HH:mm') : ''}
            </div>
          </Space>
        </div>
        <div style={{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'0px 5px 0px 20px'}}>
          <div style={{display:'flex',alignItems:'center'}}>
            <a
              onClick={() => getOptionByType(item.opType) ? null : goToNewPage(item)}
              className='tms-link-nocolor'
              style={{
                display:'flex',
                alignItems:'center',
                marginLeft:!!item.opTypeName ? 5 : 0,
                width: getWidth(item,getOptionByType)
              }}
            >
              <div title={item.objTitle} className="fontsize-12 text-overflow">{item.objTitle}</div>
              <Badge dot={item.opStatus == 1} size="small" offset={[-4,-4]}/>
            </a>
          </div>
          {!!item.opInfo ?
            buttonUi(item,goToNewPage,getOptionByType)
            :
            dropDownUi(item,memberMenu)
          }
        </div>
        {!!item.opInfo &&
          <div style={{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'0px 5px 0px 20px'}}>
            <div
              style={{
                fontSize: 12,
                color:'#999',
                width: 380,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              {item.opInfo}
            </div>
            {dropDownUi(item,memberMenu)}
          </div>
        }
      </div>
    )
  }

  const ListItem = ({key,item,goToNewPage,goToNewPage1,getOptionByType,memberMenu,getFirstName}) => {
    return <List.Item>
      {isLink(item)?
        <Link key={key} style={{width:'100%'}} to={getLinkUrl(item)} target="_blank" onClick={() => goToNewPage1(item)}>
          {contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName)}
        </Link>
        :
        contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName)
      }
    </List.Item >
  }

  const notificationReminders = useMemo(() => {
    return (data?.notificationReminders??[]).filter(item => {
      if(showUnReadView) {
        return item.opStatus == 1
      } else {
        return true
      }
    })
  },[team578DataUpdatedAt, showUnReadView])

  return <div className="flex-column-parent">
    <List className={"noticPopover-list" + (notificationReminders.length > 9 ? ' flex-column-child section' : '')}
          style={notificationReminders.length > 9 ? {height:'480px'} : {}}
          itemLayout="horizontal"
          dataSource={notificationReminders}
          renderItem={(item,index) =>
            isLink(item)?
              <ListItem
                key={index}
                item={item}
                goToNewPage={() => {}}
                goToNewPage1={goToNewPage}
                getOptionByType={getOptionByType}
                memberMenu={memberMenu}
                isLink={isLink}
                getFirstName={getFirstName}/>
              :
              <ListItem
                key={index}
                item={item}
                goToNewPage={goToNewPage}
                getOptionByType={getOptionByType}
                memberMenu={memberMenu}
                isLink={isLink}
                getFirstName={getFirstName}/>
          }
    />
  </div >
}

export default React.memo(AppNoticeIcon)