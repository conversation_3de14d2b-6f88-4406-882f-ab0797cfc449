{"ast": null, "code": "// Selects all codicon names encapsulated in the `$()` syntax and wraps the\n// results with spaces so that screen readers can read the text better.\nexport function getCodiconAriaLabel(text) {\n  if (!text) {\n    return '';\n  }\n  return text.replace(/\\$\\((.*?)\\)/g, (_match, codiconName) => ` ${codiconName} `).trim();\n}\n/**\n * The Codicon library is a set of default icons that are built-in in VS Code.\n *\n * In the product (outside of base) Codicons should only be used as defaults. In order to have all icons in VS Code\n * themeable, component should define new, UI component specific icons using `iconRegistry.registerIcon`.\n * In that call a Codicon can be named as default.\n */\nexport class Codicon {\n  constructor(id, definition, description) {\n    this.id = id;\n    this.definition = definition;\n    this.description = description;\n    Codicon._allCodicons.push(this);\n  }\n  get classNames() {\n    return 'codicon codicon-' + this.id;\n  }\n  // classNamesArray is useful for migrating to ES6 classlist\n  get classNamesArray() {\n    return ['codicon', 'codicon-' + this.id];\n  }\n  get cssSelector() {\n    return '.codicon.codicon-' + this.id;\n  }\n  /**\n   * @returns Returns all default icons covered by the codicon font. Only to be used by the icon registry in platform.\n   */\n  static getAll() {\n    return Codicon._allCodicons;\n  }\n}\n// registry\nCodicon._allCodicons = [];\n// built-in icons, with image name\nCodicon.add = new Codicon('add', {\n  fontCharacter: '\\\\ea60'\n});\nCodicon.plus = new Codicon('plus', Codicon.add.definition);\nCodicon.gistNew = new Codicon('gist-new', Codicon.add.definition);\nCodicon.repoCreate = new Codicon('repo-create', Codicon.add.definition);\nCodicon.lightbulb = new Codicon('lightbulb', {\n  fontCharacter: '\\\\ea61'\n});\nCodicon.lightBulb = new Codicon('light-bulb', {\n  fontCharacter: '\\\\ea61'\n});\nCodicon.repo = new Codicon('repo', {\n  fontCharacter: '\\\\ea62'\n});\nCodicon.repoDelete = new Codicon('repo-delete', {\n  fontCharacter: '\\\\ea62'\n});\nCodicon.gistFork = new Codicon('gist-fork', {\n  fontCharacter: '\\\\ea63'\n});\nCodicon.repoForked = new Codicon('repo-forked', {\n  fontCharacter: '\\\\ea63'\n});\nCodicon.gitPullRequest = new Codicon('git-pull-request', {\n  fontCharacter: '\\\\ea64'\n});\nCodicon.gitPullRequestAbandoned = new Codicon('git-pull-request-abandoned', {\n  fontCharacter: '\\\\ea64'\n});\nCodicon.recordKeys = new Codicon('record-keys', {\n  fontCharacter: '\\\\ea65'\n});\nCodicon.keyboard = new Codicon('keyboard', {\n  fontCharacter: '\\\\ea65'\n});\nCodicon.tag = new Codicon('tag', {\n  fontCharacter: '\\\\ea66'\n});\nCodicon.tagAdd = new Codicon('tag-add', {\n  fontCharacter: '\\\\ea66'\n});\nCodicon.tagRemove = new Codicon('tag-remove', {\n  fontCharacter: '\\\\ea66'\n});\nCodicon.person = new Codicon('person', {\n  fontCharacter: '\\\\ea67'\n});\nCodicon.personFollow = new Codicon('person-follow', {\n  fontCharacter: '\\\\ea67'\n});\nCodicon.personOutline = new Codicon('person-outline', {\n  fontCharacter: '\\\\ea67'\n});\nCodicon.personFilled = new Codicon('person-filled', {\n  fontCharacter: '\\\\ea67'\n});\nCodicon.gitBranch = new Codicon('git-branch', {\n  fontCharacter: '\\\\ea68'\n});\nCodicon.gitBranchCreate = new Codicon('git-branch-create', {\n  fontCharacter: '\\\\ea68'\n});\nCodicon.gitBranchDelete = new Codicon('git-branch-delete', {\n  fontCharacter: '\\\\ea68'\n});\nCodicon.sourceControl = new Codicon('source-control', {\n  fontCharacter: '\\\\ea68'\n});\nCodicon.mirror = new Codicon('mirror', {\n  fontCharacter: '\\\\ea69'\n});\nCodicon.mirrorPublic = new Codicon('mirror-public', {\n  fontCharacter: '\\\\ea69'\n});\nCodicon.star = new Codicon('star', {\n  fontCharacter: '\\\\ea6a'\n});\nCodicon.starAdd = new Codicon('star-add', {\n  fontCharacter: '\\\\ea6a'\n});\nCodicon.starDelete = new Codicon('star-delete', {\n  fontCharacter: '\\\\ea6a'\n});\nCodicon.starEmpty = new Codicon('star-empty', {\n  fontCharacter: '\\\\ea6a'\n});\nCodicon.comment = new Codicon('comment', {\n  fontCharacter: '\\\\ea6b'\n});\nCodicon.commentAdd = new Codicon('comment-add', {\n  fontCharacter: '\\\\ea6b'\n});\nCodicon.alert = new Codicon('alert', {\n  fontCharacter: '\\\\ea6c'\n});\nCodicon.warning = new Codicon('warning', {\n  fontCharacter: '\\\\ea6c'\n});\nCodicon.search = new Codicon('search', {\n  fontCharacter: '\\\\ea6d'\n});\nCodicon.searchSave = new Codicon('search-save', {\n  fontCharacter: '\\\\ea6d'\n});\nCodicon.logOut = new Codicon('log-out', {\n  fontCharacter: '\\\\ea6e'\n});\nCodicon.signOut = new Codicon('sign-out', {\n  fontCharacter: '\\\\ea6e'\n});\nCodicon.logIn = new Codicon('log-in', {\n  fontCharacter: '\\\\ea6f'\n});\nCodicon.signIn = new Codicon('sign-in', {\n  fontCharacter: '\\\\ea6f'\n});\nCodicon.eye = new Codicon('eye', {\n  fontCharacter: '\\\\ea70'\n});\nCodicon.eyeUnwatch = new Codicon('eye-unwatch', {\n  fontCharacter: '\\\\ea70'\n});\nCodicon.eyeWatch = new Codicon('eye-watch', {\n  fontCharacter: '\\\\ea70'\n});\nCodicon.circleFilled = new Codicon('circle-filled', {\n  fontCharacter: '\\\\ea71'\n});\nCodicon.primitiveDot = new Codicon('primitive-dot', {\n  fontCharacter: '\\\\ea71'\n});\nCodicon.closeDirty = new Codicon('close-dirty', {\n  fontCharacter: '\\\\ea71'\n});\nCodicon.debugBreakpoint = new Codicon('debug-breakpoint', {\n  fontCharacter: '\\\\ea71'\n});\nCodicon.debugBreakpointDisabled = new Codicon('debug-breakpoint-disabled', {\n  fontCharacter: '\\\\ea71'\n});\nCodicon.debugHint = new Codicon('debug-hint', {\n  fontCharacter: '\\\\ea71'\n});\nCodicon.primitiveSquare = new Codicon('primitive-square', {\n  fontCharacter: '\\\\ea72'\n});\nCodicon.edit = new Codicon('edit', {\n  fontCharacter: '\\\\ea73'\n});\nCodicon.pencil = new Codicon('pencil', {\n  fontCharacter: '\\\\ea73'\n});\nCodicon.info = new Codicon('info', {\n  fontCharacter: '\\\\ea74'\n});\nCodicon.issueOpened = new Codicon('issue-opened', {\n  fontCharacter: '\\\\ea74'\n});\nCodicon.gistPrivate = new Codicon('gist-private', {\n  fontCharacter: '\\\\ea75'\n});\nCodicon.gitForkPrivate = new Codicon('git-fork-private', {\n  fontCharacter: '\\\\ea75'\n});\nCodicon.lock = new Codicon('lock', {\n  fontCharacter: '\\\\ea75'\n});\nCodicon.mirrorPrivate = new Codicon('mirror-private', {\n  fontCharacter: '\\\\ea75'\n});\nCodicon.close = new Codicon('close', {\n  fontCharacter: '\\\\ea76'\n});\nCodicon.removeClose = new Codicon('remove-close', {\n  fontCharacter: '\\\\ea76'\n});\nCodicon.x = new Codicon('x', {\n  fontCharacter: '\\\\ea76'\n});\nCodicon.repoSync = new Codicon('repo-sync', {\n  fontCharacter: '\\\\ea77'\n});\nCodicon.sync = new Codicon('sync', {\n  fontCharacter: '\\\\ea77'\n});\nCodicon.clone = new Codicon('clone', {\n  fontCharacter: '\\\\ea78'\n});\nCodicon.desktopDownload = new Codicon('desktop-download', {\n  fontCharacter: '\\\\ea78'\n});\nCodicon.beaker = new Codicon('beaker', {\n  fontCharacter: '\\\\ea79'\n});\nCodicon.microscope = new Codicon('microscope', {\n  fontCharacter: '\\\\ea79'\n});\nCodicon.vm = new Codicon('vm', {\n  fontCharacter: '\\\\ea7a'\n});\nCodicon.deviceDesktop = new Codicon('device-desktop', {\n  fontCharacter: '\\\\ea7a'\n});\nCodicon.file = new Codicon('file', {\n  fontCharacter: '\\\\ea7b'\n});\nCodicon.fileText = new Codicon('file-text', {\n  fontCharacter: '\\\\ea7b'\n});\nCodicon.more = new Codicon('more', {\n  fontCharacter: '\\\\ea7c'\n});\nCodicon.ellipsis = new Codicon('ellipsis', {\n  fontCharacter: '\\\\ea7c'\n});\nCodicon.kebabHorizontal = new Codicon('kebab-horizontal', {\n  fontCharacter: '\\\\ea7c'\n});\nCodicon.mailReply = new Codicon('mail-reply', {\n  fontCharacter: '\\\\ea7d'\n});\nCodicon.reply = new Codicon('reply', {\n  fontCharacter: '\\\\ea7d'\n});\nCodicon.organization = new Codicon('organization', {\n  fontCharacter: '\\\\ea7e'\n});\nCodicon.organizationFilled = new Codicon('organization-filled', {\n  fontCharacter: '\\\\ea7e'\n});\nCodicon.organizationOutline = new Codicon('organization-outline', {\n  fontCharacter: '\\\\ea7e'\n});\nCodicon.newFile = new Codicon('new-file', {\n  fontCharacter: '\\\\ea7f'\n});\nCodicon.fileAdd = new Codicon('file-add', {\n  fontCharacter: '\\\\ea7f'\n});\nCodicon.newFolder = new Codicon('new-folder', {\n  fontCharacter: '\\\\ea80'\n});\nCodicon.fileDirectoryCreate = new Codicon('file-directory-create', {\n  fontCharacter: '\\\\ea80'\n});\nCodicon.trash = new Codicon('trash', {\n  fontCharacter: '\\\\ea81'\n});\nCodicon.trashcan = new Codicon('trashcan', {\n  fontCharacter: '\\\\ea81'\n});\nCodicon.history = new Codicon('history', {\n  fontCharacter: '\\\\ea82'\n});\nCodicon.clock = new Codicon('clock', {\n  fontCharacter: '\\\\ea82'\n});\nCodicon.folder = new Codicon('folder', {\n  fontCharacter: '\\\\ea83'\n});\nCodicon.fileDirectory = new Codicon('file-directory', {\n  fontCharacter: '\\\\ea83'\n});\nCodicon.symbolFolder = new Codicon('symbol-folder', {\n  fontCharacter: '\\\\ea83'\n});\nCodicon.logoGithub = new Codicon('logo-github', {\n  fontCharacter: '\\\\ea84'\n});\nCodicon.markGithub = new Codicon('mark-github', {\n  fontCharacter: '\\\\ea84'\n});\nCodicon.github = new Codicon('github', {\n  fontCharacter: '\\\\ea84'\n});\nCodicon.terminal = new Codicon('terminal', {\n  fontCharacter: '\\\\ea85'\n});\nCodicon.console = new Codicon('console', {\n  fontCharacter: '\\\\ea85'\n});\nCodicon.repl = new Codicon('repl', {\n  fontCharacter: '\\\\ea85'\n});\nCodicon.zap = new Codicon('zap', {\n  fontCharacter: '\\\\ea86'\n});\nCodicon.symbolEvent = new Codicon('symbol-event', {\n  fontCharacter: '\\\\ea86'\n});\nCodicon.error = new Codicon('error', {\n  fontCharacter: '\\\\ea87'\n});\nCodicon.stop = new Codicon('stop', {\n  fontCharacter: '\\\\ea87'\n});\nCodicon.variable = new Codicon('variable', {\n  fontCharacter: '\\\\ea88'\n});\nCodicon.symbolVariable = new Codicon('symbol-variable', {\n  fontCharacter: '\\\\ea88'\n});\nCodicon.array = new Codicon('array', {\n  fontCharacter: '\\\\ea8a'\n});\nCodicon.symbolArray = new Codicon('symbol-array', {\n  fontCharacter: '\\\\ea8a'\n});\nCodicon.symbolModule = new Codicon('symbol-module', {\n  fontCharacter: '\\\\ea8b'\n});\nCodicon.symbolPackage = new Codicon('symbol-package', {\n  fontCharacter: '\\\\ea8b'\n});\nCodicon.symbolNamespace = new Codicon('symbol-namespace', {\n  fontCharacter: '\\\\ea8b'\n});\nCodicon.symbolObject = new Codicon('symbol-object', {\n  fontCharacter: '\\\\ea8b'\n});\nCodicon.symbolMethod = new Codicon('symbol-method', {\n  fontCharacter: '\\\\ea8c'\n});\nCodicon.symbolFunction = new Codicon('symbol-function', {\n  fontCharacter: '\\\\ea8c'\n});\nCodicon.symbolConstructor = new Codicon('symbol-constructor', {\n  fontCharacter: '\\\\ea8c'\n});\nCodicon.symbolBoolean = new Codicon('symbol-boolean', {\n  fontCharacter: '\\\\ea8f'\n});\nCodicon.symbolNull = new Codicon('symbol-null', {\n  fontCharacter: '\\\\ea8f'\n});\nCodicon.symbolNumeric = new Codicon('symbol-numeric', {\n  fontCharacter: '\\\\ea90'\n});\nCodicon.symbolNumber = new Codicon('symbol-number', {\n  fontCharacter: '\\\\ea90'\n});\nCodicon.symbolStructure = new Codicon('symbol-structure', {\n  fontCharacter: '\\\\ea91'\n});\nCodicon.symbolStruct = new Codicon('symbol-struct', {\n  fontCharacter: '\\\\ea91'\n});\nCodicon.symbolParameter = new Codicon('symbol-parameter', {\n  fontCharacter: '\\\\ea92'\n});\nCodicon.symbolTypeParameter = new Codicon('symbol-type-parameter', {\n  fontCharacter: '\\\\ea92'\n});\nCodicon.symbolKey = new Codicon('symbol-key', {\n  fontCharacter: '\\\\ea93'\n});\nCodicon.symbolText = new Codicon('symbol-text', {\n  fontCharacter: '\\\\ea93'\n});\nCodicon.symbolReference = new Codicon('symbol-reference', {\n  fontCharacter: '\\\\ea94'\n});\nCodicon.goToFile = new Codicon('go-to-file', {\n  fontCharacter: '\\\\ea94'\n});\nCodicon.symbolEnum = new Codicon('symbol-enum', {\n  fontCharacter: '\\\\ea95'\n});\nCodicon.symbolValue = new Codicon('symbol-value', {\n  fontCharacter: '\\\\ea95'\n});\nCodicon.symbolRuler = new Codicon('symbol-ruler', {\n  fontCharacter: '\\\\ea96'\n});\nCodicon.symbolUnit = new Codicon('symbol-unit', {\n  fontCharacter: '\\\\ea96'\n});\nCodicon.activateBreakpoints = new Codicon('activate-breakpoints', {\n  fontCharacter: '\\\\ea97'\n});\nCodicon.archive = new Codicon('archive', {\n  fontCharacter: '\\\\ea98'\n});\nCodicon.arrowBoth = new Codicon('arrow-both', {\n  fontCharacter: '\\\\ea99'\n});\nCodicon.arrowDown = new Codicon('arrow-down', {\n  fontCharacter: '\\\\ea9a'\n});\nCodicon.arrowLeft = new Codicon('arrow-left', {\n  fontCharacter: '\\\\ea9b'\n});\nCodicon.arrowRight = new Codicon('arrow-right', {\n  fontCharacter: '\\\\ea9c'\n});\nCodicon.arrowSmallDown = new Codicon('arrow-small-down', {\n  fontCharacter: '\\\\ea9d'\n});\nCodicon.arrowSmallLeft = new Codicon('arrow-small-left', {\n  fontCharacter: '\\\\ea9e'\n});\nCodicon.arrowSmallRight = new Codicon('arrow-small-right', {\n  fontCharacter: '\\\\ea9f'\n});\nCodicon.arrowSmallUp = new Codicon('arrow-small-up', {\n  fontCharacter: '\\\\eaa0'\n});\nCodicon.arrowUp = new Codicon('arrow-up', {\n  fontCharacter: '\\\\eaa1'\n});\nCodicon.bell = new Codicon('bell', {\n  fontCharacter: '\\\\eaa2'\n});\nCodicon.bold = new Codicon('bold', {\n  fontCharacter: '\\\\eaa3'\n});\nCodicon.book = new Codicon('book', {\n  fontCharacter: '\\\\eaa4'\n});\nCodicon.bookmark = new Codicon('bookmark', {\n  fontCharacter: '\\\\eaa5'\n});\nCodicon.debugBreakpointConditionalUnverified = new Codicon('debug-breakpoint-conditional-unverified', {\n  fontCharacter: '\\\\eaa6'\n});\nCodicon.debugBreakpointConditional = new Codicon('debug-breakpoint-conditional', {\n  fontCharacter: '\\\\eaa7'\n});\nCodicon.debugBreakpointConditionalDisabled = new Codicon('debug-breakpoint-conditional-disabled', {\n  fontCharacter: '\\\\eaa7'\n});\nCodicon.debugBreakpointDataUnverified = new Codicon('debug-breakpoint-data-unverified', {\n  fontCharacter: '\\\\eaa8'\n});\nCodicon.debugBreakpointData = new Codicon('debug-breakpoint-data', {\n  fontCharacter: '\\\\eaa9'\n});\nCodicon.debugBreakpointDataDisabled = new Codicon('debug-breakpoint-data-disabled', {\n  fontCharacter: '\\\\eaa9'\n});\nCodicon.debugBreakpointLogUnverified = new Codicon('debug-breakpoint-log-unverified', {\n  fontCharacter: '\\\\eaaa'\n});\nCodicon.debugBreakpointLog = new Codicon('debug-breakpoint-log', {\n  fontCharacter: '\\\\eaab'\n});\nCodicon.debugBreakpointLogDisabled = new Codicon('debug-breakpoint-log-disabled', {\n  fontCharacter: '\\\\eaab'\n});\nCodicon.briefcase = new Codicon('briefcase', {\n  fontCharacter: '\\\\eaac'\n});\nCodicon.broadcast = new Codicon('broadcast', {\n  fontCharacter: '\\\\eaad'\n});\nCodicon.browser = new Codicon('browser', {\n  fontCharacter: '\\\\eaae'\n});\nCodicon.bug = new Codicon('bug', {\n  fontCharacter: '\\\\eaaf'\n});\nCodicon.calendar = new Codicon('calendar', {\n  fontCharacter: '\\\\eab0'\n});\nCodicon.caseSensitive = new Codicon('case-sensitive', {\n  fontCharacter: '\\\\eab1'\n});\nCodicon.check = new Codicon('check', {\n  fontCharacter: '\\\\eab2'\n});\nCodicon.checklist = new Codicon('checklist', {\n  fontCharacter: '\\\\eab3'\n});\nCodicon.chevronDown = new Codicon('chevron-down', {\n  fontCharacter: '\\\\eab4'\n});\nCodicon.dropDownButton = new Codicon('drop-down-button', Codicon.chevronDown.definition);\nCodicon.chevronLeft = new Codicon('chevron-left', {\n  fontCharacter: '\\\\eab5'\n});\nCodicon.chevronRight = new Codicon('chevron-right', {\n  fontCharacter: '\\\\eab6'\n});\nCodicon.chevronUp = new Codicon('chevron-up', {\n  fontCharacter: '\\\\eab7'\n});\nCodicon.chromeClose = new Codicon('chrome-close', {\n  fontCharacter: '\\\\eab8'\n});\nCodicon.chromeMaximize = new Codicon('chrome-maximize', {\n  fontCharacter: '\\\\eab9'\n});\nCodicon.chromeMinimize = new Codicon('chrome-minimize', {\n  fontCharacter: '\\\\eaba'\n});\nCodicon.chromeRestore = new Codicon('chrome-restore', {\n  fontCharacter: '\\\\eabb'\n});\nCodicon.circleOutline = new Codicon('circle-outline', {\n  fontCharacter: '\\\\eabc'\n});\nCodicon.debugBreakpointUnverified = new Codicon('debug-breakpoint-unverified', {\n  fontCharacter: '\\\\eabc'\n});\nCodicon.circleSlash = new Codicon('circle-slash', {\n  fontCharacter: '\\\\eabd'\n});\nCodicon.circuitBoard = new Codicon('circuit-board', {\n  fontCharacter: '\\\\eabe'\n});\nCodicon.clearAll = new Codicon('clear-all', {\n  fontCharacter: '\\\\eabf'\n});\nCodicon.clippy = new Codicon('clippy', {\n  fontCharacter: '\\\\eac0'\n});\nCodicon.closeAll = new Codicon('close-all', {\n  fontCharacter: '\\\\eac1'\n});\nCodicon.cloudDownload = new Codicon('cloud-download', {\n  fontCharacter: '\\\\eac2'\n});\nCodicon.cloudUpload = new Codicon('cloud-upload', {\n  fontCharacter: '\\\\eac3'\n});\nCodicon.code = new Codicon('code', {\n  fontCharacter: '\\\\eac4'\n});\nCodicon.collapseAll = new Codicon('collapse-all', {\n  fontCharacter: '\\\\eac5'\n});\nCodicon.colorMode = new Codicon('color-mode', {\n  fontCharacter: '\\\\eac6'\n});\nCodicon.commentDiscussion = new Codicon('comment-discussion', {\n  fontCharacter: '\\\\eac7'\n});\nCodicon.compareChanges = new Codicon('compare-changes', {\n  fontCharacter: '\\\\eafd'\n});\nCodicon.creditCard = new Codicon('credit-card', {\n  fontCharacter: '\\\\eac9'\n});\nCodicon.dash = new Codicon('dash', {\n  fontCharacter: '\\\\eacc'\n});\nCodicon.dashboard = new Codicon('dashboard', {\n  fontCharacter: '\\\\eacd'\n});\nCodicon.database = new Codicon('database', {\n  fontCharacter: '\\\\eace'\n});\nCodicon.debugContinue = new Codicon('debug-continue', {\n  fontCharacter: '\\\\eacf'\n});\nCodicon.debugDisconnect = new Codicon('debug-disconnect', {\n  fontCharacter: '\\\\ead0'\n});\nCodicon.debugPause = new Codicon('debug-pause', {\n  fontCharacter: '\\\\ead1'\n});\nCodicon.debugRestart = new Codicon('debug-restart', {\n  fontCharacter: '\\\\ead2'\n});\nCodicon.debugStart = new Codicon('debug-start', {\n  fontCharacter: '\\\\ead3'\n});\nCodicon.debugStepInto = new Codicon('debug-step-into', {\n  fontCharacter: '\\\\ead4'\n});\nCodicon.debugStepOut = new Codicon('debug-step-out', {\n  fontCharacter: '\\\\ead5'\n});\nCodicon.debugStepOver = new Codicon('debug-step-over', {\n  fontCharacter: '\\\\ead6'\n});\nCodicon.debugStop = new Codicon('debug-stop', {\n  fontCharacter: '\\\\ead7'\n});\nCodicon.debug = new Codicon('debug', {\n  fontCharacter: '\\\\ead8'\n});\nCodicon.deviceCameraVideo = new Codicon('device-camera-video', {\n  fontCharacter: '\\\\ead9'\n});\nCodicon.deviceCamera = new Codicon('device-camera', {\n  fontCharacter: '\\\\eada'\n});\nCodicon.deviceMobile = new Codicon('device-mobile', {\n  fontCharacter: '\\\\eadb'\n});\nCodicon.diffAdded = new Codicon('diff-added', {\n  fontCharacter: '\\\\eadc'\n});\nCodicon.diffIgnored = new Codicon('diff-ignored', {\n  fontCharacter: '\\\\eadd'\n});\nCodicon.diffModified = new Codicon('diff-modified', {\n  fontCharacter: '\\\\eade'\n});\nCodicon.diffRemoved = new Codicon('diff-removed', {\n  fontCharacter: '\\\\eadf'\n});\nCodicon.diffRenamed = new Codicon('diff-renamed', {\n  fontCharacter: '\\\\eae0'\n});\nCodicon.diff = new Codicon('diff', {\n  fontCharacter: '\\\\eae1'\n});\nCodicon.discard = new Codicon('discard', {\n  fontCharacter: '\\\\eae2'\n});\nCodicon.editorLayout = new Codicon('editor-layout', {\n  fontCharacter: '\\\\eae3'\n});\nCodicon.emptyWindow = new Codicon('empty-window', {\n  fontCharacter: '\\\\eae4'\n});\nCodicon.exclude = new Codicon('exclude', {\n  fontCharacter: '\\\\eae5'\n});\nCodicon.extensions = new Codicon('extensions', {\n  fontCharacter: '\\\\eae6'\n});\nCodicon.eyeClosed = new Codicon('eye-closed', {\n  fontCharacter: '\\\\eae7'\n});\nCodicon.fileBinary = new Codicon('file-binary', {\n  fontCharacter: '\\\\eae8'\n});\nCodicon.fileCode = new Codicon('file-code', {\n  fontCharacter: '\\\\eae9'\n});\nCodicon.fileMedia = new Codicon('file-media', {\n  fontCharacter: '\\\\eaea'\n});\nCodicon.filePdf = new Codicon('file-pdf', {\n  fontCharacter: '\\\\eaeb'\n});\nCodicon.fileSubmodule = new Codicon('file-submodule', {\n  fontCharacter: '\\\\eaec'\n});\nCodicon.fileSymlinkDirectory = new Codicon('file-symlink-directory', {\n  fontCharacter: '\\\\eaed'\n});\nCodicon.fileSymlinkFile = new Codicon('file-symlink-file', {\n  fontCharacter: '\\\\eaee'\n});\nCodicon.fileZip = new Codicon('file-zip', {\n  fontCharacter: '\\\\eaef'\n});\nCodicon.files = new Codicon('files', {\n  fontCharacter: '\\\\eaf0'\n});\nCodicon.filter = new Codicon('filter', {\n  fontCharacter: '\\\\eaf1'\n});\nCodicon.flame = new Codicon('flame', {\n  fontCharacter: '\\\\eaf2'\n});\nCodicon.foldDown = new Codicon('fold-down', {\n  fontCharacter: '\\\\eaf3'\n});\nCodicon.foldUp = new Codicon('fold-up', {\n  fontCharacter: '\\\\eaf4'\n});\nCodicon.fold = new Codicon('fold', {\n  fontCharacter: '\\\\eaf5'\n});\nCodicon.folderActive = new Codicon('folder-active', {\n  fontCharacter: '\\\\eaf6'\n});\nCodicon.folderOpened = new Codicon('folder-opened', {\n  fontCharacter: '\\\\eaf7'\n});\nCodicon.gear = new Codicon('gear', {\n  fontCharacter: '\\\\eaf8'\n});\nCodicon.gift = new Codicon('gift', {\n  fontCharacter: '\\\\eaf9'\n});\nCodicon.gistSecret = new Codicon('gist-secret', {\n  fontCharacter: '\\\\eafa'\n});\nCodicon.gist = new Codicon('gist', {\n  fontCharacter: '\\\\eafb'\n});\nCodicon.gitCommit = new Codicon('git-commit', {\n  fontCharacter: '\\\\eafc'\n});\nCodicon.gitCompare = new Codicon('git-compare', {\n  fontCharacter: '\\\\eafd'\n});\nCodicon.gitMerge = new Codicon('git-merge', {\n  fontCharacter: '\\\\eafe'\n});\nCodicon.githubAction = new Codicon('github-action', {\n  fontCharacter: '\\\\eaff'\n});\nCodicon.githubAlt = new Codicon('github-alt', {\n  fontCharacter: '\\\\eb00'\n});\nCodicon.globe = new Codicon('globe', {\n  fontCharacter: '\\\\eb01'\n});\nCodicon.grabber = new Codicon('grabber', {\n  fontCharacter: '\\\\eb02'\n});\nCodicon.graph = new Codicon('graph', {\n  fontCharacter: '\\\\eb03'\n});\nCodicon.gripper = new Codicon('gripper', {\n  fontCharacter: '\\\\eb04'\n});\nCodicon.heart = new Codicon('heart', {\n  fontCharacter: '\\\\eb05'\n});\nCodicon.home = new Codicon('home', {\n  fontCharacter: '\\\\eb06'\n});\nCodicon.horizontalRule = new Codicon('horizontal-rule', {\n  fontCharacter: '\\\\eb07'\n});\nCodicon.hubot = new Codicon('hubot', {\n  fontCharacter: '\\\\eb08'\n});\nCodicon.inbox = new Codicon('inbox', {\n  fontCharacter: '\\\\eb09'\n});\nCodicon.issueClosed = new Codicon('issue-closed', {\n  fontCharacter: '\\\\eba4'\n});\nCodicon.issueReopened = new Codicon('issue-reopened', {\n  fontCharacter: '\\\\eb0b'\n});\nCodicon.issues = new Codicon('issues', {\n  fontCharacter: '\\\\eb0c'\n});\nCodicon.italic = new Codicon('italic', {\n  fontCharacter: '\\\\eb0d'\n});\nCodicon.jersey = new Codicon('jersey', {\n  fontCharacter: '\\\\eb0e'\n});\nCodicon.json = new Codicon('json', {\n  fontCharacter: '\\\\eb0f'\n});\nCodicon.kebabVertical = new Codicon('kebab-vertical', {\n  fontCharacter: '\\\\eb10'\n});\nCodicon.key = new Codicon('key', {\n  fontCharacter: '\\\\eb11'\n});\nCodicon.law = new Codicon('law', {\n  fontCharacter: '\\\\eb12'\n});\nCodicon.lightbulbAutofix = new Codicon('lightbulb-autofix', {\n  fontCharacter: '\\\\eb13'\n});\nCodicon.linkExternal = new Codicon('link-external', {\n  fontCharacter: '\\\\eb14'\n});\nCodicon.link = new Codicon('link', {\n  fontCharacter: '\\\\eb15'\n});\nCodicon.listOrdered = new Codicon('list-ordered', {\n  fontCharacter: '\\\\eb16'\n});\nCodicon.listUnordered = new Codicon('list-unordered', {\n  fontCharacter: '\\\\eb17'\n});\nCodicon.liveShare = new Codicon('live-share', {\n  fontCharacter: '\\\\eb18'\n});\nCodicon.loading = new Codicon('loading', {\n  fontCharacter: '\\\\eb19'\n});\nCodicon.location = new Codicon('location', {\n  fontCharacter: '\\\\eb1a'\n});\nCodicon.mailRead = new Codicon('mail-read', {\n  fontCharacter: '\\\\eb1b'\n});\nCodicon.mail = new Codicon('mail', {\n  fontCharacter: '\\\\eb1c'\n});\nCodicon.markdown = new Codicon('markdown', {\n  fontCharacter: '\\\\eb1d'\n});\nCodicon.megaphone = new Codicon('megaphone', {\n  fontCharacter: '\\\\eb1e'\n});\nCodicon.mention = new Codicon('mention', {\n  fontCharacter: '\\\\eb1f'\n});\nCodicon.milestone = new Codicon('milestone', {\n  fontCharacter: '\\\\eb20'\n});\nCodicon.mortarBoard = new Codicon('mortar-board', {\n  fontCharacter: '\\\\eb21'\n});\nCodicon.move = new Codicon('move', {\n  fontCharacter: '\\\\eb22'\n});\nCodicon.multipleWindows = new Codicon('multiple-windows', {\n  fontCharacter: '\\\\eb23'\n});\nCodicon.mute = new Codicon('mute', {\n  fontCharacter: '\\\\eb24'\n});\nCodicon.noNewline = new Codicon('no-newline', {\n  fontCharacter: '\\\\eb25'\n});\nCodicon.note = new Codicon('note', {\n  fontCharacter: '\\\\eb26'\n});\nCodicon.octoface = new Codicon('octoface', {\n  fontCharacter: '\\\\eb27'\n});\nCodicon.openPreview = new Codicon('open-preview', {\n  fontCharacter: '\\\\eb28'\n});\nCodicon.package_ = new Codicon('package', {\n  fontCharacter: '\\\\eb29'\n});\nCodicon.paintcan = new Codicon('paintcan', {\n  fontCharacter: '\\\\eb2a'\n});\nCodicon.pin = new Codicon('pin', {\n  fontCharacter: '\\\\eb2b'\n});\nCodicon.play = new Codicon('play', {\n  fontCharacter: '\\\\eb2c'\n});\nCodicon.run = new Codicon('run', {\n  fontCharacter: '\\\\eb2c'\n});\nCodicon.plug = new Codicon('plug', {\n  fontCharacter: '\\\\eb2d'\n});\nCodicon.preserveCase = new Codicon('preserve-case', {\n  fontCharacter: '\\\\eb2e'\n});\nCodicon.preview = new Codicon('preview', {\n  fontCharacter: '\\\\eb2f'\n});\nCodicon.project = new Codicon('project', {\n  fontCharacter: '\\\\eb30'\n});\nCodicon.pulse = new Codicon('pulse', {\n  fontCharacter: '\\\\eb31'\n});\nCodicon.question = new Codicon('question', {\n  fontCharacter: '\\\\eb32'\n});\nCodicon.quote = new Codicon('quote', {\n  fontCharacter: '\\\\eb33'\n});\nCodicon.radioTower = new Codicon('radio-tower', {\n  fontCharacter: '\\\\eb34'\n});\nCodicon.reactions = new Codicon('reactions', {\n  fontCharacter: '\\\\eb35'\n});\nCodicon.references = new Codicon('references', {\n  fontCharacter: '\\\\eb36'\n});\nCodicon.refresh = new Codicon('refresh', {\n  fontCharacter: '\\\\eb37'\n});\nCodicon.regex = new Codicon('regex', {\n  fontCharacter: '\\\\eb38'\n});\nCodicon.remoteExplorer = new Codicon('remote-explorer', {\n  fontCharacter: '\\\\eb39'\n});\nCodicon.remote = new Codicon('remote', {\n  fontCharacter: '\\\\eb3a'\n});\nCodicon.remove = new Codicon('remove', {\n  fontCharacter: '\\\\eb3b'\n});\nCodicon.replaceAll = new Codicon('replace-all', {\n  fontCharacter: '\\\\eb3c'\n});\nCodicon.replace = new Codicon('replace', {\n  fontCharacter: '\\\\eb3d'\n});\nCodicon.repoClone = new Codicon('repo-clone', {\n  fontCharacter: '\\\\eb3e'\n});\nCodicon.repoForcePush = new Codicon('repo-force-push', {\n  fontCharacter: '\\\\eb3f'\n});\nCodicon.repoPull = new Codicon('repo-pull', {\n  fontCharacter: '\\\\eb40'\n});\nCodicon.repoPush = new Codicon('repo-push', {\n  fontCharacter: '\\\\eb41'\n});\nCodicon.report = new Codicon('report', {\n  fontCharacter: '\\\\eb42'\n});\nCodicon.requestChanges = new Codicon('request-changes', {\n  fontCharacter: '\\\\eb43'\n});\nCodicon.rocket = new Codicon('rocket', {\n  fontCharacter: '\\\\eb44'\n});\nCodicon.rootFolderOpened = new Codicon('root-folder-opened', {\n  fontCharacter: '\\\\eb45'\n});\nCodicon.rootFolder = new Codicon('root-folder', {\n  fontCharacter: '\\\\eb46'\n});\nCodicon.rss = new Codicon('rss', {\n  fontCharacter: '\\\\eb47'\n});\nCodicon.ruby = new Codicon('ruby', {\n  fontCharacter: '\\\\eb48'\n});\nCodicon.saveAll = new Codicon('save-all', {\n  fontCharacter: '\\\\eb49'\n});\nCodicon.saveAs = new Codicon('save-as', {\n  fontCharacter: '\\\\eb4a'\n});\nCodicon.save = new Codicon('save', {\n  fontCharacter: '\\\\eb4b'\n});\nCodicon.screenFull = new Codicon('screen-full', {\n  fontCharacter: '\\\\eb4c'\n});\nCodicon.screenNormal = new Codicon('screen-normal', {\n  fontCharacter: '\\\\eb4d'\n});\nCodicon.searchStop = new Codicon('search-stop', {\n  fontCharacter: '\\\\eb4e'\n});\nCodicon.server = new Codicon('server', {\n  fontCharacter: '\\\\eb50'\n});\nCodicon.settingsGear = new Codicon('settings-gear', {\n  fontCharacter: '\\\\eb51'\n});\nCodicon.settings = new Codicon('settings', {\n  fontCharacter: '\\\\eb52'\n});\nCodicon.shield = new Codicon('shield', {\n  fontCharacter: '\\\\eb53'\n});\nCodicon.smiley = new Codicon('smiley', {\n  fontCharacter: '\\\\eb54'\n});\nCodicon.sortPrecedence = new Codicon('sort-precedence', {\n  fontCharacter: '\\\\eb55'\n});\nCodicon.splitHorizontal = new Codicon('split-horizontal', {\n  fontCharacter: '\\\\eb56'\n});\nCodicon.splitVertical = new Codicon('split-vertical', {\n  fontCharacter: '\\\\eb57'\n});\nCodicon.squirrel = new Codicon('squirrel', {\n  fontCharacter: '\\\\eb58'\n});\nCodicon.starFull = new Codicon('star-full', {\n  fontCharacter: '\\\\eb59'\n});\nCodicon.starHalf = new Codicon('star-half', {\n  fontCharacter: '\\\\eb5a'\n});\nCodicon.symbolClass = new Codicon('symbol-class', {\n  fontCharacter: '\\\\eb5b'\n});\nCodicon.symbolColor = new Codicon('symbol-color', {\n  fontCharacter: '\\\\eb5c'\n});\nCodicon.symbolCustomColor = new Codicon('symbol-customcolor', {\n  fontCharacter: '\\\\eb5c'\n});\nCodicon.symbolConstant = new Codicon('symbol-constant', {\n  fontCharacter: '\\\\eb5d'\n});\nCodicon.symbolEnumMember = new Codicon('symbol-enum-member', {\n  fontCharacter: '\\\\eb5e'\n});\nCodicon.symbolField = new Codicon('symbol-field', {\n  fontCharacter: '\\\\eb5f'\n});\nCodicon.symbolFile = new Codicon('symbol-file', {\n  fontCharacter: '\\\\eb60'\n});\nCodicon.symbolInterface = new Codicon('symbol-interface', {\n  fontCharacter: '\\\\eb61'\n});\nCodicon.symbolKeyword = new Codicon('symbol-keyword', {\n  fontCharacter: '\\\\eb62'\n});\nCodicon.symbolMisc = new Codicon('symbol-misc', {\n  fontCharacter: '\\\\eb63'\n});\nCodicon.symbolOperator = new Codicon('symbol-operator', {\n  fontCharacter: '\\\\eb64'\n});\nCodicon.symbolProperty = new Codicon('symbol-property', {\n  fontCharacter: '\\\\eb65'\n});\nCodicon.wrench = new Codicon('wrench', {\n  fontCharacter: '\\\\eb65'\n});\nCodicon.wrenchSubaction = new Codicon('wrench-subaction', {\n  fontCharacter: '\\\\eb65'\n});\nCodicon.symbolSnippet = new Codicon('symbol-snippet', {\n  fontCharacter: '\\\\eb66'\n});\nCodicon.tasklist = new Codicon('tasklist', {\n  fontCharacter: '\\\\eb67'\n});\nCodicon.telescope = new Codicon('telescope', {\n  fontCharacter: '\\\\eb68'\n});\nCodicon.textSize = new Codicon('text-size', {\n  fontCharacter: '\\\\eb69'\n});\nCodicon.threeBars = new Codicon('three-bars', {\n  fontCharacter: '\\\\eb6a'\n});\nCodicon.thumbsdown = new Codicon('thumbsdown', {\n  fontCharacter: '\\\\eb6b'\n});\nCodicon.thumbsup = new Codicon('thumbsup', {\n  fontCharacter: '\\\\eb6c'\n});\nCodicon.tools = new Codicon('tools', {\n  fontCharacter: '\\\\eb6d'\n});\nCodicon.triangleDown = new Codicon('triangle-down', {\n  fontCharacter: '\\\\eb6e'\n});\nCodicon.triangleLeft = new Codicon('triangle-left', {\n  fontCharacter: '\\\\eb6f'\n});\nCodicon.triangleRight = new Codicon('triangle-right', {\n  fontCharacter: '\\\\eb70'\n});\nCodicon.triangleUp = new Codicon('triangle-up', {\n  fontCharacter: '\\\\eb71'\n});\nCodicon.twitter = new Codicon('twitter', {\n  fontCharacter: '\\\\eb72'\n});\nCodicon.unfold = new Codicon('unfold', {\n  fontCharacter: '\\\\eb73'\n});\nCodicon.unlock = new Codicon('unlock', {\n  fontCharacter: '\\\\eb74'\n});\nCodicon.unmute = new Codicon('unmute', {\n  fontCharacter: '\\\\eb75'\n});\nCodicon.unverified = new Codicon('unverified', {\n  fontCharacter: '\\\\eb76'\n});\nCodicon.verified = new Codicon('verified', {\n  fontCharacter: '\\\\eb77'\n});\nCodicon.versions = new Codicon('versions', {\n  fontCharacter: '\\\\eb78'\n});\nCodicon.vmActive = new Codicon('vm-active', {\n  fontCharacter: '\\\\eb79'\n});\nCodicon.vmOutline = new Codicon('vm-outline', {\n  fontCharacter: '\\\\eb7a'\n});\nCodicon.vmRunning = new Codicon('vm-running', {\n  fontCharacter: '\\\\eb7b'\n});\nCodicon.watch = new Codicon('watch', {\n  fontCharacter: '\\\\eb7c'\n});\nCodicon.whitespace = new Codicon('whitespace', {\n  fontCharacter: '\\\\eb7d'\n});\nCodicon.wholeWord = new Codicon('whole-word', {\n  fontCharacter: '\\\\eb7e'\n});\nCodicon.window = new Codicon('window', {\n  fontCharacter: '\\\\eb7f'\n});\nCodicon.wordWrap = new Codicon('word-wrap', {\n  fontCharacter: '\\\\eb80'\n});\nCodicon.zoomIn = new Codicon('zoom-in', {\n  fontCharacter: '\\\\eb81'\n});\nCodicon.zoomOut = new Codicon('zoom-out', {\n  fontCharacter: '\\\\eb82'\n});\nCodicon.listFilter = new Codicon('list-filter', {\n  fontCharacter: '\\\\eb83'\n});\nCodicon.listFlat = new Codicon('list-flat', {\n  fontCharacter: '\\\\eb84'\n});\nCodicon.listSelection = new Codicon('list-selection', {\n  fontCharacter: '\\\\eb85'\n});\nCodicon.selection = new Codicon('selection', {\n  fontCharacter: '\\\\eb85'\n});\nCodicon.listTree = new Codicon('list-tree', {\n  fontCharacter: '\\\\eb86'\n});\nCodicon.debugBreakpointFunctionUnverified = new Codicon('debug-breakpoint-function-unverified', {\n  fontCharacter: '\\\\eb87'\n});\nCodicon.debugBreakpointFunction = new Codicon('debug-breakpoint-function', {\n  fontCharacter: '\\\\eb88'\n});\nCodicon.debugBreakpointFunctionDisabled = new Codicon('debug-breakpoint-function-disabled', {\n  fontCharacter: '\\\\eb88'\n});\nCodicon.debugStackframeActive = new Codicon('debug-stackframe-active', {\n  fontCharacter: '\\\\eb89'\n});\nCodicon.circleSmallFilled = new Codicon('circle-small-filled', {\n  fontCharacter: '\\\\eb8a'\n});\nCodicon.debugStackframeDot = new Codicon('debug-stackframe-dot', Codicon.circleSmallFilled.definition);\nCodicon.debugStackframe = new Codicon('debug-stackframe', {\n  fontCharacter: '\\\\eb8b'\n});\nCodicon.debugStackframeFocused = new Codicon('debug-stackframe-focused', {\n  fontCharacter: '\\\\eb8b'\n});\nCodicon.debugBreakpointUnsupported = new Codicon('debug-breakpoint-unsupported', {\n  fontCharacter: '\\\\eb8c'\n});\nCodicon.symbolString = new Codicon('symbol-string', {\n  fontCharacter: '\\\\eb8d'\n});\nCodicon.debugReverseContinue = new Codicon('debug-reverse-continue', {\n  fontCharacter: '\\\\eb8e'\n});\nCodicon.debugStepBack = new Codicon('debug-step-back', {\n  fontCharacter: '\\\\eb8f'\n});\nCodicon.debugRestartFrame = new Codicon('debug-restart-frame', {\n  fontCharacter: '\\\\eb90'\n});\nCodicon.callIncoming = new Codicon('call-incoming', {\n  fontCharacter: '\\\\eb92'\n});\nCodicon.callOutgoing = new Codicon('call-outgoing', {\n  fontCharacter: '\\\\eb93'\n});\nCodicon.menu = new Codicon('menu', {\n  fontCharacter: '\\\\eb94'\n});\nCodicon.expandAll = new Codicon('expand-all', {\n  fontCharacter: '\\\\eb95'\n});\nCodicon.feedback = new Codicon('feedback', {\n  fontCharacter: '\\\\eb96'\n});\nCodicon.groupByRefType = new Codicon('group-by-ref-type', {\n  fontCharacter: '\\\\eb97'\n});\nCodicon.ungroupByRefType = new Codicon('ungroup-by-ref-type', {\n  fontCharacter: '\\\\eb98'\n});\nCodicon.account = new Codicon('account', {\n  fontCharacter: '\\\\eb99'\n});\nCodicon.bellDot = new Codicon('bell-dot', {\n  fontCharacter: '\\\\eb9a'\n});\nCodicon.debugConsole = new Codicon('debug-console', {\n  fontCharacter: '\\\\eb9b'\n});\nCodicon.library = new Codicon('library', {\n  fontCharacter: '\\\\eb9c'\n});\nCodicon.output = new Codicon('output', {\n  fontCharacter: '\\\\eb9d'\n});\nCodicon.runAll = new Codicon('run-all', {\n  fontCharacter: '\\\\eb9e'\n});\nCodicon.syncIgnored = new Codicon('sync-ignored', {\n  fontCharacter: '\\\\eb9f'\n});\nCodicon.pinned = new Codicon('pinned', {\n  fontCharacter: '\\\\eba0'\n});\nCodicon.githubInverted = new Codicon('github-inverted', {\n  fontCharacter: '\\\\eba1'\n});\nCodicon.debugAlt = new Codicon('debug-alt', {\n  fontCharacter: '\\\\eb91'\n});\nCodicon.serverProcess = new Codicon('server-process', {\n  fontCharacter: '\\\\eba2'\n});\nCodicon.serverEnvironment = new Codicon('server-environment', {\n  fontCharacter: '\\\\eba3'\n});\nCodicon.pass = new Codicon('pass', {\n  fontCharacter: '\\\\eba4'\n});\nCodicon.stopCircle = new Codicon('stop-circle', {\n  fontCharacter: '\\\\eba5'\n});\nCodicon.playCircle = new Codicon('play-circle', {\n  fontCharacter: '\\\\eba6'\n});\nCodicon.record = new Codicon('record', {\n  fontCharacter: '\\\\eba7'\n});\nCodicon.debugAltSmall = new Codicon('debug-alt-small', {\n  fontCharacter: '\\\\eba8'\n});\nCodicon.vmConnect = new Codicon('vm-connect', {\n  fontCharacter: '\\\\eba9'\n});\nCodicon.cloud = new Codicon('cloud', {\n  fontCharacter: '\\\\ebaa'\n});\nCodicon.merge = new Codicon('merge', {\n  fontCharacter: '\\\\ebab'\n});\nCodicon.exportIcon = new Codicon('export', {\n  fontCharacter: '\\\\ebac'\n});\nCodicon.graphLeft = new Codicon('graph-left', {\n  fontCharacter: '\\\\ebad'\n});\nCodicon.magnet = new Codicon('magnet', {\n  fontCharacter: '\\\\ebae'\n});\nCodicon.notebook = new Codicon('notebook', {\n  fontCharacter: '\\\\ebaf'\n});\nCodicon.redo = new Codicon('redo', {\n  fontCharacter: '\\\\ebb0'\n});\nCodicon.checkAll = new Codicon('check-all', {\n  fontCharacter: '\\\\ebb1'\n});\nCodicon.pinnedDirty = new Codicon('pinned-dirty', {\n  fontCharacter: '\\\\ebb2'\n});\nCodicon.passFilled = new Codicon('pass-filled', {\n  fontCharacter: '\\\\ebb3'\n});\nCodicon.circleLargeFilled = new Codicon('circle-large-filled', {\n  fontCharacter: '\\\\ebb4'\n});\nCodicon.circleLargeOutline = new Codicon('circle-large-outline', {\n  fontCharacter: '\\\\ebb5'\n});\nCodicon.combine = new Codicon('combine', {\n  fontCharacter: '\\\\ebb6'\n});\nCodicon.gather = new Codicon('gather', {\n  fontCharacter: '\\\\ebb6'\n});\nCodicon.table = new Codicon('table', {\n  fontCharacter: '\\\\ebb7'\n});\nCodicon.variableGroup = new Codicon('variable-group', {\n  fontCharacter: '\\\\ebb8'\n});\nCodicon.typeHierarchy = new Codicon('type-hierarchy', {\n  fontCharacter: '\\\\ebb9'\n});\nCodicon.typeHierarchySub = new Codicon('type-hierarchy-sub', {\n  fontCharacter: '\\\\ebba'\n});\nCodicon.typeHierarchySuper = new Codicon('type-hierarchy-super', {\n  fontCharacter: '\\\\ebbb'\n});\nCodicon.gitPullRequestCreate = new Codicon('git-pull-request-create', {\n  fontCharacter: '\\\\ebbc'\n});\nCodicon.runAbove = new Codicon('run-above', {\n  fontCharacter: '\\\\ebbd'\n});\nCodicon.runBelow = new Codicon('run-below', {\n  fontCharacter: '\\\\ebbe'\n});\nCodicon.notebookTemplate = new Codicon('notebook-template', {\n  fontCharacter: '\\\\ebbf'\n});\nCodicon.debugRerun = new Codicon('debug-rerun', {\n  fontCharacter: '\\\\ebc0'\n});\nCodicon.workspaceTrusted = new Codicon('workspace-trusted', {\n  fontCharacter: '\\\\ebc1'\n});\nCodicon.workspaceUntrusted = new Codicon('workspace-untrusted', {\n  fontCharacter: '\\\\ebc2'\n});\nCodicon.workspaceUnspecified = new Codicon('workspace-unspecified', {\n  fontCharacter: '\\\\ebc3'\n});\nCodicon.terminalCmd = new Codicon('terminal-cmd', {\n  fontCharacter: '\\\\ebc4'\n});\nCodicon.terminalDebian = new Codicon('terminal-debian', {\n  fontCharacter: '\\\\ebc5'\n});\nCodicon.terminalLinux = new Codicon('terminal-linux', {\n  fontCharacter: '\\\\ebc6'\n});\nCodicon.terminalPowershell = new Codicon('terminal-powershell', {\n  fontCharacter: '\\\\ebc7'\n});\nCodicon.terminalTmux = new Codicon('terminal-tmux', {\n  fontCharacter: '\\\\ebc8'\n});\nCodicon.terminalUbuntu = new Codicon('terminal-ubuntu', {\n  fontCharacter: '\\\\ebc9'\n});\nCodicon.terminalBash = new Codicon('terminal-bash', {\n  fontCharacter: '\\\\ebca'\n});\nCodicon.arrowSwap = new Codicon('arrow-swap', {\n  fontCharacter: '\\\\ebcb'\n});\nCodicon.copy = new Codicon('copy', {\n  fontCharacter: '\\\\ebcc'\n});\nCodicon.personAdd = new Codicon('person-add', {\n  fontCharacter: '\\\\ebcd'\n});\nCodicon.filterFilled = new Codicon('filter-filled', {\n  fontCharacter: '\\\\ebce'\n});\nCodicon.wand = new Codicon('wand', {\n  fontCharacter: '\\\\ebcf'\n});\nCodicon.debugLineByLine = new Codicon('debug-line-by-line', {\n  fontCharacter: '\\\\ebd0'\n});\nCodicon.inspect = new Codicon('inspect', {\n  fontCharacter: '\\\\ebd1'\n});\nCodicon.layers = new Codicon('layers', {\n  fontCharacter: '\\\\ebd2'\n});\nCodicon.layersDot = new Codicon('layers-dot', {\n  fontCharacter: '\\\\ebd3'\n});\nCodicon.layersActive = new Codicon('layers-active', {\n  fontCharacter: '\\\\ebd4'\n});\nCodicon.compass = new Codicon('compass', {\n  fontCharacter: '\\\\ebd5'\n});\nCodicon.compassDot = new Codicon('compass-dot', {\n  fontCharacter: '\\\\ebd6'\n});\nCodicon.compassActive = new Codicon('compass-active', {\n  fontCharacter: '\\\\ebd7'\n});\nCodicon.azure = new Codicon('azure', {\n  fontCharacter: '\\\\ebd8'\n});\nCodicon.issueDraft = new Codicon('issue-draft', {\n  fontCharacter: '\\\\ebd9'\n});\nCodicon.gitPullRequestClosed = new Codicon('git-pull-request-closed', {\n  fontCharacter: '\\\\ebda'\n});\nCodicon.gitPullRequestDraft = new Codicon('git-pull-request-draft', {\n  fontCharacter: '\\\\ebdb'\n});\nCodicon.debugAll = new Codicon('debug-all', {\n  fontCharacter: '\\\\ebdc'\n});\nCodicon.debugCoverage = new Codicon('debug-coverage', {\n  fontCharacter: '\\\\ebdd'\n});\nCodicon.runErrors = new Codicon('run-errors', {\n  fontCharacter: '\\\\ebde'\n});\nCodicon.folderLibrary = new Codicon('folder-library', {\n  fontCharacter: '\\\\ebdf'\n});\nCodicon.debugContinueSmall = new Codicon('debug-continue-small', {\n  fontCharacter: '\\\\ebe0'\n});\nCodicon.beakerStop = new Codicon('beaker-stop', {\n  fontCharacter: '\\\\ebe1'\n});\nCodicon.graphLine = new Codicon('graph-line', {\n  fontCharacter: '\\\\ebe2'\n});\nCodicon.graphScatter = new Codicon('graph-scatter', {\n  fontCharacter: '\\\\ebe3'\n});\nCodicon.pieChart = new Codicon('pie-chart', {\n  fontCharacter: '\\\\ebe4'\n});\nCodicon.bracket = new Codicon('bracket', Codicon.json.definition);\nCodicon.bracketDot = new Codicon('bracket-dot', {\n  fontCharacter: '\\\\ebe5'\n});\nCodicon.bracketError = new Codicon('bracket-error', {\n  fontCharacter: '\\\\ebe6'\n});\nCodicon.lockSmall = new Codicon('lock-small', {\n  fontCharacter: '\\\\ebe7'\n});\nCodicon.azureDevops = new Codicon('azure-devops', {\n  fontCharacter: '\\\\ebe8'\n});\nCodicon.verifiedFilled = new Codicon('verified-filled', {\n  fontCharacter: '\\\\ebe9'\n});\nCodicon.newLine = new Codicon('newline', {\n  fontCharacter: '\\\\ebea'\n});\nCodicon.layout = new Codicon('layout', {\n  fontCharacter: '\\\\ebeb'\n});\nCodicon.layoutActivitybarLeft = new Codicon('layout-activitybar-left', {\n  fontCharacter: '\\\\ebec'\n});\nCodicon.layoutActivitybarRight = new Codicon('layout-activitybar-right', {\n  fontCharacter: '\\\\ebed'\n});\nCodicon.layoutPanelLeft = new Codicon('layout-panel-left', {\n  fontCharacter: '\\\\ebee'\n});\nCodicon.layoutPanelCenter = new Codicon('layout-panel-center', {\n  fontCharacter: '\\\\ebef'\n});\nCodicon.layoutPanelJustify = new Codicon('layout-panel-justify', {\n  fontCharacter: '\\\\ebf0'\n});\nCodicon.layoutPanelRight = new Codicon('layout-panel-right', {\n  fontCharacter: '\\\\ebf1'\n});\nCodicon.layoutPanel = new Codicon('layout-panel', {\n  fontCharacter: '\\\\ebf2'\n});\nCodicon.layoutSidebarLeft = new Codicon('layout-sidebar-left', {\n  fontCharacter: '\\\\ebf3'\n});\nCodicon.layoutSidebarRight = new Codicon('layout-sidebar-right', {\n  fontCharacter: '\\\\ebf4'\n});\nCodicon.layoutStatusbar = new Codicon('layout-statusbar', {\n  fontCharacter: '\\\\ebf5'\n});\nCodicon.layoutMenubar = new Codicon('layout-menubar', {\n  fontCharacter: '\\\\ebf6'\n});\nCodicon.layoutCentered = new Codicon('layout-centered', {\n  fontCharacter: '\\\\ebf7'\n});\nCodicon.layoutSidebarRightOff = new Codicon('layout-sidebar-right-off', {\n  fontCharacter: '\\\\ec00'\n});\nCodicon.layoutPanelOff = new Codicon('layout-panel-off', {\n  fontCharacter: '\\\\ec01'\n});\nCodicon.layoutSidebarLeftOff = new Codicon('layout-sidebar-left-off', {\n  fontCharacter: '\\\\ec02'\n});\nCodicon.target = new Codicon('target', {\n  fontCharacter: '\\\\ebf8'\n});\nCodicon.indent = new Codicon('indent', {\n  fontCharacter: '\\\\ebf9'\n});\nCodicon.recordSmall = new Codicon('record-small', {\n  fontCharacter: '\\\\ebfa'\n});\nCodicon.errorSmall = new Codicon('error-small', {\n  fontCharacter: '\\\\ebfb'\n});\nCodicon.arrowCircleDown = new Codicon('arrow-circle-down', {\n  fontCharacter: '\\\\ebfc'\n});\nCodicon.arrowCircleLeft = new Codicon('arrow-circle-left', {\n  fontCharacter: '\\\\ebfd'\n});\nCodicon.arrowCircleRight = new Codicon('arrow-circle-right', {\n  fontCharacter: '\\\\ebfe'\n});\nCodicon.arrowCircleUp = new Codicon('arrow-circle-up', {\n  fontCharacter: '\\\\ebff'\n});\nCodicon.heartFilled = new Codicon('heart-filled', {\n  fontCharacter: '\\\\ec04'\n});\nCodicon.map = new Codicon('map', {\n  fontCharacter: '\\\\ec05'\n});\nCodicon.mapFilled = new Codicon('map-filled', {\n  fontCharacter: '\\\\ec06'\n});\nCodicon.circleSmall = new Codicon('circle-small', {\n  fontCharacter: '\\\\ec07'\n});\nCodicon.bellSlash = new Codicon('bell-slash', {\n  fontCharacter: '\\\\ec08'\n});\nCodicon.bellSlashDot = new Codicon('bell-slash-dot', {\n  fontCharacter: '\\\\ec09'\n});\nCodicon.commentUnresolved = new Codicon('comment-unresolved', {\n  fontCharacter: '\\\\ec0a'\n});\nCodicon.gitPullRequestGoToChanges = new Codicon('git-pull-request-go-to-changes', {\n  fontCharacter: '\\\\ec0b'\n});\nCodicon.gitPullRequestNewChanges = new Codicon('git-pull-request-new-changes', {\n  fontCharacter: '\\\\ec0c'\n});\n// derived icons, that could become separate icons\nCodicon.dialogError = new Codicon('dialog-error', Codicon.error.definition);\nCodicon.dialogWarning = new Codicon('dialog-warning', Codicon.warning.definition);\nCodicon.dialogInfo = new Codicon('dialog-info', Codicon.info.definition);\nCodicon.dialogClose = new Codicon('dialog-close', Codicon.close.definition);\nCodicon.treeItemExpanded = new Codicon('tree-item-expanded', Codicon.chevronDown.definition); // collapsed is done with rotation\nCodicon.treeFilterOnTypeOn = new Codicon('tree-filter-on-type-on', Codicon.listFilter.definition);\nCodicon.treeFilterOnTypeOff = new Codicon('tree-filter-on-type-off', Codicon.listSelection.definition);\nCodicon.treeFilterClear = new Codicon('tree-filter-clear', Codicon.close.definition);\nCodicon.treeItemLoading = new Codicon('tree-item-loading', Codicon.loading.definition);\nCodicon.menuSelection = new Codicon('menu-selection', Codicon.check.definition);\nCodicon.menuSubmenu = new Codicon('menu-submenu', Codicon.chevronRight.definition);\nCodicon.menuBarMore = new Codicon('menubar-more', Codicon.more.definition);\nCodicon.scrollbarButtonLeft = new Codicon('scrollbar-button-left', Codicon.triangleLeft.definition);\nCodicon.scrollbarButtonRight = new Codicon('scrollbar-button-right', Codicon.triangleRight.definition);\nCodicon.scrollbarButtonUp = new Codicon('scrollbar-button-up', Codicon.triangleUp.definition);\nCodicon.scrollbarButtonDown = new Codicon('scrollbar-button-down', Codicon.triangleDown.definition);\nCodicon.toolBarMore = new Codicon('toolbar-more', Codicon.more.definition);\nCodicon.quickInputBack = new Codicon('quick-input-back', Codicon.arrowLeft.definition);\nexport var CSSIcon;\n(function (CSSIcon) {\n  CSSIcon.iconNameSegment = '[A-Za-z0-9]+';\n  CSSIcon.iconNameExpression = '[A-Za-z0-9-]+';\n  CSSIcon.iconModifierExpression = '~[A-Za-z]+';\n  CSSIcon.iconNameCharacter = '[A-Za-z0-9~-]';\n  const cssIconIdRegex = new RegExp(`^(${CSSIcon.iconNameExpression})(${CSSIcon.iconModifierExpression})?$`);\n  function asClassNameArray(icon) {\n    if (icon instanceof Codicon) {\n      return ['codicon', 'codicon-' + icon.id];\n    }\n    const match = cssIconIdRegex.exec(icon.id);\n    if (!match) {\n      return asClassNameArray(Codicon.error);\n    }\n    const [, id, modifier] = match;\n    const classNames = ['codicon', 'codicon-' + id];\n    if (modifier) {\n      classNames.push('codicon-modifier-' + modifier.substr(1));\n    }\n    return classNames;\n  }\n  CSSIcon.asClassNameArray = asClassNameArray;\n  function asClassName(icon) {\n    return asClassNameArray(icon).join(' ');\n  }\n  CSSIcon.asClassName = asClassName;\n  function asCSSSelector(icon) {\n    return '.' + asClassNameArray(icon).join('.');\n  }\n  CSSIcon.asCSSSelector = asCSSSelector;\n})(CSSIcon || (CSSIcon = {}));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}