import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import TEditor from "@common/components/TEditor/TEditor";
import { useDebounce } from "@common/hook/index";
import { useQuerySetting407_getCodeValueList, useQueryTeam571_GetSpaceVaildUserList } from "@common/service/commonHooks";
import { compareObj, getAttrPropByType, getPropValueByIdType } from "@common/utils/ArrayUtils";
import {
  eConsoleNodeId,
  eConsoleNodeType,
  eConsolePropId,
  eConsoleUiControl,
  eDebounceTime,
  eEnableFlg,
  eFileObjId,
  eObjRelType
} from "@common/utils/enum";
import { globalUtil } from "@common/utils/globalUtil";
import { eNodeType, eNodeTypeId } from "@common/utils/TsbConfig";
import AppNodeResourceIcon from "@components/AppNodeResourceIcon";
import DefaultAvatar from "@components/DefaultAvatar";
import ObjRelDrawer from "@components/ObjRelDrawer";
import UploadLoading from "@components/UploadLoading";
import { Button, DatePicker, Form, Input, InputNumber, Modal, Select, Table, Upload } from "antd";
import { nanoid } from "nanoid";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { unstable_usePrompt, useBeforeUnload, useParams } from "react-router-dom";
import { useMutationTrack006CreateIssue, useQuerySetting409_getTeamAttrgrpProps } from "src/issueTrack/service/issueHooks";
import { formatSvg } from "src/issueTrack/utils/ArrayUtils";
import "./../IssueDetail/IssueDetail.scss";

// 新建issue选择人员更改为当前协作群有效人员 Walt 2023-03-27
function CreateIssueContent({
  nodeItem,
  largeView,
  createAnotherIssueFlg,
  setOpen,
  uploadLoading,
  setUploadLoading,
  drawerWidth,
}, ref) {
  
  let editRef = useRef(null);
  const [fromRef] = Form.useForm();
  const issueSaveRef = useRef();
  const { teamId } = useParams();
  const [formId, setFormId] = useState(nanoid());
  const [attrList, setAttrList] = useState([]);
  // 默认初始数据
  const [objList, setObjList] = useState([]); //附件列表
  const [objRelList, setObjRelList] = useState([]); //对象关联列表
  const [objRelModalVisibleFlg, setObjRelModalVisibleFlg] = useState(false); //是否显示对象关联对话框
  const [objRelModalContentChangedFlg, setObjRelModalContentChangeFlg] = useState(true); //对象关联弹框是否有内容变更
  const [initialValues, setInitialValues] = useState({})
  // projectInfo：项目信息，用于显示别名
  const { nodeId:issueListNodeId, projectInfo, callback } = nodeItem;
  const { subclassAttrList=[], isLoading: isLoadingGetSubclassAttrs, dataUpdatedAt: dataUpdatedAtSetting409 }
    = useQuerySetting409_getTeamAttrgrpProps(teamId, issueListNodeId, "", !!issueListNodeId);

  const [titleObj, setTitleObj] = useState({name: '', title: ''});
  const isLoading = isLoadingGetSubclassAttrs;
  const { data: selectionList,  } = useQuerySetting407_getCodeValueList(teamId); //字典数据
  const { data: spaceUserList,  } = useQueryTeam571_GetSpaceVaildUserList(teamId, issueListNodeId, eEnableFlg.disable, !!issueListNodeId); //字典数据

  const { onIssue012CreateIssue } = useMutationTrack006CreateIssue(teamId, issueListNodeId, 1)
  const _load_issue012_create_issue_debounce = useDebounce(_load_issue012_create_issue, 500);

  useImperativeHandle(ref, () => ({
    onOk: handleSubmitButtonClick,
    onCancel: handleCloseCreateIssueModal,
  }));

  const {  userInfo,  } = useSelector((state) => ({
    userInfo: state.getIn(["login", "loginInfo", "userInfo"]),
  }), shallowEqual);

  // 是否过滤不可修改项且没有默认值
  function isShowModifiable(attr) {
    const modifiable = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_60_modifiable)?.propValue;
    const defaultValue = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_5_default_value)?.propValue;
    // 可修改的属性，或者不可修改但有默认值的属性
    return (modifiable || 1) == 1 || (modifiable == "0" && defaultValue != '');
  }

  // 判断是否使用两列布局
  const shouldUseTwoColumns = drawerWidth >= 800; // 当drawer宽度大于等于800px时使用两列布局

  // 渲染单个字段
  const renderField = (attr, index) => {
    const isFullWidth = attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title;
    
    return (
      <Form.Item name={attr.nodeId}
        style={{
          marginBottom: 15,
          // 确保label右对齐
          '.ant-form-item-label': {
            textAlign: 'right',
            paddingRight: '8px'
          }
        }}
        key={index}
        label={[<span key={index}>{attr.nodeName}</span>]}
        rules={[
          {
            required: attr.requiredFlg == 1,
            message: "请填写" + attr.nodeName
          }
        ]}
        // 使用Form的全局设置
      >
              {
          attr.uiControl == eConsoleUiControl.TextBox ?
            showTextBoxUI(attr, index)
            :
            attr.uiControl == eConsoleUiControl.MultiTextBox ?
            <Input.TextArea className="tms-multi-textbox" />:
            attr.uiControl == eConsoleUiControl.RichTextBox ?
              <div style={{ width: '100%', paddingRight: '16px' }}>
                <TEditor ref={editRef}
                  placeholderText=""
                  contentChanged={(e) => editContentChange(index, attr, e)}
                  uploadParams={{
                    teamId,
                    nodeId: issueListNodeId,
                    moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]?.nameEn,
                    objType: eNodeTypeId.nt_31704_objtype_issue_item
                  }}
                  uploadCallback={uploadFileCallBack}/>
              </div> :
            attr.uiControl == eConsoleUiControl.ListBox ?
            attr.selectionLid == "-701" ?
                <Select
                  allowClear
                  showSearch
                  style={{ 
                    width: isFullWidth ? '100%' : 300, 
                    borderRadius: 3 
                  }}
                  //select选择框搜索
                  disabled={modifyFlg(attr)}
                  filterOption={(input, option) => {
                    return (option.children[1].props.children || "").toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }}
                >
                  {(spaceUserList || []).map((_user, index) => {
                    return <Select.Option 
                      key={index} 
                      value={_user.userId?.toString()} 
                      style={(_user.deleteFlg === 1 || _user.enableFlg === 0)?{color: "#c1c1c1"}:{}}>
                      <DefaultAvatar avatarSrc={_user.avatar} />
                      <span className={`member-select-${_user.deleteFlg > 0 ? 'delete' : _user.enableFlg == 0 ? 'enable' : 'normal'}`}
                            style={{ marginLeft: 5 }}>{_user.userName}</span>
                    </Select.Option>
                  })}
                </Select>
                :
                <Select disabled={modifyFlg(attr)}
                  allowClear
                  showSearch
                  style={{ 
                    width: isFullWidth ? '100%' : 300, 
                    borderRadius: 3 
                  }}
                  filterOption={(input, option) => {
                      // tmsbug-6469: issue在"模块"下拉时，使用键盘进行模糊匹配时崩溃  注意:option结构需保持一致
                      return (option.children.props.children[1].props.children || "").toLowerCase().indexOf(input.toLowerCase()) >= 0
                   }}
                >
                  {(selectionList || []).filter(item => item.selectionId == attr.selectionLid).map((item, index) => {
                      return <Select.Option key={index} value={item.propType} >
                        <div style={{ display: "flex", alignItems: "center" }}>
                         { attr.isSysUser ?  <div style={{ paddingRight: 5 }}><DefaultAvatar avatarSrc={item.iconValue} /></div> :
                            item.iconValue ?  <div style={{ paddingRight: 5 }}>{formatSvg(item.iconValue)}</div> :
                             <div></div>
                          }
                          <span>{item.propValue}</span>
                        </div>
                      </Select.Option>
                  })}
                </Select> :
              // 时间选择
              attr.uiControl == eConsoleUiControl.Date ?
                showDateUI(attr, index) : <></>
        }
      </Form.Item>
    );
  };

  // 生成两列布局，保持字段顺序，富文本框跨越两列宽度
  const generateTwoColumnLayout = () => {
    const leftFields = [];
    const rightFields = [];
    const result = [];
    
    attrList.forEach((attr, index) => {
      // 标题字段单独占一行，但放在左侧容器中
      if (attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title) {
        result.push(
          <div key={`title-${index}`} style={{
            display: 'flex',
            gap: '16px'
          }}>
            <div style={{
              flex: '1 1 calc(50% - 8px)',
              minWidth: '300px'
            }}>
              {renderField(attr, index)}
            </div>
            <div style={{
              flex: '1 1 calc(50% - 8px)',
              minWidth: '300px'
            }}>
              {/* 空的右列 */}
            </div>
          </div>
        );
      } else if (attr.uiControl === eConsoleUiControl.RichTextBox) {
        // 富文本框使用与两列布局相同的Form.Item结构，确保label和内容区对齐
        result.push(
          <div key={`rich-text-${index}`} style={{ display: 'flex', width: '100%', marginBottom: 15 }}>
            <div style={{ flex: '1 1 calc(50% - 8px)', minWidth: '0' }}>
              <Form.Item
                name={attr.nodeId}
                style={{ marginBottom: 0 }}
                label={<span>{attr.nodeName}</span>}
                rules={[
                  {
                    required: attr.requiredFlg == 1,
                    message: "请填写" + attr.nodeName
                  }
                ]}
              >
                <div style={{ width: 'calc(200% + 16px)', paddingRight: '12px' }}>
                  <TEditor
                    ref={editRef}
                    placeholderText=""
                    contentChanged={(e) => editContentChange(index, attr, e)}
                    uploadParams={{
                      teamId,
                      nodeId: issueListNodeId,
                      moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]?.nameEn,
                      objType: eNodeTypeId.nt_31704_objtype_issue_item
                    }}
                    uploadCallback={uploadFileCallBack}
                  />
                </div>
              </Form.Item>
            </div>
            <div style={{ flex: '1 1 calc(50% - 8px)', minWidth: '0' }}>
              {/* 空的右列，保持布局结构 */}
            </div>
          </div>
        );
      } else {
        // 其他字段按顺序分配到左右两列
        if (leftFields.length <= rightFields.length) {
          leftFields.push(renderField(attr, index));
        } else {
          rightFields.push(renderField(attr, index));
        }
        
        // 当左右两列都有内容时，渲染两列布局
        if (leftFields.length > 0 && rightFields.length > 0) {
          result.push(
            <div key={`two-column-${index}`} style={{
              display: 'flex',
              gap: '16px'
            }}>
              <div style={{
                flex: '1 1 calc(50% - 8px)',
                minWidth: '300px'
              }}>
                {leftFields.splice(0, leftFields.length)}
              </div>
              <div style={{
                flex: '1 1 calc(50% - 8px)',
                minWidth: '300px'
              }}>
                {rightFields.splice(0, rightFields.length)}
              </div>
            </div>
          );
        }
      }
    });
    
    // 处理剩余的单个字段
    if (leftFields.length > 0 || rightFields.length > 0) {
      result.push(
        <div key="remaining-fields" style={{
          display: 'flex',
          gap: '16px'
        }}>
          <div style={{
            flex: '1 1 calc(50% - 8px)',
            minWidth: '300px'
          }}>
            {leftFields}
          </div>
          <div style={{
            flex: '1 1 calc(50% - 8px)',
            minWidth: '300px'
          }}>
            {rightFields}
          </div>
        </div>
      );
    }
    
    return result;
  };

  useEffect(()=>{
    if(dataUpdatedAtSetting409){
      const attrList = subclassAttrList.filter(attr => attr.nodeType == eConsoleNodeType.NodeType_1_Attribute &&
        // 匹配是否启用
        (attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_9_visible)?.propValue || 1) == 1 &&
        isShowModifiable(attr))
        .map(attr => {
          const selectionLid = getAttrPropByType(attr.propertyList, eConsolePropId.Prop_12_selection )?.propValue || "";
          return {
            ...attr,
            uiControl: (attr.propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue,
            selectionLid: selectionLid,
            requiredFlg: (attr.propertyList.find(el => el.propType == eConsolePropId.Prop_239_required))?.propValue || 0,   // 是否必填
            itemValue: attr.propertyList.find(el => el.propType == eConsolePropId.Prop_5_default_value)?.propValue || "",   // 默认值
            itemValueBak: attr.propertyList.find(el => el.propType == eConsolePropId.Prop_5_default_value)?.propValue || "", // 原值,与itemValue相同
            isSysUser: getAttrPropByType(attr.propertyList, eConsolePropId.Prop_27_list_type)?.propValue == eEnableFlg.enable
          }
        }) || [];

        // 初始数据赋值及存储
        let fields = {}
        attrList.map((item, index) => {
          if (item.itemValue) {
            return fields[item.nodeId] = item.itemValue
          }
          // return fields[item.nodeId] = item.itemValue || null
        })
        setInitialValues(fields)
        fromRef.setFieldsValue(fields)
        setAttrList(attrList);
    }
  },[dataUpdatedAtSetting409])

  useBeforeUnload(useCallback((event) => {
    if(isChange()) event.preventDefault()
  },[]), {capture: true})

  unstable_usePrompt({
    when: useCallback(() => {
      return !issueSaveRef.current && isChange()
    },[]),
    message: "正在编辑问题，是否确定放弃编辑?"
  })

    // 提交表单
    const handleSubmitButtonClick = async () => {
      fromRef.submit();
    };
  
    function isChange(){
      let formData = fromRef.getFieldsValue(true);
      //比较Form初始值和最终的值是否相等
      let isNotChange = compareObj(formData, initialValues);
      return !(isNotChange && objRelModalContentChangedFlg)
    }
  
    // 关闭新建弹窗
    function handleCloseCreateIssueModal() {
      if (!isChange()) {
        setOpen(false); //表单没有改动，直接关闭表单
      } else {
        return Modal.confirm({
          title: '提示',
          centered: true,
          content: <div style={{ textAlign: "center", color: "#333333" }}>
          <div>正在编辑{projectInfo?.issueAlias??"问题"}，是否确定放弃编辑?</div>
        </div>,
          okText: "确定",
          cancelText: "取消",
          onOk: () => {
            setOpen(false);
          },
          onCancel: () => {
          },
        })
      }
  }

  //对象关联表格columns
  const columns = [
    {
      title: '名称', dataIndex: 'name', key: 'name',
      render: (text, record, index) => <>
        <AppNodeResourceIcon nodeType={record.objType} className="fontsize-16" style={{ color: "#3279fe", opacity: 0.4 }}/>
        {text}
      </>
    },
    { title: '类型', dataIndex: 'nodeTypeName', key: 'nodeTypeName', },
    { title: '创建人', dataIndex: 'userName', key: 'userName', },
    { title: '关联人', dataIndex: 'refName', key: 'refName', },
    { title: '创建时间', dataIndex: 'createDt', key: 'createDt', },
    { title: '操作', dataIndex: 'operation', key: 'operation', render: (text, record) => <a onClick={() => deleteResource(record)}>删除</a> }
  ];


  function uploadFileCallBack(data) {
    let link = data.link
    let name = link.substring(link.lastIndexOf("/") + 1)
    let fileObj = { objId: data.id, objType: eObjRelType.objrel_23_attachment, objName: name, seqNo: 1 }
    setObjList([...objList, fileObj]);
  }

  // 编辑器
  function editContentChange(index, attr, e) {
    let nodeId = attr.nodeId
    fromRef.setFieldValue(nodeId, editRef.current.getContent())
  }
 
  // 文本textBox
  function showTextBoxUI(attr, index) {
    const isFullWidth = attr.nodeId === eConsoleNodeId.Nid_11102_Issue_Title || attr.uiControl === eConsoleUiControl.RichTextBox;
    
    let format = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_13_format)
    let value = getPropValueByIdType(selectionList, format.selectionLid, format.propValue)
    switch (value) {
      case "数字":
        return <InputNumber type={"number"} style={{ width: isFullWidth ? '100%' : 300 }} />
      case "整数":
        return <InputNumber type={"number"} precision={0} style={{ width: isFullWidth ? '100%' : 300 }} />
      default:
        return <div style={{display:'flex',alignItems:'center'}}>
          <Input style={{ width: isFullWidth ? '100%' : 500, borderRadius: 3 }}
                 maxLength={100} autoComplete="off"
                 onChange={(e)=>setTitleObj({name: attr.nodeId, title: e.target.value})}/>
          <span style={{color:'#999',marginLeft:5}}>
            {titleObj.name == attr.nodeId && titleObj.title.length == 100 ? '(至多100字)' : ''}
          </span>
        </div>
    }
  }
  // 日期 date
  function showDateUI(attr, index) {
    let dateItem = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_13_format)
    let format = dateItem ? getPropValueByIdType(selectionList, dateItem.selectionLid, dateItem.propValue) : "YYYY-MM-DD"
    var time = "HH:mm:ss"
    let showTime = format.indexOf(time) == -1 ? false : true
    return <DatePicker placeholder="请选择时间" format={format} showTime={showTime} />
  }

  // 是否可修改
  function modifyFlg(item) {
    let modifiableProp = item.propertyList.find(el => el.propType == eConsolePropId.Prop_60_modifiable)
    if (modifiableProp?.propValue == "0") {
      return true
    } else {
      return false
    }
  }

  // 删除对象关联
  function deleteResource(_objRel) {
    setObjRelList(objRelList.filter(item => item.obj2Id != _objRel.obj2Id));
  }

  // 对象关联提交至新建issue
  function submitObjRelForm(newObjRelList) {
    //关闭弹窗
    setObjRelModalVisibleFlg(false);
    newObjRelList.forEach((item, index) => {
      item.objId = item.obj2Id
      item.objType = item.nodeType
      item.objName = item.name
      item.seqNo = index
      item.refName = userInfo.userName
    });
    setObjRelList([...objRelList, ...newObjRelList]);
    setObjRelModalContentChangeFlg(false);
  }
  // 012接口 - 新建issue
  async  function _load_issue012_create_issue() {
    setUploadLoading(true);
    // TODO: 为何没有返回包含undefined的数据?
    // tmsbug-6274:issue扩展字段，数据更新，没有从后端返回出应有的数据
    let formData = fromRef.getFieldsValue(true);
    let _attrListObj = {};
    (attrList?.filter(attr=>(!Object.keys(formData)?.some(nodeId=>nodeId == attr.nodeId))) || [])?.map(attr=>{
      _attrListObj[attr.nodeId] = null
    });
    console.log("formData", formData);
    // 处理日期数据，避免影响数据源，产生报错date.clone is not a function
    formData = { ...formData }
    for (let attr in formData) {
      attrList.filter(el => el.uiControl == eConsoleUiControl.Date).map((item, index) => {
        if (attr == item.nodeId && formData[attr]) {
          formData[attr] = formData[attr].format('YYYY-MM-DD')
        }
      })
    }
    let params = { formId: formId, issuegrpNodeId: issueListNodeId, teamId: teamId, objRelList: [...objRelList, ...objList], ...formData, ..._attrListObj }
    await new Promise((resolve, reject)=>  {
      onIssue012CreateIssue(params, {
        onSuccess: (data, vars) => {
          if (data.resultCode == 200) {
            globalUtil.success("提交成功！");
            issueSaveRef.current = true;
            callback?.(data.objNodeId); //列表更新
            // 如果勾选继续新建
            if (createAnotherIssueFlg) {
              editRef.current.setContent(null); //清空编辑器数据
              fromRef.setFieldsValue(initialValues);//恢复默认值
              setObjList([]); //清空附件列表
              setObjRelList([]); //清空对象关联列表
            } else {
              // fromRef.resetFields(); 
              setOpen(false);
            }
          } else {
            console.log(data, vars)
          }
          setFormId(nanoid());
          resolve();
        },
        onError: (err, params) => {
        }
      })
    })
    setUploadLoading(false);
  }

  //上传文件
  function uploadFile(info) {
    if (info.file.status !== 'uploading') {
      console.log('uploading', info.file, info.fileList);
    }
    if (info.file.status === 'done') {
      if (info.file.response.resultCode == 200) {
        let link = info.file.response.link
        let name = link.substring(link.lastIndexOf("/") + 1)
        let fileObj = { objId: info.file.response.id, objType: eObjRelType.objrel_23_attachment, objName: name, seqNo: 1 }
        setObjList([...objList, fileObj]);
        globalUtil.success(`${info.file.name} 文件上传成功！`);
      }
    } else if (info.file.status === 'error') {
      console.log('error', info.file, info.fileList);
      globalUtil.error(`${info.file.name} 文件上传失败！`);
    }
  }

  return <>
      <UploadLoading spinning={isLoading || uploadLoading} delay={eDebounceTime.fiveHundred} >
        <div className="issue-detail" style={{marginTop:"24px"}}>
          <Form
            form={fromRef}
            labelCol={{ span: 3 }}
            wrapperCol={{ span: 21 }}
            labelAlign="right"
            onFinish={_load_issue012_create_issue_debounce}
            scrollToFirstError// 提交失败自动滚动到第一个错误字段 tmsbug-4081:issue必填项为空，无法提交
          >
            {shouldUseTwoColumns ? (
              // 两列布局
              generateTwoColumnLayout()
            ) : (
              // 单列布局
              attrList.map((attr, index) => renderField(attr, index))
            )}
            {
            !isLoading && shouldUseTwoColumns && <>
             <div style={{
               display: 'flex',
               gap: '16px'
             }}>
               <div style={{
                 flex: '1 1 calc(50% - 8px)',
                 minWidth: '300px'
               }}>
                 <Form.Item name="fujian" label="附件" style={{marginBottom:15}}>
                   <Upload name={'file'}
                     multiple={true}
                     action={`${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`}
                     data={{
                       teamId: teamId,
                       moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]?.nameEn,
                       nodeId: eFileObjId,
                       objType: eNodeTypeId.nt_31704_objtype_issue_item
                   }}
                     onChange={uploadFile}
                   >
                     <Button type="link" icon={<PlusOutlined />}>上传附件</Button>
                   </Upload>
                 </Form.Item>
               </div>
               <div style={{
                 flex: '1 1 calc(50% - 8px)',
                 minWidth: '300px'
               }}>
                 {/* 空的右列 */}
               </div>
             </div>
             <div style={{
               display: 'flex',
               gap: '16px'
             }}>
               <div style={{
                 flex: '1 1 calc(50% - 8px)',
                 minWidth: '300px'
               }}>
                 <Form.Item name="resource" label="对象关联" style={{marginBottom:0}}>
                   <Button type="link" icon={<PlusOutlined />}
                     onClick={(e) => setObjRelModalVisibleFlg(true)}>对象关联</Button>
                   {objRelList.length > 0 &&
                     <Table className="custome-table"
                       columns={columns}
                       dataSource={objRelList}
                       size={"small"}
                       pagination={false} />
                   }
                 </Form.Item>
               </div>
               <div style={{
                 flex: '1 1 calc(50% - 8px)',
                 minWidth: '300px'
               }}>
                 {/* 空的右列 */}
               </div>
             </div>
            </>
            }
            {
            !isLoading && !shouldUseTwoColumns && <>
             <Form.Item name="fujian" label="附件" style={{marginBottom:15}}>
               <Upload name={'file'}
                 multiple={true}
                 action={`${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`}
                 data={{
                   teamId: teamId,
                   moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item]?.nameEn,
                   nodeId: eFileObjId,
                   objType: eNodeTypeId.nt_31704_objtype_issue_item
               }}
                 onChange={uploadFile}
               >
                 <Button type="link" icon={<PlusOutlined />}>上传附件</Button>
               </Upload>
             </Form.Item>
             <Form.Item name="resource" label="对象关联" style={{marginBottom:0}}>
               <Button type="link" icon={<PlusOutlined />}
                 onClick={(e) => setObjRelModalVisibleFlg(true)}>对象关联</Button>
               {objRelList.length > 0 &&
                 <Table className="custome-table"
                   columns={columns}
                   dataSource={objRelList}
                   size={"small"}
                   pagination={false} />
               }
             </Form.Item>
            </>
            }
          </Form>
          <ObjRelDrawer
            teamId={teamId}
            showResource={objRelModalVisibleFlg}
            objId={"2"}
            objType={eNodeTypeId.nt_31704_objtype_issue_item}
            onOk={submitObjRelForm}
            onCancle={(e) => setObjRelModalVisibleFlg(false)} 
          />
        </div>
      </UploadLoading>
  </>;
}

export default forwardRef(CreateIssueContent);