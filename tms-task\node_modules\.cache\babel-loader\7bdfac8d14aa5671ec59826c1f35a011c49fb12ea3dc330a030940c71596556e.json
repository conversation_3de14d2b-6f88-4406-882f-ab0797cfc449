{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\inspect\\\\views\\\\InspectProject\\\\InspectHome\\\\InspectHome.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable no-use-before-define */\nimport { ePagination, eSelectionListId, eFlowAttrNid, eEnableFlg, eTreeOpType } from \"@common/utils/enum\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { eNodeTypeId, inspCreatePartitionNodeTypeList, eCtxTypeId } from \"@common/utils/TsbConfig\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport { priSettingPermission } from \"@common/utils/logicUtils\";\nimport NoviceGuide from \"@components/NoviceGuide\"; // 新手引导\nimport PageTitle from \"@components/PageTitle\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { Layout, Spin, Row, Space } from \"antd\";\nimport { useEffect, useRef, useState } from \"react\";\nimport { Outlet, useAsyncValue, useLocation, useParams } from \"react-router-dom\";\nimport { useIssueSearchParams, useSearchQuery } from \"src/inspect/service/inspectSearchHooks\";\nimport InspectTopSearchBar from \"./InspectTopSearchBar\";\nimport * as http from \"@common/api/http\";\nimport ContextBoard from \"@common/components/ContextBoard\";\nimport { objNodeListMoreOps } from \"@common/service/objNodeListMoreOps\";\nimport { isEmpty, getSysIconByIdType } from \"@common/utils/ArrayUtils\";\nimport { isContain, scrollToAnchor } from \"@common/utils/ViewUtils\";\nimport { eOrderByType } from \"src/inspect/utils/enum\";\nimport { formatSvg, getViewModePath } from \"src/issueTrack/utils/ArrayUtils\";\nimport { useImmer } from \"use-immer\";\nimport \"./InspectHome.scss\";\nimport { useQuerySetting407_getCodeValueList, useQuerySetting320GetNodePrivQuery } from \"@common/service/commonHooks\";\nimport { useQuerySetting409_getTeamAttrgrpProps } from \"src/issueTrack/service/issueHooks\";\nimport * as toolUtil from \"@common/utils/toolUtil\";\nimport qs from \"qs\";\nimport ScrollableHeader from \"@components/ScrollableHeader\";\nimport { getFlowList } from \"@common/utils/logicUtils\";\nimport { insp_091_get_insp_project_detail_query } from \"@common/api/query/inspect/query_insp_01_mgmt\";\nimport { insp_098_get_project_process_query } from \"@common/api/query/inspect/query_insp_10_issues_and_setting\";\nimport { useGetInspectRelatedNodes } from \"src/inspect/service/inspectHooks\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function InspectHome() {\n  _s();\n  const location = useLocation();\n  const contextboardRef = useRef(null);\n  const {\n    teamId,\n    nodeId\n  } = useParams();\n  const {\n    setting320Result,\n    userList = [],\n    spaceUserList = [],\n    allSpaceUserList = [],\n    queryKeywords,\n    objInfo,\n    criteriaList: _searchQuery\n  } = useAsyncValue(); //预加载获取的数据\n  const {\n    data: {\n      isOA\n    }\n  } //isLoading: insp072Loading\n  = useGetInspectRelatedNodes({\n    teamId,\n    nodeId,\n    enabled: !!nodeId\n  });\n  const viewMode = getViewModePath(location.pathname); // 视图回显\n\n  const [rootNode, setRootNode] = useImmer(null); // 列表数据\n  const [totalCnt, setTotalCnt] = useState(0); //查询结果集总的记录数，分页加载数据\n  const [iconSelectionLid] = useState(null); //icon列表id\n\n  // 搜索条件相关\n  const {\n    issueNodeId,\n    queryId,\n    setIssueSearchParams\n  } = useIssueSearchParams(); //issue路由配置，页面刷新\n  let pageSize = ePagination.PageSize_30; //默认pageSize为30\n  const {\n    searchQuery,\n    updateSearchQuery,\n    loading\n  } = useSearchQuery({\n    _searchQuery\n  }); //查询条件列表\n  const [orderBy, setOrderBy] = useState(eOrderByType.create); //排序，创建时间\n  const [ascendingFlg, setAscendingFlg] = useState(false); //false:倒序,true:正序 \n  const [keywords, setKeywords] = useState(); //关键字\n  const [contextKey, setContextKey] = useState([]); // 右键菜单选中的key\n  const [currentIssueIdx, setCurrentIssueIdx] = useState(0); // 用于切换页码时，加载数据前，先设置选中的issue索引,上一条为当前页的最后一条，下一条为当前页的第一条\n  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);\n  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);\n  const [selectedKeys, setSelectedKeys] = useState([]); // 选中节点\n  const [flowList, setFlowList] = useState([]); // 流程图列表\n  const [defaultFlow, setDefaultFlow] = useState([]); // 默认可新建流程图\n  const [selectedFlow, setselectedFlow] = useState(); // 选择的流程图\n  const [pageNum, setPageNum] = useState(); // 页码 注意：不要赋值默认值，需要先通过load_exam_040_get_question_page来获取真实页码\n  const [guid, setGuid] = useState(toolUtil.guid());\n  const [isLoading, setIsLoading] = useState(true);\n  const [isLoadingCreate, setIsLoadingCreate] = useState(false); // 新建发现项loading\n  const [createObjNode, setCreateObjNode] = useState(); // 需要新增的node节点\n  const [needShowPowerLock, setNeedShowPowerLock] = useState(false);\n  const {\n    data: selectionList,\n    isLoading: isLoadingCodeValueList\n  } = useQuerySetting407_getCodeValueList(teamId); //字典数据\n\n  // 权限相关\n  const {\n    data: {\n      privSetting\n    }\n  } = useQuerySetting320GetNodePrivQuery({\n    teamId,\n    nodeId\n  });\n  const canEditFlg = inspCreatePartitionNodeTypeList.includes(objInfo === null || objInfo === void 0 ? void 0 : objInfo.nodeType);\n\n  // 巡检项目详情\n  const {\n    data: projectInfo = {}\n  } = useQuery(insp_091_get_insp_project_detail_query(teamId, nodeId));\n  const {\n    data: projectProcessList = [],\n    dataUpdatedAt: dataUpdatedAtInsp098\n  } = useQuery(insp_098_get_project_process_query(teamId, nodeId));\n\n  // 自定义表单属性列表\n  // 用户组需要创建人字段，此处放开全部字段（包含不显示字段）\n  const {\n    subclassAttrList = []\n  } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeId, selectedFlow === null || selectedFlow === void 0 ? void 0 : selectedFlow.subclassId, !!(selectedFlow !== null && selectedFlow !== void 0 && selectedFlow.subclassId), eEnableFlg.enable);\n\n  /**\r\n  * useQuery无法很好的解决俩个接口之间的条件依赖关系，所以需要使用useEffect配合fetch来解决\r\n  * 需要先调用exam-040 get_question_page 获取题目在题库的页码 再通过页码查询题库列表的场景\r\n  * 1.页面初始化\r\n  * 2.过滤条件和排序规则变更时\r\n  */\n  useEffect(() => {\n    if (!loading) {\n      load_insp_094_get_process_page_with_id(issueNodeId);\n    }\n  }, [loading]);\n  useEffect(() => {\n    if (!loading && dataUpdatedAtInsp098) {\n      initSelectedRow();\n    }\n  }, [loading, dataUpdatedAtInsp098]);\n\n  // 监听queryKeywords\n  useEffect(() => {\n    setKeywords(queryKeywords);\n  }, [queryKeywords]);\n\n  // 需要获取到pageNum后才能调用\n  useEffect(() => {\n    if (!!pageNum) {\n      load_insp_092_get_process_inst_list();\n    }\n  }, [guid, pageNum, pageSize]);\n  useEffect(() => {\n    // isLoadingAttrgrpProps 更改自定义表单后是否仍然有效\n    if (dataUpdatedAtInsp098) {\n      const [flowList, defaultFlow] = getFlowList(projectProcessList);\n      setFlowList(flowList);\n      setDefaultFlow(defaultFlow);\n      if (objInfo.nodeType == eNodeTypeId.nt_574_insp_partition || objInfo.nodeType == eNodeTypeId.nt_674_oa_partition) {\n        // 采集口径只有一个流程，则默认选中该流程，无需参与数据逻辑\n        setselectedFlow(projectProcessList[0]);\n      }\n    }\n  }, [dataUpdatedAtInsp098]);\n\n  // 监听issueNodeId\n  useEffect(() => {\n    if (!!issueNodeId && !isLoadingCodeValueList) {\n      setSelectedKeys([issueNodeId === null || issueNodeId === void 0 ? void 0 : issueNodeId.toString()]);\n    } else {\n      setSelectedKeys([]);\n    }\n  }, [issueNodeId, isLoadingCodeValueList]);\n  useEffect(() => {\n    if (!!location.search) {\n      let search = qs.parse(location.search.slice(1));\n      if ((search === null || search === void 0 ? void 0 : search.openPowerLock) == '1') {\n        setNeedShowPowerLock(true);\n        return;\n      }\n    }\n    setNeedShowPowerLock(false);\n  }, [location.search]);\n\n  // 选中流程\n  function initSelectedRow() {\n    var _searchQuery$find;\n    const processdefId = (((_searchQuery$find = searchQuery.find(search => search.col == eFlowAttrNid)) === null || _searchQuery$find === void 0 ? void 0 : _searchQuery$find.begin) || [])[0];\n    if (!!processdefId) {\n      const selectedFlow = projectProcessList.find(process => process.processdefId == processdefId);\n      setselectedFlow(selectedFlow);\n    }\n  }\n\n  // 初始化数据\n  function load_insp_094_get_process_page_with_id(objNodeId) {\n    if (isEmpty(objNodeId)) {\n      // 没有objNodeId时不需调用这个接口,默认加载第一页数据\n      setPageNum(1);\n      setGuid(toolUtil.guid()); // 页码可能没有更改；需要手动通知更新下\n      return;\n    }\n    setPageNum(); // 清除掉页码\n    setIsLoading(true);\n    let params = {\n      teamId,\n      anchorNodeId: nodeId,\n      nodeId: objNodeId,\n      pageSize,\n      order: orderBy,\n      query: parseQuerey(searchQuery),\n      keywords,\n      desc: ascendingFlg ? \"asc\" : \"desc\"\n    };\n    http.insp_094_get_process_page_with_id(params).then(res => {\n      if (res.resultCode === 200) {\n        setPageNum(res.pageNum);\n      } else {\n        setIsLoading(false);\n      }\n    }).catch(e => {\n      console.log(\"exam_040_get_question_page\" + e);\n      setIsLoading(false);\n    });\n  }\n  function load_insp_092_get_process_inst_list() {\n    setIsLoading(true);\n    let params = {\n      nodeId,\n      teamId,\n      pageNum: pageNum,\n      pageSize: pageSize,\n      order: orderBy,\n      query: parseQuerey(searchQuery),\n      keywords,\n      desc: ascendingFlg ? \"asc\" : \"desc\"\n    };\n    http.insp_092_get_process_inst_list(params).then(res => {\n      if (res.resultCode == 200) {\n        const {\n          processInstanceList = [],\n          total\n        } = res;\n        processInstanceList.forEach(objNode => {\n          objNode.key = objNode.nodeId; //增加key属性，表格需要用key属性\n          objNode.anchorNodeId = nodeId; // 项目nodeId\n          objNode.realNodeId = objNode.nodeId; // 对象nodeId，兼容快捷方式\n          // tmsbug-10268：pc端巡检发现项列表，标题参考移动端补充列表信息\n          // https://os.iteam.com/#/team/9176631781/issues/9747391877011/issue/74302168893183?queryId=59221509971460\n          objNode.label = objNode.chkpointName || objNode.name; // 巡检优先取值chekpointName\n          objNode.icon = formatSvg(getSysIconByIdType(selectionList, eSelectionListId.Selection_1939_Icon, objNode.processIcon)); // 图标(取自系统图标)\n          objNode.showMoreIcon = true; // 显示更多...\n        });\n        setRootNode(processInstanceList);\n        setTotalCnt(total);\n        setIsLoading(false);\n        if (!isEmpty(createObjNode)) {\n          // 需要跳转至新增的节点\n          setTimeout(() => {\n            setIssueSearchParams({\n              viewMode,\n              issueNodeId: createObjNode.nodeId,\n              queryId\n            }); // 用宏任务来处理setState更新Loading存在异步的问题\n          });\n          setCreateObjNode(); // 清除待添加的节点\n        }\n      } else {\n        setIsLoading(false);\n      }\n    }).catch(e => {\n      console.log(e);\n      setIsLoading(false);\n    });\n  }\n  function parseQuerey(searchQuery) {\n    let query = null;\n    if (!isEmpty(searchQuery)) {\n      // 先过滤begin为空的数据\n      query = searchQuery.filter(el => !isEmpty(el.begin)).map(search => {\n        var _search$begin;\n        //  const inspType = subclassAttrList.find(attr => attr.nodeId == search.col)?.inspType;\n        //  if(inspType == eRegionType.region || inspType == eRegionType.check){\n        //   return {\n        //       ...search,\n        //       // 如果是日期 则为字符串，不需要把数组变成逗号\n        //       begin: search.begin[search.begin.length - 1], // 取最后一个\n        //   }\n        //  }\n        return {\n          ...search,\n          // 如果是日期 则为字符串，不需要把数组变成逗号\n          begin: search.end ? search.begin : (_search$begin = search.begin) === null || _search$begin === void 0 ? void 0 : _search$begin.join(\",\") //数组变成以逗号隔开\n        };\n      });\n    }\n    return query;\n  }\n\n  // 选中的节点\n  useEffect(() => {\n    if (!isLoading) {\n      if (rootNode) {\n        let _idx = rootNode.findIndex(question => question.nodeId == issueNodeId);\n        setPreviousIssueLinkDisabledFlg(pageNum == 1 && _idx == 0);\n        setNextIssueLinkDisabledFlg(pageNum == Math.ceil(totalCnt / pageSize) && _idx == (rootNode === null || rootNode === void 0 ? void 0 : rootNode.length) - 1);\n        if (totalCnt == 0) {\n          globalUtil.info(\"暂无数据!\");\n          setIssueSearchParams({\n            viewMode,\n            issueNodeId: null,\n            queryId\n          });\n        } else {\n          var _rootNode$currentIssu;\n          // 有数据，则进行数据处理, 根据objNodeId 路由跳转question详情\n          if (issueNodeId) {\n            // 查找列表中是否有objNodeId\n            let foundNode = rootNode.find(question => question.nodeId == issueNodeId);\n            if (foundNode) {\n              scrollToSelectedKey(issueNodeId);\n              return;\n            }\n          }\n          const _issueNodeId = (_rootNode$currentIssu = rootNode[currentIssueIdx]) === null || _rootNode$currentIssu === void 0 ? void 0 : _rootNode$currentIssu.nodeId;\n          if (_issueNodeId) {\n            setIssueSearchParams({\n              viewMode,\n              issueNodeId: _issueNodeId,\n              queryId\n            });\n            scrollToSelectedKey(_issueNodeId);\n          } else {\n            // fix tmsbug-9738:删除发现项之后，详情没有清空，页面没有自动刷新, 列表无数据的处理, TODO:如此处理，数据被删除后点击路由会默认回到第一条数据\n            setIssueSearchParams({\n              viewMode,\n              issueNodeId: null,\n              queryId\n            });\n          }\n        }\n      } else {\n        setIssueSearchParams({\n          viewMode,\n          issueNodeId: null,\n          queryId\n        });\n      }\n    }\n  }, [isLoading, rootNode, issueNodeId]);\n\n  //  滚动至选中的节点\n  const scrollToSelectedKey = objNodeId => {\n    if (!!objNodeId) {\n      setTimeout(() => {\n        let target = document.querySelector(`div[data-content-key=\"${objNodeId}\"]`); // 节点 兼容长短列表\n        if (!isContain(target)) {\n          scrollToAnchor(target);\n        }\n      }, 500);\n    }\n  };\n\n  // 上一条\n  function gotoPreviousIssue() {\n    const currentIssueIdx = rootNode.findIndex(_issue => _issue.nodeId == issueNodeId);\n    if (currentIssueIdx == 0) {\n      //当前页的最后一行记录，再往前即需要向前翻页\n      if (pageNum > 1) {\n        setPageNum(pageNum - 1);\n        // globalUtil.getQueryClient().setQueryData([track010, teamId, nodeId], (pageNum)=>(pageNum - 1)); //触发 useQueryIssue017_getIssueList 再次加载\n        setCurrentIssueIdx(ePagination.PageSize_30 - 1);\n      }\n    } else {\n      setIssueSearchParams({\n        viewMode,\n        issueNodeId: rootNode[currentIssueIdx - 1].nodeId\n      });\n    }\n  }\n\n  // 下一条\n  function gotoNextIssue() {\n    const currentIssueIdx = rootNode.findIndex(_issue => _issue.nodeId == issueNodeId);\n    if (currentIssueIdx == pageSize - 1) {\n      //当前页的最后一行记录，再往后即需要向后翻页\n      if (pageNum < Math.ceil(totalCnt / pageSize)) {\n        setPageNum(pageNum + 1);\n        // globalUtil.getQueryClient().setQueryData([track010, teamId, nodeId], (pageNo)=>(pageNo + 1)); //触发 useQueryIssue017_getIssueList 再次加载\n        setCurrentIssueIdx(0);\n      }\n    } else {\n      setIssueSearchParams({\n        viewMode,\n        issueNodeId: rootNode[currentIssueIdx + 1].nodeId\n      });\n    }\n  }\n\n  // 右击菜单事件\n  const onMoreBtnClick = ({\n    nodeItem,\n    ctxType,\n    ...args\n  }) => {\n    objNodeListMoreOps({\n      teamId,\n      nodeType: eNodeTypeId.nt_572_insp_processList_process,\n      objNode: {\n        ...nodeItem,\n        key: nodeItem.nodeId\n      },\n      ctxType,\n      setRootNode: setRootNode,\n      setTotal: setTotalCnt,\n      args\n    });\n  };\n  const onShowMoreButtonClick = info => {\n    var _info$node$nodeId;\n    console.log(\"onShowMoreButtonClick info\", info);\n    contextboardRef.current.showContextBoard(info.event.nativeEvent || info.event, {\n      ...info.node,\n      nodeType: eNodeTypeId.nt_572_insp_processList_process\n    }, info.node.nodeId);\n    setSelectedKeys([...selectedKeys, (_info$node$nodeId = info.node.nodeId) === null || _info$node$nodeId === void 0 ? void 0 : _info$node$nodeId.toString()]); // 右击菜单显示时，选中当前节点\n    setContextKey(info.node.nodeId);\n  };\n\n  // react-contexify 隐藏事件，v6.0版本:废弃onHidden,改用onVisibilityChange属性，参考：https://github.com/fkhadra/react-contexify/pull/220\n  function handleOnVisibilityChange(isVisible) {\n    if (!isVisible && contextKey != issueNodeId) {\n      // 右击菜单隐藏时，取消选中当前节点\n      let _selectedKeys = selectedKeys.filter(selectedKey => selectedKey != contextKey);\n      setSelectedKeys([..._selectedKeys]);\n    }\n  }\n\n  // 新建数据\n  const handleUpdateData = result => {\n    // 方案：新建的第一条流程，不确定在那一页（因为有排序规则的存在），所以需要先查询页码，再跳转\n    load_insp_094_get_process_page_with_id(result === null || result === void 0 ? void 0 : result.nodeId);\n    setCreateObjNode(result); // 缓存下新增的节点      \n  };\n\n  // 新建采集口径设置\n  const openCreateInspectPartitionEvent = objInfo => {\n    globalEventBus.emit(\"onCreateBtnClick\", \"\", {\n      nodeItem: objInfo,\n      ctxType: eCtxTypeId.ctx_38_create,\n      treeOpType: eTreeOpType.opTitle\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ScrollableHeader, {\n      title: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n          teamId: teamId,\n          nodeId: nodeId,\n          powerLock: true,\n          refetchData: load_insp_092_get_process_inst_list,\n          needShowPowerLock: needShowPowerLock\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: 10,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            className: \"issuehome-header-btns\",\n            size: 2,\n            children: canEditFlg && priSettingPermission(privSetting) ? /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"fontsize-12 fontcolor-normal\",\n              onClick: () => openCreateInspectPartitionEvent(objInfo),\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                size: 2,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"iconfont shezhixitongshezhigongnengshezhishuxing fontsize-12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u91C7\\u96C6\\u53E3\\u5F84\\u8BBE\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 7\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(InspectTopSearchBar, {\n        subclassAttrList: subclassAttrList,\n        allSpaceUserList: allSpaceUserList\n        // TODO: 巡检其他模块暂未加当前协作群成员的限制，暂时放开限制\n        ,\n        spaceUserList: spaceUserList,\n        selectionList: selectionList,\n        objInfo: objInfo,\n        keywords: keywords,\n        setKeywords: setKeywords,\n        searchQuery: searchQuery,\n        updateSearchQuery: updateSearchQuery,\n        refetchIssueList: load_insp_092_get_process_inst_list,\n        selectedFlow: selectedFlow,\n        projectProcessList: projectProcessList,\n        needShowPowerLock: needShowPowerLock\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        flex: \"auto\",\n        backgroundColor: \"#fff\",\n        height: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: isLoading || isLoadingCreate,\n        wrapperClassName: \"issueSpin\",\n        children: /*#__PURE__*/_jsxDEV(Outlet, {\n          context: {\n            userList,\n            spaceUserList,\n            selectionList,\n            objInfo,\n            rootNode,\n            totalCnt: totalCnt,\n            pageNum,\n            setPageNum,\n            searchQuery,\n            iconSelectionLid,\n            setCurrentIssueIdx,\n            previousIssueLinkDisabledFlg,\n            nextIssueLinkDisabledFlg,\n            gotoPreviousIssue,\n            gotoNextIssue,\n            ascendingFlg,\n            setAscendingFlg,\n            orderBy,\n            setOrderBy,\n            viewMode,\n            projectInfo,\n            setting320Result,\n            selectedKeys,\n            onMoreBtnClick,\n            onShowMoreButtonClick,\n            flowList,\n            defaultFlow,\n            handleUpdateData,\n            load_insp_092_get_process_inst_list,\n            projectProcessList,\n            setIsLoadingCreate,\n            useIssueSearchParams,\n            isOA\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(ContextBoard, {\n      ref: contextboardRef,\n      teamId: teamId,\n      onMoreBtnClick: onMoreBtnClick\n      // onCreateBtnClick={onCreateBtnClick} \n      ,\n      id: \"inspect-context-menu\",\n      handleOnVisibilityChange: handleOnVisibilityChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(NoviceGuide, {\n      nodeType: eNodeTypeId.nt_31705_objtype_issue_list,\n      awakeFlg: 0\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n}\n_s(InspectHome, \"rUSMgMHPOF43MPCxmm0Wl38tOOs=\", false, function () {\n  return [useLocation, useParams, useAsyncValue, useGetInspectRelatedNodes, useImmer, useIssueSearchParams, useSearchQuery, useQuerySetting407_getCodeValueList, useQuerySetting320GetNodePrivQuery, useQuery, useQuery, useQuerySetting409_getTeamAttrgrpProps];\n});\n_c = InspectHome;\nvar _c;\n$RefreshReg$(_c, \"InspectHome\");", "map": {"version": 3, "names": ["ePagination", "eSelectionListId", "eFlowAttrNid", "eEnableFlg", "eTreeOpType", "globalUtil", "eNodeTypeId", "inspCreatePartitionNodeTypeList", "eCtxTypeId", "globalEventBus", "priSettingPermission", "NoviceGuide", "Page<PERSON><PERSON>le", "useQuery", "Layout", "Spin", "Row", "Space", "useEffect", "useRef", "useState", "Outlet", "useAsyncValue", "useLocation", "useParams", "useIssueSearchParams", "useSearchQuery", "InspectTopSearchBar", "http", "ContextBoard", "objNodeListMoreOps", "isEmpty", "getSysIconByIdType", "isContain", "scrollToAnchor", "eOrderByType", "formatSvg", "getViewModePath", "useImmer", "useQuerySetting407_getCodeValueList", "useQuerySetting320GetNodePrivQuery", "useQuerySetting409_getTeamAttrgrpProps", "toolUtil", "qs", "ScrollableHeader", "getFlowList", "insp_091_get_insp_project_detail_query", "insp_098_get_project_process_query", "useGetInspectRelatedNodes", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InspectHome", "_s", "location", "contextboardRef", "teamId", "nodeId", "setting320Result", "userList", "spaceUserList", "allSpaceUserList", "queryKeywords", "objInfo", "criteriaList", "_searchQuery", "data", "isOA", "enabled", "viewMode", "pathname", "rootNode", "setRootNode", "totalCnt", "setTotalCnt", "iconSelectionLid", "issueNodeId", "queryId", "setIssueSearchParams", "pageSize", "PageSize_30", "searchQuery", "updateSearchQuery", "loading", "orderBy", "setOrderBy", "create", "ascendingFlg", "setAscendingFlg", "keywords", "setKeywords", "<PERSON><PERSON>ey", "setContextKey", "currentIssueIdx", "setCurrentIssueIdx", "previousIssueLinkDisabledFlg", "setPreviousIssueLinkDisabledFlg", "nextIssueLinkDisabledFlg", "setNextIssueLinkDisabledFlg", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedKeys", "flowList", "setFlowList", "defaultFlow", "setDefaultFlow", "<PERSON><PERSON><PERSON>", "setselectedFlow", "pageNum", "setPageNum", "guid", "setGuid", "isLoading", "setIsLoading", "isLoadingCreate", "setIsLoadingCreate", "createObjNode", "setCreateObjNode", "needShowPowerLock", "setNeedShowPowerLock", "selectionList", "isLoadingCodeValueList", "privSetting", "canEditFlg", "includes", "nodeType", "projectInfo", "projectProcessList", "dataUpdatedAt", "dataUpdatedAtInsp098", "subclassAttrList", "subclassId", "enable", "load_insp_094_get_process_page_with_id", "initSelectedRow", "load_insp_092_get_process_inst_list", "nt_574_insp_partition", "nt_674_oa_partition", "toString", "search", "parse", "slice", "openPowerLock", "_searchQuery$find", "processdefId", "find", "col", "begin", "process", "objNodeId", "params", "anchorNodeId", "order", "query", "<PERSON>se<PERSON><PERSON><PERSON>", "desc", "insp_094_get_process_page_with_id", "then", "res", "resultCode", "catch", "e", "console", "log", "insp_092_get_process_inst_list", "processInstanceList", "total", "for<PERSON>ach", "objNode", "key", "realNodeId", "label", "chkpointName", "name", "icon", "Selection_1939_Icon", "processIcon", "showMoreIcon", "setTimeout", "filter", "el", "map", "_search$begin", "end", "join", "_idx", "findIndex", "question", "Math", "ceil", "length", "info", "_rootNode$currentIssu", "foundNode", "scrollToSelectedKey", "_issueNodeId", "target", "document", "querySelector", "gotoPreviousIssue", "_issue", "gotoNextIssue", "onMoreBtnClick", "nodeItem", "ctxType", "args", "nt_572_insp_processList_process", "setTotal", "onShowMoreButtonClick", "_info$node$nodeId", "current", "showContextBoard", "event", "nativeEvent", "node", "handleOnVisibilityChange", "isVisible", "_selected<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "handleUpdateData", "result", "openCreateInspectPartitionEvent", "emit", "ctx_38_create", "treeOpType", "opTitle", "children", "title", "justify", "align", "powerLock", "refetchData", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "className", "onClick", "refetchIssueList", "style", "flex", "backgroundColor", "height", "spinning", "wrapperClassName", "context", "ref", "id", "nt_31705_objtype_issue_list", "awakeFlg", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/inspect/views/InspectProject/InspectHome/InspectHome.jsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n/* eslint-disable no-use-before-define */\r\nimport { ePagination, eSelectionListId, eFlowAttrNid, eEnableFlg, eTreeOpType } from \"@common/utils/enum\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { eNodeTypeId, inspCreatePartitionNodeTypeList, eCtxTypeId } from \"@common/utils/TsbConfig\";\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport { priSettingPermission } from \"@common/utils/logicUtils\";\r\nimport NoviceGuide from \"@components/NoviceGuide\"; // 新手引导\r\nimport PageTitle from \"@components/PageTitle\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { Layout, Spin, Row, Space } from \"antd\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { Outlet, useAsyncValue, useLocation, useParams } from \"react-router-dom\";\r\nimport { useIssueSearchParams, useSearchQuery } from \"src/inspect/service/inspectSearchHooks\";\r\nimport InspectTopSearchBar from \"./InspectTopSearchBar\";\r\nimport * as http from \"@common/api/http\";\r\nimport ContextBoard from \"@common/components/ContextBoard\";\r\nimport { objNodeListMoreOps } from \"@common/service/objNodeListMoreOps\";\r\nimport { isEmpty, getSysIconByIdType } from \"@common/utils/ArrayUtils\";\r\nimport { isContain, scrollToAnchor } from \"@common/utils/ViewUtils\";\r\nimport { eOrderByType } from \"src/inspect/utils/enum\";\r\nimport { formatSvg, getViewModePath } from \"src/issueTrack/utils/ArrayUtils\";\r\nimport { useImmer } from \"use-immer\";\r\nimport \"./InspectHome.scss\";\r\nimport { useQuerySetting407_getCodeValueList, useQuerySetting320GetNodePrivQuery } from \"@common/service/commonHooks\";\r\nimport {  useQuerySetting409_getTeamAttrgrpProps } from \"src/issueTrack/service/issueHooks\";\r\nimport * as toolUtil from \"@common/utils/toolUtil\";\r\nimport qs from \"qs\";\r\nimport ScrollableHeader from \"@components/ScrollableHeader\";\r\nimport { getFlowList } from \"@common/utils/logicUtils\";\r\nimport {insp_091_get_insp_project_detail_query} from \"@common/api/query/inspect/query_insp_01_mgmt\";\r\nimport {insp_098_get_project_process_query} from \"@common/api/query/inspect/query_insp_10_issues_and_setting\";\r\nimport { useGetInspectRelatedNodes } from \"src/inspect/service/inspectHooks\";\r\n\r\nexport default function InspectHome() {\r\n  const location = useLocation();\r\n  const contextboardRef = useRef(null)\r\n  const { teamId, nodeId } = useParams();\r\n  const { setting320Result, userList = [], spaceUserList = [], allSpaceUserList = [], queryKeywords, objInfo, criteriaList: _searchQuery } = useAsyncValue(); //预加载获取的数据\r\n  const { data: { isOA,},  } //isLoading: insp072Loading\r\n      = useGetInspectRelatedNodes({teamId, nodeId, enabled: !!nodeId});\r\n  const viewMode = getViewModePath(location.pathname); // 视图回显\r\n\r\n  const [rootNode, setRootNode] = useImmer(null); // 列表数据\r\n  const [totalCnt, setTotalCnt] = useState(0); //查询结果集总的记录数，分页加载数据\r\n  const [iconSelectionLid] = useState(null); //icon列表id\r\n\r\n  // 搜索条件相关\r\n  const { issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams(); //issue路由配置，页面刷新\r\n  let pageSize = ePagination.PageSize_30; //默认pageSize为30\r\n  const { searchQuery, updateSearchQuery, loading } = useSearchQuery({ _searchQuery }); //查询条件列表\r\n  const [orderBy, setOrderBy] = useState(eOrderByType.create); //排序，创建时间\r\n  const [ascendingFlg, setAscendingFlg] = useState(false); //false:倒序,true:正序 \r\n  const [keywords, setKeywords] = useState(); //关键字\r\n  const [contextKey, setContextKey] = useState([]); // 右键菜单选中的key\r\n  const [currentIssueIdx, setCurrentIssueIdx] = useState(0); // 用于切换页码时，加载数据前，先设置选中的issue索引,上一条为当前页的最后一条，下一条为当前页的第一条\r\n  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);\r\n  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);\r\n  const [selectedKeys, setSelectedKeys] = useState([]); // 选中节点\r\n  const [flowList, setFlowList] = useState([]);           // 流程图列表\r\n  const [defaultFlow, setDefaultFlow] = useState([]);     // 默认可新建流程图\r\n  const [selectedFlow, setselectedFlow] = useState();     // 选择的流程图\r\n  const [pageNum, setPageNum] = useState(); // 页码 注意：不要赋值默认值，需要先通过load_exam_040_get_question_page来获取真实页码\r\n  const [guid, setGuid] = useState(toolUtil.guid());\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isLoadingCreate, setIsLoadingCreate] = useState(false); // 新建发现项loading\r\n  const [createObjNode, setCreateObjNode] = useState(); // 需要新增的node节点\r\n  const [needShowPowerLock,setNeedShowPowerLock] = useState(false);\r\n\r\n  const { data: selectionList, isLoading: isLoadingCodeValueList } = useQuerySetting407_getCodeValueList(teamId); //字典数据\r\n  \r\n  // 权限相关\r\n  const { data: { privSetting } } = useQuerySetting320GetNodePrivQuery({ teamId, nodeId });\r\n  const canEditFlg = (inspCreatePartitionNodeTypeList.includes(objInfo?.nodeType));\r\n\r\n  // 巡检项目详情\r\n  const { data: projectInfo = {} } = useQuery(insp_091_get_insp_project_detail_query(teamId, nodeId))\r\n  const { data: projectProcessList = [], dataUpdatedAt: dataUpdatedAtInsp098  } = useQuery(insp_098_get_project_process_query(teamId, nodeId, ));\r\n\r\n  // 自定义表单属性列表\r\n  // 用户组需要创建人字段，此处放开全部字段（包含不显示字段）\r\n  const { subclassAttrList=[] } =\r\n      useQuerySetting409_getTeamAttrgrpProps(teamId, nodeId, selectedFlow?.subclassId, !!selectedFlow?.subclassId, eEnableFlg.enable,);\r\n\r\n  /**\r\n  * useQuery无法很好的解决俩个接口之间的条件依赖关系，所以需要使用useEffect配合fetch来解决\r\n  * 需要先调用exam-040 get_question_page 获取题目在题库的页码 再通过页码查询题库列表的场景\r\n  * 1.页面初始化\r\n  * 2.过滤条件和排序规则变更时\r\n  */\r\n  useEffect(() => {\r\n    if (!loading) {\r\n      load_insp_094_get_process_page_with_id(issueNodeId);\r\n    }\r\n  }, [loading]);\r\n\r\n  useEffect(()=>{\r\n    if (!loading && dataUpdatedAtInsp098) {\r\n      initSelectedRow()\r\n    }\r\n  },[loading, dataUpdatedAtInsp098])\r\n\r\n   // 监听queryKeywords\r\n   useEffect(() => {\r\n    setKeywords(queryKeywords)\r\n  }, [queryKeywords])\r\n\r\n  // 需要获取到pageNum后才能调用\r\n  useEffect(() => {\r\n    if (!!pageNum) {\r\n      load_insp_092_get_process_inst_list()\r\n    }\r\n  }, [guid, pageNum, pageSize])\r\n\r\n  useEffect(() => {\r\n    // isLoadingAttrgrpProps 更改自定义表单后是否仍然有效\r\n    if (dataUpdatedAtInsp098 ) {\r\n      const [flowList, defaultFlow] = getFlowList(projectProcessList);\r\n      setFlowList(flowList);\r\n      setDefaultFlow(defaultFlow);\r\n      if(objInfo.nodeType == eNodeTypeId.nt_574_insp_partition || objInfo.nodeType == eNodeTypeId.nt_674_oa_partition){ // 采集口径只有一个流程，则默认选中该流程，无需参与数据逻辑\r\n        setselectedFlow(projectProcessList[0]);\r\n      }\r\n    }\r\n  }, [dataUpdatedAtInsp098]);\r\n\r\n  // 监听issueNodeId\r\n  useEffect(() => {\r\n    if (!!issueNodeId && !isLoadingCodeValueList) {\r\n      setSelectedKeys([issueNodeId?.toString()]);\r\n    } else {\r\n      setSelectedKeys([]);\r\n    }\r\n  }, [issueNodeId, isLoadingCodeValueList])\r\n\r\n  useEffect(()=>{\r\n    if(!!location.search){\r\n      let search = qs.parse(location.search.slice(1))\r\n      if(search?.openPowerLock == '1'){\r\n        setNeedShowPowerLock(true);\r\n        return\r\n      }\r\n    }\r\n    setNeedShowPowerLock(false);\r\n  },[location.search]);\r\n\r\n  // 选中流程\r\n  function initSelectedRow () {\r\n    const processdefId = (searchQuery.find(search => search.col == eFlowAttrNid)?.begin || [])[0];\r\n    if(!!processdefId){\r\n      const selectedFlow = projectProcessList.find(process => process.processdefId == processdefId);\r\n      setselectedFlow(selectedFlow);\r\n    }\r\n  }\r\n\r\n  // 初始化数据\r\n  function load_insp_094_get_process_page_with_id(objNodeId) {\r\n    if(isEmpty(objNodeId)){ // 没有objNodeId时不需调用这个接口,默认加载第一页数据\r\n         setPageNum(1); \r\n         setGuid(toolUtil.guid());// 页码可能没有更改；需要手动通知更新下\r\n         return\r\n    }\r\n    setPageNum(); // 清除掉页码\r\n    setIsLoading(true);\r\n    let params = { teamId, anchorNodeId: nodeId, nodeId: objNodeId, pageSize, order: orderBy, query: parseQuerey(searchQuery), keywords, desc: ascendingFlg ? \"asc\" : \"desc\" }\r\n    http.insp_094_get_process_page_with_id(params)\r\n      .then((res) => {\r\n        if (res.resultCode === 200) {\r\n          setPageNum(res.pageNum);\r\n        } else {\r\n          setIsLoading(false)\r\n        }\r\n      })\r\n      .catch((e) => {\r\n        console.log(\"exam_040_get_question_page\" + e);\r\n        setIsLoading(false)\r\n      })\r\n  }\r\n\r\n  function load_insp_092_get_process_inst_list() {\r\n    setIsLoading(true);\r\n    let params = { nodeId, teamId, pageNum: pageNum, pageSize: pageSize, order: orderBy, query: parseQuerey(searchQuery), keywords, desc: ascendingFlg ? \"asc\" : \"desc\" }\r\n    http.insp_092_get_process_inst_list(params)\r\n      .then((res) => {\r\n        if(res.resultCode == 200){\r\n          const { processInstanceList = [], total } = res;\r\n          processInstanceList.forEach(objNode => {\r\n            objNode.key = objNode.nodeId //增加key属性，表格需要用key属性\r\n            objNode.anchorNodeId = nodeId; // 项目nodeId\r\n            objNode.realNodeId = objNode.nodeId;     // 对象nodeId，兼容快捷方式\r\n            // tmsbug-10268：pc端巡检发现项列表，标题参考移动端补充列表信息\r\n            // https://os.iteam.com/#/team/9176631781/issues/9747391877011/issue/74302168893183?queryId=59221509971460\r\n            objNode.label =  objNode.chkpointName || objNode.name; // 巡检优先取值chekpointName\r\n            objNode.icon = formatSvg(getSysIconByIdType(selectionList, eSelectionListId.Selection_1939_Icon, objNode.processIcon)) // 图标(取自系统图标)\r\n            objNode.showMoreIcon = true; // 显示更多...\r\n          });\r\n          setRootNode(processInstanceList);\r\n          setTotalCnt(total);\r\n          setIsLoading(false);\r\n          if (!isEmpty(createObjNode)) { // 需要跳转至新增的节点\r\n            setTimeout(() => {\r\n              setIssueSearchParams({ viewMode, issueNodeId: createObjNode.nodeId, queryId })// 用宏任务来处理setState更新Loading存在异步的问题\r\n            })\r\n            setCreateObjNode(); // 清除待添加的节点\r\n          }\r\n        } else {\r\n          setIsLoading(false);\r\n        }\r\n      })\r\n      .catch((e) => {\r\n        console.log(e);\r\n        setIsLoading(false);\r\n      });\r\n  }\r\n\r\n  function parseQuerey(searchQuery){\r\n    let query = null;\r\n    if(!isEmpty(searchQuery)){\r\n       // 先过滤begin为空的数据\r\n       query =  searchQuery.filter(el => !isEmpty(el.begin)).map(search => {\r\n        //  const inspType = subclassAttrList.find(attr => attr.nodeId == search.col)?.inspType;\r\n        //  if(inspType == eRegionType.region || inspType == eRegionType.check){\r\n        //   return {\r\n        //       ...search,\r\n        //       // 如果是日期 则为字符串，不需要把数组变成逗号\r\n        //       begin: search.begin[search.begin.length - 1], // 取最后一个\r\n        //   }\r\n        //  }\r\n         return {\r\n              ...search,\r\n              // 如果是日期 则为字符串，不需要把数组变成逗号\r\n              begin: search.end ? search.begin : search.begin?.join(\",\"), //数组变成以逗号隔开\r\n          }\r\n        })\r\n    }\r\n    return query\r\n  }\r\n\r\n  // 选中的节点\r\n  useEffect(() => {\r\n    if (!isLoading) {\r\n      if(rootNode){\r\n        let _idx = rootNode.findIndex(question => question.nodeId == issueNodeId);\r\n        setPreviousIssueLinkDisabledFlg(pageNum == 1 && _idx == 0);\r\n        setNextIssueLinkDisabledFlg(pageNum == Math.ceil(totalCnt / pageSize) && _idx == rootNode?.length - 1);\r\n\r\n        if (totalCnt == 0) {\r\n          globalUtil.info(\"暂无数据!\");\r\n          setIssueSearchParams({ viewMode, issueNodeId: null, queryId })\r\n        } else {\r\n          // 有数据，则进行数据处理, 根据objNodeId 路由跳转question详情\r\n          if (issueNodeId) {\r\n            // 查找列表中是否有objNodeId\r\n            let foundNode = rootNode.find(question => question.nodeId == issueNodeId);\r\n            if (foundNode) {\r\n              scrollToSelectedKey(issueNodeId);\r\n              return;\r\n            } \r\n          }\r\n          const _issueNodeId = rootNode[currentIssueIdx]?.nodeId;\r\n          if(_issueNodeId){\r\n            setIssueSearchParams({ viewMode, issueNodeId: _issueNodeId, queryId })\r\n            scrollToSelectedKey(_issueNodeId);\r\n          } else { // fix tmsbug-9738:删除发现项之后，详情没有清空，页面没有自动刷新, 列表无数据的处理, TODO:如此处理，数据被删除后点击路由会默认回到第一条数据\r\n            setIssueSearchParams({ viewMode, issueNodeId: null, queryId })\r\n          }\r\n        }\r\n      } else {\r\n        setIssueSearchParams({ viewMode, issueNodeId: null, queryId })\r\n      }\r\n    } \r\n  }, [isLoading, rootNode, issueNodeId])\r\n\r\n  //  滚动至选中的节点\r\n  const scrollToSelectedKey = (objNodeId) => {\r\n    if (!!objNodeId) {\r\n      setTimeout(() => {\r\n        let target = document.querySelector(`div[data-content-key=\"${objNodeId}\"]`); // 节点 兼容长短列表\r\n        if (!isContain(target)) {\r\n          scrollToAnchor(target);\r\n        }\r\n      },500);\r\n    }\r\n  }\r\n\r\n  // 上一条\r\n  function gotoPreviousIssue() {\r\n    const currentIssueIdx = rootNode.findIndex(_issue => _issue.nodeId == issueNodeId);\r\n    if (currentIssueIdx == 0) {  //当前页的最后一行记录，再往前即需要向前翻页\r\n      if (pageNum > 1) {\r\n        setPageNum(pageNum - 1);\r\n        // globalUtil.getQueryClient().setQueryData([track010, teamId, nodeId], (pageNum)=>(pageNum - 1)); //触发 useQueryIssue017_getIssueList 再次加载\r\n        setCurrentIssueIdx(ePagination.PageSize_30 - 1);\r\n      }\r\n    } else {\r\n      setIssueSearchParams({ viewMode, issueNodeId: rootNode[currentIssueIdx - 1].nodeId });\r\n    }\r\n  }\r\n\r\n  // 下一条\r\n  function gotoNextIssue() {\r\n    const currentIssueIdx = rootNode.findIndex(_issue => _issue.nodeId == issueNodeId);\r\n    if (currentIssueIdx == pageSize - 1) { //当前页的最后一行记录，再往后即需要向后翻页\r\n      if (pageNum < Math.ceil(totalCnt / pageSize)) {\r\n        setPageNum(pageNum + 1);\r\n        // globalUtil.getQueryClient().setQueryData([track010, teamId, nodeId], (pageNo)=>(pageNo + 1)); //触发 useQueryIssue017_getIssueList 再次加载\r\n        setCurrentIssueIdx(0);\r\n      }\r\n    } else {\r\n      setIssueSearchParams({ viewMode, issueNodeId: rootNode[currentIssueIdx + 1].nodeId });\r\n    }\r\n  }\r\n\r\n  // 右击菜单事件\r\n  const onMoreBtnClick = ({ nodeItem, ctxType, ...args }) => {\r\n    objNodeListMoreOps({\r\n      teamId,\r\n      nodeType: eNodeTypeId.nt_572_insp_processList_process,\r\n      objNode: { ...nodeItem, key: nodeItem.nodeId },\r\n      ctxType,\r\n      setRootNode: setRootNode,\r\n      setTotal: setTotalCnt,\r\n      args,\r\n    });\r\n  }\r\n\r\n  const onShowMoreButtonClick = (info) => {\r\n    console.log(\"onShowMoreButtonClick info\", info)\r\n    contextboardRef.current.showContextBoard(info.event.nativeEvent || info.event, { ...info.node, nodeType: eNodeTypeId.nt_572_insp_processList_process }, info.node.nodeId, )\r\n    setSelectedKeys([...selectedKeys, info.node.nodeId?.toString()]);// 右击菜单显示时，选中当前节点\r\n    setContextKey(info.node.nodeId);\r\n  }\r\n\r\n  // react-contexify 隐藏事件，v6.0版本:废弃onHidden,改用onVisibilityChange属性，参考：https://github.com/fkhadra/react-contexify/pull/220\r\n  function handleOnVisibilityChange(isVisible) {\r\n    \r\n    if (!isVisible && contextKey != issueNodeId) {  // 右击菜单隐藏时，取消选中当前节点\r\n      let _selectedKeys = selectedKeys.filter(selectedKey => selectedKey != contextKey);\r\n      setSelectedKeys([..._selectedKeys]);\r\n    }\r\n  }\r\n\r\n  // 新建数据\r\n  const handleUpdateData = (result) => {\r\n    // 方案：新建的第一条流程，不确定在那一页（因为有排序规则的存在），所以需要先查询页码，再跳转\r\n    load_insp_094_get_process_page_with_id(result?.nodeId);\r\n    setCreateObjNode(result); // 缓存下新增的节点      \r\n  }\r\n\r\n  // 新建采集口径设置\r\n  const openCreateInspectPartitionEvent = (objInfo) => {\r\n    globalEventBus.emit(\"onCreateBtnClick\", \"\", { \r\n      nodeItem: objInfo, \r\n      ctxType: eCtxTypeId.ctx_38_create, \r\n      treeOpType: eTreeOpType.opTitle \r\n    });\r\n    \r\n  }\r\n\r\n  return <>\r\n    {/* 头部搜索栏 */}\r\n    {/* <IssueTopSearchBar  objInfo={objInfo} setting320Result={setting320Result}/> */}\r\n    <ScrollableHeader title={\r\n      <Row justify=\"space-between\" align=\"middle\">\r\n        <PageTitle teamId={teamId} nodeId={nodeId} powerLock refetchData={load_insp_092_get_process_inst_list} needShowPowerLock={needShowPowerLock}/>\r\n        <Space size={10}>\r\n          <Space className=\"issuehome-header-btns\" size={2}>\r\n            {canEditFlg && priSettingPermission(privSetting) ? (\r\n              <a className=\"fontsize-12 fontcolor-normal\" onClick={() => openCreateInspectPartitionEvent(objInfo)}>\r\n                <Space size={2}>\r\n                  <span className=\"iconfont shezhixitongshezhigongnengshezhishuxing fontsize-12\"></span>\r\n                  <span>采集口径设置</span>\r\n                </Space>\r\n              </a>\r\n            ) : (\r\n              <></>\r\n            )}\r\n          </Space>\r\n        </Space>\r\n      </Row>\r\n    }>\r\n      <InspectTopSearchBar\r\n        subclassAttrList={subclassAttrList}\r\n        allSpaceUserList={allSpaceUserList}\r\n        // TODO: 巡检其他模块暂未加当前协作群成员的限制，暂时放开限制\r\n        spaceUserList={spaceUserList}\r\n        selectionList={selectionList}\r\n        objInfo={objInfo}\r\n        keywords={keywords}\r\n        setKeywords={setKeywords}\r\n        searchQuery={searchQuery}\r\n        updateSearchQuery={updateSearchQuery}\r\n        refetchIssueList={load_insp_092_get_process_inst_list}\r\n        selectedFlow={selectedFlow}\r\n        projectProcessList={projectProcessList}\r\n        needShowPowerLock={needShowPowerLock}\r\n      />\r\n    </ScrollableHeader>\r\n    {/* 短列表/长列表/看板 视图 */}\r\n    <Layout style={{ flex: \"auto\", backgroundColor: \"#fff\", height: 0 }}>\r\n      <Spin spinning={isLoading || isLoadingCreate} wrapperClassName=\"issueSpin\">\r\n        <Outlet context={{\r\n          userList, spaceUserList, selectionList, objInfo, rootNode, totalCnt: totalCnt, pageNum, setPageNum,\r\n          searchQuery,\r\n          iconSelectionLid, setCurrentIssueIdx, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, gotoPreviousIssue, gotoNextIssue,\r\n          ascendingFlg, setAscendingFlg, orderBy, setOrderBy, viewMode, projectInfo, setting320Result,\r\n          selectedKeys, onMoreBtnClick, onShowMoreButtonClick, flowList, defaultFlow, handleUpdateData, load_insp_092_get_process_inst_list, projectProcessList, setIsLoadingCreate, useIssueSearchParams, isOA\r\n        }} />\r\n      </Spin>\r\n    </Layout>\r\n    {/* 右击菜单 */}\r\n    <ContextBoard\r\n      ref={contextboardRef}\r\n      teamId={teamId}\r\n      onMoreBtnClick={onMoreBtnClick}\r\n      // onCreateBtnClick={onCreateBtnClick} \r\n      id={\"inspect-context-menu\"}\r\n      handleOnVisibilityChange={handleOnVisibilityChange}\r\n    />\r\n    {/* 新手引导 */}\r\n    <NoviceGuide nodeType={eNodeTypeId.nt_31705_objtype_issue_list} awakeFlg={0} />\r\n  </>;\r\n}"], "mappings": ";;AAAA;AACA;AACA,SAASA,WAAW,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,QAAQ,oBAAoB;AACzG,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,WAAW,EAAEC,+BAA+B,EAAEC,UAAU,QAAQ,yBAAyB;AAClG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,OAAOC,WAAW,MAAM,yBAAyB,CAAC,CAAC;AACnD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AAC/C,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAChF,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,wCAAwC;AAC7F,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAO,KAAKC,IAAI,MAAM,kBAAkB;AACxC,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,OAAO,EAAEC,kBAAkB,QAAQ,0BAA0B;AACtE,SAASC,SAAS,EAAEC,cAAc,QAAQ,yBAAyB;AACnE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,SAAS,EAAEC,eAAe,QAAQ,iCAAiC;AAC5E,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAO,oBAAoB;AAC3B,SAASC,mCAAmC,EAAEC,kCAAkC,QAAQ,6BAA6B;AACrH,SAAUC,sCAAsC,QAAQ,mCAAmC;AAC3F,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,OAAOC,EAAE,MAAM,IAAI;AACnB,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAAQC,sCAAsC,QAAO,8CAA8C;AACnG,SAAQC,kCAAkC,QAAO,4DAA4D;AAC7G,SAASC,yBAAyB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7E,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,eAAe,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM;IAAEsC,MAAM;IAAEC;EAAO,CAAC,GAAGlC,SAAS,CAAC,CAAC;EACtC,MAAM;IAAEmC,gBAAgB;IAAEC,QAAQ,GAAG,EAAE;IAAEC,aAAa,GAAG,EAAE;IAAEC,gBAAgB,GAAG,EAAE;IAAEC,aAAa;IAAEC,OAAO;IAAEC,YAAY,EAAEC;EAAa,CAAC,GAAG5C,aAAa,CAAC,CAAC,CAAC,CAAC;EAC5J,MAAM;IAAE6C,IAAI,EAAE;MAAEC;IAAK;EAAI,CAAC,CAAC;EAAA,EACrBpB,yBAAyB,CAAC;IAACS,MAAM;IAAEC,MAAM;IAAEW,OAAO,EAAE,CAAC,CAACX;EAAM,CAAC,CAAC;EACpE,MAAMY,QAAQ,GAAGjC,eAAe,CAACkB,QAAQ,CAACgB,QAAQ,CAAC,CAAC,CAAC;;EAErD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwD,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE3C;EACA,MAAM;IAAEyD,WAAW;IAAEC,OAAO;IAAEC;EAAqB,CAAC,GAAGtD,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC/E,IAAIuD,QAAQ,GAAGhF,WAAW,CAACiF,WAAW,CAAC,CAAC;EACxC,MAAM;IAAEC,WAAW;IAAEC,iBAAiB;IAAEC;EAAQ,CAAC,GAAG1D,cAAc,CAAC;IAAEwC;EAAa,CAAC,CAAC,CAAC,CAAC;EACtF,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAACe,YAAY,CAACoD,MAAM,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzD,MAAM,CAACsE,QAAQ,EAAEC,WAAW,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC4E,4BAA4B,EAAEC,+BAA+B,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACvF,MAAM,CAAC8E,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkF,QAAQ,EAAEC,WAAW,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAW;EACxD,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAK;EACxD,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAK;EACxD,MAAM,CAACwF,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAAC0F,IAAI,EAAEC,OAAO,CAAC,GAAG3F,QAAQ,CAACsB,QAAQ,CAACoE,IAAI,CAAC,CAAC,CAAC;EACjD,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8F,eAAe,EAAEC,kBAAkB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACgG,aAAa,EAAEC,gBAAgB,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkG,iBAAiB,EAACC,oBAAoB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAEhE,MAAM;IAAE+C,IAAI,EAAEqD,aAAa;IAAER,SAAS,EAAES;EAAuB,CAAC,GAAGlF,mCAAmC,CAACkB,MAAM,CAAC,CAAC,CAAC;;EAEhH;EACA,MAAM;IAAEU,IAAI,EAAE;MAAEuD;IAAY;EAAE,CAAC,GAAGlF,kCAAkC,CAAC;IAAEiB,MAAM;IAAEC;EAAO,CAAC,CAAC;EACxF,MAAMiE,UAAU,GAAIpH,+BAA+B,CAACqH,QAAQ,CAAC5D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6D,QAAQ,CAAE;;EAEhF;EACA,MAAM;IAAE1D,IAAI,EAAE2D,WAAW,GAAG,CAAC;EAAE,CAAC,GAAGjH,QAAQ,CAACiC,sCAAsC,CAACW,MAAM,EAAEC,MAAM,CAAC,CAAC;EACnG,MAAM;IAAES,IAAI,EAAE4D,kBAAkB,GAAG,EAAE;IAAEC,aAAa,EAAEC;EAAsB,CAAC,GAAGpH,QAAQ,CAACkC,kCAAkC,CAACU,MAAM,EAAEC,MAAQ,CAAC,CAAC;;EAE9I;EACA;EACA,MAAM;IAAEwE,gBAAgB,GAAC;EAAG,CAAC,GACzBzF,sCAAsC,CAACgB,MAAM,EAAEC,MAAM,EAAEgD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyB,UAAU,EAAE,CAAC,EAACzB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEyB,UAAU,GAAEhI,UAAU,CAACiI,MAAO,CAAC;;EAEpI;AACF;AACA;AACA;AACA;AACA;EACElH,SAAS,CAAC,MAAM;IACd,IAAI,CAACkE,OAAO,EAAE;MACZiD,sCAAsC,CAACxD,WAAW,CAAC;IACrD;EACF,CAAC,EAAE,CAACO,OAAO,CAAC,CAAC;EAEblE,SAAS,CAAC,MAAI;IACZ,IAAI,CAACkE,OAAO,IAAI6C,oBAAoB,EAAE;MACpCK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAC,CAAClD,OAAO,EAAE6C,oBAAoB,CAAC,CAAC;;EAEjC;EACA/G,SAAS,CAAC,MAAM;IACfyE,WAAW,CAAC5B,aAAa,CAAC;EAC5B,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA7C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC,CAAC0F,OAAO,EAAE;MACb2B,mCAAmC,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACzB,IAAI,EAAEF,OAAO,EAAE5B,QAAQ,CAAC,CAAC;EAE7B9D,SAAS,CAAC,MAAM;IACd;IACA,IAAI+G,oBAAoB,EAAG;MACzB,MAAM,CAAC3B,QAAQ,EAAEE,WAAW,CAAC,GAAG3D,WAAW,CAACkF,kBAAkB,CAAC;MAC/DxB,WAAW,CAACD,QAAQ,CAAC;MACrBG,cAAc,CAACD,WAAW,CAAC;MAC3B,IAAGxC,OAAO,CAAC6D,QAAQ,IAAIvH,WAAW,CAACkI,qBAAqB,IAAIxE,OAAO,CAAC6D,QAAQ,IAAIvH,WAAW,CAACmI,mBAAmB,EAAC;QAAE;QAChH9B,eAAe,CAACoB,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACxC;IACF;EACF,CAAC,EAAE,CAACE,oBAAoB,CAAC,CAAC;;EAE1B;EACA/G,SAAS,CAAC,MAAM;IACd,IAAI,CAAC,CAAC2D,WAAW,IAAI,CAAC4C,sBAAsB,EAAE;MAC5CpB,eAAe,CAAC,CAACxB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6D,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLrC,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC,EAAE,CAACxB,WAAW,EAAE4C,sBAAsB,CAAC,CAAC;EAEzCvG,SAAS,CAAC,MAAI;IACZ,IAAG,CAAC,CAACqC,QAAQ,CAACoF,MAAM,EAAC;MACnB,IAAIA,MAAM,GAAGhG,EAAE,CAACiG,KAAK,CAACrF,QAAQ,CAACoF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/C,IAAG,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,aAAa,KAAI,GAAG,EAAC;QAC9BvB,oBAAoB,CAAC,IAAI,CAAC;QAC1B;MACF;IACF;IACAA,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAC,CAAChE,QAAQ,CAACoF,MAAM,CAAC,CAAC;;EAEpB;EACA,SAASL,eAAeA,CAAA,EAAI;IAAA,IAAAS,iBAAA;IAC1B,MAAMC,YAAY,GAAG,CAAC,EAAAD,iBAAA,GAAA7D,WAAW,CAAC+D,IAAI,CAACN,MAAM,IAAIA,MAAM,CAACO,GAAG,IAAIhJ,YAAY,CAAC,cAAA6I,iBAAA,uBAAtDA,iBAAA,CAAwDI,KAAK,KAAI,EAAE,EAAE,CAAC,CAAC;IAC7F,IAAG,CAAC,CAACH,YAAY,EAAC;MAChB,MAAMtC,YAAY,GAAGqB,kBAAkB,CAACkB,IAAI,CAACG,OAAO,IAAIA,OAAO,CAACJ,YAAY,IAAIA,YAAY,CAAC;MAC7FrC,eAAe,CAACD,YAAY,CAAC;IAC/B;EACF;;EAEA;EACA,SAAS2B,sCAAsCA,CAACgB,SAAS,EAAE;IACzD,IAAGtH,OAAO,CAACsH,SAAS,CAAC,EAAC;MAAE;MACnBxC,UAAU,CAAC,CAAC,CAAC;MACbE,OAAO,CAACrE,QAAQ,CAACoE,IAAI,CAAC,CAAC,CAAC,CAAC;MACzB;IACL;IACAD,UAAU,CAAC,CAAC,CAAC,CAAC;IACdI,YAAY,CAAC,IAAI,CAAC;IAClB,IAAIqC,MAAM,GAAG;MAAE7F,MAAM;MAAE8F,YAAY,EAAE7F,MAAM;MAAEA,MAAM,EAAE2F,SAAS;MAAErE,QAAQ;MAAEwE,KAAK,EAAEnE,OAAO;MAAEoE,KAAK,EAAEC,WAAW,CAACxE,WAAW,CAAC;MAAEQ,QAAQ;MAAEiE,IAAI,EAAEnE,YAAY,GAAG,KAAK,GAAG;IAAO,CAAC;IAC1K5D,IAAI,CAACgI,iCAAiC,CAACN,MAAM,CAAC,CAC3CO,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1BlD,UAAU,CAACiD,GAAG,CAAClD,OAAO,CAAC;MACzB,CAAC,MAAM;QACLK,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC,CAAC,CACD+C,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAAC,4BAA4B,GAAGF,CAAC,CAAC;MAC7ChD,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACN;EAEA,SAASsB,mCAAmCA,CAAA,EAAG;IAC7CtB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAIqC,MAAM,GAAG;MAAE5F,MAAM;MAAED,MAAM;MAAEmD,OAAO,EAAEA,OAAO;MAAE5B,QAAQ,EAAEA,QAAQ;MAAEwE,KAAK,EAAEnE,OAAO;MAAEoE,KAAK,EAAEC,WAAW,CAACxE,WAAW,CAAC;MAAEQ,QAAQ;MAAEiE,IAAI,EAAEnE,YAAY,GAAG,KAAK,GAAG;IAAO,CAAC;IACrK5D,IAAI,CAACwI,8BAA8B,CAACd,MAAM,CAAC,CACxCO,IAAI,CAAEC,GAAG,IAAK;MACb,IAAGA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAC;QACvB,MAAM;UAAEM,mBAAmB,GAAG,EAAE;UAAEC;QAAM,CAAC,GAAGR,GAAG;QAC/CO,mBAAmB,CAACE,OAAO,CAACC,OAAO,IAAI;UACrCA,OAAO,CAACC,GAAG,GAAGD,OAAO,CAAC9G,MAAM,EAAC;UAC7B8G,OAAO,CAACjB,YAAY,GAAG7F,MAAM,CAAC,CAAC;UAC/B8G,OAAO,CAACE,UAAU,GAAGF,OAAO,CAAC9G,MAAM,CAAC,CAAK;UACzC;UACA;UACA8G,OAAO,CAACG,KAAK,GAAIH,OAAO,CAACI,YAAY,IAAIJ,OAAO,CAACK,IAAI,CAAC,CAAC;UACvDL,OAAO,CAACM,IAAI,GAAG1I,SAAS,CAACJ,kBAAkB,CAACwF,aAAa,EAAEvH,gBAAgB,CAAC8K,mBAAmB,EAAEP,OAAO,CAACQ,WAAW,CAAC,CAAC,EAAC;UACvHR,OAAO,CAACS,YAAY,GAAG,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC;QACFxG,WAAW,CAAC4F,mBAAmB,CAAC;QAChC1F,WAAW,CAAC2F,KAAK,CAAC;QAClBrD,YAAY,CAAC,KAAK,CAAC;QACnB,IAAI,CAAClF,OAAO,CAACqF,aAAa,CAAC,EAAE;UAAE;UAC7B8D,UAAU,CAAC,MAAM;YACfnG,oBAAoB,CAAC;cAAET,QAAQ;cAAEO,WAAW,EAAEuC,aAAa,CAAC1D,MAAM;cAAEoB;YAAQ,CAAC,CAAC;UAChF,CAAC,CAAC;UACFuC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,MAAM;QACLJ,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC,CAAC,CACD+C,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACdhD,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACN;EAEA,SAASyC,WAAWA,CAACxE,WAAW,EAAC;IAC/B,IAAIuE,KAAK,GAAG,IAAI;IAChB,IAAG,CAAC1H,OAAO,CAACmD,WAAW,CAAC,EAAC;MACtB;MACAuE,KAAK,GAAIvE,WAAW,CAACiG,MAAM,CAACC,EAAE,IAAI,CAACrJ,OAAO,CAACqJ,EAAE,CAACjC,KAAK,CAAC,CAAC,CAACkC,GAAG,CAAC1C,MAAM,IAAI;QAAA,IAAA2C,aAAA;QACnE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACC,OAAO;UACF,GAAG3C,MAAM;UACT;UACAQ,KAAK,EAAER,MAAM,CAAC4C,GAAG,GAAG5C,MAAM,CAACQ,KAAK,IAAAmC,aAAA,GAAG3C,MAAM,CAACQ,KAAK,cAAAmC,aAAA,uBAAZA,aAAA,CAAcE,IAAI,CAAC,GAAG,CAAC,CAAE;QAChE,CAAC;MACH,CAAC,CAAC;IACN;IACA,OAAO/B,KAAK;EACd;;EAEA;EACAvI,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8F,SAAS,EAAE;MACd,IAAGxC,QAAQ,EAAC;QACV,IAAIiH,IAAI,GAAGjH,QAAQ,CAACkH,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACjI,MAAM,IAAImB,WAAW,CAAC;QACzEoB,+BAA+B,CAACW,OAAO,IAAI,CAAC,IAAI6E,IAAI,IAAI,CAAC,CAAC;QAC1DtF,2BAA2B,CAACS,OAAO,IAAIgF,IAAI,CAACC,IAAI,CAACnH,QAAQ,GAAGM,QAAQ,CAAC,IAAIyG,IAAI,IAAI,CAAAjH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsH,MAAM,IAAG,CAAC,CAAC;QAEtG,IAAIpH,QAAQ,IAAI,CAAC,EAAE;UACjBrE,UAAU,CAAC0L,IAAI,CAAC,OAAO,CAAC;UACxBhH,oBAAoB,CAAC;YAAET,QAAQ;YAAEO,WAAW,EAAE,IAAI;YAAEC;UAAQ,CAAC,CAAC;QAChE,CAAC,MAAM;UAAA,IAAAkH,qBAAA;UACL;UACA,IAAInH,WAAW,EAAE;YACf;YACA,IAAIoH,SAAS,GAAGzH,QAAQ,CAACyE,IAAI,CAAC0C,QAAQ,IAAIA,QAAQ,CAACjI,MAAM,IAAImB,WAAW,CAAC;YACzE,IAAIoH,SAAS,EAAE;cACbC,mBAAmB,CAACrH,WAAW,CAAC;cAChC;YACF;UACF;UACA,MAAMsH,YAAY,IAAAH,qBAAA,GAAGxH,QAAQ,CAACsB,eAAe,CAAC,cAAAkG,qBAAA,uBAAzBA,qBAAA,CAA2BtI,MAAM;UACtD,IAAGyI,YAAY,EAAC;YACdpH,oBAAoB,CAAC;cAAET,QAAQ;cAAEO,WAAW,EAAEsH,YAAY;cAAErH;YAAQ,CAAC,CAAC;YACtEoH,mBAAmB,CAACC,YAAY,CAAC;UACnC,CAAC,MAAM;YAAE;YACPpH,oBAAoB,CAAC;cAAET,QAAQ;cAAEO,WAAW,EAAE,IAAI;cAAEC;YAAQ,CAAC,CAAC;UAChE;QACF;MACF,CAAC,MAAM;QACLC,oBAAoB,CAAC;UAAET,QAAQ;UAAEO,WAAW,EAAE,IAAI;UAAEC;QAAQ,CAAC,CAAC;MAChE;IACF;EACF,CAAC,EAAE,CAACkC,SAAS,EAAExC,QAAQ,EAAEK,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAMqH,mBAAmB,GAAI7C,SAAS,IAAK;IACzC,IAAI,CAAC,CAACA,SAAS,EAAE;MACf6B,UAAU,CAAC,MAAM;QACf,IAAIkB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,yBAAyBjD,SAAS,IAAI,CAAC,CAAC,CAAC;QAC7E,IAAI,CAACpH,SAAS,CAACmK,MAAM,CAAC,EAAE;UACtBlK,cAAc,CAACkK,MAAM,CAAC;QACxB;MACF,CAAC,EAAC,GAAG,CAAC;IACR;EACF,CAAC;;EAED;EACA,SAASG,iBAAiBA,CAAA,EAAG;IAC3B,MAAMzG,eAAe,GAAGtB,QAAQ,CAACkH,SAAS,CAACc,MAAM,IAAIA,MAAM,CAAC9I,MAAM,IAAImB,WAAW,CAAC;IAClF,IAAIiB,eAAe,IAAI,CAAC,EAAE;MAAG;MAC3B,IAAIc,OAAO,GAAG,CAAC,EAAE;QACfC,UAAU,CAACD,OAAO,GAAG,CAAC,CAAC;QACvB;QACAb,kBAAkB,CAAC/F,WAAW,CAACiF,WAAW,GAAG,CAAC,CAAC;MACjD;IACF,CAAC,MAAM;MACLF,oBAAoB,CAAC;QAAET,QAAQ;QAAEO,WAAW,EAAEL,QAAQ,CAACsB,eAAe,GAAG,CAAC,CAAC,CAACpC;MAAO,CAAC,CAAC;IACvF;EACF;;EAEA;EACA,SAAS+I,aAAaA,CAAA,EAAG;IACvB,MAAM3G,eAAe,GAAGtB,QAAQ,CAACkH,SAAS,CAACc,MAAM,IAAIA,MAAM,CAAC9I,MAAM,IAAImB,WAAW,CAAC;IAClF,IAAIiB,eAAe,IAAId,QAAQ,GAAG,CAAC,EAAE;MAAE;MACrC,IAAI4B,OAAO,GAAGgF,IAAI,CAACC,IAAI,CAACnH,QAAQ,GAAGM,QAAQ,CAAC,EAAE;QAC5C6B,UAAU,CAACD,OAAO,GAAG,CAAC,CAAC;QACvB;QACAb,kBAAkB,CAAC,CAAC,CAAC;MACvB;IACF,CAAC,MAAM;MACLhB,oBAAoB,CAAC;QAAET,QAAQ;QAAEO,WAAW,EAAEL,QAAQ,CAACsB,eAAe,GAAG,CAAC,CAAC,CAACpC;MAAO,CAAC,CAAC;IACvF;EACF;;EAEA;EACA,MAAMgJ,cAAc,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,OAAO;IAAE,GAAGC;EAAK,CAAC,KAAK;IACzD/K,kBAAkB,CAAC;MACjB2B,MAAM;MACNoE,QAAQ,EAAEvH,WAAW,CAACwM,+BAA+B;MACrDtC,OAAO,EAAE;QAAE,GAAGmC,QAAQ;QAAElC,GAAG,EAAEkC,QAAQ,CAACjJ;MAAO,CAAC;MAC9CkJ,OAAO;MACPnI,WAAW,EAAEA,WAAW;MACxBsI,QAAQ,EAAEpI,WAAW;MACrBkI;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,qBAAqB,GAAIjB,IAAI,IAAK;IAAA,IAAAkB,iBAAA;IACtC/C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4B,IAAI,CAAC;IAC/CvI,eAAe,CAAC0J,OAAO,CAACC,gBAAgB,CAACpB,IAAI,CAACqB,KAAK,CAACC,WAAW,IAAItB,IAAI,CAACqB,KAAK,EAAE;MAAE,GAAGrB,IAAI,CAACuB,IAAI;MAAEzF,QAAQ,EAAEvH,WAAW,CAACwM;IAAgC,CAAC,EAAEf,IAAI,CAACuB,IAAI,CAAC5J,MAAQ,CAAC;IAC3K2C,eAAe,CAAC,CAAC,GAAGD,YAAY,GAAA6G,iBAAA,GAAElB,IAAI,CAACuB,IAAI,CAAC5J,MAAM,cAAAuJ,iBAAA,uBAAhBA,iBAAA,CAAkBvE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE7C,aAAa,CAACkG,IAAI,CAACuB,IAAI,CAAC5J,MAAM,CAAC;EACjC,CAAC;;EAED;EACA,SAAS6J,wBAAwBA,CAACC,SAAS,EAAE;IAE3C,IAAI,CAACA,SAAS,IAAI5H,UAAU,IAAIf,WAAW,EAAE;MAAG;MAC9C,IAAI4I,aAAa,GAAGrH,YAAY,CAAC+E,MAAM,CAACuC,WAAW,IAAIA,WAAW,IAAI9H,UAAU,CAAC;MACjFS,eAAe,CAAC,CAAC,GAAGoH,aAAa,CAAC,CAAC;IACrC;EACF;;EAEA;EACA,MAAME,gBAAgB,GAAIC,MAAM,IAAK;IACnC;IACAvF,sCAAsC,CAACuF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAElK,MAAM,CAAC;IACtD2D,gBAAgB,CAACuG,MAAM,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMC,+BAA+B,GAAI7J,OAAO,IAAK;IACnDvD,cAAc,CAACqN,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE;MAC1CnB,QAAQ,EAAE3I,OAAO;MACjB4I,OAAO,EAAEpM,UAAU,CAACuN,aAAa;MACjCC,UAAU,EAAE5N,WAAW,CAAC6N;IAC1B,CAAC,CAAC;EAEJ,CAAC;EAED,oBAAO/K,OAAA,CAAAE,SAAA;IAAA8K,QAAA,gBAGLhL,OAAA,CAACN,gBAAgB;MAACuL,KAAK,eACrBjL,OAAA,CAAClC,GAAG;QAACoN,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAH,QAAA,gBACzChL,OAAA,CAACtC,SAAS;UAAC6C,MAAM,EAAEA,MAAO;UAACC,MAAM,EAAEA,MAAO;UAAC4K,SAAS;UAACC,WAAW,EAAEhG,mCAAoC;UAACjB,iBAAiB,EAAEA;QAAkB;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eAC9IzL,OAAA,CAACjC,KAAK;UAAC2N,IAAI,EAAE,EAAG;UAAAV,QAAA,eACdhL,OAAA,CAACjC,KAAK;YAAC4N,SAAS,EAAC,uBAAuB;YAACD,IAAI,EAAE,CAAE;YAAAV,QAAA,EAC9CvG,UAAU,IAAIjH,oBAAoB,CAACgH,WAAW,CAAC,gBAC9CxE,OAAA;cAAG2L,SAAS,EAAC,8BAA8B;cAACC,OAAO,EAAEA,CAAA,KAAMjB,+BAA+B,CAAC7J,OAAO,CAAE;cAAAkK,QAAA,eAClGhL,OAAA,CAACjC,KAAK;gBAAC2N,IAAI,EAAE,CAAE;gBAAAV,QAAA,gBACbhL,OAAA;kBAAM2L,SAAS,EAAC;gBAA8D;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtFzL,OAAA;kBAAAgL,QAAA,EAAM;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,gBAEJzL,OAAA,CAAAE,SAAA,mBAAI;UACL;YAAAoL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;MAAAT,QAAA,eACChL,OAAA,CAACvB,mBAAmB;QAClBuG,gBAAgB,EAAEA,gBAAiB;QACnCpE,gBAAgB,EAAEA;QAClB;QAAA;QACAD,aAAa,EAAEA,aAAc;QAC7B2D,aAAa,EAAEA,aAAc;QAC7BxD,OAAO,EAAEA,OAAQ;QACjB0B,QAAQ,EAAEA,QAAS;QACnBC,WAAW,EAAEA,WAAY;QACzBT,WAAW,EAAEA,WAAY;QACzBC,iBAAiB,EAAEA,iBAAkB;QACrC4J,gBAAgB,EAAExG,mCAAoC;QACtD7B,YAAY,EAAEA,YAAa;QAC3BqB,kBAAkB,EAAEA,kBAAmB;QACvCT,iBAAiB,EAAEA;MAAkB;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAEnBzL,OAAA,CAACpC,MAAM;MAACkO,KAAK,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,eAAe,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAE,CAAE;MAAAjB,QAAA,eAClEhL,OAAA,CAACnC,IAAI;QAACqO,QAAQ,EAAEpI,SAAS,IAAIE,eAAgB;QAACmI,gBAAgB,EAAC,WAAW;QAAAnB,QAAA,eACxEhL,OAAA,CAAC7B,MAAM;UAACiO,OAAO,EAAE;YACf1L,QAAQ;YAAEC,aAAa;YAAE2D,aAAa;YAAExD,OAAO;YAAEQ,QAAQ;YAAEE,QAAQ,EAAEA,QAAQ;YAAEkC,OAAO;YAAEC,UAAU;YAClG3B,WAAW;YACXN,gBAAgB;YAAEmB,kBAAkB;YAAEC,4BAA4B;YAAEE,wBAAwB;YAAEqG,iBAAiB;YAAEE,aAAa;YAC9HjH,YAAY;YAAEC,eAAe;YAAEJ,OAAO;YAAEC,UAAU;YAAEhB,QAAQ;YAAEwD,WAAW;YAAEnE,gBAAgB;YAC3FyC,YAAY;YAAEsG,cAAc;YAAEM,qBAAqB;YAAE1G,QAAQ;YAAEE,WAAW;YAAEmH,gBAAgB;YAAEpF,mCAAmC;YAAER,kBAAkB;YAAEZ,kBAAkB;YAAE1F,oBAAoB;YAAE2C;UACnM;QAAE;UAAAoK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAETzL,OAAA,CAACrB,YAAY;MACX0N,GAAG,EAAE/L,eAAgB;MACrBC,MAAM,EAAEA,MAAO;MACfiJ,cAAc,EAAEA;MAChB;MAAA;MACA8C,EAAE,EAAE,sBAAuB;MAC3BjC,wBAAwB,EAAEA;IAAyB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eAEFzL,OAAA,CAACvC,WAAW;MAACkH,QAAQ,EAAEvH,WAAW,CAACmP,2BAA4B;MAACC,QAAQ,EAAE;IAAE;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAC/E,CAAC;AACL;AAACrL,EAAA,CApYuBD,WAAW;EAAA,QAChB9B,WAAW,EAEDC,SAAS,EACuGF,aAAa,EAElJ0B,yBAAyB,EAGCV,QAAQ,EAKeb,oBAAoB,EAEvBC,cAAc,EAmBCa,mCAAmC,EAGpEC,kCAAkC,EAIjC3B,QAAQ,EACqCA,QAAQ,EAKpF4B,sCAAsC;AAAA;AAAAkN,EAAA,GAhDpBtM,WAAW;AAAA,IAAAsM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}