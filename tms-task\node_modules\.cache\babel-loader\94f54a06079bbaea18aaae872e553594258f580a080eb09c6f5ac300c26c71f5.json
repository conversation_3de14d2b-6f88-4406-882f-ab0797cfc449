{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\AppNoticeIcon\\\\AppNoticeIcon.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useMemo } from \"react\";\nimport { Badge, Button, Checkbox, Drawer, Dropdown, List, Menu, Popover, Space, Modal, Avatar, Popconfirm } from \"antd\";\nimport { useNavigate, useParams, Link } from \"react-router-dom\";\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\nimport { getOptionByType } from \"@common/utils/TsbConfig\";\nimport * as httpCommon from \"@common/api/http\";\nimport \"./AppNoticeIcon.scss\";\nimport AppNoticeIconAllModal from \"./AppNoticeIconAllModal\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\nimport CreateTeamModal, { CREATETYPE_UPGRADE } from \"@components/CreateTeam\";\nimport moment from \"moment\";\nimport { getUrlByModule } from '@common/utils/logicUtils';\nimport { useDispatch } from \"react-redux\";\nimport { getTeamList } from \"src/team/store/actionCreators\";\nimport { CheckCircleOutlined } from '@ant-design/icons';\nimport { team_578_get_notify_query, /* setting_125_refresh_team_query */useQuery_setting_126_get_setting_teams } from \"@common/api/query/query\";\nimport { useThrottle } from \"@common/hook/useThrottle\";\nimport { eProductGroupId, eProductStatus, eSysNotifierOpType } from \"@common/utils/enum\";\nimport DraggableDrawer from \"@components/DraggableDrawer\";\nimport SettingsDrawer from \"@/settings/views/SettingsDrawer\";\n//系统通知 \nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppNoticeIcon(props) {\n  _s();\n  const {\n    noticeVisible,\n    setNoticeVisible\n  } = props;\n  //const queryClient = useQueryClient();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    teamId\n  } = useParams();\n  const [showUnReadView, setShowUnReadView] = useState(true);\n  // const {data: selectionList, isLoading: isLoadingCodeValueList} = useQuerySetting407_getCodeValueList(teamId); // 字典数据\n  //const { data, isLoading } = useQueryTeam557GetSysNotification()\n  const {\n    data: teamData\n  } = useQuery({\n    ...useQuery_setting_126_get_setting_teams()\n  });\n  const teamList = (teamData === null || teamData === void 0 ? void 0 : teamData.teams) || [];\n  const {\n    data: notifyData,\n    refetch: refetchNotify,\n    dataUpdatedAt\n  } = useQuery({\n    //isLoading,\n    ...team_578_get_notify_query(teamId)\n  });\n  // const { data: setting125Data } = useQuery({\n  //   ...setting_125_refresh_team_query(teamId),\n  //   enabled: false\n  // })\n  const [appNoticeIconVisible, setAppNoticeIconVisible] = useState(false);\n  const [showMessage, _setShowMessage] = useState(false);\n  const [dropDownOpenId, setDropDownOpenId] = useState(null);\n  const [createTeamModalVisible, setCreateTeamModalVisible] = useState(false);\n  // const [systemUpdateTips,setSystemUpdateTips] = useState(true);\n  const [productListFinal, setProductListFinal] = useState([]);\n  //const autoClosePopRef = useRef(false);\n\n  const [settingsDrawerVisible, setSettingsDrawerVisible] = useState(false); //打开团队设置->应用管理\n  const [settingsDrawerTab, setSettingsDrawerTab] = useState('product'); //默认打开 应用管理\n  const [productId, setProductId] = useState(); //打开应用授权，具体的某一个应用\n\n  const setShowMessage = useThrottle(value => {\n    _setShowMessage(value);\n  }, 200);\n  useEffect(() => {\n    getProductsList();\n  }, []);\n  useEffect(() => {\n    if (noticeVisible) {\n      setShowMessage(true);\n    }\n  }, [noticeVisible]);\n  useEffect(() => {\n    // 通知自动弹出的条件\n    // 1、系统身份信息已填写\n    // 2、团队个人信息已填写\n    // 3、第一次的新手向导已做\n    if ((notifyData === null || notifyData === void 0 ? void 0 : notifyData.needPopupFlg) == 1 && (notifyData === null || notifyData === void 0 ? void 0 : notifyData.autoPopupFlg) == 1 /* && !setting125Data?.firstEntryFlg && setting125Data?.bindMobileFlg && setting125Data?.trialReminderFlg */) {\n      if (!showMessage) {\n        setShowMessage(true);\n      }\n    }\n  }, [dataUpdatedAt]);\n  function getProductsList() {\n    httpCommon.team_711_get_team_product_list({\n      teamId: teamId\n    }).then(res => {\n      if (res.resultCode == 200) {\n        let allProductsList = (res.productList || []).filter(product => product.freeFlg == 0 && product.groupId != eProductGroupId.Pgid_1_OS && product.statusType != eProductStatus.Status_3_Unreleased).map(product => {\n          product.key = product.productId;\n          return product;\n        });\n        productListFormat(allProductsList);\n      }\n    });\n  }\n  function productListFormat(productList = []) {\n    let packageList = [];\n    productList.forEach(product => {\n      let item = packageList.find(_product => product.groupId == _product.groupId);\n      if (!item) {\n        packageList.push({\n          groupName: product.groupName,\n          groupId: product.groupId,\n          groupList: [product]\n        });\n      } else {\n        item.groupList.push(product);\n      }\n    });\n    let allProductsList = [];\n    packageList.forEach(group => {\n      let groupList = group.groupList.map((config, index) => {\n        var _productListFinal$fin, _productListFinal$fin2;\n        return {\n          ...config,\n          key: config.productId,\n          isRowSpan: index == 0 ? true : false,\n          groupListLength: index == 0 ? group.groupList.length : 0,\n          authCntDesc: !!config.authCntDesc ? config.authCntDesc : ((_productListFinal$fin = productListFinal.find(product => product.productId == config.productId)) === null || _productListFinal$fin === void 0 ? void 0 : _productListFinal$fin.authCntDesc) || '',\n          objCntDesc: !!config.objCntDesc ? config.objCntDesc : ((_productListFinal$fin2 = productListFinal.find(product => product.productId == config.productId)) === null || _productListFinal$fin2 === void 0 ? void 0 : _productListFinal$fin2.objCntDesc) || ''\n        };\n      });\n      allProductsList = allProductsList.concat(groupList);\n    });\n    setProductListFinal([...allProductsList]);\n  }\n\n  // 删除单条通知\n  function deleteNotic(item, refetch557) {\n    let params = {\n      teamId: teamId,\n      opType: \"phy_delete\",\n      notifyIds: [item.id]\n    };\n    httpCommon.team_558_update_notification_status(params).then(res => {\n      if (res.resultCode === 200) {\n        globalUtil.success('删除成功');\n        refetch557 && refetch557();\n      }\n    });\n  }\n\n  //通知设置为已读\n  function readNotics(id, opType, refetch557) {\n    let params = {\n      teamId: teamId,\n      opType: \"update\",\n      notifyIds: [id]\n    };\n    if (!!opType) {\n      params.notifyType = opType;\n    }\n    httpCommon.team_558_update_notification_status(params).then(res => {\n      if (res.resultCode === 200) {\n        globalUtil.success('本消息已读');\n        refetch557 && refetch557();\n        refetchNotify();\n      }\n    });\n  }\n  function isLink(item) {\n    var _item$supplierMap, _item$supplierMap2, _item$supplierMap3;\n    if (item.opType == eSysNotifierOpType.op_1_comment || item.opType == eSysNotifierOpType.op_2_at_me || item.opType == eSysNotifierOpType.op_6_like) {\n      return true;\n    } else if (item.opType == eSysNotifierOpType.op_11_approve_mbr_join && item !== null && item !== void 0 && (_item$supplierMap = item.supplierMap) !== null && _item$supplierMap !== void 0 && _item$supplierMap.groupId) {\n      //申请加入\n      return true;\n    } else if (item.opType == eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited && item !== null && item !== void 0 && (_item$supplierMap2 = item.supplierMap) !== null && _item$supplierMap2 !== void 0 && _item$supplierMap2.teamId) {\n      //手机/邮箱邀请\n      return true;\n    } else if (item.opType == eSysNotifierOpType.op_25_product_auth) {\n      //应用授权\n      if ((item === null || item === void 0 ? void 0 : item.teamId) == teamId) return false;else return true;\n    } else if (item.opType == eSysNotifierOpType.op_27_set_kpi_stop_bpmn && item !== null && item !== void 0 && (_item$supplierMap3 = item.supplierMap) !== null && _item$supplierMap3 !== void 0 && _item$supplierMap3.teamId) {\n      // 任务停止流程尚未配置完全\n      return true;\n    } else {\n      return false;\n    }\n  }\n  function goToNewPage(item, refetch557) {\n    var _item$supplierMap4, _item$supplierMap5, _item$supplierMap6;\n    setDropDownOpenId(null);\n    switch (item.opType) {\n      case eSysNotifierOpType.op_1_comment:\n        //评论\n        // let url = getLinkUrl(item);\n        // navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\n        btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_2_at_me:\n        //去到被@的节点\n        // let url = getLinkUrl(item);\n        // navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\n        btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_6_like:\n        //点赞\n        // let url = getLinkUrl(item);\n        // navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\n        btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_11_approve_mbr_join:\n        //去审批\n        // item?.supplierMap?.spaceId && item?.supplierMap?.groupId && navigate(`/${teamId}/settings/space/${item.supplierMap.spaceId}/member/approval/${item.supplierMap.groupId}/2`);\n        !!(item !== null && item !== void 0 && (_item$supplierMap4 = item.supplierMap) !== null && _item$supplierMap4 !== void 0 && _item$supplierMap4.groupId) && btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_13_add_mbr_user_quota:\n        //团队成员扩容\n        setCreateTeamModalVisible(true);\n        btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_14_buy_vip_expiration_dt:\n        //续费\n        setCreateTeamModalVisible(true);\n        btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_15_buy_cdisk_traffic:\n        //云盘扩容\n        globalUtil.error('待开放');\n        break;\n      case eSysNotifierOpType.op_17_accept_invite:\n        //接受邀请\n        (item === null || item === void 0 ? void 0 : item.objId) && (item === null || item === void 0 ? void 0 : (_item$supplierMap5 = item.supplierMap) === null || _item$supplierMap5 === void 0 ? void 0 : _item$supplierMap5.inviteTeamId) && acceptInvitation(item, refetch557);\n        break;\n      case eSysNotifierOpType.op_18_buy:\n        //购买\n        setCreateTeamModalVisible(true);\n        btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_22_upgrade_to_vip:\n        //升级企业版\n        setCreateTeamModalVisible(true);\n        btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited:\n        //进入团队\n        (item === null || item === void 0 ? void 0 : (_item$supplierMap6 = item.supplierMap) === null || _item$supplierMap6 === void 0 ? void 0 : _item$supplierMap6.teamId) && btnClick(item, item.opType, refetch557);\n        break;\n      case eSysNotifierOpType.op_25_product_auth:\n        //产品授权\n        if (teamId == item.teamId) {\n          //授权的请求，正好是本团队的请求，直接打开 SettingsDrawer的应用管理\n          if (appNoticeIconVisible) {\n            setAppNoticeIconVisible(false);\n          }\n          if (noticeVisible) {\n            setNoticeVisible(false); //关闭本身(系统通知)Drawer\n          }\n          // setSettingsDrawerTab('product')\n          setProductId(item.objId); //objId存储的就是productId,如21为文档/文档库\n          setSettingsDrawerVisible(true);\n        } else {\n          btnClick(item, 0, refetch557); //继续走default路径\n        }\n        break;\n      default:\n        btnClick(item, 0, refetch557);\n        break;\n    }\n  }\n  function btnClick(item, opType, refetch557) {\n    // let closeList = [2,11,13,14,15,17,18,22,23]\n    // if(closeList.find(objType => objType == item.opType)){\n    //   setAppNoticeIconVisible(false)\n    // }\n    if (notifyData.notificationReminders.find(remind => remind.id == item.id && remind.opStatus == 1)) {\n      readNotics(item.id, opType, refetch557);\n    }\n  }\n  function acceptInvitation(item, refetch557) {\n    httpCommon.setting_217_add_user_by_invite_url({\n      teamId: item.supplierMap.inviteTeamId,\n      inviteId: item.objId\n    }).then(res => {\n      if (res.resultCode == 200) {\n        //刷新团队列表\n        btnClick(item, eSysNotifierOpType.op_17_accept_invite, refetch557);\n        dispatch(getTeamList());\n        joinTeamSuccess(item.supplierMap.inviteTeamId, item.objTitle);\n      }\n    });\n  }\n  function joinTeamSuccess(_teamId, newTeamName) {\n    Modal.confirm({\n      title: '提示',\n      icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n        style: {\n          color: '#52c41a'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u56E2\\u961F\\u52A0\\u5165\\u6210\\u529F\\uFF0C\\u662F\\u5426\\u5207\\u6362\\u81F3 \", newTeamName.substring(0, newTeamName.indexOf('团队的')), \" \\uFF1F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u70B9\\u51FB\\\"\\u5426\\\"\\uFF0C\\u505C\\u7559\\u5728\\u5F53\\u524D\\u9875\\u9762\\uFF1B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u70B9\\u51FB\\\"\\u662F\\\"\\uFF0C\\u5207\\u6362\\u81F3\\u65B0\\u7684\\u56E2\\u961F\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 16\n      }, this),\n      okText: '是，切换团队',\n      cancelText: '否',\n      onOk: () => {\n        navigate(`/team/${_teamId}`);\n      },\n      onCancel: () => {}\n    });\n  }\n  const getLinkUrl = item => {\n    var _item$supplierMap9, _item$supplierMap10, _item$supplierMap11, _item$supplierMap12;\n    if (item.opType == eSysNotifierOpType.op_11_approve_mbr_join) {\n      var _item$supplierMap7;\n      // 申请加入 去审批\n      if (!!(item !== null && item !== void 0 && (_item$supplierMap7 = item.supplierMap) !== null && _item$supplierMap7 !== void 0 && _item$supplierMap7.spaceId)) {\n        return `/${item.teamId}/settings/space/${item.supplierMap.spaceId}/member/approval/${item.supplierMap.groupId}/2`;\n      }\n      return `/${item.teamId}/settings/user/member/approval/${item.supplierMap.groupId}/2`;\n    }\n    if (item.opType == eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited) {\n      var _item$supplierMap8;\n      //手机/邮箱邀请 进入团队\n      if (!!((_item$supplierMap8 = item.supplierMap) !== null && _item$supplierMap8 !== void 0 && _item$supplierMap8.teamId)) {\n        return `/team/${item.supplierMap.teamId}`;\n      }\n      return ``;\n    }\n    if (item.opType == eSysNotifierOpType.op_25_product_auth) {\n      //去授权\n      //item.objId 存放 productId\n      return `/${item.teamId}/settings/product/${item.objId}`;\n    }\n    if (item.opType == eSysNotifierOpType.op_27_set_kpi_stop_bpmn) {\n      // 任务停止流程尚未配置完全\n      if (!!item.teamId) {\n        return `/team/${item.supplierMap.teamId}/stopTasks/${item.supplierMap.nodeId}`;\n      }\n      return ``;\n    }\n    let obj = {\n      nodeType: (_item$supplierMap9 = item.supplierMap) === null || _item$supplierMap9 === void 0 ? void 0 : _item$supplierMap9.objType,\n      anchorNodeId: (_item$supplierMap10 = item.supplierMap) === null || _item$supplierMap10 === void 0 ? void 0 : _item$supplierMap10.anchorNodeId,\n      nodeId: (_item$supplierMap11 = item.supplierMap) === null || _item$supplierMap11 === void 0 ? void 0 : _item$supplierMap11.nodeId\n    };\n    let no_hash_url = getUrlByModule(item.teamId, {\n      ...obj\n    });\n    let hash_url = item.objType == 1000 && (_item$supplierMap12 = item.supplierMap) !== null && _item$supplierMap12 !== void 0 && _item$supplierMap12.commentId ? `#comment-${item.supplierMap.commentId}` : '';\n    return no_hash_url + hash_url;\n  };\n  const getFirstName = teamName => {\n    try {\n      return teamName.substring(0, 1);\n    } catch (e) {\n      return \"\";\n    }\n  };\n  const onOpenChange = open => {\n    if (!open) {\n      setDropDownOpenId(null);\n      if (open != showMessage) {\n        setTimeout(() => {\n          setShowMessage(false);\n          setNoticeVisible(false);\n        }, 300); //20250801 2000 -> 300, 2秒太久\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Popover, {\n      open: showMessage,\n      placement: \"bottom\",\n      overlayClassName: \"noticPopover \" + (appNoticeIconVisible ? \"noticPopover-999\" : \"\"),\n      trigger: 'click',\n      onOpenChange: onOpenChange,\n      title: /*#__PURE__*/_jsxDEV(NoticeTitle, {\n        teamId: teamId,\n        setAppNoticeIconVisible: setAppNoticeIconVisible,\n        setNoticeVisible: setNoticeVisible,\n        closePopover: () => {\n          setShowMessage(false);\n          setNoticeVisible(false);\n          setDropDownOpenId(null);\n        },\n        showUnReadView: showUnReadView,\n        setShowUnReadView: setShowUnReadView\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 16\n      }, this),\n      content: showMessage ? /*#__PURE__*/_jsxDEV(NoticeContent, {\n        teamId: teamId,\n        goToNewPage: goToNewPage,\n        isLink: isLink,\n        dropDownOpenId: dropDownOpenId,\n        setDropDownOpenId: setDropDownOpenId,\n        getLinkUrl: getLinkUrl,\n        getFirstName: getFirstName,\n        teamList: teamList,\n        showUnReadView: showUnReadView\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false),\n      children: /*#__PURE__*/_jsxDEV(Badge, {\n        offset: [-5, 5],\n        size: \"small\",\n        count: ((notifyData === null || notifyData === void 0 ? void 0 : notifyData.notificationReminders) || []).filter(reminder => reminder.opStatus == 1).length,\n        showZero: false,\n        ...props,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          shape: \"circle\",\n          icon: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `iconfont ${(notifyData === null || notifyData === void 0 ? void 0 : notifyData.autoPopupFlg) == 1 ? \"tongzhi1\" : \"tongzhibutixin\"} `\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 52\n          }, this),\n          onClick: () => {\n            setShowMessage(!showMessage);\n            setNoticeVisible(false);\n          },\n          className: \"header-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n      title: /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: \"orange\"\n        },\n        className: \"iconfont tongzhi1\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: \"#666\",\n            marginLeft: 10\n          },\n          children: \"\\u7CFB\\u7EDF\\u901A\\u77E5 - \\u5168\\u90E8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 11\n      }, this),\n      className: \"AppNoticeIconAllModl-drawer\",\n      centered: true,\n      open: appNoticeIconVisible,\n      destroyOnClose: true,\n      onClose: () => {\n        setShowMessage(false);\n        setNoticeVisible(false);\n        setDropDownOpenId(null);\n        setTimeout(() => {\n          setAppNoticeIconVisible(false);\n        }, 300);\n      },\n      width: 800,\n      footer: false,\n      children: /*#__PURE__*/_jsxDEV(AppNoticeIconAllModal, {\n        teamId: teamId,\n        isLink: isLink,\n        getLinkUrl: getLinkUrl,\n        deleteNotic: deleteNotic,\n        goToNewPage: goToNewPage,\n        getFirstName: getFirstName,\n        refetchNotify: refetchNotify,\n        teamList: teamList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateTeamModal, {\n      teamId: teamId,\n      type: CREATETYPE_UPGRADE,\n      visible: createTeamModalVisible,\n      onCancel: () => setCreateTeamModalVisible(false),\n      onOk: () => setCreateTeamModalVisible(false),\n      productList: productListFinal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingsDrawer, {\n      visible: settingsDrawerVisible,\n      onClose: () => setSettingsDrawerVisible(false),\n      teamId: teamId,\n      defaultTab: settingsDrawerTab,\n      productId: productId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 330,\n    columnNumber: 5\n  }, this);\n}\n\n// the content of notice\n_s(AppNoticeIcon, \"YQ7pySL7bXoasdOjBj6VBBF3MDI=\", false, function () {\n  return [useNavigate, useDispatch, useParams, useQuery, useQuery_setting_126_get_setting_teams, useQuery, useThrottle];\n});\n_c = AppNoticeIcon;\nfunction NoticeTitle({\n  teamId,\n  setAppNoticeIconVisible,\n  setNoticeVisible,\n  closePopover,\n  showUnReadView,\n  setShowUnReadView\n}) {\n  _s2();\n  const queryClient = useQueryClient();\n  const {\n    data,\n    refetch: refetchNotify,\n    dataUpdatedAt: team578DataUpdatedAt\n  } = useQuery({\n    ...team_578_get_notify_query(teamId),\n    enabled: false,\n    refetchInterval: false\n  });\n  const [ignorePopoverVisible, setIgnorePopoverVisible] = useState(false);\n  const onAutoEjectChange = e => {\n    let autoPopupFlg = e.target.checked ? 1 : 0;\n    httpCommon.team_563_set_notify_auto_popup({\n      teamId,\n      autoPopupFlg\n    }).then(result => {\n      if (result.resultCode == 200) {\n        queryClient.setQueryData(team_578_get_notify_query(teamId).queryKey, oldData => {\n          return {\n            ...oldData,\n            autoPopupFlg\n          };\n        });\n      }\n    });\n  };\n  const onShowUnReadChange = e => {\n    setShowUnReadView(e.target.checked);\n  };\n\n  // 忽略全部通知\n  const ignoreNotic = () => {\n    let params = {\n      teamId: teamId,\n      opType: \"delete\",\n      notifyIds: []\n    };\n    httpCommon.team_558_update_notification_status(params).then(result => {\n      if (result.resultCode == 200) {\n        refetchNotify();\n        setIgnorePopoverVisible(false);\n      }\n    });\n  };\n  const notifyCount = useMemo(() => {\n    var _data$notificationRem;\n    return ((_data$notificationRem = data === null || data === void 0 ? void 0 : data.notificationReminders) !== null && _data$notificationRem !== void 0 ? _data$notificationRem : []).filter(item => {\n      if (showUnReadView) {\n        return item.opStatus == 1;\n      } else {\n        return true;\n      }\n    }).length;\n  }, [showUnReadView, team578DataUpdatedAt]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"noticPopover-title\",\n    children: [/*#__PURE__*/_jsxDEV(Space, {\n      size: 10,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"noticPopover-title-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"iconfont tongzhi1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"noticPopover-title-text\",\n          children: [\"\\u7CFB\\u7EDF\\u901A\\u77E5\", notifyCount ? `(${notifyCount})` : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n        className: \"noticPopover-title-check\",\n        checked: data === null || data === void 0 ? void 0 : data.autoPopupFlg,\n        onChange: onAutoEjectChange,\n        children: \"\\u81EA\\u52A8\\u5F39\\u51FA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n        className: \"noticPopover-title-check\",\n        checked: showUnReadView,\n        onChange: onShowUnReadChange,\n        children: \"\\u53EA\\u770B\\u672A\\u8BFB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        overlayClassName: \"ignorePopover\",\n        title: `忽略全部(${notifyCount}条)通知?`,\n        placement: 'right',\n        trigger: 'click',\n        open: ignorePopoverVisible,\n        onConfirm: ignoreNotic,\n        onCancel: () => {\n          setIgnorePopoverVisible(false);\n        },\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          className: \"noticPopover-title-option\",\n          style: !notifyCount ? {\n            color: '#999'\n          } : {},\n          onClick: () => {\n            notifyCount && setIgnorePopoverVisible(!ignorePopoverVisible);\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            title: \"\\u5FFD\\u7565\\u5168\\u90E8\\u901A\\u77E5\",\n            className: \"iconfont butixing fontsize-14\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"a\", {\n        className: \"fontsize-12\",\n        onClick: () => {\n          setAppNoticeIconVisible(true);\n          setNoticeVisible(false); //20250801 Jim Song, 系统通知，点击全部后，将自身小弹窗关闭\n        },\n        children: [\"\\u5168\\u90E8\", data !== null && data !== void 0 && data.allCount ? '(' + (data.allCount > 99 ? '99+' : data.allCount) + ')' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        style: {\n          marginLeft: 20,\n          color: '#666'\n        },\n        onClick: () => closePopover(),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"iconfont guanbi\",\n          style: {\n            fontSize: 14\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 76\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 483,\n    columnNumber: 11\n  }, this);\n}\n_s2(NoticeTitle, \"qRdwsusuUxveZsBWBtf4kje37lU=\", false, function () {\n  return [useQueryClient, useQuery];\n});\n_c2 = NoticeTitle;\nfunction NoticeContent({\n  teamId,\n  goToNewPage,\n  isLink,\n  dropDownOpenId,\n  setDropDownOpenId,\n  getLinkUrl,\n  getFirstName,\n  teamList,\n  showUnReadView\n}) {\n  _s3();\n  const {\n    data,\n    refetch: refetchNotify,\n    dataUpdatedAt: team578DataUpdatedAt\n  } = useQuery({\n    ...team_578_get_notify_query(teamId),\n    enabled: false,\n    refetchInterval: false\n  });\n  const {\n    data: selectionList\n  } = useQuerySetting407_getCodeValueList(teamId); // 字典数据\n\n  function ignoreNotic(item) {\n    let params = {\n      teamId: teamId,\n      opType: \"delete\",\n      notifyIds: [item.id]\n    };\n    httpCommon.team_558_update_notification_status(params).then(result => {\n      if (result.resultCode == 200) {\n        refetchNotify();\n      }\n    });\n  }\n  function remindLater(key, item) {\n    let params = {\n      teamId: teamId,\n      notifyIds: [item.id],\n      remindType: key\n    };\n    httpCommon.team_559_set_notification_remind_later(params).then(res => {\n      if (res.resultCode == 200) {\n        ignoreNotic(item);\n      }\n    });\n  }\n  function menuList() {\n    let list = [{\n      key: 'ignore',\n      label: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          fontSize: 12,\n          justifyContent: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"iconfont butixing\",\n          style: {\n            fontSize: 12,\n            marginRight: 5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u5FFD\\u7565\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 17\n      }, this)\n    }];\n    let dataList = (selectionList || []).filter(selection => selection.selectionId == 1947).map(selection => ({\n      key: selection.propType,\n      label: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 12,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          width: 60\n        },\n        children: [selection.propValue, /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u540E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 139\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 16\n      }, this)\n    }));\n    return list.concat(dataList);\n  }\n  function menuClick(e, item) {\n    setDropDownOpenId(null);\n    if (e.key == 'ignore') {\n      ignoreNotic(item);\n    } else {\n      remindLater(e.key, item);\n    }\n  }\n  const memberMenu = item => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-column-parent\",\n    children: /*#__PURE__*/_jsxDEV(Menu, {\n      items: menuList(),\n      className: \"flex-column-child section\",\n      style: {\n        height: 150\n      },\n      onClick: e => menuClick(e, item)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 586,\n    columnNumber: 31\n  }, this);\n  function getWidth(item, getOptionByType) {\n    let width = 400;\n    if (!getOptionByType(item.opType) && !!item.opInfo) {\n      width = 400;\n    } else {\n      width = 350;\n    }\n    if (item.opTypeName.length > 2) {\n      width = width - 20;\n    }\n    return width;\n  }\n  function buttonUi(item, goToNewPage, getOptionByType) {\n    if (!getOptionByType(item.opType)) {\n      return null;\n    }\n    return /*#__PURE__*/_jsxDEV(Button, {\n      disabled: !!(teamList || []).find(team => {\n        var _item$supplierMap13;\n        return team.teamId == (item === null || item === void 0 ? void 0 : (_item$supplierMap13 = item.supplierMap) === null || _item$supplierMap13 === void 0 ? void 0 : _item$supplierMap13.inviteTeamId);\n      }),\n      className: \"noticPopover-list-btn\",\n      onClick: () => goToNewPage(item),\n      children: getOptionByType(item.opType)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 7\n    }, this);\n  }\n  function dropDownUi(item, memberMenu) {\n    return /*#__PURE__*/_jsxDEV(Dropdown, {\n      trigger: ['click'],\n      overlay: memberMenu(item),\n      open: dropDownOpenId == item.id,\n      children: /*#__PURE__*/_jsxDEV(\"a\", {\n        style: {\n          color: '#999',\n          fontSize: 12,\n          display: 'flex',\n          alignItems: 'center'\n        },\n        onClick: e => {\n          e.preventDefault();\n          e.stopPropagation();\n          setDropDownOpenId(item.id == dropDownOpenId ? null : item.id);\n        },\n        children: [\"\\u7A0D\\u540E\\u63D0\\u9192\", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"iconfont xiala1\",\n          style: {\n            fontSize: 14,\n            color: '#999'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 7\n    }, this);\n  }\n  function contentUi(item, goToNewPage, getOptionByType, memberMenu, getFirstName) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          margin: '3px 5px 0px 20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [!item.logoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notic-team-logo\",\n            children: getFirstName(item.teamName)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Avatar, {\n            style: {\n              marginRight: 5,\n              width: 28,\n              minWidth: 28,\n              height: 28\n            },\n            src: item.logoUrl\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fontsize-12 text-overflow\",\n            style: {\n              color: '#333',\n              flex: \"auto\"\n            },\n            children: item.teamName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: 12,\n              color: '#333',\n              marginLeft: 10\n            },\n            children: item.opTypeName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: 5,\n          children: [!item.opInfo && buttonUi(item, goToNewPage, getOptionByType), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: 12,\n              color: '#999'\n            },\n            children: item.notifyDt ? moment(item.notifyDt).format('YY-MM-DD HH:mm') : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          margin: '0px 5px 0px 20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            onClick: () => getOptionByType(item.opType) ? null : goToNewPage(item),\n            className: \"tms-link-nocolor\",\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginLeft: !!item.opTypeName ? 5 : 0,\n              width: getWidth(item, getOptionByType)\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              title: item.objTitle,\n              className: \"fontsize-12 text-overflow\",\n              children: item.objTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              dot: item.opStatus == 1,\n              size: \"small\",\n              offset: [-4, -4]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 11\n        }, this), !!item.opInfo ? buttonUi(item, goToNewPage, getOptionByType) : dropDownUi(item, memberMenu)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 667,\n        columnNumber: 9\n      }, this), !!item.opInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          margin: '0px 5px 0px 20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: 12,\n            color: '#999',\n            width: 380,\n            whiteSpace: 'nowrap',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          children: item.opInfo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 13\n        }, this), dropDownUi(item, memberMenu)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 7\n    }, this);\n  }\n  const ListItem = ({\n    key,\n    item,\n    goToNewPage,\n    goToNewPage1,\n    getOptionByType,\n    memberMenu,\n    getFirstName\n  }) => {\n    return /*#__PURE__*/_jsxDEV(List.Item, {\n      children: isLink(item) ? /*#__PURE__*/_jsxDEV(Link, {\n        style: {\n          width: '100%'\n        },\n        to: getLinkUrl(item),\n        target: \"_blank\",\n        onClick: () => goToNewPage1(item),\n        children: contentUi(item, goToNewPage, getOptionByType, memberMenu, getFirstName)\n      }, key, false, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 9\n      }, this) : contentUi(item, goToNewPage, getOptionByType, memberMenu, getFirstName)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 12\n    }, this);\n  };\n  const notificationReminders = useMemo(() => {\n    var _data$notificationRem2;\n    return ((_data$notificationRem2 = data === null || data === void 0 ? void 0 : data.notificationReminders) !== null && _data$notificationRem2 !== void 0 ? _data$notificationRem2 : []).filter(item => {\n      if (showUnReadView) {\n        return item.opStatus == 1;\n      } else {\n        return true;\n      }\n    });\n  }, [team578DataUpdatedAt, showUnReadView]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-column-parent\",\n    children: /*#__PURE__*/_jsxDEV(List, {\n      className: \"noticPopover-list\" + (notificationReminders.length > 9 ? ' flex-column-child section' : ''),\n      style: notificationReminders.length > 9 ? {\n        height: '480px'\n      } : {},\n      itemLayout: \"horizontal\",\n      dataSource: notificationReminders,\n      renderItem: (item, index) => isLink(item) ? /*#__PURE__*/_jsxDEV(ListItem, {\n        item: item,\n        goToNewPage: () => {},\n        goToNewPage1: goToNewPage,\n        getOptionByType: getOptionByType,\n        memberMenu: memberMenu,\n        isLink: isLink,\n        getFirstName: getFirstName\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 15\n      }, this) : /*#__PURE__*/_jsxDEV(ListItem, {\n        item: item,\n        goToNewPage: goToNewPage,\n        getOptionByType: getOptionByType,\n        memberMenu: memberMenu,\n        isLink: isLink,\n        getFirstName: getFirstName\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 15\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 733,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 732,\n    columnNumber: 10\n  }, this);\n}\n_s3(NoticeContent, \"SxbfhbiPsf73xVmD8lGtJ1Fqu3E=\", false, function () {\n  return [useQuery, useQuerySetting407_getCodeValueList];\n});\n_c3 = NoticeContent;\nexport default _c4 = /*#__PURE__*/React.memo(AppNoticeIcon);\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"AppNoticeIcon\");\n$RefreshReg$(_c2, \"NoticeTitle\");\n$RefreshReg$(_c3, \"NoticeContent\");\n$RefreshReg$(_c4, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "Badge", "<PERSON><PERSON>", "Checkbox", "Drawer", "Dropdown", "List", "<PERSON><PERSON>", "Popover", "Space", "Modal", "Avatar", "Popconfirm", "useNavigate", "useParams", "Link", "useQuery", "useQueryClient", "getOptionByType", "httpCommon", "AppNoticeIconAllModal", "globalUtil", "useQuerySetting407_getCodeValueList", "CreateTeamModal", "CREATETYPE_UPGRADE", "moment", "getUrlByModule", "useDispatch", "getTeamList", "CheckCircleOutlined", "team_578_get_notify_query", "useQuery_setting_126_get_setting_teams", "useThrottle", "eProductGroupId", "eProductStatus", "eSysNotifierOpType", "DraggableDrawer", "SettingsDrawer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppNoticeIcon", "props", "_s", "noticeVisible", "setNoticeVisible", "navigate", "dispatch", "teamId", "showUnReadView", "setShowUnReadView", "data", "teamData", "teamList", "teams", "notifyData", "refetch", "refetchNotify", "dataUpdatedAt", "appNoticeIconVisible", "setAppNoticeIconVisible", "showMessage", "_setShowMessage", "dropDownOpenId", "setDropDownOpenId", "createTeamModalVisible", "setCreateTeamModalVisible", "productListFinal", "setProductListFinal", "settingsDrawerVisible", "setSettingsDrawerVisible", "settingsDrawerTab", "setSettingsDrawerTab", "productId", "setProductId", "setShowMessage", "value", "getProductsList", "needPopupFlg", "autoPopupFlg", "team_711_get_team_product_list", "then", "res", "resultCode", "allProductsList", "productList", "filter", "product", "freeFlg", "groupId", "Pgid_1_OS", "statusType", "Status_3_Unreleased", "map", "key", "productListFormat", "packageList", "for<PERSON>ach", "item", "find", "_product", "push", "groupName", "groupList", "group", "config", "index", "_productListFinal$fin", "_productListFinal$fin2", "isRowSpan", "groupListLength", "length", "authCntDesc", "objCntDesc", "concat", "deleteNotic", "refetch557", "params", "opType", "notifyIds", "id", "team_558_update_notification_status", "success", "readNotics", "notifyType", "isLink", "_item$supplierMap", "_item$supplierMap2", "_item$supplierMap3", "op_1_comment", "op_2_at_me", "op_6_like", "op_11_approve_mbr_join", "supplierMap", "op_23_mobielOrEmail_mbr_invited", "op_25_product_auth", "op_27_set_kpi_stop_bpmn", "goToNewPage", "_item$supplierMap4", "_item$supplierMap5", "_item$supplierMap6", "btnClick", "op_13_add_mbr_user_quota", "op_14_buy_vip_expiration_dt", "op_15_buy_cdisk_traffic", "error", "op_17_accept_invite", "objId", "inviteTeamId", "acceptInvitation", "op_18_buy", "op_22_upgrade_to_vip", "notificationReminders", "remind", "opStatus", "setting_217_add_user_by_invite_url", "inviteId", "joinTeamSuccess", "obj<PERSON><PERSON>le", "_teamId", "newTeamName", "confirm", "title", "icon", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "display", "flexDirection", "children", "substring", "indexOf", "okText", "cancelText", "onOk", "onCancel", "getLinkUrl", "_item$supplierMap9", "_item$supplierMap10", "_item$supplierMap11", "_item$supplierMap12", "_item$supplierMap7", "spaceId", "_item$supplierMap8", "nodeId", "obj", "nodeType", "objType", "anchorNodeId", "no_hash_url", "hash_url", "commentId", "getFirstName", "teamName", "e", "onOpenChange", "open", "setTimeout", "placement", "overlayClassName", "trigger", "NoticeTitle", "closePopover", "NoticeContent", "offset", "size", "count", "reminder", "showZero", "type", "shape", "className", "onClick", "marginLeft", "centered", "destroyOnClose", "onClose", "width", "footer", "visible", "defaultTab", "_c", "_s2", "queryClient", "team578DataUpdatedAt", "enabled", "refetchInterval", "ignorePopoverVisible", "setIgnorePopoverVisible", "onAutoEjectChange", "target", "checked", "team_563_set_notify_auto_popup", "result", "setQueryData", "query<PERSON><PERSON>", "oldData", "onShowUnReadChange", "ignoreNotic", "notifyCount", "_data$notificationRem", "onChange", "onConfirm", "alignItems", "allCount", "fontSize", "_c2", "_s3", "selectionList", "remindLater", "remindType", "team_559_set_notification_remind_later", "menuList", "list", "label", "justifyContent", "marginRight", "dataList", "selection", "selectionId", "propType", "propValue", "menuClick", "memberMenu", "items", "height", "getWidth", "opInfo", "opTypeName", "buttonUi", "disabled", "team", "_item$supplierMap13", "dropDownUi", "overlay", "preventDefault", "stopPropagation", "contentUi", "margin", "logoUrl", "min<PERSON><PERSON><PERSON>", "src", "flex", "notifyDt", "format", "dot", "whiteSpace", "overflow", "textOverflow", "ListItem", "goToNewPage1", "<PERSON><PERSON>", "to", "_data$notificationRem2", "itemLayout", "dataSource", "renderItem", "_c3", "_c4", "memo", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/AppNoticeIcon/AppNoticeIcon.jsx"], "sourcesContent": ["\r\nimport React, { useState, useEffect, useMemo } from \"react\";\r\nimport { Bad<PERSON>, Button, Checkbox, Drawer, Dropdown, List, Menu, Popover, Space, Modal, Avatar, Popconfirm } from \"antd\";\r\nimport { useNavigate, useParams, Link } from \"react-router-dom\";\r\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { getOptionByType } from \"@common/utils/TsbConfig\";\r\nimport * as httpCommon from \"@common/api/http\";\r\nimport \"./AppNoticeIcon.scss\";\r\nimport AppNoticeIconAllModal from \"./AppNoticeIconAllModal\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\r\nimport CreateTeamModal, { CREATETYPE_UPGRADE, } from \"@components/CreateTeam\";\r\nimport moment from \"moment\";\r\nimport { getUrlByModule } from '@common/utils/logicUtils';\r\nimport { useDispatch } from \"react-redux\";\r\nimport { getTeamList } from \"src/team/store/actionCreators\";\r\nimport { CheckCircleOutlined } from '@ant-design/icons';\r\nimport { team_578_get_notify_query, /* setting_125_refresh_team_query */useQuery_setting_126_get_setting_teams } from \"@common/api/query/query\";\r\nimport { useThrottle } from \"@common/hook/useThrottle\";\r\nimport { eProductGroupId, eProductStatus, eSysNotifierOpType } from \"@common/utils/enum\";\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\nimport SettingsDrawer from \"@/settings/views/SettingsDrawer\";\r\n//系统通知 \r\nfunction AppNoticeIcon(props) {\r\n  const {noticeVisible, setNoticeVisible} = props\r\n  //const queryClient = useQueryClient();\r\n  const navigate = useNavigate()\r\n  const dispatch = useDispatch();\r\n  const { teamId } = useParams();\r\n  const [showUnReadView, setShowUnReadView] = useState(true);\r\n  // const {data: selectionList, isLoading: isLoadingCodeValueList} = useQuerySetting407_getCodeValueList(teamId); // 字典数据\r\n  //const { data, isLoading } = useQueryTeam557GetSysNotification()\r\n  const { data: teamData } = useQuery({\r\n    ...useQuery_setting_126_get_setting_teams()\r\n  });\r\n  const teamList = teamData?.teams||[]\r\n  const {data: notifyData, refetch: refetchNotify, dataUpdatedAt } = useQuery({ //isLoading,\r\n    ...team_578_get_notify_query(teamId)\r\n  });\r\n  // const { data: setting125Data } = useQuery({\r\n  //   ...setting_125_refresh_team_query(teamId),\r\n  //   enabled: false\r\n  // })\r\n  const [appNoticeIconVisible, setAppNoticeIconVisible] = useState(false);\r\n  const [showMessage,_setShowMessage] = useState(false);\r\n  const [dropDownOpenId,setDropDownOpenId] = useState(null);\r\n  const [createTeamModalVisible,setCreateTeamModalVisible] = useState(false);\r\n  // const [systemUpdateTips,setSystemUpdateTips] = useState(true);\r\n  const [productListFinal,setProductListFinal] = useState([]);\r\n  //const autoClosePopRef = useRef(false);\r\n\r\n  const [settingsDrawerVisible, setSettingsDrawerVisible] = useState(false); //打开团队设置->应用管理\r\n  const [settingsDrawerTab, setSettingsDrawerTab] = useState('product'); //默认打开 应用管理\r\n  const [productId, setProductId] = useState(); //打开应用授权，具体的某一个应用\r\n\r\n\r\n  const setShowMessage = useThrottle((value) => {\r\n    _setShowMessage(value)\r\n  }, 200)\r\n\r\n  useEffect(()=>{\r\n    getProductsList()\r\n  },[]);\r\n\r\n  useEffect(()=>{\r\n    if(noticeVisible){\r\n      setShowMessage(true);\r\n    }\r\n  },[noticeVisible]);\r\n\r\n  useEffect(()=>{\r\n    // 通知自动弹出的条件\r\n    // 1、系统身份信息已填写\r\n    // 2、团队个人信息已填写\r\n    // 3、第一次的新手向导已做\r\n    if(notifyData?.needPopupFlg == 1 && notifyData?.autoPopupFlg == 1 /* && !setting125Data?.firstEntryFlg && setting125Data?.bindMobileFlg && setting125Data?.trialReminderFlg */){\r\n      if(!showMessage){\r\n        setShowMessage(true)\r\n      }\r\n    }\r\n  },[dataUpdatedAt]);\r\n\r\n  function getProductsList() {\r\n    httpCommon.team_711_get_team_product_list({ teamId: teamId }).then(res => {\r\n      if (res.resultCode == 200) {\r\n        let allProductsList = (res.productList || [])\r\n          .filter(product => product.freeFlg == 0 && product.groupId != eProductGroupId.Pgid_1_OS && product.statusType != eProductStatus.Status_3_Unreleased)\r\n          .map(product => {\r\n            product.key = product.productId\r\n            return product\r\n          });\r\n        productListFormat(allProductsList);\r\n      }\r\n    });\r\n  }\r\n\r\n  function productListFormat(productList=[]){\r\n    let packageList = [];\r\n    productList.forEach(product => {\r\n      let item = packageList.find(_product => product.groupId == _product.groupId)\r\n      if(!item){\r\n        packageList.push({groupName: product.groupName, groupId: product.groupId, groupList: [product]});\r\n      }else{\r\n        item.groupList.push(product)\r\n      }\r\n    });\r\n    let allProductsList = [];\r\n    packageList.forEach(group => {\r\n      let groupList = group.groupList.map((config,index) => ({\r\n        ...config,\r\n        key: config.productId,\r\n        isRowSpan: index == 0 ? true : false,\r\n        groupListLength: index == 0 ? group.groupList.length : 0,\r\n        authCntDesc: !!config.authCntDesc ? config.authCntDesc : (productListFinal.find(product => product.productId == config.productId)?.authCntDesc||''),\r\n        objCntDesc: !!config.objCntDesc ? config.objCntDesc : (productListFinal.find(product => product.productId == config.productId)?.objCntDesc||''),\r\n      }));\r\n      allProductsList = allProductsList.concat(groupList)\r\n    });\r\n    setProductListFinal([...allProductsList]);\r\n  }\r\n\r\n  // 删除单条通知\r\n  function deleteNotic(item,refetch557) {\r\n    let params = { teamId: teamId, opType: \"phy_delete\", notifyIds: [item.id], }\r\n    httpCommon.team_558_update_notification_status(params)\r\n      .then((res) => {\r\n        if (res.resultCode === 200) {\r\n          globalUtil.success('删除成功')\r\n          refetch557 && refetch557();\r\n        }\r\n      });\r\n  }\r\n\r\n  //通知设置为已读\r\n  function readNotics(id,opType,refetch557){\r\n    let params = { teamId: teamId, opType: \"update\", notifyIds: [id], }\r\n    if(!!opType){\r\n      params.notifyType = opType\r\n    }\r\n    httpCommon.team_558_update_notification_status(params)\r\n      .then((res) => {\r\n        if (res.resultCode === 200) {\r\n          globalUtil.success('本消息已读');\r\n          refetch557 && refetch557()\r\n          refetchNotify()\r\n        }\r\n      });\r\n  }\r\n\r\n  function isLink(item){\r\n    if(item.opType == eSysNotifierOpType.op_1_comment || item.opType == eSysNotifierOpType.op_2_at_me || item.opType == eSysNotifierOpType.op_6_like){\r\n      return true;\r\n    }else if(item.opType == eSysNotifierOpType.op_11_approve_mbr_join && item?.supplierMap?.groupId){ //申请加入\r\n      return true;\r\n    }else if(item.opType == eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited && item?.supplierMap?.teamId){ //手机/邮箱邀请\r\n      return true;\r\n    } else if(item.opType == eSysNotifierOpType.op_25_product_auth){ //应用授权\r\n      if(item?.teamId == teamId)\r\n        return false\r\n      else\r\n        return true\r\n    } else if(item.opType == eSysNotifierOpType.op_27_set_kpi_stop_bpmn && item?.supplierMap?.teamId){ // 任务停止流程尚未配置完全\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  function goToNewPage(item,refetch557){\r\n    setDropDownOpenId(null);\r\n    switch(item.opType){\r\n      case eSysNotifierOpType.op_1_comment://评论\r\n        // let url = getLinkUrl(item);\r\n        // navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\r\n        btnClick(item,item.opType,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_2_at_me://去到被@的节点\r\n        // let url = getLinkUrl(item);\r\n        // navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\r\n        btnClick(item,item.opType,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_6_like://点赞\r\n        // let url = getLinkUrl(item);\r\n        // navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\r\n        btnClick(item,item.opType,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_11_approve_mbr_join://去审批\r\n        // item?.supplierMap?.spaceId && item?.supplierMap?.groupId && navigate(`/${teamId}/settings/space/${item.supplierMap.spaceId}/member/approval/${item.supplierMap.groupId}/2`);\r\n        !!item?.supplierMap?.groupId && btnClick(item,item.opType,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_13_add_mbr_user_quota://团队成员扩容\r\n        setCreateTeamModalVisible(true);\r\n        btnClick(item,item.opType,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_14_buy_vip_expiration_dt://续费\r\n       setCreateTeamModalVisible(true);\r\n       btnClick(item,item.opType,refetch557);\r\n       break;\r\n      case eSysNotifierOpType.op_15_buy_cdisk_traffic://云盘扩容\r\n        globalUtil.error('待开放');\r\n        break;\r\n      case eSysNotifierOpType.op_17_accept_invite://接受邀请\r\n        item?.objId && item?.supplierMap?.inviteTeamId && acceptInvitation(item,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_18_buy://购买\r\n        setCreateTeamModalVisible(true);\r\n        btnClick(item,item.opType,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_22_upgrade_to_vip://升级企业版\r\n        setCreateTeamModalVisible(true);\r\n        btnClick(item,item.opType,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited://进入团队\r\n        item?.supplierMap?.teamId && btnClick(item,item.opType,refetch557);\r\n        break;\r\n      case eSysNotifierOpType.op_25_product_auth: //产品授权\r\n          if(teamId == item.teamId) { //授权的请求，正好是本团队的请求，直接打开 SettingsDrawer的应用管理\r\n            if(appNoticeIconVisible){\r\n              setAppNoticeIconVisible(false)\r\n            }\r\n            if(noticeVisible){\r\n              setNoticeVisible(false) //关闭本身(系统通知)Drawer\r\n            }\r\n           // setSettingsDrawerTab('product')\r\n            setProductId(item.objId); //objId存储的就是productId,如21为文档/文档库\r\n            setSettingsDrawerVisible(true)\r\n          }else {\r\n            btnClick(item,0,refetch557); //继续走default路径\r\n          }\r\n          break;\r\n      default:\r\n        btnClick(item,0,refetch557);\r\n        break; \r\n    }\r\n  }\r\n\r\n  function btnClick(item,opType,refetch557){\r\n    // let closeList = [2,11,13,14,15,17,18,22,23]\r\n    // if(closeList.find(objType => objType == item.opType)){\r\n    //   setAppNoticeIconVisible(false)\r\n    // }\r\n    if(notifyData.notificationReminders.find(remind => remind.id == item.id && remind.opStatus == 1)){\r\n      readNotics(item.id,opType,refetch557);\r\n    }\r\n  }\r\n\r\n  function acceptInvitation(item,refetch557){\r\n    httpCommon.setting_217_add_user_by_invite_url({teamId: item.supplierMap.inviteTeamId,inviteId: item.objId}).then(res=>{\r\n      if(res.resultCode == 200){\r\n        //刷新团队列表\r\n        btnClick(item,eSysNotifierOpType.op_17_accept_invite,refetch557);\r\n        dispatch(getTeamList());\r\n        joinTeamSuccess(item.supplierMap.inviteTeamId,item.objTitle)\r\n      }\r\n    });\r\n  }\r\n\r\n  function joinTeamSuccess(_teamId,newTeamName){\r\n    Modal.confirm({\r\n      title: '提示',\r\n      icon: <CheckCircleOutlined style={{ color: '#52c41a' }}/>,\r\n      content: <div style={{ display: 'flex', flexDirection: 'column' }}>\r\n        <span>团队加入成功，是否切换至 {newTeamName.substring(0,newTeamName.indexOf('团队的'))} ？</span>\r\n        <span>点击\"否\"，停留在当前页面；</span>\r\n        <span>点击\"是\"，切换至新的团队。</span>\r\n      </div>,\r\n      okText: '是，切换团队',\r\n      cancelText: '否',\r\n      onOk:() =>{\r\n        navigate(`/team/${_teamId}`)\r\n      },\r\n      onCancel:()=>{}\r\n    });\r\n  }\r\n\r\n  const getLinkUrl = (item) => {\r\n    if(item.opType == eSysNotifierOpType.op_11_approve_mbr_join){ // 申请加入 去审批\r\n      if(!!item?.supplierMap?.spaceId){\r\n        return `/${item.teamId}/settings/space/${item.supplierMap.spaceId}/member/approval/${item.supplierMap.groupId}/2`\r\n      }\r\n      return `/${item.teamId}/settings/user/member/approval/${item.supplierMap.groupId}/2`\r\n    }\r\n    if(item.opType == eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited){ //手机/邮箱邀请 进入团队\r\n      if(!!item.supplierMap?.teamId){\r\n        return `/team/${item.supplierMap.teamId}`\r\n      }\r\n      return ``\r\n    }\r\n    if(item.opType == eSysNotifierOpType.op_25_product_auth){ //去授权\r\n      //item.objId 存放 productId\r\n      return `/${item.teamId}/settings/product/${item.objId}`\r\n    }\r\n    if(item.opType == eSysNotifierOpType.op_27_set_kpi_stop_bpmn){ // 任务停止流程尚未配置完全\r\n      if(!!item.teamId){\r\n        return `/team/${item.supplierMap.teamId}/stopTasks/${item.supplierMap.nodeId}`\r\n      }\r\n      return ``\r\n    }\r\n    let obj = {\r\n      nodeType: item.supplierMap?.objType, \r\n      anchorNodeId: item.supplierMap?.anchorNodeId, \r\n      nodeId: item.supplierMap?.nodeId\r\n    }\r\n    let no_hash_url = getUrlByModule(item.teamId, { ...obj });\r\n    let hash_url = ((item.objType == 1000 && item.supplierMap?.commentId)?`#comment-${item.supplierMap.commentId}`:'');\r\n    return no_hash_url + hash_url\r\n  }\r\n\r\n  const getFirstName = (teamName) => {\r\n    try {\r\n      return teamName.substring(0, 1);\r\n    } catch (e) {\r\n      return \"\";\r\n    }\r\n  };\r\n\r\n  const onOpenChange = (open) => {\r\n    if(!open) {\r\n      setDropDownOpenId(null);\r\n      if(open != showMessage) {\r\n        setTimeout(() => {\r\n          setShowMessage(false);\r\n          setNoticeVisible(false);\r\n        }, 300) //20250801 2000 -> 300, 2秒太久\r\n      }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <React.Fragment>\r\n      <Popover \r\n        open={showMessage}\r\n        placement=\"bottom\"\r\n        overlayClassName={\"noticPopover \" + (appNoticeIconVisible ? \"noticPopover-999\" : \"\")}\r\n        trigger={'click'}\r\n        onOpenChange={onOpenChange}\r\n        title={<NoticeTitle\r\n          teamId={teamId}\r\n          setAppNoticeIconVisible={setAppNoticeIconVisible}\r\n          setNoticeVisible = {setNoticeVisible}\r\n          closePopover={() => {setShowMessage(false);setNoticeVisible(false);setDropDownOpenId(null);}}\r\n          showUnReadView={showUnReadView} \r\n          setShowUnReadView={setShowUnReadView}\r\n        />}\r\n        content={showMessage?\r\n            <NoticeContent\r\n              teamId={teamId}\r\n              goToNewPage={goToNewPage}\r\n              isLink={isLink}\r\n              dropDownOpenId={dropDownOpenId}\r\n              setDropDownOpenId={setDropDownOpenId}\r\n              getLinkUrl={getLinkUrl}\r\n              getFirstName={getFirstName}\r\n              teamList={teamList}\r\n              showUnReadView={showUnReadView} />\r\n            :\r\n            <></>\r\n        }>\r\n        <Badge offset={[-5, 5]} size=\"small\" count={(notifyData?.notificationReminders || []).filter(reminder => reminder.opStatus == 1).length}\r\n               showZero={false} {...props}>\r\n          <Button type=\"link\" shape=\"circle\" icon={<span className={`iconfont ${notifyData?.autoPopupFlg == 1 ? \"tongzhi1\" : \"tongzhibutixin\"} `} />}\r\n                  onClick={()=>{setShowMessage(!showMessage);setNoticeVisible(false);}}\r\n                  className=\"header-icon\" />\r\n        </Badge>\r\n      </Popover>\r\n      <DraggableDrawer\r\n        title={\r\n          <span style={{ color: \"orange\" }} className=\"iconfont tongzhi1\">\r\n            <span style={{ color: \"#666\",marginLeft:10 }}>系统通知 - 全部</span>\r\n          </span>\r\n        }\r\n        className=\"AppNoticeIconAllModl-drawer\"\r\n        centered\r\n        open={appNoticeIconVisible}\r\n        destroyOnClose\r\n        onClose={() => {\r\n          setShowMessage(false);\r\n          setNoticeVisible(false);\r\n          setDropDownOpenId(null);\r\n          setTimeout(() => {\r\n            setAppNoticeIconVisible(false);\r\n          }, 300);\r\n        }}\r\n        width={800}\r\n        footer={false}>\r\n        <AppNoticeIconAllModal\r\n          teamId={teamId}\r\n          isLink={isLink}\r\n          getLinkUrl={getLinkUrl}\r\n          deleteNotic={deleteNotic}\r\n          goToNewPage={goToNewPage}\r\n          getFirstName={getFirstName}\r\n          refetchNotify={refetchNotify}\r\n          teamList={teamList}\r\n        />\r\n      </DraggableDrawer>\r\n      <CreateTeamModal\r\n        teamId={teamId}\r\n        type={CREATETYPE_UPGRADE}\r\n        visible={createTeamModalVisible}\r\n        onCancel={() => setCreateTeamModalVisible(false)}\r\n        onOk={() => setCreateTeamModalVisible(false)}\r\n        productList={productListFinal}\r\n      />\r\n      {/* 团队设置/成员管理/应用管理 Drawer */}\r\n      <SettingsDrawer\r\n        visible={settingsDrawerVisible}\r\n        onClose={()=>setSettingsDrawerVisible(false)}\r\n        teamId={teamId}\r\n        defaultTab={settingsDrawerTab}\r\n        productId={productId}\r\n      />\r\n      {/* <BuyTeamModal \r\n        teamId={teamId} \r\n        type={buyTeamModalVisible.type} \r\n        visible={buyTeamModalVisible.visible}\r\n        defaultPackageList={buyTeamModalVisible.defaultPackageList} \r\n        onCancel={()=> setBuyTeamModalVisible({visible:false,type:\"\",defaultPackageList:[]})} \r\n        onOk={()=> setBuyTeamModalVisible({visible:false,type:\"\",defaultPackageList:[]})}\r\n      /> */}\r\n    </React.Fragment>\r\n  )\r\n}\r\n\r\n// the content of notice\r\nfunction NoticeTitle({teamId,setAppNoticeIconVisible,setNoticeVisible,closePopover,showUnReadView,setShowUnReadView}) {\r\n  const queryClient = useQueryClient();\r\n\r\n  const {data, refetch: refetchNotify, dataUpdatedAt: team578DataUpdatedAt } = useQuery({\r\n    ...team_578_get_notify_query(teamId),\r\n    enabled: false,\r\n    refetchInterval: false\r\n  });\r\n\r\n  const [ignorePopoverVisible,setIgnorePopoverVisible] = useState(false);\r\n\r\n  const onAutoEjectChange = (e) => {\r\n    let autoPopupFlg = e.target.checked ? 1 : 0;\r\n    httpCommon.team_563_set_notify_auto_popup({teamId,autoPopupFlg})\r\n      .then(result=>{\r\n        if(result.resultCode == 200){\r\n          queryClient.setQueryData(team_578_get_notify_query(teamId).queryKey, oldData => {\r\n            return {\r\n              ...oldData,\r\n              autoPopupFlg\r\n            }\r\n          })\r\n        }\r\n      });\r\n  }\r\n\r\n  const onShowUnReadChange = (e) => {\r\n    setShowUnReadView(e.target.checked);\r\n  }\r\n\r\n  // 忽略全部通知\r\n  const ignoreNotic = () => {\r\n    let params = {\r\n      teamId: teamId,\r\n      opType: \"delete\",\r\n      notifyIds: []\r\n    }\r\n\r\n    httpCommon.team_558_update_notification_status(params)\r\n      .then(result => {\r\n        if(result.resultCode == 200){\r\n          refetchNotify();\r\n          setIgnorePopoverVisible(false);\r\n        }\r\n      })\r\n  }\r\n\r\n  const notifyCount = useMemo(() => {\r\n    return (data?.notificationReminders??[]).filter(item => {\r\n      if(showUnReadView) {\r\n        return item.opStatus == 1\r\n      } else {\r\n        return true\r\n      }\r\n    }).length\r\n  }, [showUnReadView, team578DataUpdatedAt])\r\n\r\n  return (<div className=\"noticPopover-title\">\r\n    <Space size={10}>\r\n      <div className=\"noticPopover-title-left\">\r\n        <span className=\"iconfont tongzhi1\" />\r\n        <div className=\"noticPopover-title-text\">系统通知{notifyCount ? `(${notifyCount})` : ''}</div>\r\n      </div>\r\n      <Checkbox className=\"noticPopover-title-check\" checked={data?.autoPopupFlg} onChange={onAutoEjectChange}>自动弹出</Checkbox>\r\n      <Checkbox className=\"noticPopover-title-check\" checked={showUnReadView} onChange={onShowUnReadChange}>只看未读</Checkbox>\r\n      <Popconfirm\r\n        overlayClassName=\"ignorePopover\"\r\n        title={`忽略全部(${notifyCount}条)通知?`}\r\n        placement={'right'}\r\n        trigger={'click'}\r\n        open={ignorePopoverVisible}\r\n        onConfirm={ignoreNotic}\r\n        onCancel={()=>{setIgnorePopoverVisible(false)}}\r\n      >\r\n        <a\r\n          className=\"noticPopover-title-option\"\r\n          style={!notifyCount ? {color:'#999'} : {}}\r\n          onClick={()=>{notifyCount && setIgnorePopoverVisible(!ignorePopoverVisible)}}\r\n        >\r\n          <span title=\"忽略全部通知\" className=\"iconfont butixing fontsize-14\"></span>\r\n        </a>\r\n      </Popconfirm>\r\n    </Space>\r\n    <div style={{display:'flex',alignItems:'center'}}>\r\n      <a className=\"fontsize-12\" onClick={() => {\r\n        setAppNoticeIconVisible(true)\r\n        setNoticeVisible(false); //20250801 Jim Song, 系统通知，点击全部后，将自身小弹窗关闭\r\n      }}>\r\n        全部{data?.allCount ? ('(' + (data.allCount > 99 ? '99+' : data.allCount) + ')') : ''}\r\n      </a>\r\n      <a style={{marginLeft:20,color:'#666'}} onClick={()=>closePopover()}><span className=\"iconfont guanbi\" style={{fontSize:14}}/></a>\r\n    </div>\r\n  </div>)\r\n}\r\n\r\nfunction NoticeContent({teamId, goToNewPage, isLink, dropDownOpenId, setDropDownOpenId, getLinkUrl, getFirstName, teamList, showUnReadView}) {\r\n  const {data, refetch: refetchNotify, dataUpdatedAt: team578DataUpdatedAt } = useQuery({\r\n    ...team_578_get_notify_query(teamId),\r\n    enabled: false,\r\n    refetchInterval: false\r\n  });\r\n\r\n  const {data: selectionList } = useQuerySetting407_getCodeValueList(teamId); // 字典数据\r\n\r\n  function ignoreNotic(item){\r\n    let params = {\r\n      teamId: teamId,\r\n      opType: \"delete\",\r\n      notifyIds: [item.id]\r\n    }\r\n\r\n    httpCommon.team_558_update_notification_status(params)\r\n      .then(result => {\r\n        if(result.resultCode == 200){\r\n          refetchNotify();\r\n        }\r\n      })\r\n  }\r\n\r\n  function remindLater(key,item){\r\n    let params = {\r\n      teamId: teamId,\r\n      notifyIds: [item.id],\r\n      remindType: key\r\n    }\r\n    httpCommon.team_559_set_notification_remind_later(params).then(res => {\r\n      if(res.resultCode == 200){\r\n        ignoreNotic(item);\r\n      }\r\n    });\r\n  }\r\n\r\n  function menuList(){\r\n    let list = [\r\n      {\r\n        key:'ignore',\r\n        label: (<div style={{display:'flex',alignItems:'center',fontSize:12,justifyContent:'center'}}>\r\n          <span className=\"iconfont butixing\" style={{fontSize:12,marginRight:5}}/>\r\n          <span>忽略</span>\r\n        </div>)\r\n      }\r\n    ];\r\n    let dataList = (selectionList||[])\r\n      .filter(selection => selection.selectionId == 1947)\r\n      .map(selection => ({\r\n        key: selection.propType,\r\n        label: <div style={{fontSize:12,display:'flex',alignItems:'center',justifyContent:'space-between',width:60}}>{selection.propValue}<div>后</div></div>\r\n      }));\r\n    return list.concat(dataList)\r\n  }\r\n\r\n  function menuClick(e,item){\r\n    setDropDownOpenId(null);\r\n    if(e.key == 'ignore'){\r\n      ignoreNotic(item)\r\n    }else{\r\n      remindLater(e.key,item)\r\n    }\r\n  }\r\n\r\n  const memberMenu = item => (<div className=\"flex-column-parent\">\r\n    <Menu\r\n      items={menuList()}\r\n      className='flex-column-child section'\r\n      style={{height:150}}\r\n      onClick={(e) => menuClick(e,item)}/>\r\n  </div>);\r\n\r\n  function getWidth(item,getOptionByType){\r\n    let width = 400\r\n    if(!getOptionByType(item.opType) && !!item.opInfo){\r\n      width = 400\r\n    }else{\r\n      width = 350\r\n    }\r\n    if(item.opTypeName.length > 2){\r\n      width = width - 20\r\n    }\r\n    return width\r\n  }\r\n\r\n  function buttonUi(item,goToNewPage,getOptionByType){\r\n    if(!getOptionByType(item.opType)){\r\n      return null\r\n    }\r\n    return (\r\n      <Button disabled={!!(teamList||[]).find(team => team.teamId == item?.supplierMap?.inviteTeamId)}\r\n              className=\"noticPopover-list-btn\"\r\n              onClick={() => goToNewPage(item)}>\r\n        {getOptionByType(item.opType)}\r\n      </Button>\r\n    )\r\n  }\r\n\r\n  function dropDownUi(item,memberMenu){\r\n    return (\r\n      <Dropdown trigger={['click']} overlay={memberMenu(item)} open={dropDownOpenId == item.id}>\r\n        <a style={{color:'#999',fontSize:12,display:'flex',alignItems:'center'}}\r\n           onClick={(e)=>{\r\n             e.preventDefault();\r\n             e.stopPropagation();\r\n             setDropDownOpenId(item.id == dropDownOpenId ? null : item.id)\r\n           }}\r\n        >\r\n          稍后提醒<span className=\"iconfont xiala1\" style={{fontSize:14,color:'#999'}}/>\r\n        </a>\r\n      </Dropdown>\r\n    )\r\n  }\r\n\r\n  function contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName){\r\n    return (\r\n      <div style={{width:'100%'}}>\r\n        <div style={{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'3px 5px 0px 20px'}}>\r\n          <div style={{display:'flex',alignItems:'center'}}>\r\n            {!item.logoUrl ?\r\n              <div className=\"notic-team-logo\">\r\n                {getFirstName(item.teamName)}\r\n              </div>\r\n              :\r\n              <Avatar\r\n                style={{\r\n                  marginRight: 5,\r\n                  width: 28,\r\n                  minWidth: 28,\r\n                  height: 28,\r\n                }}\r\n                src={item.logoUrl}\r\n              />\r\n            }\r\n            <div className=\"fontsize-12 text-overflow\" style={{color:'#333', flex: \"auto\"}}>{item.teamName}</div>\r\n            <div style={{fontSize: 12, color: '#333', marginLeft: 10}}>{item.opTypeName}</div>\r\n          </div>\r\n          <Space size={5}>\r\n            {!item.opInfo &&\r\n              buttonUi(item,goToNewPage,getOptionByType)}\r\n            <div style={{fontSize:12,color:'#999'}}>\r\n              {item.notifyDt ? moment(item.notifyDt).format('YY-MM-DD HH:mm') : ''}\r\n            </div>\r\n          </Space>\r\n        </div>\r\n        <div style={{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'0px 5px 0px 20px'}}>\r\n          <div style={{display:'flex',alignItems:'center'}}>\r\n            <a\r\n              onClick={() => getOptionByType(item.opType) ? null : goToNewPage(item)}\r\n              className='tms-link-nocolor'\r\n              style={{\r\n                display:'flex',\r\n                alignItems:'center',\r\n                marginLeft:!!item.opTypeName ? 5 : 0,\r\n                width: getWidth(item,getOptionByType)\r\n              }}\r\n            >\r\n              <div title={item.objTitle} className=\"fontsize-12 text-overflow\">{item.objTitle}</div>\r\n              <Badge dot={item.opStatus == 1} size=\"small\" offset={[-4,-4]}/>\r\n            </a>\r\n          </div>\r\n          {!!item.opInfo ?\r\n            buttonUi(item,goToNewPage,getOptionByType)\r\n            :\r\n            dropDownUi(item,memberMenu)\r\n          }\r\n        </div>\r\n        {!!item.opInfo &&\r\n          <div style={{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'0px 5px 0px 20px'}}>\r\n            <div\r\n              style={{\r\n                fontSize: 12,\r\n                color:'#999',\r\n                width: 380,\r\n                whiteSpace: 'nowrap',\r\n                overflow: 'hidden',\r\n                textOverflow: 'ellipsis'\r\n              }}\r\n            >\r\n              {item.opInfo}\r\n            </div>\r\n            {dropDownUi(item,memberMenu)}\r\n          </div>\r\n        }\r\n      </div>\r\n    )\r\n  }\r\n\r\n  const ListItem = ({key,item,goToNewPage,goToNewPage1,getOptionByType,memberMenu,getFirstName}) => {\r\n    return <List.Item>\r\n      {isLink(item)?\r\n        <Link key={key} style={{width:'100%'}} to={getLinkUrl(item)} target=\"_blank\" onClick={() => goToNewPage1(item)}>\r\n          {contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName)}\r\n        </Link>\r\n        :\r\n        contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName)\r\n      }\r\n    </List.Item >\r\n  }\r\n\r\n  const notificationReminders = useMemo(() => {\r\n    return (data?.notificationReminders??[]).filter(item => {\r\n      if(showUnReadView) {\r\n        return item.opStatus == 1\r\n      } else {\r\n        return true\r\n      }\r\n    })\r\n  },[team578DataUpdatedAt, showUnReadView])\r\n\r\n  return <div className=\"flex-column-parent\">\r\n    <List className={\"noticPopover-list\" + (notificationReminders.length > 9 ? ' flex-column-child section' : '')}\r\n          style={notificationReminders.length > 9 ? {height:'480px'} : {}}\r\n          itemLayout=\"horizontal\"\r\n          dataSource={notificationReminders}\r\n          renderItem={(item,index) =>\r\n            isLink(item)?\r\n              <ListItem\r\n                key={index}\r\n                item={item}\r\n                goToNewPage={() => {}}\r\n                goToNewPage1={goToNewPage}\r\n                getOptionByType={getOptionByType}\r\n                memberMenu={memberMenu}\r\n                isLink={isLink}\r\n                getFirstName={getFirstName}/>\r\n              :\r\n              <ListItem\r\n                key={index}\r\n                item={item}\r\n                goToNewPage={goToNewPage}\r\n                getOptionByType={getOptionByType}\r\n                memberMenu={memberMenu}\r\n                isLink={isLink}\r\n                getFirstName={getFirstName}/>\r\n          }\r\n    />\r\n  </div >\r\n}\r\n\r\nexport default React.memo(AppNoticeIcon)"], "mappings": ";;;;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SAASC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACvH,SAASC,WAAW,EAAEC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,QAAQ,EAAEC,cAAc,QAAQ,uBAAuB;AAChE,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,OAAO,sBAAsB;AAC7B,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,mCAAmC,QAAQ,6BAA6B;AACjF,OAAOC,eAAe,IAAIC,kBAAkB,QAAS,wBAAwB;AAC7E,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,yBAAyB,EAAE,oCAAoCC,sCAAsC,QAAQ,yBAAyB;AAC/I,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,eAAe,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,oBAAoB;AACxF,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,cAAc,MAAM,iCAAiC;AAC5D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAAAC,EAAA;EAC5B,MAAM;IAACC,aAAa;IAAEC;EAAgB,CAAC,GAAGH,KAAK;EAC/C;EACA,MAAMI,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsB;EAAO,CAAC,GAAGnC,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC1D;EACA;EACA,MAAM;IAAEsD,IAAI,EAAEC;EAAS,CAAC,GAAGrC,QAAQ,CAAC;IAClC,GAAGe,sCAAsC,CAAC;EAC5C,CAAC,CAAC;EACF,MAAMuB,QAAQ,GAAG,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,KAAK,KAAE,EAAE;EACpC,MAAM;IAACH,IAAI,EAAEI,UAAU;IAAEC,OAAO,EAAEC,aAAa;IAAEC;EAAc,CAAC,GAAG3C,QAAQ,CAAC;IAAE;IAC5E,GAAGc,yBAAyB,CAACmB,MAAM;EACrC,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA,MAAM,CAACW,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgE,WAAW,EAACC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkE,cAAc,EAACC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACzD,MAAM,CAACoE,sBAAsB,EAACC,yBAAyB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC1E;EACA,MAAM,CAACsE,gBAAgB,EAACC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC3D;;EAEA,MAAM,CAACwE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3E,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EACvE,MAAM,CAAC4E,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAG9C,MAAM8E,cAAc,GAAG5C,WAAW,CAAE6C,KAAK,IAAK;IAC5Cd,eAAe,CAACc,KAAK,CAAC;EACxB,CAAC,EAAE,GAAG,CAAC;EAEP9E,SAAS,CAAC,MAAI;IACZ+E,eAAe,CAAC,CAAC;EACnB,CAAC,EAAC,EAAE,CAAC;EAEL/E,SAAS,CAAC,MAAI;IACZ,IAAG8C,aAAa,EAAC;MACf+B,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAC,CAAC/B,aAAa,CAAC,CAAC;EAElB9C,SAAS,CAAC,MAAI;IACZ;IACA;IACA;IACA;IACA,IAAG,CAAAyD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuB,YAAY,KAAI,CAAC,IAAI,CAAAvB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEwB,YAAY,KAAI,CAAC,CAAC,8GAA6G;MAC7K,IAAG,CAAClB,WAAW,EAAC;QACdc,cAAc,CAAC,IAAI,CAAC;MACtB;IACF;EACF,CAAC,EAAC,CAACjB,aAAa,CAAC,CAAC;EAElB,SAASmB,eAAeA,CAAA,EAAG;IACzB3D,UAAU,CAAC8D,8BAA8B,CAAC;MAAEhC,MAAM,EAAEA;IAAO,CAAC,CAAC,CAACiC,IAAI,CAACC,GAAG,IAAI;MACxE,IAAIA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAE;QACzB,IAAIC,eAAe,GAAG,CAACF,GAAG,CAACG,WAAW,IAAI,EAAE,EACzCC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACC,OAAO,IAAI,CAAC,IAAID,OAAO,CAACE,OAAO,IAAIzD,eAAe,CAAC0D,SAAS,IAAIH,OAAO,CAACI,UAAU,IAAI1D,cAAc,CAAC2D,mBAAmB,CAAC,CACnJC,GAAG,CAACN,OAAO,IAAI;UACdA,OAAO,CAACO,GAAG,GAAGP,OAAO,CAACd,SAAS;UAC/B,OAAOc,OAAO;QAChB,CAAC,CAAC;QACJQ,iBAAiB,CAACX,eAAe,CAAC;MACpC;IACF,CAAC,CAAC;EACJ;EAEA,SAASW,iBAAiBA,CAACV,WAAW,GAAC,EAAE,EAAC;IACxC,IAAIW,WAAW,GAAG,EAAE;IACpBX,WAAW,CAACY,OAAO,CAACV,OAAO,IAAI;MAC7B,IAAIW,IAAI,GAAGF,WAAW,CAACG,IAAI,CAACC,QAAQ,IAAIb,OAAO,CAACE,OAAO,IAAIW,QAAQ,CAACX,OAAO,CAAC;MAC5E,IAAG,CAACS,IAAI,EAAC;QACPF,WAAW,CAACK,IAAI,CAAC;UAACC,SAAS,EAAEf,OAAO,CAACe,SAAS;UAAEb,OAAO,EAAEF,OAAO,CAACE,OAAO;UAAEc,SAAS,EAAE,CAAChB,OAAO;QAAC,CAAC,CAAC;MAClG,CAAC,MAAI;QACHW,IAAI,CAACK,SAAS,CAACF,IAAI,CAACd,OAAO,CAAC;MAC9B;IACF,CAAC,CAAC;IACF,IAAIH,eAAe,GAAG,EAAE;IACxBY,WAAW,CAACC,OAAO,CAACO,KAAK,IAAI;MAC3B,IAAID,SAAS,GAAGC,KAAK,CAACD,SAAS,CAACV,GAAG,CAAC,CAACY,MAAM,EAACC,KAAK;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAAA,OAAM;UACrD,GAAGH,MAAM;UACTX,GAAG,EAAEW,MAAM,CAAChC,SAAS;UACrBoC,SAAS,EAAEH,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK;UACpCI,eAAe,EAAEJ,KAAK,IAAI,CAAC,GAAGF,KAAK,CAACD,SAAS,CAACQ,MAAM,GAAG,CAAC;UACxDC,WAAW,EAAE,CAAC,CAACP,MAAM,CAACO,WAAW,GAAGP,MAAM,CAACO,WAAW,GAAI,EAAAL,qBAAA,GAAAxC,gBAAgB,CAACgC,IAAI,CAACZ,OAAO,IAAIA,OAAO,CAACd,SAAS,IAAIgC,MAAM,CAAChC,SAAS,CAAC,cAAAkC,qBAAA,uBAAvEA,qBAAA,CAAyEK,WAAW,KAAE,EAAG;UACnJC,UAAU,EAAE,CAAC,CAACR,MAAM,CAACQ,UAAU,GAAGR,MAAM,CAACQ,UAAU,GAAI,EAAAL,sBAAA,GAAAzC,gBAAgB,CAACgC,IAAI,CAACZ,OAAO,IAAIA,OAAO,CAACd,SAAS,IAAIgC,MAAM,CAAChC,SAAS,CAAC,cAAAmC,sBAAA,uBAAvEA,sBAAA,CAAyEK,UAAU,KAAE;QAC9I,CAAC;MAAA,CAAC,CAAC;MACH7B,eAAe,GAAGA,eAAe,CAAC8B,MAAM,CAACX,SAAS,CAAC;IACrD,CAAC,CAAC;IACFnC,mBAAmB,CAAC,CAAC,GAAGgB,eAAe,CAAC,CAAC;EAC3C;;EAEA;EACA,SAAS+B,WAAWA,CAACjB,IAAI,EAACkB,UAAU,EAAE;IACpC,IAAIC,MAAM,GAAG;MAAErE,MAAM,EAAEA,MAAM;MAAEsE,MAAM,EAAE,YAAY;MAAEC,SAAS,EAAE,CAACrB,IAAI,CAACsB,EAAE;IAAG,CAAC;IAC5EtG,UAAU,CAACuG,mCAAmC,CAACJ,MAAM,CAAC,CACnDpC,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1B/D,UAAU,CAACsG,OAAO,CAAC,MAAM,CAAC;QAC1BN,UAAU,IAAIA,UAAU,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC;EACN;;EAEA;EACA,SAASO,UAAUA,CAACH,EAAE,EAACF,MAAM,EAACF,UAAU,EAAC;IACvC,IAAIC,MAAM,GAAG;MAAErE,MAAM,EAAEA,MAAM;MAAEsE,MAAM,EAAE,QAAQ;MAAEC,SAAS,EAAE,CAACC,EAAE;IAAG,CAAC;IACnE,IAAG,CAAC,CAACF,MAAM,EAAC;MACVD,MAAM,CAACO,UAAU,GAAGN,MAAM;IAC5B;IACApG,UAAU,CAACuG,mCAAmC,CAACJ,MAAM,CAAC,CACnDpC,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1B/D,UAAU,CAACsG,OAAO,CAAC,OAAO,CAAC;QAC3BN,UAAU,IAAIA,UAAU,CAAC,CAAC;QAC1B3D,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC;EACN;EAEA,SAASoE,MAAMA,CAAC3B,IAAI,EAAC;IAAA,IAAA4B,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IACnB,IAAG9B,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAAC+F,YAAY,IAAI/B,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACgG,UAAU,IAAIhC,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACiG,SAAS,EAAC;MAC/I,OAAO,IAAI;IACb,CAAC,MAAK,IAAGjC,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACkG,sBAAsB,IAAIlC,IAAI,aAAJA,IAAI,gBAAA4B,iBAAA,GAAJ5B,IAAI,CAAEmC,WAAW,cAAAP,iBAAA,eAAjBA,iBAAA,CAAmBrC,OAAO,EAAC;MAAE;MAChG,OAAO,IAAI;IACb,CAAC,MAAK,IAAGS,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACoG,+BAA+B,IAAIpC,IAAI,aAAJA,IAAI,gBAAA6B,kBAAA,GAAJ7B,IAAI,CAAEmC,WAAW,cAAAN,kBAAA,eAAjBA,kBAAA,CAAmB/E,MAAM,EAAC;MAAE;MACxG,OAAO,IAAI;IACb,CAAC,MAAM,IAAGkD,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACqG,kBAAkB,EAAC;MAAE;MAC/D,IAAG,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAElD,MAAM,KAAIA,MAAM,EACvB,OAAO,KAAK,MAEZ,OAAO,IAAI;IACf,CAAC,MAAM,IAAGkD,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACsG,uBAAuB,IAAItC,IAAI,aAAJA,IAAI,gBAAA8B,kBAAA,GAAJ9B,IAAI,CAAEmC,WAAW,cAAAL,kBAAA,eAAjBA,kBAAA,CAAmBhF,MAAM,EAAC;MAAE;MACjG,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAEA,SAASyF,WAAWA,CAACvC,IAAI,EAACkB,UAAU,EAAC;IAAA,IAAAsB,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IACnC5E,iBAAiB,CAAC,IAAI,CAAC;IACvB,QAAOkC,IAAI,CAACoB,MAAM;MAChB,KAAKpF,kBAAkB,CAAC+F,YAAY;QAAC;QACnC;QACA;QACAY,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QACrC;MACF,KAAKlF,kBAAkB,CAACgG,UAAU;QAAC;QACjC;QACA;QACAW,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QACrC;MACF,KAAKlF,kBAAkB,CAACiG,SAAS;QAAC;QAChC;QACA;QACAU,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QACrC;MACF,KAAKlF,kBAAkB,CAACkG,sBAAsB;QAAC;QAC7C;QACA,CAAC,EAAClC,IAAI,aAAJA,IAAI,gBAAAwC,kBAAA,GAAJxC,IAAI,CAAEmC,WAAW,cAAAK,kBAAA,eAAjBA,kBAAA,CAAmBjD,OAAO,KAAIoD,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QACrE;MACF,KAAKlF,kBAAkB,CAAC4G,wBAAwB;QAAC;QAC/C5E,yBAAyB,CAAC,IAAI,CAAC;QAC/B2E,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QACrC;MACF,KAAKlF,kBAAkB,CAAC6G,2BAA2B;QAAC;QACnD7E,yBAAyB,CAAC,IAAI,CAAC;QAC/B2E,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QACrC;MACD,KAAKlF,kBAAkB,CAAC8G,uBAAuB;QAAC;QAC9C5H,UAAU,CAAC6H,KAAK,CAAC,KAAK,CAAC;QACvB;MACF,KAAK/G,kBAAkB,CAACgH,mBAAmB;QAAC;QAC1C,CAAAhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,KAAK,MAAIjD,IAAI,aAAJA,IAAI,wBAAAyC,kBAAA,GAAJzC,IAAI,CAAEmC,WAAW,cAAAM,kBAAA,uBAAjBA,kBAAA,CAAmBS,YAAY,KAAIC,gBAAgB,CAACnD,IAAI,EAACkB,UAAU,CAAC;QACnF;MACF,KAAKlF,kBAAkB,CAACoH,SAAS;QAAC;QAChCpF,yBAAyB,CAAC,IAAI,CAAC;QAC/B2E,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QACrC;MACF,KAAKlF,kBAAkB,CAACqH,oBAAoB;QAAC;QAC3CrF,yBAAyB,CAAC,IAAI,CAAC;QAC/B2E,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QACrC;MACF,KAAKlF,kBAAkB,CAACoG,+BAA+B;QAAC;QACtD,CAAApC,IAAI,aAAJA,IAAI,wBAAA0C,kBAAA,GAAJ1C,IAAI,CAAEmC,WAAW,cAAAO,kBAAA,uBAAjBA,kBAAA,CAAmB5F,MAAM,KAAI6F,QAAQ,CAAC3C,IAAI,EAACA,IAAI,CAACoB,MAAM,EAACF,UAAU,CAAC;QAClE;MACF,KAAKlF,kBAAkB,CAACqG,kBAAkB;QAAE;QACxC,IAAGvF,MAAM,IAAIkD,IAAI,CAAClD,MAAM,EAAE;UAAE;UAC1B,IAAGW,oBAAoB,EAAC;YACtBC,uBAAuB,CAAC,KAAK,CAAC;UAChC;UACA,IAAGhB,aAAa,EAAC;YACfC,gBAAgB,CAAC,KAAK,CAAC,EAAC;UAC1B;UACD;UACC6B,YAAY,CAACwB,IAAI,CAACiD,KAAK,CAAC,CAAC,CAAC;UAC1B7E,wBAAwB,CAAC,IAAI,CAAC;QAChC,CAAC,MAAK;UACJuE,QAAQ,CAAC3C,IAAI,EAAC,CAAC,EAACkB,UAAU,CAAC,CAAC,CAAC;QAC/B;QACA;MACJ;QACEyB,QAAQ,CAAC3C,IAAI,EAAC,CAAC,EAACkB,UAAU,CAAC;QAC3B;IACJ;EACF;EAEA,SAASyB,QAAQA,CAAC3C,IAAI,EAACoB,MAAM,EAACF,UAAU,EAAC;IACvC;IACA;IACA;IACA;IACA,IAAG7D,UAAU,CAACiG,qBAAqB,CAACrD,IAAI,CAACsD,MAAM,IAAIA,MAAM,CAACjC,EAAE,IAAItB,IAAI,CAACsB,EAAE,IAAIiC,MAAM,CAACC,QAAQ,IAAI,CAAC,CAAC,EAAC;MAC/F/B,UAAU,CAACzB,IAAI,CAACsB,EAAE,EAACF,MAAM,EAACF,UAAU,CAAC;IACvC;EACF;EAEA,SAASiC,gBAAgBA,CAACnD,IAAI,EAACkB,UAAU,EAAC;IACxClG,UAAU,CAACyI,kCAAkC,CAAC;MAAC3G,MAAM,EAAEkD,IAAI,CAACmC,WAAW,CAACe,YAAY;MAACQ,QAAQ,EAAE1D,IAAI,CAACiD;IAAK,CAAC,CAAC,CAAClE,IAAI,CAACC,GAAG,IAAE;MACpH,IAAGA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAC;QACvB;QACA0D,QAAQ,CAAC3C,IAAI,EAAChE,kBAAkB,CAACgH,mBAAmB,EAAC9B,UAAU,CAAC;QAChErE,QAAQ,CAACpB,WAAW,CAAC,CAAC,CAAC;QACvBkI,eAAe,CAAC3D,IAAI,CAACmC,WAAW,CAACe,YAAY,EAAClD,IAAI,CAAC4D,QAAQ,CAAC;MAC9D;IACF,CAAC,CAAC;EACJ;EAEA,SAASD,eAAeA,CAACE,OAAO,EAACC,WAAW,EAAC;IAC3CvJ,KAAK,CAACwJ,OAAO,CAAC;MACZC,KAAK,EAAE,IAAI;MACXC,IAAI,eAAE7H,OAAA,CAACV,mBAAmB;QAACwI,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;MACzDC,OAAO,eAAEpI,OAAA;QAAK8H,KAAK,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAChEvI,OAAA;UAAAuI,QAAA,GAAM,2EAAa,EAACb,WAAW,CAACc,SAAS,CAAC,CAAC,EAACd,WAAW,CAACe,OAAO,CAAC,KAAK,CAAC,CAAC,EAAC,SAAE;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjFnI,OAAA;UAAAuI,QAAA,EAAM;QAAc;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3BnI,OAAA;UAAAuI,QAAA,EAAM;QAAc;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;MACNO,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,GAAG;MACfC,IAAI,EAACA,CAAA,KAAK;QACRpI,QAAQ,CAAC,SAASiH,OAAO,EAAE,CAAC;MAC9B,CAAC;MACDoB,QAAQ,EAACA,CAAA,KAAI,CAAC;IAChB,CAAC,CAAC;EACJ;EAEA,MAAMC,UAAU,GAAIlF,IAAI,IAAK;IAAA,IAAAmF,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;IAC3B,IAAGtF,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACkG,sBAAsB,EAAC;MAAA,IAAAqD,kBAAA;MAAE;MAC5D,IAAG,CAAC,EAACvF,IAAI,aAAJA,IAAI,gBAAAuF,kBAAA,GAAJvF,IAAI,CAAEmC,WAAW,cAAAoD,kBAAA,eAAjBA,kBAAA,CAAmBC,OAAO,GAAC;QAC9B,OAAO,IAAIxF,IAAI,CAAClD,MAAM,mBAAmBkD,IAAI,CAACmC,WAAW,CAACqD,OAAO,oBAAoBxF,IAAI,CAACmC,WAAW,CAAC5C,OAAO,IAAI;MACnH;MACA,OAAO,IAAIS,IAAI,CAAClD,MAAM,kCAAkCkD,IAAI,CAACmC,WAAW,CAAC5C,OAAO,IAAI;IACtF;IACA,IAAGS,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACoG,+BAA+B,EAAC;MAAA,IAAAqD,kBAAA;MAAE;MACrE,IAAG,CAAC,GAAAA,kBAAA,GAACzF,IAAI,CAACmC,WAAW,cAAAsD,kBAAA,eAAhBA,kBAAA,CAAkB3I,MAAM,GAAC;QAC5B,OAAO,SAASkD,IAAI,CAACmC,WAAW,CAACrF,MAAM,EAAE;MAC3C;MACA,OAAO,EAAE;IACX;IACA,IAAGkD,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACqG,kBAAkB,EAAC;MAAE;MACxD;MACA,OAAO,IAAIrC,IAAI,CAAClD,MAAM,qBAAqBkD,IAAI,CAACiD,KAAK,EAAE;IACzD;IACA,IAAGjD,IAAI,CAACoB,MAAM,IAAIpF,kBAAkB,CAACsG,uBAAuB,EAAC;MAAE;MAC7D,IAAG,CAAC,CAACtC,IAAI,CAAClD,MAAM,EAAC;QACf,OAAO,SAASkD,IAAI,CAACmC,WAAW,CAACrF,MAAM,cAAckD,IAAI,CAACmC,WAAW,CAACuD,MAAM,EAAE;MAChF;MACA,OAAO,EAAE;IACX;IACA,IAAIC,GAAG,GAAG;MACRC,QAAQ,GAAAT,kBAAA,GAAEnF,IAAI,CAACmC,WAAW,cAAAgD,kBAAA,uBAAhBA,kBAAA,CAAkBU,OAAO;MACnCC,YAAY,GAAAV,mBAAA,GAAEpF,IAAI,CAACmC,WAAW,cAAAiD,mBAAA,uBAAhBA,mBAAA,CAAkBU,YAAY;MAC5CJ,MAAM,GAAAL,mBAAA,GAAErF,IAAI,CAACmC,WAAW,cAAAkD,mBAAA,uBAAhBA,mBAAA,CAAkBK;IAC5B,CAAC;IACD,IAAIK,WAAW,GAAGxK,cAAc,CAACyE,IAAI,CAAClD,MAAM,EAAE;MAAE,GAAG6I;IAAI,CAAC,CAAC;IACzD,IAAIK,QAAQ,GAAKhG,IAAI,CAAC6F,OAAO,IAAI,IAAI,KAAAP,mBAAA,GAAItF,IAAI,CAACmC,WAAW,cAAAmD,mBAAA,eAAhBA,mBAAA,CAAkBW,SAAS,GAAE,YAAYjG,IAAI,CAACmC,WAAW,CAAC8D,SAAS,EAAE,GAAC,EAAG;IAClH,OAAOF,WAAW,GAAGC,QAAQ;EAC/B,CAAC;EAED,MAAME,YAAY,GAAIC,QAAQ,IAAK;IACjC,IAAI;MACF,OAAOA,QAAQ,CAACvB,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC,CAAC,OAAOwB,CAAC,EAAE;MACV,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,IAAI,IAAK;IAC7B,IAAG,CAACA,IAAI,EAAE;MACRxI,iBAAiB,CAAC,IAAI,CAAC;MACvB,IAAGwI,IAAI,IAAI3I,WAAW,EAAE;QACtB4I,UAAU,CAAC,MAAM;UACf9H,cAAc,CAAC,KAAK,CAAC;UACrB9B,gBAAgB,CAAC,KAAK,CAAC;QACzB,CAAC,EAAE,GAAG,CAAC,EAAC;MACV;IACF;EACF,CAAC;EAED,oBACEP,OAAA,CAAC1C,KAAK,CAAC2C,QAAQ;IAAAsI,QAAA,gBACbvI,OAAA,CAAC/B,OAAO;MACNiM,IAAI,EAAE3I,WAAY;MAClB6I,SAAS,EAAC,QAAQ;MAClBC,gBAAgB,EAAE,eAAe,IAAIhJ,oBAAoB,GAAG,kBAAkB,GAAG,EAAE,CAAE;MACrFiJ,OAAO,EAAE,OAAQ;MACjBL,YAAY,EAAEA,YAAa;MAC3BrC,KAAK,eAAE5H,OAAA,CAACuK,WAAW;QACjB7J,MAAM,EAAEA,MAAO;QACfY,uBAAuB,EAAEA,uBAAwB;QACjDf,gBAAgB,EAAIA,gBAAiB;QACrCiK,YAAY,EAAEA,CAAA,KAAM;UAACnI,cAAc,CAAC,KAAK,CAAC;UAAC9B,gBAAgB,CAAC,KAAK,CAAC;UAACmB,iBAAiB,CAAC,IAAI,CAAC;QAAC,CAAE;QAC7Ff,cAAc,EAAEA,cAAe;QAC/BC,iBAAiB,EAAEA;MAAkB;QAAAoH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAE;MACHC,OAAO,EAAE7G,WAAW,gBAChBvB,OAAA,CAACyK,aAAa;QACZ/J,MAAM,EAAEA,MAAO;QACfyF,WAAW,EAAEA,WAAY;QACzBZ,MAAM,EAAEA,MAAO;QACf9D,cAAc,EAAEA,cAAe;QAC/BC,iBAAiB,EAAEA,iBAAkB;QACrCoH,UAAU,EAAEA,UAAW;QACvBgB,YAAY,EAAEA,YAAa;QAC3B/I,QAAQ,EAAEA,QAAS;QACnBJ,cAAc,EAAEA;MAAe;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEpCnI,OAAA,CAAAE,SAAA,mBAAI,CACP;MAAAqI,QAAA,eACDvI,OAAA,CAACtC,KAAK;QAACgN,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAE;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE,CAAC,CAAA3J,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiG,qBAAqB,KAAI,EAAE,EAAElE,MAAM,CAAC6H,QAAQ,IAAIA,QAAQ,CAACzD,QAAQ,IAAI,CAAC,CAAC,CAAC3C,MAAO;QACjIqG,QAAQ,EAAE,KAAM;QAAA,GAAK1K,KAAK;QAAAmI,QAAA,eAC/BvI,OAAA,CAACrC,MAAM;UAACoN,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,QAAQ;UAACnD,IAAI,eAAE7H,OAAA;YAAMiL,SAAS,EAAE,YAAY,CAAAhK,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEwB,YAAY,KAAI,CAAC,GAAG,UAAU,GAAG,gBAAgB;UAAI;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnI+C,OAAO,EAAEA,CAAA,KAAI;YAAC7I,cAAc,CAAC,CAACd,WAAW,CAAC;YAAChB,gBAAgB,CAAC,KAAK,CAAC;UAAC,CAAE;UACrE0K,SAAS,EAAC;QAAa;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACVnI,OAAA,CAACH,eAAe;MACd+H,KAAK,eACH5H,OAAA;QAAM8H,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAS,CAAE;QAACkD,SAAS,EAAC,mBAAmB;QAAA1C,QAAA,eAC7DvI,OAAA;UAAM8H,KAAK,EAAE;YAAEC,KAAK,EAAE,MAAM;YAACoD,UAAU,EAAC;UAAG,CAAE;UAAA5C,QAAA,EAAC;QAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACP;MACD8C,SAAS,EAAC,6BAA6B;MACvCG,QAAQ;MACRlB,IAAI,EAAE7I,oBAAqB;MAC3BgK,cAAc;MACdC,OAAO,EAAEA,CAAA,KAAM;QACbjJ,cAAc,CAAC,KAAK,CAAC;QACrB9B,gBAAgB,CAAC,KAAK,CAAC;QACvBmB,iBAAiB,CAAC,IAAI,CAAC;QACvByI,UAAU,CAAC,MAAM;UACf7I,uBAAuB,CAAC,KAAK,CAAC;QAChC,CAAC,EAAE,GAAG,CAAC;MACT,CAAE;MACFiK,KAAK,EAAE,GAAI;MACXC,MAAM,EAAE,KAAM;MAAAjD,QAAA,eACdvI,OAAA,CAACnB,qBAAqB;QACpB6B,MAAM,EAAEA,MAAO;QACf6E,MAAM,EAAEA,MAAO;QACfuD,UAAU,EAAEA,UAAW;QACvBjE,WAAW,EAAEA,WAAY;QACzBsB,WAAW,EAAEA,WAAY;QACzB2D,YAAY,EAAEA,YAAa;QAC3B3I,aAAa,EAAEA,aAAc;QAC7BJ,QAAQ,EAAEA;MAAS;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa,CAAC,eAClBnI,OAAA,CAAChB,eAAe;MACd0B,MAAM,EAAEA,MAAO;MACfqK,IAAI,EAAE9L,kBAAmB;MACzBwM,OAAO,EAAE9J,sBAAuB;MAChCkH,QAAQ,EAAEA,CAAA,KAAMjH,yBAAyB,CAAC,KAAK,CAAE;MACjDgH,IAAI,EAAEA,CAAA,KAAMhH,yBAAyB,CAAC,KAAK,CAAE;MAC7CmB,WAAW,EAAElB;IAAiB;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eAEFnI,OAAA,CAACF,cAAc;MACb2L,OAAO,EAAE1J,qBAAsB;MAC/BuJ,OAAO,EAAEA,CAAA,KAAItJ,wBAAwB,CAAC,KAAK,CAAE;MAC7CtB,MAAM,EAAEA,MAAO;MACfgL,UAAU,EAAEzJ,iBAAkB;MAC9BE,SAAS,EAAEA;IAAU;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OASY,CAAC;AAErB;;AAEA;AAAA9H,EAAA,CAjZSF,aAAa;EAAA,QAGH7B,WAAW,EACXc,WAAW,EACTb,SAAS,EAIDE,QAAQ,EAC9Be,sCAAsC,EAGwBf,QAAQ,EAoBpDgB,WAAW;AAAA;AAAAkM,EAAA,GAjC3BxL,aAAa;AAkZtB,SAASoK,WAAWA,CAAC;EAAC7J,MAAM;EAACY,uBAAuB;EAACf,gBAAgB;EAACiK,YAAY;EAAC7J,cAAc;EAACC;AAAiB,CAAC,EAAE;EAAAgL,GAAA;EACpH,MAAMC,WAAW,GAAGnN,cAAc,CAAC,CAAC;EAEpC,MAAM;IAACmC,IAAI;IAAEK,OAAO,EAAEC,aAAa;IAAEC,aAAa,EAAE0K;EAAqB,CAAC,GAAGrN,QAAQ,CAAC;IACpF,GAAGc,yBAAyB,CAACmB,MAAM,CAAC;IACpCqL,OAAO,EAAE,KAAK;IACdC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM,CAACC,oBAAoB,EAACC,uBAAuB,CAAC,GAAG3O,QAAQ,CAAC,KAAK,CAAC;EAEtE,MAAM4O,iBAAiB,GAAInC,CAAC,IAAK;IAC/B,IAAIvH,YAAY,GAAGuH,CAAC,CAACoC,MAAM,CAACC,OAAO,GAAG,CAAC,GAAG,CAAC;IAC3CzN,UAAU,CAAC0N,8BAA8B,CAAC;MAAC5L,MAAM;MAAC+B;IAAY,CAAC,CAAC,CAC7DE,IAAI,CAAC4J,MAAM,IAAE;MACZ,IAAGA,MAAM,CAAC1J,UAAU,IAAI,GAAG,EAAC;QAC1BgJ,WAAW,CAACW,YAAY,CAACjN,yBAAyB,CAACmB,MAAM,CAAC,CAAC+L,QAAQ,EAAEC,OAAO,IAAI;UAC9E,OAAO;YACL,GAAGA,OAAO;YACVjK;UACF,CAAC;QACH,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACN,CAAC;EAED,MAAMkK,kBAAkB,GAAI3C,CAAC,IAAK;IAChCpJ,iBAAiB,CAACoJ,CAAC,CAACoC,MAAM,CAACC,OAAO,CAAC;EACrC,CAAC;;EAED;EACA,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI7H,MAAM,GAAG;MACXrE,MAAM,EAAEA,MAAM;MACdsE,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE;IACb,CAAC;IAEDrG,UAAU,CAACuG,mCAAmC,CAACJ,MAAM,CAAC,CACnDpC,IAAI,CAAC4J,MAAM,IAAI;MACd,IAAGA,MAAM,CAAC1J,UAAU,IAAI,GAAG,EAAC;QAC1B1B,aAAa,CAAC,CAAC;QACf+K,uBAAuB,CAAC,KAAK,CAAC;MAChC;IACF,CAAC,CAAC;EACN,CAAC;EAED,MAAMW,WAAW,GAAGpP,OAAO,CAAC,MAAM;IAAA,IAAAqP,qBAAA;IAChC,OAAO,EAAAA,qBAAA,GAACjM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqG,qBAAqB,cAAA4F,qBAAA,cAAAA,qBAAA,GAAE,EAAE,EAAE9J,MAAM,CAACY,IAAI,IAAI;MACtD,IAAGjD,cAAc,EAAE;QACjB,OAAOiD,IAAI,CAACwD,QAAQ,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC,CAAC,CAAC3C,MAAM;EACX,CAAC,EAAE,CAAC9D,cAAc,EAAEmL,oBAAoB,CAAC,CAAC;EAE1C,oBAAQ9L,OAAA;IAAKiL,SAAS,EAAC,oBAAoB;IAAA1C,QAAA,gBACzCvI,OAAA,CAAC9B,KAAK;MAACyM,IAAI,EAAE,EAAG;MAAApC,QAAA,gBACdvI,OAAA;QAAKiL,SAAS,EAAC,yBAAyB;QAAA1C,QAAA,gBACtCvI,OAAA;UAAMiL,SAAS,EAAC;QAAmB;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtCnI,OAAA;UAAKiL,SAAS,EAAC,yBAAyB;UAAA1C,QAAA,GAAC,0BAAI,EAACsE,WAAW,GAAG,IAAIA,WAAW,GAAG,GAAG,EAAE;QAAA;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACNnI,OAAA,CAACpC,QAAQ;QAACqN,SAAS,EAAC,0BAA0B;QAACoB,OAAO,EAAExL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,YAAa;QAACsK,QAAQ,EAAEZ,iBAAkB;QAAA5D,QAAA,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACxHnI,OAAA,CAACpC,QAAQ;QAACqN,SAAS,EAAC,0BAA0B;QAACoB,OAAO,EAAE1L,cAAe;QAACoM,QAAQ,EAAEJ,kBAAmB;QAAApE,QAAA,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACrHnI,OAAA,CAAC3B,UAAU;QACTgM,gBAAgB,EAAC,eAAe;QAChCzC,KAAK,EAAE,QAAQiF,WAAW,OAAQ;QAClCzC,SAAS,EAAE,OAAQ;QACnBE,OAAO,EAAE,OAAQ;QACjBJ,IAAI,EAAE+B,oBAAqB;QAC3Be,SAAS,EAAEJ,WAAY;QACvB/D,QAAQ,EAAEA,CAAA,KAAI;UAACqD,uBAAuB,CAAC,KAAK,CAAC;QAAA,CAAE;QAAA3D,QAAA,eAE/CvI,OAAA;UACEiL,SAAS,EAAC,2BAA2B;UACrCnD,KAAK,EAAE,CAAC+E,WAAW,GAAG;YAAC9E,KAAK,EAAC;UAAM,CAAC,GAAG,CAAC,CAAE;UAC1CmD,OAAO,EAAEA,CAAA,KAAI;YAAC2B,WAAW,IAAIX,uBAAuB,CAAC,CAACD,oBAAoB,CAAC;UAAA,CAAE;UAAA1D,QAAA,eAE7EvI,OAAA;YAAM4H,KAAK,EAAC,sCAAQ;YAACqD,SAAS,EAAC;UAA+B;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACRnI,OAAA;MAAK8H,KAAK,EAAE;QAACO,OAAO,EAAC,MAAM;QAAC4E,UAAU,EAAC;MAAQ,CAAE;MAAA1E,QAAA,gBAC/CvI,OAAA;QAAGiL,SAAS,EAAC,aAAa;QAACC,OAAO,EAAEA,CAAA,KAAM;UACxC5J,uBAAuB,CAAC,IAAI,CAAC;UAC7Bf,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3B,CAAE;QAAAgI,QAAA,GAAC,cACC,EAAC1H,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqM,QAAQ,GAAI,GAAG,IAAIrM,IAAI,CAACqM,QAAQ,GAAG,EAAE,GAAG,KAAK,GAAGrM,IAAI,CAACqM,QAAQ,CAAC,GAAG,GAAG,GAAI,EAAE;MAAA;QAAAlF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC,eACJnI,OAAA;QAAG8H,KAAK,EAAE;UAACqD,UAAU,EAAC,EAAE;UAACpD,KAAK,EAAC;QAAM,CAAE;QAACmD,OAAO,EAAEA,CAAA,KAAIV,YAAY,CAAC,CAAE;QAAAjC,QAAA,eAACvI,OAAA;UAAMiL,SAAS,EAAC,iBAAiB;UAACnD,KAAK,EAAE;YAACqF,QAAQ,EAAC;UAAE;QAAE;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/H,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AACR;AAACyD,GAAA,CA7FQrB,WAAW;EAAA,QACE7L,cAAc,EAE2CD,QAAQ;AAAA;AAAA2O,GAAA,GAH9E7C,WAAW;AA+FpB,SAASE,aAAaA,CAAC;EAAC/J,MAAM;EAAEyF,WAAW;EAAEZ,MAAM;EAAE9D,cAAc;EAAEC,iBAAiB;EAAEoH,UAAU;EAAEgB,YAAY;EAAE/I,QAAQ;EAAEJ;AAAc,CAAC,EAAE;EAAA0M,GAAA;EAC3I,MAAM;IAACxM,IAAI;IAAEK,OAAO,EAAEC,aAAa;IAAEC,aAAa,EAAE0K;EAAqB,CAAC,GAAGrN,QAAQ,CAAC;IACpF,GAAGc,yBAAyB,CAACmB,MAAM,CAAC;IACpCqL,OAAO,EAAE,KAAK;IACdC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM;IAACnL,IAAI,EAAEyM;EAAc,CAAC,GAAGvO,mCAAmC,CAAC2B,MAAM,CAAC,CAAC,CAAC;;EAE5E,SAASkM,WAAWA,CAAChJ,IAAI,EAAC;IACxB,IAAImB,MAAM,GAAG;MACXrE,MAAM,EAAEA,MAAM;MACdsE,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,CAACrB,IAAI,CAACsB,EAAE;IACrB,CAAC;IAEDtG,UAAU,CAACuG,mCAAmC,CAACJ,MAAM,CAAC,CACnDpC,IAAI,CAAC4J,MAAM,IAAI;MACd,IAAGA,MAAM,CAAC1J,UAAU,IAAI,GAAG,EAAC;QAC1B1B,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC;EACN;EAEA,SAASoM,WAAWA,CAAC/J,GAAG,EAACI,IAAI,EAAC;IAC5B,IAAImB,MAAM,GAAG;MACXrE,MAAM,EAAEA,MAAM;MACduE,SAAS,EAAE,CAACrB,IAAI,CAACsB,EAAE,CAAC;MACpBsI,UAAU,EAAEhK;IACd,CAAC;IACD5E,UAAU,CAAC6O,sCAAsC,CAAC1I,MAAM,CAAC,CAACpC,IAAI,CAACC,GAAG,IAAI;MACpE,IAAGA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAC;QACvB+J,WAAW,CAAChJ,IAAI,CAAC;MACnB;IACF,CAAC,CAAC;EACJ;EAEA,SAAS8J,QAAQA,CAAA,EAAE;IACjB,IAAIC,IAAI,GAAG,CACT;MACEnK,GAAG,EAAC,QAAQ;MACZoK,KAAK,eAAG5N,OAAA;QAAK8H,KAAK,EAAE;UAACO,OAAO,EAAC,MAAM;UAAC4E,UAAU,EAAC,QAAQ;UAACE,QAAQ,EAAC,EAAE;UAACU,cAAc,EAAC;QAAQ,CAAE;QAAAtF,QAAA,gBAC3FvI,OAAA;UAAMiL,SAAS,EAAC,mBAAmB;UAACnD,KAAK,EAAE;YAACqF,QAAQ,EAAC,EAAE;YAACW,WAAW,EAAC;UAAC;QAAE;UAAA9F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACzEnI,OAAA;UAAAuI,QAAA,EAAM;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IACP,CAAC,CACF;IACD,IAAI4F,QAAQ,GAAG,CAACT,aAAa,IAAE,EAAE,EAC9BtK,MAAM,CAACgL,SAAS,IAAIA,SAAS,CAACC,WAAW,IAAI,IAAI,CAAC,CAClD1K,GAAG,CAACyK,SAAS,KAAK;MACjBxK,GAAG,EAAEwK,SAAS,CAACE,QAAQ;MACvBN,KAAK,eAAE5N,OAAA;QAAK8H,KAAK,EAAE;UAACqF,QAAQ,EAAC,EAAE;UAAC9E,OAAO,EAAC,MAAM;UAAC4E,UAAU,EAAC,QAAQ;UAACY,cAAc,EAAC,eAAe;UAACtC,KAAK,EAAC;QAAE,CAAE;QAAAhD,QAAA,GAAEyF,SAAS,CAACG,SAAS,eAACnO,OAAA;UAAAuI,QAAA,EAAK;QAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IACrJ,CAAC,CAAC,CAAC;IACL,OAAOwF,IAAI,CAAC/I,MAAM,CAACmJ,QAAQ,CAAC;EAC9B;EAEA,SAASK,SAASA,CAACpE,CAAC,EAACpG,IAAI,EAAC;IACxBlC,iBAAiB,CAAC,IAAI,CAAC;IACvB,IAAGsI,CAAC,CAACxG,GAAG,IAAI,QAAQ,EAAC;MACnBoJ,WAAW,CAAChJ,IAAI,CAAC;IACnB,CAAC,MAAI;MACH2J,WAAW,CAACvD,CAAC,CAACxG,GAAG,EAACI,IAAI,CAAC;IACzB;EACF;EAEA,MAAMyK,UAAU,GAAGzK,IAAI,iBAAK5D,OAAA;IAAKiL,SAAS,EAAC,oBAAoB;IAAA1C,QAAA,eAC7DvI,OAAA,CAAChC,IAAI;MACHsQ,KAAK,EAAEZ,QAAQ,CAAC,CAAE;MAClBzC,SAAS,EAAC,2BAA2B;MACrCnD,KAAK,EAAE;QAACyG,MAAM,EAAC;MAAG,CAAE;MACpBrD,OAAO,EAAGlB,CAAC,IAAKoE,SAAS,CAACpE,CAAC,EAACpG,IAAI;IAAE;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnC,CAAE;EAEP,SAASqG,QAAQA,CAAC5K,IAAI,EAACjF,eAAe,EAAC;IACrC,IAAI4M,KAAK,GAAG,GAAG;IACf,IAAG,CAAC5M,eAAe,CAACiF,IAAI,CAACoB,MAAM,CAAC,IAAI,CAAC,CAACpB,IAAI,CAAC6K,MAAM,EAAC;MAChDlD,KAAK,GAAG,GAAG;IACb,CAAC,MAAI;MACHA,KAAK,GAAG,GAAG;IACb;IACA,IAAG3H,IAAI,CAAC8K,UAAU,CAACjK,MAAM,GAAG,CAAC,EAAC;MAC5B8G,KAAK,GAAGA,KAAK,GAAG,EAAE;IACpB;IACA,OAAOA,KAAK;EACd;EAEA,SAASoD,QAAQA,CAAC/K,IAAI,EAACuC,WAAW,EAACxH,eAAe,EAAC;IACjD,IAAG,CAACA,eAAe,CAACiF,IAAI,CAACoB,MAAM,CAAC,EAAC;MAC/B,OAAO,IAAI;IACb;IACA,oBACEhF,OAAA,CAACrC,MAAM;MAACiR,QAAQ,EAAE,CAAC,CAAC,CAAC7N,QAAQ,IAAE,EAAE,EAAE8C,IAAI,CAACgL,IAAI;QAAA,IAAAC,mBAAA;QAAA,OAAID,IAAI,CAACnO,MAAM,KAAIkD,IAAI,aAAJA,IAAI,wBAAAkL,mBAAA,GAAJlL,IAAI,CAAEmC,WAAW,cAAA+I,mBAAA,uBAAjBA,mBAAA,CAAmBhI,YAAY;MAAA,EAAE;MACxFmE,SAAS,EAAC,uBAAuB;MACjCC,OAAO,EAAEA,CAAA,KAAM/E,WAAW,CAACvC,IAAI,CAAE;MAAA2E,QAAA,EACtC5J,eAAe,CAACiF,IAAI,CAACoB,MAAM;IAAC;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAEb;EAEA,SAAS4G,UAAUA,CAACnL,IAAI,EAACyK,UAAU,EAAC;IAClC,oBACErO,OAAA,CAAClC,QAAQ;MAACwM,OAAO,EAAE,CAAC,OAAO,CAAE;MAAC0E,OAAO,EAAEX,UAAU,CAACzK,IAAI,CAAE;MAACsG,IAAI,EAAEzI,cAAc,IAAImC,IAAI,CAACsB,EAAG;MAAAqD,QAAA,eACvFvI,OAAA;QAAG8H,KAAK,EAAE;UAACC,KAAK,EAAC,MAAM;UAACoF,QAAQ,EAAC,EAAE;UAAC9E,OAAO,EAAC,MAAM;UAAC4E,UAAU,EAAC;QAAQ,CAAE;QACrE/B,OAAO,EAAGlB,CAAC,IAAG;UACZA,CAAC,CAACiF,cAAc,CAAC,CAAC;UAClBjF,CAAC,CAACkF,eAAe,CAAC,CAAC;UACnBxN,iBAAiB,CAACkC,IAAI,CAACsB,EAAE,IAAIzD,cAAc,GAAG,IAAI,GAAGmC,IAAI,CAACsB,EAAE,CAAC;QAC/D,CAAE;QAAAqD,QAAA,GACJ,0BACK,eAAAvI,OAAA;UAAMiL,SAAS,EAAC,iBAAiB;UAACnD,KAAK,EAAE;YAACqF,QAAQ,EAAC,EAAE;YAACpF,KAAK,EAAC;UAAM;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEf;EAEA,SAASgH,SAASA,CAACvL,IAAI,EAACuC,WAAW,EAACxH,eAAe,EAAC0P,UAAU,EAACvE,YAAY,EAAC;IAC1E,oBACE9J,OAAA;MAAK8H,KAAK,EAAE;QAACyD,KAAK,EAAC;MAAM,CAAE;MAAAhD,QAAA,gBACzBvI,OAAA;QAAK8H,KAAK,EAAE;UAACO,OAAO,EAAC,MAAM;UAAC4E,UAAU,EAAC,QAAQ;UAACY,cAAc,EAAC,eAAe;UAACuB,MAAM,EAAC;QAAkB,CAAE;QAAA7G,QAAA,gBACxGvI,OAAA;UAAK8H,KAAK,EAAE;YAACO,OAAO,EAAC,MAAM;YAAC4E,UAAU,EAAC;UAAQ,CAAE;UAAA1E,QAAA,GAC9C,CAAC3E,IAAI,CAACyL,OAAO,gBACZrP,OAAA;YAAKiL,SAAS,EAAC,iBAAiB;YAAA1C,QAAA,EAC7BuB,YAAY,CAAClG,IAAI,CAACmG,QAAQ;UAAC;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,gBAENnI,OAAA,CAAC5B,MAAM;YACL0J,KAAK,EAAE;cACLgG,WAAW,EAAE,CAAC;cACdvC,KAAK,EAAE,EAAE;cACT+D,QAAQ,EAAE,EAAE;cACZf,MAAM,EAAE;YACV,CAAE;YACFgB,GAAG,EAAE3L,IAAI,CAACyL;UAAQ;YAAArH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAEJnI,OAAA;YAAKiL,SAAS,EAAC,2BAA2B;YAACnD,KAAK,EAAE;cAACC,KAAK,EAAC,MAAM;cAAEyH,IAAI,EAAE;YAAM,CAAE;YAAAjH,QAAA,EAAE3E,IAAI,CAACmG;UAAQ;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrGnI,OAAA;YAAK8H,KAAK,EAAE;cAACqF,QAAQ,EAAE,EAAE;cAAEpF,KAAK,EAAE,MAAM;cAAEoD,UAAU,EAAE;YAAE,CAAE;YAAA5C,QAAA,EAAE3E,IAAI,CAAC8K;UAAU;YAAA1G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACNnI,OAAA,CAAC9B,KAAK;UAACyM,IAAI,EAAE,CAAE;UAAApC,QAAA,GACZ,CAAC3E,IAAI,CAAC6K,MAAM,IACXE,QAAQ,CAAC/K,IAAI,EAACuC,WAAW,EAACxH,eAAe,CAAC,eAC5CqB,OAAA;YAAK8H,KAAK,EAAE;cAACqF,QAAQ,EAAC,EAAE;cAACpF,KAAK,EAAC;YAAM,CAAE;YAAAQ,QAAA,EACpC3E,IAAI,CAAC6L,QAAQ,GAAGvQ,MAAM,CAAC0E,IAAI,CAAC6L,QAAQ,CAAC,CAACC,MAAM,CAAC,gBAAgB,CAAC,GAAG;UAAE;YAAA1H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNnI,OAAA;QAAK8H,KAAK,EAAE;UAACO,OAAO,EAAC,MAAM;UAAC4E,UAAU,EAAC,QAAQ;UAACY,cAAc,EAAC,eAAe;UAACuB,MAAM,EAAC;QAAkB,CAAE;QAAA7G,QAAA,gBACxGvI,OAAA;UAAK8H,KAAK,EAAE;YAACO,OAAO,EAAC,MAAM;YAAC4E,UAAU,EAAC;UAAQ,CAAE;UAAA1E,QAAA,eAC/CvI,OAAA;YACEkL,OAAO,EAAEA,CAAA,KAAMvM,eAAe,CAACiF,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,GAAGmB,WAAW,CAACvC,IAAI,CAAE;YACvEqH,SAAS,EAAC,kBAAkB;YAC5BnD,KAAK,EAAE;cACLO,OAAO,EAAC,MAAM;cACd4E,UAAU,EAAC,QAAQ;cACnB9B,UAAU,EAAC,CAAC,CAACvH,IAAI,CAAC8K,UAAU,GAAG,CAAC,GAAG,CAAC;cACpCnD,KAAK,EAAEiD,QAAQ,CAAC5K,IAAI,EAACjF,eAAe;YACtC,CAAE;YAAA4J,QAAA,gBAEFvI,OAAA;cAAK4H,KAAK,EAAEhE,IAAI,CAAC4D,QAAS;cAACyD,SAAS,EAAC,2BAA2B;cAAA1C,QAAA,EAAE3E,IAAI,CAAC4D;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtFnI,OAAA,CAACtC,KAAK;cAACiS,GAAG,EAAE/L,IAAI,CAACwD,QAAQ,IAAI,CAAE;cAACuD,IAAI,EAAC,OAAO;cAACD,MAAM,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL,CAAC,CAACvE,IAAI,CAAC6K,MAAM,GACZE,QAAQ,CAAC/K,IAAI,EAACuC,WAAW,EAACxH,eAAe,CAAC,GAE1CoQ,UAAU,CAACnL,IAAI,EAACyK,UAAU,CAAC;MAAA;QAAArG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1B,CAAC,EACL,CAAC,CAACvE,IAAI,CAAC6K,MAAM,iBACZzO,OAAA;QAAK8H,KAAK,EAAE;UAACO,OAAO,EAAC,MAAM;UAAC4E,UAAU,EAAC,QAAQ;UAACY,cAAc,EAAC,eAAe;UAACuB,MAAM,EAAC;QAAkB,CAAE;QAAA7G,QAAA,gBACxGvI,OAAA;UACE8H,KAAK,EAAE;YACLqF,QAAQ,EAAE,EAAE;YACZpF,KAAK,EAAC,MAAM;YACZwD,KAAK,EAAE,GAAG;YACVqE,UAAU,EAAE,QAAQ;YACpBC,QAAQ,EAAE,QAAQ;YAClBC,YAAY,EAAE;UAChB,CAAE;UAAAvH,QAAA,EAED3E,IAAI,CAAC6K;QAAM;UAAAzG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACL4G,UAAU,CAACnL,IAAI,EAACyK,UAAU,CAAC;MAAA;QAAArG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL,CAAC;EAEV;EAEA,MAAM4H,QAAQ,GAAGA,CAAC;IAACvM,GAAG;IAACI,IAAI;IAACuC,WAAW;IAAC6J,YAAY;IAACrR,eAAe;IAAC0P,UAAU;IAACvE;EAAY,CAAC,KAAK;IAChG,oBAAO9J,OAAA,CAACjC,IAAI,CAACkS,IAAI;MAAA1H,QAAA,EACdhD,MAAM,CAAC3B,IAAI,CAAC,gBACX5D,OAAA,CAACxB,IAAI;QAAWsJ,KAAK,EAAE;UAACyD,KAAK,EAAC;QAAM,CAAE;QAAC2E,EAAE,EAAEpH,UAAU,CAAClF,IAAI,CAAE;QAACwI,MAAM,EAAC,QAAQ;QAAClB,OAAO,EAAEA,CAAA,KAAM8E,YAAY,CAACpM,IAAI,CAAE;QAAA2E,QAAA,EAC5G4G,SAAS,CAACvL,IAAI,EAACuC,WAAW,EAACxH,eAAe,EAAC0P,UAAU,EAACvE,YAAY;MAAC,GAD3DtG,GAAG;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC,GAEPgH,SAAS,CAACvL,IAAI,EAACuC,WAAW,EAACxH,eAAe,EAAC0P,UAAU,EAACvE,YAAY;IAAC;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE3D,CAAC;EACf,CAAC;EAED,MAAMjB,qBAAqB,GAAGzJ,OAAO,CAAC,MAAM;IAAA,IAAA0S,sBAAA;IAC1C,OAAO,EAAAA,sBAAA,GAACtP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqG,qBAAqB,cAAAiJ,sBAAA,cAAAA,sBAAA,GAAE,EAAE,EAAEnN,MAAM,CAACY,IAAI,IAAI;MACtD,IAAGjD,cAAc,EAAE;QACjB,OAAOiD,IAAI,CAACwD,QAAQ,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC,CAAC;EACJ,CAAC,EAAC,CAAC0E,oBAAoB,EAAEnL,cAAc,CAAC,CAAC;EAEzC,oBAAOX,OAAA;IAAKiL,SAAS,EAAC,oBAAoB;IAAA1C,QAAA,eACxCvI,OAAA,CAACjC,IAAI;MAACkN,SAAS,EAAE,mBAAmB,IAAI/D,qBAAqB,CAACzC,MAAM,GAAG,CAAC,GAAG,4BAA4B,GAAG,EAAE,CAAE;MACxGqD,KAAK,EAAEZ,qBAAqB,CAACzC,MAAM,GAAG,CAAC,GAAG;QAAC8J,MAAM,EAAC;MAAO,CAAC,GAAG,CAAC,CAAE;MAChE6B,UAAU,EAAC,YAAY;MACvBC,UAAU,EAAEnJ,qBAAsB;MAClCoJ,UAAU,EAAEA,CAAC1M,IAAI,EAACQ,KAAK,KACrBmB,MAAM,CAAC3B,IAAI,CAAC,gBACV5D,OAAA,CAAC+P,QAAQ;QAEPnM,IAAI,EAAEA,IAAK;QACXuC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAE;QACtB6J,YAAY,EAAE7J,WAAY;QAC1BxH,eAAe,EAAEA,eAAgB;QACjC0P,UAAU,EAAEA,UAAW;QACvB9I,MAAM,EAAEA,MAAO;QACfuE,YAAY,EAAEA;MAAa,GAPtB1F,KAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOkB,CAAC,gBAE/BnI,OAAA,CAAC+P,QAAQ;QAEPnM,IAAI,EAAEA,IAAK;QACXuC,WAAW,EAAEA,WAAY;QACzBxH,eAAe,EAAEA,eAAgB;QACjC0P,UAAU,EAAEA,UAAW;QACvB9I,MAAM,EAAEA,MAAO;QACfuE,YAAY,EAAEA;MAAa,GANtB1F,KAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMkB;IACjC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AACT;AAACkF,GAAA,CA/OQ5C,aAAa;EAAA,QACyDhM,QAAQ,EAMtDM,mCAAmC;AAAA;AAAAwR,GAAA,GAP3D9F,aAAa;AAiPtB,eAAA+F,GAAA,gBAAelT,KAAK,CAACmT,IAAI,CAACtQ,aAAa,CAAC;AAAA,IAAAwL,EAAA,EAAAyB,GAAA,EAAAmD,GAAA,EAAAC,GAAA;AAAAE,YAAA,CAAA/E,EAAA;AAAA+E,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}