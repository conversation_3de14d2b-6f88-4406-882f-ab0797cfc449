{"ast": null, "code": "import{CaretDownOutlined}from\"@ant-design/icons\";import{Button,Input,Drawer,Form,Checkbox,Avatar,Select,Space,Dropdown}from\"antd\";import DraggablePopUp from\"@components/DraggablePopUp\";import React,{useEffect,useRef,useState}from\"react\";import{useParams}from\"react-router-dom\";import'../DataPush.scss';import*as httpQuickAccess from\"@common/api/http\";import{globalUtil}from\"@common/utils/globalUtil\";import{eNodeTypeId,eNodeType}from\"@common/utils/TsbConfig\";import{NoAvatarIcon}from'@common/components/IconUtil';import TEditor from\"@common/components/TEditor/TEditor\";import TextModelModal from'./TextModel/TextModelModal';import PlanModal from'./Plan/PlanModal';import SubscribeContent from'./SubscribeContent/SubscribeContent';import{ePriorityTypeObj,ePriorityType,eMsgChannel,eMsgChannelObj,eExecTimeType}from\"src/quickAcess/utils/Config\";import{useQueryTeam599GetDataDictionary}from\"src/quickAcess/service/quickHooks\";import{eSelectionListId}from\"@common/utils/enum\";import{isEmpty}from\"@common/utils/ArrayUtils\";import DraggableDrawer from\"@components/DraggableDrawer\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{TextArea}=Input;// 新建/编辑 系统订阅\nexport default function SubscribeEdit(_ref){var _allMember$find,_eNodeType$nodeType;let{allMember,opType,closeDrawer,modalVisible,editData,isChange,setIsChange,selectedKey,typeList,fromType,ganttName,nodeType}=_ref;const{teamId,nodeId}=useParams();const[form]=Form.useForm();const codeInputRef=useRef(Object.create(null));// Form.Item布局样式\nconst formItemLayout={labelCol:{span:2}};// Form.Item的样式\nconst style={style:{height:\"50%\",marginBottom:20}};const[subscribeDataVisible,setSubscribeDataVisible]=useState(false);const[subscribeTextVisible,setSubscribeTextVisible]=useState(false);const[subscribePlanVisible,setSubscribePlanVisible]=useState(false);const[touids,setTouids]=useState([]);// 收件人\nconst[ccuids,setCcuids]=useState([]);// 抄送人\nconst[pushwhenEmpty,setPushwhenEmpty]=useState(false);const[subscribeData,setSubscribeData]=useState(null);const[timedPlan,setTimedPlan]=useState(null);const[description,setDescription]=useState(\"\");// 备注\nconst[showTip,setShowTip]=useState(false);const{data:{dataDictionaries=[],templateVariables=[],workflowVariables=[]}={dataDictionaries:[],templateVariables:[],workflowVariables:[]},isLoading:isLoadingTeamXxx,refetch:refetchTeamXxx}=useQueryTeam599GetDataDictionary({teamId,selectionId:eSelectionListId.Selection_1970_datapush});useEffect(()=>{if(modalVisible){if(!!editData){form.setFieldValue('subscrTitle',editData.subscrTitle||'');// form.setFieldValue('description',editData.description||'');\nsetDescription(editData.description||'');let toUids=(editData.toUids||'').split(',').filter(uid=>!!uid);setTouids([...toUids]);let ccUids=(editData.ccUids||'').split(',').filter(uid=>!!uid);setCcuids([...ccUids]);let obj=!!editData.objId?{objId:editData.objId,objType:editData.objType,objName:editData.objName}:null;setSubscribeData(obj);setPushwhenEmpty(editData.pushWhenEmpty==1);setTimedPlan({cronId:editData.cronId,cronName:editData.cronDesc});setTimeout(()=>{var _codeInputRef$current;!!(codeInputRef!==null&&codeInputRef!==void 0&&(_codeInputRef$current=codeInputRef.current)!==null&&_codeInputRef$current!==void 0&&_codeInputRef$current.setContent)&&codeInputRef.current.setContent(editData.msgTpl);},500);}else{form.setFieldValue('subscrTitle','');// form.setFieldValue('description','');\nsetDescription(\"\");setTouids([]);setCcuids([]);setSubscribeData(null);setPushwhenEmpty(false);setTimedPlan(null);setTimeout(()=>{var _codeInputRef$current2;//todo 推送文案默认值\n!!(codeInputRef!==null&&codeInputRef!==void 0&&(_codeInputRef$current2=codeInputRef.current)!==null&&_codeInputRef$current2!==void 0&&_codeInputRef$current2.setContent)&&codeInputRef.current.setContent(\"<p>你好，</p><p>%订阅数据%，</p><p>谢谢</p>\");},500);}}},[modalVisible]);function avatarFormat(src,name){return/*#__PURE__*/_jsx(Avatar,{style:{marginRight:5},src:src,icon:/*#__PURE__*/_jsx(NoAvatarIcon,{}),size:24});}function onOk(opType){var _codeInputRef$current3;let values=form.getFieldsValue(true);setShowTip(false);if((touids||[]).length==0){globalUtil.warning('请选择收件人');return;}if(!timedPlan){globalUtil.warning('请选择定时计划');return;}let msgtpl=(codeInputRef===null||codeInputRef===void 0?void 0:(_codeInputRef$current3=codeInputRef.current)===null||_codeInputRef$current3===void 0?void 0:_codeInputRef$current3.getContent())||'';if(opType==0){if(!!(subscribeData!==null&&subscribeData!==void 0&&subscribeData.objId)){if((msgtpl||'').indexOf('%订阅数据%')==-1){setShowTip(true);return;}}}let params={teamId:teamId,subscrTitle:values.subscrTitle,msgChannel:values.msgChannel,objId:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objId,objType:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objType,objName:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objName,msgTpl:msgtpl,toUids:(touids||[]).join(','),ccUids:(ccuids||[]).join(','),execTimeType:eExecTimeType.reportSubscribe,// 报表订阅 \ncronId:timedPlan.cronId,description:description,pushWhenEmpty:pushwhenEmpty?1:0,projectNodeId:nodeId,nodeType:nodeType};if(!!editData){params.nodeId=selectedKey;}httpQuickAccess.team_523_save_sched_task_src(params).then(res=>{if(res.resultCode==200){closeDrawer(1);}});}function _onClose(){var _codeInputRef$current4;closeDrawer(0,{values:form.getFieldsValue(true),objId:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objId,msgTpl:(codeInputRef===null||codeInputRef===void 0?void 0:(_codeInputRef$current4=codeInputRef.current)===null||_codeInputRef$current4===void 0?void 0:_codeInputRef$current4.getContent())||'',cronId:timedPlan===null||timedPlan===void 0?void 0:timedPlan.cronId,toUids:touids||[],ccUids:ccuids||[],pushwhenEmpty:pushwhenEmpty?1:0});}function _onChange(value,opType){if(!value){if(opType==1){// 编辑\nsetTimedPlan(null);}if(opType==0){// 新建\nsetSubscribeData(null);}}}/*  // 模版\r\n  function onClickTemplateVariables (info) {\r\n    let theme = form.getFieldValue(\"theme\")??\"\";\r\n    console.log(\"theme\", theme, info.key);\r\n    form.setFieldValue(\"theme\", `${theme}${info.key}`);\r\n  }*/function onChangeRemark(e){setDescription(e.target.value);}return/*#__PURE__*/_jsxs(DraggableDrawer,{className:\"tms-drawer\",title:`${opType==0?\"新建\":'编辑'}${fromType=='gantt'?'进度推送':'报表'}订阅`,width:'70%',onClose:()=>_onClose(),open:modalVisible,destroyOnClose:true//关闭时销毁子元素,避免重新打开数据不会刷新\n,closable:true,centered:true,footer:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'end'},children:/*#__PURE__*/_jsxs(Space,{size:20,children:[!!subscribeData&&/*#__PURE__*/_jsx(Checkbox,{className:\"subscribe-way-mark-check\",onChange:e=>setPushwhenEmpty(e.target.checked),checked:pushwhenEmpty,children:\"\\u8BA2\\u9605\\u6570\\u636E\\u4E3A\\u7A7A\\u65F6\\uFF0C\\u4F9D\\u7136\\u63A8\\u9001\\u3002\"}),/*#__PURE__*/_jsx(Input,{value:description,placeholder:\"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\",style:{borderRadius:5,width:300},autoComplete:\"off\",onChange:onChangeRemark}),/*#__PURE__*/_jsx(Button,{style:{borderRadius:5},type:'primary',onClick:()=>{form.submit();},children:\"\\u63D0\\u4EA4\"})]})}),children:[/*#__PURE__*/_jsxs(Form,{className:\"subscribe-way\",form:form,...formItemLayout,labelAlign:\"right\",onFinish:()=>onOk(0),initialValues:{msgChannel:eMsgChannel.mail// 默认邮件\n},children:[/*#__PURE__*/_jsx(\"div\",{className:\"label-header\",children:\"\\u63A8\\u9001\\u89C4\\u5219\"}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u540D\\u79F0\",rules:[{required:true,message:'名称不能为空'}],...style,name:\"subscrTitle\",children:/*#__PURE__*/_jsx(Input,{style:{width:300,borderRadius:5},autoComplete:\"off\"})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5B9A\\u65F6\\u8BA1\\u5212\",required:true,...style,children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Select,{style:{width:300},placeholder:\"\\u8BF7\\u9009\\u62E9\",value:timedPlan===null||timedPlan===void 0?void 0:timedPlan.cronName,suffixIcon:/*#__PURE__*/_jsx(CaretDownOutlined,{style:{pointerEvents:'none'}}),onChange:value=>_onChange(value,1),allowClear:true,onClick:()=>setSubscribePlanVisible(true)}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#999',marginLeft:10},children:\"\\u5907\\u6CE8\\uFF1A\\u8BA2\\u9605\\u90AE\\u4EF6\\u53D1\\u9001\\u65F6\\u95F4\\u4F1A\\u67091-5\\u5206\\u949F\\u7684\\u5EF6\\u8FDF\"})]})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u4F18\\u5148\\u7EA7\",...style,name:\"priority\",children:/*#__PURE__*/_jsx(Select,{showSearch:true,allowClear:true,style:{width:300},placeholder:\"\\u8BF7\\u9009\\u62E9\",options:[ePriorityTypeObj[ePriorityType.high],ePriorityTypeObj[ePriorityType.middle],ePriorityTypeObj[ePriorityType.low]]})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u7CFB\\u7EDF\\u63A8\\u9001\\u901A\\u9053\",...style,name:\"msgChannel\",children:/*#__PURE__*/_jsx(Select,{disabled:true,showSearch:true,allowClear:true,style:{width:300},placeholder:\"\\u8BF7\\u9009\\u62E9\",options:[eMsgChannelObj[eMsgChannel.mail],eMsgChannelObj[eMsgChannel.msg],eMsgChannelObj[eMsgChannel.voice],eMsgChannelObj[eMsgChannel.wechat],eMsgChannelObj[eMsgChannel.inSiteMsg]]})}),/*#__PURE__*/_jsx(\"div\",{className:\"label-header\",children:\"\\u63A8\\u9001\\u5185\\u5BB9\"}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u6536\\u4EF6\\u4EBA\",required:true,...style,children:/*#__PURE__*/_jsx(Select,{showSearch:true,allowClear:true// mode='multiple' fix tmsbug-11663:系统订阅，收件人，期望从多选改为单选\n,placeholder:\"\\u8BF7\\u9009\\u62E9\\u6536\\u4EF6\\u4EBA\",value:touids===null||touids===void 0?void 0:touids[0],onChange:value=>setTouids(!!value?[value]:[]),optionFilterProp:\"children\",dropdownMatchSelectWidth:false,filterOption:(input,option)=>{var _option$key;return((_option$key=option===null||option===void 0?void 0:option.key)!==null&&_option$key!==void 0?_option$key:'').toLowerCase().includes(input.toLowerCase());},options:allMember.map(user=>({value:user.key.toString(),label:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[avatarFormat(user.avatar,user.label),user.label]}),key:user.label}))})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u6284\\u9001\\u4EBA\",...style,children:/*#__PURE__*/_jsx(Select,{showSearch:true,mode:\"multiple\",allowClear:true,placeholder:\"\\u8BF7\\u9009\\u62E9\\u6284\\u9001\\u4EBA\",value:ccuids,onChange:value=>setCcuids(value),optionFilterProp:\"children\",filterOption:(input,option)=>{var _option$key2;return((_option$key2=option===null||option===void 0?void 0:option.key)!==null&&_option$key2!==void 0?_option$key2:'').toLowerCase().includes(input.toLowerCase());},options:allMember.map(user=>({value:user.key.toString(),label:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[avatarFormat(user.avatar,user.label),user.label]}),key:user.label}))})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u8BA2\\u9605\\u6570\\u636E\\u6E90\",...style,children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Select,{style:{width:300},open:false,placeholder:\"\\u8BF7\\u9009\\u62E9\",value:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objName,suffixIcon:/*#__PURE__*/_jsx(CaretDownOutlined,{style:{pointerEvents:'none'}}),onChange:value=>_onChange(value,0),allowClear:true,onClick:()=>setSubscribeDataVisible(true)}),/*#__PURE__*/_jsxs(\"div\",{style:{color:'#999',marginLeft:10},children:[\"\\u5907\\u6CE8\\uFF1A\\u7CFB\\u7EDF\\u53D1\\u4EF6\\u65F6\\uFF0C\\u4F1A\\u4EE5\\u6536\\u4EF6\\u4EBA\",`${!isEmpty(touids)?`(${(_allMember$find=allMember.find(member=>member.key==touids[0]))===null||_allMember$find===void 0?void 0:_allMember$find.label})`:\"\"}`,\"\\u8EAB\\u4EFD\\u5BF9\\u8BA2\\u9605\\u6570\\u636E\\u8FDB\\u884C\\u8FC7\\u6EE4\\u3002\"]})]})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u6B63\\u6587\",...style,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:5},children:[/*#__PURE__*/_jsx(\"a\",{onClick:()=>setSubscribeTextVisible(true),children:\"\\u9009\\u62E9\\u6A21\\u7248\"}),/*#__PURE__*/_jsx(TEditor,{ref:codeInputRef,placeholderText:\"\",heightMin:'200px',uploadParams:{teamId:teamId,nodeId:nodeId,moduleName:(_eNodeType$nodeType=eNodeType[nodeType])===null||_eNodeType$nodeType===void 0?void 0:_eNodeType$nodeType.nameEn,objType:nodeType},autofocus:false}),/*#__PURE__*/_jsxs(\"div\",{style:{color:'#999'},children:[\"\\u63D0\\u793A\\uFF1A\\u5360\\u4F4D\\u7B26 \",/*#__PURE__*/_jsx(\"a\",{onClick:()=>codeInputRef.current.insert('%订阅数据%'),children:\"%\\u8BA2\\u9605\\u6570\\u636E%\"}),\"\\uFF0C\\u4EE3\\u8868\\u9700\\u8981\\u8BA2\\u9605\\u7684\\u641C\\u7D22\\u7ED3\\u679C\\u6216\\u62A5\\u8868\\u6570\\u636E\\uFF0C\\u8BF7\\u70B9\\u51FB\\u5360\\u4F4D\\u7B26\\uFF0C\\u5C06\\u5176\\u5305\\u542B\\u8FDB\\u6B63\\u6587\\u3002\"]})]})})]}),/*#__PURE__*/_jsx(SubscribeContent,{allMember:allMember,subscribeDataVisible:subscribeDataVisible,setSubscribeDataVisible:setSubscribeDataVisible,subscribeData:subscribeData,setSubscribeData:setSubscribeData,typeList:typeList,fromType:fromType,ganttName:ganttName}),/*#__PURE__*/_jsx(TextModelModal,{subscribeTextVisible:subscribeTextVisible,setSubscribeTextVisible:setSubscribeTextVisible,setPushText:codeInputRef}),/*#__PURE__*/_jsx(PlanModal,{subscribePlanVisible:subscribePlanVisible,setSubscribePlanVisible:setSubscribePlanVisible,timedPlan:timedPlan,setTimedPlan:setTimedPlan}),/*#__PURE__*/_jsx(DraggablePopUp,{className:\"tms-modal\",title:\"\\u63D0\\u793A\",centered:true,width:300,open:isChange,onCancel:()=>setIsChange(false),onOk:()=>closeDrawer(0),children:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',margin:'10px 0px'},children:\"\\u60A8\\u6B63\\u5728\\u7F16\\u8F91\\u8BA2\\u9605\\uFF0C\\u786E\\u5B9A\\u653E\\u5F03\\u7F16\\u8F91\\uFF1F\"})}),/*#__PURE__*/_jsx(DraggablePopUp,{className:\"tms-modal\",title:\"\\u63D0\\u793A\",centered:true,width:600,open:showTip,cancelText:\"\\u7EE7\\u7EED\\u4FDD\\u5B58\",onCancel:()=>onOk(1),okText:\"\\u91CD\\u65B0\\u4FEE\\u6539\",onOk:()=>setShowTip(false),children:/*#__PURE__*/_jsxs(\"div\",{style:{margin:'10px 0px',fontSize:12},children:[/*#__PURE__*/_jsx(\"div\",{children:\"\\u5185\\u5BB9\\u6846\\u4E2D\\uFF0C\\u672A\\u68C0\\u6D4B\\u5230\\u6709 %\\u8BA2\\u9605\\u6570\\u636E% \\u5360\\u4F4D\\u7B26\\uFF0C\\u82E5\\u4E0D\\u542B\\u6B64\\u5360\\u4F4D\\u7B26\\uFF0C\\u8BA2\\u9605\\u90AE\\u4EF6\\u4E2D\\u5C06\\u4E0D\\u4F1A\\u6709 \\u641C\\u7D22\\u6216\\u62A5\\u8868\\u7ED3\\u679C\\u3002\"}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u70B9\\u51FB\\\"\\u7EE7\\u7EED\\u4FDD\\u5B58\\\"\\uFF0C\\u8868\\u793A\\u60A8\\u4E0D\\u9700\\u8981\\u5305\\u542B\\u8BA2\\u9605\\u6570\\u636E\\uFF0C\\u4EC5\\u4EE5\\\"\\u5185\\u5BB9\\\"\\u6846\\u4E2D\\u6587\\u6848\\u4F5C\\u4E3A\\u90AE\\u4EF6\\u4E3B\\u4F53\\u5185\\u5BB9\\uFF1B\"}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u70B9\\u51FB\\\"\\u91CD\\u65B0\\u4FEE\\u6539\\\"\\uFF0C\\u505C\\u5728\\u5F53\\u524D\\u754C\\u9762\\uFF0C\\u60A8\\u53EF\\u5728\\\"\\u5185\\u5BB9\\\"\\u6846\\u4E2D\\u67D0\\u4E2A\\u4F4D\\u7F6E\\uFF0C\\u70B9\\u51FB\\u84DD\\u8272%\\u8BA2\\u9605\\u6570\\u636E%\\u63D2\\u5165\\u5360\\u4F4D\\u7B26\\u3002\"})]})})]});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}