{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Codicon } from '../../base/common/codicons.js';\nimport { URI } from '../../base/common/uri.js';\nimport { Range } from './core/range.js';\nimport { TokenizationRegistry as TokenizationRegistryImpl } from './tokenizationRegistry.js';\nexport class Token {\n  constructor(offset, type, language) {\n    this._tokenBrand = undefined;\n    this.offset = offset;\n    this.type = type;\n    this.language = language;\n  }\n  toString() {\n    return '(' + this.offset + ', ' + this.type + ')';\n  }\n}\n/**\n * @internal\n */\nexport class TokenizationResult {\n  constructor(tokens, endState) {\n    this._tokenizationResultBrand = undefined;\n    this.tokens = tokens;\n    this.endState = endState;\n  }\n}\n/**\n * @internal\n */\nexport class EncodedTokenizationResult {\n  constructor(tokens, endState) {\n    this._encodedTokenizationResultBrand = undefined;\n    this.tokens = tokens;\n    this.endState = endState;\n  }\n}\n/**\n * @internal\n */\nexport var CompletionItemKinds;\n(function (CompletionItemKinds) {\n  const byKind = new Map();\n  byKind.set(0 /* CompletionItemKind.Method */, Codicon.symbolMethod);\n  byKind.set(1 /* CompletionItemKind.Function */, Codicon.symbolFunction);\n  byKind.set(2 /* CompletionItemKind.Constructor */, Codicon.symbolConstructor);\n  byKind.set(3 /* CompletionItemKind.Field */, Codicon.symbolField);\n  byKind.set(4 /* CompletionItemKind.Variable */, Codicon.symbolVariable);\n  byKind.set(5 /* CompletionItemKind.Class */, Codicon.symbolClass);\n  byKind.set(6 /* CompletionItemKind.Struct */, Codicon.symbolStruct);\n  byKind.set(7 /* CompletionItemKind.Interface */, Codicon.symbolInterface);\n  byKind.set(8 /* CompletionItemKind.Module */, Codicon.symbolModule);\n  byKind.set(9 /* CompletionItemKind.Property */, Codicon.symbolProperty);\n  byKind.set(10 /* CompletionItemKind.Event */, Codicon.symbolEvent);\n  byKind.set(11 /* CompletionItemKind.Operator */, Codicon.symbolOperator);\n  byKind.set(12 /* CompletionItemKind.Unit */, Codicon.symbolUnit);\n  byKind.set(13 /* CompletionItemKind.Value */, Codicon.symbolValue);\n  byKind.set(15 /* CompletionItemKind.Enum */, Codicon.symbolEnum);\n  byKind.set(14 /* CompletionItemKind.Constant */, Codicon.symbolConstant);\n  byKind.set(15 /* CompletionItemKind.Enum */, Codicon.symbolEnum);\n  byKind.set(16 /* CompletionItemKind.EnumMember */, Codicon.symbolEnumMember);\n  byKind.set(17 /* CompletionItemKind.Keyword */, Codicon.symbolKeyword);\n  byKind.set(27 /* CompletionItemKind.Snippet */, Codicon.symbolSnippet);\n  byKind.set(18 /* CompletionItemKind.Text */, Codicon.symbolText);\n  byKind.set(19 /* CompletionItemKind.Color */, Codicon.symbolColor);\n  byKind.set(20 /* CompletionItemKind.File */, Codicon.symbolFile);\n  byKind.set(21 /* CompletionItemKind.Reference */, Codicon.symbolReference);\n  byKind.set(22 /* CompletionItemKind.Customcolor */, Codicon.symbolCustomColor);\n  byKind.set(23 /* CompletionItemKind.Folder */, Codicon.symbolFolder);\n  byKind.set(24 /* CompletionItemKind.TypeParameter */, Codicon.symbolTypeParameter);\n  byKind.set(25 /* CompletionItemKind.User */, Codicon.account);\n  byKind.set(26 /* CompletionItemKind.Issue */, Codicon.issues);\n  /**\n   * @internal\n   */\n  function toIcon(kind) {\n    let codicon = byKind.get(kind);\n    if (!codicon) {\n      console.info('No codicon found for CompletionItemKind ' + kind);\n      codicon = Codicon.symbolProperty;\n    }\n    return codicon;\n  }\n  CompletionItemKinds.toIcon = toIcon;\n  const data = new Map();\n  data.set('method', 0 /* CompletionItemKind.Method */);\n  data.set('function', 1 /* CompletionItemKind.Function */);\n  data.set('constructor', 2 /* CompletionItemKind.Constructor */);\n  data.set('field', 3 /* CompletionItemKind.Field */);\n  data.set('variable', 4 /* CompletionItemKind.Variable */);\n  data.set('class', 5 /* CompletionItemKind.Class */);\n  data.set('struct', 6 /* CompletionItemKind.Struct */);\n  data.set('interface', 7 /* CompletionItemKind.Interface */);\n  data.set('module', 8 /* CompletionItemKind.Module */);\n  data.set('property', 9 /* CompletionItemKind.Property */);\n  data.set('event', 10 /* CompletionItemKind.Event */);\n  data.set('operator', 11 /* CompletionItemKind.Operator */);\n  data.set('unit', 12 /* CompletionItemKind.Unit */);\n  data.set('value', 13 /* CompletionItemKind.Value */);\n  data.set('constant', 14 /* CompletionItemKind.Constant */);\n  data.set('enum', 15 /* CompletionItemKind.Enum */);\n  data.set('enum-member', 16 /* CompletionItemKind.EnumMember */);\n  data.set('enumMember', 16 /* CompletionItemKind.EnumMember */);\n  data.set('keyword', 17 /* CompletionItemKind.Keyword */);\n  data.set('snippet', 27 /* CompletionItemKind.Snippet */);\n  data.set('text', 18 /* CompletionItemKind.Text */);\n  data.set('color', 19 /* CompletionItemKind.Color */);\n  data.set('file', 20 /* CompletionItemKind.File */);\n  data.set('reference', 21 /* CompletionItemKind.Reference */);\n  data.set('customcolor', 22 /* CompletionItemKind.Customcolor */);\n  data.set('folder', 23 /* CompletionItemKind.Folder */);\n  data.set('type-parameter', 24 /* CompletionItemKind.TypeParameter */);\n  data.set('typeParameter', 24 /* CompletionItemKind.TypeParameter */);\n  data.set('account', 25 /* CompletionItemKind.User */);\n  data.set('issue', 26 /* CompletionItemKind.Issue */);\n  /**\n   * @internal\n   */\n  function fromString(value, strict) {\n    let res = data.get(value);\n    if (typeof res === 'undefined' && !strict) {\n      res = 9 /* CompletionItemKind.Property */;\n    }\n    return res;\n  }\n  CompletionItemKinds.fromString = fromString;\n})(CompletionItemKinds || (CompletionItemKinds = {}));\n/**\n * How an {@link InlineCompletionsProvider inline completion provider} was triggered.\n */\nexport var InlineCompletionTriggerKind;\n(function (InlineCompletionTriggerKind) {\n  /**\n   * Completion was triggered automatically while editing.\n   * It is sufficient to return a single completion item in this case.\n   */\n  InlineCompletionTriggerKind[InlineCompletionTriggerKind[\"Automatic\"] = 0] = \"Automatic\";\n  /**\n   * Completion was triggered explicitly by a user gesture.\n   * Return multiple completion items to enable cycling through them.\n   */\n  InlineCompletionTriggerKind[InlineCompletionTriggerKind[\"Explicit\"] = 1] = \"Explicit\";\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nexport var SignatureHelpTriggerKind;\n(function (SignatureHelpTriggerKind) {\n  SignatureHelpTriggerKind[SignatureHelpTriggerKind[\"Invoke\"] = 1] = \"Invoke\";\n  SignatureHelpTriggerKind[SignatureHelpTriggerKind[\"TriggerCharacter\"] = 2] = \"TriggerCharacter\";\n  SignatureHelpTriggerKind[SignatureHelpTriggerKind[\"ContentChange\"] = 3] = \"ContentChange\";\n})(SignatureHelpTriggerKind || (SignatureHelpTriggerKind = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n  /**\n   * A textual occurrence.\n   */\n  DocumentHighlightKind[DocumentHighlightKind[\"Text\"] = 0] = \"Text\";\n  /**\n   * Read-access of a symbol, like reading a variable.\n   */\n  DocumentHighlightKind[DocumentHighlightKind[\"Read\"] = 1] = \"Read\";\n  /**\n   * Write-access of a symbol, like writing to a variable.\n   */\n  DocumentHighlightKind[DocumentHighlightKind[\"Write\"] = 2] = \"Write\";\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * @internal\n */\nexport function isLocationLink(thing) {\n  return thing && URI.isUri(thing.uri) && Range.isIRange(thing.range) && (Range.isIRange(thing.originSelectionRange) || Range.isIRange(thing.targetSelectionRange));\n}\n/**\n * @internal\n */\nexport var SymbolKinds;\n(function (SymbolKinds) {\n  const byKind = new Map();\n  byKind.set(0 /* SymbolKind.File */, Codicon.symbolFile);\n  byKind.set(1 /* SymbolKind.Module */, Codicon.symbolModule);\n  byKind.set(2 /* SymbolKind.Namespace */, Codicon.symbolNamespace);\n  byKind.set(3 /* SymbolKind.Package */, Codicon.symbolPackage);\n  byKind.set(4 /* SymbolKind.Class */, Codicon.symbolClass);\n  byKind.set(5 /* SymbolKind.Method */, Codicon.symbolMethod);\n  byKind.set(6 /* SymbolKind.Property */, Codicon.symbolProperty);\n  byKind.set(7 /* SymbolKind.Field */, Codicon.symbolField);\n  byKind.set(8 /* SymbolKind.Constructor */, Codicon.symbolConstructor);\n  byKind.set(9 /* SymbolKind.Enum */, Codicon.symbolEnum);\n  byKind.set(10 /* SymbolKind.Interface */, Codicon.symbolInterface);\n  byKind.set(11 /* SymbolKind.Function */, Codicon.symbolFunction);\n  byKind.set(12 /* SymbolKind.Variable */, Codicon.symbolVariable);\n  byKind.set(13 /* SymbolKind.Constant */, Codicon.symbolConstant);\n  byKind.set(14 /* SymbolKind.String */, Codicon.symbolString);\n  byKind.set(15 /* SymbolKind.Number */, Codicon.symbolNumber);\n  byKind.set(16 /* SymbolKind.Boolean */, Codicon.symbolBoolean);\n  byKind.set(17 /* SymbolKind.Array */, Codicon.symbolArray);\n  byKind.set(18 /* SymbolKind.Object */, Codicon.symbolObject);\n  byKind.set(19 /* SymbolKind.Key */, Codicon.symbolKey);\n  byKind.set(20 /* SymbolKind.Null */, Codicon.symbolNull);\n  byKind.set(21 /* SymbolKind.EnumMember */, Codicon.symbolEnumMember);\n  byKind.set(22 /* SymbolKind.Struct */, Codicon.symbolStruct);\n  byKind.set(23 /* SymbolKind.Event */, Codicon.symbolEvent);\n  byKind.set(24 /* SymbolKind.Operator */, Codicon.symbolOperator);\n  byKind.set(25 /* SymbolKind.TypeParameter */, Codicon.symbolTypeParameter);\n  /**\n   * @internal\n   */\n  function toIcon(kind) {\n    let icon = byKind.get(kind);\n    if (!icon) {\n      console.info('No codicon found for SymbolKind ' + kind);\n      icon = Codicon.symbolProperty;\n    }\n    return icon;\n  }\n  SymbolKinds.toIcon = toIcon;\n})(SymbolKinds || (SymbolKinds = {}));\nexport class FoldingRangeKind {\n  /**\n   * Creates a new {@link FoldingRangeKind}.\n   *\n   * @param value of the kind.\n   */\n  constructor(value) {\n    this.value = value;\n  }\n}\n/**\n * Kind for folding range representing a comment. The value of the kind is 'comment'.\n */\nFoldingRangeKind.Comment = new FoldingRangeKind('comment');\n/**\n * Kind for folding range representing a import. The value of the kind is 'imports'.\n */\nFoldingRangeKind.Imports = new FoldingRangeKind('imports');\n/**\n * Kind for folding range representing regions (for example marked by `#region`, `#endregion`).\n * The value of the kind is 'region'.\n */\nFoldingRangeKind.Region = new FoldingRangeKind('region');\n/**\n * @internal\n */\nexport var Command;\n(function (Command) {\n  /**\n   * @internal\n   */\n  function is(obj) {\n    if (!obj || typeof obj !== 'object') {\n      return false;\n    }\n    return typeof obj.id === 'string' && typeof obj.title === 'string';\n  }\n  Command.is = is;\n})(Command || (Command = {}));\nexport var InlayHintKind;\n(function (InlayHintKind) {\n  InlayHintKind[InlayHintKind[\"Type\"] = 1] = \"Type\";\n  InlayHintKind[InlayHintKind[\"Parameter\"] = 2] = \"Parameter\";\n})(InlayHintKind || (InlayHintKind = {}));\n/**\n * @internal\n */\nexport const TokenizationRegistry = new TokenizationRegistryImpl();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}