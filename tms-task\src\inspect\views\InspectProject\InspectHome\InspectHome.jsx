/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-use-before-define */
import { ePagination, eSelectionListId, eFlowAttrNid, eEnableFlg, eTreeOpType } from "@common/utils/enum";
import { globalUtil } from "@common/utils/globalUtil";
import { eNodeTypeId, inspCreatePartitionNodeTypeList, eCtxTypeId } from "@common/utils/TsbConfig";
import { globalEventBus } from "@common/utils/eventBus";
import { priSettingPermission } from "@common/utils/logicUtils";
import NoviceGuide from "@components/NoviceGuide"; // 新手引导
import PageTitle from "@components/PageTitle";
import { useQuery } from "@tanstack/react-query";
import { Layout, Spin, Row, Space } from "antd";
import { useEffect, useRef, useState } from "react";
import { Outlet, useAsyncValue, useLocation, useParams } from "react-router-dom";
import { useIssueSearchParams, useSearchQuery } from "src/inspect/service/inspectSearchHooks";
import InspectTopSearchBar from "./InspectTopSearchBar";
import * as http from "@common/api/http";
import ContextBoard from "@common/components/ContextBoard";
import { objNodeListMoreOps } from "@common/service/objNodeListMoreOps";
import { isEmpty, getSysIconByIdType } from "@common/utils/ArrayUtils";
import { isContain, scrollToAnchor } from "@common/utils/ViewUtils";
import { eOrderByType } from "src/inspect/utils/enum";
import { formatSvg, getViewModePath } from "src/issueTrack/utils/ArrayUtils";
import { useImmer } from "use-immer";
import "./InspectHome.scss";
import { useQuerySetting407_getCodeValueList, useQuerySetting320GetNodePrivQuery } from "@common/service/commonHooks";
import {  useQuerySetting409_getTeamAttrgrpProps } from "src/issueTrack/service/issueHooks";
import * as toolUtil from "@common/utils/toolUtil";
import qs from "qs";
import ScrollableHeader from "@components/ScrollableHeader";
import { getFlowList } from "@common/utils/logicUtils";
import {insp_091_get_insp_project_detail_query} from "@common/api/query/inspect/query_insp_01_mgmt";
import {insp_098_get_project_process_query} from "@common/api/query/inspect/query_insp_10_issues_and_setting";
import { useGetInspectRelatedNodes } from "src/inspect/service/inspectHooks";

export default function InspectHome() {
  const location = useLocation();
  const contextboardRef = useRef(null)
  const { teamId, nodeId } = useParams();
  const { setting320Result, userList = [], spaceUserList = [], allSpaceUserList = [], queryKeywords, objInfo, criteriaList: _searchQuery } = useAsyncValue(); //预加载获取的数据
  const { data: { isOA,},  } //isLoading: insp072Loading
      = useGetInspectRelatedNodes({teamId, nodeId, enabled: !!nodeId});
  const viewMode = getViewModePath(location.pathname); // 视图回显

  const [rootNode, setRootNode] = useImmer(null); // 列表数据
  const [totalCnt, setTotalCnt] = useState(0); //查询结果集总的记录数，分页加载数据
  const [iconSelectionLid] = useState(null); //icon列表id

  // 搜索条件相关
  const { issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams(); //issue路由配置，页面刷新
  let pageSize = ePagination.PageSize_30; //默认pageSize为30
  const { searchQuery, updateSearchQuery, loading } = useSearchQuery({ _searchQuery }); //查询条件列表
  const [orderBy, setOrderBy] = useState(eOrderByType.create); //排序，创建时间
  const [ascendingFlg, setAscendingFlg] = useState(false); //false:倒序,true:正序 
  const [keywords, setKeywords] = useState(); //关键字
  const [contextKey, setContextKey] = useState([]); // 右键菜单选中的key
  const [currentIssueIdx, setCurrentIssueIdx] = useState(0); // 用于切换页码时，加载数据前，先设置选中的issue索引,上一条为当前页的最后一条，下一条为当前页的第一条
  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);
  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState([]); // 选中节点
  const [flowList, setFlowList] = useState([]);           // 流程图列表
  const [defaultFlow, setDefaultFlow] = useState([]);     // 默认可新建流程图
  const [selectedFlow, setselectedFlow] = useState();     // 选择的流程图
  const [pageNum, setPageNum] = useState(); // 页码 注意：不要赋值默认值，需要先通过load_exam_040_get_question_page来获取真实页码
  const [guid, setGuid] = useState(toolUtil.guid());
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingCreate, setIsLoadingCreate] = useState(false); // 新建发现项loading
  const [createObjNode, setCreateObjNode] = useState(); // 需要新增的node节点
  const [needShowPowerLock,setNeedShowPowerLock] = useState(false);

  const { data: selectionList, isLoading: isLoadingCodeValueList } = useQuerySetting407_getCodeValueList(teamId); //字典数据
  
  // 权限相关
  const { data: { privSetting } } = useQuerySetting320GetNodePrivQuery({ teamId, nodeId });
  const canEditFlg = (inspCreatePartitionNodeTypeList.includes(objInfo?.nodeType));

  // 巡检项目详情
  const { data: projectInfo = {} } = useQuery(insp_091_get_insp_project_detail_query(teamId, nodeId))
  const { data: projectProcessList = [], dataUpdatedAt: dataUpdatedAtInsp098  } = useQuery(insp_098_get_project_process_query(teamId, nodeId, ));

  // 自定义表单属性列表
  // 用户组需要创建人字段，此处放开全部字段（包含不显示字段）
  const { subclassAttrList=[] } =
      useQuerySetting409_getTeamAttrgrpProps(teamId, nodeId, selectedFlow?.subclassId, !!selectedFlow?.subclassId, eEnableFlg.enable,);

  /**
  * useQuery无法很好的解决俩个接口之间的条件依赖关系，所以需要使用useEffect配合fetch来解决
  * 需要先调用exam-040 get_question_page 获取题目在题库的页码 再通过页码查询题库列表的场景
  * 1.页面初始化
  * 2.过滤条件和排序规则变更时
  */
  useEffect(() => {
    if (!loading) {
      load_insp_094_get_process_page_with_id(issueNodeId);
    }
  }, [loading]);

  useEffect(()=>{
    if (!loading && dataUpdatedAtInsp098) {
      initSelectedRow()
    }
  },[loading, dataUpdatedAtInsp098])

   // 监听queryKeywords
   useEffect(() => {
    setKeywords(queryKeywords)
  }, [queryKeywords])

  // 需要获取到pageNum后才能调用
  useEffect(() => {
    if (!!pageNum) {
      load_insp_092_get_process_inst_list()
    }
  }, [guid, pageNum, pageSize])

  useEffect(() => {
    // isLoadingAttrgrpProps 更改自定义表单后是否仍然有效
    if (dataUpdatedAtInsp098 ) {
      const [flowList, defaultFlow] = getFlowList(projectProcessList);
      setFlowList(flowList);
      setDefaultFlow(defaultFlow);
      if(objInfo.nodeType == eNodeTypeId.nt_574_insp_partition || objInfo.nodeType == eNodeTypeId.nt_674_oa_partition){ // 采集口径只有一个流程，则默认选中该流程，无需参与数据逻辑
        setselectedFlow(projectProcessList[0]);
      }
    }
  }, [dataUpdatedAtInsp098]);

  // 监听issueNodeId
  useEffect(() => {
    if (!!issueNodeId && !isLoadingCodeValueList) {
      setSelectedKeys([issueNodeId?.toString()]);
    } else {
      setSelectedKeys([]);
    }
  }, [issueNodeId, isLoadingCodeValueList])

  useEffect(()=>{
    if(!!location.search){
      let search = qs.parse(location.search.slice(1))
      if(search?.openPowerLock == '1'){
        setNeedShowPowerLock(true);
        return
      }
    }
    setNeedShowPowerLock(false);
  },[location.search]);

  // 选中流程
  function initSelectedRow () {
    const processdefId = (searchQuery.find(search => search.col == eFlowAttrNid)?.begin || [])[0];
    if(!!processdefId){
      const selectedFlow = projectProcessList.find(process => process.processdefId == processdefId);
      setselectedFlow(selectedFlow);
    }
  }

  // 初始化数据
  function load_insp_094_get_process_page_with_id(objNodeId) {
    if(isEmpty(objNodeId)){ // 没有objNodeId时不需调用这个接口,默认加载第一页数据
         setPageNum(1); 
         setGuid(toolUtil.guid());// 页码可能没有更改；需要手动通知更新下
         return
    }
    setPageNum(); // 清除掉页码
    setIsLoading(true);
    let params = { teamId, anchorNodeId: nodeId, nodeId: objNodeId, pageSize, order: orderBy, query: parseQuerey(searchQuery), keywords, desc: ascendingFlg ? "asc" : "desc" }
    http.insp_094_get_process_page_with_id(params)
      .then((res) => {
        if (res.resultCode === 200) {
          setPageNum(res.pageNum);
        } else {
          setIsLoading(false)
        }
      })
      .catch((e) => {
        console.log("exam_040_get_question_page" + e);
        setIsLoading(false)
      })
  }

  function load_insp_092_get_process_inst_list() {
    setIsLoading(true);
    let params = { nodeId, teamId, pageNum: pageNum, pageSize: pageSize, order: orderBy, query: parseQuerey(searchQuery), keywords, desc: ascendingFlg ? "asc" : "desc" }
    http.insp_092_get_process_inst_list(params)
      .then((res) => {
        if(res.resultCode == 200){
          const { processInstanceList = [], total } = res;
          processInstanceList.forEach(objNode => {
            objNode.key = objNode.nodeId //增加key属性，表格需要用key属性
            objNode.anchorNodeId = nodeId; // 项目nodeId
            objNode.realNodeId = objNode.nodeId;     // 对象nodeId，兼容快捷方式
            // tmsbug-10268：pc端巡检发现项列表，标题参考移动端补充列表信息
            // https://os.iteam.com/#/team/9176631781/issues/9747391877011/issue/74302168893183?queryId=59221509971460
            objNode.label =  objNode.chkpointName || objNode.name; // 巡检优先取值chekpointName
            objNode.icon = formatSvg(getSysIconByIdType(selectionList, eSelectionListId.Selection_1939_Icon, objNode.processIcon)) // 图标(取自系统图标)
            objNode.showMoreIcon = true; // 显示更多...
          });
          setRootNode(processInstanceList);
          setTotalCnt(total);
          setIsLoading(false);
          if (!isEmpty(createObjNode)) { // 需要跳转至新增的节点
            setTimeout(() => {
              setIssueSearchParams({ viewMode, issueNodeId: createObjNode.nodeId, queryId })// 用宏任务来处理setState更新Loading存在异步的问题
            })
            setCreateObjNode(); // 清除待添加的节点
          }
        } else {
          setIsLoading(false);
        }
      })
      .catch((e) => {
        console.log(e);
        setIsLoading(false);
      });
  }

  function parseQuerey(searchQuery){
    let query = null;
    if(!isEmpty(searchQuery)){
       // 先过滤begin为空的数据
       query =  searchQuery.filter(el => !isEmpty(el.begin)).map(search => {
        //  const inspType = subclassAttrList.find(attr => attr.nodeId == search.col)?.inspType;
        //  if(inspType == eRegionType.region || inspType == eRegionType.check){
        //   return {
        //       ...search,
        //       // 如果是日期 则为字符串，不需要把数组变成逗号
        //       begin: search.begin[search.begin.length - 1], // 取最后一个
        //   }
        //  }
         return {
              ...search,
              // 如果是日期 则为字符串，不需要把数组变成逗号
              begin: search.end ? search.begin : search.begin?.join(","), //数组变成以逗号隔开
          }
        })
    }
    return query
  }

  // 选中的节点
  useEffect(() => {
    if (!isLoading) {
      if(rootNode){
        let _idx = rootNode.findIndex(question => question.nodeId == issueNodeId);
        setPreviousIssueLinkDisabledFlg(pageNum == 1 && _idx == 0);
        setNextIssueLinkDisabledFlg(pageNum == Math.ceil(totalCnt / pageSize) && _idx == rootNode?.length - 1);

        if (totalCnt == 0) {
          globalUtil.info("暂无数据!");
          setIssueSearchParams({ viewMode, issueNodeId: null, queryId })
        } else {
          // 有数据，则进行数据处理, 根据objNodeId 路由跳转question详情
          if (issueNodeId) {
            // 查找列表中是否有objNodeId
            let foundNode = rootNode.find(question => question.nodeId == issueNodeId);
            if (foundNode) {
              scrollToSelectedKey(issueNodeId);
              return;
            } 
          }
          const _issueNodeId = rootNode[currentIssueIdx]?.nodeId;
          if(_issueNodeId){
            setIssueSearchParams({ viewMode, issueNodeId: _issueNodeId, queryId })
            scrollToSelectedKey(_issueNodeId);
          } else { // fix tmsbug-9738:删除发现项之后，详情没有清空，页面没有自动刷新, 列表无数据的处理, TODO:如此处理，数据被删除后点击路由会默认回到第一条数据
            setIssueSearchParams({ viewMode, issueNodeId: null, queryId })
          }
        }
      } else {
        setIssueSearchParams({ viewMode, issueNodeId: null, queryId })
      }
    } 
  }, [isLoading, rootNode, issueNodeId])

  //  滚动至选中的节点
  const scrollToSelectedKey = (objNodeId) => {
    if (!!objNodeId) {
      setTimeout(() => {
        let target = document.querySelector(`div[data-content-key="${objNodeId}"]`); // 节点 兼容长短列表
        if (!isContain(target)) {
          scrollToAnchor(target);
        }
      },500);
    }
  }

  // 上一条
  function gotoPreviousIssue() {
    const currentIssueIdx = rootNode.findIndex(_issue => _issue.nodeId == issueNodeId);
    if (currentIssueIdx == 0) {  //当前页的最后一行记录，再往前即需要向前翻页
      if (pageNum > 1) {
        setPageNum(pageNum - 1);
        // globalUtil.getQueryClient().setQueryData([track010, teamId, nodeId], (pageNum)=>(pageNum - 1)); //触发 useQueryIssue017_getIssueList 再次加载
        setCurrentIssueIdx(ePagination.PageSize_30 - 1);
      }
    } else {
      setIssueSearchParams({ viewMode, issueNodeId: rootNode[currentIssueIdx - 1].nodeId });
    }
  }

  // 下一条
  function gotoNextIssue() {
    const currentIssueIdx = rootNode.findIndex(_issue => _issue.nodeId == issueNodeId);
    if (currentIssueIdx == pageSize - 1) { //当前页的最后一行记录，再往后即需要向后翻页
      if (pageNum < Math.ceil(totalCnt / pageSize)) {
        setPageNum(pageNum + 1);
        // globalUtil.getQueryClient().setQueryData([track010, teamId, nodeId], (pageNo)=>(pageNo + 1)); //触发 useQueryIssue017_getIssueList 再次加载
        setCurrentIssueIdx(0);
      }
    } else {
      setIssueSearchParams({ viewMode, issueNodeId: rootNode[currentIssueIdx + 1].nodeId });
    }
  }

  // 右击菜单事件
  const onMoreBtnClick = ({ nodeItem, ctxType, ...args }) => {
    objNodeListMoreOps({
      teamId,
      nodeType: eNodeTypeId.nt_572_insp_processList_process,
      objNode: { ...nodeItem, key: nodeItem.nodeId },
      ctxType,
      setRootNode: setRootNode,
      setTotal: setTotalCnt,
      args,
    });
  }

  const onShowMoreButtonClick = (info) => {
    console.log("onShowMoreButtonClick info", info)
    contextboardRef.current.showContextBoard(info.event.nativeEvent || info.event, { ...info.node, nodeType: eNodeTypeId.nt_572_insp_processList_process }, info.node.nodeId, )
    setSelectedKeys([...selectedKeys, info.node.nodeId?.toString()]);// 右击菜单显示时，选中当前节点
    setContextKey(info.node.nodeId);
  }

  // react-contexify 隐藏事件，v6.0版本:废弃onHidden,改用onVisibilityChange属性，参考：https://github.com/fkhadra/react-contexify/pull/220
  function handleOnVisibilityChange(isVisible) {
    
    if (!isVisible && contextKey != issueNodeId) {  // 右击菜单隐藏时，取消选中当前节点
      let _selectedKeys = selectedKeys.filter(selectedKey => selectedKey != contextKey);
      setSelectedKeys([..._selectedKeys]);
    }
  }

  // 新建数据
  const handleUpdateData = (result) => {
    // 方案：新建的第一条流程，不确定在那一页（因为有排序规则的存在），所以需要先查询页码，再跳转
    load_insp_094_get_process_page_with_id(result?.nodeId);
    setCreateObjNode(result); // 缓存下新增的节点      
  }

  // 新建采集口径设置
  const openCreateInspectPartitionEvent = (objInfo) => {
      globalEventBus.emit("openCreatePartitionDrawerEvent", "", { ...objInfo });
  }

  return <>
    {/* 头部搜索栏 */}
    {/* <IssueTopSearchBar  objInfo={objInfo} setting320Result={setting320Result}/> */}
    <ScrollableHeader title={
      <Row justify="space-between" align="middle">
        <PageTitle teamId={teamId} nodeId={nodeId} powerLock refetchData={load_insp_092_get_process_inst_list} needShowPowerLock={needShowPowerLock}/>
        <Space size={10}>
          <Space className="issuehome-header-btns" size={2}>
            {canEditFlg && priSettingPermission(privSetting) ? (
              <a className="fontsize-12 fontcolor-normal" onClick={() => openCreateInspectPartitionEvent(objInfo)}>
                <Space size={2}>
                  <span className="iconfont shezhixitongshezhigongnengshezhishuxing fontsize-12"></span>
                  <span>采集口径设置</span>
                </Space>
              </a>
            ) : (
              <></>
            )}
          </Space>
        </Space>
      </Row>
    }>
      <InspectTopSearchBar
        subclassAttrList={subclassAttrList}
        allSpaceUserList={allSpaceUserList}
        // TODO: 巡检其他模块暂未加当前协作群成员的限制，暂时放开限制
        spaceUserList={spaceUserList}
        selectionList={selectionList}
        objInfo={objInfo}
        keywords={keywords}
        setKeywords={setKeywords}
        searchQuery={searchQuery}
        updateSearchQuery={updateSearchQuery}
        refetchIssueList={load_insp_092_get_process_inst_list}
        selectedFlow={selectedFlow}
        projectProcessList={projectProcessList}
        needShowPowerLock={needShowPowerLock}
      />
    </ScrollableHeader>
    {/* 短列表/长列表/看板 视图 */}
    <Layout style={{ flex: "auto", backgroundColor: "#fff", height: 0 }}>
      <Spin spinning={isLoading || isLoadingCreate} wrapperClassName="issueSpin">
        <Outlet context={{
          userList, spaceUserList, selectionList, objInfo, rootNode, totalCnt: totalCnt, pageNum, setPageNum,
          searchQuery,
          iconSelectionLid, setCurrentIssueIdx, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, gotoPreviousIssue, gotoNextIssue,
          ascendingFlg, setAscendingFlg, orderBy, setOrderBy, viewMode, projectInfo, setting320Result,
          selectedKeys, onMoreBtnClick, onShowMoreButtonClick, flowList, defaultFlow, handleUpdateData, load_insp_092_get_process_inst_list, projectProcessList, setIsLoadingCreate, useIssueSearchParams, isOA
        }} />
      </Spin>
    </Layout>
    {/* 右击菜单 */}
    <ContextBoard
      ref={contextboardRef}
      teamId={teamId}
      onMoreBtnClick={onMoreBtnClick}
      // onCreateBtnClick={onCreateBtnClick} 
      id={"inspect-context-menu"}
      handleOnVisibilityChange={handleOnVisibilityChange}
    />
    {/* 新手引导 */}
    <NoviceGuide nodeType={eNodeTypeId.nt_31705_objtype_issue_list} awakeFlg={0} />
  </>;
}