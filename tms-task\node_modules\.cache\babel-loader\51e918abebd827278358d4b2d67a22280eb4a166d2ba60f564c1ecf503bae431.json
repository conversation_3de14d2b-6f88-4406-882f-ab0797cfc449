{"ast": null, "code": "import{Layout}from\"antd\";import React,{useEffect,useState}from\"react\";import{Outlet,useNavigate,useParams}from\"react-router-dom\";import\"./DataPush.scss\";import*as httpQuickAccess from\"@common/api/http\";import{eNodeTypeId}from\"@common/utils/TsbConfig\";import NoviceGuide from\"@components/NoviceGuide\";// 系统订阅-新手引导\nimport{useQuerySetting202_getTeamAllUsers,useQueryTeam571_GetSpaceVaildUserList,useQuerySetting320GetNodePrivQuery}from\"@common/service/commonHooks\";import{eEnableFlg}from\"@common/utils/enum\";import SysSubscribeHeaderSearch from'./SubscribeChild/SysSubscribeHeaderSearch';import SysSubscribeSider from'./SubscribeChild/SysSubscribeSider';import SubscribeEdit from'./EditDrawer/SubscribeEdit';import OpDataPush from'./EditDrawer/OpDataPush';// 订阅类型/ 选择类型\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const typeList=[{key:1,label:\"搜索\"},{key:6,label:\"报表\"},{key:9,label:\"事件\"}];// 系统订阅主页\nexport default function DataPush(_ref){let{fromType,ganttName}=_ref;const{teamId,nodeId,nid:objNodeId}=useParams();const navigate=useNavigate();const{data:userList,dataUpdatedAt:dataUpdatedAtTeam}=useQuerySetting202_getTeamAllUsers(teamId,fromType!='gantt');// 人员\nconst{data:_userList,dataUpdatedAt:dataUpdatedAtSpace}=useQueryTeam571_GetSpaceVaildUserList(teamId,nodeId,0,fromType=='gantt');// 人员\nconst{data:{privWrite,privDelete}={},isLoading:isLoadingPriv,refetch:refetchPriv,dataUpdatedAt}=useQuerySetting320GetNodePrivQuery({teamId,nodeId});const[allMember,setAllMember]=useState([]);//所有成员\n//新建弹出框\nconst[modalVisible,setModalVisible]=useState(false);const[total,setTotal]=useState(0);const[dataSource,setDataSource]=useState([]);const[selectedKey,setSelectedKey]=useState(null);const[submitFlag,setSubmitFlag]=useState(true);const[submitFlag1,setSubmitFlag1]=useState(false);const[editSubmitFlag,setEditSubmitFlag]=useState(0);const[editData,setEditData]=useState(null);const[opType,setOpType]=useState(0);// 新建/编辑\nconst[nodeType,setNodeType]=useState();// 10801(系统订阅) / 10802(消息推送)\nconsole.log(\"nodeType\",nodeType);const[searchQuery,setSearchQuery]=useState({subscribersKey:null,typeKey:null,keyWordConfirm:'',addresseeKey:null,createDtList:[],entryDtList:[],nextDtList:[],sortValue:1,sortType:1,pageNum:1,isSort:false,pageChange:false,isDeleteOwn:false,isSubmit:false,isClear:false});const[loading,setLoading]=useState(false);const[isChange,setIsChange]=useState(false);useEffect(()=>{if(fromType!='gantt'){let memberList=[];(userList||[]).forEach(item=>{let obj={key:item.userId,label:item.userName,avatar:item.avatar,email:item.email};memberList.push(obj);});setAllMember([...memberList]);}},[dataUpdatedAtTeam]);useEffect(()=>{if(fromType=='gantt'){let memberList=[];(_userList||[]).forEach(item=>{let obj={key:item.userId,label:item.userName,avatar:item.avatar,email:item.email};memberList.push(obj);});setAllMember([...memberList]);}},[dataUpdatedAtSpace]);useEffect(()=>{if(allMember.length>0&&submitFlag&&editSubmitFlag!=2&&!searchQuery.isSubmit){if(searchQuery.isSort||editSubmitFlag==1){getData(1,false);}else{getData(0,true);}}},[allMember,submitFlag,editSubmitFlag,JSON.stringify(searchQuery)]);useEffect(()=>{if(!!objNodeId){getPageNo();setSelectedKey(objNodeId);}else{setSelectedKey(null);}},[objNodeId]);function getPageNo(){let params={teamId,nodeId,taskNodeId:objNodeId,pageSize:30,order:searchQuery.sortValue,desc:searchQuery.sortType};httpQuickAccess.team_598_get_sched_task_page_by_id(params).then(res=>{if(res.resultCode==200){setSearchQuery({...searchQuery,pageNum:res.pageNum});}});}//获取中树列表\nasync function getData(type,isNeedLoading){if(isNeedLoading){setLoading(true);}let params={teamId,nodeId,pageNum:searchQuery.pageNum,pageSize:30,order:searchQuery.sortValue,desc:searchQuery.sortType};if(!!searchQuery.subscribersKey){params.creatorUid=searchQuery.subscribersKey;}if(!!searchQuery.typeKey){params.objType=searchQuery.typeKey;}if(!!searchQuery.keyWordConfirm){params.keywords=searchQuery.keyWordConfirm;}if(!!searchQuery.addresseeKey){params.toUid=searchQuery.addresseeKey;}if(searchQuery.createDtList.length!=0){params.createDtBegin=searchQuery.createDtList[0]+' 00:00:00';params.createDtEnd=searchQuery.createDtList[1]+' 23:59:59';}if(searchQuery.entryDtList.length!==0){params.latestExecDtBegin=searchQuery.entryDtList[0]+' 00:00:00';params.latestExecDtEnd=searchQuery.entryDtList[1]+' 23:59:59';}if(searchQuery.nextDtList.length!==0){params.nextExecDtBegin=searchQuery.nextDtList[0]+' 00:00:00';params.nextExecDtEnd=searchQuery.nextDtList[1]+' 23:59:59';}await httpQuickAccess.team_537_get_sched_task_src_list(params).then(res=>{if(res.resultCode===200){setTotal(res.totalCount);let dataList=(res.schedNodeList||[]).map(item=>{let creatorItem=allMember.find(element=>element.key===item.creatorUid);item.key=item.id;item.avatar=creatorItem===null||creatorItem===void 0?void 0:creatorItem.avatar;return item;});if(type==0){if(dataList.length>0&&(isSearch()||!objNodeId||searchQuery.pageChange||searchQuery.isDeleteOwn||searchQuery.isClear||submitFlag1)){if(dataList[0].nodeType==eNodeTypeId.nt_10801_quick_access_sys_subscription_single){navigate(`subscribe/${dataList[0].id}`);}else{navigate(`datapush/${dataList[0].id}`);}}}else{if(!selectedKey&&dataList.length>0){if(dataList[0].nodeType==eNodeTypeId.nt_10801_quick_access_sys_subscription_single){navigate(`subscribe/${dataList[0].id}`);}else{navigate(`datapush/${dataList[0].id}`);}}}if(dataList.length==0){setSelectedKey(null);navigate(``);}setDataSource([...dataList]);}else{setTotal(0);setDataSource([]);}});setLoading(false);setSubmitFlag1(false);if(isNeedLoading){setEditSubmitFlag(0);}}// 添加订阅\nfunction addSubscribeClick(nodeType){console.log(\"nodeType\",nodeType);setOpType(0);setNodeType(nodeType);setSubmitFlag(false);setModalVisible(true);setEditData(null);};//关闭新建/编辑弹出层\nfunction closeDrawer(tp,obj){let isSubmit=false;if(tp==0){if(!!obj){return setIsChange(true);}isSubmit=true;}setIsChange(false);if(!submitFlag){if(tp==1){setSearchQuery({...searchQuery,isSort:false,pageChange:false,isDeleteOwn:false,isSubmit:isSubmit,pageNum:1});setSubmitFlag1(true);}else{setSearchQuery({...searchQuery,isSort:false,pageChange:false,isDeleteOwn:false,isSubmit:isSubmit});}setSubmitFlag(true);}else{setSearchQuery({...searchQuery,isSort:false,pageChange:false,isDeleteOwn:false,isSubmit:isSubmit});}if(editSubmitFlag==2){setEditSubmitFlag(1);}setModalVisible(false);}//是否处于搜索状态\nfunction isSearch(){return!!searchQuery.subscribersKey||!!searchQuery.typeKey||!!searchQuery.addresseeKey||!!searchQuery.keyWordConfirm||searchQuery.createDtList.length>0||searchQuery.entryDtList.length>0||searchQuery.nextDtList.length>0;}//字体个性化\nfunction getTextFontType(nameTextFontType,type){if(!!nameTextFontType){if(type==0){if(nameTextFontType.split(',')[0]==eEnableFlg.enable){return true;}}if(type==1){if(nameTextFontType.split(',')[1]==eEnableFlg.enable){return true;}}if(type==2){if(nameTextFontType.split(',')[2]==eEnableFlg.enable){return true;}}if(type==3){if(nameTextFontType.split(',')[3]==eEnableFlg.enable){return true;}}if(type==4){if(nameTextFontType.split(',')[2]==eEnableFlg.enable&&nameTextFontType.split(',')[3]==eEnableFlg.enable){return true;}}}return false;}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Layout,{className:\"subscribe ant-layout-sider-light\",children:[/*#__PURE__*/_jsx(SysSubscribeHeaderSearch,{allMember:allMember,searchQuery:searchQuery,setSearchQuery:setSearchQuery,typeList:typeList,fromType:fromType,getData:getData}),/*#__PURE__*/_jsxs(Layout,{hasSider:true,style:{flex:\"auto\"},className:\"ant-layout-sider-light\",children:[/*#__PURE__*/_jsx(SysSubscribeSider,{privWrite:privWrite,searchQuery:searchQuery,setSearchQuery:setSearchQuery,getData:getData,loading:loading,addSubscribeClick:addSubscribeClick,total:total,dataSource:dataSource,selectedKey:selectedKey,isSearch:isSearch,getTextFontType:getTextFontType}),/*#__PURE__*/_jsx(Outlet,{context:{dataSource,selectedKey,allMember,getData,setOpType,setNodeType,setEditSubmitFlag,setModalVisible,setEditData,getTextFontType,privWrite}}),/*#__PURE__*/_jsx(SubscribeEdit,{allMember:allMember,opType:opType,closeDrawer:closeDrawer,modalVisible:nodeType==eNodeTypeId.nt_10801_quick_access_sys_subscription_single&&modalVisible,setModalVisible:setModalVisible,editData:editData,isChange:isChange,setIsChange:setIsChange,selectedKey:selectedKey,typeList:typeList,fromType:fromType,ganttName:ganttName,nodeType:nodeType}),/*#__PURE__*/_jsx(OpDataPush,{allMember:allMember,opType:opType,closeDrawer:closeDrawer,modalVisible:nodeType==eNodeTypeId.nt_10802_quick_access_sys_subscription_single_datapush&&modalVisible,setModalVisible:setModalVisible,editData:editData,isChange:isChange,setIsChange:setIsChange,selectedKey:selectedKey,typeList:typeList,fromType:fromType,ganttName:ganttName,nodeType:nodeType})]})]}),/*#__PURE__*/_jsx(NoviceGuide,{nodeType:eNodeTypeId.nt_108_quick_access_sys_subscription,awakeFlg:0})]});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}