import { useEffect, useState, useRef } from "react"
import { Modal,Form,Input,Radio,Badge,InputNumber,Row,Image,Button, Table, Checkbox, Dropdown, Menu, Card, Tooltip, Popconfirm } from "antd"
import DraggablePopUp from "@components/DraggablePopUp";import { useNavigate, useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import { getTeamList } from "@/team/store/actionCreators";
import { CheckCircleOutlined,CloseOutlined, ExclamationCircleOutlined, QuestionCircleOutlined, DownOutlined } from '@ant-design/icons';
import {
  team_703_calc_price,
  team_704_submit_order,
  team_705_get_order_status,
  team_711_get_team_product_list,
  team_735_bind_user_coupon,
  team_736_unbind_user_coupon, } from "@common/api/http"
import { 
  setting_105_get_team_detail_query,
  team_701_get_product_list_query,
  team_706_get_free_team_count_by_user_query,
  useQueryGetUserValidCoupon
} from "@common/api/query/query";
import {useDebounce} from "@common/hook"
import QRCode from "qrcode";
import "./CreateTeam.scss"
import TLoading from "./TLoading";
import { useQueries } from "@tanstack/react-query";
import moment from "moment";
import { globalUtil } from "@common/utils/globalUtil";
import { eCouponType, eOrderStatus, eProductGroupId, eProductId, eProductStatus } from "@common/utils/enum";
import DraggableDrawer from "@components/DraggableDrawer";

const { Search } = Input;
const { Column,ColumnGroup } = Table;

export const CREATETYPE_CREATE = 0; // 创建团队
export const CREATETYPE_UPGRADE = 1; // 升级至企业版
// export const CREATETYPE_EXPAND = 2; // 成员扩容
// export const CREATETYPE_RENEWAL = 3; // "应用"续费
// export const CREATETYPE_BUY = 4; // "应用"新购

const formItemLayout = {
  labelCol: {
    span: 0,
  },
};

/**
 * @description 创建工作区
 * @param {*} param0 
 * @returns 
 */
export function CreateTeam({onCancel,onOk,teamId,type,priceData,productList,setIsChange}){
  const {teamId: localTeamId} = useParams();
  const queryResult = useQueries({
    queries: [
      team_701_get_product_list_query(),
      team_706_get_free_team_count_by_user_query(),
      {
        ...setting_105_get_team_detail_query(teamId),
        enabled: !!teamId
      },
      // team_702_get_team_package_duration_rebate_query(), //20231025 Jim Song, 月度折扣接口已废弃，伴随"应用"列表接口一起范围
    ]
  })
  const [team701Result, team706Result, setting105Result ] = queryResult;
  const { data: { couponList } = { couponList: [] }, refetch: refetchGetUserValidCoupon } = useQueryGetUserValidCoupon(teamId);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const [discountForm] = Form.useForm();
  const [cdiskCheck,setCdiskCheck] = useState(true);

  const [priceInfo,setPriceInfo] = useState({
    originalPrice: 0, regularAmt: 0, promoAmt: 0, discountReduction: 0, payPrice: 0, couponReduction: 0, resultCode: null, resultMessage: null
  })

  const freeAuthUserQuota1000 = 1000; //1000个免费用户数

  // 是否确认购买
  const [isPaying,setIsPaying] = useState(false);
  const [payInfo,setPayInfo] = useState();
  const [showPriceDetail,setShowPriceDetail] = useState(false);
  const vipFlg = Form.useWatch('isEnterpriseEdition', form);
  const [discountCodeVisible, setDiscountCodeVisible] = useState(false);
  const [payCode,setPayCode] = useState({orderId: null});
  // const [payCode1,setPayCode1] = useState({
  //   orderInfo:{
  //     orderId: null,
  //   },
  //   payUrl: null
  // });

  const [url, setUrl] = useState();
  const [url1, setUrl1] = useState();
  const [spining,setSpining] = useState(false);
  const discountCodeRef = useRef({});
  const [selectedProductList,setSelectedProductList] = useState([]);
  const [tipShow,setTipShow] = useState(false);
  const [couponSelect,setCouponSelect] = useState(null);
  const [refreshingFlg,setRefreshingFlg] = useState(false);
  const [myCouponList,setMyCouponList] = useState([]);

  // 获取协作群基本信息
  useEffect(()=>{
    if(type !== CREATETYPE_CREATE){
      let res = setting105Result.data
      if (res && res.resultCode === 200) {
        form.setFieldValue("teamName", res.name)
      }
    }
  },[setting105Result.dataUpdatedAt])

  useEffect(() => {
    if(!!priceData?.code && priceData?.isEnterpriseEdition) {
      setCouponSelect({couponCode: priceData.code});
    }
  },[priceData?.code])

  useEffect(()=>{
    if((couponList||[]).length > 0){
      setMyCouponList([...couponList]);
    }
  },[JSON.stringify(couponList)]);

  useEffect(()=>{
    if((team701Result.data?.productList||[]).length > 0){
      let formatList = team701Result.data.productList.filter(group => vipFlg == 1 ? group.groupId != eProductGroupId.Pgid_1_OS : true)
      let _prodGrpList = [];
      formatList.forEach(product => {
        let item = _prodGrpList.find(_product => product.groupId == _product.groupId)
        if(!item){
          _prodGrpList.push({groupName: product.groupName, groupId: product.groupId, products: [product]});
        }else{
          item.products.push(product)
        }
      });
      let allProductsList = [];
      productList = productList || []; //尝试修复 tmsbug-8699
      _prodGrpList.forEach(_prodGrp => {
        let _productList = _prodGrp.products.map((config,index) => {
          // !!data.lastExpirationDt && leftDaysFormat(data.lastExpirationDt) < 0
          let lastMemberNo = type === CREATETYPE_UPGRADE && (productList.find(product => product.productId == config.productId)?.freeFlg) != 1 ?
          ((productList.find(product => product.productId == config.productId)?.authCnt || 0) > 0 ?
              productList.find(product => product.productId == config.productId)?.authCnt
              : 0
          ) : 0;

          let lastExpirationDt = type === CREATETYPE_UPGRADE && (productList.find(product => product.productId == config.productId)?.freeFlg) != 1 ?
          (!!productList.find(product => product.productId == config.productId)?.expirationDt ?
              productList.find(product => product.productId == config.productId)?.expirationDt
              : ''
          ) : '';

          let checked = (selectedProductList||[]).find(record => record.productId == config.productId)?.checked
          let item = {
            ...config,
            key: config.productId,
            isRowSpan: index == 0 ? true : false,
            prodsLength: index == 0 ? _prodGrp.products.length : 0,
            memberNo: lastMemberNo > 0 && !!lastExpirationDt && leftDaysFormat(lastExpirationDt) < 0 ? lastMemberNo : 0,
            memberNoShow: lastMemberNo > 0 && !!lastExpirationDt && leftDaysFormat(lastExpirationDt) < 0 ? lastMemberNo : 0,
            lastMemberNo: lastMemberNo,
            nextMemberNo: type === CREATETYPE_UPGRADE &&
                (productList.find(product => product.productId == config.productId)?.freeFlg) != 1 ?
                ((productList.find(product => product.productId == config.productId)?.authCnt || 0) > 0 ?
                    productList.find(product => product.productId == config.productId)?.authCnt : 0
                ) : 0,
            durationMonth: null,
            name: '',
            rebate: '',
            effectBeginDt: '',
            expirationDt: '',
            lastExpirationDt: lastExpirationDt,
            originalPrice: '',
            discountPrice: '',
            checked: config.productId == eProductId.Pid_13_Cdisk ? cdiskCheck :
              (checked == true || checked == false) ? checked :
                (config.statusType == eProductStatus.Status_2_QA || config.statusType == eProductStatus.Status_3_Unreleased) ? false : true,
          };
          return item
        });
        allProductsList = allProductsList.concat(_productList)
      });
      if(!!priceData?.teamPackage){
        let list1 = JSON.parse(priceData.teamPackage)
        allProductsList = allProductsList.map(config => {
          config.memberNo = list1.find(a => a.productId == config.productId)?.memberNo
          config.durationMonth = list1.find(a => a.productId == config.productId)?.durationMonth
          config.name = list1.find(a => a.productId == config.productId)?.name
          config.rebate = list1.find(a => a.productId == config.productId)?.rebate
          return config
        })
      }
      setSelectedProductList([...allProductsList]);
      if(!!priceData?.teamPackage){
        setTimeout(()=>{
          load_team_703_calc_price(priceData?.code,allProductsList)
        },3000)
      }
    }
  },[JSON.stringify(team701Result),vipFlg]);

  function checkBoxChange(e,key){
    let checked = e.target.checked
    let item = selectedProductList.find(data => data.key == key)
    if(!!item){
      if(key == eProductId.Pid_13_Cdisk){
        setCdiskCheck(checked)
      }
      item.checked = checked
      let list = []
      if(checked){
        list = (item.dependsOnIds||'').split(',').filter(_productId => !!_productId)
      }else{
        list = (item.dependedByIds||'').split(',').filter(_productId => !!_productId)
      }
      list.forEach(_productId => {
        let _item = selectedProductList.find(data => data.key == _productId)
        _item.checked = checked
      })
      setSelectedProductList([...selectedProductList]);
    }
  }

  function memberNoChange(item,clickType){
    let data = selectedProductList.find(data => data.key == item.key)
    if(clickType == 0){
      if(type == CREATETYPE_CREATE && data.memberNo <= 0){
        return
      }
      if(type == CREATETYPE_UPGRADE && data.memberNo <= -data.lastMemberNo){
        return
      }
    }
    if(clickType == 1){
      if(data.memberNo >= 1000){
        return
      }
    }
    data.memberNo = clickType == 0 ? data.memberNo - 1 : clickType == 1 ? data.memberNo + 1 : (data.memberNoShow||0)
    data.memberNoShow = clickType == 0 ? data.memberNoShow - 1 : clickType == 1 ? data.memberNoShow + 1 : (data.memberNoShow||0)
    if(newBuyFormat(data) == 1){
      if(!data.durationMonth){
        data.durationMonth = 12
        data.name = (team701Result.data?.monthPromoList || []).find(item1 => item1.monthCnt == 12)?.monthCntDesc||''
        data.rebate = (team701Result.data?.monthPromoList || []).find(item1 => item1.monthCnt == 12)?.promoRateDesc||''
      }
      if(data.memberNo <= 0){
        data.durationMonth = null
        data.name = ''
        data.rebate = ''
      }
    }
    let teamName = form.getFieldValue('teamName');
    isChange(teamName,selectedProductList);
    load_team_703_calc_price(couponSelect?.couponCode);
    setSelectedProductList([...selectedProductList]);
  }

  function memberNoChangeW(item,value){
    let data = selectedProductList.find(data => data.key == item.key)
    data.memberNo = value
    data.memberNoShow = value
    if(newBuyFormat(data) == 1){
      if(!data.durationMonth){
        data.durationMonth = 12
        data.name = (team701Result.data?.monthPromoList || []).find(item1 => item1.monthCnt == 12)?.monthCntDesc||''
        data.rebate = (team701Result.data?.monthPromoList || []).find(item1 => item1.monthCnt == 12)?.promoRateDesc||''
      }
      if((value||0) <= 0){
        data.durationMonth = null
        data.name = ''
        data.rebate = ''
      }
    }
    setSelectedProductList([...selectedProductList]);
  }

  function durationMonthChange(item,item1){
    let data = selectedProductList.find(data => data.key == item.key)
    if(!!item1){
      data.durationMonth = item1.monthCnt
      data.name = item1.monthCntDesc
      data.rebate = item1.promoRateDesc
      if(newBuyFormat(data) == 1){
        if(data.memberNo == 0){
          data.memberNo = 1
          data.memberNoShow = 1
        }
      }
    }else{
      data.durationMonth = null
      data.name = ''
      data.rebate = ''
      if(newBuyFormat(data) == 1){
        data.memberNo = 0
        data.memberNoShow = 0
      }
    }
    let teamName = form.getFieldValue('teamName');
    isChange(teamName,selectedProductList);
    load_team_703_calc_price(couponSelect?.couponCode);
    setSelectedProductList([...selectedProductList]);
  }

  function teamNameChange(e){
    isChange(e.target.value,selectedProductList);
  }

  function isChange(teamName,currentList){
    if(type == CREATETYPE_CREATE && !!teamName){
      setIsChange(true);
    }else{
      if(currentList.filter(product => product.memberNo != 0 || !!product.durationMonth).length > 0){
        setIsChange(true);
      }else{
        setIsChange(false);
      }
    }
  }

  const _onOk = useDebounce(async () => {
    let values = form.getFieldsValue(true)
    if(!values.teamName){
      globalUtil.warning('请输入团队名称');
      return
    }
    if(vipFlg == 0 && selectedProductList.filter(product => product.checked && product.productId != eProductId.Pid_11_Explorer && product.productId != eProductId.Pid_12_Space).length <= 0){
      globalUtil.warning('至少启用一个应用');
      return
    }
    let validFlg = false
    if(priceInfo.payPrice <= 0){
      if(type == CREATETYPE_CREATE){
        if(vipFlg == 1 && (priceInfo.couponReduction||0) <= 0){
          globalUtil.warning('请选择套餐');
          return
        }
        validFlg = true
      }else{
        if(priceInfo.payPrice < 0){
          setTipShow(true);
          return
        }
        if(selectedProductList.filter(data => data.memberNo < 0).length == 0 && (priceInfo.couponReduction||0) <= 0){
          globalUtil.warning('请选择套餐');
          return
        }
        validFlg = true
      }
    }
    let _prodsInCart = selectedProductList  //购物车(有数量的产品）
      .filter(data => data.statusType != eProductStatus.Status_3_Unreleased && (data.memberNo != 0 || !!data.durationMonth))
      .map(data => ({
        productId: data.productId,
        adjustMonthCnt: !!data.durationMonth ? data.durationMonth : 0,
        adjustAuthCnt: data.memberNo,
      }))
    setSpining(true);
    let payInfo1 = {
      teamName: values.teamName,
      orderType: type == CREATETYPE_CREATE ? 1 : type == CREATETYPE_UPGRADE ? 2 : '',
    }
    let couponCode = null;
    if(vipFlg == 0){
      payInfo1.productList = [];
      payInfo1.productIdList = selectedProductList.filter(product => product.checked).map(product => {return product.productId}) //20250731 后端需要这个数组字段来判断
      //payInfo1.disabledProductList = selectedProductList.filter(product => !product.checked).map(product => {return product.productId}) //后端不需要，禁用掉
    }else{
      payInfo1.productList = _prodsInCart
      if(!!couponSelect && priceInfo.couponReduction > 0){
        couponCode = couponSelect.couponCode
      }
    }
    if(validFlg){
      await upload_team_704_team_package_pay_order(payInfo1,couponCode,1);
    }else{
      await upload_team_704_team_package_pay_order(payInfo1,couponCode,2);
    }
    setPayInfo(payInfo1)
    setSpining(false);
    // } else {
    //   setSpining(true);
    //   let _packageList = dataSource.map(data => ({
    //     objType: data.objType,
    //     enableFlg: data.checked ? 1: 0,
    //     typeDesc: data.typeDesc,
    //     userCnt: 0
    //   }))
    //   // 创建免费团队
    //   let request = {
    //     teamName: values.teamName,
    //     isEnterpriseEdition: 0,
    //     pkgOpCode: type === CREATETYPE_CREATE?"1000":
    //                type === CREATETYPE_UPGRADE?"1001":"",
    //     packageList: _packageList
    //   }
    //   //备注: 前端不再需要调用 create_team接口，这里暂且注释掉 20231025 @garry，这里也是走 创建订单的流程
    //  /* await team_002_create_team(request).then(result => {
    //     if(result.resultCode == 200){
    //       sucessPay(result.teamId)
    //     }
    //   });*/
    // }
    // setSpining(false);
  },500)

  const cancelPay = () => {
    setIsPaying(false);
  }

  const sucessPay = (_teamId) => {
    dispatch(getTeamList(localTeamId));
    // 刷新团队列表
    cancelPay()
    onCancel()
    onOk?.()
    if(!teamId){
      // 弹出跳转弹窗
      Modal.confirm({
        title: '提示',
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }}/>,
        content: <div style={{ display: 'flex', flexDirection: 'column' }}>
          <span>团队创建成功，是否切换至 {form.getFieldValue('teamName')} ？</span>
          <span>点击"否"，停留在当前页面；</span>
          <span>点击"是"，切换至新的团队。</span>
        </div>,
        okText: '是，切换团队',
        cancelText: '否',
        onOk:() =>{
          navigate(`/team/${_teamId}`)
        }
      });
    }
  }

  // 打开优惠码校验框
  const openDiscountCode = () => {
    if(discountCodeVisible) return
    setDiscountCodeVisible(!discountCodeVisible)
  }

  // 关闭优惠码校验框
  const closeDiscountCode = () => {
    setDiscountCodeVisible(!discountCodeVisible)
    discountForm.resetFields()
    discountCodeRef.current = {}
    //clearDiscountCodeVerification()
  }

  // 校验优惠码
  const verificationDiscountCode = (value, event) => {
    if(event.nativeEvent.type === 'click' && !value) {
      //if(event.nativeEvent.type === 'click') clearDiscountCodeVerification()
      if(!value) //globalUtil.warning("请填写优惠码");
      return
    }
    team_735_bind_user_coupon({couponCode: value }).then(res => {
      if(res.resultCode == 200){
        discountForm.resetFields()
        discountCodeRef.current = {}
        if((priceInfo.payPrice||0) > 0){
          load_team_703_calc_price(!!couponSelect?.couponCode ? couponSelect.couponCode : value,[],!couponSelect?.couponCode);
        }else{
          refetchGetUserValidCoupon()
        }
      }
      if(res.resultCode == 500){
        globalUtil.error(res.resultMessage);
      }
    });
  }

  function warn(discountCode="",dataList=[]){
    let _packageList = (dataList.length > 0 ? dataList : selectedProductList).filter(data => {
      if(type == CREATETYPE_CREATE){
        return (data.memberNo > 0 && !!data.durationMonth)
      }else{
        if(!!newBuyFormat(data)){
          return (data.memberNo > 0 && !!data.durationMonth)
        }
        return (data.memberNo != 0 || !!data.durationMonth)
      }
    }).map(data => ({
      productId: data.productId,
      adjustMonthCnt: !!data.durationMonth ? data.durationMonth : 0,
      adjustAuthCnt: data.memberNo,
    }))
    if(_packageList.length == 0){
      if(!!discountCode){
        globalUtil.warning('请选择套餐');
      }
      return []
    }
    return _packageList
  }

  // 获取支付价格
  const load_team_703_calc_price = useDebounce((discountCode="", dataList=[], couponUnBindFlg=false)=>{
    let _packageList = warn(discountCode,dataList);
    if(_packageList.length == 0){
      setPriceInfo({
        originalPrice: 0,
        regularAmt: 0,
        promoAmt: 0,
        discountReduction: 0,
        payPrice: 0,
        couponReduction: 0,
        resultCode: null,
        resultMessage: null
      });
      setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList).map(data => {
        data.effectBeginDt = ''
        data.expirationDt = ''
        data.originalPrice = ''
        data.discountPrice = ''
        data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0
        return data
      })]);
      return
    }
    let request = { teamId, productList: _packageList, orderType: type == CREATETYPE_UPGRADE ? 2 : 1, couponCode: discountCode }
    team_703_calc_price(request).then(result =>{
      if(result.resultCode == 200){
        const {listingAmt, promoAmt, regularAmt, volumePromoAmt, netAmt,couponAmt,orderProductList,couponList} = result;
        setPriceInfo({
          originalPrice: listingAmt,
          regularAmt: regularAmt,
          promoAmt: promoAmt,
          discountReduction: volumePromoAmt,
          payPrice: netAmt,
          couponReduction: couponAmt,
          resultCode: !!discountCode ? result.resultCode : null,
          resultMessage: !!discountCode ? "有效优惠码" : ""
        });
        if(couponUnBindFlg){
          let coupon = (couponList||[]).find(_coupon => _coupon.couponCode == discountCode);
          if(!!coupon && !couponCanUse(coupon)){
            setCouponSelect(coupon);
          }
        }
        setMyCouponList([...(couponList||[])]);
        if((orderProductList||[]).length > 0){
          (dataList.length > 0 ? dataList : selectedProductList).forEach(data => {
            let pkgItem = orderProductList.find(pkg => pkg.productId == data.productId)
            if(!!pkgItem){
              data.effectBeginDt = pkgItem.effectiveDt
              data.expirationDt = pkgItem.expirationDt
              data.originalPrice = pkgItem.listingAmt
              data.discountPrice = pkgItem.amtSubstractPromo
              data.nextMemberNo = pkgItem.authCnt
            }else{
              data.effectBeginDt = ''
              data.expirationDt = ''
              data.originalPrice = ''
              data.discountPrice = ''
              data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0
            }
          });
          setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList)]);
        }
      } else if(result.resultCode == 500){
        const {listingAmt=0, promoAmt=0, regularAmt=0, volumePromoAmt=0, netAmt=0,couponAmt=0,orderProductList=[],couponList} = result;
        setPriceInfo({
          originalPrice: listingAmt,
          regularAmt: regularAmt,
          promoAmt: promoAmt,
          discountReduction: volumePromoAmt,
          payPrice: netAmt,
          couponReduction: couponAmt,
          resultCode: !!discountCode ? result.resultCode : null,
          resultMessage: !!discountCode? result.resultMessage : null
        });
        //setMyCouponList([...(couponList||[])]);
        if((orderProductList||[]).length > 0){
          (dataList.length > 0 ? dataList : selectedProductList).forEach(data => {
            let pkgItem = orderProductList.find(pkg => pkg.productId == data.productId)
            if(!!pkgItem){
              data.effectBeginDt = pkgItem.effectiveDt
              data.expirationDt = pkgItem.expirationDt
              data.originalPrice = pkgItem.listingAmt
              data.discountPrice = pkgItem.amtSubstractPromo
              data.nextMemberNo = pkgItem.authCnt
            }else{
              data.effectBeginDt = ''
              data.expirationDt = ''
              data.originalPrice = ''
              data.discountPrice = ''
              data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0
            }
          });
          setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList)]);
        }
      }
    })
  },500)

  const onFieldsChange = (args) => {
    // console.log("我正在瞬息万变！！！！", args);
  }

  if(team701Result.isLoading || team706Result.isLoading || (!!teamId && setting105Result.isLoading)) return <TLoading/>

  const userCntHelp = vipFlg === 0?
    `限免基础版(免费)团队${team706Result.data?.teamCountFree}个,已使用${team706Result.data?.enabledTeamCountFree}个。`
    :
    ""

  // 上传信息获取支付Url
  const upload_team_704_team_package_pay_order = async(payInfo, discountCode ,flag)=>{
    let request = {
      ...payInfo,
      teamId,
      couponCode: discountCode || null
    }
    await team_704_submit_order(request).then((result) => {
      if(result.resultCode == 200){
        if(flag == 1){
          sucessPay(result?.teamId);
          return
        }
        const {orderId, alipayUrl, wxpayUrl} = result
        load_pay_code_url(alipayUrl,'zhifubao')
        load_pay_code_url(wxpayUrl,'weixin')
        setPayCode({orderId: orderId})
        if(flag == 2){
          if(isPaying) return
          setIsPaying(true)
        }
      } else {
        
      }
    })
  }

  // 生成支付二维码
  const load_pay_code_url = async(url, payMethod) => {
    try {
      if (!url) {
        console.error('Invalid URL for QR code generation');
        if(payMethod == 'zhifubao'){
          setUrl1(null);
        } else {
          setUrl(null);
        }
        return;
      }

      const qrOptions = { errorCorrectionLevel: 'H', margin: 2, width: 120, color: { dark: '#000000', light: '#ffffff' } };
      const image_url = await QRCode.toDataURL(url, qrOptions);
      if(payMethod == 'zhifubao'){
        setUrl1(image_url);
      } else {
        setUrl(image_url);
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      if(payMethod == 'zhifubao'){
        setUrl1(null);
      } else {
        setUrl(null);
      }
    }
  }

  const navigateTo = (type) => {
    let url = ''
    if(type == 1){
      url = 'personal/personaldata'
    }else{
      url = 'personal/invoice'
    }
    window.open(
      `${window.location.origin}/#/${url}`
    );
  }

  function couponSelectChange(coupon){
    if(!couponCanUse(coupon)){
      if(couponSelect?.couponCode == coupon.couponCode){
        setCouponSelect(null);
        load_team_703_calc_price();
      }else{
        setCouponSelect(coupon);
        load_team_703_calc_price(coupon.couponCode);
      }
    }
  }

  function couponCanUse(coupon){
    if(!!coupon.expirationDt){
      if(moment(coupon.expirationDt).isBefore(moment())){
        return '优惠券已过期'
      }
    }
    if(coupon.orderAmtFlg == 0){//金额是否适用，0---不适用   1---适用
      return '订单金额不在适用范围'
    } 
    if(!!teamId){//购买应用
      if(!!coupon.teamId && coupon.teamId != teamId){//如果指定团队和该团队不匹配，无法使用
        return `指定团队可用(${coupon.teamId})`
      }
      if(coupon.usedByTeamFlg == 1){//券有没有被该团队使用过，0---未使用过  1---使用过
        return '本团队已使用过该券'
      }
      //20250401 Jim Song, tmsbug-11993 全已经被使用过，也需要显示为灰色
      if(coupon.usedByUserFlg == 1){//有没有被该用户使用过， 0---未使用过  1---使用过
        return '您已使用过该券'
      }
    }else{//创建团队
      if(!!coupon.teamId){//是否指定团队使用，不为空即表示指定团队使用，为空表示任意团队都可使用
        return `指定团队可用(${coupon.teamId})`
      }
      //因为是新建团队，所以不必关注是否被团队使用过
      if(coupon.usedByUserFlg == 1){//有没有被该用户使用过， 0---未使用过  1---使用过
        return '您已使用过该券'
      }
    }
    return ''
  }

  function deleteCoupon(coupon){
    team_736_unbind_user_coupon({couponId: coupon.couponId}).then(res => {
      if(res.resultCode == 200){
        if((priceInfo.payPrice||0) > 0){
          if(couponSelect?.couponCode == coupon.couponCode){
            setCouponSelect(null);
            load_team_703_calc_price();
          }else{
            load_team_703_calc_price(couponSelect?.couponCode);
          }
        }else{
          refetchGetUserValidCoupon()
        }
      }
    });
    //setMyCouponList([...myCouponList.filter(myCoupon => myCoupon.couponId != coupon.couponId)]);
  }

  function leftDaysFormat(date){
    let days = moment(moment(date).format('YYYYMMDD')).diff(moment(moment().format('YYYYMMDD')),'days') - 1
    if(days >= 0){
      return days
    }
    return -1
  }

  function newBuyFormat(data){
    if(data.lastMemberNo <= 0 || !data.lastExpirationDt){
      return 1 //新购
    }
    if(!!data.lastExpirationDt && leftDaysFormat(data.lastExpirationDt) < 0){
      return 2 //过期
    }
    return 0 //在有效期内
  }

  return (<>
  <Form
    form={form}
    name="basic"
    onFinish={_onOk}
    initialValues={{
      teamName:"", 
      isEnterpriseEdition: priceData?.isEnterpriseEdition == 1 ? 1 : (type===CREATETYPE_CREATE?0:1)
    }}
    {...formItemLayout}
    style={{paddingRight:24,paddingLeft:24}}
    autoComplete="off"
    onFieldsChange={onFieldsChange}>
     
    <Form.Item
      className="team-name-label"
      label="团队名称"
      name="teamName">
      <Input className="team-name-input" autoComplete="off" maxLength={100} disabled={type !== CREATETYPE_CREATE} onChange={teamNameChange}/>
    </Form.Item>

    <Form.Item 
      label="版本选择" 
      name="isEnterpriseEdition" 
      style={type == CREATETYPE_CREATE?{marginBottom:10}:{display:"none"}}>
        <Radio.Group buttonStyle="solid">
          <Radio.Button value={0} style={{borderTopLeftRadius:5,borderBottomLeftRadius:5,width:120,textAlign:'center'}}>基础版(免费)</Radio.Button>
          <Radio.Button value={1} style={{borderTopRightRadius:5,borderBottomRightRadius:5,width:120,textAlign:'center'}}>VIP版</Radio.Button>
        </Radio.Group>
    </Form.Item>
  </Form>
  <Form
    style={{paddingLeft:24}}
    name="productCheck"
    initialValues={{
      packageList: [],
    }}
    {...formItemLayout}
    autoComplete="off"
  >
    <Form.Item 
      className="product-selection-formItem"
      label="选择应用"
      name="packageList"
      style={{marginBottom:10}}>
      <Table
        size="small"
        className="before-header"
        dataSource={selectedProductList}
        pagination={false}
        bordered
        scroll={{y:`calc(100vh - ${vipFlg == 1?400:300}px)`}}
      >
        <Column
          title={<div style={{ display: 'flex', justifyContent: 'center' }}>类别</div>}
          dataIndex={'groupName'}
          key={'groupName'}
          width={vipFlg == 1 ? (type == CREATETYPE_CREATE ? 200 : 135) : '19%'}
          render={(groupName) => <div style={{ display: 'flex', justifyContent: 'center' }}>{groupName}</div>}
          onCell={(item) => {
            if (item.isRowSpan) {
              return { rowSpan: item.prodsLength }
            } else {
              return { rowSpan: 0 }
            }
          }}
        />
        <Column
          title={'应用'}
          dataIndex={'productName'}
          key={'productName'}
          width={vipFlg == 1 ? (type == CREATETYPE_CREATE ? 220 : 155) : '19%'}
          render={(productName, item) => (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
              {productName}
              {item?.statusType == eProductStatus.Status_2_QA && <span style={{ color: '#70B603', fontSize: 12, marginLeft: 10 }}>内测中</span>}
              {item?.statusType == eProductStatus.Status_3_Unreleased && <span style={{ color: '#F59A23', fontSize: 12, marginLeft: 10 }}>即将推出</span>}
            </div>
          )}
          onCell={() => { return { rowSpan: 1 }}}
        />
        {type == CREATETYPE_CREATE ?
          <Column
            title={<div style={{textAlign:'center'}}>{vipFlg == 1 ? '授权人数' : '成员数'}</div>}
            dataIndex={'memberNo'}
            key={'memberNo'}
            width={vipFlg == 1 ? 150 : '19%'}
            render={(memberNo,item) => {
              if(vipFlg == 1){
                if(item.groupId == eProductGroupId.Pgid_1_OS){
                  return (<div style={{textAlign:'center'}}>-</div>)
                }
                return (
                  <div style={{display:'flex',alignItems:'center',justifyContent:'center'}}>
                    <div className={`member-edit-primary`}>
                      <InputNumber 
                        controls={false}
                        style={{width:110}}
                        size="small"
                        precision={0}
                        min={0}
                        max={1000}
                        disabled={item?.statusType == eProductStatus.Status_3_Unreleased}
                        addonBefore={
                          <Button 
                            type={'primary'} 
                            disabled={item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == 0}
                            style={{borderTopLeftRadius:5,borderBottomLeftRadius:5,borderRight:'0px',borderColor: (item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == 0) && '#d9d9d9'}}
                            onClick={()=>memberNoChange(item,0)}
                          >
                            -
                          </Button>
                        }
                        addonAfter={
                          <Button 
                            type={'primary'} 
                            disabled={item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == {freeAuthUserQuota1000}}
                            style={{borderTopRightRadius:5,borderBottomRightRadius:5,borderLeft:'0px',borderColor: (item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == 1000) && '#d9d9d9'}}
                            onClick={()=>memberNoChange(item,1)}
                          >
                            +
                          </Button>
                        }
                        value={memberNo}
                        onChange={(value)=>memberNoChangeW(item,value)}
                        onPressEnter={()=>memberNoChange(item,2)}
                        onBlur={()=>memberNoChange(item,2)}
                      />
                    </div>
                  </div>
                )
              }
              else{
                return (<div style={{textAlign:'center'}}>{freeAuthUserQuota1000}</div>)
              }
            }}
            onCell={() => { return { rowSpan: 1 }}}
          />
        :
          <ColumnGroup title={'购买前'} className="top-header-a">
            <Column
              title={'授权数'}
              className="top-header-a"
              dataIndex={'lastMemberNo'}
              key={'lastMemberNo'}
              width={90}
              render={(lastMemberNo) => <div>{lastMemberNo||0}人</div>}
              onCell={() => { return { rowSpan: 1 }}}
            />
            <Column
              title={'对象新建数'}
              className="top-header-a"
              dataIndex={'lastResourceNo'}
              key={'lastResourceNo'}
              width={150}
              render={(lastResourceNo,item) => {
                if(!!item.lastExpirationDt && (item.lastMemberNo||0) > 0){
                  return  (<div style={newBuyFormat(item) == 2 ? {color: '#999'} : {}}>∞</div>);
                }
                return  (<div>{item?.freeQuotaDesc||'-'}</div>);
              }}
              onCell={() => { return { rowSpan: 1 }}}
            />
            <Column
              title={'有效期至'}
              className="top-header-a"
              dataIndex={'lastExpirationDt'}
              key={'lastExpirationDt'}
              width={100}
              render={(lastExpirationDt,item) => {
                if(!!lastExpirationDt && (item.lastMemberNo||0) > 0){
                  return (<div style={newBuyFormat(item) == 2 ? {color: '#999'} : {}}>{moment(lastExpirationDt).format('YYYY-MM-DD')}</div>);
                }
                return (<div>∞</div>);
              }}
              onCell={() => { return { rowSpan: 1 }}}
            />
          </ColumnGroup>
        }
        {type == CREATETYPE_CREATE &&
          <Column
            title={<div style={{textAlign:'center'}}>对象新建数</div>}
            dataIndex={'lastResourceNo'}
            key={'lastResourceNo'}
            width={vipFlg == 1 ? 200 : '19%'}
            render={(lastResourceNo,item) => {
              if(vipFlg == 1 && (item.memberNo||0) > 0){
                return  (<div style={{textAlign:'center'}}>∞</div>);
              }
              return  (<div style={{textAlign:'center'}}>{item?.freeQuotaDesc||'-'}</div>);
            }}
            onCell={() => { return { rowSpan: 1 }}}
          />
        }
        {vipFlg == 1 ?
          (type == CREATETYPE_CREATE ?
            <Column 
              title={<div style={{textAlign:'center'}}>购买时长</div>}
              dataIndex={'durationMonth'}
              key={'durationMonth'}
              width={160}
              render={(durationMonth,item,index1) => {
                if(item.groupId == eProductGroupId.Pgid_1_OS){
                  return (<div style={{textAlign:'center'}}>-</div>);
                }
                return (
                  <div style={{display:'flex',alignItems:'center'}}>  {/*style={{display:'flex',alignItems:'center',justifyContent:'center' */} 
                    <Dropdown trigger={['click']} disabled={item?.statusType == eProductStatus.Status_3_Unreleased} overlay={
                      <Menu>
                        <Menu.Item>
                          <Radio.Group size="small" buttonStyle="solid" className="create-team-date-item" value={item.durationMonth}>
                            {(team701Result.data?.monthPromoList || []).map((item1,index)=> 
                              <Badge key={index} size="small" title="" className="pay-badge" count={item1.promoRateDesc || ""} offset={[-8,-5]}>
                                <Radio.Button onClick={()=>durationMonthChange(item,item1)} value={item1.monthCnt}>{item1.monthCntDesc}</Radio.Button> 
                              </Badge>
                            )}
                          </Radio.Group>
                        </Menu.Item>
                      </Menu>
                    }>
                      <a style={{color:'inherit', display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%'}}>
                        <div style={{display: 'flex', alignItems: 'center', flex: '0 0 auto', paddingRight: '16px'}}>
                          {newBuyFormat(item) != 2 &&
                            <span style={{color:'#999',fontStyle:'italic'}}>
                              {!!newBuyFormat(item) ? '' : `剩${leftDaysFormat(item.lastExpirationDt)}天`}
                            </span>
                          }
                          {newBuyFormat(item) == 2 &&
                            <span style={{color:'red',fontStyle:'italic'}}>已过期</span>
                          }
                        </div>
                        <div style={{display: 'flex', alignItems: 'center', flex: '0 0 auto', paddingLeft: '16px'}}>
                          {!!durationMonth ?
                            <Badge key={index1} size="small" title="" className="pay-badge" count={item.rebate || ""} offset={[-8,-5]}>
                              <div style={{height:24,width:60,display:'flex',alignItems:'center',fontSize:12,color:'#fff',backgroundColor:'#0077F2',borderRadius:5}}>
                                <span style={{width:40,display:'flex',justifyContent:'center'}}>{item.name}</span>
                                <span style={{width:20,display:'flex',justifyContent:'center'}}><DownOutlined/></span>
                              </div> 
                            </Badge>
                          :
                            <span style={{color: newBuyFormat(item) == 1 ? '#B3D6FB' : '#F59A23'}}>{newBuyFormat(item) == 1 ? '选时长' : '续时长'}</span>
                          }
                        </div>
                      </a>
                    </Dropdown>
                    {!!durationMonth && 
                      <Button 
                        style={{ borderRadius: '50%', transition: 'all 0s 0s'}} 
                        size="small" 
                        type="text" 
                        icon={<CloseOutlined className="fontsize-12" />} 
                        onClick={()=>durationMonthChange(item)}/>
                    }
                  </div>
                );
              }}
              onCell={() => { return { rowSpan: 1 }}}
            />
          :
            <ColumnGroup title={'规格选择'} className="top-header-b">
              <Column 
                title={'增/减员数'}
                className="top-header-b"
                dataIndex={'memberNo'}
                key={'memberNo'}
                width={160}
                render={(memberNo,item) => {
                  return (
                    <div style={{display:'flex',alignItems:'center',justifyContent:'center'}}>
                      <div className={`member-edit-primary`}>
                        <InputNumber 
                          controls={false}
                          style={{width:110}}
                          size="small"
                          precision={0}
                          min={!!newBuyFormat(item) ? 0 : -item.lastMemberNo}
                          max={1000}
                          disabled={item?.statusType == eProductStatus.Status_3_Unreleased}
                          addonBefore={
                            <Button 
                              type={'primary'} 
                              disabled={item?.statusType == eProductStatus.Status_3_Unreleased || (!!newBuyFormat(item) ? ((memberNo||0) == 0) : ((memberNo||0) == -item.lastMemberNo))}
                              style={{
                                borderTopLeftRadius:5,
                                borderBottomLeftRadius:5,
                                borderRight:'0px',
                                borderColor: (item?.statusType == eProductStatus.Status_3_Unreleased || (!!newBuyFormat(item) ? ((memberNo||0) == 0) : ((memberNo||0) == -item.lastMemberNo))) && '#d9d9d9'
                              }}
                              onClick={()=>memberNoChange(item,0)}
                            >
                              -
                            </Button>
                          }
                          addonAfter={
                            <Button 
                              type={'primary'} 
                              disabled={item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == {freeAuthUserQuota1000}}
                              style={{borderTopRightRadius:5,borderBottomRightRadius:5,borderLeft:'0px',
                                borderColor: (item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == {freeAuthUserQuota1000}) && '#d9d9d9'
                              }}
                              onClick={()=>memberNoChange(item,1)}
                            >
                              +
                            </Button>
                          }
                          value={memberNo}
                          onChange={(value)=>memberNoChangeW(item,value)}
                          onPressEnter={()=>memberNoChange(item,2)}
                          onBlur={()=>memberNoChange(item,2)}
                        />
                      </div>
                    </div>
                  );
                }}
                onCell={() => { return { rowSpan: 1 }}}
              />
              <Column
                title={'购买时长'}
                className="top-header-b"
                dataIndex={'durationMonth'}
                key={'durationMonth'}
                width={160}
                render={(durationMonth,item,index1) => {
                  return (
                    <div style={{display:'flex',alignItems:'center',justifyContent:'space-between'}}>

                      <div style={{display:'flex',alignItems:'center'}}>
                        <Dropdown trigger={['click']} disabled={item?.statusType == eProductStatus.Status_3_Unreleased} overlay={
                          <Menu>
                            <Menu.Item>
                              <Radio.Group size="small" buttonStyle="solid" className="create-team-date-item" value={item.durationMonth}>
                                {(team701Result.data?.monthPromoList || []).map((item1,index)=> 
                                  <Badge key={index} size="small" title="" className="pay-badge" count={item1.promoRateDesc || ""} offset={[-8,-5]}>
                                    <Radio.Button onClick={()=>durationMonthChange(item,item1)} value={item1.monthCnt}>{item1.monthCntDesc}</Radio.Button> 
                                  </Badge>
                                )}
                              </Radio.Group>
                            </Menu.Item>
                          </Menu>
                        }>
                          <a style={{color:'inherit', display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%'}}>
                            <div style={{display: 'flex', alignItems: 'center', flex: '0 0 auto', paddingRight: '16px'}}>
                              {newBuyFormat(item) != 2 &&
                                <span style={{color:'#999',fontStyle:'italic'}}>
                                  {!!newBuyFormat(item) ? '' : `剩${leftDaysFormat(item.lastExpirationDt)}天`}
                                </span>
                              }
                              {newBuyFormat(item) == 2 &&
                                <span style={{color:'red',fontStyle:'italic'}}>已过期</span>
                              }
                            </div>
                            <div style={{display: 'flex', alignItems: 'center', flex: '0 0 auto', paddingLeft: '16px'}}>
                              {!!durationMonth ?
                                <Badge key={index1} size="small" title="" className="pay-badge" count={item.rebate || ""} offset={[-8,-5]}>
                                  <div style={{height:24,width:60,display:'flex',alignItems:'center',fontSize:12,color:'#fff',backgroundColor:'#0077F2',borderRadius:5}}>
                                    <span style={{width:40,display:'flex',justifyContent:'center'}}>{item.name}</span>
                                    <span style={{width:20,display:'flex',justifyContent:'center'}}><DownOutlined/></span>
                                  </div> 
                                </Badge>
                              :
                                <span style={{color: newBuyFormat(item) == 1 ? '#B3D6FB' : '#F59A23'}}>{newBuyFormat(item) == 1 ? '选时长' : '续时长'}</span>
                              }
                            </div>
                          </a>
                        </Dropdown>
                        {!!durationMonth && 
                          <Button 
                            style={{ borderRadius: '50%', transition: 'all 0s 0s'}} 
                            size="small" 
                            type="text" 
                            icon={<CloseOutlined className="fontsize-12" />} 
                            onClick={()=>durationMonthChange(item)}/>
                        }
                      </div>
                    </div>
                  );
                }}
                onCell={() => { return { rowSpan: 1 }}}
              />
            </ColumnGroup>
          )
        :
          null
        }
        {type == CREATETYPE_CREATE ?
          (vipFlg == 1 ?
            <Column
              title={<div style={{textAlign:'center'}}>生效开始</div>}
              dataIndex={'effectBeginDt'}
              key={'effectBeginDt'}
              width={120}
              render={(effectBeginDt, item) => {
                if(item.groupId == eProductGroupId.Pgid_1_OS){
                  return (<div style={{textAlign:'center'}}>-</div>)
                }
                return (<div style={{textAlign:'center'}}>{!!effectBeginDt ? moment(effectBeginDt).format('YYYY-MM-DD') : ''}</div>)
              }}
              onCell={() => { return { rowSpan: 1 }}}
            />
          :
            <Column
              title={<div style={{textAlign:'center'}}>有效期至</div>}
              dataIndex={'expirationDt'}
              key={'expirationDt'}
              width={'19%'}
              render={()=> <div style={{textAlign:'center'}}>∞</div>}
              onCell={() => { return { rowSpan: 1 }}}
            />
          )
        :
          <ColumnGroup title={'预览'} className="top-header-c">
            <Column
              title={'授权数'}
              className="top-header-c"
              dataIndex={'nextMemberNo'}
              key={'nextMemberNo'}
              width={90}
              render={(nextMemberNo,item) => {
                if(!!newBuyFormat(item) && !item.durationMonth && item.memberNo <= 0){
                  return (<div>{item.memberNo}人</div>);
                }
                return (<div>{nextMemberNo||0}人</div>);
              }}
              onCell={() => { return { rowSpan: 1 }}}
            />
            <Column
              title={'对象新建数'}
              className="top-header-c"
              dataIndex={'nextResourceNo'}
              key={'nextResourceNo'}
              width={150}
              render={(nextResourceNo,item) => {
                if(!newBuyFormat(item)){
                  if(item.memberNo < 0 && ((item.memberNo + item.lastMemberNo) == 0)){
                    return  (<div>{item?.freeQuotaDesc||'-'}</div>);
                  }
                  return  (<div>∞</div>);
                }
                if((item.memberNo||0) > 0 && !!item.durationMonth){
                  return  (<div>∞</div>);
                }
                return  (<div>{item?.freeQuotaDesc||'-'}</div>);
              }}
              onCell={() => { return { rowSpan: 1 }}}
            />
            <Column
              title={'有效期至'}
              className="top-header-c"
              dataIndex={'expirationDt'}
              key={'expirationDt'}
              width={90}
              render={(expirationDt,item) => {
                if(!newBuyFormat(item)){
                  if(!!expirationDt){
                    return (<div>{moment(expirationDt).format('YYYY-MM-DD')}</div>);
                  }
                  return (<div>{moment(item.lastExpirationDt).format('YYYY-MM-DD')}</div>);
                }
                if((item.memberNo||0) > 0 && !!item.durationMonth){
                  if(!!expirationDt){
                    return (<div>{moment(expirationDt).format('YYYY-MM-DD')}</div>);
                  }
                  return (<></>);
                }
                return (<div>∞</div>);
              }}
              onCell={() => { return { rowSpan: 1 }}}
            />
          </ColumnGroup>
        }
        {vipFlg == 1 && type == CREATETYPE_CREATE ?
          <Column
            title={<div style={{textAlign:'center'}}>生效结束</div>}
            dataIndex={'expirationDt'}
            key={'expirationDt'}
            width={120}
            render={(expirationDt)=> {
              if(!!expirationDt){
                return (
                  <div style={{textAlign:'center'}}>{!!expirationDt ? moment(expirationDt).format('YYYY-MM-DD') : ''}</div>
                )
              }else{
                return (<div style={{textAlign:'center'}}>∞</div>)
              }
            }}
            onCell={() => { return { rowSpan: 1 }}}
          />
        :
          null
        }
        {vipFlg == 1 &&
          <Column
            title={<div style={{textAlign:'center'}}>单价</div>}
            dataIndex={'discountPrice'}
            key={'discountPrice'}
            width={120}
            render={(discountPrice, item) => {
              if(item.groupId == eProductGroupId.Pgid_1_OS){
                return (<div style={{textAlign:'center'}}>0</div>)
              }
              return (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{color: '#999',width: 40, textDecoration: 'line-through'}}>¥{item?.listingUnitAmt||'0'}</span>
                  <span>¥{item?.regularUnitAmt||'0'}/人/月</span>
                </div>
              );
            }}
            onCell={() => { return { rowSpan: 1 }}}
          />
        }
        {vipFlg == 1 &&
          <Column
            title={'小计'}
            dataIndex={'subtotal'}
            key={'subtotal'}
            render={(subtotal, item) => {
              if(item.groupId == eProductGroupId.Pgid_1_OS){
                return (<div></div>)
              }
              return (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{color: '#999',marginRight: 10, textDecoration: 'line-through',flex:1}}>{!!item.originalPrice ? '¥' : ''}{item.originalPrice}</span>
                  <span style={{flex:1}}>{!!item.discountPrice ? '¥' : ''}{item.discountPrice}</span>
                </div>
              );
            }}
            onCell={() => { return { rowSpan: 1 }}}
          />
        }
        {vipFlg == 1 ? null : type == CREATETYPE_CREATE ?
          <Column
            title={<div style={{textAlign:'center'}}>勾选使用</div>}
            dataIndex={'productId'}
            key={'productId'}
            width={'5%'}
            render={(productId,item) => {
              return (
                <div style={{textAlign:'center'}}>
                  <Checkbox
                    disabled={productId == eProductId.Pid_11_Explorer || productId == eProductId.Pid_12_Space || item?.statusType == eProductStatus.Status_3_Unreleased}
                    checked={item.checked}
                    onChange={(e)=>checkBoxChange(e,productId)}
                  />
                </div>
              )
            }}
            onCell={() => { return { rowSpan: 1 }}}
          />
        : 
          null
        }
      </Table>
    </Form.Item>
  </Form>
  <div style={{height:220}}>
    {vipFlg == 1 ?
      <div className="price-bottom">
        <div className="price-bottom-left">
          <div style={{display:'flex',alignItems:'center'}}>
            <span style={{marginRight:50}}>
              选择优惠券({myCouponList.length})<Tooltip title="高亮显示的优惠券可用，灰显的优惠券不可用" placement="right"><QuestionCircleOutlined style={{ color: '#f59a23',marginLeft:5 }}/></Tooltip>
            </span>
            <Button
              className={refreshingFlg && 'refresh-icon'}
              style={{ position: 'relative', color: '#666' }}
              type="link"
              icon={<span className="refresh-position fontsize-14 iconfont shuaxin1" />}
              onClick={() => {
                setRefreshingFlg(true);
                setTimeout(() => {
                  if((priceInfo.payPrice||0) > 0){
                    load_team_703_calc_price(couponSelect?.couponCode);
                  }else{
                    refetchGetUserValidCoupon()
                  }
                  setRefreshingFlg(false);
                }, 500);
              }}
            />
            <span style={{marginRight:50}}>刷新</span>
            <Form colon={false} form={discountForm}>
              <Form.Item 
              style={{ margin: 0 }}
              label={<span style={{ color: '#0077f2', cursor: 'pointer' }}
              onClick={openDiscountCode}>我有优惠码</span>}
              // validateStatus={priceInfo.resultCode === 200 ? "success" : (priceInfo.resultCode === 500 ? "error" : null)}
              >
                {discountCodeVisible &&
                <Form.Item name="code" noStyle>
                  <Search
                    className="discount-code discount-code-visible"
                    size="small"
                    maxLength={12}
                    allowClear
                    autoComplete="off"
                    placeholder="请填写优惠码"
                    enterButton="校验"
                    onChange={e => { discountCodeRef.current.code =  e.target.value }}
                    onSearch={verificationDiscountCode}
                  />
                </Form.Item>
                }
                {discountCodeVisible &&
                <Form.Item noStyle>
                  <Button 
                    style={{ borderRadius: '50%', transition: 'all 0s 0s'}} 
                    size="small" 
                    type="text" 
                    icon={<CloseOutlined className="fontsize-12" />} 
                    onClick={closeDiscountCode}/>
                </Form.Item>
                }
                {/* <span 
                  className="fontsize-12" 
                  style={{ color: priceInfo.resultCode == 500 ? 'red' : '',marginLeft:10}}
                >
                  {priceInfo.resultCode === 500 ? <CloseCircleOutlined /> : ''}
                  {priceInfo.resultCode === 500 ? priceInfo.resultMessage : null}
                </span> */}
              </Form.Item>
            </Form>
          </div>
          <div className="flex-column-parent" style={{height: 110, width:'90%', border: '1px solid #f0f0f0', borderRadius: 5}}>
            <div style={{height:'100%',padding: '5px 0px 5px 5px'}} className="flex-column-child section">
              <Row>
                {myCouponList.map(coupon => {
                  return (
                    <div style={{display:'flex',alignItems:'flex-start',position:'relative',height:105}}>
                      <Button type='text' title={!couponCanUse(coupon) ? `优惠码：${coupon.couponCode}` : `优惠码：${coupon.couponCode}\n不可用原因：${couponCanUse(coupon)}`}
                              onClick={() => couponSelectChange(coupon)}
                              style={{padding:0}}
                              disabled={!!couponCanUse(coupon)}>
                        <Card 
                          style={{backgroundColor: !couponCanUse(coupon) ? '#E6F2FE' : '#f0f0f0'}} 
                          className={(couponSelect?.couponCode == coupon.couponCode ? "CouponCard-select" : '') + " CouponCard"} 
                          hoverable={false}
                        >
                          <div
                            style={{
                              width:'100%', 
                              height: 70, 
                              backgroundColor: !couponCanUse(coupon) ? '#0077F2' : '#999',
                              borderTopLeftRadius:5,
                              borderTopRightRadius:5
                            }}
                          >
                            <div style={{display:'flex',alignItems:'center',height:'100%',marginLeft:10}}>
                              <div style={{color:'#fff'}}>
                                <div style={{display:'flex',alignItems:'center'}}>
                                  <div style={{fontSize:18,marginRight:20}}>
                                    {coupon.couponType == eCouponType.Type_2_AmountOff ? ('减￥'+(coupon.couponDiscount||'')) : ((coupon.couponDiscount||'')+'折')}
                                  </div>
                                  <div>满  ￥{coupon.minOrderAmt}</div>
                                </div>
                                <div>有效期至{moment(coupon.expirationDt).format('YYYY-MM-DD')}</div>
                              </div>
                            </div>
                          </div>
                          <div style={{width:'100%',height: 30}}>
                            <div style={{display:'flex',alignItems:'center',height: 30, marginLeft:10,color: !couponCanUse(coupon) ? '#0077F2' : '#999'}}>
                              <span>{coupon.couponName}</span>
                            </div>
                          </div>
                        </Card>
                      </Button>
                      <Popconfirm title='确定删除该优惠券?' onConfirm={()=>deleteCoupon(coupon)}>
                        <Button 
                          style={{ borderRadius: '50%', transition: 'all 0s 0s',width:14,height:14,position:'absolute',right:0}} 
                          size="small" 
                          icon={<CloseOutlined style={{fontSize:10}} />}
                        />
                      </Popconfirm>
                    </div>
                  );
                })}
              </Row>
            </div>
          </div>
        </div>
        <div className="price-bottom-right">
          <div className="price-bottom-detailed">
            <div className="price-bottom-detailed-descriptions">
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label">原价:</span>
                <span className="li-value"><span style={{textDecoration: 'line-through',color:'#999',marginRight:10}}>
                  ￥{priceInfo.originalPrice||0}</span>{(priceInfo.promoAmt||0) > 0 ? `(立减-￥${priceInfo.promoAmt})` : ''}
                </span>
              </div>
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label">现价:</span>
                <span className="li-value">￥{priceInfo.regularAmt||0}</span>
              </div>
              {(priceInfo.discountReduction||0) > 0 ?
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label">折扣减:</span>
                <span className="li-value">-￥{priceInfo.discountReduction}</span>
              </div>
              :
              <div style={{display:'none'}}></div>
              }
              {!!couponSelect ?
                <div className="price-bottom-detailed-descriptions-li">
                  <span className="li-label" style={{color: priceInfo.couponReduction == 0 ? '#999' : '#333'}}>优惠券:</span>
                  <span className="li-value" style={{color: priceInfo.couponReduction == 0 ? '#999' : '#333'}}>
                    -￥{priceInfo.couponReduction}
                    <span className="coupon" style={{backgroundColor: priceInfo.couponReduction == 0 ? '#999' : '#0077F2'}}>
                      满￥{myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.minOrderAmt}
                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponType == eCouponType.Type_2_AmountOff ? '减￥' : '享'}
                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponDiscount}
                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponType == eCouponType.Type_2_AmountOff ? '' : '折'}
                      <a 
                        className="delete" 
                        style={{color: priceInfo.couponReduction == 0 ? '#999' : '#0077F2'}} 
                        onClick={()=>{setCouponSelect(null);load_team_703_calc_price();}}
                      >
                        <CloseOutlined style={{fontSize:10}} />
                      </a>
                    </span>
                  </span>
                </div>
              :
                <div style={{display:'none'}}></div>
              }
              
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label">合计:</span>
                <span className="li-value">
                  <div style={{fontSize:12,display:'flex',alignItems:'baseline',justifyContent:'end'}}>
                    <span style={{color:'#f59a23'}}>￥</span>
                    <span className="shifu-rate" style={{color:'#f59a23'}}>{priceInfo.payPrice||0}</span>
                  </div>
                </span>
              </div>
              {/* <a style={{fontSize:12,display:'flex',color:'#0077F2'}} onClick={()=>setShowPriceDetail(true)}>价格详情</a> */}
            </div>  
          </div>
        </div>
      </div>
      :
      <div style={{height: 40}}/>
    }
    <div style={{display:'flex',alignItems:"flex-end",justifyContent:'space-between',paddingLeft:14}}>
      <div>
        {vipFlg == 0 &&
          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999' }}>备注1：{userCntHelp}</div>
        }
        {vipFlg == 1 &&
          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999' }}>备注1：vip人数为0的应用，您将继续享有基础版的功能免费长期使用。</div>
        }
        {type == CREATETYPE_CREATE ?
          <div 
            style={{ 
              marginLeft: 10, 
              height: 20, 
              fontSize: 12, 
              color: '#999'
            }}
          >
            备注2：您不需要的功能，在团队创建成功后，可在 设置 {'->'} 应用管理 页面中禁用(或再次开启)
          </div>
        :
          null
        }
        {type == CREATETYPE_CREATE &&
          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999' }}>
            <span style={{color:'#fff'}}>备注2：</span>点击 帮助中心 {'->'} 应用管理 链接，可定位到 官网帮助页的 关于应用管理的帮助文档。
          </div>
        }
        {type == CREATETYPE_UPGRADE && 
        <div 
          style={{ 
            marginLeft: 10, 
            height: 20, 
            fontSize: 12, 
            color: '#999'
          }}
        >
          备注2：可在 设置 {'->'} 应用管理 中进行应用启用/禁用。
        </div>
        }
      </div>
      <div style={{paddingRight:24}}>
        <div style={{textAlign: 'right' }}>
          <Button
            className={vipFlg == 1 ? "purchase-btn":"found-btn"}
            type="primary"
            loading={spining}
            style={{ minWidth: 140, height: 34, borderRadius: 5 }}
            onClick={_onOk}
            >
              {vipFlg == 1 ? "立即购买" : `创建(已选${selectedProductList.filter(product => product.checked && product.productId != eProductId.Pid_11_Explorer && product.productId != eProductId.Pid_12_Space).length}个应用)`}
          </Button>
        </div>
        {vipFlg == 1 &&
          <div style={{textAlign: 'right',fontSize:12,color:'#999',marginTop:5}}>
            支付成功后，可前往 <a style={{color:'#0077F2'}} onClick={()=>navigateTo(1)}>个人中心</a> {'->'} <a style={{color:'#0077F2'}} onClick={()=>navigateTo(2)}>发票管理</a> 进行开票
          </div>
        }
      </div>  
    </div>
  </div>
  <PayCreateTeamModal 
    teamId={teamId}
    visible={isPaying} 
    onCancel={cancelPay} 
    priceInfo={priceInfo} 
    onOk={sucessPay}
    payCode={payCode}
    url={url}
    url1={url1}/>
  <PriceDetailModal 
    visible={showPriceDetail}
    onCancel={()=>setShowPriceDetail(false)} />
  <DraggablePopUp
    title='提示'
    className="tms-modal"
    width={420}
    open={tipShow}
    onCancel={()=>setTipShow(false)}
    centered
    maskClosable={false}
    footer={
      <div style={{display:'flex',justifyContent:'center'}}>
        <Button type={'primary'} style={{borderRadius:5}} onClick={()=>setTipShow(false)}>我知道了</Button>
      </div>
    }
  >
    <div style={{display:'flex',justifyContent:'center',marginTop:20}}>因减员等因素，订单金额＜0，您可继续增购应用</div>
    <div style={{display:'flex',justifyContent:'center',marginBottom:20}}>或延续时长，确保订单金额≥0。</div>
  </DraggablePopUp>
  </>)
}

function PayCreateTeam({onOk, teamId, priceInfo, payCode, url, url1}){
  const [form] = Form.useForm()
  const payMethod = Form.useWatch('payMethod', form);
  //const [refreshingFlg,setRefreshingFlg] = useState(false);

  useEffect(()=>{
    const timer = !(payCode && payCode?.orderId) || setInterval(()=>{
      let params = {orderId: payCode.orderId}
      if(!!teamId){
        params.teamId = teamId
      }
      team_705_get_order_status(params).then(result => {
        if(result.resultCode == 200){
          if(result.statusType == eOrderStatus.Status_1_Paid){
            clearInterval(timer);
            onOk && onOk(result.teamId);
            globalUtil.success('购买成功');
          }
        }
      })
    },1000)

    return () => {
      clearInterval(timer)
    }
  },[payCode])

  useEffect(()=>{
    if(payMethod){}
  },[payMethod])

  return (
  <Form
    className="PayCreateTeam-form"
    form={form}
    name="basic"
    // {...formItemLayout}
    autoComplete="off">

    <Form.Item 
    noStyle
    >
      <div className="pay-images">
        <div className="image">
          <div className="image-header">
            <img width={24} height={24} src={require("@assets/images/createTeam/wxpay.png")}/>微信
          </div>
          <div className="image-content" style={{backgroundColor:'#04C161'}}>
            {url
             ? <Image 
                src={url}
                width={120} 
                height={120}
                preview={false} 
                placeholde={<TLoading />}
               />
              : <div className="invalid_QR_code">二维码无效</div>
            }
          </div>
        </div>
        <div className="image">
          <div className="image-header">
            <img width={24} height={24} src={require("@assets/images/createTeam/alipay.png")}/>支付宝
          </div>
          <div className="image-content" style={{backgroundColor:'#1273FF'}}>
            {url1
             ? <Image 
                src={url1} 
                width={120} 
                height={120} 
                preview={false} 
                placeholde={<TLoading />}
               />
              : <div className="invalid_QR_code">二维码无效</div>
            }
          </div>
        </div>
      </div>
      <div className="pay-tips">
        <span className="create-team-price-color" style={{marginLeft:10,marginRight:10}}>￥</span>
        <span className="ant-form-text create-team-discountPrice create-team-price-color">{priceInfo.payPrice}</span>
      </div>
    </Form.Item>

  </Form>) 
}

function PayCreateTeamModal({teamId, visible=false, onCancel, priceInfo, onOk, payCode, url, url1}) {
  return <DraggablePopUp
    className="createTeamDraModal"
    title={
      <span style={{ display: 'flex', alignItems: 'center' }}>
        扫码支付
        <span className="fontsize-12" style={{ marginLeft: 10, color: '#999' }}>
          流水编号：{payCode?.orderId||''}
        </span>
      </span>
    }
    width={520}
    open={visible}
    onCancel={onCancel}
    centered
    footer={null}
    maskClosable={false}
    destroyOnClose>
      <PayCreateTeam 
      priceInfo={priceInfo} 
      teamId={teamId}
      onOk={onOk} 
      payCode={payCode} 
      url={url}
      url1={url1}/>
  </DraggablePopUp>
}

function PriceDetailModal({visible=false,onCancel}) {
  return <DraggablePopUp
    className="createTeamDraModal"
    title="价格详情"
    width={800}
    open={visible}
    onCancel={onCancel}
    centered
    footer={null}
    maskClosable={false}
    destroyOnClose>
      <PriceDetailTable/>
  </DraggablePopUp>
}

function PriceDetailTable({}){
  const [dataSource,setDataSource] = useState([]);
  const columns = [
    {title: '应用',dataIndex: 'productName', key: 'productName'},
    {title: '原价',dataIndex: 'oldPrice', key: 'oldPrice'},
    {title: '现价',dataIndex: 'nowPrice', key: 'nowPrice'},
    {title: '购买日期',dataIndex: 'buyDate', key: 'buyDate'},
    {title: '有效期至',dataIndex: 'expiration', key: 'expiration'},
    {title: '剩余时长(天)',dataIndex: 'remainingDuration', key: 'remainingDuration'},
    {title: '原价：扩容单人',dataIndex: 'oldPersonalPrice', key: 'oldPersonalPrice'},
    {title: '现价：扩容单人',dataIndex: 'nowPersonalPrice', key: 'nowPersonalPrice'},
    {title: '折扣减',dataIndex: 'rate', key: 'rate'},
  ]

  return (
    <Table 
      size="small"
      columns={columns}
      dataSource={dataSource}
      pagination={{
        position: ['bottomCenter'],
        size: 'small',
        showSizeChanger: true,
        showQuickJumper: true,
        total: dataSource.length,
        showTotal: (total)=>{return `共${total}条`}
      }}/>
  ) 
}

export default function CreateTeamModal({visible=false,onCancel,teamId,onOk,type=CREATETYPE_CREATE,priceData, productList}) {
  const [isChange,setIsChange] = useState(false);
  // 新增：动态设置浏览器标题
  const prevTitle = useRef(document.title);
  useEffect(() => {
    if (visible) {
      prevTitle.current = document.title;
      document.title = type === CREATETYPE_UPGRADE ? "应用管理-购买" : "新建团队";
    } else {
      document.title = prevTitle.current;
    }
    return () => {
      document.title = prevTitle.current;
    };
  }, [visible, type]);
  function checkClose(){
    if(isChange){
      Modal.confirm({
        title: '提示',
        icon: <ExclamationCircleOutlined style={{ color: 'orange' }}/>,
        centered: true,
        content: <div style={{ display: 'flex', flexDirection: 'column' }}>
          <span>正在{`${type == CREATETYPE_CREATE ? '创建团队' : '购买应用'}`}，是否放弃{`${type == CREATETYPE_CREATE ? '创建' : '购买'}`} ？</span>
        </div>,
        okText: '是',
        cancelText: '否',
        onOk:() =>{
          onCancel();
          setIsChange(false);
        }
      });
    }else{
      onCancel();
    }
  }
  return <DraggableDrawer
            className="tms-drawer createTeamDrawer"
            centered
            title={type === CREATETYPE_UPGRADE?"应用管理-购买":"新建团队"}
            width={'60%'}
            minWidth={'50%'}
            maxWidth={'95%'}
            open={visible}
            onClose={checkClose}
            footer={null}
            destroyOnClose>
              <CreateTeam teamId={teamId}
                          onCancel={onCancel}
                          onOk={onOk}
                          type={type}
                          priceData={priceData}
                          productList={productList}
                          setIsChange={setIsChange}/>
        </DraggableDrawer>
}

/*unused
//应用续费、应用扩容、应用新购
export function BuyTeamModal({visible=false,onCancel,teamId,onOk,type=CREATETYPE_EXPAND,defaultPackageList=[]}) {
  const [createType,setCreateType] = useState(CREATETYPE_EXPAND)
  const [loading,setLoading] = useState(true)
  const [_defaultPackageList,setDefaultPackageList] = useState([])
  const [hideBuy,setHideBuy] = useState(false);

  useEffect(()=>{
    if(visible){
      setLoading(true)
      getProductsList();
      setDefaultPackageList(defaultPackageList)
      setCreateType(type)
    }
  },[visible])

  useEffect(()=>{
    if(visible){
      setLoading(true)
      if(type === createType){
        console.log("defaultPackageList",defaultPackageList)
        setDefaultPackageList(defaultPackageList)
      }else{
        setDefaultPackageList([])
      }
      setTimeout(()=>{
        setLoading(false)
      },300)
    }
  },[createType])

  async function getProductsList() {
    await team_711_get_team_product_list({teamId}).then(res => {
        if(res.resultCode == 200){
            let allProductsList = (res.productList||[])
            setHideBuy(allProductsList.filter(product => !product.expirationDt).length == 0)
        }
    });
    setTimeout(()=>{
      setLoading(false)
    },300)
}

  return <DraggablePopUp
    className="createTeamDraModal"
    title={
      <Radio.Group className="createTeamTypeRadio" value={createType} onChange={(e)=>{setCreateType(Number(e.target.value))}}>
        <Radio.Button value={CREATETYPE_EXPAND}>成员扩容</Radio.Button>
        <Radio.Button value={CREATETYPE_RENEWAL}>应用续费</Radio.Button>
        {!hideBuy && <Radio.Button value={CREATETYPE_BUY}>应用新购</Radio.Button>}
      </Radio.Group>
    }
    width={1000}
    open={visible}
    onCancel={onCancel}
    maskClosable={false}
    footer={null}
    centered
    destroyOnClose>
      {loading?
        <TLoading/>
        :
        <CreateTeam teamId={teamId}
                    onCancel={onCancel}
                    onOk={onOk}
                    type={createType}
                    defaultPackageList={_defaultPackageList}/>}
    </DraggablePopUp>
}
*/
