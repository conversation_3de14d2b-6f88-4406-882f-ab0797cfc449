{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Position } from './position.js';\nimport { Range } from './range.js';\n/**\n * A selection in the editor.\n * The selection is a range that has an orientation.\n */\nexport class Selection extends Range {\n  constructor(selectionStartLineNumber, selectionStartColumn, positionLineNumber, positionColumn) {\n    super(selectionStartLineNumber, selectionStartColumn, positionLineNumber, positionColumn);\n    this.selectionStartLineNumber = selectionStartLineNumber;\n    this.selectionStartColumn = selectionStartColumn;\n    this.positionLineNumber = positionLineNumber;\n    this.positionColumn = positionColumn;\n  }\n  /**\n   * Transform to a human-readable representation.\n   */\n  toString() {\n    return '[' + this.selectionStartLineNumber + ',' + this.selectionStartColumn + ' -> ' + this.positionLineNumber + ',' + this.positionColumn + ']';\n  }\n  /**\n   * Test if equals other selection.\n   */\n  equalsSelection(other) {\n    return Selection.selectionsEqual(this, other);\n  }\n  /**\n   * Test if the two selections are equal.\n   */\n  static selectionsEqual(a, b) {\n    return a.selectionStartLineNumber === b.selectionStartLineNumber && a.selectionStartColumn === b.selectionStartColumn && a.positionLineNumber === b.positionLineNumber && a.positionColumn === b.positionColumn;\n  }\n  /**\n   * Get directions (LTR or RTL).\n   */\n  getDirection() {\n    if (this.selectionStartLineNumber === this.startLineNumber && this.selectionStartColumn === this.startColumn) {\n      return 0 /* SelectionDirection.LTR */;\n    }\n    return 1 /* SelectionDirection.RTL */;\n  }\n  /**\n   * Create a new selection with a different `positionLineNumber` and `positionColumn`.\n   */\n  setEndPosition(endLineNumber, endColumn) {\n    if (this.getDirection() === 0 /* SelectionDirection.LTR */) {\n      return new Selection(this.startLineNumber, this.startColumn, endLineNumber, endColumn);\n    }\n    return new Selection(endLineNumber, endColumn, this.startLineNumber, this.startColumn);\n  }\n  /**\n   * Get the position at `positionLineNumber` and `positionColumn`.\n   */\n  getPosition() {\n    return new Position(this.positionLineNumber, this.positionColumn);\n  }\n  /**\n   * Get the position at the start of the selection.\n  */\n  getSelectionStart() {\n    return new Position(this.selectionStartLineNumber, this.selectionStartColumn);\n  }\n  /**\n   * Create a new selection with a different `selectionStartLineNumber` and `selectionStartColumn`.\n   */\n  setStartPosition(startLineNumber, startColumn) {\n    if (this.getDirection() === 0 /* SelectionDirection.LTR */) {\n      return new Selection(startLineNumber, startColumn, this.endLineNumber, this.endColumn);\n    }\n    return new Selection(this.endLineNumber, this.endColumn, startLineNumber, startColumn);\n  }\n  // ----\n  /**\n   * Create a `Selection` from one or two positions\n   */\n  static fromPositions(start) {\n    let end = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : start;\n    return new Selection(start.lineNumber, start.column, end.lineNumber, end.column);\n  }\n  /**\n   * Creates a `Selection` from a range, given a direction.\n   */\n  static fromRange(range, direction) {\n    if (direction === 0 /* SelectionDirection.LTR */) {\n      return new Selection(range.startLineNumber, range.startColumn, range.endLineNumber, range.endColumn);\n    } else {\n      return new Selection(range.endLineNumber, range.endColumn, range.startLineNumber, range.startColumn);\n    }\n  }\n  /**\n   * Create a `Selection` from an `ISelection`.\n   */\n  static liftSelection(sel) {\n    return new Selection(sel.selectionStartLineNumber, sel.selectionStartColumn, sel.positionLineNumber, sel.positionColumn);\n  }\n  /**\n   * `a` equals `b`.\n   */\n  static selectionsArrEqual(a, b) {\n    if (a && !b || !a && b) {\n      return false;\n    }\n    if (!a && !b) {\n      return true;\n    }\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0, len = a.length; i < len; i++) {\n      if (!this.selectionsEqual(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Test if `obj` is an `ISelection`.\n   */\n  static isISelection(obj) {\n    return obj && typeof obj.selectionStartLineNumber === 'number' && typeof obj.selectionStartColumn === 'number' && typeof obj.positionLineNumber === 'number' && typeof obj.positionColumn === 'number';\n  }\n  /**\n   * Create with a direction.\n   */\n  static createWithDirection(startLineNumber, startColumn, endLineNumber, endColumn, direction) {\n    if (direction === 0 /* SelectionDirection.LTR */) {\n      return new Selection(startLineNumber, startColumn, endLineNumber, endColumn);\n    }\n    return new Selection(endLineNumber, endColumn, startLineNumber, startColumn);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}