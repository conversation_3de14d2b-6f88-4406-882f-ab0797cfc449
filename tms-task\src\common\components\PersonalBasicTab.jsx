import React, { useEffect, useState, useRef } from "react";
import { Avatar,Button,Input,Form,Upload } from "antd";
import DraggablePopUp from "@components/DraggablePopUp";
import { CloseCircleFilled, EditOutlined, CameraOutlined } from "@ant-design/icons";
import { useParams } from "react-router-dom";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import { NoAvatarIcon } from '@components/IconUtil';
import { QuestionsUploadIcon } from "@components/IconUtil";
import ImgCrop from "antd-img-crop";
import { get_team_mbr_user_info } from "@/settings/store/actionCreators"
import {
  setting_225_update_user_info,
} from '@common/api/http';
import "./PersonalBasicTab.scss";
import { globalUtil } from "@common/utils/globalUtil";
import { getRootUrl } from "@common/router/RouterRegister";
import { setting202 } from "@common/utils/ApiPath";
import { useQueryClient } from "@tanstack/react-query";

const { Dragger } = Upload;


// 基本信息
export default function PersonalBasicTab({flag}) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const { teamId } = useParams();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [customFormData, setCustomFormData] = useState([]);
  const state = useSelector((state) => ({
    teamMbrUserInfo: state.getIn(["workSetUp", "teamMbrUserInfo"])
  }), shallowEqual);

  useEffect(() => {
    if(!!state.teamMbrUserInfo.mbrUserInfo) {
      let customFormData_ = [
        {
          key: 'userName',
          label: '成员名',
          name: 'userName',
          value: state.teamMbrUserInfo?.mbrUserInfo?.userName,
          tipsFlag: 1,
          tips: '我在团队中的昵称'
        },
        {
          key: 'mobileNo',
          label: '手机号',
          name: 'mobileNo',
          value: state.teamMbrUserInfo?.mbrUserInfo?.mobileNo,
          tipsFlag: 1,
          tips: '此号码仅在本团队可见'
        },
        {
          key: 'email',
          label: '邮箱',
          name: 'email',
          value: state.teamMbrUserInfo?.mbrUserInfo?.email,
          tipsFlag: 1,
          tips: '接收团队邮件通知'
        },
      ]
      setCustomFormData([...customFormData_])
    }
  },[state.teamMbrUserInfo.mbrUserInfo])

  // 修改图片
  const modifyAvater = () => {
    setIsModalVisible(true);
  }

  // 上传头像
  const avatarUpload = (link) => {
    let formData = form.getFieldsValue(true)
    let params = {
      teamId: teamId,
      avatar: link,
      userName: formData.userName.formItemValue,
      userId: state.teamMbrUserInfo.mbrUserInfo.userId,
      email: formData.email.formItemValue,
      mobileNo: formData.mobileNo.formItemValue,
      emailSubscribeMyChanges: state.teamMbrUserInfo.subscribeInfo.emailSubscribeMyChanges,
      watchMyChangesFlg: state.teamMbrUserInfo.subscribeInfo.watchMyChangesFlg
    };
    setting_225_update_user_info(params).then((res) => {
      if (res.resultCode === 200) {
        dispatch(get_team_mbr_user_info(teamId))
      }
    });
  }

  const updateUserInfo = (label) => {
    let formData = form.getFieldsValue(true)
    let params = {
      teamId: teamId,
      userName: formData.userName.formItemValue,
      userId: state.teamMbrUserInfo.mbrUserInfo.userId,
      email: formData.email.formItemValue,
      mobileNo: formData.mobileNo.formItemValue,
    };
    setting_225_update_user_info(params).then((res) => {
      if (res.resultCode === 200) {
        dispatch(get_team_mbr_user_info(teamId));
        queryClient.invalidateQueries(['get_team_allusergrp']);
        globalUtil.success(`${label}更新成功`);
        //tmsbug-4221:新建团队，新建文档，期望“创建者”信息显示成员名，而不是用户名(手机号码)  原因：用户名修改后需要刷新用户数据 
        globalUtil.getQueryClient().invalidateQueries( [setting202, teamId]);
      }
    });
  }

  const navigateTo = () => {
    let url = getRootUrl() + '/' + teamId + '/settings/personal';
    window.open(url);
  }

  // 删除头像
  const deleteLogo = () => {
    avatarUpload('') //20230312 null -> ''
  }
  return (
    <div className="Basic">
        {flag == 1 &&
          <span className="Basic-tips">
              <span className="iconfont tishi" style={{color:'#F59A23',marginRight: 4, fontSize: 18}}/>
              为让其他团队成员很容易辨识您，请修改如下成员信息。
          </span>
        }
        {flag == 1 && 
        <div style={{color:'#999',marginLeft:23}}>
          后续您也可以在
          <a style={{marginLeft:8,marginRight:8}} onClick={navigateTo}>
            <span className="iconfont shezhixitongshezhigongnengshezhishuxing fontsize-14" style={{color:'#999'}}/>设置&nbsp;-&gt;&nbsp;
            <span className="fontsize-10 iconfont chengyuan treeIconStyle" style={{color:'#999'}}/>个人设置(团队内)
          </a>
          页面中再次修改。
        </div>
        }
        <Form className="Basic-form" colon={false} form={form}>
            <Form.Item label="头像">
                <div className="Basic-form-avatar">
                    <Avatar
                      className="Basic-avatar"
                      src={state.teamMbrUserInfo?.mbrUserInfo?.avatar}
                      icon={<NoAvatarIcon/>}
                    />
                    <div className="Basic-form-upload">
                    <CameraOutlined
                      className="Basic-form-uploadIcon"
                      onClick={(event) => modifyAvater(true)}/>
                    </div>
                    {!!state.teamMbrUserInfo?.mbrUserInfo?.avatar && <CloseCircleFilled title="删除头像" className="delete-Basic-avatar" onClick={deleteLogo}/>}
                </div>
                <DraggablePopUp
                title="上传logo"
                centered
                className="avatarUpload-modal"
                open={isModalVisible}
                onCancel={() => setIsModalVisible(false)}
                footer={null}
                >
                  <PersonalBasicTab.ImgUpload avatarUpload={avatarUpload} onCancel={() => setIsModalVisible(false)}/>
                </DraggablePopUp>
            </Form.Item>
            {customFormData.map((memberInfo, index) => {
              if(memberInfo.tipsFlag) {
                return (
                  <Form.Item label={memberInfo.label} className="customFormItem">
                    <Form.Item name={memberInfo.name} noStyle>
                      <PersonalBasicTab.MemberFromItem key={memberInfo.name} memberItem={memberInfo} updateUserInfo={updateUserInfo}/>
                    </Form.Item>
                    <span className="formItem-tips">{memberInfo.tips}</span>
                  </Form.Item>
                )
              } else {
                return (
                  <Form.Item label={memberInfo.label} name={memberInfo.name}>
                    <PersonalBasicTab.MemberFromItem key={memberInfo.name} memberItem={memberInfo} updateUserInfo={updateUserInfo}/>
                  </Form.Item>
                )
              }
            })}
        </Form>
        {flag == 0 && 
          <span className="Basic-tips">
            <span className="iconfont tishi" style={{color:'#F59A23',marginRight: 4, fontSize: 18}}/>
            个人信息修改后，只会在此团队内显示，团队内成员都可以看见
          </span>
        }
    </div>
  );
}

// 图片上传
PersonalBasicTab.ImgUpload = function _(props) {
  const {avatarUpload,onCancel} = props
  const dataSource = {
    maxCount: 1,
    name: "file",
    multiple: false,
    showUploadList: false,
    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,
    beforeUpload: (file) => {
      const isPNG = file.type === "image/png" || file.type === 'image/jpeg';
      if (!isPNG) {
        globalUtil.error(`${file.name}不是图片格式`);
      }
      return isPNG || Upload.LIST_IGNORE;
    },
    onChange(info) {
      onCancel()
      const { status, response } = info.file;
      if (status == "uploading") {
        console.log(info.file, info.fileList);
      }
      if (status === "done") {
        if(response.resultCode == 200) {
          avatarUpload(response.link)
          globalUtil.success('上传成功');
        } else {
          globalUtil.error("上传失败")
        }
      } else if (status === "error") {
        globalUtil.error(`${info.file.name} file upload failed.`);
      }
    },
  };
  
  // 预览/裁剪图片
  const onPreview = async (file) => {
    let src = file.url;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj);
        reader.onload = () => resolve(reader.result);
      });
    }
    const image = new Image();
    image.src = src;
    const imgWindow = window.open(src);
    imgWindow?.document.write(image.outerHTML);
  };

  return (
    <>
      <ImgCrop 
      modalClassName="clippingImgCrop" 
      rotate modalTitle={"编辑图片"} 
      modalOk="确认" 
      modalCancel="取消"
      >
        <Dragger {...dataSource} onPreview={onPreview}>
          <p className="ant-upload-drag-icon">
            <QuestionsUploadIcon />
          </p>
          <p className="ant-upload-text">点击或拖动图片至此处</p>
          <p style={{color:'#999',marginTop:2}}>图片格式：jpg、png</p>
        </Dragger>
      </ImgCrop>
    </>
  );
}

// 自定义form表单item
PersonalBasicTab.MemberFromItem = function _({ memberItem, updateUserInfo, value='', onChange }) {
  const [modifyFlag, setModifyFlag] = useState(false);
  const [formItemValue, setFormItemValue] = useState('')
  const modifyRef = useRef();
  const triggerChange = (changedValue) => {
    onChange?.({
      formItemValue,
      ...value,
      ...changedValue,
    });
  };
  useEffect(() => {
    setFormItemValue(memberItem.value)
    triggerChange({
      formItemValue: memberItem.value,
    });
  },[])
  useEffect(() => {
    if (modifyFlag && memberItem.name != '') {
      modifyRef.current.focus({ cursor: "end" });
    }
  }, [modifyFlag]);
  const modifyClick = () => {
    setModifyFlag(true);
  };
  const modifyBlur = (e) => {
    if(!e.target.value && memberItem.key != 'email') {
      modifyRef.current.focus({ cursor: "end" });
      globalUtil.warning('不能为空')
      return
    }
    var str = e.target.value.replace(/\s/g, ""); //去除空格
    if(memberItem.key == 'mobileNo'){
      let reg = /^1[3-9][0-9]{9}$/
      if(!reg.test(str)){
        globalUtil.warning('请输入正确的手机号码')
        return
      }
    }
    if(memberItem.key == 'email' && !!str){
      let regs = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
      if(!regs.test(str)){
        globalUtil.warning('请输入正确的邮箱')
        return
      }
    }
    setModifyFlag(false);
    setFormItemValue(e.target.value)
    triggerChange({
      formItemValue: e.target.value,
    });
    if(memberItem.value === e.target.value) return
    updateUserInfo(memberItem.label);
  };

  // 获取form item编辑样式
  const getEditFormItemValue = () => (
    <Input
    className="MemberFromItem-inpvalue"
    autoComplete="off"
    style={{ width: 240 }}
    defaultValue={formItemValue}
    ref={modifyRef}
    onBlur={modifyBlur}
    onPressEnter={modifyBlur}
    />
  )
  // 格式化form item只读状态样式
  const getReadFormItemValue = () => formItemValue
  return (
    <div className="Basic-MemberFromItem" style={{ width: 240 }}>
        {modifyFlag
         ? getEditFormItemValue(memberItem)
          : <span>{getReadFormItemValue()}</span>}
        {!modifyFlag && (
          <Button
            className="Basic-MemberFromItem-editIcon"
            size="small"
            type="link"
            icon={<EditOutlined />}
            onClick={modifyClick}
          />
        )}
    </div>
  );
}