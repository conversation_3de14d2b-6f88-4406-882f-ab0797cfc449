/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable react-hooks/exhaustive-deps */
/*
 * @Author: Walt <EMAIL>
 * @Date: 2023-03-10 10:46:54
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2025-01-23 19:07:27
 * @Description: 高级搜索编辑表格
 */
import { DeleteOutlined, MinusOutlined, PlusOutlined, EditOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined, ArrowUpOutlined, ArrowDownOutlined, MenuOutlined } from "@ant-design/icons";
import { CRITERIA_RELOP_TYPE_LIST, eConsoleUiControl, CRITERIA_RELOP_TYPE, eDynamicConditionNodeId, eDynamicCondition, eQueryType, eEnTag,  eMoveType } from "@common/utils/enum";
import * as toolUtil from "@common/utils/toolUtil";
import { Button, DatePicker, Form, Input, Space, Select, Table } from "antd";
import moment from "moment";
import { nanoid } from "nanoid";
import * as qs from 'qs';
import React, { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState, } from "react";
import { isAllEmptyObjValue } from 'src/quickAcess/utils/ArrayUtils';
import { getRelOpTypeTitle } from 'src/quickAcess/utils/ViewUtils';
import {
  ATTRVALUE_LIST, ATTR_NID, ATTR_VALUE, DATE_FORMAT,
  LEFT_BRACKET, LEFT_BRACKET_STR,
  LOGICAL_OP_TYPE, RELOP_LIST, REL_OP_TYPE, RIGHT_BRACKET, RIGHT_BRACKET_STR, eDynamicVarType, eAttrSiteType,
} from "src/quickAcess/utils/Config";
import { getPropValueList } from '@common/utils/ViewUtils';
import { getOptionsView } from "@common/utils/ViewUtils";
import SearchCodeEditorInput from "@components/SearchCodeEditorInput";
import cloneDeep from 'lodash/cloneDeep';
import SearchCodeEditorModal from "./SearchCodeEditorModal"
import { globalEventBus } from "@common/utils/eventBus";
import "./SearchEditTable.scss"
import { globalUtil } from "@common/utils/globalUtil";
import { isEmpty, handleMoveAction } from "@common/utils/ArrayUtils";
import { DndContext } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { eRegionType } from "src/inspect/utils/enum";
import {getExprs} from "@common/utils/logicUtils"

const EditableContext = React.createContext(null);

const DragCustomerFormRow = ({ children, ...props }) => {
  const [form] = Form.useForm();
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key'],
  });
  const style = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1, }),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} ref={setNodeRef} style={style} {...attributes}>
          {React.Children.map(children, (child) => {
            if (child.key === "operation" && !child.props.record.isDefault) { // 操作列，且非默认行
              return {
                ...child,
                props: {
                  ...child.props,
                  render: (value, row, index) => <>
                    {/* {React.cloneElement(<span></span>, {
                      children: (
                        <MenuOutlined title="拖拽排序" ref={setActivatorNodeRef} style={{ color: "#0077f2", touchAction: 'none', cursor: 'move' }} {...listeners} />
                      ),
                    })} */}
                     <Button type="link" ref={setActivatorNodeRef} icon={<MenuOutlined title="拖拽排序"  style={{ color: "#0077f2", touchAction: 'none', cursor: 'move', fontSize: 12 }}  />} {...listeners}/>
                    {child.props.render && child.props.render(value, row, index)}
                  </>
                }
              }
            }
            return child;
          })}
        </tr>
      </EditableContext.Provider>
    </Form>
  );
};

const EditableRow = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

const EditableCell = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  attrNodeList,
  _exprs,
  envList,
  suggestionList,
  opreateList,
  queryType,
  ...restProps
}) => {

  const [editing, setEditing] = useState(false);
  const inputRef = useRef(Object.create(null));
  const codeInputRef = useRef(Object.create(null));
  const form = useContext(EditableContext);

  useEffect(() => {
    if (editing) {
      if (record.attrNid == eDynamicConditionNodeId) {
        // TODO: select和focus
      } else {
        // inputRef.current.select();
        // inputRef.current.foucs();
      }
    }
  }, [editing]);

  // 根据record更新值
  useEffect(() => {
    if (dataIndex) {
      setTableFieldsValue(record, dataIndex);
    }
  }, [record])

  const toggleEdit = () => {
    setEditing(!editing);
    setTableFieldsValue(record, dataIndex);
  };

  const toggleSearchCodeInputEdit = (e) => {
    e.stopPropagation();
    globalEventBus.emit("openSearchCodeEditorModalEvent", "", { attrNodeList, exprs: _exprs, value: record[dataIndex], envList, suggestionList, opreateList, record,  queryType, callback: codeInputSave.bind(this) });
  }

  // 刷新数据
  const setTableFieldsValue = (data, dataIndex) => {
    form.setFieldsValue({
      [dataIndex]: data[dataIndex],
    });
  }

  // 保存
  const save = async () => {
    try {
      const values = await form.validateFields();
      if (values[ATTR_NID] != record[ATTR_NID]) { // 字段更新时同时也需要更新字段类型
        //  t.clone is not a function
        let properType = attrNodeList.find(item => item.nodeId === values[ATTR_NID]);
        if(!!properType){ // 动态条件不在attrNodeList中，TODO:待优化，实际上需要解决的是动态条件的值添加后更新到form中
          const { dataType, list, relopList } = properType
          values.dataType = dataType;     // 字段类型
          values[ATTRVALUE_LIST] = list;  // 值
          values[ATTR_VALUE] = undefined; // 重置值
          values[RELOP_LIST] = relopList; // 连接符
          setTableFieldsValue(values, ATTR_VALUE);
        }
      }
      if (record.dataType == 'ListBox' && values[REL_OP_TYPE] == '1' && (values[ATTR_VALUE] || []).length > 1) {
        values[REL_OP_TYPE] = '17'
      }
      const newData = { ...record, ...values };
      handleSave(newData); // 合并原有的row和新的values值
      setTableFieldsValue(newData, dataIndex);
    } catch (errInfo) {
      console.log("Save failed:", errInfo);
    }
  };

  // Input的保存，需要切换UI,与Select和Date类型处理方式不同
  const codeInputSave = async ({ originValue }) => {
    console.log("save", originValue)
    try {
      //如果值没有更改的话，则不触发保存方法
      const newData = { ...record, [dataIndex]: originValue };
      handleSave(newData); // 合并原有的row和新的values值
      codeInputRef.current.setValue(originValue);
    } catch (errInfo) {
      console.log("Save failed:", errInfo);
    }
  };

  // Input的保存，需要切换UI,与Select和Date类型处理方式不同
  const inputSave = async ({ originValue }) => {
    console.log("save", originValue)
    try {
      //如果值没有更改的话，则不触发保存方法
      handleSave({ ...record, [dataIndex]: originValue }); // 合并原有的row和新的values值
      toggleEdit();
    } catch (errInfo) {
      console.log("Save failed:", errInfo);
    }
  };

  // 值渲染的UI
  const getValueInputUI = () => {
    if (record.attrNid == eDynamicConditionNodeId) { // 动态条件
      if (editing) {
        childNode = (
          <Form.Item
            style={{
              margin: 0,
            }}
            name={dataIndex}
          >
            <SearchCodeEditorInput
              ref={codeInputRef}
              envList={envList}
              suggestionList={suggestionList}
              onPressEnter={inputSave}
              onBlur={inputSave}
            />
          </Form.Item>
        );
      } else {
        childNode = (
          <div className="search-input-editble" onClick={toggleEdit}>
            <SearchCodeEditorInput
              ref={codeInputRef}
              envList={envList}
              suggestionList={suggestionList}
              readOnly={true}
              value={record[dataIndex]}
            />
            <span className="editble-icon"><EditOutlined onClick={toggleSearchCodeInputEdit} /></span>
          </div>
        );
      }
    } else if (record.dataType === eConsoleUiControl.TextBox) {
      // input类型
      if (editing) {
        childNode = (
          <Form.Item
            style={{
              margin: 0,
            }}
            name={dataIndex}
          >
            <Input
              ref={inputRef}
              autoComplete="off"
              placeholder={placeholder}
              onPressEnter={(e) => { inputSave({ originValue: e.target.value }) }}
              onBlur={(e) => { inputSave({ originValue: e.target.value }) }}
              className="search-table-input"
            />
          </Form.Item>
        );
      } else {
        //非编辑状态，如果没有值，则显示默认值placeholder，需要创建引用变量data，因为children只读，不能更改
        let data = [...children];
        let value = !isEmpty(children[1]) ? children[1] : placeholder;
        data[1] = value;
        let className = !isEmpty(children[1])
          ? "editable-cell-value-wrap"
          : "editable-cell-value-wrap-none";
        childNode = (
          <div className={className} onClick={toggleEdit}>
            {data}
          </div>
        );
      }
    } else if (record.dataType === eConsoleUiControl.ListBox) {
      childNode = (
        <Form.Item
          style={{
            margin: 0,
          }}
          name={dataIndex}
        >
          <Select
            // open={true} //默认展开，单击即可展开
            // dropdownClassName="table-select-option"
            dropdownMatchSelectWidth={false}
            bordered={false}
            mode="multiple" //值可以多选
            className="search-table-select"
            popupClassName="search-table-select-pop"
            optionFilterProp="propValue"
            onBlur={save} //失去焦点回调
            onChange={save} //改变后回调
            allowClear
            showSearch
            fieldNames={{
              label: "propValue",
              value: "propType"
            }}
            options={record[ATTRVALUE_LIST]}
          >
          </Select>
        </Form.Item>
      );
    } else if (record.dataType === eConsoleUiControl.Date) {
      childNode =
        <Form.Item
          style={{
            margin: 0,
          }}
          name={dataIndex}
        >
          <DatePicker format={DATE_FORMAT} onChange={save} className="search-table-datepicker"/>
        </Form.Item>
    }
    return childNode;
  }

  let childNode = children;
  let placeholder = "请输入" + title;

  const fieldObjectType = useMemo(() => {
    return (attrNodeList || []).reduce((pre, cur) => {
      const curObj = {
        key: cur.nodeId,
        value: cur.nodeId,
        label: cur.nodeName,
        dataType: cur.dataType
      };
      return pre.concat(curObj);
    }, []);
  }, [attrNodeList]);

  // 下拉列表的值
  let objectTypeOptions;
  switch (dataIndex) {
    case ATTR_NID:
      // 字段
      // objectTypeSelect = getOptionsView(fieldObjectType);
      objectTypeOptions = fieldObjectType;
      break;
    case LOGICAL_OP_TYPE:
      // 连接符
      // objectTypeSelect = getOptionsView(CRITERIA_RELOP_TYPE_LIST);
      objectTypeOptions = CRITERIA_RELOP_TYPE_LIST;
      break;
    case REL_OP_TYPE:
      // 条件
      // objectTypeSelect = getPropValueList(record[RELOP_LIST])
      objectTypeOptions =  (record[RELOP_LIST] || []).map(relop =>({
        key: relop.propType,
        value: relop.propType,
        label: relop.propValue
      }))
      break;
    default:
      break;
  }

  if (editable) {
    if (dataIndex == ATTR_NID ||
      dataIndex == LOGICAL_OP_TYPE || dataIndex == REL_OP_TYPE) {
      if (record[ATTR_NID] == eDynamicConditionNodeId && dataIndex == REL_OP_TYPE) { // 动态字段没有条件
        childNode = (
          <div className="editable-cell-disabled">
          </div>
        );
      } else if (record[ATTR_NID] == eDynamicConditionNodeId && dataIndex == ATTR_NID) { // 动态字段
        childNode = (
          <div>
            动态条件（<FunctionOutlined className="color-blue" style={{margin: 0}}/>）
          </div>
        );
      } else {
        childNode = (
          <Form.Item
            style={{
              margin: 0,
            }}
            name={dataIndex}
          >
            <Select
              // open={true} //默认展开，单击即可展开
              dropdownMatchSelectWidth={false}
              bordered={false}
              className="search-table-select"
              popupClassName="search-table-select-pop"
              // dropdownClassName="table-select-option"
              optionFilterProp="label" 
              onBlur={save} //失去焦点回调
              onChange={save} //改变后回调
              allowClear={dataIndex == ATTR_NID}
              showSearch={dataIndex == ATTR_NID}
              options={objectTypeOptions}
            >
            </Select>
          </Form.Item>
        );
      }
    } else {
      // 值
      childNode = getValueInputUI();
    }
  }

  return <td {...restProps}>{childNode}</td>;
};

/**
 * TODO: 编辑器值fontsize-12,color:#333
 * 高级搜索-搜索条件表格
 *  FIXME: 父组件获取子组件实例关键点:
 * 1.useImperativeHandle/forwardRef 
 * 2.ref不是结构赋值，而是直接取值
 * @param {queryType} 是否是Kpi模块 
 * @param {*} ref 
 * @returns 
 */
// TODO:此组件为了方便kpi和高级搜索，将数据处理逻辑合并到该组件，需要注意数据处理不能影响原数据，因此全部采用map遍历的形式，可能并不是好的思路，需要注意
function SearchEditTable({ attrNodeList = [], criteriaList = [], selectionList, exprs, queryType, loading }, ref,) {

  const [initDataSource, setInitDataSource] = useState([])//初始化数据
  const [dataSource, setDataSource] = useState([]) //修改后的数据
  const [_exprs, setExprs] = useState([]) // 变量

  // 可以让你在使用 ref 时自定义暴露给父组件的实例值
  useImperativeHandle(ref, () => ({
    // 获取列表内容:过滤掉默认选项
    getCriteriaListForUI: () => getCriteriaListForUI(dataSource),
    getInitDataSource: () => getCriteriaListForUI(initDataSource),
    getCriteriaListForBackend: () => getCriteriaListForBackend(),
    addSearchCode: () => addSearchCode(),
  }));

  useEffect(() => {
    if (!isEmpty(attrNodeList)) {
      initData();
    }
  }, [qs.stringify(attrNodeList), qs.stringify(criteriaList), qs.stringify(exprs)]);

  // 获取表格属性
  const getCriteriaListForUI = (data) => {
    try {
      // 注意：该方法会走两遍,不能更改原有数据结构，所以需要使用map，不能使用forEach改变原有数据结构，也不能使用json.parse()
      data = data.filter(item => !item.isDefault);
      return (data || []).map(item => {
        let attrValue = item.attrValue;
        switch (item.dataType) {
          case eConsoleUiControl.ListBox:
            // 多选框，需要将多选数组转化为字符串逗号拼接 
            attrValue = !isEmpty(item.attrValue) ? item.attrValue.join(",") : item.attrValue;
            break;
          case eConsoleUiControl.Date:
            // 时间需要转为字符串
            attrValue = !isEmpty(item.attrValue) ? item.attrValue.format(DATE_FORMAT) : item.attrValue;
            break;
          default:
            break;
        }
        return { ...item, ...{ attrValue } }
      });
    } catch (error) {
      globalUtil.warning("保存失败");
      return [];
    }
  }

  // 获取高级搜索自定义表单接口数据
  const getCriteriaListForBackend = () => {
    try {
      let data = getCriteriaListForUI(dataSource);
      // 获取表格内容
      data = data.map(item => (
        {
          "leftBracketCnt": item[LEFT_BRACKET]?.length,
          "attrNid": item[ATTR_NID],
          "relOpType": item[REL_OP_TYPE],
          "attrValue": item[ATTR_VALUE],
          "logicalOpType": item[LOGICAL_OP_TYPE] || "0",
          "rightBracketCnt": item[RIGHT_BRACKET]?.length,
          "isDisableDelete": item.isDisableDelete,
        }
      ))
      return data || [];
    } catch (e) {
      globalUtil.warning("保存失败");
      console.warn("高级搜索自定义表单保存失败", e);
      // message.warn("高级搜索自定义表单保存失败");
      return []
    }
  }

  const relOpTypeTitle = useMemo(() => (getRelOpTypeTitle(selectionList)), []);

  const getColumnsOperate = (text, record, index, bracketCnt, bracketStr) => {
    return <div className="search-table-opreate">
      <a
        onClick={() => {
          reduce(record, index, bracketCnt, bracketStr);
        }}
        className="minus-btn "
      ><MinusOutlined /></a>
      <span>{text}</span>
      <a
        onClick={() => {
          add(record, index, bracketCnt, bracketStr);
        }}
        className="plus-btn"
      ><PlusOutlined /></a>
    </div>
  }

  const columnsRow = [
    {
      title: "左(",
      dataIndex: LEFT_BRACKET,
      // colunms的render不会随着数据的刷新而刷新,FIXME:改成function函数式组件后，可以刷新了，说明Class的刷新机制和function不太一致
      render: (text, record, index) => (getColumnsOperate(text, record, index, LEFT_BRACKET, LEFT_BRACKET_STR)),
      width: 60,
    },
    {
      title: "字段",
      dataIndex: ATTR_NID,
      editable: true,
      width: 130,
    },
    {
      title: relOpTypeTitle,
      dataIndex: REL_OP_TYPE,
      editable: true,
      width: 80,
    },
    {
      title: "值",
      dataIndex: ATTR_VALUE,
      editable: true,
    },
    {
      title: "右)",
      dataIndex: RIGHT_BRACKET,
      render: (text, record, index) => (getColumnsOperate(text, record, index, RIGHT_BRACKET, RIGHT_BRACKET_STR)),
      width: 60,
    },
    {
      title: "连接符",
      dataIndex: LOGICAL_OP_TYPE,
      editable: true,
      width: 60,
    },
    {
      title: "操作",
      dataIndex: "operation",
      width: 100,
      render: (_, record, index) => {
        if (record.isDefault) {
          return <></>
        }
        return (
          <Space size={10}>
            <Button
              type="link"
              icon={<DeleteOutlined />}
              onClick={() => {
                handleDelete(record.key);
              }}
              size="small"
              disabled={record.isDefault || record.isDisableDelete} //默认行、不可删除
              title="删除"
            ></Button>
          </Space>
        )
      }
    },
  ];

  // 获取默认值
  const getDefaultRow = () => {
    return {
      key: nanoid(),
      leftBracket: "",        //左括号(
      attrNid: "",            //字段
      relOpType: undefined,   //条件
      attrValue: undefined,   //值
      rightBracket: "",       //右括号）
      logicalOpType: "",      //连接符
      dataType: eConsoleUiControl.TextBox, //字段类型:默认文本
      attrValueList: [],                    //值Select的List
      isDefault: true,
    };
  };

  // 初始化数据
  const initData = () => {
    let initData1 = assembleInitTableData([...criteriaList]);
    setInitDataSource([...initData1]); //记录初始化数据
    let data = addDefaultRow([...initData1]);
    setDataSource(cloneDeep(data));
    const _exprs = getExprs(attrNodeList, exprs);
    setExprs(_exprs);
  }

  // 初始化数据
  const assembleInitTableData = (criteriaList) => {
    try {
      if (isEmpty(criteriaList)) return [];
      // TODO:288个条件直接卡死
      return criteriaList.map(item => {
        let newItem = {}
        let properType = getProperType(item.attrNid);
        const { dataType, list, relopList } = properType;
        newItem.key = toolUtil.guid(); //本地数据没有id,需要guid作为key
        newItem.dataType = dataType;
        newItem[ATTRVALUE_LIST] = list;
        newItem[RELOP_LIST] = relopList; //连接符
        newItem[REL_OP_TYPE] = item[REL_OP_TYPE]?.toString(); //连接符,需要转为String
        if (dataType == eConsoleUiControl.ListBox) {
          newItem.attrValue = item.attrValue.split(",");  //List
        } else if (dataType == eConsoleUiControl.Date) {
          newItem.attrValue = moment(item.attrValue, DATE_FORMAT) //日期类型格式化
        }
        return { ...item, ...newItem };
      }) || []
    } catch (e) {
      globalUtil.warning('自定义表单数据初始化失败！');
      console.log('自定义表单数据初始化失败！', e);
    }
  }

  // 根据attrNid获取下拉List
  const getProperType = (attrNid) => {
    return ([...attrNodeList, eDynamicCondition]).find(item => item.nodeId === attrNid) || {};
  }

  //添加默认项
  const addDefaultRow = (obj) => {
    let defaultIndex = obj.findIndex((item) => item.isDefault);
    if (defaultIndex === -1) {
      let defaultRow = getDefaultRow();
      obj.push(defaultRow);
    }
    return obj
  };

  // 减少左括号/增加右括号
  const reduce = (record, index, bracketCnt, bracketStr) => {
    const newDataSource = [...dataSource];
    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt].replace(bracketStr, "");
    setDataSource(cloneDeep(newDataSource));
  };

  const add = (record, index, bracketCnt, bracketStr) => {
    const newDataSource = [...dataSource];
    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt] + bracketStr;
    setDataSource(cloneDeep(newDataSource));
  };

  const handleDelete = (key) => {
    const newDataSource = [...dataSource];
    setDataSource(cloneDeep(newDataSource.filter((item) => item.key !== key)));
  };

  // 上移下移 type
  const handleMove = (record, type) => {
    const data = handleMoveAction(dataSource, record, type);
    setDataSource(data);
  };

  // 当输入新的一行内容后，上一行的“连接符”默认给 “且”，因为这个是用户必选的项，又经常容易忘了设置。
  const handleAddLogicalOpType = (dataSource) => {
    dataSource.forEach((field, index) => {
      if (!field.isDefault && index != dataSource.length - 2 && (field[LOGICAL_OP_TYPE] == CRITERIA_RELOP_TYPE.op_place || isEmpty(field[LOGICAL_OP_TYPE]))) {
        field[LOGICAL_OP_TYPE] = CRITERIA_RELOP_TYPE.op_and
      }
    });
    setDataSource(cloneDeep(dataSource))
  }

  const handleSave = (row) => {
    // 判断row是否有数据
    const newData = [...dataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    if (row.isDefault && !isAllEmptyObjValue(row)) row.isDefault = false; //将默认行且非空则更改为非默认行
    newData.splice(index, 1, { ...item, ...row });
    let data = addDefaultRow([...newData]);
    handleAddLogicalOpType(data);
  };

  // 变量
  const envList = useMemo(() => {
    // 搜索模块，需要去除kpiTag为isKpi的变量
    let _envList = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.variable && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) )).map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue,}));
    return [..._envList]
  }, [attrNodeList, _exprs, queryType]);

  // 函数
  const suggestionList = useMemo(() => {
    const _dynamicCondition = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.func &&  (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) ));
    let _suggestionList1 = _dynamicCondition.map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue, functionName: attrNode.functionName }));
    return [..._suggestionList1,]
  }, [attrNodeList, _exprs, queryType]);

  // 操作符
  const opreateList = useMemo(() => {
    const _dynamicCondition = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.operator &&  (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) ));
    let _operatorList = _dynamicCondition.map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue, functionName: attrNode.functionName }));
    return [..._operatorList,]
  }, [attrNodeList, _exprs, queryType]);

  // 添加动态条件
  const addSearchCode = () => {
    globalEventBus.emit("openSearchCodeEditorModalEvent", "", { attrNodeList, exprs: _exprs, value: "", envList, suggestionList, opreateList, queryType, callback: addSearchCodeCallback });
  }

  // 添加动态条件回调
  const addSearchCodeCallback = ({ originValue }) => {
    const newData = [...dataSource];
    const newValue = {
      key: nanoid(),
      leftBracket: "",                  //左括号(
      attrNid: eDynamicConditionNodeId, //字段
      relOpType: undefined,             //条件
      attrValue: originValue,           //值
      rightBracket: "",                 //右括号）
      logicalOpType: "",                //连接符
      dataType: eConsoleUiControl.TextBox, //字段类型:默认文本
      attrValueList: [],                    //值Select的List
      isDefault: false,
    };
    // 数组倒数第二位插入
    newData.splice(newData.length - 1, 0, newValue);
    handleAddLogicalOpType(newData); // 添加动态条件也需要检查连接符
  }

  // 拖拽排序
  const onDragEnd = ({ active, over }) => {
    if ((active?.id && over?.id) && (active?.id !== over?.id)) {
      let _dataSource = [...dataSource];
      const activeIndex = _dataSource.findIndex((i) => i.key == active?.id);
      const overIndex = _dataSource.findIndex((i) => i.key == over?.id);
      _dataSource = arrayMove(_dataSource, activeIndex, overIndex);
      setDataSource(cloneDeep(_dataSource))
    }
  };
    
  return (
    <div>
      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.key)} // 需要全部数据的key,否则无法拖拽
          strategy={verticalListSortingStrategy}
        >
        <Table
          components={{
            body: {
              row: DragCustomerFormRow,
              cell: EditableCell,
            },
          }}
          rowKey="key" // 注意不能是key,也不能不填
          rowClassName={() => "editable-row"}
          className="search-table custome-table custom-table-border"
          bordered
          loading={loading}
          dataSource={dataSource}
          columns={columnsRow.map((col) => {
            if (!col.editable) {
              return col;
            }

            return {
              ...col,
              onCell: (record) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: handleSave,
                attrNodeList: attrNodeList,
                _exprs: _exprs,
                envList: envList,
                suggestionList: suggestionList,
                opreateList: opreateList,
                queryType: queryType,
              }),
            };
          })}
          pagination={false}
        />
        </SortableContext>
      </DndContext>
      <SearchCodeEditorModal />
    </div>
  );
}

export default forwardRef(SearchEditTable);
