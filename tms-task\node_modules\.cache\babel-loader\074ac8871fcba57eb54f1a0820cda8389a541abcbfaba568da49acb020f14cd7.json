{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\CreateTeam.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { useEffect, useState, useRef } from \"react\";\nimport { Modal, Form, Input, Radio, Badge, InputNumber, Row, Image, Button, Table, Checkbox, Dropdown, Menu, Card, Tooltip, Popconfirm } from \"antd\";\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport { getTeamList } from \"@/team/store/actionCreators\";\nimport { CheckCircleOutlined, CloseOutlined, ExclamationCircleOutlined, QuestionCircleOutlined, DownOutlined } from '@ant-design/icons';\nimport { team_703_calc_price, team_704_submit_order, team_705_get_order_status, team_711_get_team_product_list, team_735_bind_user_coupon, team_736_unbind_user_coupon } from \"@common/api/http\";\nimport { setting_105_get_team_detail_query, team_701_get_product_list_query, team_706_get_free_team_count_by_user_query, useQueryGetUserValidCoupon } from \"@common/api/query/query\";\nimport { useDebounce } from \"@common/hook\";\nimport QRCode from \"qrcode\";\nimport \"./CreateTeam.scss\";\nimport TLoading from \"./TLoading\";\nimport { useQueries } from \"@tanstack/react-query\";\nimport moment from \"moment\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { eCouponType, eOrderStatus, eProductGroupId, eProductId, eProductStatus } from \"@common/utils/enum\";\nimport DraggableDrawer from \"@components/DraggableDrawer\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Search\n} = Input;\nconst {\n  Column,\n  ColumnGroup\n} = Table;\nexport const CREATETYPE_CREATE = 0; // 创建团队\nexport const CREATETYPE_UPGRADE = 1; // 升级至企业版\n// export const CREATETYPE_EXPAND = 2; // 成员扩容\n// export const CREATETYPE_RENEWAL = 3; // \"应用\"续费\n// export const CREATETYPE_BUY = 4; // \"应用\"新购\n\nconst formItemLayout = {\n  labelCol: {\n    span: 0\n  }\n};\n\n/**\r\n * @description 创建工作区\r\n * @param {*} param0 \r\n * @returns \r\n */\nexport function CreateTeam({\n  onCancel,\n  onOk,\n  teamId,\n  type,\n  priceData,\n  productList,\n  setIsChange\n}) {\n  _s();\n  var _team706Result$data, _team706Result$data2, _myCouponList$find, _myCouponList$find2, _myCouponList$find3, _myCouponList$find4;\n  const {\n    teamId: localTeamId\n  } = useParams();\n  const queryResult = useQueries({\n    queries: [team_701_get_product_list_query(), team_706_get_free_team_count_by_user_query(), {\n      ...setting_105_get_team_detail_query(teamId),\n      enabled: !!teamId\n    }\n    // team_702_get_team_package_duration_rebate_query(), //20231025 Jim Song, 月度折扣接口已废弃，伴随\"应用\"列表接口一起范围\n    ]\n  });\n  const [team701Result, team706Result, setting105Result] = queryResult;\n  const {\n    data: {\n      couponList\n    } = {\n      couponList: []\n    },\n    refetch: refetchGetUserValidCoupon\n  } = useQueryGetUserValidCoupon(teamId);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [discountForm] = Form.useForm();\n  const [cdiskCheck, setCdiskCheck] = useState(true);\n  const [priceInfo, setPriceInfo] = useState({\n    originalPrice: 0,\n    regularAmt: 0,\n    promoAmt: 0,\n    discountReduction: 0,\n    payPrice: 0,\n    couponReduction: 0,\n    resultCode: null,\n    resultMessage: null\n  });\n  const freeAuthUserQuota1000 = 1000; //1000个免费用户数\n\n  // 是否确认购买\n  const [isPaying, setIsPaying] = useState(false);\n  const [payInfo, setPayInfo] = useState();\n  const [showPriceDetail, setShowPriceDetail] = useState(false);\n  const vipFlg = Form.useWatch('isEnterpriseEdition', form);\n  const [discountCodeVisible, setDiscountCodeVisible] = useState(false);\n  const [payCode, setPayCode] = useState({\n    orderId: null\n  });\n  // const [payCode1,setPayCode1] = useState({\n  //   orderInfo:{\n  //     orderId: null,\n  //   },\n  //   payUrl: null\n  // });\n\n  const [url, setUrl] = useState();\n  const [url1, setUrl1] = useState();\n  const [spining, setSpining] = useState(false);\n  const discountCodeRef = useRef({});\n  const [selectedProductList, setSelectedProductList] = useState([]);\n  const [tipShow, setTipShow] = useState(false);\n  const [couponSelect, setCouponSelect] = useState(null);\n  const [refreshingFlg, setRefreshingFlg] = useState(false);\n  const [myCouponList, setMyCouponList] = useState([]);\n\n  // 获取协作群基本信息\n  useEffect(() => {\n    if (type !== CREATETYPE_CREATE) {\n      let res = setting105Result.data;\n      if (res && res.resultCode === 200) {\n        form.setFieldValue(\"teamName\", res.name);\n      }\n    }\n  }, [setting105Result.dataUpdatedAt]);\n  useEffect(() => {\n    if (!!(priceData !== null && priceData !== void 0 && priceData.code) && priceData !== null && priceData !== void 0 && priceData.isEnterpriseEdition) {\n      setCouponSelect({\n        couponCode: priceData.code\n      });\n    }\n  }, [priceData === null || priceData === void 0 ? void 0 : priceData.code]);\n  useEffect(() => {\n    if ((couponList || []).length > 0) {\n      setMyCouponList([...couponList]);\n    }\n  }, [JSON.stringify(couponList)]);\n  useEffect(() => {\n    var _team701Result$data;\n    if ((((_team701Result$data = team701Result.data) === null || _team701Result$data === void 0 ? void 0 : _team701Result$data.productList) || []).length > 0) {\n      let formatList = team701Result.data.productList.filter(group => vipFlg == 1 ? group.groupId != eProductGroupId.Pgid_1_OS : true);\n      let _prodGrpList = [];\n      formatList.forEach(product => {\n        let item = _prodGrpList.find(_product => product.groupId == _product.groupId);\n        if (!item) {\n          _prodGrpList.push({\n            groupName: product.groupName,\n            groupId: product.groupId,\n            products: [product]\n          });\n        } else {\n          item.products.push(product);\n        }\n      });\n      let allProductsList = [];\n      productList = productList || []; //尝试修复 tmsbug-8699\n      _prodGrpList.forEach(_prodGrp => {\n        let _productList = _prodGrp.products.map((config, index) => {\n          var _productList$find, _productList$find2, _productList$find3, _productList$find4, _productList$find5, _productList$find6, _find, _productList$find7, _productList$find8, _productList$find9;\n          // !!data.lastExpirationDt && leftDaysFormat(data.lastExpirationDt) < 0\n          let lastMemberNo = type === CREATETYPE_UPGRADE && ((_productList$find = productList.find(product => product.productId == config.productId)) === null || _productList$find === void 0 ? void 0 : _productList$find.freeFlg) != 1 ? (((_productList$find2 = productList.find(product => product.productId == config.productId)) === null || _productList$find2 === void 0 ? void 0 : _productList$find2.authCnt) || 0) > 0 ? (_productList$find3 = productList.find(product => product.productId == config.productId)) === null || _productList$find3 === void 0 ? void 0 : _productList$find3.authCnt : 0 : 0;\n          let lastExpirationDt = type === CREATETYPE_UPGRADE && ((_productList$find4 = productList.find(product => product.productId == config.productId)) === null || _productList$find4 === void 0 ? void 0 : _productList$find4.freeFlg) != 1 ? !!((_productList$find5 = productList.find(product => product.productId == config.productId)) !== null && _productList$find5 !== void 0 && _productList$find5.expirationDt) ? (_productList$find6 = productList.find(product => product.productId == config.productId)) === null || _productList$find6 === void 0 ? void 0 : _productList$find6.expirationDt : '' : '';\n          let checked = (_find = (selectedProductList || []).find(record => record.productId == config.productId)) === null || _find === void 0 ? void 0 : _find.checked;\n          let item = {\n            ...config,\n            key: config.productId,\n            isRowSpan: index == 0 ? true : false,\n            prodsLength: index == 0 ? _prodGrp.products.length : 0,\n            memberNo: lastMemberNo > 0 && !!lastExpirationDt && leftDaysFormat(lastExpirationDt) < 0 ? lastMemberNo : 0,\n            memberNoShow: lastMemberNo > 0 && !!lastExpirationDt && leftDaysFormat(lastExpirationDt) < 0 ? lastMemberNo : 0,\n            lastMemberNo: lastMemberNo,\n            nextMemberNo: type === CREATETYPE_UPGRADE && ((_productList$find7 = productList.find(product => product.productId == config.productId)) === null || _productList$find7 === void 0 ? void 0 : _productList$find7.freeFlg) != 1 ? (((_productList$find8 = productList.find(product => product.productId == config.productId)) === null || _productList$find8 === void 0 ? void 0 : _productList$find8.authCnt) || 0) > 0 ? (_productList$find9 = productList.find(product => product.productId == config.productId)) === null || _productList$find9 === void 0 ? void 0 : _productList$find9.authCnt : 0 : 0,\n            durationMonth: null,\n            name: '',\n            rebate: '',\n            effectBeginDt: '',\n            expirationDt: '',\n            lastExpirationDt: lastExpirationDt,\n            originalPrice: '',\n            discountPrice: '',\n            checked: config.productId == eProductId.Pid_13_Cdisk ? cdiskCheck : checked == true || checked == false ? checked : config.statusType == eProductStatus.Status_2_QA || config.statusType == eProductStatus.Status_3_Unreleased ? false : true\n          };\n          return item;\n        });\n        allProductsList = allProductsList.concat(_productList);\n      });\n      if (!!(priceData !== null && priceData !== void 0 && priceData.teamPackage)) {\n        let list1 = JSON.parse(priceData.teamPackage);\n        allProductsList = allProductsList.map(config => {\n          var _list1$find, _list1$find2, _list1$find3, _list1$find4;\n          config.memberNo = (_list1$find = list1.find(a => a.productId == config.productId)) === null || _list1$find === void 0 ? void 0 : _list1$find.memberNo;\n          config.durationMonth = (_list1$find2 = list1.find(a => a.productId == config.productId)) === null || _list1$find2 === void 0 ? void 0 : _list1$find2.durationMonth;\n          config.name = (_list1$find3 = list1.find(a => a.productId == config.productId)) === null || _list1$find3 === void 0 ? void 0 : _list1$find3.name;\n          config.rebate = (_list1$find4 = list1.find(a => a.productId == config.productId)) === null || _list1$find4 === void 0 ? void 0 : _list1$find4.rebate;\n          return config;\n        });\n      }\n      setSelectedProductList([...allProductsList]);\n      if (!!(priceData !== null && priceData !== void 0 && priceData.teamPackage)) {\n        setTimeout(() => {\n          load_team_703_calc_price(priceData === null || priceData === void 0 ? void 0 : priceData.code, allProductsList);\n        }, 3000);\n      }\n    }\n  }, [JSON.stringify(team701Result), vipFlg]);\n  function checkBoxChange(e, key) {\n    let checked = e.target.checked;\n    let item = selectedProductList.find(data => data.key == key);\n    if (!!item) {\n      if (key == eProductId.Pid_13_Cdisk) {\n        setCdiskCheck(checked);\n      }\n      item.checked = checked;\n      let list = [];\n      if (checked) {\n        list = (item.dependsOnIds || '').split(',').filter(_productId => !!_productId);\n      } else {\n        list = (item.dependedByIds || '').split(',').filter(_productId => !!_productId);\n      }\n      list.forEach(_productId => {\n        let _item = selectedProductList.find(data => data.key == _productId);\n        _item.checked = checked;\n      });\n      setSelectedProductList([...selectedProductList]);\n    }\n  }\n  function memberNoChange(item, clickType) {\n    let data = selectedProductList.find(data => data.key == item.key);\n    if (clickType == 0) {\n      if (type == CREATETYPE_CREATE && data.memberNo <= 0) {\n        return;\n      }\n      if (type == CREATETYPE_UPGRADE && data.memberNo <= -data.lastMemberNo) {\n        return;\n      }\n    }\n    if (clickType == 1) {\n      if (data.memberNo >= 1000) {\n        return;\n      }\n    }\n    data.memberNo = clickType == 0 ? data.memberNo - 1 : clickType == 1 ? data.memberNo + 1 : data.memberNoShow || 0;\n    data.memberNoShow = clickType == 0 ? data.memberNoShow - 1 : clickType == 1 ? data.memberNoShow + 1 : data.memberNoShow || 0;\n    if (newBuyFormat(data) == 1) {\n      if (!data.durationMonth) {\n        var _find2, _team701Result$data2, _find3, _team701Result$data3;\n        data.durationMonth = 12;\n        data.name = ((_find2 = (((_team701Result$data2 = team701Result.data) === null || _team701Result$data2 === void 0 ? void 0 : _team701Result$data2.monthPromoList) || []).find(item1 => item1.monthCnt == 12)) === null || _find2 === void 0 ? void 0 : _find2.monthCntDesc) || '';\n        data.rebate = ((_find3 = (((_team701Result$data3 = team701Result.data) === null || _team701Result$data3 === void 0 ? void 0 : _team701Result$data3.monthPromoList) || []).find(item1 => item1.monthCnt == 12)) === null || _find3 === void 0 ? void 0 : _find3.promoRateDesc) || '';\n      }\n      if (data.memberNo <= 0) {\n        data.durationMonth = null;\n        data.name = '';\n        data.rebate = '';\n      }\n    }\n    let teamName = form.getFieldValue('teamName');\n    isChange(teamName, selectedProductList);\n    load_team_703_calc_price(couponSelect === null || couponSelect === void 0 ? void 0 : couponSelect.couponCode);\n    setSelectedProductList([...selectedProductList]);\n  }\n  function memberNoChangeW(item, value) {\n    let data = selectedProductList.find(data => data.key == item.key);\n    data.memberNo = value;\n    data.memberNoShow = value;\n    if (newBuyFormat(data) == 1) {\n      if (!data.durationMonth) {\n        var _find4, _team701Result$data4, _find5, _team701Result$data5;\n        data.durationMonth = 12;\n        data.name = ((_find4 = (((_team701Result$data4 = team701Result.data) === null || _team701Result$data4 === void 0 ? void 0 : _team701Result$data4.monthPromoList) || []).find(item1 => item1.monthCnt == 12)) === null || _find4 === void 0 ? void 0 : _find4.monthCntDesc) || '';\n        data.rebate = ((_find5 = (((_team701Result$data5 = team701Result.data) === null || _team701Result$data5 === void 0 ? void 0 : _team701Result$data5.monthPromoList) || []).find(item1 => item1.monthCnt == 12)) === null || _find5 === void 0 ? void 0 : _find5.promoRateDesc) || '';\n      }\n      if ((value || 0) <= 0) {\n        data.durationMonth = null;\n        data.name = '';\n        data.rebate = '';\n      }\n    }\n    setSelectedProductList([...selectedProductList]);\n  }\n  function durationMonthChange(item, item1) {\n    let data = selectedProductList.find(data => data.key == item.key);\n    if (!!item1) {\n      data.durationMonth = item1.monthCnt;\n      data.name = item1.monthCntDesc;\n      data.rebate = item1.promoRateDesc;\n      if (newBuyFormat(data) == 1) {\n        if (data.memberNo == 0) {\n          data.memberNo = 1;\n          data.memberNoShow = 1;\n        }\n      }\n    } else {\n      data.durationMonth = null;\n      data.name = '';\n      data.rebate = '';\n      if (newBuyFormat(data) == 1) {\n        data.memberNo = 0;\n        data.memberNoShow = 0;\n      }\n    }\n    let teamName = form.getFieldValue('teamName');\n    isChange(teamName, selectedProductList);\n    load_team_703_calc_price(couponSelect === null || couponSelect === void 0 ? void 0 : couponSelect.couponCode);\n    setSelectedProductList([...selectedProductList]);\n  }\n  function teamNameChange(e) {\n    isChange(e.target.value, selectedProductList);\n  }\n  function isChange(teamName, currentList) {\n    if (type == CREATETYPE_CREATE && !!teamName) {\n      setIsChange(true);\n    } else {\n      if (currentList.filter(product => product.memberNo != 0 || !!product.durationMonth).length > 0) {\n        setIsChange(true);\n      } else {\n        setIsChange(false);\n      }\n    }\n  }\n  const _onOk = useDebounce(async () => {\n    let values = form.getFieldsValue(true);\n    if (!values.teamName) {\n      globalUtil.warning('请输入团队名称');\n      return;\n    }\n    if (vipFlg == 0 && selectedProductList.filter(product => product.checked && product.productId != eProductId.Pid_11_Explorer && product.productId != eProductId.Pid_12_Space).length <= 0) {\n      globalUtil.warning('至少启用一个应用');\n      return;\n    }\n    let validFlg = false;\n    if (priceInfo.payPrice <= 0) {\n      if (type == CREATETYPE_CREATE) {\n        if (vipFlg == 1 && (priceInfo.couponReduction || 0) <= 0) {\n          globalUtil.warning('请选择套餐');\n          return;\n        }\n        validFlg = true;\n      } else {\n        if (priceInfo.payPrice < 0) {\n          setTipShow(true);\n          return;\n        }\n        if (selectedProductList.filter(data => data.memberNo < 0).length == 0 && (priceInfo.couponReduction || 0) <= 0) {\n          globalUtil.warning('请选择套餐');\n          return;\n        }\n        validFlg = true;\n      }\n    }\n    let _prodsInCart = selectedProductList //购物车(有数量的产品）\n    .filter(data => data.statusType != eProductStatus.Status_3_Unreleased && (data.memberNo != 0 || !!data.durationMonth)).map(data => ({\n      productId: data.productId,\n      adjustMonthCnt: !!data.durationMonth ? data.durationMonth : 0,\n      adjustAuthCnt: data.memberNo\n    }));\n    setSpining(true);\n    let payInfo1 = {\n      teamName: values.teamName,\n      orderType: type == CREATETYPE_CREATE ? 1 : type == CREATETYPE_UPGRADE ? 2 : ''\n    };\n    let couponCode = null;\n    if (vipFlg == 0) {\n      payInfo1.productList = [];\n      payInfo1.productIdList = selectedProductList.filter(product => product.checked).map(product => {\n        return product.productId;\n      }); //20250731 后端需要这个数组字段来判断\n      //payInfo1.disabledProductList = selectedProductList.filter(product => !product.checked).map(product => {return product.productId}) //后端不需要，禁用掉\n    } else {\n      payInfo1.productList = _prodsInCart;\n      if (!!couponSelect && priceInfo.couponReduction > 0) {\n        couponCode = couponSelect.couponCode;\n      }\n    }\n    if (validFlg) {\n      await upload_team_704_team_package_pay_order(payInfo1, couponCode, 1);\n    } else {\n      await upload_team_704_team_package_pay_order(payInfo1, couponCode, 2);\n    }\n    setPayInfo(payInfo1);\n    setSpining(false);\n    // } else {\n    //   setSpining(true);\n    //   let _packageList = dataSource.map(data => ({\n    //     objType: data.objType,\n    //     enableFlg: data.checked ? 1: 0,\n    //     typeDesc: data.typeDesc,\n    //     userCnt: 0\n    //   }))\n    //   // 创建免费团队\n    //   let request = {\n    //     teamName: values.teamName,\n    //     isEnterpriseEdition: 0,\n    //     pkgOpCode: type === CREATETYPE_CREATE?\"1000\":\n    //                type === CREATETYPE_UPGRADE?\"1001\":\"\",\n    //     packageList: _packageList\n    //   }\n    //   //备注: 前端不再需要调用 create_team接口，这里暂且注释掉 20231025 @garry，这里也是走 创建订单的流程\n    //  /* await team_002_create_team(request).then(result => {\n    //     if(result.resultCode == 200){\n    //       sucessPay(result.teamId)\n    //     }\n    //   });*/\n    // }\n    // setSpining(false);\n  }, 500);\n  const cancelPay = () => {\n    setIsPaying(false);\n  };\n  const sucessPay = _teamId => {\n    dispatch(getTeamList(localTeamId));\n    // 刷新团队列表\n    cancelPay();\n    onCancel();\n    onOk === null || onOk === void 0 ? void 0 : onOk();\n    if (!teamId) {\n      // 弹出跳转弹窗\n      Modal.confirm({\n        title: '提示',\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 15\n        }, this),\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u56E2\\u961F\\u521B\\u5EFA\\u6210\\u529F\\uFF0C\\u662F\\u5426\\u5207\\u6362\\u81F3 \", form.getFieldValue('teamName'), \" \\uFF1F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u70B9\\u51FB\\\"\\u5426\\\"\\uFF0C\\u505C\\u7559\\u5728\\u5F53\\u524D\\u9875\\u9762\\uFF1B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u70B9\\u51FB\\\"\\u662F\\\"\\uFF0C\\u5207\\u6362\\u81F3\\u65B0\\u7684\\u56E2\\u961F\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 18\n        }, this),\n        okText: '是，切换团队',\n        cancelText: '否',\n        onOk: () => {\n          navigate(`/team/${_teamId}`);\n        }\n      });\n    }\n  };\n\n  // 打开优惠码校验框\n  const openDiscountCode = () => {\n    if (discountCodeVisible) return;\n    setDiscountCodeVisible(!discountCodeVisible);\n  };\n\n  // 关闭优惠码校验框\n  const closeDiscountCode = () => {\n    setDiscountCodeVisible(!discountCodeVisible);\n    discountForm.resetFields();\n    discountCodeRef.current = {};\n    //clearDiscountCodeVerification()\n  };\n\n  // 校验优惠码\n  const verificationDiscountCode = (value, event) => {\n    if (event.nativeEvent.type === 'click' && !value) {\n      //if(event.nativeEvent.type === 'click') clearDiscountCodeVerification()\n      if (!value)\n        //globalUtil.warning(\"请填写优惠码\");\n        return;\n    }\n    team_735_bind_user_coupon({\n      couponCode: value\n    }).then(res => {\n      if (res.resultCode == 200) {\n        discountForm.resetFields();\n        discountCodeRef.current = {};\n        if ((priceInfo.payPrice || 0) > 0) {\n          load_team_703_calc_price(!!(couponSelect !== null && couponSelect !== void 0 && couponSelect.couponCode) ? couponSelect.couponCode : value, [], !(couponSelect !== null && couponSelect !== void 0 && couponSelect.couponCode));\n        } else {\n          refetchGetUserValidCoupon();\n        }\n      }\n      if (res.resultCode == 500) {\n        globalUtil.error(res.resultMessage);\n      }\n    });\n  };\n  function warn(discountCode = \"\", dataList = []) {\n    let _packageList = (dataList.length > 0 ? dataList : selectedProductList).filter(data => {\n      if (type == CREATETYPE_CREATE) {\n        return data.memberNo > 0 && !!data.durationMonth;\n      } else {\n        if (!!newBuyFormat(data)) {\n          return data.memberNo > 0 && !!data.durationMonth;\n        }\n        return data.memberNo != 0 || !!data.durationMonth;\n      }\n    }).map(data => ({\n      productId: data.productId,\n      adjustMonthCnt: !!data.durationMonth ? data.durationMonth : 0,\n      adjustAuthCnt: data.memberNo\n    }));\n    if (_packageList.length == 0) {\n      if (!!discountCode) {\n        globalUtil.warning('请选择套餐');\n      }\n      return [];\n    }\n    return _packageList;\n  }\n\n  // 获取支付价格\n  const load_team_703_calc_price = useDebounce((discountCode = \"\", dataList = [], couponUnBindFlg = false) => {\n    let _packageList = warn(discountCode, dataList);\n    if (_packageList.length == 0) {\n      setPriceInfo({\n        originalPrice: 0,\n        regularAmt: 0,\n        promoAmt: 0,\n        discountReduction: 0,\n        payPrice: 0,\n        couponReduction: 0,\n        resultCode: null,\n        resultMessage: null\n      });\n      setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList).map(data => {\n        data.effectBeginDt = '';\n        data.expirationDt = '';\n        data.originalPrice = '';\n        data.discountPrice = '';\n        data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0;\n        return data;\n      })]);\n      return;\n    }\n    let request = {\n      teamId,\n      productList: _packageList,\n      orderType: type == CREATETYPE_UPGRADE ? 2 : 1,\n      couponCode: discountCode\n    };\n    team_703_calc_price(request).then(result => {\n      if (result.resultCode == 200) {\n        const {\n          listingAmt,\n          promoAmt,\n          regularAmt,\n          volumePromoAmt,\n          netAmt,\n          couponAmt,\n          orderProductList,\n          couponList\n        } = result;\n        setPriceInfo({\n          originalPrice: listingAmt,\n          regularAmt: regularAmt,\n          promoAmt: promoAmt,\n          discountReduction: volumePromoAmt,\n          payPrice: netAmt,\n          couponReduction: couponAmt,\n          resultCode: !!discountCode ? result.resultCode : null,\n          resultMessage: !!discountCode ? \"有效优惠码\" : \"\"\n        });\n        if (couponUnBindFlg) {\n          let coupon = (couponList || []).find(_coupon => _coupon.couponCode == discountCode);\n          if (!!coupon && !couponCanUse(coupon)) {\n            setCouponSelect(coupon);\n          }\n        }\n        setMyCouponList([...(couponList || [])]);\n        if ((orderProductList || []).length > 0) {\n          (dataList.length > 0 ? dataList : selectedProductList).forEach(data => {\n            let pkgItem = orderProductList.find(pkg => pkg.productId == data.productId);\n            if (!!pkgItem) {\n              data.effectBeginDt = pkgItem.effectiveDt;\n              data.expirationDt = pkgItem.expirationDt;\n              data.originalPrice = pkgItem.listingAmt;\n              data.discountPrice = pkgItem.amtSubstractPromo;\n              data.nextMemberNo = pkgItem.authCnt;\n            } else {\n              data.effectBeginDt = '';\n              data.expirationDt = '';\n              data.originalPrice = '';\n              data.discountPrice = '';\n              data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0;\n            }\n          });\n          setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList)]);\n        }\n      } else if (result.resultCode == 500) {\n        const {\n          listingAmt = 0,\n          promoAmt = 0,\n          regularAmt = 0,\n          volumePromoAmt = 0,\n          netAmt = 0,\n          couponAmt = 0,\n          orderProductList = [],\n          couponList\n        } = result;\n        setPriceInfo({\n          originalPrice: listingAmt,\n          regularAmt: regularAmt,\n          promoAmt: promoAmt,\n          discountReduction: volumePromoAmt,\n          payPrice: netAmt,\n          couponReduction: couponAmt,\n          resultCode: !!discountCode ? result.resultCode : null,\n          resultMessage: !!discountCode ? result.resultMessage : null\n        });\n        //setMyCouponList([...(couponList||[])]);\n        if ((orderProductList || []).length > 0) {\n          (dataList.length > 0 ? dataList : selectedProductList).forEach(data => {\n            let pkgItem = orderProductList.find(pkg => pkg.productId == data.productId);\n            if (!!pkgItem) {\n              data.effectBeginDt = pkgItem.effectiveDt;\n              data.expirationDt = pkgItem.expirationDt;\n              data.originalPrice = pkgItem.listingAmt;\n              data.discountPrice = pkgItem.amtSubstractPromo;\n              data.nextMemberNo = pkgItem.authCnt;\n            } else {\n              data.effectBeginDt = '';\n              data.expirationDt = '';\n              data.originalPrice = '';\n              data.discountPrice = '';\n              data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0;\n            }\n          });\n          setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList)]);\n        }\n      }\n    });\n  }, 500);\n  const onFieldsChange = args => {\n    // console.log(\"我正在瞬息万变！！！！\", args);\n  };\n  if (team701Result.isLoading || team706Result.isLoading || !!teamId && setting105Result.isLoading) return /*#__PURE__*/_jsxDEV(TLoading, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 600,\n    columnNumber: 109\n  }, this);\n  const userCntHelp = vipFlg === 0 ? `限免基础版(免费)团队${(_team706Result$data = team706Result.data) === null || _team706Result$data === void 0 ? void 0 : _team706Result$data.teamCountFree}个,已使用${(_team706Result$data2 = team706Result.data) === null || _team706Result$data2 === void 0 ? void 0 : _team706Result$data2.enabledTeamCountFree}个。` : \"\";\n\n  // 上传信息获取支付Url\n  const upload_team_704_team_package_pay_order = async (payInfo, discountCode, flag) => {\n    let request = {\n      ...payInfo,\n      teamId,\n      couponCode: discountCode || null\n    };\n    await team_704_submit_order(request).then(result => {\n      if (result.resultCode == 200) {\n        if (flag == 1) {\n          sucessPay(result === null || result === void 0 ? void 0 : result.teamId);\n          return;\n        }\n        const {\n          orderId,\n          alipayUrl,\n          wxpayUrl\n        } = result;\n        load_pay_code_url(alipayUrl, 'zhifubao');\n        load_pay_code_url(wxpayUrl, 'weixin');\n        setPayCode({\n          orderId: orderId\n        });\n        if (flag == 2) {\n          if (isPaying) return;\n          setIsPaying(true);\n        }\n      } else {}\n    });\n  };\n\n  // 生成支付二维码\n  const load_pay_code_url = async (url, payMethod) => {\n    try {\n      if (!url) {\n        console.error('Invalid URL for QR code generation');\n        if (payMethod == 'zhifubao') {\n          setUrl1(null);\n        } else {\n          setUrl(null);\n        }\n        return;\n      }\n      const qrOptions = {\n        errorCorrectionLevel: 'H',\n        margin: 2,\n        width: 120,\n        color: {\n          dark: '#000000',\n          light: '#ffffff'\n        }\n      };\n      const image_url = await QRCode.toDataURL(url, qrOptions);\n      if (payMethod == 'zhifubao') {\n        setUrl1(image_url);\n      } else {\n        setUrl(image_url);\n      }\n    } catch (error) {\n      console.error('Error generating QR code:', error);\n      if (payMethod == 'zhifubao') {\n        setUrl1(null);\n      } else {\n        setUrl(null);\n      }\n    }\n  };\n  const navigateTo = type => {\n    let url = '';\n    if (type == 1) {\n      url = 'personal/personaldata';\n    } else {\n      url = 'personal/invoice';\n    }\n    window.open(`${window.location.origin}/#/${url}`);\n  };\n  function couponSelectChange(coupon) {\n    if (!couponCanUse(coupon)) {\n      if ((couponSelect === null || couponSelect === void 0 ? void 0 : couponSelect.couponCode) == coupon.couponCode) {\n        setCouponSelect(null);\n        load_team_703_calc_price();\n      } else {\n        setCouponSelect(coupon);\n        load_team_703_calc_price(coupon.couponCode);\n      }\n    }\n  }\n  function couponCanUse(coupon) {\n    if (!!coupon.expirationDt) {\n      if (moment(coupon.expirationDt).isBefore(moment())) {\n        return '优惠券已过期';\n      }\n    }\n    if (coupon.orderAmtFlg == 0) {\n      //金额是否适用，0---不适用   1---适用\n      return '订单金额不在适用范围';\n    }\n    if (!!teamId) {\n      //购买应用\n      if (!!coupon.teamId && coupon.teamId != teamId) {\n        //如果指定团队和该团队不匹配，无法使用\n        return `指定团队可用(${coupon.teamId})`;\n      }\n      if (coupon.usedByTeamFlg == 1) {\n        //券有没有被该团队使用过，0---未使用过  1---使用过\n        return '本团队已使用过该券';\n      }\n      //20250401 Jim Song, tmsbug-11993 全已经被使用过，也需要显示为灰色\n      if (coupon.usedByUserFlg == 1) {\n        //有没有被该用户使用过， 0---未使用过  1---使用过\n        return '您已使用过该券';\n      }\n    } else {\n      //创建团队\n      if (!!coupon.teamId) {\n        //是否指定团队使用，不为空即表示指定团队使用，为空表示任意团队都可使用\n        return `指定团队可用(${coupon.teamId})`;\n      }\n      //因为是新建团队，所以不必关注是否被团队使用过\n      if (coupon.usedByUserFlg == 1) {\n        //有没有被该用户使用过， 0---未使用过  1---使用过\n        return '您已使用过该券';\n      }\n    }\n    return '';\n  }\n  function deleteCoupon(coupon) {\n    team_736_unbind_user_coupon({\n      couponId: coupon.couponId\n    }).then(res => {\n      if (res.resultCode == 200) {\n        if ((priceInfo.payPrice || 0) > 0) {\n          if ((couponSelect === null || couponSelect === void 0 ? void 0 : couponSelect.couponCode) == coupon.couponCode) {\n            setCouponSelect(null);\n            load_team_703_calc_price();\n          } else {\n            load_team_703_calc_price(couponSelect === null || couponSelect === void 0 ? void 0 : couponSelect.couponCode);\n          }\n        } else {\n          refetchGetUserValidCoupon();\n        }\n      }\n    });\n    //setMyCouponList([...myCouponList.filter(myCoupon => myCoupon.couponId != coupon.couponId)]);\n  }\n  function leftDaysFormat(date) {\n    let days = moment(moment(date).format('YYYYMMDD')).diff(moment(moment().format('YYYYMMDD')), 'days') - 1;\n    if (days >= 0) {\n      return days;\n    }\n    return -1;\n  }\n  function newBuyFormat(data) {\n    if (data.lastMemberNo <= 0 || !data.lastExpirationDt) {\n      return 1; //新购\n    }\n    if (!!data.lastExpirationDt && leftDaysFormat(data.lastExpirationDt) < 0) {\n      return 2; //过期\n    }\n    return 0; //在有效期内\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Form, {\n      form: form,\n      name: \"basic\",\n      onFinish: _onOk,\n      initialValues: {\n        teamName: \"\",\n        isEnterpriseEdition: (priceData === null || priceData === void 0 ? void 0 : priceData.isEnterpriseEdition) == 1 ? 1 : type === CREATETYPE_CREATE ? 0 : 1\n      },\n      ...formItemLayout,\n      style: {\n        paddingRight: 24,\n        paddingLeft: 24\n      },\n      autoComplete: \"off\",\n      onFieldsChange: onFieldsChange,\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        className: \"team-name-label\",\n        label: \"\\u56E2\\u961F\\u540D\\u79F0\",\n        name: \"teamName\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          className: \"team-name-input\",\n          autoComplete: \"off\",\n          maxLength: 100,\n          disabled: type !== CREATETYPE_CREATE,\n          onChange: teamNameChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 770,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u7248\\u672C\\u9009\\u62E9\",\n        name: \"isEnterpriseEdition\",\n        style: type == CREATETYPE_CREATE ? {\n          marginBottom: 10\n        } : {\n          display: \"none\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n          buttonStyle: \"solid\",\n          children: [/*#__PURE__*/_jsxDEV(Radio.Button, {\n            value: 0,\n            style: {\n              borderTopLeftRadius: 5,\n              borderBottomLeftRadius: 5,\n              width: 120,\n              textAlign: 'center'\n            },\n            children: \"\\u57FA\\u7840\\u7248(\\u514D\\u8D39)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Button, {\n            value: 1,\n            style: {\n              borderTopRightRadius: 5,\n              borderBottomRightRadius: 5,\n              width: 120,\n              textAlign: 'center'\n            },\n            children: \"VIP\\u7248\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 3\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      style: {\n        paddingLeft: 24\n      },\n      name: \"productCheck\",\n      initialValues: {\n        packageList: []\n      },\n      ...formItemLayout,\n      autoComplete: \"off\",\n      children: /*#__PURE__*/_jsxDEV(Form.Item, {\n        className: \"product-selection-formItem\",\n        label: \"\\u9009\\u62E9\\u5E94\\u7528\",\n        name: \"packageList\",\n        style: {\n          marginBottom: 10\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          className: \"before-header\",\n          dataSource: selectedProductList,\n          pagination: false,\n          bordered: true,\n          scroll: {\n            y: `calc(100vh - ${vipFlg == 1 ? 400 : 300}px)`\n          },\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: \"\\u7C7B\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 18\n            }, this),\n            dataIndex: 'groupName',\n            width: vipFlg == 1 ? type == CREATETYPE_CREATE ? 200 : 135 : '19%',\n            render: groupName => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: groupName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 34\n            }, this),\n            onCell: item => {\n              if (item.isRowSpan) {\n                return {\n                  rowSpan: item.prodsLength\n                };\n              } else {\n                return {\n                  rowSpan: 0\n                };\n              }\n            }\n          }, 'groupName', false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            title: '应用',\n            dataIndex: 'productName',\n            width: vipFlg == 1 ? type == CREATETYPE_CREATE ? 220 : 155 : '19%',\n            render: (productName, item) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [productName, (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_2_QA && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#70B603',\n                  fontSize: 12,\n                  marginLeft: 10\n                },\n                children: \"\\u5185\\u6D4B\\u4E2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 66\n              }, this), (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#F59A23',\n                  fontSize: 12,\n                  marginLeft: 10\n                },\n                children: \"\\u5373\\u5C06\\u63A8\\u51FA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 74\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 13\n            }, this),\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'productName', false, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 9\n          }, this), type == CREATETYPE_CREATE ? /*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: vipFlg == 1 ? '授权人数' : '成员数'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 20\n            }, this),\n            dataIndex: 'memberNo',\n            width: vipFlg == 1 ? 150 : '19%',\n            render: (memberNo, item) => {\n              if (vipFlg == 1) {\n                if (item.groupId == eProductGroupId.Pgid_1_OS) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      textAlign: 'center'\n                    },\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 27\n                  }, this);\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `member-edit-primary`,\n                    children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                      controls: false,\n                      style: {\n                        width: 110\n                      },\n                      size: \"small\",\n                      precision: 0,\n                      min: 0,\n                      max: 1000,\n                      disabled: (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased,\n                      addonBefore: /*#__PURE__*/_jsxDEV(Button, {\n                        type: 'primary',\n                        disabled: (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased || (memberNo || 0) == 0,\n                        style: {\n                          borderTopLeftRadius: 5,\n                          borderBottomLeftRadius: 5,\n                          borderRight: '0px',\n                          borderColor: ((item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased || (memberNo || 0) == 0) && '#d9d9d9'\n                        },\n                        onClick: () => memberNoChange(item, 0),\n                        children: \"-\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 860,\n                        columnNumber: 27\n                      }, this),\n                      addonAfter: /*#__PURE__*/_jsxDEV(Button, {\n                        type: 'primary',\n                        disabled: (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased || (memberNo || 0) == {\n                          freeAuthUserQuota1000\n                        },\n                        style: {\n                          borderTopRightRadius: 5,\n                          borderBottomRightRadius: 5,\n                          borderLeft: '0px',\n                          borderColor: ((item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased || (memberNo || 0) == 1000) && '#d9d9d9'\n                        },\n                        onClick: () => memberNoChange(item, 1),\n                        children: \"+\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 870,\n                        columnNumber: 27\n                      }, this),\n                      value: memberNo,\n                      onChange: value => memberNoChangeW(item, value),\n                      onPressEnter: () => memberNoChange(item, 2),\n                      onBlur: () => memberNoChange(item, 2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 851,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 19\n                }, this);\n              } else {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center'\n                  },\n                  children: freeAuthUserQuota1000\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 25\n                }, this);\n              }\n            },\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'memberNo', false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 11\n          }, this) : /*#__PURE__*/_jsxDEV(ColumnGroup, {\n            title: '购买前',\n            className: \"top-header-a\",\n            children: [/*#__PURE__*/_jsxDEV(Column, {\n              title: '授权数',\n              className: \"top-header-a\",\n              dataIndex: 'lastMemberNo',\n              width: 90,\n              render: lastMemberNo => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [lastMemberNo || 0, \"\\u4EBA\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 41\n              }, this),\n              onCell: () => {\n                return {\n                  rowSpan: 1\n                };\n              }\n            }, 'lastMemberNo', false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              title: '对象新建数',\n              className: \"top-header-a\",\n              dataIndex: 'lastResourceNo',\n              width: 150,\n              render: (lastResourceNo, item) => {\n                if (!!item.lastExpirationDt && (item.lastMemberNo || 0) > 0) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: newBuyFormat(item) == 2 ? {\n                      color: '#999'\n                    } : {},\n                    children: \"\\u221E\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 28\n                  }, this);\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: (item === null || item === void 0 ? void 0 : item.freeQuotaDesc) || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 26\n                }, this);\n              },\n              onCell: () => {\n                return {\n                  rowSpan: 1\n                };\n              }\n            }, 'lastResourceNo', false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              title: '有效期至',\n              className: \"top-header-a\",\n              dataIndex: 'lastExpirationDt',\n              width: 100,\n              render: (lastExpirationDt, item) => {\n                if (!!lastExpirationDt && (item.lastMemberNo || 0) > 0) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: newBuyFormat(item) == 2 ? {\n                      color: '#999'\n                    } : {},\n                    children: moment(lastExpirationDt).format('YYYY-MM-DD')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 27\n                  }, this);\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u221E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 25\n                }, this);\n              },\n              onCell: () => {\n                return {\n                  rowSpan: 1\n                };\n              }\n            }, 'lastExpirationDt', false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 11\n          }, this), type == CREATETYPE_CREATE && /*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: \"\\u5BF9\\u8C61\\u65B0\\u5EFA\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 20\n            }, this),\n            dataIndex: 'lastResourceNo',\n            width: vipFlg == 1 ? 200 : '19%',\n            render: (lastResourceNo, item) => {\n              if (vipFlg == 1 && (item.memberNo || 0) > 0) {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center'\n                  },\n                  children: \"\\u221E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 26\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: (item === null || item === void 0 ? void 0 : item.freeQuotaDesc) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 24\n              }, this);\n            },\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'lastResourceNo', false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 11\n          }, this), vipFlg == 1 ? type == CREATETYPE_CREATE ? /*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: \"\\u8D2D\\u4E70\\u65F6\\u957F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 22\n            }, this),\n            dataIndex: 'durationMonth',\n            width: 160,\n            render: (durationMonth, item, index1) => {\n              var _team701Result$data6;\n              if (item.groupId == eProductGroupId.Pgid_1_OS) {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center'\n                  },\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 27\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [\"  \", /*#__PURE__*/_jsxDEV(Dropdown, {\n                  trigger: ['click'],\n                  disabled: (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased,\n                  overlay: /*#__PURE__*/_jsxDEV(Menu, {\n                    children: /*#__PURE__*/_jsxDEV(Menu.Item, {\n                      children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n                        size: \"small\",\n                        buttonStyle: \"solid\",\n                        className: \"create-team-date-item\",\n                        value: item.durationMonth,\n                        children: (((_team701Result$data6 = team701Result.data) === null || _team701Result$data6 === void 0 ? void 0 : _team701Result$data6.monthPromoList) || []).map((item1, index) => /*#__PURE__*/_jsxDEV(Badge, {\n                          size: \"small\",\n                          title: \"\",\n                          className: \"pay-badge\",\n                          count: item1.promoRateDesc || \"\",\n                          offset: [-8, -5],\n                          children: /*#__PURE__*/_jsxDEV(Radio.Button, {\n                            onClick: () => durationMonthChange(item, item1),\n                            value: item1.monthCnt,\n                            children: item1.monthCntDesc\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 969,\n                            columnNumber: 33\n                          }, this)\n                        }, index, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 968,\n                          columnNumber: 31\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 966,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 965,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 964,\n                    columnNumber: 23\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    style: {\n                      color: 'inherit',\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        flex: '0 0 auto',\n                        paddingRight: '16px'\n                      },\n                      children: [newBuyFormat(item) != 2 && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#999',\n                          fontStyle: 'italic'\n                        },\n                        children: !!newBuyFormat(item) ? '' : `剩${leftDaysFormat(item.lastExpirationDt)}天`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 979,\n                        columnNumber: 29\n                      }, this), newBuyFormat(item) == 2 && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: 'red',\n                          fontStyle: 'italic'\n                        },\n                        children: \"\\u5DF2\\u8FC7\\u671F\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 984,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 977,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        flex: '0 0 auto',\n                        paddingLeft: '16px'\n                      },\n                      children: !!durationMonth ? /*#__PURE__*/_jsxDEV(Badge, {\n                        size: \"small\",\n                        title: \"\",\n                        className: \"pay-badge\",\n                        count: item.rebate || \"\",\n                        offset: [-8, -5],\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            height: 24,\n                            width: 60,\n                            display: 'flex',\n                            alignItems: 'center',\n                            fontSize: 12,\n                            color: '#fff',\n                            backgroundColor: '#0077F2',\n                            borderRadius: 5\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              width: 40,\n                              display: 'flex',\n                              justifyContent: 'center'\n                            },\n                            children: item.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 991,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              width: 20,\n                              display: 'flex',\n                              justifyContent: 'center'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(DownOutlined, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 992,\n                              columnNumber: 97\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 992,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 990,\n                          columnNumber: 31\n                        }, this)\n                      }, index1, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 989,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: newBuyFormat(item) == 1 ? '#B3D6FB' : '#F59A23'\n                        },\n                        children: newBuyFormat(item) == 1 ? '选时长' : '续时长'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 996,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 987,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 21\n                }, this), !!durationMonth && /*#__PURE__*/_jsxDEV(Button, {\n                  style: {\n                    borderRadius: '50%',\n                    transition: 'all 0s 0s'\n                  },\n                  size: \"small\",\n                  type: \"text\",\n                  icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {\n                    className: \"fontsize-12\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => durationMonthChange(item)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 19\n              }, this);\n            },\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'durationMonth', false, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(ColumnGroup, {\n            title: '规格选择',\n            className: \"top-header-b\",\n            children: [/*#__PURE__*/_jsxDEV(Column, {\n              title: '增/减员数',\n              className: \"top-header-b\",\n              dataIndex: 'memberNo',\n              width: 160,\n              render: (memberNo, item) => {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `member-edit-primary`,\n                    children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                      controls: false,\n                      style: {\n                        width: 110\n                      },\n                      size: \"small\",\n                      precision: 0,\n                      min: !!newBuyFormat(item) ? 0 : -item.lastMemberNo,\n                      max: 1000,\n                      disabled: (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased,\n                      addonBefore: /*#__PURE__*/_jsxDEV(Button, {\n                        type: 'primary',\n                        disabled: (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased || (!!newBuyFormat(item) ? (memberNo || 0) == 0 : (memberNo || 0) == -item.lastMemberNo),\n                        style: {\n                          borderTopLeftRadius: 5,\n                          borderBottomLeftRadius: 5,\n                          borderRight: '0px',\n                          borderColor: ((item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased || (!!newBuyFormat(item) ? (memberNo || 0) == 0 : (memberNo || 0) == -item.lastMemberNo)) && '#d9d9d9'\n                        },\n                        onClick: () => memberNoChange(item, 0),\n                        children: \"-\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1035,\n                        columnNumber: 29\n                      }, this),\n                      addonAfter: /*#__PURE__*/_jsxDEV(Button, {\n                        type: 'primary',\n                        disabled: (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased || (memberNo || 0) == {\n                          freeAuthUserQuota1000\n                        },\n                        style: {\n                          borderTopRightRadius: 5,\n                          borderBottomRightRadius: 5,\n                          borderLeft: '0px',\n                          borderColor: ((item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased || (memberNo || 0) == {\n                            freeAuthUserQuota1000\n                          }) && '#d9d9d9'\n                        },\n                        onClick: () => memberNoChange(item, 1),\n                        children: \"+\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1050,\n                        columnNumber: 29\n                      }, this),\n                      value: memberNo,\n                      onChange: value => memberNoChangeW(item, value),\n                      onPressEnter: () => memberNoChange(item, 2),\n                      onBlur: () => memberNoChange(item, 2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1026,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 21\n                }, this);\n              },\n              onCell: () => {\n                return {\n                  rowSpan: 1\n                };\n              }\n            }, 'memberNo', false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              title: '购买时长',\n              className: \"top-header-b\",\n              dataIndex: 'durationMonth',\n              width: 160,\n              render: (durationMonth, item, index1) => {\n                var _team701Result$data7;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n                      trigger: ['click'],\n                      disabled: (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased,\n                      overlay: /*#__PURE__*/_jsxDEV(Menu, {\n                        children: /*#__PURE__*/_jsxDEV(Menu.Item, {\n                          children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n                            size: \"small\",\n                            buttonStyle: \"solid\",\n                            className: \"create-team-date-item\",\n                            value: item.durationMonth,\n                            children: (((_team701Result$data7 = team701Result.data) === null || _team701Result$data7 === void 0 ? void 0 : _team701Result$data7.monthPromoList) || []).map((item1, index) => /*#__PURE__*/_jsxDEV(Badge, {\n                              size: \"small\",\n                              title: \"\",\n                              className: \"pay-badge\",\n                              count: item1.promoRateDesc || \"\",\n                              offset: [-8, -5],\n                              children: /*#__PURE__*/_jsxDEV(Radio.Button, {\n                                onClick: () => durationMonthChange(item, item1),\n                                value: item1.monthCnt,\n                                children: item1.monthCntDesc\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1089,\n                                columnNumber: 37\n                              }, this)\n                            }, index, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1088,\n                              columnNumber: 35\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1086,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1085,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1084,\n                        columnNumber: 27\n                      }, this),\n                      children: /*#__PURE__*/_jsxDEV(\"a\", {\n                        style: {\n                          color: 'inherit',\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          alignItems: 'center',\n                          width: '100%'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            flex: '0 0 auto',\n                            paddingRight: '16px'\n                          },\n                          children: [newBuyFormat(item) != 2 && /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: '#999',\n                              fontStyle: 'italic'\n                            },\n                            children: !!newBuyFormat(item) ? '' : `剩${leftDaysFormat(item.lastExpirationDt)}天`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1099,\n                            columnNumber: 33\n                          }, this), newBuyFormat(item) == 2 && /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: 'red',\n                              fontStyle: 'italic'\n                            },\n                            children: \"\\u5DF2\\u8FC7\\u671F\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1104,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1097,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            flex: '0 0 auto',\n                            paddingLeft: '16px'\n                          },\n                          children: !!durationMonth ? /*#__PURE__*/_jsxDEV(Badge, {\n                            size: \"small\",\n                            title: \"\",\n                            className: \"pay-badge\",\n                            count: item.rebate || \"\",\n                            offset: [-8, -5],\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                height: 24,\n                                width: 60,\n                                display: 'flex',\n                                alignItems: 'center',\n                                fontSize: 12,\n                                color: '#fff',\n                                backgroundColor: '#0077F2',\n                                borderRadius: 5\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  width: 40,\n                                  display: 'flex',\n                                  justifyContent: 'center'\n                                },\n                                children: item.name\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1111,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  width: 20,\n                                  display: 'flex',\n                                  justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(DownOutlined, {}, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1112,\n                                  columnNumber: 101\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1112,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1110,\n                              columnNumber: 35\n                            }, this)\n                          }, index1, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1109,\n                            columnNumber: 33\n                          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              color: newBuyFormat(item) == 1 ? '#B3D6FB' : '#F59A23'\n                            },\n                            children: newBuyFormat(item) == 1 ? '选时长' : '续时长'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1116,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1107,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1096,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1083,\n                      columnNumber: 25\n                    }, this), !!durationMonth && /*#__PURE__*/_jsxDEV(Button, {\n                      style: {\n                        borderRadius: '50%',\n                        transition: 'all 0s 0s'\n                      },\n                      size: \"small\",\n                      type: \"text\",\n                      icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {\n                        className: \"fontsize-12\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1126,\n                        columnNumber: 35\n                      }, this),\n                      onClick: () => durationMonthChange(item)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1122,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1082,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 21\n                }, this);\n              },\n              onCell: () => {\n                return {\n                  rowSpan: 1\n                };\n              }\n            }, 'durationMonth', false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 13\n          }, this) : null, type == CREATETYPE_CREATE ? vipFlg == 1 ? /*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: \"\\u751F\\u6548\\u5F00\\u59CB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1143,\n              columnNumber: 22\n            }, this),\n            dataIndex: 'effectBeginDt',\n            width: 120,\n            render: (effectBeginDt, item) => {\n              if (item.groupId == eProductGroupId.Pgid_1_OS) {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center'\n                  },\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1149,\n                  columnNumber: 27\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: !!effectBeginDt ? moment(effectBeginDt).format('YYYY-MM-DD') : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 25\n              }, this);\n            },\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'effectBeginDt', false, {\n            fileName: _jsxFileName,\n            lineNumber: 1142,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: \"\\u6709\\u6548\\u671F\\u81F3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1157,\n              columnNumber: 22\n            }, this),\n            dataIndex: 'expirationDt',\n            width: '19%',\n            render: () => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: \"\\u221E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1161,\n              columnNumber: 28\n            }, this),\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'expirationDt', false, {\n            fileName: _jsxFileName,\n            lineNumber: 1156,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(ColumnGroup, {\n            title: '预览',\n            className: \"top-header-c\",\n            children: [/*#__PURE__*/_jsxDEV(Column, {\n              title: '授权数',\n              className: \"top-header-c\",\n              dataIndex: 'nextMemberNo',\n              width: 90,\n              render: (nextMemberNo, item) => {\n                if (!!newBuyFormat(item) && !item.durationMonth && item.memberNo <= 0) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [item.memberNo, \"\\u4EBA\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1175,\n                    columnNumber: 27\n                  }, this);\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [nextMemberNo || 0, \"\\u4EBA\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 25\n                }, this);\n              },\n              onCell: () => {\n                return {\n                  rowSpan: 1\n                };\n              }\n            }, 'nextMemberNo', false, {\n              fileName: _jsxFileName,\n              lineNumber: 1167,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              title: '对象新建数',\n              className: \"top-header-c\",\n              dataIndex: 'nextResourceNo',\n              width: 150,\n              render: (nextResourceNo, item) => {\n                if (!newBuyFormat(item)) {\n                  if (item.memberNo < 0 && item.memberNo + item.lastMemberNo == 0) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: (item === null || item === void 0 ? void 0 : item.freeQuotaDesc) || '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1190,\n                      columnNumber: 30\n                    }, this);\n                  }\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"\\u221E\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1192,\n                    columnNumber: 28\n                  }, this);\n                }\n                if ((item.memberNo || 0) > 0 && !!item.durationMonth) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"\\u221E\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1195,\n                    columnNumber: 28\n                  }, this);\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: (item === null || item === void 0 ? void 0 : item.freeQuotaDesc) || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1197,\n                  columnNumber: 26\n                }, this);\n              },\n              onCell: () => {\n                return {\n                  rowSpan: 1\n                };\n              }\n            }, 'nextResourceNo', false, {\n              fileName: _jsxFileName,\n              lineNumber: 1181,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              title: '有效期至',\n              className: \"top-header-c\",\n              dataIndex: 'expirationDt',\n              width: 90,\n              render: (expirationDt, item) => {\n                if (!newBuyFormat(item)) {\n                  if (!!expirationDt) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: moment(expirationDt).format('YYYY-MM-DD')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1210,\n                      columnNumber: 29\n                    }, this);\n                  }\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: moment(item.lastExpirationDt).format('YYYY-MM-DD')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1212,\n                    columnNumber: 27\n                  }, this);\n                }\n                if ((item.memberNo || 0) > 0 && !!item.durationMonth) {\n                  if (!!expirationDt) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: moment(expirationDt).format('YYYY-MM-DD')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1216,\n                      columnNumber: 29\n                    }, this);\n                  }\n                  return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u221E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1220,\n                  columnNumber: 25\n                }, this);\n              },\n              onCell: () => {\n                return {\n                  rowSpan: 1\n                };\n              }\n            }, 'expirationDt', false, {\n              fileName: _jsxFileName,\n              lineNumber: 1201,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1166,\n            columnNumber: 11\n          }, this), vipFlg == 1 && type == CREATETYPE_CREATE ? /*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: \"\\u751F\\u6548\\u7ED3\\u675F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1228,\n              columnNumber: 20\n            }, this),\n            dataIndex: 'expirationDt',\n            width: 120,\n            render: expirationDt => {\n              if (!!expirationDt) {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center'\n                  },\n                  children: !!expirationDt ? moment(expirationDt).format('YYYY-MM-DD') : ''\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1235,\n                  columnNumber: 19\n                }, this);\n              } else {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center'\n                  },\n                  children: \"\\u221E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1238,\n                  columnNumber: 25\n                }, this);\n              }\n            },\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'expirationDt', false, {\n            fileName: _jsxFileName,\n            lineNumber: 1227,\n            columnNumber: 11\n          }, this) : null, vipFlg == 1 && /*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: \"\\u5355\\u4EF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1248,\n              columnNumber: 20\n            }, this),\n            dataIndex: 'discountPrice',\n            width: 120,\n            render: (discountPrice, item) => {\n              if (item.groupId == eProductGroupId.Pgid_1_OS) {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center'\n                  },\n                  children: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1254,\n                  columnNumber: 25\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#999',\n                    width: 40,\n                    textDecoration: 'line-through'\n                  },\n                  children: [\"\\xA5\", (item === null || item === void 0 ? void 0 : item.listingUnitAmt) || '0']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1258,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\xA5\", (item === null || item === void 0 ? void 0 : item.regularUnitAmt) || '0', \"/\\u4EBA/\\u6708\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1259,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1257,\n                columnNumber: 17\n              }, this);\n            },\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'discountPrice', false, {\n            fileName: _jsxFileName,\n            lineNumber: 1247,\n            columnNumber: 11\n          }, this), vipFlg == 1 && /*#__PURE__*/_jsxDEV(Column, {\n            title: '小计',\n            dataIndex: 'subtotal',\n            render: (subtotal, item) => {\n              if (item.groupId == eProductGroupId.Pgid_1_OS) {\n                return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1273,\n                  columnNumber: 25\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#999',\n                    marginRight: 10,\n                    textDecoration: 'line-through',\n                    flex: 1\n                  },\n                  children: [!!item.originalPrice ? '¥' : '', item.originalPrice]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    flex: 1\n                  },\n                  children: [!!item.discountPrice ? '¥' : '', item.discountPrice]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 17\n              }, this);\n            },\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'subtotal', false, {\n            fileName: _jsxFileName,\n            lineNumber: 1267,\n            columnNumber: 11\n          }, this), vipFlg == 1 ? null : type == CREATETYPE_CREATE ? /*#__PURE__*/_jsxDEV(Column, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: \"\\u52FE\\u9009\\u4F7F\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1287,\n              columnNumber: 20\n            }, this),\n            dataIndex: 'productId',\n            width: '5%',\n            render: (productId, item) => {\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  disabled: productId == eProductId.Pid_11_Explorer || productId == eProductId.Pid_12_Space || (item === null || item === void 0 ? void 0 : item.statusType) == eProductStatus.Status_3_Unreleased,\n                  checked: item.checked,\n                  onChange: e => checkBoxChange(e, productId)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1293,\n                columnNumber: 17\n              }, this);\n            },\n            onCell: () => {\n              return {\n                rowSpan: 1\n              };\n            }\n          }, 'productId', false, {\n            fileName: _jsxFileName,\n            lineNumber: 1286,\n            columnNumber: 11\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 787,\n      columnNumber: 3\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: 220\n      },\n      children: [vipFlg == 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"price-bottom\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"price-bottom-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginRight: 50\n              },\n              children: [\"\\u9009\\u62E9\\u4F18\\u60E0\\u5238(\", myCouponList.length, \")\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u9AD8\\u4EAE\\u663E\\u793A\\u7684\\u4F18\\u60E0\\u5238\\u53EF\\u7528\\uFF0C\\u7070\\u663E\\u7684\\u4F18\\u60E0\\u5238\\u4E0D\\u53EF\\u7528\",\n                placement: \"right\",\n                children: /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {\n                  style: {\n                    color: '#f59a23',\n                    marginLeft: 5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1316,\n                  columnNumber: 99\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1316,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1315,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              className: refreshingFlg && 'refresh-icon',\n              style: {\n                position: 'relative',\n                color: '#666'\n              },\n              type: \"link\",\n              icon: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"refresh-position fontsize-14 iconfont shuaxin1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 21\n              }, this),\n              onClick: () => {\n                setRefreshingFlg(true);\n                setTimeout(() => {\n                  if ((priceInfo.payPrice || 0) > 0) {\n                    load_team_703_calc_price(couponSelect === null || couponSelect === void 0 ? void 0 : couponSelect.couponCode);\n                  } else {\n                    refetchGetUserValidCoupon();\n                  }\n                  setRefreshingFlg(false);\n                }, 500);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1318,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginRight: 50\n              },\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1335,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              colon: false,\n              form: discountForm,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                style: {\n                  margin: 0\n                },\n                label: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#0077f2',\n                    cursor: 'pointer'\n                  },\n                  onClick: openDiscountCode,\n                  children: \"\\u6211\\u6709\\u4F18\\u60E0\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1339,\n                  columnNumber: 22\n                }, this)\n                // validateStatus={priceInfo.resultCode === 200 ? \"success\" : (priceInfo.resultCode === 500 ? \"error\" : null)}\n                ,\n                children: [discountCodeVisible && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"code\",\n                  noStyle: true,\n                  children: /*#__PURE__*/_jsxDEV(Search, {\n                    className: \"discount-code discount-code-visible\",\n                    size: \"small\",\n                    maxLength: 12,\n                    allowClear: true,\n                    autoComplete: \"off\",\n                    placeholder: \"\\u8BF7\\u586B\\u5199\\u4F18\\u60E0\\u7801\",\n                    enterButton: \"\\u6821\\u9A8C\",\n                    onChange: e => {\n                      discountCodeRef.current.code = e.target.value;\n                    },\n                    onSearch: verificationDiscountCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1345,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1344,\n                  columnNumber: 17\n                }, this), discountCodeVisible && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  noStyle: true,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    style: {\n                      borderRadius: '50%',\n                      transition: 'all 0s 0s'\n                    },\n                    size: \"small\",\n                    type: \"text\",\n                    icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {\n                      className: \"fontsize-12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1364,\n                      columnNumber: 27\n                    }, this),\n                    onClick: closeDiscountCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1360,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1359,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1337,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1336,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1314,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-column-parent\",\n            style: {\n              height: 110,\n              width: '90%',\n              border: '1px solid #f0f0f0',\n              borderRadius: 5\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '100%',\n                padding: '5px 0px 5px 5px'\n              },\n              className: \"flex-column-child section\",\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                children: myCouponList.map(coupon => {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      position: 'relative',\n                      height: 105\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"text\",\n                      title: !couponCanUse(coupon) ? `优惠码：${coupon.couponCode}` : `优惠码：${coupon.couponCode}\\n不可用原因：${couponCanUse(coupon)}`,\n                      onClick: () => couponSelectChange(coupon),\n                      style: {\n                        padding: 0\n                      },\n                      disabled: !!couponCanUse(coupon),\n                      children: /*#__PURE__*/_jsxDEV(Card, {\n                        style: {\n                          backgroundColor: !couponCanUse(coupon) ? '#E6F2FE' : '#f0f0f0'\n                        },\n                        className: ((couponSelect === null || couponSelect === void 0 ? void 0 : couponSelect.couponCode) == coupon.couponCode ? \"CouponCard-select\" : '') + \" CouponCard\",\n                        hoverable: false,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '100%',\n                            height: 70,\n                            backgroundColor: !couponCanUse(coupon) ? '#0077F2' : '#999',\n                            borderTopLeftRadius: 5,\n                            borderTopRightRadius: 5\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: 'flex',\n                              alignItems: 'center',\n                              height: '100%',\n                              marginLeft: 10\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                color: '#fff'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                style: {\n                                  display: 'flex',\n                                  alignItems: 'center'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  style: {\n                                    fontSize: 18,\n                                    marginRight: 20\n                                  },\n                                  children: coupon.couponType == eCouponType.Type_2_AmountOff ? '减￥' + (coupon.couponDiscount || '') : (coupon.couponDiscount || '') + '折'\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1405,\n                                  columnNumber: 35\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  children: [\"\\u6EE1  \\uFFE5\", coupon.minOrderAmt]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1408,\n                                  columnNumber: 35\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1404,\n                                columnNumber: 33\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                children: [\"\\u6709\\u6548\\u671F\\u81F3\", moment(coupon.expirationDt).format('YYYY-MM-DD')]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1410,\n                                columnNumber: 33\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1403,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1402,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1393,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '100%',\n                            height: 30\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: 'flex',\n                              alignItems: 'center',\n                              height: 30,\n                              marginLeft: 10,\n                              color: !couponCanUse(coupon) ? '#0077F2' : '#999'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: coupon.couponName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1416,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1415,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1414,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1388,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1384,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n                      title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8BE5\\u4F18\\u60E0\\u5238?\",\n                      onConfirm: () => deleteCoupon(coupon),\n                      children: /*#__PURE__*/_jsxDEV(Button, {\n                        style: {\n                          borderRadius: '50%',\n                          transition: 'all 0s 0s',\n                          width: 14,\n                          height: 14,\n                          position: 'absolute',\n                          right: 0\n                        },\n                        size: \"small\",\n                        icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {\n                          style: {\n                            fontSize: 10\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1425,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1422,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1421,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1383,\n                    columnNumber: 21\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1379,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1378,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1313,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"price-bottom-right\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price-bottom-detailed\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"price-bottom-detailed-descriptions\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-bottom-detailed-descriptions-li\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-label\",\n                  children: \"\\u539F\\u4EF7:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1439,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-value\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      textDecoration: 'line-through',\n                      color: '#999',\n                      marginRight: 10\n                    },\n                    children: [\"\\uFFE5\", priceInfo.originalPrice || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1440,\n                    columnNumber: 44\n                  }, this), (priceInfo.promoAmt || 0) > 0 ? `(立减-￥${priceInfo.promoAmt})` : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1440,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1438,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-bottom-detailed-descriptions-li\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-label\",\n                  children: \"\\u73B0\\u4EF7:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1445,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-value\",\n                  children: [\"\\uFFE5\", priceInfo.regularAmt || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1446,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1444,\n                columnNumber: 15\n              }, this), (priceInfo.discountReduction || 0) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-bottom-detailed-descriptions-li\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-label\",\n                  children: \"\\u6298\\u6263\\u51CF:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1450,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-value\",\n                  children: [\"-\\uFFE5\", priceInfo.discountReduction]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1451,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1449,\n                columnNumber: 15\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1454,\n                columnNumber: 15\n              }, this), !!couponSelect ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-bottom-detailed-descriptions-li\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-label\",\n                  style: {\n                    color: priceInfo.couponReduction == 0 ? '#999' : '#333'\n                  },\n                  children: \"\\u4F18\\u60E0\\u5238:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-value\",\n                  style: {\n                    color: priceInfo.couponReduction == 0 ? '#999' : '#333'\n                  },\n                  children: [\"-\\uFFE5\", priceInfo.couponReduction, /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"coupon\",\n                    style: {\n                      backgroundColor: priceInfo.couponReduction == 0 ? '#999' : '#0077F2'\n                    },\n                    children: [\"\\u6EE1\\uFFE5\", (_myCouponList$find = myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)) === null || _myCouponList$find === void 0 ? void 0 : _myCouponList$find.minOrderAmt, ((_myCouponList$find2 = myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)) === null || _myCouponList$find2 === void 0 ? void 0 : _myCouponList$find2.couponType) == eCouponType.Type_2_AmountOff ? '减￥' : '享', (_myCouponList$find3 = myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)) === null || _myCouponList$find3 === void 0 ? void 0 : _myCouponList$find3.couponDiscount, ((_myCouponList$find4 = myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)) === null || _myCouponList$find4 === void 0 ? void 0 : _myCouponList$find4.couponType) == eCouponType.Type_2_AmountOff ? '' : '折', /*#__PURE__*/_jsxDEV(\"a\", {\n                      className: \"delete\",\n                      style: {\n                        color: priceInfo.couponReduction == 0 ? '#999' : '#0077F2'\n                      },\n                      onClick: () => {\n                        setCouponSelect(null);\n                        load_team_703_calc_price();\n                      },\n                      children: /*#__PURE__*/_jsxDEV(CloseOutlined, {\n                        style: {\n                          fontSize: 10\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1471,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1466,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1461,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1459,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1457,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-bottom-detailed-descriptions-li\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-label\",\n                  children: \"\\u5408\\u8BA1:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1481,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"li-value\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: 12,\n                      display: 'flex',\n                      alignItems: 'baseline',\n                      justifyContent: 'end'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#f59a23'\n                      },\n                      children: \"\\uFFE5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1484,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"shifu-rate\",\n                      style: {\n                        color: '#f59a23'\n                      },\n                      children: priceInfo.payPrice || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1485,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1483,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1482,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1480,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1437,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1436,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1435,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1312,\n        columnNumber: 7\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: 40\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1495,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: \"flex-end\",\n          justifyContent: 'space-between',\n          paddingLeft: 14\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [vipFlg == 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: 10,\n              height: 20,\n              fontSize: 12,\n              color: '#999'\n            },\n            children: [\"\\u5907\\u6CE81\\uFF1A\", userCntHelp]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1500,\n            columnNumber: 11\n          }, this), vipFlg == 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: 10,\n              height: 20,\n              fontSize: 12,\n              color: '#999'\n            },\n            children: \"\\u5907\\u6CE81\\uFF1Avip\\u4EBA\\u6570\\u4E3A0\\u7684\\u5E94\\u7528\\uFF0C\\u60A8\\u5C06\\u7EE7\\u7EED\\u4EAB\\u6709\\u57FA\\u7840\\u7248\\u7684\\u529F\\u80FD\\u514D\\u8D39\\u957F\\u671F\\u4F7F\\u7528\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1503,\n            columnNumber: 11\n          }, this), type == CREATETYPE_CREATE ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: 10,\n              height: 20,\n              fontSize: 12,\n              color: '#999'\n            },\n            children: [\"\\u5907\\u6CE82\\uFF1A\\u60A8\\u4E0D\\u9700\\u8981\\u7684\\u529F\\u80FD\\uFF0C\\u5728\\u56E2\\u961F\\u521B\\u5EFA\\u6210\\u529F\\u540E\\uFF0C\\u53EF\\u5728 \\u8BBE\\u7F6E \", '->', \" \\u5E94\\u7528\\u7BA1\\u7406 \\u9875\\u9762\\u4E2D\\u7981\\u7528(\\u6216\\u518D\\u6B21\\u5F00\\u542F)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1506,\n            columnNumber: 11\n          }, this) : null, type == CREATETYPE_CREATE && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: 10,\n              height: 20,\n              fontSize: 12,\n              color: '#999'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#fff'\n              },\n              children: \"\\u5907\\u6CE82\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1521,\n              columnNumber: 13\n            }, this), \"\\u70B9\\u51FB \\u5E2E\\u52A9\\u4E2D\\u5FC3 \", '->', \" \\u5E94\\u7528\\u7BA1\\u7406 \\u94FE\\u63A5\\uFF0C\\u53EF\\u5B9A\\u4F4D\\u5230 \\u5B98\\u7F51\\u5E2E\\u52A9\\u9875\\u7684 \\u5173\\u4E8E\\u5E94\\u7528\\u7BA1\\u7406\\u7684\\u5E2E\\u52A9\\u6587\\u6863\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1520,\n            columnNumber: 11\n          }, this), type == CREATETYPE_UPGRADE && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: 10,\n              height: 20,\n              fontSize: 12,\n              color: '#999'\n            },\n            children: [\"\\u5907\\u6CE82\\uFF1A\\u53EF\\u5728 \\u8BBE\\u7F6E \", '->', \" \\u5E94\\u7528\\u7BA1\\u7406 \\u4E2D\\u8FDB\\u884C\\u5E94\\u7528\\u542F\\u7528/\\u7981\\u7528\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1525,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1498,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            paddingRight: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right'\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              className: vipFlg == 1 ? \"purchase-btn\" : \"found-btn\",\n              type: \"primary\",\n              loading: spining,\n              style: {\n                minWidth: 140,\n                height: 34,\n                borderRadius: 5\n              },\n              onClick: _onOk,\n              children: vipFlg == 1 ? \"立即购买\" : `创建(已选${selectedProductList.filter(product => product.checked && product.productId != eProductId.Pid_11_Explorer && product.productId != eProductId.Pid_12_Space).length}个应用)`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1539,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1538,\n            columnNumber: 9\n          }, this), vipFlg == 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right',\n              fontSize: 12,\n              color: '#999',\n              marginTop: 5\n            },\n            children: [\"\\u652F\\u4ED8\\u6210\\u529F\\u540E\\uFF0C\\u53EF\\u524D\\u5F80 \", /*#__PURE__*/_jsxDEV(\"a\", {\n              style: {\n                color: '#0077F2'\n              },\n              onClick: () => navigateTo(1),\n              children: \"\\u4E2A\\u4EBA\\u4E2D\\u5FC3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1551,\n              columnNumber: 23\n            }, this), \" \", '->', \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n              style: {\n                color: '#0077F2'\n              },\n              onClick: () => navigateTo(2),\n              children: \"\\u53D1\\u7968\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1551,\n              columnNumber: 96\n            }, this), \" \\u8FDB\\u884C\\u5F00\\u7968\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1550,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1537,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1497,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1310,\n      columnNumber: 3\n    }, this), /*#__PURE__*/_jsxDEV(PayCreateTeamModal, {\n      teamId: teamId,\n      visible: isPaying,\n      onCancel: cancelPay,\n      priceInfo: priceInfo,\n      onOk: sucessPay,\n      payCode: payCode,\n      url: url,\n      url1: url1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1557,\n      columnNumber: 3\n    }, this), /*#__PURE__*/_jsxDEV(PriceDetailModal, {\n      visible: showPriceDetail,\n      onCancel: () => setShowPriceDetail(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1566,\n      columnNumber: 3\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      title: \"\\u63D0\\u793A\",\n      className: \"tms-modal\",\n      width: 420,\n      open: tipShow,\n      onCancel: () => setTipShow(false),\n      centered: true,\n      maskClosable: false,\n      footer: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: 'primary',\n          style: {\n            borderRadius: 5\n          },\n          onClick: () => setTipShow(false),\n          children: \"\\u6211\\u77E5\\u9053\\u4E86\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1579,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1578,\n        columnNumber: 7\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          marginTop: 20\n        },\n        children: \"\\u56E0\\u51CF\\u5458\\u7B49\\u56E0\\u7D20\\uFF0C\\u8BA2\\u5355\\u91D1\\u989D\\uFF1C0\\uFF0C\\u60A8\\u53EF\\u7EE7\\u7EED\\u589E\\u8D2D\\u5E94\\u7528\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1583,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          marginBottom: 20\n        },\n        children: \"\\u6216\\u5EF6\\u7EED\\u65F6\\u957F\\uFF0C\\u786E\\u4FDD\\u8BA2\\u5355\\u91D1\\u989D\\u22650\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1584,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1569,\n      columnNumber: 3\n    }, this)]\n  }, void 0, true);\n}\n_s(CreateTeam, \"4DqQRU8edwuM//1kc1R/2Tbhpsc=\", false, function () {\n  return [useParams, useQueries, useQueryGetUserValidCoupon, useNavigate, useDispatch, Form.useForm, Form.useForm, Form.useWatch, useDebounce, useDebounce];\n});\n_c = CreateTeam;\nfunction PayCreateTeam({\n  onOk,\n  teamId,\n  priceInfo,\n  payCode,\n  url,\n  url1\n}) {\n  _s2();\n  const [form] = Form.useForm();\n  const payMethod = Form.useWatch('payMethod', form);\n  //const [refreshingFlg,setRefreshingFlg] = useState(false);\n\n  useEffect(() => {\n    const timer = !(payCode && payCode !== null && payCode !== void 0 && payCode.orderId) || setInterval(() => {\n      let params = {\n        orderId: payCode.orderId\n      };\n      if (!!teamId) {\n        params.teamId = teamId;\n      }\n      team_705_get_order_status(params).then(result => {\n        if (result.resultCode == 200) {\n          if (result.statusType == eOrderStatus.Status_1_Paid) {\n            clearInterval(timer);\n            onOk && onOk(result.teamId);\n            globalUtil.success('购买成功');\n          }\n        }\n      });\n    }, 1000);\n    return () => {\n      clearInterval(timer);\n    };\n  }, [payCode]);\n  useEffect(() => {\n    if (payMethod) {}\n  }, [payMethod]);\n  return /*#__PURE__*/_jsxDEV(Form, {\n    className: \"PayCreateTeam-form\",\n    form: form,\n    name: \"basic\"\n    // {...formItemLayout}\n    ,\n    autoComplete: \"off\",\n    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n      noStyle: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pay-images\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              width: 24,\n              height: 24,\n              src: require(\"@assets/images/createTeam/wxpay.png\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1634,\n              columnNumber: 13\n            }, this), \"\\u5FAE\\u4FE1\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1633,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-content\",\n            style: {\n              backgroundColor: '#04C161'\n            },\n            children: url ? /*#__PURE__*/_jsxDEV(Image, {\n              src: url,\n              width: 120,\n              height: 120,\n              preview: false,\n              placeholde: /*#__PURE__*/_jsxDEV(TLoading, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1643,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1638,\n              columnNumber: 16\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invalid_QR_code\",\n              children: \"\\u4E8C\\u7EF4\\u7801\\u65E0\\u6548\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1645,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1636,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1632,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              width: 24,\n              height: 24,\n              src: require(\"@assets/images/createTeam/alipay.png\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1651,\n              columnNumber: 13\n            }, this), \"\\u652F\\u4ED8\\u5B9D\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1650,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-content\",\n            style: {\n              backgroundColor: '#1273FF'\n            },\n            children: url1 ? /*#__PURE__*/_jsxDEV(Image, {\n              src: url1,\n              width: 120,\n              height: 120,\n              preview: false,\n              placeholde: /*#__PURE__*/_jsxDEV(TLoading, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1660,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1655,\n              columnNumber: 16\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invalid_QR_code\",\n              children: \"\\u4E8C\\u7EF4\\u7801\\u65E0\\u6548\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1662,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1653,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1649,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1631,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pay-tips\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"create-team-price-color\",\n          style: {\n            marginLeft: 10,\n            marginRight: 10\n          },\n          children: \"\\uFFE5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1668,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ant-form-text create-team-discountPrice create-team-price-color\",\n          children: priceInfo.payPrice\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1669,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1667,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1628,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1621,\n    columnNumber: 3\n  }, this);\n}\n_s2(PayCreateTeam, \"lJitMVNKO4G2T1QX69b5f3uJG20=\", false, function () {\n  return [Form.useForm, Form.useWatch];\n});\n_c2 = PayCreateTeam;\nfunction PayCreateTeamModal({\n  teamId,\n  visible = false,\n  onCancel,\n  priceInfo,\n  onOk,\n  payCode,\n  url,\n  url1\n}) {\n  return /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n    className: \"createTeamDraModal\",\n    title: /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [\"\\u626B\\u7801\\u652F\\u4ED8\", /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"fontsize-12\",\n        style: {\n          marginLeft: 10,\n          color: '#999'\n        },\n        children: [\"\\u6D41\\u6C34\\u7F16\\u53F7\\uFF1A\", (payCode === null || payCode === void 0 ? void 0 : payCode.orderId) || '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1682,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1680,\n      columnNumber: 7\n    }, this),\n    width: 520,\n    open: visible,\n    onCancel: onCancel,\n    centered: true,\n    footer: null,\n    maskClosable: false,\n    destroyOnClose: true,\n    children: /*#__PURE__*/_jsxDEV(PayCreateTeam, {\n      priceInfo: priceInfo,\n      teamId: teamId,\n      onOk: onOk,\n      payCode: payCode,\n      url: url,\n      url1: url1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1694,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1677,\n    columnNumber: 10\n  }, this);\n}\n_c3 = PayCreateTeamModal;\nfunction PriceDetailModal({\n  visible = false,\n  onCancel\n}) {\n  return /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n    className: \"createTeamDraModal\",\n    title: \"\\u4EF7\\u683C\\u8BE6\\u60C5\",\n    width: 800,\n    open: visible,\n    onCancel: onCancel,\n    centered: true,\n    footer: null,\n    maskClosable: false,\n    destroyOnClose: true,\n    children: /*#__PURE__*/_jsxDEV(PriceDetailTable, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1715,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1705,\n    columnNumber: 10\n  }, this);\n}\n_c4 = PriceDetailModal;\nfunction PriceDetailTable({}) {\n  _s3();\n  const [dataSource, setDataSource] = useState([]);\n  const columns = [{\n    title: '应用',\n    dataIndex: 'productName',\n    key: 'productName'\n  }, {\n    title: '原价',\n    dataIndex: 'oldPrice',\n    key: 'oldPrice'\n  }, {\n    title: '现价',\n    dataIndex: 'nowPrice',\n    key: 'nowPrice'\n  }, {\n    title: '购买日期',\n    dataIndex: 'buyDate',\n    key: 'buyDate'\n  }, {\n    title: '有效期至',\n    dataIndex: 'expiration',\n    key: 'expiration'\n  }, {\n    title: '剩余时长(天)',\n    dataIndex: 'remainingDuration',\n    key: 'remainingDuration'\n  }, {\n    title: '原价：扩容单人',\n    dataIndex: 'oldPersonalPrice',\n    key: 'oldPersonalPrice'\n  }, {\n    title: '现价：扩容单人',\n    dataIndex: 'nowPersonalPrice',\n    key: 'nowPersonalPrice'\n  }, {\n    title: '折扣减',\n    dataIndex: 'rate',\n    key: 'rate'\n  }];\n  return /*#__PURE__*/_jsxDEV(Table, {\n    size: \"small\",\n    columns: columns,\n    dataSource: dataSource,\n    pagination: {\n      position: ['bottomCenter'],\n      size: 'small',\n      showSizeChanger: true,\n      showQuickJumper: true,\n      total: dataSource.length,\n      showTotal: total => {\n        return `共${total}条`;\n      }\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1734,\n    columnNumber: 5\n  }, this);\n}\n_s3(PriceDetailTable, \"Y0HlLXWRZlbLxTf5Li4KnA40930=\");\n_c5 = PriceDetailTable;\nexport default function CreateTeamModal({\n  visible = false,\n  onCancel,\n  teamId,\n  onOk,\n  type = CREATETYPE_CREATE,\n  priceData,\n  productList\n}) {\n  _s4();\n  const [isChange, setIsChange] = useState(false);\n  // 新增：动态设置浏览器标题\n  const prevTitle = useRef(document.title);\n  useEffect(() => {\n    if (visible) {\n      prevTitle.current = document.title;\n      document.title = type === CREATETYPE_UPGRADE ? \"应用管理-购买\" : \"新建团队\";\n    } else {\n      document.title = prevTitle.current;\n    }\n    return () => {\n      document.title = prevTitle.current;\n    };\n  }, [visible, type]);\n  function checkClose() {\n    if (isChange) {\n      Modal.confirm({\n        title: '提示',\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n          style: {\n            color: 'orange'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1768,\n          columnNumber: 15\n        }, this),\n        centered: true,\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u6B63\\u5728\", `${type == CREATETYPE_CREATE ? '创建团队' : '购买应用'}`, \"\\uFF0C\\u662F\\u5426\\u653E\\u5F03\", `${type == CREATETYPE_CREATE ? '创建' : '购买'}`, \" \\uFF1F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1771,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1770,\n          columnNumber: 18\n        }, this),\n        okText: '是',\n        cancelText: '否',\n        onOk: () => {\n          onCancel();\n          setIsChange(false);\n        }\n      });\n    } else {\n      onCancel();\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n    className: \"tms-drawer createTeamDrawer\",\n    centered: true,\n    title: type === CREATETYPE_UPGRADE ? \"应用管理-购买\" : \"新建团队\",\n    width: '60%',\n    minWidth: '50%',\n    maxWidth: '95%',\n    open: visible,\n    onClose: checkClose,\n    footer: null,\n    destroyOnClose: true,\n    children: /*#__PURE__*/_jsxDEV(CreateTeam, {\n      teamId: teamId,\n      onCancel: onCancel,\n      onOk: onOk,\n      type: type,\n      priceData: priceData,\n      productList: productList,\n      setIsChange: setIsChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1795,\n      columnNumber: 15\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1784,\n    columnNumber: 10\n  }, this);\n}\n\n/*unused\r\n//应用续费、应用扩容、应用新购\r\nexport function BuyTeamModal({visible=false,onCancel,teamId,onOk,type=CREATETYPE_EXPAND,defaultPackageList=[]}) {\r\n  const [createType,setCreateType] = useState(CREATETYPE_EXPAND)\r\n  const [loading,setLoading] = useState(true)\r\n  const [_defaultPackageList,setDefaultPackageList] = useState([])\r\n  const [hideBuy,setHideBuy] = useState(false);\r\n\r\n  useEffect(()=>{\r\n    if(visible){\r\n      setLoading(true)\r\n      getProductsList();\r\n      setDefaultPackageList(defaultPackageList)\r\n      setCreateType(type)\r\n    }\r\n  },[visible])\r\n\r\n  useEffect(()=>{\r\n    if(visible){\r\n      setLoading(true)\r\n      if(type === createType){\r\n        console.log(\"defaultPackageList\",defaultPackageList)\r\n        setDefaultPackageList(defaultPackageList)\r\n      }else{\r\n        setDefaultPackageList([])\r\n      }\r\n      setTimeout(()=>{\r\n        setLoading(false)\r\n      },300)\r\n    }\r\n  },[createType])\r\n\r\n  async function getProductsList() {\r\n    await team_711_get_team_product_list({teamId}).then(res => {\r\n        if(res.resultCode == 200){\r\n            let allProductsList = (res.productList||[])\r\n            setHideBuy(allProductsList.filter(product => !product.expirationDt).length == 0)\r\n        }\r\n    });\r\n    setTimeout(()=>{\r\n      setLoading(false)\r\n    },300)\r\n}\r\n\r\n  return <DraggablePopUp\r\n    className=\"createTeamDraModal\"\r\n    title={\r\n      <Radio.Group className=\"createTeamTypeRadio\" value={createType} onChange={(e)=>{setCreateType(Number(e.target.value))}}>\r\n        <Radio.Button value={CREATETYPE_EXPAND}>成员扩容</Radio.Button>\r\n        <Radio.Button value={CREATETYPE_RENEWAL}>应用续费</Radio.Button>\r\n        {!hideBuy && <Radio.Button value={CREATETYPE_BUY}>应用新购</Radio.Button>}\r\n      </Radio.Group>\r\n    }\r\n    width={1000}\r\n    open={visible}\r\n    onCancel={onCancel}\r\n    maskClosable={false}\r\n    footer={null}\r\n    centered\r\n    destroyOnClose>\r\n      {loading?\r\n        <TLoading/>\r\n        :\r\n        <CreateTeam teamId={teamId}\r\n                    onCancel={onCancel}\r\n                    onOk={onOk}\r\n                    type={createType}\r\n                    defaultPackageList={_defaultPackageList}/>}\r\n    </DraggablePopUp>\r\n}\r\n*/\n_s4(CreateTeamModal, \"Dj4RPzZxjB1lkNTFt0iLamN2zZc=\");\n_c6 = CreateTeamModal;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"CreateTeam\");\n$RefreshReg$(_c2, \"PayCreateTeam\");\n$RefreshReg$(_c3, \"PayCreateTeamModal\");\n$RefreshReg$(_c4, \"PriceDetailModal\");\n$RefreshReg$(_c5, \"PriceDetailTable\");\n$RefreshReg$(_c6, \"CreateTeamModal\");", "map": {"version": 3, "names": ["useEffect", "useState", "useRef", "Modal", "Form", "Input", "Radio", "Badge", "InputNumber", "Row", "Image", "<PERSON><PERSON>", "Table", "Checkbox", "Dropdown", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON>", "Popconfirm", "DraggablePopUp", "useNavigate", "useParams", "useDispatch", "getTeamList", "CheckCircleOutlined", "CloseOutlined", "ExclamationCircleOutlined", "QuestionCircleOutlined", "DownOutlined", "team_703_calc_price", "team_704_submit_order", "team_705_get_order_status", "team_711_get_team_product_list", "team_735_bind_user_coupon", "team_736_unbind_user_coupon", "setting_105_get_team_detail_query", "team_701_get_product_list_query", "team_706_get_free_team_count_by_user_query", "useQueryGetUserValidCoupon", "useDebounce", "QRCode", "TLoading", "useQueries", "moment", "globalUtil", "eCouponType", "eOrderStatus", "eProductGroupId", "eProductId", "eProductStatus", "DraggableDrawer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Search", "Column", "ColumnGroup", "CREATETYPE_CREATE", "CREATETYPE_UPGRADE", "formItemLayout", "labelCol", "span", "CreateTeam", "onCancel", "onOk", "teamId", "type", "priceData", "productList", "setIsChange", "_s", "_team706Result$data", "_team706Result$data2", "_myCouponList$find", "_myCouponList$find2", "_myCouponList$find3", "_myCouponList$find4", "localTeamId", "query<PERSON><PERSON>ult", "queries", "enabled", "team701Result", "team706Result", "setting105Result", "data", "couponList", "refetch", "refetchGetUserValidCoupon", "navigate", "dispatch", "form", "useForm", "discountForm", "cdiskCheck", "setCdiskCheck", "priceInfo", "setPriceInfo", "originalPrice", "regularAmt", "promoAmt", "discountReduction", "payPrice", "couponReduction", "resultCode", "resultMessage", "freeAuthUserQuota1000", "isPaying", "setIsPaying", "payInfo", "setPayInfo", "showPriceDetail", "setShowPriceDetail", "vipFlg", "useWatch", "discountCodeVisible", "setDiscountCodeVisible", "payCode", "setPayCode", "orderId", "url", "setUrl", "url1", "setUrl1", "spining", "setSpining", "discountCodeRef", "selectedProductList", "setSelectedProductList", "tipShow", "setTipShow", "couponSelect", "setCouponSelect", "refreshingFlg", "setRefreshingFlg", "myCouponList", "setMyCouponList", "res", "setFieldValue", "name", "dataUpdatedAt", "code", "isEnterpriseEdition", "couponCode", "length", "JSON", "stringify", "_team701Result$data", "formatList", "filter", "group", "groupId", "Pgid_1_OS", "_prodGrpList", "for<PERSON>ach", "product", "item", "find", "_product", "push", "groupName", "products", "allProductsList", "_prodGrp", "_productList", "map", "config", "index", "_productList$find", "_productList$find2", "_productList$find3", "_productList$find4", "_productList$find5", "_productList$find6", "_find", "_productList$find7", "_productList$find8", "_productList$find9", "lastMemberNo", "productId", "freeFlg", "authCnt", "lastExpirationDt", "expirationDt", "checked", "record", "key", "isRowSpan", "prodsLength", "memberNo", "leftDaysFormat", "memberNoShow", "nextMemberNo", "durationMonth", "rebate", "effectBeginDt", "discountPrice", "Pid_13_Cdisk", "statusType", "Status_2_QA", "Status_3_Unreleased", "concat", "teamPackage", "list1", "parse", "_list1$find", "_list1$find2", "_list1$find3", "_list1$find4", "a", "setTimeout", "load_team_703_calc_price", "checkBoxChange", "e", "target", "list", "dependsOnIds", "split", "_productId", "dependedByIds", "_item", "memberNoChange", "clickType", "newBuyFormat", "_find2", "_team701Result$data2", "_find3", "_team701Result$data3", "monthPromoList", "item1", "monthCnt", "monthCntDesc", "promoRateDesc", "teamName", "getFieldValue", "isChange", "memberNoChangeW", "value", "_find4", "_team701Result$data4", "_find5", "_team701Result$data5", "durationMonthChange", "teamNameChange", "currentList", "_onOk", "values", "getFieldsValue", "warning", "Pid_11_Explorer", "Pid_12_Space", "validFlg", "_prodsInCart", "adjustMonthCnt", "adjustAuthCnt", "payInfo1", "orderType", "productIdList", "upload_team_704_team_package_pay_order", "cancelPay", "sucessPay", "_teamId", "confirm", "title", "icon", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "display", "flexDirection", "children", "okText", "cancelText", "openDiscountCode", "closeDiscountCode", "resetFields", "current", "verificationDiscountCode", "event", "nativeEvent", "then", "error", "warn", "discountCode", "dataList", "_packageList", "couponUnBindFlg", "request", "result", "listingAmt", "volumePromoAmt", "netAmt", "couponAmt", "orderProductList", "coupon", "_coupon", "couponCanUse", "pkgItem", "pkg", "effectiveDt", "amtSubstractPromo", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args", "isLoading", "userCntHelp", "teamCountFree", "enabledTeamCountFree", "flag", "alipayUrl", "wxpayUrl", "load_pay_code_url", "payMethod", "console", "qrOptions", "errorCorrectionLevel", "margin", "width", "dark", "light", "image_url", "toDataURL", "navigateTo", "window", "open", "location", "origin", "couponSelectChange", "isBefore", "orderAmtFlg", "usedByTeamFlg", "usedByUserFlg", "deleteCoupon", "couponId", "date", "days", "format", "diff", "onFinish", "initialValues", "paddingRight", "paddingLeft", "autoComplete", "<PERSON><PERSON>", "className", "label", "max<PERSON><PERSON><PERSON>", "disabled", "onChange", "marginBottom", "Group", "buttonStyle", "borderTopLeftRadius", "borderBottomLeftRadius", "textAlign", "borderTopRightRadius", "borderBottomRightRadius", "packageList", "size", "dataSource", "pagination", "bordered", "scroll", "y", "justifyContent", "dataIndex", "render", "onCell", "rowSpan", "productName", "alignItems", "fontSize", "marginLeft", "controls", "precision", "min", "max", "addonBefore", "borderRight", "borderColor", "onClick", "addonAfter", "borderLeft", "onPressEnter", "onBlur", "lastResourceNo", "freeQuotaDesc", "index1", "_team701Result$data6", "trigger", "overlay", "count", "offset", "flex", "fontStyle", "height", "backgroundColor", "borderRadius", "transition", "_team701Result$data7", "nextResourceNo", "textDecoration", "listingUnitAmt", "regularUnitAmt", "subtotal", "marginRight", "placement", "position", "colon", "cursor", "noStyle", "allowClear", "placeholder", "enterButton", "onSearch", "border", "padding", "hoverable", "couponType", "Type_2_AmountOff", "couponDiscount", "minOrderAmt", "couponName", "onConfirm", "right", "loading", "min<PERSON><PERSON><PERSON>", "marginTop", "PayCreateTeamModal", "visible", "PriceDetailModal", "centered", "maskClosable", "footer", "_c", "PayCreateTeam", "_s2", "timer", "setInterval", "params", "Status_1_Paid", "clearInterval", "success", "src", "require", "preview", "placeholde", "_c2", "destroyOnClose", "_c3", "PriceDetailTable", "_c4", "_s3", "setDataSource", "columns", "showSizeChanger", "showQuickJumper", "total", "showTotal", "_c5", "CreateTeamModal", "_s4", "prevTitle", "document", "checkClose", "max<PERSON><PERSON><PERSON>", "onClose", "_c6", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/CreateTeam.jsx"], "sourcesContent": ["import { useEffect, useState, useRef } from \"react\"\r\nimport { Modal,Form,Input,Radio,Badge,InputNumber,Row,Image,Button, Table, Checkbox, Dropdown, Menu, Card, Tooltip, Popconfirm } from \"antd\"\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";import { useNavigate, useParams } from \"react-router-dom\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { getTeamList } from \"@/team/store/actionCreators\";\r\nimport { CheckCircleOutlined,CloseOutlined, ExclamationCircleOutlined, QuestionCircleOutlined, DownOutlined } from '@ant-design/icons';\r\nimport {\r\n  team_703_calc_price,\r\n  team_704_submit_order,\r\n  team_705_get_order_status,\r\n  team_711_get_team_product_list,\r\n  team_735_bind_user_coupon,\r\n  team_736_unbind_user_coupon, } from \"@common/api/http\"\r\nimport { \r\n  setting_105_get_team_detail_query,\r\n  team_701_get_product_list_query,\r\n  team_706_get_free_team_count_by_user_query,\r\n  useQueryGetUserValidCoupon\r\n} from \"@common/api/query/query\";\r\nimport {useDebounce} from \"@common/hook\"\r\nimport QRCode from \"qrcode\";\r\nimport \"./CreateTeam.scss\"\r\nimport TLoading from \"./TLoading\";\r\nimport { useQueries } from \"@tanstack/react-query\";\r\nimport moment from \"moment\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { eCouponType, eOrderStatus, eProductGroupId, eProductId, eProductStatus } from \"@common/utils/enum\";\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\n\r\nconst { Search } = Input;\r\nconst { Column,ColumnGroup } = Table;\r\n\r\nexport const CREATETYPE_CREATE = 0; // 创建团队\r\nexport const CREATETYPE_UPGRADE = 1; // 升级至企业版\r\n// export const CREATETYPE_EXPAND = 2; // 成员扩容\r\n// export const CREATETYPE_RENEWAL = 3; // \"应用\"续费\r\n// export const CREATETYPE_BUY = 4; // \"应用\"新购\r\n\r\nconst formItemLayout = {\r\n  labelCol: {\r\n    span: 0,\r\n  },\r\n};\r\n\r\n/**\r\n * @description 创建工作区\r\n * @param {*} param0 \r\n * @returns \r\n */\r\nexport function CreateTeam({onCancel,onOk,teamId,type,priceData,productList,setIsChange}){\r\n  const {teamId: localTeamId} = useParams();\r\n  const queryResult = useQueries({\r\n    queries: [\r\n      team_701_get_product_list_query(),\r\n      team_706_get_free_team_count_by_user_query(),\r\n      {\r\n        ...setting_105_get_team_detail_query(teamId),\r\n        enabled: !!teamId\r\n      },\r\n      // team_702_get_team_package_duration_rebate_query(), //20231025 Jim Song, 月度折扣接口已废弃，伴随\"应用\"列表接口一起范围\r\n    ]\r\n  })\r\n  const [team701Result, team706Result, setting105Result ] = queryResult;\r\n  const { data: { couponList } = { couponList: [] }, refetch: refetchGetUserValidCoupon } = useQueryGetUserValidCoupon(teamId);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const [form] = Form.useForm()\r\n  const [discountForm] = Form.useForm();\r\n  const [cdiskCheck,setCdiskCheck] = useState(true);\r\n\r\n  const [priceInfo,setPriceInfo] = useState({\r\n    originalPrice: 0, regularAmt: 0, promoAmt: 0, discountReduction: 0, payPrice: 0, couponReduction: 0, resultCode: null, resultMessage: null\r\n  })\r\n\r\n  const freeAuthUserQuota1000 = 1000; //1000个免费用户数\r\n\r\n  // 是否确认购买\r\n  const [isPaying,setIsPaying] = useState(false);\r\n  const [payInfo,setPayInfo] = useState();\r\n  const [showPriceDetail,setShowPriceDetail] = useState(false);\r\n  const vipFlg = Form.useWatch('isEnterpriseEdition', form);\r\n  const [discountCodeVisible, setDiscountCodeVisible] = useState(false);\r\n  const [payCode,setPayCode] = useState({orderId: null});\r\n  // const [payCode1,setPayCode1] = useState({\r\n  //   orderInfo:{\r\n  //     orderId: null,\r\n  //   },\r\n  //   payUrl: null\r\n  // });\r\n\r\n  const [url, setUrl] = useState();\r\n  const [url1, setUrl1] = useState();\r\n  const [spining,setSpining] = useState(false);\r\n  const discountCodeRef = useRef({});\r\n  const [selectedProductList,setSelectedProductList] = useState([]);\r\n  const [tipShow,setTipShow] = useState(false);\r\n  const [couponSelect,setCouponSelect] = useState(null);\r\n  const [refreshingFlg,setRefreshingFlg] = useState(false);\r\n  const [myCouponList,setMyCouponList] = useState([]);\r\n\r\n  // 获取协作群基本信息\r\n  useEffect(()=>{\r\n    if(type !== CREATETYPE_CREATE){\r\n      let res = setting105Result.data\r\n      if (res && res.resultCode === 200) {\r\n        form.setFieldValue(\"teamName\", res.name)\r\n      }\r\n    }\r\n  },[setting105Result.dataUpdatedAt])\r\n\r\n  useEffect(() => {\r\n    if(!!priceData?.code && priceData?.isEnterpriseEdition) {\r\n      setCouponSelect({couponCode: priceData.code});\r\n    }\r\n  },[priceData?.code])\r\n\r\n  useEffect(()=>{\r\n    if((couponList||[]).length > 0){\r\n      setMyCouponList([...couponList]);\r\n    }\r\n  },[JSON.stringify(couponList)]);\r\n\r\n  useEffect(()=>{\r\n    if((team701Result.data?.productList||[]).length > 0){\r\n      let formatList = team701Result.data.productList.filter(group => vipFlg == 1 ? group.groupId != eProductGroupId.Pgid_1_OS : true)\r\n      let _prodGrpList = [];\r\n      formatList.forEach(product => {\r\n        let item = _prodGrpList.find(_product => product.groupId == _product.groupId)\r\n        if(!item){\r\n          _prodGrpList.push({groupName: product.groupName, groupId: product.groupId, products: [product]});\r\n        }else{\r\n          item.products.push(product)\r\n        }\r\n      });\r\n      let allProductsList = [];\r\n      productList = productList || []; //尝试修复 tmsbug-8699\r\n      _prodGrpList.forEach(_prodGrp => {\r\n        let _productList = _prodGrp.products.map((config,index) => {\r\n          // !!data.lastExpirationDt && leftDaysFormat(data.lastExpirationDt) < 0\r\n          let lastMemberNo = type === CREATETYPE_UPGRADE && (productList.find(product => product.productId == config.productId)?.freeFlg) != 1 ?\r\n          ((productList.find(product => product.productId == config.productId)?.authCnt || 0) > 0 ?\r\n              productList.find(product => product.productId == config.productId)?.authCnt\r\n              : 0\r\n          ) : 0;\r\n\r\n          let lastExpirationDt = type === CREATETYPE_UPGRADE && (productList.find(product => product.productId == config.productId)?.freeFlg) != 1 ?\r\n          (!!productList.find(product => product.productId == config.productId)?.expirationDt ?\r\n              productList.find(product => product.productId == config.productId)?.expirationDt\r\n              : ''\r\n          ) : '';\r\n\r\n          let checked = (selectedProductList||[]).find(record => record.productId == config.productId)?.checked\r\n          let item = {\r\n            ...config,\r\n            key: config.productId,\r\n            isRowSpan: index == 0 ? true : false,\r\n            prodsLength: index == 0 ? _prodGrp.products.length : 0,\r\n            memberNo: lastMemberNo > 0 && !!lastExpirationDt && leftDaysFormat(lastExpirationDt) < 0 ? lastMemberNo : 0,\r\n            memberNoShow: lastMemberNo > 0 && !!lastExpirationDt && leftDaysFormat(lastExpirationDt) < 0 ? lastMemberNo : 0,\r\n            lastMemberNo: lastMemberNo,\r\n            nextMemberNo: type === CREATETYPE_UPGRADE &&\r\n                (productList.find(product => product.productId == config.productId)?.freeFlg) != 1 ?\r\n                ((productList.find(product => product.productId == config.productId)?.authCnt || 0) > 0 ?\r\n                    productList.find(product => product.productId == config.productId)?.authCnt : 0\r\n                ) : 0,\r\n            durationMonth: null,\r\n            name: '',\r\n            rebate: '',\r\n            effectBeginDt: '',\r\n            expirationDt: '',\r\n            lastExpirationDt: lastExpirationDt,\r\n            originalPrice: '',\r\n            discountPrice: '',\r\n            checked: config.productId == eProductId.Pid_13_Cdisk ? cdiskCheck :\r\n              (checked == true || checked == false) ? checked :\r\n                (config.statusType == eProductStatus.Status_2_QA || config.statusType == eProductStatus.Status_3_Unreleased) ? false : true,\r\n          };\r\n          return item\r\n        });\r\n        allProductsList = allProductsList.concat(_productList)\r\n      });\r\n      if(!!priceData?.teamPackage){\r\n        let list1 = JSON.parse(priceData.teamPackage)\r\n        allProductsList = allProductsList.map(config => {\r\n          config.memberNo = list1.find(a => a.productId == config.productId)?.memberNo\r\n          config.durationMonth = list1.find(a => a.productId == config.productId)?.durationMonth\r\n          config.name = list1.find(a => a.productId == config.productId)?.name\r\n          config.rebate = list1.find(a => a.productId == config.productId)?.rebate\r\n          return config\r\n        })\r\n      }\r\n      setSelectedProductList([...allProductsList]);\r\n      if(!!priceData?.teamPackage){\r\n        setTimeout(()=>{\r\n          load_team_703_calc_price(priceData?.code,allProductsList)\r\n        },3000)\r\n      }\r\n    }\r\n  },[JSON.stringify(team701Result),vipFlg]);\r\n\r\n  function checkBoxChange(e,key){\r\n    let checked = e.target.checked\r\n    let item = selectedProductList.find(data => data.key == key)\r\n    if(!!item){\r\n      if(key == eProductId.Pid_13_Cdisk){\r\n        setCdiskCheck(checked)\r\n      }\r\n      item.checked = checked\r\n      let list = []\r\n      if(checked){\r\n        list = (item.dependsOnIds||'').split(',').filter(_productId => !!_productId)\r\n      }else{\r\n        list = (item.dependedByIds||'').split(',').filter(_productId => !!_productId)\r\n      }\r\n      list.forEach(_productId => {\r\n        let _item = selectedProductList.find(data => data.key == _productId)\r\n        _item.checked = checked\r\n      })\r\n      setSelectedProductList([...selectedProductList]);\r\n    }\r\n  }\r\n\r\n  function memberNoChange(item,clickType){\r\n    let data = selectedProductList.find(data => data.key == item.key)\r\n    if(clickType == 0){\r\n      if(type == CREATETYPE_CREATE && data.memberNo <= 0){\r\n        return\r\n      }\r\n      if(type == CREATETYPE_UPGRADE && data.memberNo <= -data.lastMemberNo){\r\n        return\r\n      }\r\n    }\r\n    if(clickType == 1){\r\n      if(data.memberNo >= 1000){\r\n        return\r\n      }\r\n    }\r\n    data.memberNo = clickType == 0 ? data.memberNo - 1 : clickType == 1 ? data.memberNo + 1 : (data.memberNoShow||0)\r\n    data.memberNoShow = clickType == 0 ? data.memberNoShow - 1 : clickType == 1 ? data.memberNoShow + 1 : (data.memberNoShow||0)\r\n    if(newBuyFormat(data) == 1){\r\n      if(!data.durationMonth){\r\n        data.durationMonth = 12\r\n        data.name = (team701Result.data?.monthPromoList || []).find(item1 => item1.monthCnt == 12)?.monthCntDesc||''\r\n        data.rebate = (team701Result.data?.monthPromoList || []).find(item1 => item1.monthCnt == 12)?.promoRateDesc||''\r\n      }\r\n      if(data.memberNo <= 0){\r\n        data.durationMonth = null\r\n        data.name = ''\r\n        data.rebate = ''\r\n      }\r\n    }\r\n    let teamName = form.getFieldValue('teamName');\r\n    isChange(teamName,selectedProductList);\r\n    load_team_703_calc_price(couponSelect?.couponCode);\r\n    setSelectedProductList([...selectedProductList]);\r\n  }\r\n\r\n  function memberNoChangeW(item,value){\r\n    let data = selectedProductList.find(data => data.key == item.key)\r\n    data.memberNo = value\r\n    data.memberNoShow = value\r\n    if(newBuyFormat(data) == 1){\r\n      if(!data.durationMonth){\r\n        data.durationMonth = 12\r\n        data.name = (team701Result.data?.monthPromoList || []).find(item1 => item1.monthCnt == 12)?.monthCntDesc||''\r\n        data.rebate = (team701Result.data?.monthPromoList || []).find(item1 => item1.monthCnt == 12)?.promoRateDesc||''\r\n      }\r\n      if((value||0) <= 0){\r\n        data.durationMonth = null\r\n        data.name = ''\r\n        data.rebate = ''\r\n      }\r\n    }\r\n    setSelectedProductList([...selectedProductList]);\r\n  }\r\n\r\n  function durationMonthChange(item,item1){\r\n    let data = selectedProductList.find(data => data.key == item.key)\r\n    if(!!item1){\r\n      data.durationMonth = item1.monthCnt\r\n      data.name = item1.monthCntDesc\r\n      data.rebate = item1.promoRateDesc\r\n      if(newBuyFormat(data) == 1){\r\n        if(data.memberNo == 0){\r\n          data.memberNo = 1\r\n          data.memberNoShow = 1\r\n        }\r\n      }\r\n    }else{\r\n      data.durationMonth = null\r\n      data.name = ''\r\n      data.rebate = ''\r\n      if(newBuyFormat(data) == 1){\r\n        data.memberNo = 0\r\n        data.memberNoShow = 0\r\n      }\r\n    }\r\n    let teamName = form.getFieldValue('teamName');\r\n    isChange(teamName,selectedProductList);\r\n    load_team_703_calc_price(couponSelect?.couponCode);\r\n    setSelectedProductList([...selectedProductList]);\r\n  }\r\n\r\n  function teamNameChange(e){\r\n    isChange(e.target.value,selectedProductList);\r\n  }\r\n\r\n  function isChange(teamName,currentList){\r\n    if(type == CREATETYPE_CREATE && !!teamName){\r\n      setIsChange(true);\r\n    }else{\r\n      if(currentList.filter(product => product.memberNo != 0 || !!product.durationMonth).length > 0){\r\n        setIsChange(true);\r\n      }else{\r\n        setIsChange(false);\r\n      }\r\n    }\r\n  }\r\n\r\n  const _onOk = useDebounce(async () => {\r\n    let values = form.getFieldsValue(true)\r\n    if(!values.teamName){\r\n      globalUtil.warning('请输入团队名称');\r\n      return\r\n    }\r\n    if(vipFlg == 0 && selectedProductList.filter(product => product.checked && product.productId != eProductId.Pid_11_Explorer && product.productId != eProductId.Pid_12_Space).length <= 0){\r\n      globalUtil.warning('至少启用一个应用');\r\n      return\r\n    }\r\n    let validFlg = false\r\n    if(priceInfo.payPrice <= 0){\r\n      if(type == CREATETYPE_CREATE){\r\n        if(vipFlg == 1 && (priceInfo.couponReduction||0) <= 0){\r\n          globalUtil.warning('请选择套餐');\r\n          return\r\n        }\r\n        validFlg = true\r\n      }else{\r\n        if(priceInfo.payPrice < 0){\r\n          setTipShow(true);\r\n          return\r\n        }\r\n        if(selectedProductList.filter(data => data.memberNo < 0).length == 0 && (priceInfo.couponReduction||0) <= 0){\r\n          globalUtil.warning('请选择套餐');\r\n          return\r\n        }\r\n        validFlg = true\r\n      }\r\n    }\r\n    let _prodsInCart = selectedProductList  //购物车(有数量的产品）\r\n      .filter(data => data.statusType != eProductStatus.Status_3_Unreleased && (data.memberNo != 0 || !!data.durationMonth))\r\n      .map(data => ({\r\n        productId: data.productId,\r\n        adjustMonthCnt: !!data.durationMonth ? data.durationMonth : 0,\r\n        adjustAuthCnt: data.memberNo,\r\n      }))\r\n    setSpining(true);\r\n    let payInfo1 = {\r\n      teamName: values.teamName,\r\n      orderType: type == CREATETYPE_CREATE ? 1 : type == CREATETYPE_UPGRADE ? 2 : '',\r\n    }\r\n    let couponCode = null;\r\n    if(vipFlg == 0){\r\n      payInfo1.productList = [];\r\n      payInfo1.productIdList = selectedProductList.filter(product => product.checked).map(product => {return product.productId}) //20250731 后端需要这个数组字段来判断\r\n      //payInfo1.disabledProductList = selectedProductList.filter(product => !product.checked).map(product => {return product.productId}) //后端不需要，禁用掉\r\n    }else{\r\n      payInfo1.productList = _prodsInCart\r\n      if(!!couponSelect && priceInfo.couponReduction > 0){\r\n        couponCode = couponSelect.couponCode\r\n      }\r\n    }\r\n    if(validFlg){\r\n      await upload_team_704_team_package_pay_order(payInfo1,couponCode,1);\r\n    }else{\r\n      await upload_team_704_team_package_pay_order(payInfo1,couponCode,2);\r\n    }\r\n    setPayInfo(payInfo1)\r\n    setSpining(false);\r\n    // } else {\r\n    //   setSpining(true);\r\n    //   let _packageList = dataSource.map(data => ({\r\n    //     objType: data.objType,\r\n    //     enableFlg: data.checked ? 1: 0,\r\n    //     typeDesc: data.typeDesc,\r\n    //     userCnt: 0\r\n    //   }))\r\n    //   // 创建免费团队\r\n    //   let request = {\r\n    //     teamName: values.teamName,\r\n    //     isEnterpriseEdition: 0,\r\n    //     pkgOpCode: type === CREATETYPE_CREATE?\"1000\":\r\n    //                type === CREATETYPE_UPGRADE?\"1001\":\"\",\r\n    //     packageList: _packageList\r\n    //   }\r\n    //   //备注: 前端不再需要调用 create_team接口，这里暂且注释掉 20231025 @garry，这里也是走 创建订单的流程\r\n    //  /* await team_002_create_team(request).then(result => {\r\n    //     if(result.resultCode == 200){\r\n    //       sucessPay(result.teamId)\r\n    //     }\r\n    //   });*/\r\n    // }\r\n    // setSpining(false);\r\n  },500)\r\n\r\n  const cancelPay = () => {\r\n    setIsPaying(false);\r\n  }\r\n\r\n  const sucessPay = (_teamId) => {\r\n    dispatch(getTeamList(localTeamId));\r\n    // 刷新团队列表\r\n    cancelPay()\r\n    onCancel()\r\n    onOk?.()\r\n    if(!teamId){\r\n      // 弹出跳转弹窗\r\n      Modal.confirm({\r\n        title: '提示',\r\n        icon: <CheckCircleOutlined style={{ color: '#52c41a' }}/>,\r\n        content: <div style={{ display: 'flex', flexDirection: 'column' }}>\r\n          <span>团队创建成功，是否切换至 {form.getFieldValue('teamName')} ？</span>\r\n          <span>点击\"否\"，停留在当前页面；</span>\r\n          <span>点击\"是\"，切换至新的团队。</span>\r\n        </div>,\r\n        okText: '是，切换团队',\r\n        cancelText: '否',\r\n        onOk:() =>{\r\n          navigate(`/team/${_teamId}`)\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // 打开优惠码校验框\r\n  const openDiscountCode = () => {\r\n    if(discountCodeVisible) return\r\n    setDiscountCodeVisible(!discountCodeVisible)\r\n  }\r\n\r\n  // 关闭优惠码校验框\r\n  const closeDiscountCode = () => {\r\n    setDiscountCodeVisible(!discountCodeVisible)\r\n    discountForm.resetFields()\r\n    discountCodeRef.current = {}\r\n    //clearDiscountCodeVerification()\r\n  }\r\n\r\n  // 校验优惠码\r\n  const verificationDiscountCode = (value, event) => {\r\n    if(event.nativeEvent.type === 'click' && !value) {\r\n      //if(event.nativeEvent.type === 'click') clearDiscountCodeVerification()\r\n      if(!value) //globalUtil.warning(\"请填写优惠码\");\r\n      return\r\n    }\r\n    team_735_bind_user_coupon({couponCode: value }).then(res => {\r\n      if(res.resultCode == 200){\r\n        discountForm.resetFields()\r\n        discountCodeRef.current = {}\r\n        if((priceInfo.payPrice||0) > 0){\r\n          load_team_703_calc_price(!!couponSelect?.couponCode ? couponSelect.couponCode : value,[],!couponSelect?.couponCode);\r\n        }else{\r\n          refetchGetUserValidCoupon()\r\n        }\r\n      }\r\n      if(res.resultCode == 500){\r\n        globalUtil.error(res.resultMessage);\r\n      }\r\n    });\r\n  }\r\n\r\n  function warn(discountCode=\"\",dataList=[]){\r\n    let _packageList = (dataList.length > 0 ? dataList : selectedProductList).filter(data => {\r\n      if(type == CREATETYPE_CREATE){\r\n        return (data.memberNo > 0 && !!data.durationMonth)\r\n      }else{\r\n        if(!!newBuyFormat(data)){\r\n          return (data.memberNo > 0 && !!data.durationMonth)\r\n        }\r\n        return (data.memberNo != 0 || !!data.durationMonth)\r\n      }\r\n    }).map(data => ({\r\n      productId: data.productId,\r\n      adjustMonthCnt: !!data.durationMonth ? data.durationMonth : 0,\r\n      adjustAuthCnt: data.memberNo,\r\n    }))\r\n    if(_packageList.length == 0){\r\n      if(!!discountCode){\r\n        globalUtil.warning('请选择套餐');\r\n      }\r\n      return []\r\n    }\r\n    return _packageList\r\n  }\r\n\r\n  // 获取支付价格\r\n  const load_team_703_calc_price = useDebounce((discountCode=\"\", dataList=[], couponUnBindFlg=false)=>{\r\n    let _packageList = warn(discountCode,dataList);\r\n    if(_packageList.length == 0){\r\n      setPriceInfo({\r\n        originalPrice: 0,\r\n        regularAmt: 0,\r\n        promoAmt: 0,\r\n        discountReduction: 0,\r\n        payPrice: 0,\r\n        couponReduction: 0,\r\n        resultCode: null,\r\n        resultMessage: null\r\n      });\r\n      setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList).map(data => {\r\n        data.effectBeginDt = ''\r\n        data.expirationDt = ''\r\n        data.originalPrice = ''\r\n        data.discountPrice = ''\r\n        data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0\r\n        return data\r\n      })]);\r\n      return\r\n    }\r\n    let request = { teamId, productList: _packageList, orderType: type == CREATETYPE_UPGRADE ? 2 : 1, couponCode: discountCode }\r\n    team_703_calc_price(request).then(result =>{\r\n      if(result.resultCode == 200){\r\n        const {listingAmt, promoAmt, regularAmt, volumePromoAmt, netAmt,couponAmt,orderProductList,couponList} = result;\r\n        setPriceInfo({\r\n          originalPrice: listingAmt,\r\n          regularAmt: regularAmt,\r\n          promoAmt: promoAmt,\r\n          discountReduction: volumePromoAmt,\r\n          payPrice: netAmt,\r\n          couponReduction: couponAmt,\r\n          resultCode: !!discountCode ? result.resultCode : null,\r\n          resultMessage: !!discountCode ? \"有效优惠码\" : \"\"\r\n        });\r\n        if(couponUnBindFlg){\r\n          let coupon = (couponList||[]).find(_coupon => _coupon.couponCode == discountCode);\r\n          if(!!coupon && !couponCanUse(coupon)){\r\n            setCouponSelect(coupon);\r\n          }\r\n        }\r\n        setMyCouponList([...(couponList||[])]);\r\n        if((orderProductList||[]).length > 0){\r\n          (dataList.length > 0 ? dataList : selectedProductList).forEach(data => {\r\n            let pkgItem = orderProductList.find(pkg => pkg.productId == data.productId)\r\n            if(!!pkgItem){\r\n              data.effectBeginDt = pkgItem.effectiveDt\r\n              data.expirationDt = pkgItem.expirationDt\r\n              data.originalPrice = pkgItem.listingAmt\r\n              data.discountPrice = pkgItem.amtSubstractPromo\r\n              data.nextMemberNo = pkgItem.authCnt\r\n            }else{\r\n              data.effectBeginDt = ''\r\n              data.expirationDt = ''\r\n              data.originalPrice = ''\r\n              data.discountPrice = ''\r\n              data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0\r\n            }\r\n          });\r\n          setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList)]);\r\n        }\r\n      } else if(result.resultCode == 500){\r\n        const {listingAmt=0, promoAmt=0, regularAmt=0, volumePromoAmt=0, netAmt=0,couponAmt=0,orderProductList=[],couponList} = result;\r\n        setPriceInfo({\r\n          originalPrice: listingAmt,\r\n          regularAmt: regularAmt,\r\n          promoAmt: promoAmt,\r\n          discountReduction: volumePromoAmt,\r\n          payPrice: netAmt,\r\n          couponReduction: couponAmt,\r\n          resultCode: !!discountCode ? result.resultCode : null,\r\n          resultMessage: !!discountCode? result.resultMessage : null\r\n        });\r\n        //setMyCouponList([...(couponList||[])]);\r\n        if((orderProductList||[]).length > 0){\r\n          (dataList.length > 0 ? dataList : selectedProductList).forEach(data => {\r\n            let pkgItem = orderProductList.find(pkg => pkg.productId == data.productId)\r\n            if(!!pkgItem){\r\n              data.effectBeginDt = pkgItem.effectiveDt\r\n              data.expirationDt = pkgItem.expirationDt\r\n              data.originalPrice = pkgItem.listingAmt\r\n              data.discountPrice = pkgItem.amtSubstractPromo\r\n              data.nextMemberNo = pkgItem.authCnt\r\n            }else{\r\n              data.effectBeginDt = ''\r\n              data.expirationDt = ''\r\n              data.originalPrice = ''\r\n              data.discountPrice = ''\r\n              data.nextMemberNo = data.lastMemberNo > 0 && !!data.lastExpirationDt ? data.lastMemberNo : 0\r\n            }\r\n          });\r\n          setSelectedProductList([...(dataList.length > 0 ? dataList : selectedProductList)]);\r\n        }\r\n      }\r\n    })\r\n  },500)\r\n\r\n  const onFieldsChange = (args) => {\r\n    // console.log(\"我正在瞬息万变！！！！\", args);\r\n  }\r\n\r\n  if(team701Result.isLoading || team706Result.isLoading || (!!teamId && setting105Result.isLoading)) return <TLoading/>\r\n\r\n  const userCntHelp = vipFlg === 0?\r\n    `限免基础版(免费)团队${team706Result.data?.teamCountFree}个,已使用${team706Result.data?.enabledTeamCountFree}个。`\r\n    :\r\n    \"\"\r\n\r\n  // 上传信息获取支付Url\r\n  const upload_team_704_team_package_pay_order = async(payInfo, discountCode ,flag)=>{\r\n    let request = {\r\n      ...payInfo,\r\n      teamId,\r\n      couponCode: discountCode || null\r\n    }\r\n    await team_704_submit_order(request).then((result) => {\r\n      if(result.resultCode == 200){\r\n        if(flag == 1){\r\n          sucessPay(result?.teamId);\r\n          return\r\n        }\r\n        const {orderId, alipayUrl, wxpayUrl} = result\r\n        load_pay_code_url(alipayUrl,'zhifubao')\r\n        load_pay_code_url(wxpayUrl,'weixin')\r\n        setPayCode({orderId: orderId})\r\n        if(flag == 2){\r\n          if(isPaying) return\r\n          setIsPaying(true)\r\n        }\r\n      } else {\r\n        \r\n      }\r\n    })\r\n  }\r\n\r\n  // 生成支付二维码\r\n  const load_pay_code_url = async(url, payMethod) => {\r\n    try {\r\n      if (!url) {\r\n        console.error('Invalid URL for QR code generation');\r\n        if(payMethod == 'zhifubao'){\r\n          setUrl1(null);\r\n        } else {\r\n          setUrl(null);\r\n        }\r\n        return;\r\n      }\r\n\r\n      const qrOptions = { errorCorrectionLevel: 'H', margin: 2, width: 120, color: { dark: '#000000', light: '#ffffff' } };\r\n      const image_url = await QRCode.toDataURL(url, qrOptions);\r\n      if(payMethod == 'zhifubao'){\r\n        setUrl1(image_url);\r\n      } else {\r\n        setUrl(image_url);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error generating QR code:', error);\r\n      if(payMethod == 'zhifubao'){\r\n        setUrl1(null);\r\n      } else {\r\n        setUrl(null);\r\n      }\r\n    }\r\n  }\r\n\r\n  const navigateTo = (type) => {\r\n    let url = ''\r\n    if(type == 1){\r\n      url = 'personal/personaldata'\r\n    }else{\r\n      url = 'personal/invoice'\r\n    }\r\n    window.open(\r\n      `${window.location.origin}/#/${url}`\r\n    );\r\n  }\r\n\r\n  function couponSelectChange(coupon){\r\n    if(!couponCanUse(coupon)){\r\n      if(couponSelect?.couponCode == coupon.couponCode){\r\n        setCouponSelect(null);\r\n        load_team_703_calc_price();\r\n      }else{\r\n        setCouponSelect(coupon);\r\n        load_team_703_calc_price(coupon.couponCode);\r\n      }\r\n    }\r\n  }\r\n\r\n  function couponCanUse(coupon){\r\n    if(!!coupon.expirationDt){\r\n      if(moment(coupon.expirationDt).isBefore(moment())){\r\n        return '优惠券已过期'\r\n      }\r\n    }\r\n    if(coupon.orderAmtFlg == 0){//金额是否适用，0---不适用   1---适用\r\n      return '订单金额不在适用范围'\r\n    } \r\n    if(!!teamId){//购买应用\r\n      if(!!coupon.teamId && coupon.teamId != teamId){//如果指定团队和该团队不匹配，无法使用\r\n        return `指定团队可用(${coupon.teamId})`\r\n      }\r\n      if(coupon.usedByTeamFlg == 1){//券有没有被该团队使用过，0---未使用过  1---使用过\r\n        return '本团队已使用过该券'\r\n      }\r\n      //20250401 Jim Song, tmsbug-11993 全已经被使用过，也需要显示为灰色\r\n      if(coupon.usedByUserFlg == 1){//有没有被该用户使用过， 0---未使用过  1---使用过\r\n        return '您已使用过该券'\r\n      }\r\n    }else{//创建团队\r\n      if(!!coupon.teamId){//是否指定团队使用，不为空即表示指定团队使用，为空表示任意团队都可使用\r\n        return `指定团队可用(${coupon.teamId})`\r\n      }\r\n      //因为是新建团队，所以不必关注是否被团队使用过\r\n      if(coupon.usedByUserFlg == 1){//有没有被该用户使用过， 0---未使用过  1---使用过\r\n        return '您已使用过该券'\r\n      }\r\n    }\r\n    return ''\r\n  }\r\n\r\n  function deleteCoupon(coupon){\r\n    team_736_unbind_user_coupon({couponId: coupon.couponId}).then(res => {\r\n      if(res.resultCode == 200){\r\n        if((priceInfo.payPrice||0) > 0){\r\n          if(couponSelect?.couponCode == coupon.couponCode){\r\n            setCouponSelect(null);\r\n            load_team_703_calc_price();\r\n          }else{\r\n            load_team_703_calc_price(couponSelect?.couponCode);\r\n          }\r\n        }else{\r\n          refetchGetUserValidCoupon()\r\n        }\r\n      }\r\n    });\r\n    //setMyCouponList([...myCouponList.filter(myCoupon => myCoupon.couponId != coupon.couponId)]);\r\n  }\r\n\r\n  function leftDaysFormat(date){\r\n    let days = moment(moment(date).format('YYYYMMDD')).diff(moment(moment().format('YYYYMMDD')),'days') - 1\r\n    if(days >= 0){\r\n      return days\r\n    }\r\n    return -1\r\n  }\r\n\r\n  function newBuyFormat(data){\r\n    if(data.lastMemberNo <= 0 || !data.lastExpirationDt){\r\n      return 1 //新购\r\n    }\r\n    if(!!data.lastExpirationDt && leftDaysFormat(data.lastExpirationDt) < 0){\r\n      return 2 //过期\r\n    }\r\n    return 0 //在有效期内\r\n  }\r\n\r\n  return (<>\r\n  <Form\r\n    form={form}\r\n    name=\"basic\"\r\n    onFinish={_onOk}\r\n    initialValues={{\r\n      teamName:\"\", \r\n      isEnterpriseEdition: priceData?.isEnterpriseEdition == 1 ? 1 : (type===CREATETYPE_CREATE?0:1)\r\n    }}\r\n    {...formItemLayout}\r\n    style={{paddingRight:24,paddingLeft:24}}\r\n    autoComplete=\"off\"\r\n    onFieldsChange={onFieldsChange}>\r\n     \r\n    <Form.Item\r\n      className=\"team-name-label\"\r\n      label=\"团队名称\"\r\n      name=\"teamName\">\r\n      <Input className=\"team-name-input\" autoComplete=\"off\" maxLength={100} disabled={type !== CREATETYPE_CREATE} onChange={teamNameChange}/>\r\n    </Form.Item>\r\n\r\n    <Form.Item \r\n      label=\"版本选择\" \r\n      name=\"isEnterpriseEdition\" \r\n      style={type == CREATETYPE_CREATE?{marginBottom:10}:{display:\"none\"}}>\r\n        <Radio.Group buttonStyle=\"solid\">\r\n          <Radio.Button value={0} style={{borderTopLeftRadius:5,borderBottomLeftRadius:5,width:120,textAlign:'center'}}>基础版(免费)</Radio.Button>\r\n          <Radio.Button value={1} style={{borderTopRightRadius:5,borderBottomRightRadius:5,width:120,textAlign:'center'}}>VIP版</Radio.Button>\r\n        </Radio.Group>\r\n    </Form.Item>\r\n  </Form>\r\n  <Form\r\n    style={{paddingLeft:24}}\r\n    name=\"productCheck\"\r\n    initialValues={{\r\n      packageList: [],\r\n    }}\r\n    {...formItemLayout}\r\n    autoComplete=\"off\"\r\n  >\r\n    <Form.Item \r\n      className=\"product-selection-formItem\"\r\n      label=\"选择应用\"\r\n      name=\"packageList\"\r\n      style={{marginBottom:10}}>\r\n      <Table\r\n        size=\"small\"\r\n        className=\"before-header\"\r\n        dataSource={selectedProductList}\r\n        pagination={false}\r\n        bordered\r\n        scroll={{y:`calc(100vh - ${vipFlg == 1?400:300}px)`}}\r\n      >\r\n        <Column\r\n          title={<div style={{ display: 'flex', justifyContent: 'center' }}>类别</div>}\r\n          dataIndex={'groupName'}\r\n          key={'groupName'}\r\n          width={vipFlg == 1 ? (type == CREATETYPE_CREATE ? 200 : 135) : '19%'}\r\n          render={(groupName) => <div style={{ display: 'flex', justifyContent: 'center' }}>{groupName}</div>}\r\n          onCell={(item) => {\r\n            if (item.isRowSpan) {\r\n              return { rowSpan: item.prodsLength }\r\n            } else {\r\n              return { rowSpan: 0 }\r\n            }\r\n          }}\r\n        />\r\n        <Column\r\n          title={'应用'}\r\n          dataIndex={'productName'}\r\n          key={'productName'}\r\n          width={vipFlg == 1 ? (type == CREATETYPE_CREATE ? 220 : 155) : '19%'}\r\n          render={(productName, item) => (\r\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>\r\n              {productName}\r\n              {item?.statusType == eProductStatus.Status_2_QA && <span style={{ color: '#70B603', fontSize: 12, marginLeft: 10 }}>内测中</span>}\r\n              {item?.statusType == eProductStatus.Status_3_Unreleased && <span style={{ color: '#F59A23', fontSize: 12, marginLeft: 10 }}>即将推出</span>}\r\n            </div>\r\n          )}\r\n          onCell={() => { return { rowSpan: 1 }}}\r\n        />\r\n        {type == CREATETYPE_CREATE ?\r\n          <Column\r\n            title={<div style={{textAlign:'center'}}>{vipFlg == 1 ? '授权人数' : '成员数'}</div>}\r\n            dataIndex={'memberNo'}\r\n            key={'memberNo'}\r\n            width={vipFlg == 1 ? 150 : '19%'}\r\n            render={(memberNo,item) => {\r\n              if(vipFlg == 1){\r\n                if(item.groupId == eProductGroupId.Pgid_1_OS){\r\n                  return (<div style={{textAlign:'center'}}>-</div>)\r\n                }\r\n                return (\r\n                  <div style={{display:'flex',alignItems:'center',justifyContent:'center'}}>\r\n                    <div className={`member-edit-primary`}>\r\n                      <InputNumber \r\n                        controls={false}\r\n                        style={{width:110}}\r\n                        size=\"small\"\r\n                        precision={0}\r\n                        min={0}\r\n                        max={1000}\r\n                        disabled={item?.statusType == eProductStatus.Status_3_Unreleased}\r\n                        addonBefore={\r\n                          <Button \r\n                            type={'primary'} \r\n                            disabled={item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == 0}\r\n                            style={{borderTopLeftRadius:5,borderBottomLeftRadius:5,borderRight:'0px',borderColor: (item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == 0) && '#d9d9d9'}}\r\n                            onClick={()=>memberNoChange(item,0)}\r\n                          >\r\n                            -\r\n                          </Button>\r\n                        }\r\n                        addonAfter={\r\n                          <Button \r\n                            type={'primary'} \r\n                            disabled={item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == {freeAuthUserQuota1000}}\r\n                            style={{borderTopRightRadius:5,borderBottomRightRadius:5,borderLeft:'0px',borderColor: (item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == 1000) && '#d9d9d9'}}\r\n                            onClick={()=>memberNoChange(item,1)}\r\n                          >\r\n                            +\r\n                          </Button>\r\n                        }\r\n                        value={memberNo}\r\n                        onChange={(value)=>memberNoChangeW(item,value)}\r\n                        onPressEnter={()=>memberNoChange(item,2)}\r\n                        onBlur={()=>memberNoChange(item,2)}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                )\r\n              }\r\n              else{\r\n                return (<div style={{textAlign:'center'}}>{freeAuthUserQuota1000}</div>)\r\n              }\r\n            }}\r\n            onCell={() => { return { rowSpan: 1 }}}\r\n          />\r\n        :\r\n          <ColumnGroup title={'购买前'} className=\"top-header-a\">\r\n            <Column\r\n              title={'授权数'}\r\n              className=\"top-header-a\"\r\n              dataIndex={'lastMemberNo'}\r\n              key={'lastMemberNo'}\r\n              width={90}\r\n              render={(lastMemberNo) => <div>{lastMemberNo||0}人</div>}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n            <Column\r\n              title={'对象新建数'}\r\n              className=\"top-header-a\"\r\n              dataIndex={'lastResourceNo'}\r\n              key={'lastResourceNo'}\r\n              width={150}\r\n              render={(lastResourceNo,item) => {\r\n                if(!!item.lastExpirationDt && (item.lastMemberNo||0) > 0){\r\n                  return  (<div style={newBuyFormat(item) == 2 ? {color: '#999'} : {}}>∞</div>);\r\n                }\r\n                return  (<div>{item?.freeQuotaDesc||'-'}</div>);\r\n              }}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n            <Column\r\n              title={'有效期至'}\r\n              className=\"top-header-a\"\r\n              dataIndex={'lastExpirationDt'}\r\n              key={'lastExpirationDt'}\r\n              width={100}\r\n              render={(lastExpirationDt,item) => {\r\n                if(!!lastExpirationDt && (item.lastMemberNo||0) > 0){\r\n                  return (<div style={newBuyFormat(item) == 2 ? {color: '#999'} : {}}>{moment(lastExpirationDt).format('YYYY-MM-DD')}</div>);\r\n                }\r\n                return (<div>∞</div>);\r\n              }}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n          </ColumnGroup>\r\n        }\r\n        {type == CREATETYPE_CREATE &&\r\n          <Column\r\n            title={<div style={{textAlign:'center'}}>对象新建数</div>}\r\n            dataIndex={'lastResourceNo'}\r\n            key={'lastResourceNo'}\r\n            width={vipFlg == 1 ? 200 : '19%'}\r\n            render={(lastResourceNo,item) => {\r\n              if(vipFlg == 1 && (item.memberNo||0) > 0){\r\n                return  (<div style={{textAlign:'center'}}>∞</div>);\r\n              }\r\n              return  (<div style={{textAlign:'center'}}>{item?.freeQuotaDesc||'-'}</div>);\r\n            }}\r\n            onCell={() => { return { rowSpan: 1 }}}\r\n          />\r\n        }\r\n        {vipFlg == 1 ?\r\n          (type == CREATETYPE_CREATE ?\r\n            <Column \r\n              title={<div style={{textAlign:'center'}}>购买时长</div>}\r\n              dataIndex={'durationMonth'}\r\n              key={'durationMonth'}\r\n              width={160}\r\n              render={(durationMonth,item,index1) => {\r\n                if(item.groupId == eProductGroupId.Pgid_1_OS){\r\n                  return (<div style={{textAlign:'center'}}>-</div>);\r\n                }\r\n                return (\r\n                  <div style={{display:'flex',alignItems:'center'}}>  {/*style={{display:'flex',alignItems:'center',justifyContent:'center' */} \r\n                    <Dropdown trigger={['click']} disabled={item?.statusType == eProductStatus.Status_3_Unreleased} overlay={\r\n                      <Menu>\r\n                        <Menu.Item>\r\n                          <Radio.Group size=\"small\" buttonStyle=\"solid\" className=\"create-team-date-item\" value={item.durationMonth}>\r\n                            {(team701Result.data?.monthPromoList || []).map((item1,index)=> \r\n                              <Badge key={index} size=\"small\" title=\"\" className=\"pay-badge\" count={item1.promoRateDesc || \"\"} offset={[-8,-5]}>\r\n                                <Radio.Button onClick={()=>durationMonthChange(item,item1)} value={item1.monthCnt}>{item1.monthCntDesc}</Radio.Button> \r\n                              </Badge>\r\n                            )}\r\n                          </Radio.Group>\r\n                        </Menu.Item>\r\n                      </Menu>\r\n                    }>\r\n                      <a style={{color:'inherit', display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%'}}>\r\n                        <div style={{display: 'flex', alignItems: 'center', flex: '0 0 auto', paddingRight: '16px'}}>\r\n                          {newBuyFormat(item) != 2 &&\r\n                            <span style={{color:'#999',fontStyle:'italic'}}>\r\n                              {!!newBuyFormat(item) ? '' : `剩${leftDaysFormat(item.lastExpirationDt)}天`}\r\n                            </span>\r\n                          }\r\n                          {newBuyFormat(item) == 2 &&\r\n                            <span style={{color:'red',fontStyle:'italic'}}>已过期</span>\r\n                          }\r\n                        </div>\r\n                        <div style={{display: 'flex', alignItems: 'center', flex: '0 0 auto', paddingLeft: '16px'}}>\r\n                          {!!durationMonth ?\r\n                            <Badge key={index1} size=\"small\" title=\"\" className=\"pay-badge\" count={item.rebate || \"\"} offset={[-8,-5]}>\r\n                              <div style={{height:24,width:60,display:'flex',alignItems:'center',fontSize:12,color:'#fff',backgroundColor:'#0077F2',borderRadius:5}}>\r\n                                <span style={{width:40,display:'flex',justifyContent:'center'}}>{item.name}</span>\r\n                                <span style={{width:20,display:'flex',justifyContent:'center'}}><DownOutlined/></span>\r\n                              </div> \r\n                            </Badge>\r\n                          :\r\n                            <span style={{color: newBuyFormat(item) == 1 ? '#B3D6FB' : '#F59A23'}}>{newBuyFormat(item) == 1 ? '选时长' : '续时长'}</span>\r\n                          }\r\n                        </div>\r\n                      </a>\r\n                    </Dropdown>\r\n                    {!!durationMonth && \r\n                      <Button \r\n                        style={{ borderRadius: '50%', transition: 'all 0s 0s'}} \r\n                        size=\"small\" \r\n                        type=\"text\" \r\n                        icon={<CloseOutlined className=\"fontsize-12\" />} \r\n                        onClick={()=>durationMonthChange(item)}/>\r\n                    }\r\n                  </div>\r\n                );\r\n              }}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n          :\r\n            <ColumnGroup title={'规格选择'} className=\"top-header-b\">\r\n              <Column \r\n                title={'增/减员数'}\r\n                className=\"top-header-b\"\r\n                dataIndex={'memberNo'}\r\n                key={'memberNo'}\r\n                width={160}\r\n                render={(memberNo,item) => {\r\n                  return (\r\n                    <div style={{display:'flex',alignItems:'center',justifyContent:'center'}}>\r\n                      <div className={`member-edit-primary`}>\r\n                        <InputNumber \r\n                          controls={false}\r\n                          style={{width:110}}\r\n                          size=\"small\"\r\n                          precision={0}\r\n                          min={!!newBuyFormat(item) ? 0 : -item.lastMemberNo}\r\n                          max={1000}\r\n                          disabled={item?.statusType == eProductStatus.Status_3_Unreleased}\r\n                          addonBefore={\r\n                            <Button \r\n                              type={'primary'} \r\n                              disabled={item?.statusType == eProductStatus.Status_3_Unreleased || (!!newBuyFormat(item) ? ((memberNo||0) == 0) : ((memberNo||0) == -item.lastMemberNo))}\r\n                              style={{\r\n                                borderTopLeftRadius:5,\r\n                                borderBottomLeftRadius:5,\r\n                                borderRight:'0px',\r\n                                borderColor: (item?.statusType == eProductStatus.Status_3_Unreleased || (!!newBuyFormat(item) ? ((memberNo||0) == 0) : ((memberNo||0) == -item.lastMemberNo))) && '#d9d9d9'\r\n                              }}\r\n                              onClick={()=>memberNoChange(item,0)}\r\n                            >\r\n                              -\r\n                            </Button>\r\n                          }\r\n                          addonAfter={\r\n                            <Button \r\n                              type={'primary'} \r\n                              disabled={item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == {freeAuthUserQuota1000}}\r\n                              style={{borderTopRightRadius:5,borderBottomRightRadius:5,borderLeft:'0px',\r\n                                borderColor: (item?.statusType == eProductStatus.Status_3_Unreleased || (memberNo||0) == {freeAuthUserQuota1000}) && '#d9d9d9'\r\n                              }}\r\n                              onClick={()=>memberNoChange(item,1)}\r\n                            >\r\n                              +\r\n                            </Button>\r\n                          }\r\n                          value={memberNo}\r\n                          onChange={(value)=>memberNoChangeW(item,value)}\r\n                          onPressEnter={()=>memberNoChange(item,2)}\r\n                          onBlur={()=>memberNoChange(item,2)}\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                }}\r\n                onCell={() => { return { rowSpan: 1 }}}\r\n              />\r\n              <Column\r\n                title={'购买时长'}\r\n                className=\"top-header-b\"\r\n                dataIndex={'durationMonth'}\r\n                key={'durationMonth'}\r\n                width={160}\r\n                render={(durationMonth,item,index1) => {\r\n                  return (\r\n                    <div style={{display:'flex',alignItems:'center',justifyContent:'space-between'}}>\r\n\r\n                      <div style={{display:'flex',alignItems:'center'}}>\r\n                        <Dropdown trigger={['click']} disabled={item?.statusType == eProductStatus.Status_3_Unreleased} overlay={\r\n                          <Menu>\r\n                            <Menu.Item>\r\n                              <Radio.Group size=\"small\" buttonStyle=\"solid\" className=\"create-team-date-item\" value={item.durationMonth}>\r\n                                {(team701Result.data?.monthPromoList || []).map((item1,index)=> \r\n                                  <Badge key={index} size=\"small\" title=\"\" className=\"pay-badge\" count={item1.promoRateDesc || \"\"} offset={[-8,-5]}>\r\n                                    <Radio.Button onClick={()=>durationMonthChange(item,item1)} value={item1.monthCnt}>{item1.monthCntDesc}</Radio.Button> \r\n                                  </Badge>\r\n                                )}\r\n                              </Radio.Group>\r\n                            </Menu.Item>\r\n                          </Menu>\r\n                        }>\r\n                          <a style={{color:'inherit', display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%'}}>\r\n                            <div style={{display: 'flex', alignItems: 'center', flex: '0 0 auto', paddingRight: '16px'}}>\r\n                              {newBuyFormat(item) != 2 &&\r\n                                <span style={{color:'#999',fontStyle:'italic'}}>\r\n                                  {!!newBuyFormat(item) ? '' : `剩${leftDaysFormat(item.lastExpirationDt)}天`}\r\n                                </span>\r\n                              }\r\n                              {newBuyFormat(item) == 2 &&\r\n                                <span style={{color:'red',fontStyle:'italic'}}>已过期</span>\r\n                              }\r\n                            </div>\r\n                            <div style={{display: 'flex', alignItems: 'center', flex: '0 0 auto', paddingLeft: '16px'}}>\r\n                              {!!durationMonth ?\r\n                                <Badge key={index1} size=\"small\" title=\"\" className=\"pay-badge\" count={item.rebate || \"\"} offset={[-8,-5]}>\r\n                                  <div style={{height:24,width:60,display:'flex',alignItems:'center',fontSize:12,color:'#fff',backgroundColor:'#0077F2',borderRadius:5}}>\r\n                                    <span style={{width:40,display:'flex',justifyContent:'center'}}>{item.name}</span>\r\n                                    <span style={{width:20,display:'flex',justifyContent:'center'}}><DownOutlined/></span>\r\n                                  </div> \r\n                                </Badge>\r\n                              :\r\n                                <span style={{color: newBuyFormat(item) == 1 ? '#B3D6FB' : '#F59A23'}}>{newBuyFormat(item) == 1 ? '选时长' : '续时长'}</span>\r\n                              }\r\n                            </div>\r\n                          </a>\r\n                        </Dropdown>\r\n                        {!!durationMonth && \r\n                          <Button \r\n                            style={{ borderRadius: '50%', transition: 'all 0s 0s'}} \r\n                            size=\"small\" \r\n                            type=\"text\" \r\n                            icon={<CloseOutlined className=\"fontsize-12\" />} \r\n                            onClick={()=>durationMonthChange(item)}/>\r\n                        }\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                }}\r\n                onCell={() => { return { rowSpan: 1 }}}\r\n              />\r\n            </ColumnGroup>\r\n          )\r\n        :\r\n          null\r\n        }\r\n        {type == CREATETYPE_CREATE ?\r\n          (vipFlg == 1 ?\r\n            <Column\r\n              title={<div style={{textAlign:'center'}}>生效开始</div>}\r\n              dataIndex={'effectBeginDt'}\r\n              key={'effectBeginDt'}\r\n              width={120}\r\n              render={(effectBeginDt, item) => {\r\n                if(item.groupId == eProductGroupId.Pgid_1_OS){\r\n                  return (<div style={{textAlign:'center'}}>-</div>)\r\n                }\r\n                return (<div style={{textAlign:'center'}}>{!!effectBeginDt ? moment(effectBeginDt).format('YYYY-MM-DD') : ''}</div>)\r\n              }}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n          :\r\n            <Column\r\n              title={<div style={{textAlign:'center'}}>有效期至</div>}\r\n              dataIndex={'expirationDt'}\r\n              key={'expirationDt'}\r\n              width={'19%'}\r\n              render={()=> <div style={{textAlign:'center'}}>∞</div>}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n          )\r\n        :\r\n          <ColumnGroup title={'预览'} className=\"top-header-c\">\r\n            <Column\r\n              title={'授权数'}\r\n              className=\"top-header-c\"\r\n              dataIndex={'nextMemberNo'}\r\n              key={'nextMemberNo'}\r\n              width={90}\r\n              render={(nextMemberNo,item) => {\r\n                if(!!newBuyFormat(item) && !item.durationMonth && item.memberNo <= 0){\r\n                  return (<div>{item.memberNo}人</div>);\r\n                }\r\n                return (<div>{nextMemberNo||0}人</div>);\r\n              }}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n            <Column\r\n              title={'对象新建数'}\r\n              className=\"top-header-c\"\r\n              dataIndex={'nextResourceNo'}\r\n              key={'nextResourceNo'}\r\n              width={150}\r\n              render={(nextResourceNo,item) => {\r\n                if(!newBuyFormat(item)){\r\n                  if(item.memberNo < 0 && ((item.memberNo + item.lastMemberNo) == 0)){\r\n                    return  (<div>{item?.freeQuotaDesc||'-'}</div>);\r\n                  }\r\n                  return  (<div>∞</div>);\r\n                }\r\n                if((item.memberNo||0) > 0 && !!item.durationMonth){\r\n                  return  (<div>∞</div>);\r\n                }\r\n                return  (<div>{item?.freeQuotaDesc||'-'}</div>);\r\n              }}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n            <Column\r\n              title={'有效期至'}\r\n              className=\"top-header-c\"\r\n              dataIndex={'expirationDt'}\r\n              key={'expirationDt'}\r\n              width={90}\r\n              render={(expirationDt,item) => {\r\n                if(!newBuyFormat(item)){\r\n                  if(!!expirationDt){\r\n                    return (<div>{moment(expirationDt).format('YYYY-MM-DD')}</div>);\r\n                  }\r\n                  return (<div>{moment(item.lastExpirationDt).format('YYYY-MM-DD')}</div>);\r\n                }\r\n                if((item.memberNo||0) > 0 && !!item.durationMonth){\r\n                  if(!!expirationDt){\r\n                    return (<div>{moment(expirationDt).format('YYYY-MM-DD')}</div>);\r\n                  }\r\n                  return (<></>);\r\n                }\r\n                return (<div>∞</div>);\r\n              }}\r\n              onCell={() => { return { rowSpan: 1 }}}\r\n            />\r\n          </ColumnGroup>\r\n        }\r\n        {vipFlg == 1 && type == CREATETYPE_CREATE ?\r\n          <Column\r\n            title={<div style={{textAlign:'center'}}>生效结束</div>}\r\n            dataIndex={'expirationDt'}\r\n            key={'expirationDt'}\r\n            width={120}\r\n            render={(expirationDt)=> {\r\n              if(!!expirationDt){\r\n                return (\r\n                  <div style={{textAlign:'center'}}>{!!expirationDt ? moment(expirationDt).format('YYYY-MM-DD') : ''}</div>\r\n                )\r\n              }else{\r\n                return (<div style={{textAlign:'center'}}>∞</div>)\r\n              }\r\n            }}\r\n            onCell={() => { return { rowSpan: 1 }}}\r\n          />\r\n        :\r\n          null\r\n        }\r\n        {vipFlg == 1 &&\r\n          <Column\r\n            title={<div style={{textAlign:'center'}}>单价</div>}\r\n            dataIndex={'discountPrice'}\r\n            key={'discountPrice'}\r\n            width={120}\r\n            render={(discountPrice, item) => {\r\n              if(item.groupId == eProductGroupId.Pgid_1_OS){\r\n                return (<div style={{textAlign:'center'}}>0</div>)\r\n              }\r\n              return (\r\n                <div style={{ display: 'flex', alignItems: 'center' }}>\r\n                  <span style={{color: '#999',width: 40, textDecoration: 'line-through'}}>¥{item?.listingUnitAmt||'0'}</span>\r\n                  <span>¥{item?.regularUnitAmt||'0'}/人/月</span>\r\n                </div>\r\n              );\r\n            }}\r\n            onCell={() => { return { rowSpan: 1 }}}\r\n          />\r\n        }\r\n        {vipFlg == 1 &&\r\n          <Column\r\n            title={'小计'}\r\n            dataIndex={'subtotal'}\r\n            key={'subtotal'}\r\n            render={(subtotal, item) => {\r\n              if(item.groupId == eProductGroupId.Pgid_1_OS){\r\n                return (<div></div>)\r\n              }\r\n              return (\r\n                <div style={{ display: 'flex', alignItems: 'center' }}>\r\n                  <span style={{color: '#999',marginRight: 10, textDecoration: 'line-through',flex:1}}>{!!item.originalPrice ? '¥' : ''}{item.originalPrice}</span>\r\n                  <span style={{flex:1}}>{!!item.discountPrice ? '¥' : ''}{item.discountPrice}</span>\r\n                </div>\r\n              );\r\n            }}\r\n            onCell={() => { return { rowSpan: 1 }}}\r\n          />\r\n        }\r\n        {vipFlg == 1 ? null : type == CREATETYPE_CREATE ?\r\n          <Column\r\n            title={<div style={{textAlign:'center'}}>勾选使用</div>}\r\n            dataIndex={'productId'}\r\n            key={'productId'}\r\n            width={'5%'}\r\n            render={(productId,item) => {\r\n              return (\r\n                <div style={{textAlign:'center'}}>\r\n                  <Checkbox\r\n                    disabled={productId == eProductId.Pid_11_Explorer || productId == eProductId.Pid_12_Space || item?.statusType == eProductStatus.Status_3_Unreleased}\r\n                    checked={item.checked}\r\n                    onChange={(e)=>checkBoxChange(e,productId)}\r\n                  />\r\n                </div>\r\n              )\r\n            }}\r\n            onCell={() => { return { rowSpan: 1 }}}\r\n          />\r\n        : \r\n          null\r\n        }\r\n      </Table>\r\n    </Form.Item>\r\n  </Form>\r\n  <div style={{height:220}}>\r\n    {vipFlg == 1 ?\r\n      <div className=\"price-bottom\">\r\n        <div className=\"price-bottom-left\">\r\n          <div style={{display:'flex',alignItems:'center'}}>\r\n            <span style={{marginRight:50}}>\r\n              选择优惠券({myCouponList.length})<Tooltip title=\"高亮显示的优惠券可用，灰显的优惠券不可用\" placement=\"right\"><QuestionCircleOutlined style={{ color: '#f59a23',marginLeft:5 }}/></Tooltip>\r\n            </span>\r\n            <Button\r\n              className={refreshingFlg && 'refresh-icon'}\r\n              style={{ position: 'relative', color: '#666' }}\r\n              type=\"link\"\r\n              icon={<span className=\"refresh-position fontsize-14 iconfont shuaxin1\" />}\r\n              onClick={() => {\r\n                setRefreshingFlg(true);\r\n                setTimeout(() => {\r\n                  if((priceInfo.payPrice||0) > 0){\r\n                    load_team_703_calc_price(couponSelect?.couponCode);\r\n                  }else{\r\n                    refetchGetUserValidCoupon()\r\n                  }\r\n                  setRefreshingFlg(false);\r\n                }, 500);\r\n              }}\r\n            />\r\n            <span style={{marginRight:50}}>刷新</span>\r\n            <Form colon={false} form={discountForm}>\r\n              <Form.Item \r\n              style={{ margin: 0 }}\r\n              label={<span style={{ color: '#0077f2', cursor: 'pointer' }}\r\n              onClick={openDiscountCode}>我有优惠码</span>}\r\n              // validateStatus={priceInfo.resultCode === 200 ? \"success\" : (priceInfo.resultCode === 500 ? \"error\" : null)}\r\n              >\r\n                {discountCodeVisible &&\r\n                <Form.Item name=\"code\" noStyle>\r\n                  <Search\r\n                    className=\"discount-code discount-code-visible\"\r\n                    size=\"small\"\r\n                    maxLength={12}\r\n                    allowClear\r\n                    autoComplete=\"off\"\r\n                    placeholder=\"请填写优惠码\"\r\n                    enterButton=\"校验\"\r\n                    onChange={e => { discountCodeRef.current.code =  e.target.value }}\r\n                    onSearch={verificationDiscountCode}\r\n                  />\r\n                </Form.Item>\r\n                }\r\n                {discountCodeVisible &&\r\n                <Form.Item noStyle>\r\n                  <Button \r\n                    style={{ borderRadius: '50%', transition: 'all 0s 0s'}} \r\n                    size=\"small\" \r\n                    type=\"text\" \r\n                    icon={<CloseOutlined className=\"fontsize-12\" />} \r\n                    onClick={closeDiscountCode}/>\r\n                </Form.Item>\r\n                }\r\n                {/* <span \r\n                  className=\"fontsize-12\" \r\n                  style={{ color: priceInfo.resultCode == 500 ? 'red' : '',marginLeft:10}}\r\n                >\r\n                  {priceInfo.resultCode === 500 ? <CloseCircleOutlined /> : ''}\r\n                  {priceInfo.resultCode === 500 ? priceInfo.resultMessage : null}\r\n                </span> */}\r\n              </Form.Item>\r\n            </Form>\r\n          </div>\r\n          <div className=\"flex-column-parent\" style={{height: 110, width:'90%', border: '1px solid #f0f0f0', borderRadius: 5}}>\r\n            <div style={{height:'100%',padding: '5px 0px 5px 5px'}} className=\"flex-column-child section\">\r\n              <Row>\r\n                {myCouponList.map(coupon => {\r\n                  return (\r\n                    <div style={{display:'flex',alignItems:'flex-start',position:'relative',height:105}}>\r\n                      <Button type='text' title={!couponCanUse(coupon) ? `优惠码：${coupon.couponCode}` : `优惠码：${coupon.couponCode}\\n不可用原因：${couponCanUse(coupon)}`}\r\n                              onClick={() => couponSelectChange(coupon)}\r\n                              style={{padding:0}}\r\n                              disabled={!!couponCanUse(coupon)}>\r\n                        <Card \r\n                          style={{backgroundColor: !couponCanUse(coupon) ? '#E6F2FE' : '#f0f0f0'}} \r\n                          className={(couponSelect?.couponCode == coupon.couponCode ? \"CouponCard-select\" : '') + \" CouponCard\"} \r\n                          hoverable={false}\r\n                        >\r\n                          <div\r\n                            style={{\r\n                              width:'100%', \r\n                              height: 70, \r\n                              backgroundColor: !couponCanUse(coupon) ? '#0077F2' : '#999',\r\n                              borderTopLeftRadius:5,\r\n                              borderTopRightRadius:5\r\n                            }}\r\n                          >\r\n                            <div style={{display:'flex',alignItems:'center',height:'100%',marginLeft:10}}>\r\n                              <div style={{color:'#fff'}}>\r\n                                <div style={{display:'flex',alignItems:'center'}}>\r\n                                  <div style={{fontSize:18,marginRight:20}}>\r\n                                    {coupon.couponType == eCouponType.Type_2_AmountOff ? ('减￥'+(coupon.couponDiscount||'')) : ((coupon.couponDiscount||'')+'折')}\r\n                                  </div>\r\n                                  <div>满  ￥{coupon.minOrderAmt}</div>\r\n                                </div>\r\n                                <div>有效期至{moment(coupon.expirationDt).format('YYYY-MM-DD')}</div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                          <div style={{width:'100%',height: 30}}>\r\n                            <div style={{display:'flex',alignItems:'center',height: 30, marginLeft:10,color: !couponCanUse(coupon) ? '#0077F2' : '#999'}}>\r\n                              <span>{coupon.couponName}</span>\r\n                            </div>\r\n                          </div>\r\n                        </Card>\r\n                      </Button>\r\n                      <Popconfirm title='确定删除该优惠券?' onConfirm={()=>deleteCoupon(coupon)}>\r\n                        <Button \r\n                          style={{ borderRadius: '50%', transition: 'all 0s 0s',width:14,height:14,position:'absolute',right:0}} \r\n                          size=\"small\" \r\n                          icon={<CloseOutlined style={{fontSize:10}} />}\r\n                        />\r\n                      </Popconfirm>\r\n                    </div>\r\n                  );\r\n                })}\r\n              </Row>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"price-bottom-right\">\r\n          <div className=\"price-bottom-detailed\">\r\n            <div className=\"price-bottom-detailed-descriptions\">\r\n              <div className=\"price-bottom-detailed-descriptions-li\">\r\n                <span className=\"li-label\">原价:</span>\r\n                <span className=\"li-value\"><span style={{textDecoration: 'line-through',color:'#999',marginRight:10}}>\r\n                  ￥{priceInfo.originalPrice||0}</span>{(priceInfo.promoAmt||0) > 0 ? `(立减-￥${priceInfo.promoAmt})` : ''}\r\n                </span>\r\n              </div>\r\n              <div className=\"price-bottom-detailed-descriptions-li\">\r\n                <span className=\"li-label\">现价:</span>\r\n                <span className=\"li-value\">￥{priceInfo.regularAmt||0}</span>\r\n              </div>\r\n              {(priceInfo.discountReduction||0) > 0 ?\r\n              <div className=\"price-bottom-detailed-descriptions-li\">\r\n                <span className=\"li-label\">折扣减:</span>\r\n                <span className=\"li-value\">-￥{priceInfo.discountReduction}</span>\r\n              </div>\r\n              :\r\n              <div style={{display:'none'}}></div>\r\n              }\r\n              {!!couponSelect ?\r\n                <div className=\"price-bottom-detailed-descriptions-li\">\r\n                  <span className=\"li-label\" style={{color: priceInfo.couponReduction == 0 ? '#999' : '#333'}}>优惠券:</span>\r\n                  <span className=\"li-value\" style={{color: priceInfo.couponReduction == 0 ? '#999' : '#333'}}>\r\n                    -￥{priceInfo.couponReduction}\r\n                    <span className=\"coupon\" style={{backgroundColor: priceInfo.couponReduction == 0 ? '#999' : '#0077F2'}}>\r\n                      满￥{myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.minOrderAmt}\r\n                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponType == eCouponType.Type_2_AmountOff ? '减￥' : '享'}\r\n                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponDiscount}\r\n                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponType == eCouponType.Type_2_AmountOff ? '' : '折'}\r\n                      <a \r\n                        className=\"delete\" \r\n                        style={{color: priceInfo.couponReduction == 0 ? '#999' : '#0077F2'}} \r\n                        onClick={()=>{setCouponSelect(null);load_team_703_calc_price();}}\r\n                      >\r\n                        <CloseOutlined style={{fontSize:10}} />\r\n                      </a>\r\n                    </span>\r\n                  </span>\r\n                </div>\r\n              :\r\n                <div style={{display:'none'}}></div>\r\n              }\r\n              \r\n              <div className=\"price-bottom-detailed-descriptions-li\">\r\n                <span className=\"li-label\">合计:</span>\r\n                <span className=\"li-value\">\r\n                  <div style={{fontSize:12,display:'flex',alignItems:'baseline',justifyContent:'end'}}>\r\n                    <span style={{color:'#f59a23'}}>￥</span>\r\n                    <span className=\"shifu-rate\" style={{color:'#f59a23'}}>{priceInfo.payPrice||0}</span>\r\n                  </div>\r\n                </span>\r\n              </div>\r\n              {/* <a style={{fontSize:12,display:'flex',color:'#0077F2'}} onClick={()=>setShowPriceDetail(true)}>价格详情</a> */}\r\n            </div>  \r\n          </div>\r\n        </div>\r\n      </div>\r\n      :\r\n      <div style={{height: 40}}/>\r\n    }\r\n    <div style={{display:'flex',alignItems:\"flex-end\",justifyContent:'space-between',paddingLeft:14}}>\r\n      <div>\r\n        {vipFlg == 0 &&\r\n          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999' }}>备注1：{userCntHelp}</div>\r\n        }\r\n        {vipFlg == 1 &&\r\n          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999' }}>备注1：vip人数为0的应用，您将继续享有基础版的功能免费长期使用。</div>\r\n        }\r\n        {type == CREATETYPE_CREATE ?\r\n          <div \r\n            style={{ \r\n              marginLeft: 10, \r\n              height: 20, \r\n              fontSize: 12, \r\n              color: '#999'\r\n            }}\r\n          >\r\n            备注2：您不需要的功能，在团队创建成功后，可在 设置 {'->'} 应用管理 页面中禁用(或再次开启)\r\n          </div>\r\n        :\r\n          null\r\n        }\r\n        {type == CREATETYPE_CREATE &&\r\n          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999' }}>\r\n            <span style={{color:'#fff'}}>备注2：</span>点击 帮助中心 {'->'} 应用管理 链接，可定位到 官网帮助页的 关于应用管理的帮助文档。\r\n          </div>\r\n        }\r\n        {type == CREATETYPE_UPGRADE && \r\n        <div \r\n          style={{ \r\n            marginLeft: 10, \r\n            height: 20, \r\n            fontSize: 12, \r\n            color: '#999'\r\n          }}\r\n        >\r\n          备注2：可在 设置 {'->'} 应用管理 中进行应用启用/禁用。\r\n        </div>\r\n        }\r\n      </div>\r\n      <div style={{paddingRight:24}}>\r\n        <div style={{textAlign: 'right' }}>\r\n          <Button\r\n            className={vipFlg == 1 ? \"purchase-btn\":\"found-btn\"}\r\n            type=\"primary\"\r\n            loading={spining}\r\n            style={{ minWidth: 140, height: 34, borderRadius: 5 }}\r\n            onClick={_onOk}\r\n            >\r\n              {vipFlg == 1 ? \"立即购买\" : `创建(已选${selectedProductList.filter(product => product.checked && product.productId != eProductId.Pid_11_Explorer && product.productId != eProductId.Pid_12_Space).length}个应用)`}\r\n          </Button>\r\n        </div>\r\n        {vipFlg == 1 &&\r\n          <div style={{textAlign: 'right',fontSize:12,color:'#999',marginTop:5}}>\r\n            支付成功后，可前往 <a style={{color:'#0077F2'}} onClick={()=>navigateTo(1)}>个人中心</a> {'->'} <a style={{color:'#0077F2'}} onClick={()=>navigateTo(2)}>发票管理</a> 进行开票\r\n          </div>\r\n        }\r\n      </div>  \r\n    </div>\r\n  </div>\r\n  <PayCreateTeamModal \r\n    teamId={teamId}\r\n    visible={isPaying} \r\n    onCancel={cancelPay} \r\n    priceInfo={priceInfo} \r\n    onOk={sucessPay}\r\n    payCode={payCode}\r\n    url={url}\r\n    url1={url1}/>\r\n  <PriceDetailModal \r\n    visible={showPriceDetail}\r\n    onCancel={()=>setShowPriceDetail(false)} />\r\n  <DraggablePopUp\r\n    title='提示'\r\n    className=\"tms-modal\"\r\n    width={420}\r\n    open={tipShow}\r\n    onCancel={()=>setTipShow(false)}\r\n    centered\r\n    maskClosable={false}\r\n    footer={\r\n      <div style={{display:'flex',justifyContent:'center'}}>\r\n        <Button type={'primary'} style={{borderRadius:5}} onClick={()=>setTipShow(false)}>我知道了</Button>\r\n      </div>\r\n    }\r\n  >\r\n    <div style={{display:'flex',justifyContent:'center',marginTop:20}}>因减员等因素，订单金额＜0，您可继续增购应用</div>\r\n    <div style={{display:'flex',justifyContent:'center',marginBottom:20}}>或延续时长，确保订单金额≥0。</div>\r\n  </DraggablePopUp>\r\n  </>)\r\n}\r\n\r\nfunction PayCreateTeam({onOk, teamId, priceInfo, payCode, url, url1}){\r\n  const [form] = Form.useForm()\r\n  const payMethod = Form.useWatch('payMethod', form);\r\n  //const [refreshingFlg,setRefreshingFlg] = useState(false);\r\n\r\n  useEffect(()=>{\r\n    const timer = !(payCode && payCode?.orderId) || setInterval(()=>{\r\n      let params = {orderId: payCode.orderId}\r\n      if(!!teamId){\r\n        params.teamId = teamId\r\n      }\r\n      team_705_get_order_status(params).then(result => {\r\n        if(result.resultCode == 200){\r\n          if(result.statusType == eOrderStatus.Status_1_Paid){\r\n            clearInterval(timer);\r\n            onOk && onOk(result.teamId);\r\n            globalUtil.success('购买成功');\r\n          }\r\n        }\r\n      })\r\n    },1000)\r\n\r\n    return () => {\r\n      clearInterval(timer)\r\n    }\r\n  },[payCode])\r\n\r\n  useEffect(()=>{\r\n    if(payMethod){}\r\n  },[payMethod])\r\n\r\n  return (\r\n  <Form\r\n    className=\"PayCreateTeam-form\"\r\n    form={form}\r\n    name=\"basic\"\r\n    // {...formItemLayout}\r\n    autoComplete=\"off\">\r\n\r\n    <Form.Item \r\n    noStyle\r\n    >\r\n      <div className=\"pay-images\">\r\n        <div className=\"image\">\r\n          <div className=\"image-header\">\r\n            <img width={24} height={24} src={require(\"@assets/images/createTeam/wxpay.png\")}/>微信\r\n          </div>\r\n          <div className=\"image-content\" style={{backgroundColor:'#04C161'}}>\r\n            {url\r\n             ? <Image \r\n                src={url}\r\n                width={120} \r\n                height={120}\r\n                preview={false} \r\n                placeholde={<TLoading />}\r\n               />\r\n              : <div className=\"invalid_QR_code\">二维码无效</div>\r\n            }\r\n          </div>\r\n        </div>\r\n        <div className=\"image\">\r\n          <div className=\"image-header\">\r\n            <img width={24} height={24} src={require(\"@assets/images/createTeam/alipay.png\")}/>支付宝\r\n          </div>\r\n          <div className=\"image-content\" style={{backgroundColor:'#1273FF'}}>\r\n            {url1\r\n             ? <Image \r\n                src={url1} \r\n                width={120} \r\n                height={120} \r\n                preview={false} \r\n                placeholde={<TLoading />}\r\n               />\r\n              : <div className=\"invalid_QR_code\">二维码无效</div>\r\n            }\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"pay-tips\">\r\n        <span className=\"create-team-price-color\" style={{marginLeft:10,marginRight:10}}>￥</span>\r\n        <span className=\"ant-form-text create-team-discountPrice create-team-price-color\">{priceInfo.payPrice}</span>\r\n      </div>\r\n    </Form.Item>\r\n\r\n  </Form>) \r\n}\r\n\r\nfunction PayCreateTeamModal({teamId, visible=false, onCancel, priceInfo, onOk, payCode, url, url1}) {\r\n  return <DraggablePopUp\r\n    className=\"createTeamDraModal\"\r\n    title={\r\n      <span style={{ display: 'flex', alignItems: 'center' }}>\r\n        扫码支付\r\n        <span className=\"fontsize-12\" style={{ marginLeft: 10, color: '#999' }}>\r\n          流水编号：{payCode?.orderId||''}\r\n        </span>\r\n      </span>\r\n    }\r\n    width={520}\r\n    open={visible}\r\n    onCancel={onCancel}\r\n    centered\r\n    footer={null}\r\n    maskClosable={false}\r\n    destroyOnClose>\r\n      <PayCreateTeam \r\n      priceInfo={priceInfo} \r\n      teamId={teamId}\r\n      onOk={onOk} \r\n      payCode={payCode} \r\n      url={url}\r\n      url1={url1}/>\r\n  </DraggablePopUp>\r\n}\r\n\r\nfunction PriceDetailModal({visible=false,onCancel}) {\r\n  return <DraggablePopUp\r\n    className=\"createTeamDraModal\"\r\n    title=\"价格详情\"\r\n    width={800}\r\n    open={visible}\r\n    onCancel={onCancel}\r\n    centered\r\n    footer={null}\r\n    maskClosable={false}\r\n    destroyOnClose>\r\n      <PriceDetailTable/>\r\n  </DraggablePopUp>\r\n}\r\n\r\nfunction PriceDetailTable({}){\r\n  const [dataSource,setDataSource] = useState([]);\r\n  const columns = [\r\n    {title: '应用',dataIndex: 'productName', key: 'productName'},\r\n    {title: '原价',dataIndex: 'oldPrice', key: 'oldPrice'},\r\n    {title: '现价',dataIndex: 'nowPrice', key: 'nowPrice'},\r\n    {title: '购买日期',dataIndex: 'buyDate', key: 'buyDate'},\r\n    {title: '有效期至',dataIndex: 'expiration', key: 'expiration'},\r\n    {title: '剩余时长(天)',dataIndex: 'remainingDuration', key: 'remainingDuration'},\r\n    {title: '原价：扩容单人',dataIndex: 'oldPersonalPrice', key: 'oldPersonalPrice'},\r\n    {title: '现价：扩容单人',dataIndex: 'nowPersonalPrice', key: 'nowPersonalPrice'},\r\n    {title: '折扣减',dataIndex: 'rate', key: 'rate'},\r\n  ]\r\n\r\n  return (\r\n    <Table \r\n      size=\"small\"\r\n      columns={columns}\r\n      dataSource={dataSource}\r\n      pagination={{\r\n        position: ['bottomCenter'],\r\n        size: 'small',\r\n        showSizeChanger: true,\r\n        showQuickJumper: true,\r\n        total: dataSource.length,\r\n        showTotal: (total)=>{return `共${total}条`}\r\n      }}/>\r\n  ) \r\n}\r\n\r\nexport default function CreateTeamModal({visible=false,onCancel,teamId,onOk,type=CREATETYPE_CREATE,priceData, productList}) {\r\n  const [isChange,setIsChange] = useState(false);\r\n  // 新增：动态设置浏览器标题\r\n  const prevTitle = useRef(document.title);\r\n  useEffect(() => {\r\n    if (visible) {\r\n      prevTitle.current = document.title;\r\n      document.title = type === CREATETYPE_UPGRADE ? \"应用管理-购买\" : \"新建团队\";\r\n    } else {\r\n      document.title = prevTitle.current;\r\n    }\r\n    return () => {\r\n      document.title = prevTitle.current;\r\n    };\r\n  }, [visible, type]);\r\n  function checkClose(){\r\n    if(isChange){\r\n      Modal.confirm({\r\n        title: '提示',\r\n        icon: <ExclamationCircleOutlined style={{ color: 'orange' }}/>,\r\n        centered: true,\r\n        content: <div style={{ display: 'flex', flexDirection: 'column' }}>\r\n          <span>正在{`${type == CREATETYPE_CREATE ? '创建团队' : '购买应用'}`}，是否放弃{`${type == CREATETYPE_CREATE ? '创建' : '购买'}`} ？</span>\r\n        </div>,\r\n        okText: '是',\r\n        cancelText: '否',\r\n        onOk:() =>{\r\n          onCancel();\r\n          setIsChange(false);\r\n        }\r\n      });\r\n    }else{\r\n      onCancel();\r\n    }\r\n  }\r\n  return <DraggableDrawer\r\n            className=\"tms-drawer createTeamDrawer\"\r\n            centered\r\n            title={type === CREATETYPE_UPGRADE?\"应用管理-购买\":\"新建团队\"}\r\n            width={'60%'}\r\n            minWidth={'50%'}\r\n            maxWidth={'95%'}\r\n            open={visible}\r\n            onClose={checkClose}\r\n            footer={null}\r\n            destroyOnClose>\r\n              <CreateTeam teamId={teamId}\r\n                          onCancel={onCancel}\r\n                          onOk={onOk}\r\n                          type={type}\r\n                          priceData={priceData}\r\n                          productList={productList}\r\n                          setIsChange={setIsChange}/>\r\n        </DraggableDrawer>\r\n}\r\n\r\n/*unused\r\n//应用续费、应用扩容、应用新购\r\nexport function BuyTeamModal({visible=false,onCancel,teamId,onOk,type=CREATETYPE_EXPAND,defaultPackageList=[]}) {\r\n  const [createType,setCreateType] = useState(CREATETYPE_EXPAND)\r\n  const [loading,setLoading] = useState(true)\r\n  const [_defaultPackageList,setDefaultPackageList] = useState([])\r\n  const [hideBuy,setHideBuy] = useState(false);\r\n\r\n  useEffect(()=>{\r\n    if(visible){\r\n      setLoading(true)\r\n      getProductsList();\r\n      setDefaultPackageList(defaultPackageList)\r\n      setCreateType(type)\r\n    }\r\n  },[visible])\r\n\r\n  useEffect(()=>{\r\n    if(visible){\r\n      setLoading(true)\r\n      if(type === createType){\r\n        console.log(\"defaultPackageList\",defaultPackageList)\r\n        setDefaultPackageList(defaultPackageList)\r\n      }else{\r\n        setDefaultPackageList([])\r\n      }\r\n      setTimeout(()=>{\r\n        setLoading(false)\r\n      },300)\r\n    }\r\n  },[createType])\r\n\r\n  async function getProductsList() {\r\n    await team_711_get_team_product_list({teamId}).then(res => {\r\n        if(res.resultCode == 200){\r\n            let allProductsList = (res.productList||[])\r\n            setHideBuy(allProductsList.filter(product => !product.expirationDt).length == 0)\r\n        }\r\n    });\r\n    setTimeout(()=>{\r\n      setLoading(false)\r\n    },300)\r\n}\r\n\r\n  return <DraggablePopUp\r\n    className=\"createTeamDraModal\"\r\n    title={\r\n      <Radio.Group className=\"createTeamTypeRadio\" value={createType} onChange={(e)=>{setCreateType(Number(e.target.value))}}>\r\n        <Radio.Button value={CREATETYPE_EXPAND}>成员扩容</Radio.Button>\r\n        <Radio.Button value={CREATETYPE_RENEWAL}>应用续费</Radio.Button>\r\n        {!hideBuy && <Radio.Button value={CREATETYPE_BUY}>应用新购</Radio.Button>}\r\n      </Radio.Group>\r\n    }\r\n    width={1000}\r\n    open={visible}\r\n    onCancel={onCancel}\r\n    maskClosable={false}\r\n    footer={null}\r\n    centered\r\n    destroyOnClose>\r\n      {loading?\r\n        <TLoading/>\r\n        :\r\n        <CreateTeam teamId={teamId}\r\n                    onCancel={onCancel}\r\n                    onOk={onOk}\r\n                    type={createType}\r\n                    defaultPackageList={_defaultPackageList}/>}\r\n    </DraggablePopUp>\r\n}\r\n*/\r\n"], "mappings": ";;;;;AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,KAAK,EAACC,IAAI,EAACC,KAAK,EAACC,KAAK,EAACC,KAAK,EAACC,WAAW,EAACC,GAAG,EAACC,KAAK,EAACC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC5I,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACjH,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,mBAAmB,EAACC,aAAa,EAAEC,yBAAyB,EAAEC,sBAAsB,EAAEC,YAAY,QAAQ,mBAAmB;AACtI,SACEC,mBAAmB,EACnBC,qBAAqB,EACrBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,yBAAyB,EACzBC,2BAA2B,QAAS,kBAAkB;AACxD,SACEC,iCAAiC,EACjCC,+BAA+B,EAC/BC,0CAA0C,EAC1CC,0BAA0B,QACrB,yBAAyB;AAChC,SAAQC,WAAW,QAAO,cAAc;AACxC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAO,mBAAmB;AAC1B,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,UAAU,EAAEC,cAAc,QAAQ,oBAAoB;AAC3G,OAAOC,eAAe,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAM;EAAEC;AAAO,CAAC,GAAGlD,KAAK;AACxB,MAAM;EAAEmD,MAAM;EAACC;AAAY,CAAC,GAAG7C,KAAK;AAEpC,OAAO,MAAM8C,iBAAiB,GAAG,CAAC,CAAC,CAAC;AACpC,OAAO,MAAMC,kBAAkB,GAAG,CAAC,CAAC,CAAC;AACrC;AACA;AACA;;AAEA,MAAMC,cAAc,GAAG;EACrBC,QAAQ,EAAE;IACRC,IAAI,EAAE;EACR;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAAC;EAACC,QAAQ;EAACC,IAAI;EAACC,MAAM;EAACC,IAAI;EAACC,SAAS;EAACC,WAAW;EAACC;AAAW,CAAC,EAAC;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;EACvF,MAAM;IAACX,MAAM,EAAEY;EAAW,CAAC,GAAGzD,SAAS,CAAC,CAAC;EACzC,MAAM0D,WAAW,GAAGrC,UAAU,CAAC;IAC7BsC,OAAO,EAAE,CACP5C,+BAA+B,CAAC,CAAC,EACjCC,0CAA0C,CAAC,CAAC,EAC5C;MACE,GAAGF,iCAAiC,CAAC+B,MAAM,CAAC;MAC5Ce,OAAO,EAAE,CAAC,CAACf;IACb;IACA;IAAA;EAEJ,CAAC,CAAC;EACF,MAAM,CAACgB,aAAa,EAAEC,aAAa,EAAEC,gBAAgB,CAAE,GAAGL,WAAW;EACrE,MAAM;IAAEM,IAAI,EAAE;MAAEC;IAAW,CAAC,GAAG;MAAEA,UAAU,EAAE;IAAG,CAAC;IAAEC,OAAO,EAAEC;EAA0B,CAAC,GAAGlD,0BAA0B,CAAC4B,MAAM,CAAC;EAC5H,MAAMuB,QAAQ,GAAGrE,WAAW,CAAC,CAAC;EAC9B,MAAMsE,QAAQ,GAAGpE,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqE,IAAI,CAAC,GAAGvF,IAAI,CAACwF,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,YAAY,CAAC,GAAGzF,IAAI,CAACwF,OAAO,CAAC,CAAC;EACrC,MAAM,CAACE,UAAU,EAACC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC;EAEjD,MAAM,CAAC+F,SAAS,EAACC,YAAY,CAAC,GAAGhG,QAAQ,CAAC;IACxCiG,aAAa,EAAE,CAAC;IAAEC,UAAU,EAAE,CAAC;IAAEC,QAAQ,EAAE,CAAC;IAAEC,iBAAiB,EAAE,CAAC;IAAEC,QAAQ,EAAE,CAAC;IAAEC,eAAe,EAAE,CAAC;IAAEC,UAAU,EAAE,IAAI;IAAEC,aAAa,EAAE;EACxI,CAAC,CAAC;EAEF,MAAMC,qBAAqB,GAAG,IAAI,CAAC,CAAC;;EAEpC;EACA,MAAM,CAACC,QAAQ,EAACC,WAAW,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAC9C,MAAM,CAAC4G,OAAO,EAACC,UAAU,CAAC,GAAG7G,QAAQ,CAAC,CAAC;EACvC,MAAM,CAAC8G,eAAe,EAACC,kBAAkB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;EAC5D,MAAMgH,MAAM,GAAG7G,IAAI,CAAC8G,QAAQ,CAAC,qBAAqB,EAAEvB,IAAI,CAAC;EACzD,MAAM,CAACwB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnH,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACoH,OAAO,EAACC,UAAU,CAAC,GAAGrH,QAAQ,CAAC;IAACsH,OAAO,EAAE;EAAI,CAAC,CAAC;EACtD;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGxH,QAAQ,CAAC,CAAC;EAChC,MAAM,CAACyH,IAAI,EAAEC,OAAO,CAAC,GAAG1H,QAAQ,CAAC,CAAC;EAClC,MAAM,CAAC2H,OAAO,EAACC,UAAU,CAAC,GAAG5H,QAAQ,CAAC,KAAK,CAAC;EAC5C,MAAM6H,eAAe,GAAG5H,MAAM,CAAC,CAAC,CAAC,CAAC;EAClC,MAAM,CAAC6H,mBAAmB,EAACC,sBAAsB,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EACjE,MAAM,CAACgI,OAAO,EAACC,UAAU,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;EAC5C,MAAM,CAACkI,YAAY,EAACC,eAAe,CAAC,GAAGnI,QAAQ,CAAC,IAAI,CAAC;EACrD,MAAM,CAACoI,aAAa,EAACC,gBAAgB,CAAC,GAAGrI,QAAQ,CAAC,KAAK,CAAC;EACxD,MAAM,CAACsI,YAAY,EAACC,eAAe,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAI;IACZ,IAAGmE,IAAI,KAAKT,iBAAiB,EAAC;MAC5B,IAAI+E,GAAG,GAAGrD,gBAAgB,CAACC,IAAI;MAC/B,IAAIoD,GAAG,IAAIA,GAAG,CAACjC,UAAU,KAAK,GAAG,EAAE;QACjCb,IAAI,CAAC+C,aAAa,CAAC,UAAU,EAAED,GAAG,CAACE,IAAI,CAAC;MAC1C;IACF;EACF,CAAC,EAAC,CAACvD,gBAAgB,CAACwD,aAAa,CAAC,CAAC;EAEnC5I,SAAS,CAAC,MAAM;IACd,IAAG,CAAC,EAACoE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEyE,IAAI,KAAIzE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE0E,mBAAmB,EAAE;MACtDV,eAAe,CAAC;QAACW,UAAU,EAAE3E,SAAS,CAACyE;MAAI,CAAC,CAAC;IAC/C;EACF,CAAC,EAAC,CAACzE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyE,IAAI,CAAC,CAAC;EAEpB7I,SAAS,CAAC,MAAI;IACZ,IAAG,CAACsF,UAAU,IAAE,EAAE,EAAE0D,MAAM,GAAG,CAAC,EAAC;MAC7BR,eAAe,CAAC,CAAC,GAAGlD,UAAU,CAAC,CAAC;IAClC;EACF,CAAC,EAAC,CAAC2D,IAAI,CAACC,SAAS,CAAC5D,UAAU,CAAC,CAAC,CAAC;EAE/BtF,SAAS,CAAC,MAAI;IAAA,IAAAmJ,mBAAA;IACZ,IAAG,CAAC,EAAAA,mBAAA,GAAAjE,aAAa,CAACG,IAAI,cAAA8D,mBAAA,uBAAlBA,mBAAA,CAAoB9E,WAAW,KAAE,EAAE,EAAE2E,MAAM,GAAG,CAAC,EAAC;MAClD,IAAII,UAAU,GAAGlE,aAAa,CAACG,IAAI,CAAChB,WAAW,CAACgF,MAAM,CAACC,KAAK,IAAIrC,MAAM,IAAI,CAAC,GAAGqC,KAAK,CAACC,OAAO,IAAIxG,eAAe,CAACyG,SAAS,GAAG,IAAI,CAAC;MAChI,IAAIC,YAAY,GAAG,EAAE;MACrBL,UAAU,CAACM,OAAO,CAACC,OAAO,IAAI;QAC5B,IAAIC,IAAI,GAAGH,YAAY,CAACI,IAAI,CAACC,QAAQ,IAAIH,OAAO,CAACJ,OAAO,IAAIO,QAAQ,CAACP,OAAO,CAAC;QAC7E,IAAG,CAACK,IAAI,EAAC;UACPH,YAAY,CAACM,IAAI,CAAC;YAACC,SAAS,EAAEL,OAAO,CAACK,SAAS;YAAET,OAAO,EAAEI,OAAO,CAACJ,OAAO;YAAEU,QAAQ,EAAE,CAACN,OAAO;UAAC,CAAC,CAAC;QAClG,CAAC,MAAI;UACHC,IAAI,CAACK,QAAQ,CAACF,IAAI,CAACJ,OAAO,CAAC;QAC7B;MACF,CAAC,CAAC;MACF,IAAIO,eAAe,GAAG,EAAE;MACxB7F,WAAW,GAAGA,WAAW,IAAI,EAAE,CAAC,CAAC;MACjCoF,YAAY,CAACC,OAAO,CAACS,QAAQ,IAAI;QAC/B,IAAIC,YAAY,GAAGD,QAAQ,CAACF,QAAQ,CAACI,GAAG,CAAC,CAACC,MAAM,EAACC,KAAK,KAAK;UAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,KAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;UACzD;UACA,IAAIC,YAAY,GAAG/G,IAAI,KAAKR,kBAAkB,IAAI,EAAA6G,iBAAA,GAACnG,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAX,iBAAA,uBAAlEA,iBAAA,CAAoEY,OAAO,KAAK,CAAC,GACnI,CAAC,EAAAX,kBAAA,GAAApG,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAV,kBAAA,uBAAlEA,kBAAA,CAAoEY,OAAO,KAAI,CAAC,IAAI,CAAC,IAAAX,kBAAA,GACnFrG,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAT,kBAAA,uBAAlEA,kBAAA,CAAoEW,OAAO,GACzE,CAAC,GACH,CAAC;UAEL,IAAIC,gBAAgB,GAAGnH,IAAI,KAAKR,kBAAkB,IAAI,EAAAgH,kBAAA,GAACtG,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAR,kBAAA,uBAAlEA,kBAAA,CAAoES,OAAO,KAAK,CAAC,GACvI,CAAC,GAAAR,kBAAA,GAACvG,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAP,kBAAA,eAAlEA,kBAAA,CAAoEW,YAAY,KAAAV,kBAAA,GAC/ExG,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAN,kBAAA,uBAAlEA,kBAAA,CAAoEU,YAAY,GAC9E,EAAE,GACJ,EAAE;UAEN,IAAIC,OAAO,IAAAV,KAAA,GAAG,CAAC/C,mBAAmB,IAAE,EAAE,EAAE8B,IAAI,CAAC4B,MAAM,IAAIA,MAAM,CAACN,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAL,KAAA,uBAA9EA,KAAA,CAAgFU,OAAO;UACrG,IAAI5B,IAAI,GAAG;YACT,GAAGU,MAAM;YACToB,GAAG,EAAEpB,MAAM,CAACa,SAAS;YACrBQ,SAAS,EAAEpB,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK;YACpCqB,WAAW,EAAErB,KAAK,IAAI,CAAC,GAAGJ,QAAQ,CAACF,QAAQ,CAACjB,MAAM,GAAG,CAAC;YACtD6C,QAAQ,EAAEX,YAAY,GAAG,CAAC,IAAI,CAAC,CAACI,gBAAgB,IAAIQ,cAAc,CAACR,gBAAgB,CAAC,GAAG,CAAC,GAAGJ,YAAY,GAAG,CAAC;YAC3Ga,YAAY,EAAEb,YAAY,GAAG,CAAC,IAAI,CAAC,CAACI,gBAAgB,IAAIQ,cAAc,CAACR,gBAAgB,CAAC,GAAG,CAAC,GAAGJ,YAAY,GAAG,CAAC;YAC/GA,YAAY,EAAEA,YAAY;YAC1Bc,YAAY,EAAE7H,IAAI,KAAKR,kBAAkB,IACrC,EAAAoH,kBAAA,GAAC1G,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAJ,kBAAA,uBAAlEA,kBAAA,CAAoEK,OAAO,KAAK,CAAC,GACjF,CAAC,EAAAJ,kBAAA,GAAA3G,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAH,kBAAA,uBAAlEA,kBAAA,CAAoEK,OAAO,KAAI,CAAC,IAAI,CAAC,IAAAJ,kBAAA,GACnF5G,WAAW,CAACwF,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACwB,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAAF,kBAAA,uBAAlEA,kBAAA,CAAoEI,OAAO,GAAG,CAAC,GAC/E,CAAC;YACTY,aAAa,EAAE,IAAI;YACnBtD,IAAI,EAAE,EAAE;YACRuD,MAAM,EAAE,EAAE;YACVC,aAAa,EAAE,EAAE;YACjBZ,YAAY,EAAE,EAAE;YAChBD,gBAAgB,EAAEA,gBAAgB;YAClCpF,aAAa,EAAE,EAAE;YACjBkG,aAAa,EAAE,EAAE;YACjBZ,OAAO,EAAElB,MAAM,CAACa,SAAS,IAAInI,UAAU,CAACqJ,YAAY,GAAGvG,UAAU,GAC9D0F,OAAO,IAAI,IAAI,IAAIA,OAAO,IAAI,KAAK,GAAIA,OAAO,GAC5ClB,MAAM,CAACgC,UAAU,IAAIrJ,cAAc,CAACsJ,WAAW,IAAIjC,MAAM,CAACgC,UAAU,IAAIrJ,cAAc,CAACuJ,mBAAmB,GAAI,KAAK,GAAG;UAC7H,CAAC;UACD,OAAO5C,IAAI;QACb,CAAC,CAAC;QACFM,eAAe,GAAGA,eAAe,CAACuC,MAAM,CAACrC,YAAY,CAAC;MACxD,CAAC,CAAC;MACF,IAAG,CAAC,EAAChG,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEsI,WAAW,GAAC;QAC1B,IAAIC,KAAK,GAAG1D,IAAI,CAAC2D,KAAK,CAACxI,SAAS,CAACsI,WAAW,CAAC;QAC7CxC,eAAe,GAAGA,eAAe,CAACG,GAAG,CAACC,MAAM,IAAI;UAAA,IAAAuC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;UAC9C1C,MAAM,CAACuB,QAAQ,IAAAgB,WAAA,GAAGF,KAAK,CAAC9C,IAAI,CAACoD,CAAC,IAAIA,CAAC,CAAC9B,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAA0B,WAAA,uBAAhDA,WAAA,CAAkDhB,QAAQ;UAC5EvB,MAAM,CAAC2B,aAAa,IAAAa,YAAA,GAAGH,KAAK,CAAC9C,IAAI,CAACoD,CAAC,IAAIA,CAAC,CAAC9B,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAA2B,YAAA,uBAAhDA,YAAA,CAAkDb,aAAa;UACtF3B,MAAM,CAAC3B,IAAI,IAAAoE,YAAA,GAAGJ,KAAK,CAAC9C,IAAI,CAACoD,CAAC,IAAIA,CAAC,CAAC9B,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAA4B,YAAA,uBAAhDA,YAAA,CAAkDpE,IAAI;UACpE2B,MAAM,CAAC4B,MAAM,IAAAc,YAAA,GAAGL,KAAK,CAAC9C,IAAI,CAACoD,CAAC,IAAIA,CAAC,CAAC9B,SAAS,IAAIb,MAAM,CAACa,SAAS,CAAC,cAAA6B,YAAA,uBAAhDA,YAAA,CAAkDd,MAAM;UACxE,OAAO5B,MAAM;QACf,CAAC,CAAC;MACJ;MACAtC,sBAAsB,CAAC,CAAC,GAAGkC,eAAe,CAAC,CAAC;MAC5C,IAAG,CAAC,EAAC9F,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEsI,WAAW,GAAC;QAC1BQ,UAAU,CAAC,MAAI;UACbC,wBAAwB,CAAC/I,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyE,IAAI,EAACqB,eAAe,CAAC;QAC3D,CAAC,EAAC,IAAI,CAAC;MACT;IACF;EACF,CAAC,EAAC,CAACjB,IAAI,CAACC,SAAS,CAAChE,aAAa,CAAC,EAAC+B,MAAM,CAAC,CAAC;EAEzC,SAASmG,cAAcA,CAACC,CAAC,EAAC3B,GAAG,EAAC;IAC5B,IAAIF,OAAO,GAAG6B,CAAC,CAACC,MAAM,CAAC9B,OAAO;IAC9B,IAAI5B,IAAI,GAAG7B,mBAAmB,CAAC8B,IAAI,CAACxE,IAAI,IAAIA,IAAI,CAACqG,GAAG,IAAIA,GAAG,CAAC;IAC5D,IAAG,CAAC,CAAC9B,IAAI,EAAC;MACR,IAAG8B,GAAG,IAAI1I,UAAU,CAACqJ,YAAY,EAAC;QAChCtG,aAAa,CAACyF,OAAO,CAAC;MACxB;MACA5B,IAAI,CAAC4B,OAAO,GAAGA,OAAO;MACtB,IAAI+B,IAAI,GAAG,EAAE;MACb,IAAG/B,OAAO,EAAC;QACT+B,IAAI,GAAG,CAAC3D,IAAI,CAAC4D,YAAY,IAAE,EAAE,EAAEC,KAAK,CAAC,GAAG,CAAC,CAACpE,MAAM,CAACqE,UAAU,IAAI,CAAC,CAACA,UAAU,CAAC;MAC9E,CAAC,MAAI;QACHH,IAAI,GAAG,CAAC3D,IAAI,CAAC+D,aAAa,IAAE,EAAE,EAAEF,KAAK,CAAC,GAAG,CAAC,CAACpE,MAAM,CAACqE,UAAU,IAAI,CAAC,CAACA,UAAU,CAAC;MAC/E;MACAH,IAAI,CAAC7D,OAAO,CAACgE,UAAU,IAAI;QACzB,IAAIE,KAAK,GAAG7F,mBAAmB,CAAC8B,IAAI,CAACxE,IAAI,IAAIA,IAAI,CAACqG,GAAG,IAAIgC,UAAU,CAAC;QACpEE,KAAK,CAACpC,OAAO,GAAGA,OAAO;MACzB,CAAC,CAAC;MACFxD,sBAAsB,CAAC,CAAC,GAAGD,mBAAmB,CAAC,CAAC;IAClD;EACF;EAEA,SAAS8F,cAAcA,CAACjE,IAAI,EAACkE,SAAS,EAAC;IACrC,IAAIzI,IAAI,GAAG0C,mBAAmB,CAAC8B,IAAI,CAACxE,IAAI,IAAIA,IAAI,CAACqG,GAAG,IAAI9B,IAAI,CAAC8B,GAAG,CAAC;IACjE,IAAGoC,SAAS,IAAI,CAAC,EAAC;MAChB,IAAG3J,IAAI,IAAIT,iBAAiB,IAAI2B,IAAI,CAACwG,QAAQ,IAAI,CAAC,EAAC;QACjD;MACF;MACA,IAAG1H,IAAI,IAAIR,kBAAkB,IAAI0B,IAAI,CAACwG,QAAQ,IAAI,CAACxG,IAAI,CAAC6F,YAAY,EAAC;QACnE;MACF;IACF;IACA,IAAG4C,SAAS,IAAI,CAAC,EAAC;MAChB,IAAGzI,IAAI,CAACwG,QAAQ,IAAI,IAAI,EAAC;QACvB;MACF;IACF;IACAxG,IAAI,CAACwG,QAAQ,GAAGiC,SAAS,IAAI,CAAC,GAAGzI,IAAI,CAACwG,QAAQ,GAAG,CAAC,GAAGiC,SAAS,IAAI,CAAC,GAAGzI,IAAI,CAACwG,QAAQ,GAAG,CAAC,GAAIxG,IAAI,CAAC0G,YAAY,IAAE,CAAE;IAChH1G,IAAI,CAAC0G,YAAY,GAAG+B,SAAS,IAAI,CAAC,GAAGzI,IAAI,CAAC0G,YAAY,GAAG,CAAC,GAAG+B,SAAS,IAAI,CAAC,GAAGzI,IAAI,CAAC0G,YAAY,GAAG,CAAC,GAAI1G,IAAI,CAAC0G,YAAY,IAAE,CAAE;IAC5H,IAAGgC,YAAY,CAAC1I,IAAI,CAAC,IAAI,CAAC,EAAC;MACzB,IAAG,CAACA,IAAI,CAAC4G,aAAa,EAAC;QAAA,IAAA+B,MAAA,EAAAC,oBAAA,EAAAC,MAAA,EAAAC,oBAAA;QACrB9I,IAAI,CAAC4G,aAAa,GAAG,EAAE;QACvB5G,IAAI,CAACsD,IAAI,GAAG,EAAAqF,MAAA,IAAC,EAAAC,oBAAA,GAAA/I,aAAa,CAACG,IAAI,cAAA4I,oBAAA,uBAAlBA,oBAAA,CAAoBG,cAAc,KAAI,EAAE,EAAEvE,IAAI,CAACwE,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAI,EAAE,CAAC,cAAAN,MAAA,uBAA9EA,MAAA,CAAgFO,YAAY,KAAE,EAAE;QAC5GlJ,IAAI,CAAC6G,MAAM,GAAG,EAAAgC,MAAA,IAAC,EAAAC,oBAAA,GAAAjJ,aAAa,CAACG,IAAI,cAAA8I,oBAAA,uBAAlBA,oBAAA,CAAoBC,cAAc,KAAI,EAAE,EAAEvE,IAAI,CAACwE,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAI,EAAE,CAAC,cAAAJ,MAAA,uBAA9EA,MAAA,CAAgFM,aAAa,KAAE,EAAE;MACjH;MACA,IAAGnJ,IAAI,CAACwG,QAAQ,IAAI,CAAC,EAAC;QACpBxG,IAAI,CAAC4G,aAAa,GAAG,IAAI;QACzB5G,IAAI,CAACsD,IAAI,GAAG,EAAE;QACdtD,IAAI,CAAC6G,MAAM,GAAG,EAAE;MAClB;IACF;IACA,IAAIuC,QAAQ,GAAG9I,IAAI,CAAC+I,aAAa,CAAC,UAAU,CAAC;IAC7CC,QAAQ,CAACF,QAAQ,EAAC1G,mBAAmB,CAAC;IACtCoF,wBAAwB,CAAChF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEY,UAAU,CAAC;IAClDf,sBAAsB,CAAC,CAAC,GAAGD,mBAAmB,CAAC,CAAC;EAClD;EAEA,SAAS6G,eAAeA,CAAChF,IAAI,EAACiF,KAAK,EAAC;IAClC,IAAIxJ,IAAI,GAAG0C,mBAAmB,CAAC8B,IAAI,CAACxE,IAAI,IAAIA,IAAI,CAACqG,GAAG,IAAI9B,IAAI,CAAC8B,GAAG,CAAC;IACjErG,IAAI,CAACwG,QAAQ,GAAGgD,KAAK;IACrBxJ,IAAI,CAAC0G,YAAY,GAAG8C,KAAK;IACzB,IAAGd,YAAY,CAAC1I,IAAI,CAAC,IAAI,CAAC,EAAC;MACzB,IAAG,CAACA,IAAI,CAAC4G,aAAa,EAAC;QAAA,IAAA6C,MAAA,EAAAC,oBAAA,EAAAC,MAAA,EAAAC,oBAAA;QACrB5J,IAAI,CAAC4G,aAAa,GAAG,EAAE;QACvB5G,IAAI,CAACsD,IAAI,GAAG,EAAAmG,MAAA,IAAC,EAAAC,oBAAA,GAAA7J,aAAa,CAACG,IAAI,cAAA0J,oBAAA,uBAAlBA,oBAAA,CAAoBX,cAAc,KAAI,EAAE,EAAEvE,IAAI,CAACwE,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAI,EAAE,CAAC,cAAAQ,MAAA,uBAA9EA,MAAA,CAAgFP,YAAY,KAAE,EAAE;QAC5GlJ,IAAI,CAAC6G,MAAM,GAAG,EAAA8C,MAAA,IAAC,EAAAC,oBAAA,GAAA/J,aAAa,CAACG,IAAI,cAAA4J,oBAAA,uBAAlBA,oBAAA,CAAoBb,cAAc,KAAI,EAAE,EAAEvE,IAAI,CAACwE,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAI,EAAE,CAAC,cAAAU,MAAA,uBAA9EA,MAAA,CAAgFR,aAAa,KAAE,EAAE;MACjH;MACA,IAAG,CAACK,KAAK,IAAE,CAAC,KAAK,CAAC,EAAC;QACjBxJ,IAAI,CAAC4G,aAAa,GAAG,IAAI;QACzB5G,IAAI,CAACsD,IAAI,GAAG,EAAE;QACdtD,IAAI,CAAC6G,MAAM,GAAG,EAAE;MAClB;IACF;IACAlE,sBAAsB,CAAC,CAAC,GAAGD,mBAAmB,CAAC,CAAC;EAClD;EAEA,SAASmH,mBAAmBA,CAACtF,IAAI,EAACyE,KAAK,EAAC;IACtC,IAAIhJ,IAAI,GAAG0C,mBAAmB,CAAC8B,IAAI,CAACxE,IAAI,IAAIA,IAAI,CAACqG,GAAG,IAAI9B,IAAI,CAAC8B,GAAG,CAAC;IACjE,IAAG,CAAC,CAAC2C,KAAK,EAAC;MACThJ,IAAI,CAAC4G,aAAa,GAAGoC,KAAK,CAACC,QAAQ;MACnCjJ,IAAI,CAACsD,IAAI,GAAG0F,KAAK,CAACE,YAAY;MAC9BlJ,IAAI,CAAC6G,MAAM,GAAGmC,KAAK,CAACG,aAAa;MACjC,IAAGT,YAAY,CAAC1I,IAAI,CAAC,IAAI,CAAC,EAAC;QACzB,IAAGA,IAAI,CAACwG,QAAQ,IAAI,CAAC,EAAC;UACpBxG,IAAI,CAACwG,QAAQ,GAAG,CAAC;UACjBxG,IAAI,CAAC0G,YAAY,GAAG,CAAC;QACvB;MACF;IACF,CAAC,MAAI;MACH1G,IAAI,CAAC4G,aAAa,GAAG,IAAI;MACzB5G,IAAI,CAACsD,IAAI,GAAG,EAAE;MACdtD,IAAI,CAAC6G,MAAM,GAAG,EAAE;MAChB,IAAG6B,YAAY,CAAC1I,IAAI,CAAC,IAAI,CAAC,EAAC;QACzBA,IAAI,CAACwG,QAAQ,GAAG,CAAC;QACjBxG,IAAI,CAAC0G,YAAY,GAAG,CAAC;MACvB;IACF;IACA,IAAI0C,QAAQ,GAAG9I,IAAI,CAAC+I,aAAa,CAAC,UAAU,CAAC;IAC7CC,QAAQ,CAACF,QAAQ,EAAC1G,mBAAmB,CAAC;IACtCoF,wBAAwB,CAAChF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEY,UAAU,CAAC;IAClDf,sBAAsB,CAAC,CAAC,GAAGD,mBAAmB,CAAC,CAAC;EAClD;EAEA,SAASoH,cAAcA,CAAC9B,CAAC,EAAC;IACxBsB,QAAQ,CAACtB,CAAC,CAACC,MAAM,CAACuB,KAAK,EAAC9G,mBAAmB,CAAC;EAC9C;EAEA,SAAS4G,QAAQA,CAACF,QAAQ,EAACW,WAAW,EAAC;IACrC,IAAGjL,IAAI,IAAIT,iBAAiB,IAAI,CAAC,CAAC+K,QAAQ,EAAC;MACzCnK,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAI;MACH,IAAG8K,WAAW,CAAC/F,MAAM,CAACM,OAAO,IAAIA,OAAO,CAACkC,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAClC,OAAO,CAACsC,aAAa,CAAC,CAACjD,MAAM,GAAG,CAAC,EAAC;QAC5F1E,WAAW,CAAC,IAAI,CAAC;MACnB,CAAC,MAAI;QACHA,WAAW,CAAC,KAAK,CAAC;MACpB;IACF;EACF;EAEA,MAAM+K,KAAK,GAAG9M,WAAW,CAAC,YAAY;IACpC,IAAI+M,MAAM,GAAG3J,IAAI,CAAC4J,cAAc,CAAC,IAAI,CAAC;IACtC,IAAG,CAACD,MAAM,CAACb,QAAQ,EAAC;MAClB7L,UAAU,CAAC4M,OAAO,CAAC,SAAS,CAAC;MAC7B;IACF;IACA,IAAGvI,MAAM,IAAI,CAAC,IAAIc,mBAAmB,CAACsB,MAAM,CAACM,OAAO,IAAIA,OAAO,CAAC6B,OAAO,IAAI7B,OAAO,CAACwB,SAAS,IAAInI,UAAU,CAACyM,eAAe,IAAI9F,OAAO,CAACwB,SAAS,IAAInI,UAAU,CAAC0M,YAAY,CAAC,CAAC1G,MAAM,IAAI,CAAC,EAAC;MACtLpG,UAAU,CAAC4M,OAAO,CAAC,UAAU,CAAC;MAC9B;IACF;IACA,IAAIG,QAAQ,GAAG,KAAK;IACpB,IAAG3J,SAAS,CAACM,QAAQ,IAAI,CAAC,EAAC;MACzB,IAAGnC,IAAI,IAAIT,iBAAiB,EAAC;QAC3B,IAAGuD,MAAM,IAAI,CAAC,IAAI,CAACjB,SAAS,CAACO,eAAe,IAAE,CAAC,KAAK,CAAC,EAAC;UACpD3D,UAAU,CAAC4M,OAAO,CAAC,OAAO,CAAC;UAC3B;QACF;QACAG,QAAQ,GAAG,IAAI;MACjB,CAAC,MAAI;QACH,IAAG3J,SAAS,CAACM,QAAQ,GAAG,CAAC,EAAC;UACxB4B,UAAU,CAAC,IAAI,CAAC;UAChB;QACF;QACA,IAAGH,mBAAmB,CAACsB,MAAM,CAAChE,IAAI,IAAIA,IAAI,CAACwG,QAAQ,GAAG,CAAC,CAAC,CAAC7C,MAAM,IAAI,CAAC,IAAI,CAAChD,SAAS,CAACO,eAAe,IAAE,CAAC,KAAK,CAAC,EAAC;UAC1G3D,UAAU,CAAC4M,OAAO,CAAC,OAAO,CAAC;UAC3B;QACF;QACAG,QAAQ,GAAG,IAAI;MACjB;IACF;IACA,IAAIC,YAAY,GAAG7H,mBAAmB,CAAE;IAAA,CACrCsB,MAAM,CAAChE,IAAI,IAAIA,IAAI,CAACiH,UAAU,IAAIrJ,cAAc,CAACuJ,mBAAmB,KAAKnH,IAAI,CAACwG,QAAQ,IAAI,CAAC,IAAI,CAAC,CAACxG,IAAI,CAAC4G,aAAa,CAAC,CAAC,CACrH5B,GAAG,CAAChF,IAAI,KAAK;MACZ8F,SAAS,EAAE9F,IAAI,CAAC8F,SAAS;MACzB0E,cAAc,EAAE,CAAC,CAACxK,IAAI,CAAC4G,aAAa,GAAG5G,IAAI,CAAC4G,aAAa,GAAG,CAAC;MAC7D6D,aAAa,EAAEzK,IAAI,CAACwG;IACtB,CAAC,CAAC,CAAC;IACLhE,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIkI,QAAQ,GAAG;MACbtB,QAAQ,EAAEa,MAAM,CAACb,QAAQ;MACzBuB,SAAS,EAAE7L,IAAI,IAAIT,iBAAiB,GAAG,CAAC,GAAGS,IAAI,IAAIR,kBAAkB,GAAG,CAAC,GAAG;IAC9E,CAAC;IACD,IAAIoF,UAAU,GAAG,IAAI;IACrB,IAAG9B,MAAM,IAAI,CAAC,EAAC;MACb8I,QAAQ,CAAC1L,WAAW,GAAG,EAAE;MACzB0L,QAAQ,CAACE,aAAa,GAAGlI,mBAAmB,CAACsB,MAAM,CAACM,OAAO,IAAIA,OAAO,CAAC6B,OAAO,CAAC,CAACnB,GAAG,CAACV,OAAO,IAAI;QAAC,OAAOA,OAAO,CAACwB,SAAS;MAAA,CAAC,CAAC,EAAC;MAC3H;IACF,CAAC,MAAI;MACH4E,QAAQ,CAAC1L,WAAW,GAAGuL,YAAY;MACnC,IAAG,CAAC,CAACzH,YAAY,IAAInC,SAAS,CAACO,eAAe,GAAG,CAAC,EAAC;QACjDwC,UAAU,GAAGZ,YAAY,CAACY,UAAU;MACtC;IACF;IACA,IAAG4G,QAAQ,EAAC;MACV,MAAMO,sCAAsC,CAACH,QAAQ,EAAChH,UAAU,EAAC,CAAC,CAAC;IACrE,CAAC,MAAI;MACH,MAAMmH,sCAAsC,CAACH,QAAQ,EAAChH,UAAU,EAAC,CAAC,CAAC;IACrE;IACAjC,UAAU,CAACiJ,QAAQ,CAAC;IACpBlI,UAAU,CAAC,KAAK,CAAC;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC,EAAC,GAAG,CAAC;EAEN,MAAMsI,SAAS,GAAGA,CAAA,KAAM;IACtBvJ,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMwJ,SAAS,GAAIC,OAAO,IAAK;IAC7B3K,QAAQ,CAACnE,WAAW,CAACuD,WAAW,CAAC,CAAC;IAClC;IACAqL,SAAS,CAAC,CAAC;IACXnM,QAAQ,CAAC,CAAC;IACVC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,CAAC;IACR,IAAG,CAACC,MAAM,EAAC;MACT;MACA/D,KAAK,CAACmQ,OAAO,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,IAAI,eAAEpN,OAAA,CAAC5B,mBAAmB;UAACiP,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;QACzDC,OAAO,eAAE3N,OAAA;UAAKqN,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE;UAAS,CAAE;UAAAC,QAAA,gBAChE9N,OAAA;YAAA8N,QAAA,GAAM,2EAAa,EAACvL,IAAI,CAAC+I,aAAa,CAAC,UAAU,CAAC,EAAC,SAAE;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5D1N,OAAA;YAAA8N,QAAA,EAAM;UAAc;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3B1N,OAAA;YAAA8N,QAAA,EAAM;UAAc;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;QACNK,MAAM,EAAE,QAAQ;QAChBC,UAAU,EAAE,GAAG;QACfnN,IAAI,EAACA,CAAA,KAAK;UACRwB,QAAQ,CAAC,SAAS4K,OAAO,EAAE,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMgB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAGlK,mBAAmB,EAAE;IACxBC,sBAAsB,CAAC,CAACD,mBAAmB,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMmK,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlK,sBAAsB,CAAC,CAACD,mBAAmB,CAAC;IAC5CtB,YAAY,CAAC0L,WAAW,CAAC,CAAC;IAC1BzJ,eAAe,CAAC0J,OAAO,GAAG,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAC5C,KAAK,EAAE6C,KAAK,KAAK;IACjD,IAAGA,KAAK,CAACC,WAAW,CAACxN,IAAI,KAAK,OAAO,IAAI,CAAC0K,KAAK,EAAE;MAC/C;MACA,IAAG,CAACA,KAAK;QAAE;QACX;IACF;IACA5M,yBAAyB,CAAC;MAAC8G,UAAU,EAAE8F;IAAM,CAAC,CAAC,CAAC+C,IAAI,CAACnJ,GAAG,IAAI;MAC1D,IAAGA,GAAG,CAACjC,UAAU,IAAI,GAAG,EAAC;QACvBX,YAAY,CAAC0L,WAAW,CAAC,CAAC;QAC1BzJ,eAAe,CAAC0J,OAAO,GAAG,CAAC,CAAC;QAC5B,IAAG,CAACxL,SAAS,CAACM,QAAQ,IAAE,CAAC,IAAI,CAAC,EAAC;UAC7B6G,wBAAwB,CAAC,CAAC,EAAChF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEY,UAAU,IAAGZ,YAAY,CAACY,UAAU,GAAG8F,KAAK,EAAC,EAAE,EAAC,EAAC1G,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEY,UAAU,EAAC;QACrH,CAAC,MAAI;UACHvD,yBAAyB,CAAC,CAAC;QAC7B;MACF;MACA,IAAGiD,GAAG,CAACjC,UAAU,IAAI,GAAG,EAAC;QACvB5D,UAAU,CAACiP,KAAK,CAACpJ,GAAG,CAAChC,aAAa,CAAC;MACrC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,SAASqL,IAAIA,CAACC,YAAY,GAAC,EAAE,EAACC,QAAQ,GAAC,EAAE,EAAC;IACxC,IAAIC,YAAY,GAAG,CAACD,QAAQ,CAAChJ,MAAM,GAAG,CAAC,GAAGgJ,QAAQ,GAAGjK,mBAAmB,EAAEsB,MAAM,CAAChE,IAAI,IAAI;MACvF,IAAGlB,IAAI,IAAIT,iBAAiB,EAAC;QAC3B,OAAQ2B,IAAI,CAACwG,QAAQ,GAAG,CAAC,IAAI,CAAC,CAACxG,IAAI,CAAC4G,aAAa;MACnD,CAAC,MAAI;QACH,IAAG,CAAC,CAAC8B,YAAY,CAAC1I,IAAI,CAAC,EAAC;UACtB,OAAQA,IAAI,CAACwG,QAAQ,GAAG,CAAC,IAAI,CAAC,CAACxG,IAAI,CAAC4G,aAAa;QACnD;QACA,OAAQ5G,IAAI,CAACwG,QAAQ,IAAI,CAAC,IAAI,CAAC,CAACxG,IAAI,CAAC4G,aAAa;MACpD;IACF,CAAC,CAAC,CAAC5B,GAAG,CAAChF,IAAI,KAAK;MACd8F,SAAS,EAAE9F,IAAI,CAAC8F,SAAS;MACzB0E,cAAc,EAAE,CAAC,CAACxK,IAAI,CAAC4G,aAAa,GAAG5G,IAAI,CAAC4G,aAAa,GAAG,CAAC;MAC7D6D,aAAa,EAAEzK,IAAI,CAACwG;IACtB,CAAC,CAAC,CAAC;IACH,IAAGoG,YAAY,CAACjJ,MAAM,IAAI,CAAC,EAAC;MAC1B,IAAG,CAAC,CAAC+I,YAAY,EAAC;QAChBnP,UAAU,CAAC4M,OAAO,CAAC,OAAO,CAAC;MAC7B;MACA,OAAO,EAAE;IACX;IACA,OAAOyC,YAAY;EACrB;;EAEA;EACA,MAAM9E,wBAAwB,GAAG5K,WAAW,CAAC,CAACwP,YAAY,GAAC,EAAE,EAAEC,QAAQ,GAAC,EAAE,EAAEE,eAAe,GAAC,KAAK,KAAG;IAClG,IAAID,YAAY,GAAGH,IAAI,CAACC,YAAY,EAACC,QAAQ,CAAC;IAC9C,IAAGC,YAAY,CAACjJ,MAAM,IAAI,CAAC,EAAC;MAC1B/C,YAAY,CAAC;QACXC,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbC,QAAQ,EAAE,CAAC;QACXC,iBAAiB,EAAE,CAAC;QACpBC,QAAQ,EAAE,CAAC;QACXC,eAAe,EAAE,CAAC;QAClBC,UAAU,EAAE,IAAI;QAChBC,aAAa,EAAE;MACjB,CAAC,CAAC;MACFuB,sBAAsB,CAAC,CAAC,GAAG,CAACgK,QAAQ,CAAChJ,MAAM,GAAG,CAAC,GAAGgJ,QAAQ,GAAGjK,mBAAmB,EAAEsC,GAAG,CAAChF,IAAI,IAAI;QAC5FA,IAAI,CAAC8G,aAAa,GAAG,EAAE;QACvB9G,IAAI,CAACkG,YAAY,GAAG,EAAE;QACtBlG,IAAI,CAACa,aAAa,GAAG,EAAE;QACvBb,IAAI,CAAC+G,aAAa,GAAG,EAAE;QACvB/G,IAAI,CAAC2G,YAAY,GAAG3G,IAAI,CAAC6F,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC7F,IAAI,CAACiG,gBAAgB,GAAGjG,IAAI,CAAC6F,YAAY,GAAG,CAAC;QAC5F,OAAO7F,IAAI;MACb,CAAC,CAAC,CAAC,CAAC;MACJ;IACF;IACA,IAAI8M,OAAO,GAAG;MAAEjO,MAAM;MAAEG,WAAW,EAAE4N,YAAY;MAAEjC,SAAS,EAAE7L,IAAI,IAAIR,kBAAkB,GAAG,CAAC,GAAG,CAAC;MAAEoF,UAAU,EAAEgJ;IAAa,CAAC;IAC5HlQ,mBAAmB,CAACsQ,OAAO,CAAC,CAACP,IAAI,CAACQ,MAAM,IAAG;MACzC,IAAGA,MAAM,CAAC5L,UAAU,IAAI,GAAG,EAAC;QAC1B,MAAM;UAAC6L,UAAU;UAAEjM,QAAQ;UAAED,UAAU;UAAEmM,cAAc;UAAEC,MAAM;UAACC,SAAS;UAACC,gBAAgB;UAACnN;QAAU,CAAC,GAAG8M,MAAM;QAC/GnM,YAAY,CAAC;UACXC,aAAa,EAAEmM,UAAU;UACzBlM,UAAU,EAAEA,UAAU;UACtBC,QAAQ,EAAEA,QAAQ;UAClBC,iBAAiB,EAAEiM,cAAc;UACjChM,QAAQ,EAAEiM,MAAM;UAChBhM,eAAe,EAAEiM,SAAS;UAC1BhM,UAAU,EAAE,CAAC,CAACuL,YAAY,GAAGK,MAAM,CAAC5L,UAAU,GAAG,IAAI;UACrDC,aAAa,EAAE,CAAC,CAACsL,YAAY,GAAG,OAAO,GAAG;QAC5C,CAAC,CAAC;QACF,IAAGG,eAAe,EAAC;UACjB,IAAIQ,MAAM,GAAG,CAACpN,UAAU,IAAE,EAAE,EAAEuE,IAAI,CAAC8I,OAAO,IAAIA,OAAO,CAAC5J,UAAU,IAAIgJ,YAAY,CAAC;UACjF,IAAG,CAAC,CAACW,MAAM,IAAI,CAACE,YAAY,CAACF,MAAM,CAAC,EAAC;YACnCtK,eAAe,CAACsK,MAAM,CAAC;UACzB;QACF;QACAlK,eAAe,CAAC,CAAC,IAAIlD,UAAU,IAAE,EAAE,CAAC,CAAC,CAAC;QACtC,IAAG,CAACmN,gBAAgB,IAAE,EAAE,EAAEzJ,MAAM,GAAG,CAAC,EAAC;UACnC,CAACgJ,QAAQ,CAAChJ,MAAM,GAAG,CAAC,GAAGgJ,QAAQ,GAAGjK,mBAAmB,EAAE2B,OAAO,CAACrE,IAAI,IAAI;YACrE,IAAIwN,OAAO,GAAGJ,gBAAgB,CAAC5I,IAAI,CAACiJ,GAAG,IAAIA,GAAG,CAAC3H,SAAS,IAAI9F,IAAI,CAAC8F,SAAS,CAAC;YAC3E,IAAG,CAAC,CAAC0H,OAAO,EAAC;cACXxN,IAAI,CAAC8G,aAAa,GAAG0G,OAAO,CAACE,WAAW;cACxC1N,IAAI,CAACkG,YAAY,GAAGsH,OAAO,CAACtH,YAAY;cACxClG,IAAI,CAACa,aAAa,GAAG2M,OAAO,CAACR,UAAU;cACvChN,IAAI,CAAC+G,aAAa,GAAGyG,OAAO,CAACG,iBAAiB;cAC9C3N,IAAI,CAAC2G,YAAY,GAAG6G,OAAO,CAACxH,OAAO;YACrC,CAAC,MAAI;cACHhG,IAAI,CAAC8G,aAAa,GAAG,EAAE;cACvB9G,IAAI,CAACkG,YAAY,GAAG,EAAE;cACtBlG,IAAI,CAACa,aAAa,GAAG,EAAE;cACvBb,IAAI,CAAC+G,aAAa,GAAG,EAAE;cACvB/G,IAAI,CAAC2G,YAAY,GAAG3G,IAAI,CAAC6F,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC7F,IAAI,CAACiG,gBAAgB,GAAGjG,IAAI,CAAC6F,YAAY,GAAG,CAAC;YAC9F;UACF,CAAC,CAAC;UACFlD,sBAAsB,CAAC,CAAC,IAAIgK,QAAQ,CAAChJ,MAAM,GAAG,CAAC,GAAGgJ,QAAQ,GAAGjK,mBAAmB,CAAC,CAAC,CAAC;QACrF;MACF,CAAC,MAAM,IAAGqK,MAAM,CAAC5L,UAAU,IAAI,GAAG,EAAC;QACjC,MAAM;UAAC6L,UAAU,GAAC,CAAC;UAAEjM,QAAQ,GAAC,CAAC;UAAED,UAAU,GAAC,CAAC;UAAEmM,cAAc,GAAC,CAAC;UAAEC,MAAM,GAAC,CAAC;UAACC,SAAS,GAAC,CAAC;UAACC,gBAAgB,GAAC,EAAE;UAACnN;QAAU,CAAC,GAAG8M,MAAM;QAC9HnM,YAAY,CAAC;UACXC,aAAa,EAAEmM,UAAU;UACzBlM,UAAU,EAAEA,UAAU;UACtBC,QAAQ,EAAEA,QAAQ;UAClBC,iBAAiB,EAAEiM,cAAc;UACjChM,QAAQ,EAAEiM,MAAM;UAChBhM,eAAe,EAAEiM,SAAS;UAC1BhM,UAAU,EAAE,CAAC,CAACuL,YAAY,GAAGK,MAAM,CAAC5L,UAAU,GAAG,IAAI;UACrDC,aAAa,EAAE,CAAC,CAACsL,YAAY,GAAEK,MAAM,CAAC3L,aAAa,GAAG;QACxD,CAAC,CAAC;QACF;QACA,IAAG,CAACgM,gBAAgB,IAAE,EAAE,EAAEzJ,MAAM,GAAG,CAAC,EAAC;UACnC,CAACgJ,QAAQ,CAAChJ,MAAM,GAAG,CAAC,GAAGgJ,QAAQ,GAAGjK,mBAAmB,EAAE2B,OAAO,CAACrE,IAAI,IAAI;YACrE,IAAIwN,OAAO,GAAGJ,gBAAgB,CAAC5I,IAAI,CAACiJ,GAAG,IAAIA,GAAG,CAAC3H,SAAS,IAAI9F,IAAI,CAAC8F,SAAS,CAAC;YAC3E,IAAG,CAAC,CAAC0H,OAAO,EAAC;cACXxN,IAAI,CAAC8G,aAAa,GAAG0G,OAAO,CAACE,WAAW;cACxC1N,IAAI,CAACkG,YAAY,GAAGsH,OAAO,CAACtH,YAAY;cACxClG,IAAI,CAACa,aAAa,GAAG2M,OAAO,CAACR,UAAU;cACvChN,IAAI,CAAC+G,aAAa,GAAGyG,OAAO,CAACG,iBAAiB;cAC9C3N,IAAI,CAAC2G,YAAY,GAAG6G,OAAO,CAACxH,OAAO;YACrC,CAAC,MAAI;cACHhG,IAAI,CAAC8G,aAAa,GAAG,EAAE;cACvB9G,IAAI,CAACkG,YAAY,GAAG,EAAE;cACtBlG,IAAI,CAACa,aAAa,GAAG,EAAE;cACvBb,IAAI,CAAC+G,aAAa,GAAG,EAAE;cACvB/G,IAAI,CAAC2G,YAAY,GAAG3G,IAAI,CAAC6F,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC7F,IAAI,CAACiG,gBAAgB,GAAGjG,IAAI,CAAC6F,YAAY,GAAG,CAAC;YAC9F;UACF,CAAC,CAAC;UACFlD,sBAAsB,CAAC,CAAC,IAAIgK,QAAQ,CAAChJ,MAAM,GAAG,CAAC,GAAGgJ,QAAQ,GAAGjK,mBAAmB,CAAC,CAAC,CAAC;QACrF;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAC,GAAG,CAAC;EAEN,MAAMkL,cAAc,GAAIC,IAAI,IAAK;IAC/B;EAAA,CACD;EAED,IAAGhO,aAAa,CAACiO,SAAS,IAAIhO,aAAa,CAACgO,SAAS,IAAK,CAAC,CAACjP,MAAM,IAAIkB,gBAAgB,CAAC+N,SAAU,EAAE,oBAAO/P,OAAA,CAACX,QAAQ;IAAAkO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;EAErH,MAAMsC,WAAW,GAAGnM,MAAM,KAAK,CAAC,GAC9B,eAAAzC,mBAAA,GAAcW,aAAa,CAACE,IAAI,cAAAb,mBAAA,uBAAlBA,mBAAA,CAAoB6O,aAAa,SAAA5O,oBAAA,GAAQU,aAAa,CAACE,IAAI,cAAAZ,oBAAA,uBAAlBA,oBAAA,CAAoB6O,oBAAoB,IAAI,GAEnG,EAAE;;EAEJ;EACA,MAAMpD,sCAAsC,GAAG,MAAAA,CAAMrJ,OAAO,EAAEkL,YAAY,EAAEwB,IAAI,KAAG;IACjF,IAAIpB,OAAO,GAAG;MACZ,GAAGtL,OAAO;MACV3C,MAAM;MACN6E,UAAU,EAAEgJ,YAAY,IAAI;IAC9B,CAAC;IACD,MAAMjQ,qBAAqB,CAACqQ,OAAO,CAAC,CAACP,IAAI,CAAEQ,MAAM,IAAK;MACpD,IAAGA,MAAM,CAAC5L,UAAU,IAAI,GAAG,EAAC;QAC1B,IAAG+M,IAAI,IAAI,CAAC,EAAC;UACXnD,SAAS,CAACgC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAElO,MAAM,CAAC;UACzB;QACF;QACA,MAAM;UAACqD,OAAO;UAAEiM,SAAS;UAAEC;QAAQ,CAAC,GAAGrB,MAAM;QAC7CsB,iBAAiB,CAACF,SAAS,EAAC,UAAU,CAAC;QACvCE,iBAAiB,CAACD,QAAQ,EAAC,QAAQ,CAAC;QACpCnM,UAAU,CAAC;UAACC,OAAO,EAAEA;QAAO,CAAC,CAAC;QAC9B,IAAGgM,IAAI,IAAI,CAAC,EAAC;UACX,IAAG5M,QAAQ,EAAE;UACbC,WAAW,CAAC,IAAI,CAAC;QACnB;MACF,CAAC,MAAM,CAEP;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8M,iBAAiB,GAAG,MAAAA,CAAMlM,GAAG,EAAEmM,SAAS,KAAK;IACjD,IAAI;MACF,IAAI,CAACnM,GAAG,EAAE;QACRoM,OAAO,CAAC/B,KAAK,CAAC,oCAAoC,CAAC;QACnD,IAAG8B,SAAS,IAAI,UAAU,EAAC;UACzBhM,OAAO,CAAC,IAAI,CAAC;QACf,CAAC,MAAM;UACLF,MAAM,CAAC,IAAI,CAAC;QACd;QACA;MACF;MAEA,MAAMoM,SAAS,GAAG;QAAEC,oBAAoB,EAAE,GAAG;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,GAAG;QAAEtD,KAAK,EAAE;UAAEuD,IAAI,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAU;MAAE,CAAC;MACpH,MAAMC,SAAS,GAAG,MAAM3R,MAAM,CAAC4R,SAAS,CAAC5M,GAAG,EAAEqM,SAAS,CAAC;MACxD,IAAGF,SAAS,IAAI,UAAU,EAAC;QACzBhM,OAAO,CAACwM,SAAS,CAAC;MACpB,CAAC,MAAM;QACL1M,MAAM,CAAC0M,SAAS,CAAC;MACnB;IACF,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACd+B,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,IAAG8B,SAAS,IAAI,UAAU,EAAC;QACzBhM,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,MAAM;QACLF,MAAM,CAAC,IAAI,CAAC;MACd;IACF;EACF,CAAC;EAED,MAAM4M,UAAU,GAAIlQ,IAAI,IAAK;IAC3B,IAAIqD,GAAG,GAAG,EAAE;IACZ,IAAGrD,IAAI,IAAI,CAAC,EAAC;MACXqD,GAAG,GAAG,uBAAuB;IAC/B,CAAC,MAAI;MACHA,GAAG,GAAG,kBAAkB;IAC1B;IACA8M,MAAM,CAACC,IAAI,CACT,GAAGD,MAAM,CAACE,QAAQ,CAACC,MAAM,MAAMjN,GAAG,EACpC,CAAC;EACH,CAAC;EAED,SAASkN,kBAAkBA,CAAChC,MAAM,EAAC;IACjC,IAAG,CAACE,YAAY,CAACF,MAAM,CAAC,EAAC;MACvB,IAAG,CAAAvK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEY,UAAU,KAAI2J,MAAM,CAAC3J,UAAU,EAAC;QAC/CX,eAAe,CAAC,IAAI,CAAC;QACrB+E,wBAAwB,CAAC,CAAC;MAC5B,CAAC,MAAI;QACH/E,eAAe,CAACsK,MAAM,CAAC;QACvBvF,wBAAwB,CAACuF,MAAM,CAAC3J,UAAU,CAAC;MAC7C;IACF;EACF;EAEA,SAAS6J,YAAYA,CAACF,MAAM,EAAC;IAC3B,IAAG,CAAC,CAACA,MAAM,CAACnH,YAAY,EAAC;MACvB,IAAG5I,MAAM,CAAC+P,MAAM,CAACnH,YAAY,CAAC,CAACoJ,QAAQ,CAAChS,MAAM,CAAC,CAAC,CAAC,EAAC;QAChD,OAAO,QAAQ;MACjB;IACF;IACA,IAAG+P,MAAM,CAACkC,WAAW,IAAI,CAAC,EAAC;MAAC;MAC1B,OAAO,YAAY;IACrB;IACA,IAAG,CAAC,CAAC1Q,MAAM,EAAC;MAAC;MACX,IAAG,CAAC,CAACwO,MAAM,CAACxO,MAAM,IAAIwO,MAAM,CAACxO,MAAM,IAAIA,MAAM,EAAC;QAAC;QAC7C,OAAO,UAAUwO,MAAM,CAACxO,MAAM,GAAG;MACnC;MACA,IAAGwO,MAAM,CAACmC,aAAa,IAAI,CAAC,EAAC;QAAC;QAC5B,OAAO,WAAW;MACpB;MACA;MACA,IAAGnC,MAAM,CAACoC,aAAa,IAAI,CAAC,EAAC;QAAC;QAC5B,OAAO,SAAS;MAClB;IACF,CAAC,MAAI;MAAC;MACJ,IAAG,CAAC,CAACpC,MAAM,CAACxO,MAAM,EAAC;QAAC;QAClB,OAAO,UAAUwO,MAAM,CAACxO,MAAM,GAAG;MACnC;MACA;MACA,IAAGwO,MAAM,CAACoC,aAAa,IAAI,CAAC,EAAC;QAAC;QAC5B,OAAO,SAAS;MAClB;IACF;IACA,OAAO,EAAE;EACX;EAEA,SAASC,YAAYA,CAACrC,MAAM,EAAC;IAC3BxQ,2BAA2B,CAAC;MAAC8S,QAAQ,EAAEtC,MAAM,CAACsC;IAAQ,CAAC,CAAC,CAACpD,IAAI,CAACnJ,GAAG,IAAI;MACnE,IAAGA,GAAG,CAACjC,UAAU,IAAI,GAAG,EAAC;QACvB,IAAG,CAACR,SAAS,CAACM,QAAQ,IAAE,CAAC,IAAI,CAAC,EAAC;UAC7B,IAAG,CAAA6B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEY,UAAU,KAAI2J,MAAM,CAAC3J,UAAU,EAAC;YAC/CX,eAAe,CAAC,IAAI,CAAC;YACrB+E,wBAAwB,CAAC,CAAC;UAC5B,CAAC,MAAI;YACHA,wBAAwB,CAAChF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEY,UAAU,CAAC;UACpD;QACF,CAAC,MAAI;UACHvD,yBAAyB,CAAC,CAAC;QAC7B;MACF;IACF,CAAC,CAAC;IACF;EACF;EAEA,SAASsG,cAAcA,CAACmJ,IAAI,EAAC;IAC3B,IAAIC,IAAI,GAAGvS,MAAM,CAACA,MAAM,CAACsS,IAAI,CAAC,CAACE,MAAM,CAAC,UAAU,CAAC,CAAC,CAACC,IAAI,CAACzS,MAAM,CAACA,MAAM,CAAC,CAAC,CAACwS,MAAM,CAAC,UAAU,CAAC,CAAC,EAAC,MAAM,CAAC,GAAG,CAAC;IACvG,IAAGD,IAAI,IAAI,CAAC,EAAC;MACX,OAAOA,IAAI;IACb;IACA,OAAO,CAAC,CAAC;EACX;EAEA,SAASnH,YAAYA,CAAC1I,IAAI,EAAC;IACzB,IAAGA,IAAI,CAAC6F,YAAY,IAAI,CAAC,IAAI,CAAC7F,IAAI,CAACiG,gBAAgB,EAAC;MAClD,OAAO,CAAC,EAAC;IACX;IACA,IAAG,CAAC,CAACjG,IAAI,CAACiG,gBAAgB,IAAIQ,cAAc,CAACzG,IAAI,CAACiG,gBAAgB,CAAC,GAAG,CAAC,EAAC;MACtE,OAAO,CAAC,EAAC;IACX;IACA,OAAO,CAAC,EAAC;EACX;EAEA,oBAAQlI,OAAA,CAAAE,SAAA;IAAA4N,QAAA,gBACR9N,OAAA,CAAChD,IAAI;MACHuF,IAAI,EAAEA,IAAK;MACXgD,IAAI,EAAC,OAAO;MACZ0M,QAAQ,EAAEhG,KAAM;MAChBiG,aAAa,EAAE;QACb7G,QAAQ,EAAC,EAAE;QACX3F,mBAAmB,EAAE,CAAA1E,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE0E,mBAAmB,KAAI,CAAC,GAAG,CAAC,GAAI3E,IAAI,KAAGT,iBAAiB,GAAC,CAAC,GAAC;MAC7F,CAAE;MAAA,GACEE,cAAc;MAClB6M,KAAK,EAAE;QAAC8E,YAAY,EAAC,EAAE;QAACC,WAAW,EAAC;MAAE,CAAE;MACxCC,YAAY,EAAC,KAAK;MAClBxC,cAAc,EAAEA,cAAe;MAAA/B,QAAA,gBAE/B9N,OAAA,CAAChD,IAAI,CAACsV,IAAI;QACRC,SAAS,EAAC,iBAAiB;QAC3BC,KAAK,EAAC,0BAAM;QACZjN,IAAI,EAAC,UAAU;QAAAuI,QAAA,eACf9N,OAAA,CAAC/C,KAAK;UAACsV,SAAS,EAAC,iBAAiB;UAACF,YAAY,EAAC,KAAK;UAACI,SAAS,EAAE,GAAI;UAACC,QAAQ,EAAE3R,IAAI,KAAKT,iBAAkB;UAACqS,QAAQ,EAAE5G;QAAe;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9H,CAAC,eAEZ1N,OAAA,CAAChD,IAAI,CAACsV,IAAI;QACRE,KAAK,EAAC,0BAAM;QACZjN,IAAI,EAAC,qBAAqB;QAC1B8H,KAAK,EAAEtM,IAAI,IAAIT,iBAAiB,GAAC;UAACsS,YAAY,EAAC;QAAE,CAAC,GAAC;UAAChF,OAAO,EAAC;QAAM,CAAE;QAAAE,QAAA,eAClE9N,OAAA,CAAC9C,KAAK,CAAC2V,KAAK;UAACC,WAAW,EAAC,OAAO;UAAAhF,QAAA,gBAC9B9N,OAAA,CAAC9C,KAAK,CAACK,MAAM;YAACkO,KAAK,EAAE,CAAE;YAAC4B,KAAK,EAAE;cAAC0F,mBAAmB,EAAC,CAAC;cAACC,sBAAsB,EAAC,CAAC;cAACpC,KAAK,EAAC,GAAG;cAACqC,SAAS,EAAC;YAAQ,CAAE;YAAAnF,QAAA,EAAC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACpI1N,OAAA,CAAC9C,KAAK,CAACK,MAAM;YAACkO,KAAK,EAAE,CAAE;YAAC4B,KAAK,EAAE;cAAC6F,oBAAoB,EAAC,CAAC;cAACC,uBAAuB,EAAC,CAAC;cAACvC,KAAK,EAAC,GAAG;cAACqC,SAAS,EAAC;YAAQ,CAAE;YAAAnF,QAAA,EAAC;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACP1N,OAAA,CAAChD,IAAI;MACHqQ,KAAK,EAAE;QAAC+E,WAAW,EAAC;MAAE,CAAE;MACxB7M,IAAI,EAAC,cAAc;MACnB2M,aAAa,EAAE;QACbkB,WAAW,EAAE;MACf,CAAE;MAAA,GACE5S,cAAc;MAClB6R,YAAY,EAAC,KAAK;MAAAvE,QAAA,eAElB9N,OAAA,CAAChD,IAAI,CAACsV,IAAI;QACRC,SAAS,EAAC,4BAA4B;QACtCC,KAAK,EAAC,0BAAM;QACZjN,IAAI,EAAC,aAAa;QAClB8H,KAAK,EAAE;UAACuF,YAAY,EAAC;QAAE,CAAE;QAAA9E,QAAA,eACzB9N,OAAA,CAACxC,KAAK;UACJ6V,IAAI,EAAC,OAAO;UACZd,SAAS,EAAC,eAAe;UACzBe,UAAU,EAAE3O,mBAAoB;UAChC4O,UAAU,EAAE,KAAM;UAClBC,QAAQ;UACRC,MAAM,EAAE;YAACC,CAAC,EAAC,gBAAgB7P,MAAM,IAAI,CAAC,GAAC,GAAG,GAAC,GAAG;UAAK,CAAE;UAAAiK,QAAA,gBAErD9N,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAEO,OAAO,EAAE,MAAM;gBAAE+F,cAAc,EAAE;cAAS,CAAE;cAAA7F,QAAA,EAAC;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YAC3EkG,SAAS,EAAE,WAAY;YAEvBhD,KAAK,EAAE/M,MAAM,IAAI,CAAC,GAAI9C,IAAI,IAAIT,iBAAiB,GAAG,GAAG,GAAG,GAAG,GAAI,KAAM;YACrEuT,MAAM,EAAGjN,SAAS,iBAAK5G,OAAA;cAAKqN,KAAK,EAAE;gBAAEO,OAAO,EAAE,MAAM;gBAAE+F,cAAc,EAAE;cAAS,CAAE;cAAA7F,QAAA,EAAElH;YAAS;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YACpGoG,MAAM,EAAGtN,IAAI,IAAK;cAChB,IAAIA,IAAI,CAAC+B,SAAS,EAAE;gBAClB,OAAO;kBAAEwL,OAAO,EAAEvN,IAAI,CAACgC;gBAAY,CAAC;cACtC,CAAC,MAAM;gBACL,OAAO;kBAAEuL,OAAO,EAAE;gBAAE,CAAC;cACvB;YACF;UAAE,GATG,WAAW;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUjB,CAAC,eACF1N,OAAA,CAACI,MAAM;YACL+M,KAAK,EAAE,IAAK;YACZyG,SAAS,EAAE,aAAc;YAEzBhD,KAAK,EAAE/M,MAAM,IAAI,CAAC,GAAI9C,IAAI,IAAIT,iBAAiB,GAAG,GAAG,GAAG,GAAG,GAAI,KAAM;YACrEuT,MAAM,EAAEA,CAACG,WAAW,EAAExN,IAAI,kBACxBxG,OAAA;cAAKqN,KAAK,EAAE;gBAAEO,OAAO,EAAE,MAAM;gBAAE+F,cAAc,EAAE,eAAe;gBAAEM,UAAU,EAAE;cAAQ,CAAE;cAAAnG,QAAA,GACnFkG,WAAW,EACX,CAAAxN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACsJ,WAAW,iBAAInJ,OAAA;gBAAMqN,KAAK,EAAE;kBAAEC,KAAK,EAAE,SAAS;kBAAE4G,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAG,CAAE;gBAAArG,QAAA,EAAC;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC7H,CAAAlH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,iBAAIpJ,OAAA;gBAAMqN,KAAK,EAAE;kBAAEC,KAAK,EAAE,SAAS;kBAAE4G,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAG,CAAE;gBAAArG,QAAA,EAAC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpI,CACL;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GATlC,aAAa;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUnB,CAAC,EACD3M,IAAI,IAAIT,iBAAiB,gBACxBN,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAEjK,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG;YAAK;cAAA0J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAC9EkG,SAAS,EAAE,UAAW;YAEtBhD,KAAK,EAAE/M,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,KAAM;YACjCgQ,MAAM,EAAEA,CAACpL,QAAQ,EAACjC,IAAI,KAAK;cACzB,IAAG3C,MAAM,IAAI,CAAC,EAAC;gBACb,IAAG2C,IAAI,CAACL,OAAO,IAAIxG,eAAe,CAACyG,SAAS,EAAC;kBAC3C,oBAAQpG,OAAA;oBAAKqN,KAAK,EAAE;sBAAC4F,SAAS,EAAC;oBAAQ,CAAE;oBAAAnF,QAAA,EAAC;kBAAC;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBACnD;gBACA,oBACE1N,OAAA;kBAAKqN,KAAK,EAAE;oBAACO,OAAO,EAAC,MAAM;oBAACqG,UAAU,EAAC,QAAQ;oBAACN,cAAc,EAAC;kBAAQ,CAAE;kBAAA7F,QAAA,eACvE9N,OAAA;oBAAKuS,SAAS,EAAE,qBAAsB;oBAAAzE,QAAA,eACpC9N,OAAA,CAAC5C,WAAW;sBACVgX,QAAQ,EAAE,KAAM;sBAChB/G,KAAK,EAAE;wBAACuD,KAAK,EAAC;sBAAG,CAAE;sBACnByC,IAAI,EAAC,OAAO;sBACZgB,SAAS,EAAE,CAAE;sBACbC,GAAG,EAAE,CAAE;sBACPC,GAAG,EAAE,IAAK;sBACV7B,QAAQ,EAAE,CAAAlM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAoB;sBACjEoL,WAAW,eACTxU,OAAA,CAACzC,MAAM;wBACLwD,IAAI,EAAE,SAAU;wBAChB2R,QAAQ,EAAE,CAAAlM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,IAAI,CAACX,QAAQ,IAAE,CAAC,KAAK,CAAE;wBACvF4E,KAAK,EAAE;0BAAC0F,mBAAmB,EAAC,CAAC;0BAACC,sBAAsB,EAAC,CAAC;0BAACyB,WAAW,EAAC,KAAK;0BAACC,WAAW,EAAE,CAAC,CAAAlO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,IAAI,CAACX,QAAQ,IAAE,CAAC,KAAK,CAAC,KAAK;wBAAS,CAAE;wBACnLkM,OAAO,EAAEA,CAAA,KAAIlK,cAAc,CAACjE,IAAI,EAAC,CAAC,CAAE;wBAAAsH,QAAA,EACrC;sBAED;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CACT;sBACDkH,UAAU,eACR5U,OAAA,CAACzC,MAAM;wBACLwD,IAAI,EAAE,SAAU;wBAChB2R,QAAQ,EAAE,CAAAlM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,IAAI,CAACX,QAAQ,IAAE,CAAC,KAAK;0BAACnF;wBAAqB,CAAE;wBAC7G+J,KAAK,EAAE;0BAAC6F,oBAAoB,EAAC,CAAC;0BAACC,uBAAuB,EAAC,CAAC;0BAAC0B,UAAU,EAAC,KAAK;0BAACH,WAAW,EAAE,CAAC,CAAAlO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,IAAI,CAACX,QAAQ,IAAE,CAAC,KAAK,IAAI,KAAK;wBAAS,CAAE;wBACvLkM,OAAO,EAAEA,CAAA,KAAIlK,cAAc,CAACjE,IAAI,EAAC,CAAC,CAAE;wBAAAsH,QAAA,EACrC;sBAED;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CACT;sBACDjC,KAAK,EAAEhD,QAAS;sBAChBkK,QAAQ,EAAGlH,KAAK,IAAGD,eAAe,CAAChF,IAAI,EAACiF,KAAK,CAAE;sBAC/CqJ,YAAY,EAAEA,CAAA,KAAIrK,cAAc,CAACjE,IAAI,EAAC,CAAC,CAAE;sBACzCuO,MAAM,EAAEA,CAAA,KAAItK,cAAc,CAACjE,IAAI,EAAC,CAAC;oBAAE;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV,CAAC,MACG;gBACF,oBAAQ1N,OAAA;kBAAKqN,KAAK,EAAE;oBAAC4F,SAAS,EAAC;kBAAQ,CAAE;kBAAAnF,QAAA,EAAExK;gBAAqB;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cACzE;YACF,CAAE;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GAnDlC,UAAU;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoDhB,CAAC,gBAEF1N,OAAA,CAACK,WAAW;YAAC8M,KAAK,EAAE,KAAM;YAACoF,SAAS,EAAC,cAAc;YAAAzE,QAAA,gBACjD9N,OAAA,CAACI,MAAM;cACL+M,KAAK,EAAE,KAAM;cACboF,SAAS,EAAC,cAAc;cACxBqB,SAAS,EAAE,cAAe;cAE1BhD,KAAK,EAAE,EAAG;cACViD,MAAM,EAAG/L,YAAY,iBAAK9H,OAAA;gBAAA8N,QAAA,GAAMhG,YAAY,IAAE,CAAC,EAAC,QAAC;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAE;cACxDoG,MAAM,EAAEA,CAAA,KAAM;gBAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAE,CAAC;cAAA;YAAE,GAHlC,cAAc;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIpB,CAAC,eACF1N,OAAA,CAACI,MAAM;cACL+M,KAAK,EAAE,OAAQ;cACfoF,SAAS,EAAC,cAAc;cACxBqB,SAAS,EAAE,gBAAiB;cAE5BhD,KAAK,EAAE,GAAI;cACXiD,MAAM,EAAEA,CAACmB,cAAc,EAACxO,IAAI,KAAK;gBAC/B,IAAG,CAAC,CAACA,IAAI,CAAC0B,gBAAgB,IAAI,CAAC1B,IAAI,CAACsB,YAAY,IAAE,CAAC,IAAI,CAAC,EAAC;kBACvD,oBAAS9H,OAAA;oBAAKqN,KAAK,EAAE1C,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,GAAG;sBAAC8G,KAAK,EAAE;oBAAM,CAAC,GAAG,CAAC,CAAE;oBAAAQ,QAAA,EAAC;kBAAC;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAC9E;gBACA,oBAAS1N,OAAA;kBAAA8N,QAAA,EAAM,CAAAtH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyO,aAAa,KAAE;gBAAG;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAChD,CAAE;cACFoG,MAAM,EAAEA,CAAA,KAAM;gBAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAE,CAAC;cAAA;YAAE,GARlC,gBAAgB;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAStB,CAAC,eACF1N,OAAA,CAACI,MAAM;cACL+M,KAAK,EAAE,MAAO;cACdoF,SAAS,EAAC,cAAc;cACxBqB,SAAS,EAAE,kBAAmB;cAE9BhD,KAAK,EAAE,GAAI;cACXiD,MAAM,EAAEA,CAAC3L,gBAAgB,EAAC1B,IAAI,KAAK;gBACjC,IAAG,CAAC,CAAC0B,gBAAgB,IAAI,CAAC1B,IAAI,CAACsB,YAAY,IAAE,CAAC,IAAI,CAAC,EAAC;kBAClD,oBAAQ9H,OAAA;oBAAKqN,KAAK,EAAE1C,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,GAAG;sBAAC8G,KAAK,EAAE;oBAAM,CAAC,GAAG,CAAC,CAAE;oBAAAQ,QAAA,EAAEvO,MAAM,CAAC2I,gBAAgB,CAAC,CAAC6J,MAAM,CAAC,YAAY;kBAAC;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAC3H;gBACA,oBAAQ1N,OAAA;kBAAA8N,QAAA,EAAK;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACtB,CAAE;cACFoG,MAAM,EAAEA,CAAA,KAAM;gBAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAE,CAAC;cAAA;YAAE,GARlC,kBAAkB;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,EAEf3M,IAAI,IAAIT,iBAAiB,iBACxBN,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAC;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YACrDkG,SAAS,EAAE,gBAAiB;YAE5BhD,KAAK,EAAE/M,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,KAAM;YACjCgQ,MAAM,EAAEA,CAACmB,cAAc,EAACxO,IAAI,KAAK;cAC/B,IAAG3C,MAAM,IAAI,CAAC,IAAI,CAAC2C,IAAI,CAACiC,QAAQ,IAAE,CAAC,IAAI,CAAC,EAAC;gBACvC,oBAASzI,OAAA;kBAAKqN,KAAK,EAAE;oBAAC4F,SAAS,EAAC;kBAAQ,CAAE;kBAAAnF,QAAA,EAAC;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACpD;cACA,oBAAS1N,OAAA;gBAAKqN,KAAK,EAAE;kBAAC4F,SAAS,EAAC;gBAAQ,CAAE;gBAAAnF,QAAA,EAAE,CAAAtH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyO,aAAa,KAAE;cAAG;gBAAA1H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAC7E,CAAE;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GARlC,gBAAgB;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAStB,CAAC,EAEH7J,MAAM,IAAI,CAAC,GACT9C,IAAI,IAAIT,iBAAiB,gBACxBN,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YACpDkG,SAAS,EAAE,eAAgB;YAE3BhD,KAAK,EAAE,GAAI;YACXiD,MAAM,EAAEA,CAAChL,aAAa,EAACrC,IAAI,EAAC0O,MAAM,KAAK;cAAA,IAAAC,oBAAA;cACrC,IAAG3O,IAAI,CAACL,OAAO,IAAIxG,eAAe,CAACyG,SAAS,EAAC;gBAC3C,oBAAQpG,OAAA;kBAAKqN,KAAK,EAAE;oBAAC4F,SAAS,EAAC;kBAAQ,CAAE;kBAAAnF,QAAA,EAAC;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACnD;cACA,oBACE1N,OAAA;gBAAKqN,KAAK,EAAE;kBAACO,OAAO,EAAC,MAAM;kBAACqG,UAAU,EAAC;gBAAQ,CAAE;gBAAAnG,QAAA,GAAC,IAAE,eAClD9N,OAAA,CAACtC,QAAQ;kBAAC0X,OAAO,EAAE,CAAC,OAAO,CAAE;kBAAC1C,QAAQ,EAAE,CAAAlM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAoB;kBAACiM,OAAO,eACrGrV,OAAA,CAACrC,IAAI;oBAAAmQ,QAAA,eACH9N,OAAA,CAACrC,IAAI,CAAC2U,IAAI;sBAAAxE,QAAA,eACR9N,OAAA,CAAC9C,KAAK,CAAC2V,KAAK;wBAACQ,IAAI,EAAC,OAAO;wBAACP,WAAW,EAAC,OAAO;wBAACP,SAAS,EAAC,uBAAuB;wBAAC9G,KAAK,EAAEjF,IAAI,CAACqC,aAAc;wBAAAiF,QAAA,EACvG,CAAC,EAAAqH,oBAAA,GAAArT,aAAa,CAACG,IAAI,cAAAkT,oBAAA,uBAAlBA,oBAAA,CAAoBnK,cAAc,KAAI,EAAE,EAAE/D,GAAG,CAAC,CAACgE,KAAK,EAAC9D,KAAK,kBAC1DnH,OAAA,CAAC7C,KAAK;0BAAakW,IAAI,EAAC,OAAO;0BAAClG,KAAK,EAAC,EAAE;0BAACoF,SAAS,EAAC,WAAW;0BAAC+C,KAAK,EAAErK,KAAK,CAACG,aAAa,IAAI,EAAG;0BAACmK,MAAM,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAE;0BAAAzH,QAAA,eAC/G9N,OAAA,CAAC9C,KAAK,CAACK,MAAM;4BAACoX,OAAO,EAAEA,CAAA,KAAI7I,mBAAmB,CAACtF,IAAI,EAACyE,KAAK,CAAE;4BAACQ,KAAK,EAAER,KAAK,CAACC,QAAS;4BAAA4C,QAAA,EAAE7C,KAAK,CAACE;0BAAY;4BAAAoC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAe;wBAAC,GAD5GvG,KAAK;0BAAAoG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEV,CACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACU;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACP;kBAAAI,QAAA,eACC9N,OAAA;oBAAGqN,KAAK,EAAE;sBAACC,KAAK,EAAC,SAAS;sBAAEM,OAAO,EAAE,MAAM;sBAAE+F,cAAc,EAAE,eAAe;sBAAEM,UAAU,EAAE,QAAQ;sBAAErD,KAAK,EAAE;oBAAM,CAAE;oBAAA9C,QAAA,gBACjH9N,OAAA;sBAAKqN,KAAK,EAAE;wBAACO,OAAO,EAAE,MAAM;wBAAEqG,UAAU,EAAE,QAAQ;wBAAEuB,IAAI,EAAE,UAAU;wBAAErD,YAAY,EAAE;sBAAM,CAAE;sBAAArE,QAAA,GACzFnD,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,iBACtBxG,OAAA;wBAAMqN,KAAK,EAAE;0BAACC,KAAK,EAAC,MAAM;0BAACmI,SAAS,EAAC;wBAAQ,CAAE;wBAAA3H,QAAA,EAC5C,CAAC,CAACnD,YAAY,CAACnE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAIkC,cAAc,CAAClC,IAAI,CAAC0B,gBAAgB,CAAC;sBAAG;wBAAAqF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CAAC,EAER/C,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,iBACtBxG,OAAA;wBAAMqN,KAAK,EAAE;0BAACC,KAAK,EAAC,KAAK;0BAACmI,SAAS,EAAC;wBAAQ,CAAE;wBAAA3H,QAAA,EAAC;sBAAG;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAExD,CAAC,eACN1N,OAAA;sBAAKqN,KAAK,EAAE;wBAACO,OAAO,EAAE,MAAM;wBAAEqG,UAAU,EAAE,QAAQ;wBAAEuB,IAAI,EAAE,UAAU;wBAAEpD,WAAW,EAAE;sBAAM,CAAE;sBAAAtE,QAAA,EACxF,CAAC,CAACjF,aAAa,gBACd7I,OAAA,CAAC7C,KAAK;wBAAckW,IAAI,EAAC,OAAO;wBAAClG,KAAK,EAAC,EAAE;wBAACoF,SAAS,EAAC,WAAW;wBAAC+C,KAAK,EAAE9O,IAAI,CAACsC,MAAM,IAAI,EAAG;wBAACyM,MAAM,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAE;wBAAAzH,QAAA,eACxG9N,OAAA;0BAAKqN,KAAK,EAAE;4BAACqI,MAAM,EAAC,EAAE;4BAAC9E,KAAK,EAAC,EAAE;4BAAChD,OAAO,EAAC,MAAM;4BAACqG,UAAU,EAAC,QAAQ;4BAACC,QAAQ,EAAC,EAAE;4BAAC5G,KAAK,EAAC,MAAM;4BAACqI,eAAe,EAAC,SAAS;4BAACC,YAAY,EAAC;0BAAC,CAAE;0BAAA9H,QAAA,gBACpI9N,OAAA;4BAAMqN,KAAK,EAAE;8BAACuD,KAAK,EAAC,EAAE;8BAAChD,OAAO,EAAC,MAAM;8BAAC+F,cAAc,EAAC;4BAAQ,CAAE;4BAAA7F,QAAA,EAAEtH,IAAI,CAACjB;0BAAI;4BAAAgI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAClF1N,OAAA;4BAAMqN,KAAK,EAAE;8BAACuD,KAAK,EAAC,EAAE;8BAAChD,OAAO,EAAC,MAAM;8BAAC+F,cAAc,EAAC;4BAAQ,CAAE;4BAAA7F,QAAA,eAAC9N,OAAA,CAACxB,YAAY;8BAAA+O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnF;sBAAC,GAJIwH,MAAM;wBAAA3H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAKX,CAAC,gBAER1N,OAAA;wBAAMqN,KAAK,EAAE;0BAACC,KAAK,EAAE3C,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG;wBAAS,CAAE;wBAAAsH,QAAA,EAAEnD,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG;sBAAK;wBAAA+G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEtH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACV,CAAC,CAAC7E,aAAa,iBACd7I,OAAA,CAACzC,MAAM;kBACL8P,KAAK,EAAE;oBAAEuI,YAAY,EAAE,KAAK;oBAAEC,UAAU,EAAE;kBAAW,CAAE;kBACvDxC,IAAI,EAAC,OAAO;kBACZtS,IAAI,EAAC,MAAM;kBACXqM,IAAI,eAAEpN,OAAA,CAAC3B,aAAa;oBAACkU,SAAS,EAAC;kBAAa;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAChDiH,OAAO,EAAEA,CAAA,KAAI7I,mBAAmB,CAACtF,IAAI;gBAAE;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE1C,CAAC;YAEV,CAAE;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GAzDlC,eAAe;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0DrB,CAAC,gBAEF1N,OAAA,CAACK,WAAW;YAAC8M,KAAK,EAAE,MAAO;YAACoF,SAAS,EAAC,cAAc;YAAAzE,QAAA,gBAClD9N,OAAA,CAACI,MAAM;cACL+M,KAAK,EAAE,OAAQ;cACfoF,SAAS,EAAC,cAAc;cACxBqB,SAAS,EAAE,UAAW;cAEtBhD,KAAK,EAAE,GAAI;cACXiD,MAAM,EAAEA,CAACpL,QAAQ,EAACjC,IAAI,KAAK;gBACzB,oBACExG,OAAA;kBAAKqN,KAAK,EAAE;oBAACO,OAAO,EAAC,MAAM;oBAACqG,UAAU,EAAC,QAAQ;oBAACN,cAAc,EAAC;kBAAQ,CAAE;kBAAA7F,QAAA,eACvE9N,OAAA;oBAAKuS,SAAS,EAAE,qBAAsB;oBAAAzE,QAAA,eACpC9N,OAAA,CAAC5C,WAAW;sBACVgX,QAAQ,EAAE,KAAM;sBAChB/G,KAAK,EAAE;wBAACuD,KAAK,EAAC;sBAAG,CAAE;sBACnByC,IAAI,EAAC,OAAO;sBACZgB,SAAS,EAAE,CAAE;sBACbC,GAAG,EAAE,CAAC,CAAC3J,YAAY,CAACnE,IAAI,CAAC,GAAG,CAAC,GAAG,CAACA,IAAI,CAACsB,YAAa;sBACnDyM,GAAG,EAAE,IAAK;sBACV7B,QAAQ,EAAE,CAAAlM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAoB;sBACjEoL,WAAW,eACTxU,OAAA,CAACzC,MAAM;wBACLwD,IAAI,EAAE,SAAU;wBAChB2R,QAAQ,EAAE,CAAAlM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,KAAK,CAAC,CAACuB,YAAY,CAACnE,IAAI,CAAC,GAAI,CAACiC,QAAQ,IAAE,CAAC,KAAK,CAAC,GAAK,CAACA,QAAQ,IAAE,CAAC,KAAK,CAACjC,IAAI,CAACsB,YAAa,CAAE;wBAC1JuF,KAAK,EAAE;0BACL0F,mBAAmB,EAAC,CAAC;0BACrBC,sBAAsB,EAAC,CAAC;0BACxByB,WAAW,EAAC,KAAK;0BACjBC,WAAW,EAAE,CAAC,CAAAlO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,KAAK,CAAC,CAACuB,YAAY,CAACnE,IAAI,CAAC,GAAI,CAACiC,QAAQ,IAAE,CAAC,KAAK,CAAC,GAAK,CAACA,QAAQ,IAAE,CAAC,KAAK,CAACjC,IAAI,CAACsB,YAAa,CAAC,KAAK;wBACpK,CAAE;wBACF6M,OAAO,EAAEA,CAAA,KAAIlK,cAAc,CAACjE,IAAI,EAAC,CAAC,CAAE;wBAAAsH,QAAA,EACrC;sBAED;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CACT;sBACDkH,UAAU,eACR5U,OAAA,CAACzC,MAAM;wBACLwD,IAAI,EAAE,SAAU;wBAChB2R,QAAQ,EAAE,CAAAlM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,IAAI,CAACX,QAAQ,IAAE,CAAC,KAAK;0BAACnF;wBAAqB,CAAE;wBAC7G+J,KAAK,EAAE;0BAAC6F,oBAAoB,EAAC,CAAC;0BAACC,uBAAuB,EAAC,CAAC;0BAAC0B,UAAU,EAAC,KAAK;0BACvEH,WAAW,EAAE,CAAC,CAAAlO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAmB,IAAI,CAACX,QAAQ,IAAE,CAAC,KAAK;4BAACnF;0BAAqB,CAAC,KAAK;wBACvH,CAAE;wBACFqR,OAAO,EAAEA,CAAA,KAAIlK,cAAc,CAACjE,IAAI,EAAC,CAAC,CAAE;wBAAAsH,QAAA,EACrC;sBAED;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CACT;sBACDjC,KAAK,EAAEhD,QAAS;sBAChBkK,QAAQ,EAAGlH,KAAK,IAAGD,eAAe,CAAChF,IAAI,EAACiF,KAAK,CAAE;sBAC/CqJ,YAAY,EAAEA,CAAA,KAAIrK,cAAc,CAACjE,IAAI,EAAC,CAAC,CAAE;sBACzCuO,MAAM,EAAEA,CAAA,KAAItK,cAAc,CAACjE,IAAI,EAAC,CAAC;oBAAE;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV,CAAE;cACFoG,MAAM,EAAEA,CAAA,KAAM;gBAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAE,CAAC;cAAA;YAAE,GAlDlC,UAAU;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDhB,CAAC,eACF1N,OAAA,CAACI,MAAM;cACL+M,KAAK,EAAE,MAAO;cACdoF,SAAS,EAAC,cAAc;cACxBqB,SAAS,EAAE,eAAgB;cAE3BhD,KAAK,EAAE,GAAI;cACXiD,MAAM,EAAEA,CAAChL,aAAa,EAACrC,IAAI,EAAC0O,MAAM,KAAK;gBAAA,IAAAY,oBAAA;gBACrC,oBACE9V,OAAA;kBAAKqN,KAAK,EAAE;oBAACO,OAAO,EAAC,MAAM;oBAACqG,UAAU,EAAC,QAAQ;oBAACN,cAAc,EAAC;kBAAe,CAAE;kBAAA7F,QAAA,eAE9E9N,OAAA;oBAAKqN,KAAK,EAAE;sBAACO,OAAO,EAAC,MAAM;sBAACqG,UAAU,EAAC;oBAAQ,CAAE;oBAAAnG,QAAA,gBAC/C9N,OAAA,CAACtC,QAAQ;sBAAC0X,OAAO,EAAE,CAAC,OAAO,CAAE;sBAAC1C,QAAQ,EAAE,CAAAlM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAoB;sBAACiM,OAAO,eACrGrV,OAAA,CAACrC,IAAI;wBAAAmQ,QAAA,eACH9N,OAAA,CAACrC,IAAI,CAAC2U,IAAI;0BAAAxE,QAAA,eACR9N,OAAA,CAAC9C,KAAK,CAAC2V,KAAK;4BAACQ,IAAI,EAAC,OAAO;4BAACP,WAAW,EAAC,OAAO;4BAACP,SAAS,EAAC,uBAAuB;4BAAC9G,KAAK,EAAEjF,IAAI,CAACqC,aAAc;4BAAAiF,QAAA,EACvG,CAAC,EAAAgI,oBAAA,GAAAhU,aAAa,CAACG,IAAI,cAAA6T,oBAAA,uBAAlBA,oBAAA,CAAoB9K,cAAc,KAAI,EAAE,EAAE/D,GAAG,CAAC,CAACgE,KAAK,EAAC9D,KAAK,kBAC1DnH,OAAA,CAAC7C,KAAK;8BAAakW,IAAI,EAAC,OAAO;8BAAClG,KAAK,EAAC,EAAE;8BAACoF,SAAS,EAAC,WAAW;8BAAC+C,KAAK,EAAErK,KAAK,CAACG,aAAa,IAAI,EAAG;8BAACmK,MAAM,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAE;8BAAAzH,QAAA,eAC/G9N,OAAA,CAAC9C,KAAK,CAACK,MAAM;gCAACoX,OAAO,EAAEA,CAAA,KAAI7I,mBAAmB,CAACtF,IAAI,EAACyE,KAAK,CAAE;gCAACQ,KAAK,EAAER,KAAK,CAACC,QAAS;gCAAA4C,QAAA,EAAE7C,KAAK,CAACE;8BAAY;gCAAAoC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAe;4BAAC,GAD5GvG,KAAK;8BAAAoG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAEV,CACT;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACU;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CACP;sBAAAI,QAAA,eACC9N,OAAA;wBAAGqN,KAAK,EAAE;0BAACC,KAAK,EAAC,SAAS;0BAAEM,OAAO,EAAE,MAAM;0BAAE+F,cAAc,EAAE,eAAe;0BAAEM,UAAU,EAAE,QAAQ;0BAAErD,KAAK,EAAE;wBAAM,CAAE;wBAAA9C,QAAA,gBACjH9N,OAAA;0BAAKqN,KAAK,EAAE;4BAACO,OAAO,EAAE,MAAM;4BAAEqG,UAAU,EAAE,QAAQ;4BAAEuB,IAAI,EAAE,UAAU;4BAAErD,YAAY,EAAE;0BAAM,CAAE;0BAAArE,QAAA,GACzFnD,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,iBACtBxG,OAAA;4BAAMqN,KAAK,EAAE;8BAACC,KAAK,EAAC,MAAM;8BAACmI,SAAS,EAAC;4BAAQ,CAAE;4BAAA3H,QAAA,EAC5C,CAAC,CAACnD,YAAY,CAACnE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAIkC,cAAc,CAAClC,IAAI,CAAC0B,gBAAgB,CAAC;0BAAG;4BAAAqF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrE,CAAC,EAER/C,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,iBACtBxG,OAAA;4BAAMqN,KAAK,EAAE;8BAACC,KAAK,EAAC,KAAK;8BAACmI,SAAS,EAAC;4BAAQ,CAAE;4BAAA3H,QAAA,EAAC;0BAAG;4BAAAP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAExD,CAAC,eACN1N,OAAA;0BAAKqN,KAAK,EAAE;4BAACO,OAAO,EAAE,MAAM;4BAAEqG,UAAU,EAAE,QAAQ;4BAAEuB,IAAI,EAAE,UAAU;4BAAEpD,WAAW,EAAE;0BAAM,CAAE;0BAAAtE,QAAA,EACxF,CAAC,CAACjF,aAAa,gBACd7I,OAAA,CAAC7C,KAAK;4BAAckW,IAAI,EAAC,OAAO;4BAAClG,KAAK,EAAC,EAAE;4BAACoF,SAAS,EAAC,WAAW;4BAAC+C,KAAK,EAAE9O,IAAI,CAACsC,MAAM,IAAI,EAAG;4BAACyM,MAAM,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAE;4BAAAzH,QAAA,eACxG9N,OAAA;8BAAKqN,KAAK,EAAE;gCAACqI,MAAM,EAAC,EAAE;gCAAC9E,KAAK,EAAC,EAAE;gCAAChD,OAAO,EAAC,MAAM;gCAACqG,UAAU,EAAC,QAAQ;gCAACC,QAAQ,EAAC,EAAE;gCAAC5G,KAAK,EAAC,MAAM;gCAACqI,eAAe,EAAC,SAAS;gCAACC,YAAY,EAAC;8BAAC,CAAE;8BAAA9H,QAAA,gBACpI9N,OAAA;gCAAMqN,KAAK,EAAE;kCAACuD,KAAK,EAAC,EAAE;kCAAChD,OAAO,EAAC,MAAM;kCAAC+F,cAAc,EAAC;gCAAQ,CAAE;gCAAA7F,QAAA,EAAEtH,IAAI,CAACjB;8BAAI;gCAAAgI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eAClF1N,OAAA;gCAAMqN,KAAK,EAAE;kCAACuD,KAAK,EAAC,EAAE;kCAAChD,OAAO,EAAC,MAAM;kCAAC+F,cAAc,EAAC;gCAAQ,CAAE;gCAAA7F,QAAA,eAAC9N,OAAA,CAACxB,YAAY;kCAAA+O,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAC;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnF;0BAAC,GAJIwH,MAAM;4BAAA3H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAKX,CAAC,gBAER1N,OAAA;4BAAMqN,KAAK,EAAE;8BAACC,KAAK,EAAE3C,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG;4BAAS,CAAE;4BAAAsH,QAAA,EAAEnD,YAAY,CAACnE,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG;0BAAK;4BAAA+G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEtH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,EACV,CAAC,CAAC7E,aAAa,iBACd7I,OAAA,CAACzC,MAAM;sBACL8P,KAAK,EAAE;wBAAEuI,YAAY,EAAE,KAAK;wBAAEC,UAAU,EAAE;sBAAW,CAAE;sBACvDxC,IAAI,EAAC,OAAO;sBACZtS,IAAI,EAAC,MAAM;sBACXqM,IAAI,eAAEpN,OAAA,CAAC3B,aAAa;wBAACkU,SAAS,EAAC;sBAAa;wBAAAhF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAChDiH,OAAO,EAAEA,CAAA,KAAI7I,mBAAmB,CAACtF,IAAI;oBAAE;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAE1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV,CAAE;cACFoG,MAAM,EAAEA,CAAA,KAAM;gBAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAE,CAAC;cAAA;YAAE,GAzDlC,eAAe;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0DrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,GAGhB,IAAI,EAEL3M,IAAI,IAAIT,iBAAiB,GACvBuD,MAAM,IAAI,CAAC,gBACV7D,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YACpDkG,SAAS,EAAE,eAAgB;YAE3BhD,KAAK,EAAE,GAAI;YACXiD,MAAM,EAAEA,CAAC9K,aAAa,EAAEvC,IAAI,KAAK;cAC/B,IAAGA,IAAI,CAACL,OAAO,IAAIxG,eAAe,CAACyG,SAAS,EAAC;gBAC3C,oBAAQpG,OAAA;kBAAKqN,KAAK,EAAE;oBAAC4F,SAAS,EAAC;kBAAQ,CAAE;kBAAAnF,QAAA,EAAC;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACnD;cACA,oBAAQ1N,OAAA;gBAAKqN,KAAK,EAAE;kBAAC4F,SAAS,EAAC;gBAAQ,CAAE;gBAAAnF,QAAA,EAAE,CAAC,CAAC/E,aAAa,GAAGxJ,MAAM,CAACwJ,aAAa,CAAC,CAACgJ,MAAM,CAAC,YAAY,CAAC,GAAG;cAAE;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YACrH,CAAE;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GARlC,eAAe;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASrB,CAAC,gBAEF1N,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YACpDkG,SAAS,EAAE,cAAe;YAE1BhD,KAAK,EAAE,KAAM;YACbiD,MAAM,EAAEA,CAAA,kBAAK7T,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YACvDoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GAHlC,cAAc;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIpB,CAAC,gBAGJ1N,OAAA,CAACK,WAAW;YAAC8M,KAAK,EAAE,IAAK;YAACoF,SAAS,EAAC,cAAc;YAAAzE,QAAA,gBAChD9N,OAAA,CAACI,MAAM;cACL+M,KAAK,EAAE,KAAM;cACboF,SAAS,EAAC,cAAc;cACxBqB,SAAS,EAAE,cAAe;cAE1BhD,KAAK,EAAE,EAAG;cACViD,MAAM,EAAEA,CAACjL,YAAY,EAACpC,IAAI,KAAK;gBAC7B,IAAG,CAAC,CAACmE,YAAY,CAACnE,IAAI,CAAC,IAAI,CAACA,IAAI,CAACqC,aAAa,IAAIrC,IAAI,CAACiC,QAAQ,IAAI,CAAC,EAAC;kBACnE,oBAAQzI,OAAA;oBAAA8N,QAAA,GAAMtH,IAAI,CAACiC,QAAQ,EAAC,QAAC;kBAAA;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBACrC;gBACA,oBAAQ1N,OAAA;kBAAA8N,QAAA,GAAMlF,YAAY,IAAE,CAAC,EAAC,QAAC;gBAAA;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACvC,CAAE;cACFoG,MAAM,EAAEA,CAAA,KAAM;gBAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAE,CAAC;cAAA;YAAE,GARlC,cAAc;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASpB,CAAC,eACF1N,OAAA,CAACI,MAAM;cACL+M,KAAK,EAAE,OAAQ;cACfoF,SAAS,EAAC,cAAc;cACxBqB,SAAS,EAAE,gBAAiB;cAE5BhD,KAAK,EAAE,GAAI;cACXiD,MAAM,EAAEA,CAACkC,cAAc,EAACvP,IAAI,KAAK;gBAC/B,IAAG,CAACmE,YAAY,CAACnE,IAAI,CAAC,EAAC;kBACrB,IAAGA,IAAI,CAACiC,QAAQ,GAAG,CAAC,IAAMjC,IAAI,CAACiC,QAAQ,GAAGjC,IAAI,CAACsB,YAAY,IAAK,CAAE,EAAC;oBACjE,oBAAS9H,OAAA;sBAAA8N,QAAA,EAAM,CAAAtH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyO,aAAa,KAAE;oBAAG;sBAAA1H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAChD;kBACA,oBAAS1N,OAAA;oBAAA8N,QAAA,EAAK;kBAAC;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBACvB;gBACA,IAAG,CAAClH,IAAI,CAACiC,QAAQ,IAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAACjC,IAAI,CAACqC,aAAa,EAAC;kBAChD,oBAAS7I,OAAA;oBAAA8N,QAAA,EAAK;kBAAC;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBACvB;gBACA,oBAAS1N,OAAA;kBAAA8N,QAAA,EAAM,CAAAtH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyO,aAAa,KAAE;gBAAG;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAChD,CAAE;cACFoG,MAAM,EAAEA,CAAA,KAAM;gBAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAE,CAAC;cAAA;YAAE,GAdlC,gBAAgB;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAetB,CAAC,eACF1N,OAAA,CAACI,MAAM;cACL+M,KAAK,EAAE,MAAO;cACdoF,SAAS,EAAC,cAAc;cACxBqB,SAAS,EAAE,cAAe;cAE1BhD,KAAK,EAAE,EAAG;cACViD,MAAM,EAAEA,CAAC1L,YAAY,EAAC3B,IAAI,KAAK;gBAC7B,IAAG,CAACmE,YAAY,CAACnE,IAAI,CAAC,EAAC;kBACrB,IAAG,CAAC,CAAC2B,YAAY,EAAC;oBAChB,oBAAQnI,OAAA;sBAAA8N,QAAA,EAAMvO,MAAM,CAAC4I,YAAY,CAAC,CAAC4J,MAAM,CAAC,YAAY;oBAAC;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAChE;kBACA,oBAAQ1N,OAAA;oBAAA8N,QAAA,EAAMvO,MAAM,CAACiH,IAAI,CAAC0B,gBAAgB,CAAC,CAAC6J,MAAM,CAAC,YAAY;kBAAC;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBACzE;gBACA,IAAG,CAAClH,IAAI,CAACiC,QAAQ,IAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAACjC,IAAI,CAACqC,aAAa,EAAC;kBAChD,IAAG,CAAC,CAACV,YAAY,EAAC;oBAChB,oBAAQnI,OAAA;sBAAA8N,QAAA,EAAMvO,MAAM,CAAC4I,YAAY,CAAC,CAAC4J,MAAM,CAAC,YAAY;oBAAC;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAChE;kBACA,oBAAQ1N,OAAA,CAAAE,SAAA,mBAAI,CAAC;gBACf;gBACA,oBAAQF,OAAA;kBAAA8N,QAAA,EAAK;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACtB,CAAE;cACFoG,MAAM,EAAEA,CAAA,KAAM;gBAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAE,CAAC;cAAA;YAAE,GAjBlC,cAAc;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,EAEf7J,MAAM,IAAI,CAAC,IAAI9C,IAAI,IAAIT,iBAAiB,gBACvCN,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YACpDkG,SAAS,EAAE,cAAe;YAE1BhD,KAAK,EAAE,GAAI;YACXiD,MAAM,EAAG1L,YAAY,IAAI;cACvB,IAAG,CAAC,CAACA,YAAY,EAAC;gBAChB,oBACEnI,OAAA;kBAAKqN,KAAK,EAAE;oBAAC4F,SAAS,EAAC;kBAAQ,CAAE;kBAAAnF,QAAA,EAAE,CAAC,CAAC3F,YAAY,GAAG5I,MAAM,CAAC4I,YAAY,CAAC,CAAC4J,MAAM,CAAC,YAAY,CAAC,GAAG;gBAAE;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAE7G,CAAC,MAAI;gBACH,oBAAQ1N,OAAA;kBAAKqN,KAAK,EAAE;oBAAC4F,SAAS,EAAC;kBAAQ,CAAE;kBAAAnF,QAAA,EAAC;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACnD;YACF,CAAE;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GAXlC,cAAc;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYpB,CAAC,GAEF,IAAI,EAEL7J,MAAM,IAAI,CAAC,iBACV7D,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAC;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YAClDkG,SAAS,EAAE,eAAgB;YAE3BhD,KAAK,EAAE,GAAI;YACXiD,MAAM,EAAEA,CAAC7K,aAAa,EAAExC,IAAI,KAAK;cAC/B,IAAGA,IAAI,CAACL,OAAO,IAAIxG,eAAe,CAACyG,SAAS,EAAC;gBAC3C,oBAAQpG,OAAA;kBAAKqN,KAAK,EAAE;oBAAC4F,SAAS,EAAC;kBAAQ,CAAE;kBAAAnF,QAAA,EAAC;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACnD;cACA,oBACE1N,OAAA;gBAAKqN,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEqG,UAAU,EAAE;gBAAS,CAAE;gBAAAnG,QAAA,gBACpD9N,OAAA;kBAAMqN,KAAK,EAAE;oBAACC,KAAK,EAAE,MAAM;oBAACsD,KAAK,EAAE,EAAE;oBAAEoF,cAAc,EAAE;kBAAc,CAAE;kBAAAlI,QAAA,GAAC,MAAC,EAAC,CAAAtH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyP,cAAc,KAAE,GAAG;gBAAA;kBAAA1I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3G1N,OAAA;kBAAA8N,QAAA,GAAM,MAAC,EAAC,CAAAtH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0P,cAAc,KAAE,GAAG,EAAC,gBAAI;gBAAA;kBAAA3I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAEV,CAAE;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GAblC,eAAe;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcrB,CAAC,EAEH7J,MAAM,IAAI,CAAC,iBACV7D,OAAA,CAACI,MAAM;YACL+M,KAAK,EAAE,IAAK;YACZyG,SAAS,EAAE,UAAW;YAEtBC,MAAM,EAAEA,CAACsC,QAAQ,EAAE3P,IAAI,KAAK;cAC1B,IAAGA,IAAI,CAACL,OAAO,IAAIxG,eAAe,CAACyG,SAAS,EAAC;gBAC3C,oBAAQpG,OAAA;kBAAAuN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cACrB;cACA,oBACE1N,OAAA;gBAAKqN,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEqG,UAAU,EAAE;gBAAS,CAAE;gBAAAnG,QAAA,gBACpD9N,OAAA;kBAAMqN,KAAK,EAAE;oBAACC,KAAK,EAAE,MAAM;oBAAC8I,WAAW,EAAE,EAAE;oBAAEJ,cAAc,EAAE,cAAc;oBAACR,IAAI,EAAC;kBAAC,CAAE;kBAAA1H,QAAA,GAAE,CAAC,CAACtH,IAAI,CAAC1D,aAAa,GAAG,GAAG,GAAG,EAAE,EAAE0D,IAAI,CAAC1D,aAAa;gBAAA;kBAAAyK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjJ1N,OAAA;kBAAMqN,KAAK,EAAE;oBAACmI,IAAI,EAAC;kBAAC,CAAE;kBAAA1H,QAAA,GAAE,CAAC,CAACtH,IAAI,CAACwC,aAAa,GAAG,GAAG,GAAG,EAAE,EAAExC,IAAI,CAACwC,aAAa;gBAAA;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAEV,CAAE;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GAZlC,UAAU;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAahB,CAAC,EAEH7J,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG9C,IAAI,IAAIT,iBAAiB,gBAC7CN,OAAA,CAACI,MAAM;YACL+M,KAAK,eAAEnN,OAAA;cAAKqN,KAAK,EAAE;gBAAC4F,SAAS,EAAC;cAAQ,CAAE;cAAAnF,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;YACpDkG,SAAS,EAAE,WAAY;YAEvBhD,KAAK,EAAE,IAAK;YACZiD,MAAM,EAAEA,CAAC9L,SAAS,EAACvB,IAAI,KAAK;cAC1B,oBACExG,OAAA;gBAAKqN,KAAK,EAAE;kBAAC4F,SAAS,EAAC;gBAAQ,CAAE;gBAAAnF,QAAA,eAC/B9N,OAAA,CAACvC,QAAQ;kBACPiV,QAAQ,EAAE3K,SAAS,IAAInI,UAAU,CAACyM,eAAe,IAAItE,SAAS,IAAInI,UAAU,CAAC0M,YAAY,IAAI,CAAA9F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,UAAU,KAAIrJ,cAAc,CAACuJ,mBAAoB;kBACpJhB,OAAO,EAAE5B,IAAI,CAAC4B,OAAQ;kBACtBuK,QAAQ,EAAG1I,CAAC,IAAGD,cAAc,CAACC,CAAC,EAAClC,SAAS;gBAAE;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAEV,CAAE;YACFoG,MAAM,EAAEA,CAAA,KAAM;cAAE,OAAO;gBAAEC,OAAO,EAAE;cAAE,CAAC;YAAA;UAAE,GAblC,WAAW;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcjB,CAAC,GAEF,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAED;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACP1N,OAAA;MAAKqN,KAAK,EAAE;QAACqI,MAAM,EAAC;MAAG,CAAE;MAAA5H,QAAA,GACtBjK,MAAM,IAAI,CAAC,gBACV7D,OAAA;QAAKuS,SAAS,EAAC,cAAc;QAAAzE,QAAA,gBAC3B9N,OAAA;UAAKuS,SAAS,EAAC,mBAAmB;UAAAzE,QAAA,gBAChC9N,OAAA;YAAKqN,KAAK,EAAE;cAACO,OAAO,EAAC,MAAM;cAACqG,UAAU,EAAC;YAAQ,CAAE;YAAAnG,QAAA,gBAC/C9N,OAAA;cAAMqN,KAAK,EAAE;gBAAC+I,WAAW,EAAC;cAAE,CAAE;cAAAtI,QAAA,GAAC,iCACvB,EAAC3I,YAAY,CAACS,MAAM,EAAC,GAAC,eAAA5F,OAAA,CAACnC,OAAO;gBAACsP,KAAK,EAAC,0HAAsB;gBAACkJ,SAAS,EAAC,OAAO;gBAAAvI,QAAA,eAAC9N,OAAA,CAACzB,sBAAsB;kBAAC8O,KAAK,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAC6G,UAAU,EAAC;kBAAE;gBAAE;kBAAA5G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7J,CAAC,eACP1N,OAAA,CAACzC,MAAM;cACLgV,SAAS,EAAEtN,aAAa,IAAI,cAAe;cAC3CoI,KAAK,EAAE;gBAAEiJ,QAAQ,EAAE,UAAU;gBAAEhJ,KAAK,EAAE;cAAO,CAAE;cAC/CvM,IAAI,EAAC,MAAM;cACXqM,IAAI,eAAEpN,OAAA;gBAAMuS,SAAS,EAAC;cAAgD;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1EiH,OAAO,EAAEA,CAAA,KAAM;gBACbzP,gBAAgB,CAAC,IAAI,CAAC;gBACtB4E,UAAU,CAAC,MAAM;kBACf,IAAG,CAAClH,SAAS,CAACM,QAAQ,IAAE,CAAC,IAAI,CAAC,EAAC;oBAC7B6G,wBAAwB,CAAChF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEY,UAAU,CAAC;kBACpD,CAAC,MAAI;oBACHvD,yBAAyB,CAAC,CAAC;kBAC7B;kBACA8C,gBAAgB,CAAC,KAAK,CAAC;gBACzB,CAAC,EAAE,GAAG,CAAC;cACT;YAAE;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF1N,OAAA;cAAMqN,KAAK,EAAE;gBAAC+I,WAAW,EAAC;cAAE,CAAE;cAAAtI,QAAA,EAAC;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxC1N,OAAA,CAAChD,IAAI;cAACuZ,KAAK,EAAE,KAAM;cAAChU,IAAI,EAAEE,YAAa;cAAAqL,QAAA,eACrC9N,OAAA,CAAChD,IAAI,CAACsV,IAAI;gBACVjF,KAAK,EAAE;kBAAEsD,MAAM,EAAE;gBAAE,CAAE;gBACrB6B,KAAK,eAAExS,OAAA;kBAAMqN,KAAK,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAEkJ,MAAM,EAAE;kBAAU,CAAE;kBAC5D7B,OAAO,EAAE1G,gBAAiB;kBAAAH,QAAA,EAAC;gBAAK;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;gBACtC;gBAAA;gBAAAI,QAAA,GAEG/J,mBAAmB,iBACpB/D,OAAA,CAAChD,IAAI,CAACsV,IAAI;kBAAC/M,IAAI,EAAC,MAAM;kBAACkR,OAAO;kBAAA3I,QAAA,eAC5B9N,OAAA,CAACG,MAAM;oBACLoS,SAAS,EAAC,qCAAqC;oBAC/Cc,IAAI,EAAC,OAAO;oBACZZ,SAAS,EAAE,EAAG;oBACdiE,UAAU;oBACVrE,YAAY,EAAC,KAAK;oBAClBsE,WAAW,EAAC,sCAAQ;oBACpBC,WAAW,EAAC,cAAI;oBAChBjE,QAAQ,EAAE1I,CAAC,IAAI;sBAAEvF,eAAe,CAAC0J,OAAO,CAAC3I,IAAI,GAAIwE,CAAC,CAACC,MAAM,CAACuB,KAAK;oBAAC,CAAE;oBAClEoL,QAAQ,EAAExI;kBAAyB;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,EAEX3J,mBAAmB,iBACpB/D,OAAA,CAAChD,IAAI,CAACsV,IAAI;kBAACmE,OAAO;kBAAA3I,QAAA,eAChB9N,OAAA,CAACzC,MAAM;oBACL8P,KAAK,EAAE;sBAAEuI,YAAY,EAAE,KAAK;sBAAEC,UAAU,EAAE;oBAAW,CAAE;oBACvDxC,IAAI,EAAC,OAAO;oBACZtS,IAAI,EAAC,MAAM;oBACXqM,IAAI,eAAEpN,OAAA,CAAC3B,aAAa;sBAACkU,SAAS,EAAC;oBAAa;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAChDiH,OAAO,EAAEzG;kBAAkB;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN1N,OAAA;YAAKuS,SAAS,EAAC,oBAAoB;YAAClF,KAAK,EAAE;cAACqI,MAAM,EAAE,GAAG;cAAE9E,KAAK,EAAC,KAAK;cAAEkG,MAAM,EAAE,mBAAmB;cAAElB,YAAY,EAAE;YAAC,CAAE;YAAA9H,QAAA,eAClH9N,OAAA;cAAKqN,KAAK,EAAE;gBAACqI,MAAM,EAAC,MAAM;gBAACqB,OAAO,EAAE;cAAiB,CAAE;cAACxE,SAAS,EAAC,2BAA2B;cAAAzE,QAAA,eAC3F9N,OAAA,CAAC3C,GAAG;gBAAAyQ,QAAA,EACD3I,YAAY,CAAC8B,GAAG,CAACqI,MAAM,IAAI;kBAC1B,oBACEtP,OAAA;oBAAKqN,KAAK,EAAE;sBAACO,OAAO,EAAC,MAAM;sBAACqG,UAAU,EAAC,YAAY;sBAACqC,QAAQ,EAAC,UAAU;sBAACZ,MAAM,EAAC;oBAAG,CAAE;oBAAA5H,QAAA,gBAClF9N,OAAA,CAACzC,MAAM;sBAACwD,IAAI,EAAC,MAAM;sBAACoM,KAAK,EAAE,CAACqC,YAAY,CAACF,MAAM,CAAC,GAAG,OAAOA,MAAM,CAAC3J,UAAU,EAAE,GAAG,OAAO2J,MAAM,CAAC3J,UAAU,WAAW6J,YAAY,CAACF,MAAM,CAAC,EAAG;sBAClIqF,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAChC,MAAM,CAAE;sBAC1CjC,KAAK,EAAE;wBAAC0J,OAAO,EAAC;sBAAC,CAAE;sBACnBrE,QAAQ,EAAE,CAAC,CAAClD,YAAY,CAACF,MAAM,CAAE;sBAAAxB,QAAA,eACvC9N,OAAA,CAACpC,IAAI;wBACHyP,KAAK,EAAE;0BAACsI,eAAe,EAAE,CAACnG,YAAY,CAACF,MAAM,CAAC,GAAG,SAAS,GAAG;wBAAS,CAAE;wBACxEiD,SAAS,EAAE,CAAC,CAAAxN,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEY,UAAU,KAAI2J,MAAM,CAAC3J,UAAU,GAAG,mBAAmB,GAAG,EAAE,IAAI,aAAc;wBACtGqR,SAAS,EAAE,KAAM;wBAAAlJ,QAAA,gBAEjB9N,OAAA;0BACEqN,KAAK,EAAE;4BACLuD,KAAK,EAAC,MAAM;4BACZ8E,MAAM,EAAE,EAAE;4BACVC,eAAe,EAAE,CAACnG,YAAY,CAACF,MAAM,CAAC,GAAG,SAAS,GAAG,MAAM;4BAC3DyD,mBAAmB,EAAC,CAAC;4BACrBG,oBAAoB,EAAC;0BACvB,CAAE;0BAAApF,QAAA,eAEF9N,OAAA;4BAAKqN,KAAK,EAAE;8BAACO,OAAO,EAAC,MAAM;8BAACqG,UAAU,EAAC,QAAQ;8BAACyB,MAAM,EAAC,MAAM;8BAACvB,UAAU,EAAC;4BAAE,CAAE;4BAAArG,QAAA,eAC3E9N,OAAA;8BAAKqN,KAAK,EAAE;gCAACC,KAAK,EAAC;8BAAM,CAAE;8BAAAQ,QAAA,gBACzB9N,OAAA;gCAAKqN,KAAK,EAAE;kCAACO,OAAO,EAAC,MAAM;kCAACqG,UAAU,EAAC;gCAAQ,CAAE;gCAAAnG,QAAA,gBAC/C9N,OAAA;kCAAKqN,KAAK,EAAE;oCAAC6G,QAAQ,EAAC,EAAE;oCAACkC,WAAW,EAAC;kCAAE,CAAE;kCAAAtI,QAAA,EACtCwB,MAAM,CAAC2H,UAAU,IAAIxX,WAAW,CAACyX,gBAAgB,GAAI,IAAI,IAAE5H,MAAM,CAAC6H,cAAc,IAAE,EAAE,CAAC,GAAK,CAAC7H,MAAM,CAAC6H,cAAc,IAAE,EAAE,IAAE;gCAAI;kCAAA5J,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACxH,CAAC,eACN1N,OAAA;kCAAA8N,QAAA,GAAK,gBAAI,EAACwB,MAAM,CAAC8H,WAAW;gCAAA;kCAAA7J,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChC,CAAC,eACN1N,OAAA;gCAAA8N,QAAA,GAAK,0BAAI,EAACvO,MAAM,CAAC+P,MAAM,CAACnH,YAAY,CAAC,CAAC4J,MAAM,CAAC,YAAY,CAAC;8BAAA;gCAAAxE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9D;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN1N,OAAA;0BAAKqN,KAAK,EAAE;4BAACuD,KAAK,EAAC,MAAM;4BAAC8E,MAAM,EAAE;0BAAE,CAAE;0BAAA5H,QAAA,eACpC9N,OAAA;4BAAKqN,KAAK,EAAE;8BAACO,OAAO,EAAC,MAAM;8BAACqG,UAAU,EAAC,QAAQ;8BAACyB,MAAM,EAAE,EAAE;8BAAEvB,UAAU,EAAC,EAAE;8BAAC7G,KAAK,EAAE,CAACkC,YAAY,CAACF,MAAM,CAAC,GAAG,SAAS,GAAG;4BAAM,CAAE;4BAAAxB,QAAA,eAC3H9N,OAAA;8BAAA8N,QAAA,EAAOwB,MAAM,CAAC+H;4BAAU;8BAAA9J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACT1N,OAAA,CAAClC,UAAU;sBAACqP,KAAK,EAAC,mDAAW;sBAACmK,SAAS,EAAEA,CAAA,KAAI3F,YAAY,CAACrC,MAAM,CAAE;sBAAAxB,QAAA,eAChE9N,OAAA,CAACzC,MAAM;wBACL8P,KAAK,EAAE;0BAAEuI,YAAY,EAAE,KAAK;0BAAEC,UAAU,EAAE,WAAW;0BAACjF,KAAK,EAAC,EAAE;0BAAC8E,MAAM,EAAC,EAAE;0BAACY,QAAQ,EAAC,UAAU;0BAACiB,KAAK,EAAC;wBAAC,CAAE;wBACtGlE,IAAI,EAAC,OAAO;wBACZjG,IAAI,eAAEpN,OAAA,CAAC3B,aAAa;0BAACgP,KAAK,EAAE;4BAAC6G,QAAQ,EAAC;0BAAE;wBAAE;0BAAA3G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAEV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1N,OAAA;UAAKuS,SAAS,EAAC,oBAAoB;UAAAzE,QAAA,eACjC9N,OAAA;YAAKuS,SAAS,EAAC,uBAAuB;YAAAzE,QAAA,eACpC9N,OAAA;cAAKuS,SAAS,EAAC,oCAAoC;cAAAzE,QAAA,gBACjD9N,OAAA;gBAAKuS,SAAS,EAAC,uCAAuC;gBAAAzE,QAAA,gBACpD9N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAAzE,QAAA,EAAC;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrC1N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAAzE,QAAA,gBAAC9N,OAAA;oBAAMqN,KAAK,EAAE;sBAAC2I,cAAc,EAAE,cAAc;sBAAC1I,KAAK,EAAC,MAAM;sBAAC8I,WAAW,EAAC;oBAAE,CAAE;oBAAAtI,QAAA,GAAC,QACnG,EAAClL,SAAS,CAACE,aAAa,IAAE,CAAC;kBAAA;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAAC,CAAC9K,SAAS,CAACI,QAAQ,IAAE,CAAC,IAAI,CAAC,GAAG,QAAQJ,SAAS,CAACI,QAAQ,GAAG,GAAG,EAAE;gBAAA;kBAAAuK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1N,OAAA;gBAAKuS,SAAS,EAAC,uCAAuC;gBAAAzE,QAAA,gBACpD9N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAAzE,QAAA,EAAC;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrC1N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAAzE,QAAA,GAAC,QAAC,EAAClL,SAAS,CAACG,UAAU,IAAE,CAAC;gBAAA;kBAAAwK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,EACL,CAAC9K,SAAS,CAACK,iBAAiB,IAAE,CAAC,IAAI,CAAC,gBACrCjD,OAAA;gBAAKuS,SAAS,EAAC,uCAAuC;gBAAAzE,QAAA,gBACpD9N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAAzE,QAAA,EAAC;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC1N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAAzE,QAAA,GAAC,SAAE,EAAClL,SAAS,CAACK,iBAAiB;gBAAA;kBAAAsK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,gBAEN1N,OAAA;gBAAKqN,KAAK,EAAE;kBAACO,OAAO,EAAC;gBAAM;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAEnC,CAAC,CAAC3I,YAAY,gBACb/E,OAAA;gBAAKuS,SAAS,EAAC,uCAAuC;gBAAAzE,QAAA,gBACpD9N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAClF,KAAK,EAAE;oBAACC,KAAK,EAAE1K,SAAS,CAACO,eAAe,IAAI,CAAC,GAAG,MAAM,GAAG;kBAAM,CAAE;kBAAA2K,QAAA,EAAC;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxG1N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAClF,KAAK,EAAE;oBAACC,KAAK,EAAE1K,SAAS,CAACO,eAAe,IAAI,CAAC,GAAG,MAAM,GAAG;kBAAM,CAAE;kBAAA2K,QAAA,GAAC,SACzF,EAAClL,SAAS,CAACO,eAAe,eAC5BnD,OAAA;oBAAMuS,SAAS,EAAC,QAAQ;oBAAClF,KAAK,EAAE;sBAACsI,eAAe,EAAE/S,SAAS,CAACO,eAAe,IAAI,CAAC,GAAG,MAAM,GAAG;oBAAS,CAAE;oBAAA2K,QAAA,GAAC,cACpG,GAAAxM,kBAAA,GAAC6D,YAAY,CAACsB,IAAI,CAAC6I,MAAM,IAAIA,MAAM,CAAC3J,UAAU,IAAIZ,YAAY,CAACY,UAAU,CAAC,cAAArE,kBAAA,uBAAzEA,kBAAA,CAA2E8V,WAAW,EACxF,EAAA7V,mBAAA,GAAA4D,YAAY,CAACsB,IAAI,CAAC6I,MAAM,IAAIA,MAAM,CAAC3J,UAAU,IAAIZ,YAAY,CAACY,UAAU,CAAC,cAAApE,mBAAA,uBAAzEA,mBAAA,CAA2E0V,UAAU,KAAIxX,WAAW,CAACyX,gBAAgB,GAAG,IAAI,GAAG,GAAG,GAAA1V,mBAAA,GAClI2D,YAAY,CAACsB,IAAI,CAAC6I,MAAM,IAAIA,MAAM,CAAC3J,UAAU,IAAIZ,YAAY,CAACY,UAAU,CAAC,cAAAnE,mBAAA,uBAAzEA,mBAAA,CAA2E2V,cAAc,EACzF,EAAA1V,mBAAA,GAAA0D,YAAY,CAACsB,IAAI,CAAC6I,MAAM,IAAIA,MAAM,CAAC3J,UAAU,IAAIZ,YAAY,CAACY,UAAU,CAAC,cAAAlE,mBAAA,uBAAzEA,mBAAA,CAA2EwV,UAAU,KAAIxX,WAAW,CAACyX,gBAAgB,GAAG,EAAE,GAAG,GAAG,eACjIlX,OAAA;sBACEuS,SAAS,EAAC,QAAQ;sBAClBlF,KAAK,EAAE;wBAACC,KAAK,EAAE1K,SAAS,CAACO,eAAe,IAAI,CAAC,GAAG,MAAM,GAAG;sBAAS,CAAE;sBACpEwR,OAAO,EAAEA,CAAA,KAAI;wBAAC3P,eAAe,CAAC,IAAI,CAAC;wBAAC+E,wBAAwB,CAAC,CAAC;sBAAC,CAAE;sBAAA+D,QAAA,eAEjE9N,OAAA,CAAC3B,aAAa;wBAACgP,KAAK,EAAE;0BAAC6G,QAAQ,EAAC;wBAAE;sBAAE;wBAAA3G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,gBAEN1N,OAAA;gBAAKqN,KAAK,EAAE;kBAACO,OAAO,EAAC;gBAAM;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAGtC1N,OAAA;gBAAKuS,SAAS,EAAC,uCAAuC;gBAAAzE,QAAA,gBACpD9N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAAzE,QAAA,EAAC;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrC1N,OAAA;kBAAMuS,SAAS,EAAC,UAAU;kBAAAzE,QAAA,eACxB9N,OAAA;oBAAKqN,KAAK,EAAE;sBAAC6G,QAAQ,EAAC,EAAE;sBAACtG,OAAO,EAAC,MAAM;sBAACqG,UAAU,EAAC,UAAU;sBAACN,cAAc,EAAC;oBAAK,CAAE;oBAAA7F,QAAA,gBAClF9N,OAAA;sBAAMqN,KAAK,EAAE;wBAACC,KAAK,EAAC;sBAAS,CAAE;sBAAAQ,QAAA,EAAC;oBAAC;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxC1N,OAAA;sBAAMuS,SAAS,EAAC,YAAY;sBAAClF,KAAK,EAAE;wBAACC,KAAK,EAAC;sBAAS,CAAE;sBAAAQ,QAAA,EAAElL,SAAS,CAACM,QAAQ,IAAE;oBAAC;sBAAAqK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN1N,OAAA;QAAKqN,KAAK,EAAE;UAACqI,MAAM,EAAE;QAAE;MAAE;QAAAnI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAE7B1N,OAAA;QAAKqN,KAAK,EAAE;UAACO,OAAO,EAAC,MAAM;UAACqG,UAAU,EAAC,UAAU;UAACN,cAAc,EAAC,eAAe;UAACvB,WAAW,EAAC;QAAE,CAAE;QAAAtE,QAAA,gBAC/F9N,OAAA;UAAA8N,QAAA,GACGjK,MAAM,IAAI,CAAC,iBACV7D,OAAA;YAAKqN,KAAK,EAAE;cAAE8G,UAAU,EAAE,EAAE;cAACuB,MAAM,EAAE,EAAE;cAACxB,QAAQ,EAAC,EAAE;cAAC5G,KAAK,EAAE;YAAO,CAAE;YAAAQ,QAAA,GAAC,qBAAI,EAACkC,WAAW;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAE7F7J,MAAM,IAAI,CAAC,iBACV7D,OAAA;YAAKqN,KAAK,EAAE;cAAE8G,UAAU,EAAE,EAAE;cAACuB,MAAM,EAAE,EAAE;cAACxB,QAAQ,EAAC,EAAE;cAAC5G,KAAK,EAAE;YAAO,CAAE;YAAAQ,QAAA,EAAC;UAAkC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAE9G3M,IAAI,IAAIT,iBAAiB,gBACxBN,OAAA;YACEqN,KAAK,EAAE;cACL8G,UAAU,EAAE,EAAE;cACduB,MAAM,EAAE,EAAE;cACVxB,QAAQ,EAAE,EAAE;cACZ5G,KAAK,EAAE;YACT,CAAE;YAAAQ,QAAA,GACH,qJAC4B,EAAC,IAAI,EAAC,0FACnC;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEN,IAAI,EAEL3M,IAAI,IAAIT,iBAAiB,iBACxBN,OAAA;YAAKqN,KAAK,EAAE;cAAE8G,UAAU,EAAE,EAAE;cAACuB,MAAM,EAAE,EAAE;cAACxB,QAAQ,EAAC,EAAE;cAAC5G,KAAK,EAAE;YAAO,CAAE;YAAAQ,QAAA,gBAClE9N,OAAA;cAAMqN,KAAK,EAAE;gBAACC,KAAK,EAAC;cAAM,CAAE;cAAAQ,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,0CAAQ,EAAC,IAAI,EAAC,oLACxD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAEP3M,IAAI,IAAIR,kBAAkB,iBAC3BP,OAAA;YACEqN,KAAK,EAAE;cACL8G,UAAU,EAAE,EAAE;cACduB,MAAM,EAAE,EAAE;cACVxB,QAAQ,EAAE,EAAE;cACZ5G,KAAK,EAAE;YACT,CAAE;YAAAQ,QAAA,GACH,+CACW,EAAC,IAAI,EAAC,yFAClB;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC,eACN1N,OAAA;UAAKqN,KAAK,EAAE;YAAC8E,YAAY,EAAC;UAAE,CAAE;UAAArE,QAAA,gBAC5B9N,OAAA;YAAKqN,KAAK,EAAE;cAAC4F,SAAS,EAAE;YAAQ,CAAE;YAAAnF,QAAA,eAChC9N,OAAA,CAACzC,MAAM;cACLgV,SAAS,EAAE1O,MAAM,IAAI,CAAC,GAAG,cAAc,GAAC,WAAY;cACpD9C,IAAI,EAAC,SAAS;cACdyW,OAAO,EAAEhT,OAAQ;cACjB6I,KAAK,EAAE;gBAAEoK,QAAQ,EAAE,GAAG;gBAAE/B,MAAM,EAAE,EAAE;gBAAEE,YAAY,EAAE;cAAE,CAAE;cACtDjB,OAAO,EAAE1I,KAAM;cAAA6B,QAAA,EAEZjK,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,QAAQc,mBAAmB,CAACsB,MAAM,CAACM,OAAO,IAAIA,OAAO,CAAC6B,OAAO,IAAI7B,OAAO,CAACwB,SAAS,IAAInI,UAAU,CAACyM,eAAe,IAAI9F,OAAO,CAACwB,SAAS,IAAInI,UAAU,CAAC0M,YAAY,CAAC,CAAC1G,MAAM;YAAM;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACL7J,MAAM,IAAI,CAAC,iBACV7D,OAAA;YAAKqN,KAAK,EAAE;cAAC4F,SAAS,EAAE,OAAO;cAACiB,QAAQ,EAAC,EAAE;cAAC5G,KAAK,EAAC,MAAM;cAACoK,SAAS,EAAC;YAAC,CAAE;YAAA5J,QAAA,GAAC,yDAC3D,eAAA9N,OAAA;cAAGqN,KAAK,EAAE;gBAACC,KAAK,EAAC;cAAS,CAAE;cAACqH,OAAO,EAAEA,CAAA,KAAI1D,UAAU,CAAC,CAAC,CAAE;cAAAnD,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,KAAC,EAAC,IAAI,EAAC,GAAC,eAAA1N,OAAA;cAAGqN,KAAK,EAAE;gBAACC,KAAK,EAAC;cAAS,CAAE;cAACqH,OAAO,EAAEA,CAAA,KAAI1D,UAAU,CAAC,CAAC,CAAE;cAAAnD,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,6BACtJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN1N,OAAA,CAAC2X,kBAAkB;MACjB7W,MAAM,EAAEA,MAAO;MACf8W,OAAO,EAAErU,QAAS;MAClB3C,QAAQ,EAAEmM,SAAU;MACpBnK,SAAS,EAAEA,SAAU;MACrB/B,IAAI,EAAEmM,SAAU;MAChB/I,OAAO,EAAEA,OAAQ;MACjBG,GAAG,EAAEA,GAAI;MACTE,IAAI,EAAEA;IAAK;MAAAiJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eACf1N,OAAA,CAAC6X,gBAAgB;MACfD,OAAO,EAAEjU,eAAgB;MACzB/C,QAAQ,EAAEA,CAAA,KAAIgD,kBAAkB,CAAC,KAAK;IAAE;MAAA2J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7C1N,OAAA,CAACjC,cAAc;MACboP,KAAK,EAAC,cAAI;MACVoF,SAAS,EAAC,WAAW;MACrB3B,KAAK,EAAE,GAAI;MACXO,IAAI,EAAEtM,OAAQ;MACdjE,QAAQ,EAAEA,CAAA,KAAIkE,UAAU,CAAC,KAAK,CAAE;MAChCgT,QAAQ;MACRC,YAAY,EAAE,KAAM;MACpBC,MAAM,eACJhY,OAAA;QAAKqN,KAAK,EAAE;UAACO,OAAO,EAAC,MAAM;UAAC+F,cAAc,EAAC;QAAQ,CAAE;QAAA7F,QAAA,eACnD9N,OAAA,CAACzC,MAAM;UAACwD,IAAI,EAAE,SAAU;UAACsM,KAAK,EAAE;YAACuI,YAAY,EAAC;UAAC,CAAE;UAACjB,OAAO,EAAEA,CAAA,KAAI7P,UAAU,CAAC,KAAK,CAAE;UAAAgJ,QAAA,EAAC;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CACN;MAAAI,QAAA,gBAED9N,OAAA;QAAKqN,KAAK,EAAE;UAACO,OAAO,EAAC,MAAM;UAAC+F,cAAc,EAAC,QAAQ;UAAC+D,SAAS,EAAC;QAAE,CAAE;QAAA5J,QAAA,EAAC;MAAsB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/F1N,OAAA;QAAKqN,KAAK,EAAE;UAACO,OAAO,EAAC,MAAM;UAAC+F,cAAc,EAAC,QAAQ;UAACf,YAAY,EAAC;QAAE,CAAE;QAAA9E,QAAA,EAAC;MAAe;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CAAC;EAAA,eACf,CAAC;AACL;AAACvM,EAAA,CAjgDeR,UAAU;EAAA,QACM1C,SAAS,EACnBqB,UAAU,EAY4DJ,0BAA0B,EACnGlB,WAAW,EACXE,WAAW,EACblB,IAAI,CAACwF,OAAO,EACJxF,IAAI,CAACwF,OAAO,EAapBxF,IAAI,CAAC8G,QAAQ,EA+Od3E,WAAW,EAiLQA,WAAW;AAAA;AAAA8Y,EAAA,GA/b9BtX,UAAU;AAmgD1B,SAASuX,aAAaA,CAAC;EAACrX,IAAI;EAAEC,MAAM;EAAE8B,SAAS;EAAEqB,OAAO;EAAEG,GAAG;EAAEE;AAAI,CAAC,EAAC;EAAA6T,GAAA;EACnE,MAAM,CAAC5V,IAAI,CAAC,GAAGvF,IAAI,CAACwF,OAAO,CAAC,CAAC;EAC7B,MAAM+N,SAAS,GAAGvT,IAAI,CAAC8G,QAAQ,CAAC,WAAW,EAAEvB,IAAI,CAAC;EAClD;;EAEA3F,SAAS,CAAC,MAAI;IACZ,MAAMwb,KAAK,GAAG,EAAEnU,OAAO,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,OAAO,CAAC,IAAIkU,WAAW,CAAC,MAAI;MAC9D,IAAIC,MAAM,GAAG;QAACnU,OAAO,EAAEF,OAAO,CAACE;MAAO,CAAC;MACvC,IAAG,CAAC,CAACrD,MAAM,EAAC;QACVwX,MAAM,CAACxX,MAAM,GAAGA,MAAM;MACxB;MACAnC,yBAAyB,CAAC2Z,MAAM,CAAC,CAAC9J,IAAI,CAACQ,MAAM,IAAI;QAC/C,IAAGA,MAAM,CAAC5L,UAAU,IAAI,GAAG,EAAC;UAC1B,IAAG4L,MAAM,CAAC9F,UAAU,IAAIxJ,YAAY,CAAC6Y,aAAa,EAAC;YACjDC,aAAa,CAACJ,KAAK,CAAC;YACpBvX,IAAI,IAAIA,IAAI,CAACmO,MAAM,CAAClO,MAAM,CAAC;YAC3BtB,UAAU,CAACiZ,OAAO,CAAC,MAAM,CAAC;UAC5B;QACF;MACF,CAAC,CAAC;IACJ,CAAC,EAAC,IAAI,CAAC;IAEP,OAAO,MAAM;MACXD,aAAa,CAACJ,KAAK,CAAC;IACtB,CAAC;EACH,CAAC,EAAC,CAACnU,OAAO,CAAC,CAAC;EAEZrH,SAAS,CAAC,MAAI;IACZ,IAAG2T,SAAS,EAAC,CAAC;EAChB,CAAC,EAAC,CAACA,SAAS,CAAC,CAAC;EAEd,oBACAvQ,OAAA,CAAChD,IAAI;IACHuV,SAAS,EAAC,oBAAoB;IAC9BhQ,IAAI,EAAEA,IAAK;IACXgD,IAAI,EAAC;IACL;IAAA;IACA8M,YAAY,EAAC,KAAK;IAAAvE,QAAA,eAElB9N,OAAA,CAAChD,IAAI,CAACsV,IAAI;MACVmE,OAAO;MAAA3I,QAAA,gBAEL9N,OAAA;QAAKuS,SAAS,EAAC,YAAY;QAAAzE,QAAA,gBACzB9N,OAAA;UAAKuS,SAAS,EAAC,OAAO;UAAAzE,QAAA,gBACpB9N,OAAA;YAAKuS,SAAS,EAAC,cAAc;YAAAzE,QAAA,gBAC3B9N,OAAA;cAAK4Q,KAAK,EAAE,EAAG;cAAC8E,MAAM,EAAE,EAAG;cAACgD,GAAG,EAAEC,OAAO,CAAC,qCAAqC;YAAE;cAAApL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,gBACpF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN1N,OAAA;YAAKuS,SAAS,EAAC,eAAe;YAAClF,KAAK,EAAE;cAACsI,eAAe,EAAC;YAAS,CAAE;YAAA7H,QAAA,EAC/D1J,GAAG,gBACDpE,OAAA,CAAC1C,KAAK;cACLob,GAAG,EAAEtU,GAAI;cACTwM,KAAK,EAAE,GAAI;cACX8E,MAAM,EAAE,GAAI;cACZkD,OAAO,EAAE,KAAM;cACfC,UAAU,eAAE7Y,OAAA,CAACX,QAAQ;gBAAAkO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,gBACD1N,OAAA;cAAKuS,SAAS,EAAC,iBAAiB;cAAAzE,QAAA,EAAC;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1N,OAAA;UAAKuS,SAAS,EAAC,OAAO;UAAAzE,QAAA,gBACpB9N,OAAA;YAAKuS,SAAS,EAAC,cAAc;YAAAzE,QAAA,gBAC3B9N,OAAA;cAAK4Q,KAAK,EAAE,EAAG;cAAC8E,MAAM,EAAE,EAAG;cAACgD,GAAG,EAAEC,OAAO,CAAC,sCAAsC;YAAE;cAAApL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,sBACrF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN1N,OAAA;YAAKuS,SAAS,EAAC,eAAe;YAAClF,KAAK,EAAE;cAACsI,eAAe,EAAC;YAAS,CAAE;YAAA7H,QAAA,EAC/DxJ,IAAI,gBACFtE,OAAA,CAAC1C,KAAK;cACLob,GAAG,EAAEpU,IAAK;cACVsM,KAAK,EAAE,GAAI;cACX8E,MAAM,EAAE,GAAI;cACZkD,OAAO,EAAE,KAAM;cACfC,UAAU,eAAE7Y,OAAA,CAACX,QAAQ;gBAAAkO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,gBACD1N,OAAA;cAAKuS,SAAS,EAAC,iBAAiB;cAAAzE,QAAA,EAAC;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1N,OAAA;QAAKuS,SAAS,EAAC,UAAU;QAAAzE,QAAA,gBACvB9N,OAAA;UAAMuS,SAAS,EAAC,yBAAyB;UAAClF,KAAK,EAAE;YAAC8G,UAAU,EAAC,EAAE;YAACiC,WAAW,EAAC;UAAE,CAAE;UAAAtI,QAAA,EAAC;QAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzF1N,OAAA;UAAMuS,SAAS,EAAC,iEAAiE;UAAAzE,QAAA,EAAElL,SAAS,CAACM;QAAQ;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAER,CAAC;AACT;AAACyK,GAAA,CArFQD,aAAa;EAAA,QACLlb,IAAI,CAACwF,OAAO,EACTxF,IAAI,CAAC8G,QAAQ;AAAA;AAAAgV,GAAA,GAFxBZ,aAAa;AAuFtB,SAASP,kBAAkBA,CAAC;EAAC7W,MAAM;EAAE8W,OAAO,GAAC,KAAK;EAAEhX,QAAQ;EAAEgC,SAAS;EAAE/B,IAAI;EAAEoD,OAAO;EAAEG,GAAG;EAAEE;AAAI,CAAC,EAAE;EAClG,oBAAOtE,OAAA,CAACjC,cAAc;IACpBwU,SAAS,EAAC,oBAAoB;IAC9BpF,KAAK,eACHnN,OAAA;MAAMqN,KAAK,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEqG,UAAU,EAAE;MAAS,CAAE;MAAAnG,QAAA,GAAC,0BAEtD,eAAA9N,OAAA;QAAMuS,SAAS,EAAC,aAAa;QAAClF,KAAK,EAAE;UAAE8G,UAAU,EAAE,EAAE;UAAE7G,KAAK,EAAE;QAAO,CAAE;QAAAQ,QAAA,GAAC,gCACjE,EAAC,CAAA7J,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,OAAO,KAAE,EAAE;MAAA;QAAAoJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP;IACDkD,KAAK,EAAE,GAAI;IACXO,IAAI,EAAEyG,OAAQ;IACdhX,QAAQ,EAAEA,QAAS;IACnBkX,QAAQ;IACRE,MAAM,EAAE,IAAK;IACbD,YAAY,EAAE,KAAM;IACpBgB,cAAc;IAAAjL,QAAA,eACZ9N,OAAA,CAACkY,aAAa;MACdtV,SAAS,EAAEA,SAAU;MACrB9B,MAAM,EAAEA,MAAO;MACfD,IAAI,EAAEA,IAAK;MACXoD,OAAO,EAAEA,OAAQ;MACjBG,GAAG,EAAEA,GAAI;MACTE,IAAI,EAAEA;IAAK;MAAAiJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AACnB;AAACsL,GAAA,GA1BQrB,kBAAkB;AA4B3B,SAASE,gBAAgBA,CAAC;EAACD,OAAO,GAAC,KAAK;EAAChX;AAAQ,CAAC,EAAE;EAClD,oBAAOZ,OAAA,CAACjC,cAAc;IACpBwU,SAAS,EAAC,oBAAoB;IAC9BpF,KAAK,EAAC,0BAAM;IACZyD,KAAK,EAAE,GAAI;IACXO,IAAI,EAAEyG,OAAQ;IACdhX,QAAQ,EAAEA,QAAS;IACnBkX,QAAQ;IACRE,MAAM,EAAE,IAAK;IACbD,YAAY,EAAE,KAAM;IACpBgB,cAAc;IAAAjL,QAAA,eACZ9N,OAAA,CAACiZ,gBAAgB;MAAA1L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AACnB;AAACwL,GAAA,GAbQrB,gBAAgB;AAezB,SAASoB,gBAAgBA,CAAC,CAAC,CAAC,EAAC;EAAAE,GAAA;EAC3B,MAAM,CAAC7F,UAAU,EAAC8F,aAAa,CAAC,GAAGvc,QAAQ,CAAC,EAAE,CAAC;EAC/C,MAAMwc,OAAO,GAAG,CACd;IAAClM,KAAK,EAAE,IAAI;IAACyG,SAAS,EAAE,aAAa;IAAEtL,GAAG,EAAE;EAAa,CAAC,EAC1D;IAAC6E,KAAK,EAAE,IAAI;IAACyG,SAAS,EAAE,UAAU;IAAEtL,GAAG,EAAE;EAAU,CAAC,EACpD;IAAC6E,KAAK,EAAE,IAAI;IAACyG,SAAS,EAAE,UAAU;IAAEtL,GAAG,EAAE;EAAU,CAAC,EACpD;IAAC6E,KAAK,EAAE,MAAM;IAACyG,SAAS,EAAE,SAAS;IAAEtL,GAAG,EAAE;EAAS,CAAC,EACpD;IAAC6E,KAAK,EAAE,MAAM;IAACyG,SAAS,EAAE,YAAY;IAAEtL,GAAG,EAAE;EAAY,CAAC,EAC1D;IAAC6E,KAAK,EAAE,SAAS;IAACyG,SAAS,EAAE,mBAAmB;IAAEtL,GAAG,EAAE;EAAmB,CAAC,EAC3E;IAAC6E,KAAK,EAAE,SAAS;IAACyG,SAAS,EAAE,kBAAkB;IAAEtL,GAAG,EAAE;EAAkB,CAAC,EACzE;IAAC6E,KAAK,EAAE,SAAS;IAACyG,SAAS,EAAE,kBAAkB;IAAEtL,GAAG,EAAE;EAAkB,CAAC,EACzE;IAAC6E,KAAK,EAAE,KAAK;IAACyG,SAAS,EAAE,MAAM;IAAEtL,GAAG,EAAE;EAAM,CAAC,CAC9C;EAED,oBACEtI,OAAA,CAACxC,KAAK;IACJ6V,IAAI,EAAC,OAAO;IACZgG,OAAO,EAAEA,OAAQ;IACjB/F,UAAU,EAAEA,UAAW;IACvBC,UAAU,EAAE;MACV+C,QAAQ,EAAE,CAAC,cAAc,CAAC;MAC1BjD,IAAI,EAAE,OAAO;MACbiG,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,IAAI;MACrBC,KAAK,EAAElG,UAAU,CAAC1N,MAAM;MACxB6T,SAAS,EAAGD,KAAK,IAAG;QAAC,OAAO,IAAIA,KAAK,GAAG;MAAA;IAC1C;EAAE;IAAAjM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAEV;AAACyL,GAAA,CA5BQF,gBAAgB;AAAAS,GAAA,GAAhBT,gBAAgB;AA8BzB,eAAe,SAASU,eAAeA,CAAC;EAAC/B,OAAO,GAAC,KAAK;EAAChX,QAAQ;EAACE,MAAM;EAACD,IAAI;EAACE,IAAI,GAACT,iBAAiB;EAACU,SAAS;EAAEC;AAAW,CAAC,EAAE;EAAA2Y,GAAA;EAC1H,MAAM,CAACrO,QAAQ,EAACrK,WAAW,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC9C;EACA,MAAMgd,SAAS,GAAG/c,MAAM,CAACgd,QAAQ,CAAC3M,KAAK,CAAC;EACxCvQ,SAAS,CAAC,MAAM;IACd,IAAIgb,OAAO,EAAE;MACXiC,SAAS,CAACzL,OAAO,GAAG0L,QAAQ,CAAC3M,KAAK;MAClC2M,QAAQ,CAAC3M,KAAK,GAAGpM,IAAI,KAAKR,kBAAkB,GAAG,SAAS,GAAG,MAAM;IACnE,CAAC,MAAM;MACLuZ,QAAQ,CAAC3M,KAAK,GAAG0M,SAAS,CAACzL,OAAO;IACpC;IACA,OAAO,MAAM;MACX0L,QAAQ,CAAC3M,KAAK,GAAG0M,SAAS,CAACzL,OAAO;IACpC,CAAC;EACH,CAAC,EAAE,CAACwJ,OAAO,EAAE7W,IAAI,CAAC,CAAC;EACnB,SAASgZ,UAAUA,CAAA,EAAE;IACnB,IAAGxO,QAAQ,EAAC;MACVxO,KAAK,CAACmQ,OAAO,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,IAAI,eAAEpN,OAAA,CAAC1B,yBAAyB;UAAC+O,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;QAC9DoK,QAAQ,EAAE,IAAI;QACdnK,OAAO,eAAE3N,OAAA;UAAKqN,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE;UAAS,CAAE;UAAAC,QAAA,eAChE9N,OAAA;YAAA8N,QAAA,GAAM,cAAE,EAAC,GAAG/M,IAAI,IAAIT,iBAAiB,GAAG,MAAM,GAAG,MAAM,EAAE,EAAC,gCAAK,EAAC,GAAGS,IAAI,IAAIT,iBAAiB,GAAG,IAAI,GAAG,IAAI,EAAE,EAAC,SAAE;UAAA;YAAAiN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnH,CAAC;QACNK,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,GAAG;QACfnN,IAAI,EAACA,CAAA,KAAK;UACRD,QAAQ,CAAC,CAAC;UACVM,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,CAAC;IACJ,CAAC,MAAI;MACHN,QAAQ,CAAC,CAAC;IACZ;EACF;EACA,oBAAOZ,OAAA,CAACF,eAAe;IACbyS,SAAS,EAAC,6BAA6B;IACvCuF,QAAQ;IACR3K,KAAK,EAAEpM,IAAI,KAAKR,kBAAkB,GAAC,SAAS,GAAC,MAAO;IACpDqQ,KAAK,EAAE,KAAM;IACb6G,QAAQ,EAAE,KAAM;IAChBuC,QAAQ,EAAE,KAAM;IAChB7I,IAAI,EAAEyG,OAAQ;IACdqC,OAAO,EAAEF,UAAW;IACpB/B,MAAM,EAAE,IAAK;IACbe,cAAc;IAAAjL,QAAA,eACZ9N,OAAA,CAACW,UAAU;MAACG,MAAM,EAAEA,MAAO;MACfF,QAAQ,EAAEA,QAAS;MACnBC,IAAI,EAAEA,IAAK;MACXE,IAAI,EAAEA,IAAK;MACXC,SAAS,EAAEA,SAAU;MACrBC,WAAW,EAAEA,WAAY;MACzBC,WAAW,EAAEA;IAAY;MAAAqM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtEAkM,GAAA,CAxDwBD,eAAe;AAAAO,GAAA,GAAfP,eAAe;AAAA,IAAA1B,EAAA,EAAAa,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAQ,GAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAlC,EAAA;AAAAkC,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}