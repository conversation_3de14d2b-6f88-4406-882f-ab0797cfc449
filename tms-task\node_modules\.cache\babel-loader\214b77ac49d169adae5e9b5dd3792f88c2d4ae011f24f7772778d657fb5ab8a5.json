{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { splitLines } from '../../../base/common/strings.js';\nimport { Position } from '../core/position.js';\nimport { PrefixSumComputer } from './prefixSumComputer.js';\nexport class MirrorTextModel {\n  constructor(uri, lines, eol, versionId) {\n    this._uri = uri;\n    this._lines = lines;\n    this._eol = eol;\n    this._versionId = versionId;\n    this._lineStarts = null;\n    this._cachedTextValue = null;\n  }\n  dispose() {\n    this._lines.length = 0;\n  }\n  get version() {\n    return this._versionId;\n  }\n  getText() {\n    if (this._cachedTextValue === null) {\n      this._cachedTextValue = this._lines.join(this._eol);\n    }\n    return this._cachedTextValue;\n  }\n  onEvents(e) {\n    if (e.eol && e.eol !== this._eol) {\n      this._eol = e.eol;\n      this._lineStarts = null;\n    }\n    // Update my lines\n    const changes = e.changes;\n    for (const change of changes) {\n      this._acceptDeleteRange(change.range);\n      this._acceptInsertText(new Position(change.range.startLineNumber, change.range.startColumn), change.text);\n    }\n    this._versionId = e.versionId;\n    this._cachedTextValue = null;\n  }\n  _ensureLineStarts() {\n    if (!this._lineStarts) {\n      const eolLength = this._eol.length;\n      const linesLength = this._lines.length;\n      const lineStartValues = new Uint32Array(linesLength);\n      for (let i = 0; i < linesLength; i++) {\n        lineStartValues[i] = this._lines[i].length + eolLength;\n      }\n      this._lineStarts = new PrefixSumComputer(lineStartValues);\n    }\n  }\n  /**\n   * All changes to a line's text go through this method\n   */\n  _setLineText(lineIndex, newValue) {\n    this._lines[lineIndex] = newValue;\n    if (this._lineStarts) {\n      // update prefix sum\n      this._lineStarts.setValue(lineIndex, this._lines[lineIndex].length + this._eol.length);\n    }\n  }\n  _acceptDeleteRange(range) {\n    if (range.startLineNumber === range.endLineNumber) {\n      if (range.startColumn === range.endColumn) {\n        // Nothing to delete\n        return;\n      }\n      // Delete text on the affected line\n      this._setLineText(range.startLineNumber - 1, this._lines[range.startLineNumber - 1].substring(0, range.startColumn - 1) + this._lines[range.startLineNumber - 1].substring(range.endColumn - 1));\n      return;\n    }\n    // Take remaining text on last line and append it to remaining text on first line\n    this._setLineText(range.startLineNumber - 1, this._lines[range.startLineNumber - 1].substring(0, range.startColumn - 1) + this._lines[range.endLineNumber - 1].substring(range.endColumn - 1));\n    // Delete middle lines\n    this._lines.splice(range.startLineNumber, range.endLineNumber - range.startLineNumber);\n    if (this._lineStarts) {\n      // update prefix sum\n      this._lineStarts.removeValues(range.startLineNumber, range.endLineNumber - range.startLineNumber);\n    }\n  }\n  _acceptInsertText(position, insertText) {\n    if (insertText.length === 0) {\n      // Nothing to insert\n      return;\n    }\n    const insertLines = splitLines(insertText);\n    if (insertLines.length === 1) {\n      // Inserting text on one line\n      this._setLineText(position.lineNumber - 1, this._lines[position.lineNumber - 1].substring(0, position.column - 1) + insertLines[0] + this._lines[position.lineNumber - 1].substring(position.column - 1));\n      return;\n    }\n    // Append overflowing text from first line to the end of text to insert\n    insertLines[insertLines.length - 1] += this._lines[position.lineNumber - 1].substring(position.column - 1);\n    // Delete overflowing text from first line and insert text on first line\n    this._setLineText(position.lineNumber - 1, this._lines[position.lineNumber - 1].substring(0, position.column - 1) + insertLines[0]);\n    // Insert new lines & store lengths\n    const newLengths = new Uint32Array(insertLines.length - 1);\n    for (let i = 1; i < insertLines.length; i++) {\n      this._lines.splice(position.lineNumber + i - 1, 0, insertLines[i]);\n      newLengths[i - 1] = insertLines[i].length + this._eol.length;\n    }\n    if (this._lineStarts) {\n      // update prefix sum\n      this._lineStarts.insertValues(position.lineNumber, newLengths);\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}