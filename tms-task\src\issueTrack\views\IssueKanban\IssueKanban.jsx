/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from "react";
import { But<PERSON>, Drawer, Space, Layout, Modal } from "antd";
import { useParams, useOutletContext, useNavigate, useLocation, useSearchParams } from "react-router-dom";
import InfiniteScroll from 'react-infinite-scroll-component';
import { useQueryClient } from "@tanstack/react-query";
import { getIconValueByIdType } from "src/issueTrack/utils/ArrayUtils";
import { eConsoleNodeId, ePagination } from "@common/utils/enum";
import * as httpIssue from "@common/api/http";
import { useQueryIssue500getIssueView } from "src/issueTrack/service/issueHooks";
import avatarImg from "@common/assets/images/avatar.png";
import noAvatarImg from "@common/assets/images/noAvatar.png";
import IssueDetail from "../IssueDetail/IssueDetail";
import { useIssueSearchParams } from "src/issueTrack/service/issueSearchHooks";
import { track012 } from '@common/utils/ApiPath';
import { isEmpty } from "@common/utils/ArrayUtils";
import "./IssueKanban.scss"
import TLoading from "@common/components/TLoading";
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { priPermission, getNodeNameByNodeId } from "@common/utils/logicUtils";
import { globalEventBus } from "@common/utils/eventBus";
import DraggableDrawer from "@components/DraggableDrawer";

// TODO:看板的issueId没有更改
// 看板 kanban.js
const { Kanban, Toolbar, defaultCardShape, getDefaultCardMenuItems } = window.kanban
const { Content } = Layout

export default function IssueKanban() {
  const board = useRef({});
  const scrollRef = useRef();
  const { teamId, nodeId: issueListNodeId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const createFlg = searchParams.get("createFlg"); //是否创建
  const open = searchParams.get("open"); //是否打开详情

  const { subclassAttrList, userList, spaceUserList, selectionList, objInfo, keywords, searchQuery, viewMode, projectInfo, setCurrentIssueIdx, setting320Result, finalCreateFlg } = useOutletContext();
  const queryClient = useQueryClient();
  const { issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams();// issue路由配置，页面刷新

  // 看板列表
  const [kanbanPageNo, setKanbanPageNo] = useState(1); // 看板页码
  const { kanbanTotal = 0, kanbanMaxCount = 0, kanbanGroupList = [], iconSelectionLid, isLoading: isLoadingGetIssueView } = useQueryIssue500getIssueView(teamId, issueListNodeId, "", "", searchQuery, keywords, kanbanPageNo, ePagination.PageSize_30)
  const [refreshingFlg, setRefreshingFlg] = useState(false); //是否正在"刷新中"
  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);
  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);
  const [addCard, setAddCard] = useState(false);
  const [currentKanbanIssueIdx, setCurrentKanbanIssueIdx] = useState(0); // 看板选中index
  const [currentKanbanGroup, setCurrentKanbanGroup] = useState(); //记忆用户点击了看板的哪一个组，对应IssueKanban中的 columnId: _group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题
  const [columns, setColumns] = useState([]);
  const [cards, setCards] = useState([]);
  //以最长的那个分组的issue长度来进行判断是否会有下一页
  const [issueDetailDrawerVisibleFlg, setIssueDetailDrawerVisibleFlg] = useState(false);
  const [initLoading, setInitLoading] = useState(true); // 初始化加载
  const [dataSourceKey, setDataSourceKey] = useState(0); // 数据源变化标识

  console.log("kanbanTotal,kanbanMaxCount,kanbanGroupList,kanbanPageNo, initLoading, cards", kanbanTotal, kanbanMaxCount, kanbanGroupList, kanbanPageNo, initLoading, cards)

  // const [kanbanGroupList, setKanbanGroupList] = useState([]); //看板视图时，分组列表以及在其内部的issueList (数据源)

  //issue编号发生变化, 判定是否可以上一条和下一条
  //不能使用currentIssueIdx来判定，因为鼠标点击某一个issue时，currentIssueId发生变化，需要重新计算其对应的index
  useEffect(() => {
    if (!isEmpty(cards) && !!issueNodeId) {
      handleByObjNodeId();
    }
  }, [cards, issueNodeId])

  // isLoadingGetIssueView 下拉加载都会调用，因此需要判断是否是第一次加载
  useEffect(() => {
    if (!isLoadingGetIssueView) {
      setInitLoading(false);
    }
  }, [isLoadingGetIssueView])

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef?.current?.el?.addEventListener('scroll', onScrollListener)
    }
    return () => {
      scrollRef?.current?.el?.removeEventListener('scroll', onScrollListener);
    }
  }, [scrollRef])

 
  useEffect(()=>{
    if(createFlg){
      setIssueSearchParams({queryId: -1});
      setTimeout(()=>{
        // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑
        // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；
       openCreateIssueModal()
      }, 2000)
    } 
  },[createFlg])

  function openCreateIssueModal () {
    globalEventBus.emit("openCreateIssueModalEvent", "", {
      nodeId: issueListNodeId, projectInfo, callback: onPostIssueCreated
    })
  }

  function handleByObjNodeId() {
    try {
      const { idx, kanban } = findIndexByObjNodeId(cards, issueNodeId)
      console.log("kanban", kanban, idx)
      if(!!kanban){
        setPreviousIssueLinkDisabledFlg(idx == 0);
        setNextIssueLinkDisabledFlg(idx == kanban.issueTotalNum - 1);
      }
    } catch (e) {
      console.error("handleByObjNodeId", e)
    }
  }

  // 根据objNodeId查询数据
  function findIndexByObjNodeId(cards, issueNodeId) {
    const idx = cards.findIndex(_issue => _issue.nodeId == issueNodeId);
    const kanban = cards.find(_issue => _issue.nodeId == issueNodeId);
    return { idx, kanban }
  }

  function gotoKanbanNextIssue(currentGroupCards) {
    // 当前已加载的选中看板组总数
    let currentGroupCardsLength = currentGroupCards.length
    console.log("currentKanbanIssueIdx", currentKanbanIssueIdx, currentGroupCardsLength)
    // 当前选中idx为当前看板组最后一条，判断是否有下一页
    if (currentKanbanIssueIdx == currentGroupCardsLength - 1) {
      let totalIssueCnt = kanbanGroupList.find(group => group.type == currentKanbanGroup).issueTotalNum;
      console.log("totalIssueCnt", totalIssueCnt)
      if (currentGroupCardsLength < totalIssueCnt) { //可以继续往后翻页
        loadKanbanIssueList();
        setCurrentKanbanIssueIdx(currentKanbanIssueIdx + 1);
      }
    } else {
      console.log("currentGroupCardscurrentGroupCards", currentGroupCards[currentKanbanIssueIdx + 1])
      const issueNodeId = currentGroupCards[currentKanbanIssueIdx + 1]?.nodeId;
      setCurrentKanbanIssueIdx(currentKanbanIssueIdx + 1);
      // 触发kanban组件选择card事件
      board.current.selectCard(currentGroupCards[currentKanbanIssueIdx + 1]);
      setIssueSearchParams({ viewMode, issueNodeId, queryId }); // 更改路由issueId
    }
  }

  //看板上一条issue
  function gotoKanbanPreviousIssue() {
    if (currentKanbanIssueIdx > 0) {
      let groupCards = board.current.getAreaCards(currentKanbanGroup);
      const issueNodeId = groupCards[currentKanbanIssueIdx - 1]?.nodeId;
      setCurrentKanbanIssueIdx(currentKanbanIssueIdx - 1);
      // 触发kanban组件选择card事件
      board.current.selectCard(groupCards[currentKanbanIssueIdx - 1]);
      setIssueSearchParams({ viewMode, issueNodeId, queryId }); // 更改路由issueId
    }
  }
  // 下一条
  function _nextIssue() {
    let groupCards = board.current.getAreaCards(currentKanbanGroup);
    console.log("groupCards", groupCards)
    gotoKanbanNextIssue(groupCards); //传回当前列的现有长度，以让IssueHome中判定是否需要翻页加载
  }

  //刷新issue列表
  function refreshIssueList() {
    // 如果当前是第一页强制刷新，非第一页则刷新第一页
    if (kanbanPageNo == 1) {
      queryClient.invalidateQueries([track012])
    } else {
      setKanbanPageNo(1)
    }
    setTimeout(() => {
      setRefreshingFlg(false);
    }, 500);
  }
  // card属性
  const cardShape = {
    ...defaultCardShape,
    label: true, description: true, progress: true, start_date: true, end_date: true,
    priority: true, color: true, cover: true, attached: true,
    menu: { show: false },
  };

  const columnShape = {
    menu: {
      show: true,
      items: ({ column, columnIndex, store }) => {
        console.log(column, columnIndex, store)
        return [
          { id: "set-edit", icon: "wxi-edit", label: "Rename" },
          { id: "delete-column", icon: "wxi-delete", label: "Delete" },
          { id: "add-card", icon: "wxi-plus", label: "Add new card" },
          { id: "move-column:left", icon: "wxi-arrow-left", label: "Move left", disabled: true },
          { id: "move-column:right", icon: "wxi-arrow-right", label: "Move right", disabled: true }
        ]
      }
    }
  };

  // card 模板
  function cardTemplate({ cardFields, selected, dragging, cardShape }) {
    const { label, prefix, planFinishDt, icon, userName, avatar } = cardFields;
    // TODO:userName和avatar接口返回，无数据
    return `
        <div class="custom-card">
          <div class="custom-card-content">
            <div>${icon}</div>
            <a class="fontsize-12">${prefix}</a>
          </div>
          <div>
            <div class="issueTitle">${label}</div>
          </div>
          <div class="issueinfo">
            <div style="color:#999">
              <span class="iconfont rili fontsize-12"></span>
              <span class="fontsize-12"> ${planFinishDt ? planFinishDt : ""}</span>
            </div>
            <div class="user">
              <div class="fontsize-12">${userName ? userName : ""}</div>
              <div class="avatar">
                <img src=${avatar ? avatar : (userName ? avatarImg : noAvatarImg)}></img>
              </div>
            </div>
          </div>
        </div>
    `;
  }

  // 看板滚动加载数据
  function loadKanbanIssueList() {
    setKanbanPageNo(kanbanPageNo + 1)
  }
  // 看板移动卡片
  function onKanbanColumnMoved({ id, columnId }) {
    const [nodeId,] = Array.isArray(id) ? id : id.split(",");
    let request = {
      teamId: teamId,
      nodeId: nodeId,
      [eConsoleNodeId.Nid_11110_Issue_Status]: columnId
    };

    httpIssue.track_007_edit_issue(request)
      .then((res) => {
        if (res.resultCode === 200) {
          queryClient.invalidateQueries([track012])
          // board.current.updateCard({
          // })
        }
      })
  }

  // 选择卡片 
  function onKanbanCellSelected({ id, groupMode }) {
    if(!id){ // FIXME: 点击空白区域会触发该事件
      return;
    }
    const [nodeId, group] = id.split(",");
    if (groupMode === false) {
      let idx = kanbanGroupList.find(_group => _group.type == group).issueList.findIndex(_issue => _issue.nodeId == nodeId);
      setCurrentKanbanGroup(group);
      setCurrentKanbanIssueIdx(idx);
      setIssueDetailDrawerVisibleFlg(true);
      setIssueSearchParams({ viewMode, issueNodeId: nodeId, queryId }); // 更改路由issueId
    }
  }

  // card找当前
  const findCard = (id) => {
    let findItem = cards.findIndex(el => el.id == id)
    return findItem
  }

  // 清理看板容器的函数
  const cleanupKanban = () => {
    const kanbanElement = document.getElementById('issuekanban');
    if (kanbanElement) {
      // 清理容器内容
      kanbanElement.innerHTML = '';
      console.log('清理看板容器内容');
    }
    
    // 销毁看板实例
    if (!isEmpty(board.current)) {
      try {
        board.current.destroy();
        console.log('销毁看板实例');
      } catch (e) {
        console.log('销毁看板实例时出错:', e);
      }
      board.current = {};
    }
  };



  //初始化
  useEffect(() => {
    if (!initLoading && kanbanTotal != 0) {
      // 使用清理函数彻底清理
      cleanupKanban();
      
      // 重新创建看板
      board.current = new Kanban("#issuekanban", {
        columns, cards, cardShape,
        cardTemplate: window.kanban.template(card => cardTemplate(card)), // 版本更新的写法
        readonly: { edit: false, add: false },
        columnShape: { fixedHeaders: true }
        // scrollType: "column",
      }); // columnShape,
      

    }
    // setTimeout(()=>{
    //   adjustScrollPosition();
    // },500)
  }, [initLoading, kanbanTotal, dataSourceKey]);

  //数据绑定
  useEffect(() => {
    if (!initLoading) {
      // 如果第一页 并且没有新增
      if (kanbanPageNo == 1 && kanbanGroupList?.length > 0 && !addCard) {
        const columns = []
        kanbanGroupList.map((group, index) => {
          if (group.name != null) {
            let columnObj = {
              id: group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题
              label: group.name + "(" + group.issueTotalNum + ")"
            }
            columns.push(columnObj)
          }
        });
        // 看板卡片
        let cardsList = []
        kanbanGroupList.map((_group) => {
          (_group.issueList || []).map(_issue => {
            if (_issue.id) {
              let cardObj = {
                ..._issue,
                column: _group.type,
                id: [_issue.nodeId, _group.type].toString(), //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(",")解开id,type
                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),
                label: _issue.title,
              }
              cardsList.push(cardObj)
            }
          })
        });
        console.log("columns,cardsList", columns, cardsList)
        setCards(cardsList)
        
        // 如果看板已存在，使用parse更新；否则让初始化useEffect处理
        if (!isEmpty(board.current) && board.current.parse) {
          board.current.parse({
            columns: columns,
            cards: cardsList,
          });
          board.current.events.on("move-card", onKanbanColumnMoved);
          board.current.events.on("select-card", onKanbanCellSelected);
        }
      }
      // 如果第一页 并且新增，只要插入新增项
      if (kanbanGroupList?.length > 0 && addCard) {
        // 看板列表
        const columns = []
        kanbanGroupList.map((group, index) => {
          if (group.name != null) {
            let columnObj = {
              id: group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题
              label: group.name + "(" + group.issueTotalNum + ")"
            }
            columns.push(columnObj)
          }
        });
        kanbanGroupList.map((_group) => {
          (_group.issueList || []).map(_issue => {
            if (_issue.id) {
              let cardObj = {
                ..._issue,
                column: _group.type,
                id: [_issue.nodeId, _group.type].toString(), //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(",")解开id,type
                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),
                label: _issue.title,
              }
              let findIndex = findCard([_issue.nodeId, _group.type].toString())
              // fix tmsbug-10144:看板视图下创建问题，提交之后 页面没刷新
              if (findIndex == -1) {
                cards.splice(0, 0, cardObj)
              }
            }
          })
        });
        console.log("columns,cards", columns, cards)
        // parse
        board.current.parse({
          columns: columns,
          cards: cards,
        });
        setAddCard(false)
        setCards([...cards])
      }
      // 看板页码不为1，并且非新增
      if (kanbanGroupList?.length > 0 && kanbanPageNo != 1 && !addCard) {
        // 看板列表
        const columns = []
        kanbanGroupList.map((group, index) => {
          if (group.name != null) {
            let columnObj = {
              id: group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题
              label: group.name + "(" + group.issueTotalNum + ")"
            }
            columns.push(columnObj)
          }
        });
        // 看板卡片
        kanbanGroupList.map((_group) => {
          (_group.issueList || []).map(_issue => {
            if (_issue.id) {
              let cardObj = {
                ..._issue,
                column: _group.type,
                id: [_issue.nodeId, _group.type].toString(), //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(",")解开id,type
                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),
                label: _issue.title,
              }
              let findIndex = findCard([_issue.nodeId, _group.type].toString())
              if (findIndex >= 0) {
                cards[findIndex] = cardObj
              } else {
                cards.push(cardObj)
              }
              console.log("findCard(_issue.id)", findIndex)
            }
          })
        });
        // parse
        board.current.parse({
          columns: columns,
          cards: cards,
        });
        setCards([...cards])
      }
    }
  }, [initLoading, kanbanTotal]);

  // 监听搜索条件变化，触发重新初始化
  useEffect(() => {
    if (!initLoading) {
      // 使用清理函数彻底清理
      cleanupKanban();
      
      // 触发重新初始化
      setDataSourceKey(prev => prev + 1);
      console.log('搜索条件变化，触发重新初始化');
    }
  }, [searchQuery, keywords]);

  // issue创建完成后，页面刷新
  function onPostIssueCreated() {
    setAddCard(true)
    refreshIssueList()
  }

  // 
  function onScrollListener(e) {
    console.log("onScrollListener start");
    let headerElem = document.querySelector(".wx-header");
    if (headerElem && e?.target) {
      headerElem.style.top = e.target.scrollTop + "px";
    }
  }

 async function addClick(){
    return Modal.confirm({
        title: "提示",
        icon: <ExclamationCircleOutlined />,
        centered: true,
        content: <p>{`当前列表页为"查询"页，将转至“${await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)??"全部"}”页创建问题。`}</p>,
        okText: " 好的，继续新建",
        cancelText: "取消",
        onOk: () => {
          if(!!projectInfo?.allIssueNodeId){
            navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}/kanban?createFlg=true`);
          }
        },
        onCancel: () => {
          console.log("Cancel");
        },
      });
  }

  return <Content>
    <div className="flexBetween" style={{ height: 45 }}>
      <Space size={10}>
        {searchQuery?.length > 0 || keywords ?
          <div style={{ paddingLeft:15, fontSize: 12, color: "#666" }}>结果({kanbanTotal}条)</div>
          : <div style={{ paddingLeft:15,fontSize: 12, color: "#666" }}>全部({kanbanTotal}条)</div>
        }
        {/* <Button
          className={refreshingFlg && 'refresh-icon'}
          style={{ position: 'relative', color: '#999' }}
          type="link"
          icon={<span className="refresh-position fontsize-14 iconfont shuaxin1" />}
          onClick={() => {
            setRefreshingFlg(true);
            refreshIssueList()
          }}
        /> */}
        {(priPermission(setting320Result.privWrite) && finalCreateFlg) && (
          <Button className="defaultBtn" type="primary" onClick={() => !!objInfo?.objId ? addClick():openCreateIssueModal()}>+ {projectInfo.issueAlias}</Button>
        )}
      </Space>
    </div>
    <div id='scrollableDiv' className="kanban-scroll">
      {
        <InfiniteScroll
              ref={scrollRef}
              style={{ flex: "auto" }}
              scrollableTarget="scrollableDiv"
              dataLength={kanbanPageNo * ePagination.PageSize_30}
              next={(e) => loadKanbanIssueList()}
              hasMore={kanbanPageNo * ePagination.PageSize_30 < kanbanMaxCount}
              // onScroll={onScrollListener}
              // loader={<Skeleton avatar paragraph={{ rows: 1,}} active loading={loading}/>}
              // endMessage={<div>数据加载完毕!</div>}
            >
              {
                 initLoading ? <div className="blank-page"> <TLoading /></div> :
                 // Issue总数为0、且初始化（页码为1）时，显示空白页
                 kanbanPageNo == 1 && kanbanTotal == 0 ?
                   <div
                     style={{
                       // textAlign: 'center',
                     }}
                   >
                     <div className="blank-page">
                       <div className="blank-page-title">这里是空的</div>
                       {
                        priPermission(setting320Result.privWrite) ?  
                        <div className="blank-page-des fontsize-12 flexCenter">
                          <span style={{ paddingRight: 5 }}>你可以</span>
                          <a onClick={() =>openCreateIssueModal()}>新建{projectInfo.issueAlias}</a>
                        </div> : <></>
                       }
                     </div>
                   </div> : <div id="issuekanban" className="issuekanban" style={{ minHeight: '400px', display: 'block' }} />
              }
            </InfiniteScroll>
      }
    </div>
    <DraggableDrawer
      destroyOnClose
      className="kanban-detail"
      width={'60%'}
      minWidth={'50%'}
      maxWidth={'90%'}
      onClose={() => setIssueDetailDrawerVisibleFlg(false)}
      open={issueDetailDrawerVisibleFlg}
      closable={true}
      title={`${projectInfo.issueAlias}详情`}
    >
      <IssueDetail
        viewMode={viewMode}
        selectionList={selectionList}
        userList={userList}
        spaceUserList={spaceUserList}
        gotoPreviousIssue={gotoKanbanPreviousIssue}
        gotoNextIssue={_nextIssue}
        previousIssueLinkDisabledFlg={previousIssueLinkDisabledFlg}
        nextIssueLinkDisabledFlg={nextIssueLinkDisabledFlg}
        projectInfo={projectInfo}
        setCurrentIssueIdx={setCurrentIssueIdx}
        issueList={(kanbanGroupList || [])?.find(kanban => kanban.type == currentKanbanGroup)?.issueList || []}
      />
    </DraggableDrawer>
  </Content>
}