{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\personal\\\\views\\\\PersonalData\\\\personalData.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { QuestionsUploadIcon } from \"@components/IconUtil\";\nimport { Avatar, Button, Cascader, DatePicker, Input, message, Form, Radio, Select, Tag, Upload, Modal } from \"antd\";\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport { EditOutlined, CameraOutlined, CloseCircleFilled } from \"@ant-design/icons\";\nimport ImgCrop from \"antd-img-crop\";\nimport moment from \"moment\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { team_619_user_info } from \"@common/api/http\";\nimport * as http from \"@common/api/http\";\nimport { useDispatch, shallowEqual, useSelector } from \"react-redux\";\nimport { province, personalList } from \"../../utils/Config\";\nimport { setLoginInfo } from \"@/login/store/actionCreators\";\nimport \"./personalData.scss\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { NoAvatarIcon } from '@common/components/IconUtil';\nimport SettingsDrawer from 'src/settings/views/SettingsDrawer';\nimport { useParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\n\n// 个人资料\nexport default function PersonalData(params) {\n  _s();\n  var _state$userInfo3;\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const {\n    teamId\n  } = useParams();\n  const state = useSelector(state => ({\n    userInfo: state.getIn([\"login\", \"loginInfo\", \"userInfo\"])\n  }), shallowEqual);\n  const settingsDrawerTab = 'personal';\n  const [settingsDrawerVisible, setSettingsDrawerVisible] = useState(false);\n  useEffect(() => {\n    var _state$userInfo;\n    if (!!((_state$userInfo = state.userInfo) !== null && _state$userInfo !== void 0 && _state$userInfo.userId)) {\n      var _state$userInfo2;\n      console.log('用户Id', (_state$userInfo2 = state.userInfo) === null || _state$userInfo2 === void 0 ? void 0 : _state$userInfo2.userId);\n      getPersonInfo(state.userInfo);\n    }\n  }, [(_state$userInfo3 = state.userInfo) === null || _state$userInfo3 === void 0 ? void 0 : _state$userInfo3.userId]);\n\n  // 获取个人资料\n  const getPersonInfo = userInfo => {\n    http.uc_001_get_person_info({\n      userId: userInfo.userId\n    }).then(res => {\n      if (res.resultCode === 200) {\n        // setStatisticalStudy({...res.personalInfo.statisticalStudy})\n        // setInterestList([...res.personalInfo.interestList])\n\n        form.setFieldValue(\"avatar\", res.personalInfo.baseInfo[\"avatar\"]);\n        personalList.forEach(item => {\n          form.setFieldValue(item.name, res.personalInfo.baseInfo[item.name]);\n        });\n      } else {\n        // setStatisticalStudy({})\n        // setInterestList([])\n\n        form.setFieldValue(\"avatar\", \"\");\n        personalList.forEach(item => {\n          form.setFieldValue(item.name, \"\");\n        });\n      }\n    });\n  };\n\n  // 保存个人资料\n  const savePersonInfo = () => {\n    let formData = form.getFieldsValue(true);\n    let personalInfo = {\n      ...formData\n      // avatar: personalObj.avatar,\n      // userName: formData.userName.formItemValue,\n      // gender: formData.gender.formItemValue,\n      // birthday: formData.birthday.formItemValue,\n      // job: formData.job.formItemValue,\n      // workDate: formData.workDate.formItemValue,\n      // city: formData.city.formItemValue,\n      // college: formData.college.formItemValue\n    };\n    http.uc_002_save_person_info(personalInfo).then(res => {\n      if (res.resultCode === 200) {\n        getPersonInfo(state.userInfo);\n        // fixBUG tmsbug-3347\n        updateUserInfo();\n      }\n    });\n  };\n  const updateUserInfo = () => {\n    team_619_user_info({}).then(res => {\n      if (res.resultCode == 200) {\n        dispatch(setLoginInfo({\n          isAuthenticated: true,\n          userInfo: res.userInfo\n        }));\n      }\n    });\n  };\n\n  /*  // 保存用户感兴趣的标签\r\n    const saveUserInterest = (tagList) => {\r\n      http.uc_004_save_user_interest({interestIds: tagList}).then((res) => {\r\n        if(res.resultCode === 200) {\r\n          setAddInterestVisible(false);\r\n          getPersonInfo(state.userInfo.userId)\r\n        }\r\n      })\r\n    }*/\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"PersonalData\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"PersonalData-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: 14\n          },\n          children: \"\\u57FA\\u672C\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          colon: false,\n          className: \"PersonalData-form\",\n          onFieldsChange: savePersonInfo,\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u5934\\u50CF\",\n            name: \"avatar\",\n            children: /*#__PURE__*/_jsxDEV(PersonLogoFormItem, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 17\n          }, this), personalList.map((item, index) => /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: item.label,\n            name: item.name,\n            children: /*#__PURE__*/_jsxDEV(CustomFormItem, {\n              type: item.type,\n              label: item.label,\n              name: item.name,\n              visible: setSettingsDrawerVisible,\n              teamId: teamId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 21\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 19\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), !!teamId && /*#__PURE__*/_jsxDEV(SettingsDrawer, {\n      visible: settingsDrawerVisible,\n      onClose: () => setSettingsDrawerVisible(false),\n      teamId: teamId,\n      defaultTab: settingsDrawerTab\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 20\n    }, this)]\n  }, void 0, true);\n}\n\n// 自定义FormItem\n_s(PersonalData, \"CL9FRsAzNjz4IbsnhDl1l3O3ycs=\", false, function () {\n  return [Form.useForm, useDispatch, useParams, useSelector];\n});\n_c = PersonalData;\nfunction CustomFormItem({\n  value = '',\n  onChange,\n  type,\n  label,\n  name,\n  visible,\n  teamId\n}) {\n  _s2();\n  const [modifyFlag, setModifyFlag] = useState(false);\n  const modifyRef = useRef();\n  // 日期格式\n  const dateFormat = \"YYYY/MM/DD\";\n  const triggerChange = changedValue => {\n    setModifyFlag(false);\n    onChange === null || onChange === void 0 ? void 0 : onChange(changedValue);\n  };\n  useEffect(() => {\n    if (modifyFlag && type !== '') {\n      modifyRef.current.focus({\n        cursor: \"end\"\n      });\n    }\n  }, [modifyFlag]);\n  const modifyClick = () => {\n    setModifyFlag(true);\n  };\n  function content() {\n    if (!teamId) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u4E2A\\u4EBA\\u4E2D\\u5FC3\\u7684\\u7528\\u6237\\u540D\\u662F\\u5168\\u5C40\\u7684\\uFF0C\\u5982\\u60A8\\u9700\\u8981\\u4FEE\\u6539\\u60A8\\u5728\\u5F53\\u524D\\u56E2\\u961F\\u7684\\u6210\\u5458\\u540D(\\u4F9B\\u5176\\u4ED6\\u6210\\u5458\\u8BC6\\u522B), \\u53EF\\u8FD4\\u56DE\\u56E2\\u961F\\u5E76\\u70B9\\u51FB \\u4E2A\\u4EBA\\u8BBE\\u7F6E(\\u56E2\\u961F\\u5185) \\u8FDB\\u884C\\u8C03\\u6574\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 16\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"\\u4E2A\\u4EBA\\u4E2D\\u5FC3\\u7684\\u7528\\u6237\\u540D\\u662F\\u5168\\u5C40\\u7684\\uFF0C\\u5982\\u60A8\\u9700\\u8981\\u4FEE\\u6539\\u60A8\\u5728\\u5F53\\u524D\\u56E2\\u961F\\u7684\\u6210\\u5458\\u540D(\\u4F9B\\u5176\\u4ED6\\u6210\\u5458\\u8BC6\\u522B), \\u53EF\\u70B9\\u51FB \", /*#__PURE__*/_jsxDEV(\"a\", {\n        onClick: () => {\n          visible(true);\n        },\n        children: \"\\u4E2A\\u4EBA\\u8BBE\\u7F6E(\\u56E2\\u961F\\u5185)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 63\n      }, this), \"  \\u8FDB\\u884C\\u8C03\\u6574\\u3002\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 14\n    }, this);\n  }\n  const modifyBlur = e => {\n    Modal.info({\n      title: \"友情提示\",\n      content: content(),\n      maskClosable: true,\n      //centered: true, // 居中\n      okText: \"我知道了\",\n      width: 500,\n      onOk: () => {\n        triggerChange(e.target.value);\n      }\n    });\n  };\n  const genderChange = e => {\n    triggerChange(e);\n  };\n\n  // 选择日期\n  const datePickerChange = (date, dateString) => {\n    triggerChange(dateString);\n  };\n\n  // 选择城市\n  const cascaderChange = (value, selectedOptions) => {\n    triggerChange(value.join('/'));\n  };\n\n  // 获取form item编辑样式\n  const getEditFormItemValue = (type, label) => {\n    switch (type) {\n      case 'input':\n        return /*#__PURE__*/_jsxDEV(Input, {\n          className: \"MemberFromItem-inpvalue\",\n          autoComplete: \"off\",\n          style: {\n            width: 240\n          },\n          defaultValue: value,\n          ref: modifyRef,\n          onBlur: modifyBlur,\n          onPressEnter: modifyBlur,\n          placeholder: `请输入${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 24\n        }, this);\n      case 'select':\n        return /*#__PURE__*/_jsxDEV(Select, {\n          className: \"MemberFromItem-sex\",\n          style: {\n            width: 240\n          },\n          ref: modifyRef,\n          defaultValue: value,\n          onChange: genderChange,\n          onBlur: () => setModifyFlag(false),\n          placeholder: `请选择${label}`,\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: 0,\n            children: \"\\u672A\\u77E5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: 1,\n            children: \"\\u7537\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: 2,\n            children: \"\\u5973\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 24\n        }, this);\n      case 'date':\n        return /*#__PURE__*/_jsxDEV(DatePicker, {\n          style: {\n            width: 240\n          },\n          ref: modifyRef,\n          defaultValue: value != null && value != '' ? moment(value, dateFormat) : null,\n          format: dateFormat,\n          onBlur: () => setModifyFlag(false),\n          placeholder: `请选择${label}`,\n          onChange: datePickerChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 24\n        }, this);\n      case 'cascader':\n        return /*#__PURE__*/_jsxDEV(Cascader, {\n          style: {\n            width: 240\n          },\n          ref: modifyRef,\n          defaultValue: value != null && value.split('/'),\n          options: province,\n          onBlur: () => setModifyFlag(false),\n          onChange: cascaderChange,\n          placeholder: `请选择${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 24\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n    }\n  };\n\n  // 格式化form item只读状态样式\n  const getReadFormItemValue = name => {\n    switch (name) {\n      case 'gender':\n        return value == 1 ? '男' : value == 2 ? '女' : '未知';\n      default:\n        return value;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"MemberFromItem\",\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      children: modifyFlag ? getEditFormItemValue(type, label) : getReadFormItemValue(name)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 9\n    }, this), !modifyFlag ? /*#__PURE__*/_jsxDEV(Button, {\n      className: \"CustomFromItem-editIcon\",\n      size: \"small\",\n      type: \"link\",\n      icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 19\n      }, this),\n      onClick: modifyClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 7\n  }, this);\n}\n\n// 上传头像\n_s2(CustomFormItem, \"pa6/AEo6mxolpas8N48ga/1oZ6U=\");\n_c2 = CustomFormItem;\nfunction PersonLogoFormItem({\n  value,\n  onChange\n}) {\n  _s3();\n  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑状态\n\n  // 上传头像\n  const avatarUpload = link => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(link);\n  };\n  const deleteLogo = () => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(''); //20240313 Jim 从 null 改为 ''\n  };\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"PersonalData-form-avatar\",\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        className: \"PersonalData-avatar\",\n        src: value,\n        icon: /*#__PURE__*/_jsxDEV(NoAvatarIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"PersonalData-form-upload\",\n        children: /*#__PURE__*/_jsxDEV(CameraOutlined, {\n          className: \"PersonalData-form-uploadIcon\",\n          onClick: event => setIsModalVisible(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), !!value && /*#__PURE__*/_jsxDEV(CloseCircleFilled, {\n        title: \"\\u5220\\u9664\\u5934\\u50CF\",\n        className: \"delete-PersonalData-avatar\",\n        onClick: deleteLogo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      title: \"\\u4E0A\\u4F20logo\",\n      className: \"avatarUpload-modal\",\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      children: /*#__PURE__*/_jsxDEV(ImgUpload, {\n        avatarUpload: avatarUpload,\n        onCancel: () => setIsModalVisible(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 10\n  }, this);\n}\n\n// 图片上传\n_s3(PersonLogoFormItem, \"ZFwHEtl1ZQoNaflbPbQvGOHlSaM=\");\n_c3 = PersonLogoFormItem;\nfunction ImgUpload(props) {\n  const {\n    avatarUpload,\n    onCancel\n  } = props;\n  const dataSource = {\n    maxCount: 1,\n    name: \"file\",\n    multiple: false,\n    showUploadList: false,\n    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,\n    beforeUpload: file => {\n      const isPNG = file.type === \"image/png\" || file.type === 'image/jpeg';\n      if (!isPNG) {\n        globalUtil.error(`${file.name}不是图片格式`);\n      }\n      return isPNG || Upload.LIST_IGNORE;\n    },\n    onChange(info) {\n      onCancel();\n      const {\n        status,\n        response\n      } = info.file;\n      if (status == \"uploading\") {\n        console.log(info.file, info.fileList);\n      }\n      if (status === \"done\") {\n        if (response.resultCode == 200) {\n          avatarUpload(response.link);\n          globalUtil.success('上传成功');\n        } else {\n          globalUtil.error(\"上传失败\");\n        }\n      } else if (status === \"error\") {\n        globalUtil.error(`${info.file.name} file upload failed.`);\n      }\n    },\n    onDrop(e) {\n      console.log(\"Dropped files\", e.dataTransfer.files);\n    }\n  };\n\n  // 预览/裁剪图片\n  const onPreview = async file => {\n    let src = file.url;\n    if (!src) {\n      src = await new Promise(resolve => {\n        const reader = new FileReader();\n        reader.readAsDataURL(file.originFileObj);\n        reader.onload = () => resolve(reader.result);\n      });\n    }\n    const image = new Image();\n    image.src = src;\n    const imgWindow = window.open(src);\n    imgWindow.document.write(image.outerHTML);\n  };\n  return /*#__PURE__*/_jsxDEV(ImgCrop, {\n    modalClassName: \"clippingImgCrop\",\n    rotate: true,\n    modalTitle: \"编辑图片\",\n    modalOk: \"\\u786E\\u8BA4\",\n    modalCancel: \"\\u53D6\\u6D88\",\n    children: /*#__PURE__*/_jsxDEV(Dragger, {\n      ...dataSource,\n      onPreview: onPreview,\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"ant-upload-drag-icon\",\n        children: /*#__PURE__*/_jsxDEV(QuestionsUploadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"ant-upload-text\",\n        children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u52A8\\u56FE\\u7247\\u81F3\\u6B64\\u5904\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#999',\n          marginTop: 2\n        },\n        children: \"\\u56FE\\u7247\\u683C\\u5F0F\\uFF1Ajpg\\u3001png\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 374,\n    columnNumber: 5\n  }, this);\n}\n_c4 = ImgUpload;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"PersonalData\");\n$RefreshReg$(_c2, \"CustomFormItem\");\n$RefreshReg$(_c3, \"PersonLogoFormItem\");\n$RefreshReg$(_c4, \"ImgUpload\");", "map": {"version": 3, "names": ["QuestionsUploadIcon", "Avatar", "<PERSON><PERSON>", "<PERSON>r", "DatePicker", "Input", "message", "Form", "Radio", "Select", "Tag", "Upload", "Modal", "DraggablePopUp", "EditOutlined", "CameraOutlined", "CloseCircleFilled", "ImgCrop", "moment", "React", "useEffect", "useState", "useRef", "team_619_user_info", "http", "useDispatch", "shallowEqual", "useSelector", "province", "personalList", "setLoginInfo", "globalUtil", "NoAvatarIcon", "SettingsDrawer", "useParams", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Option", "PersonalData", "params", "_s", "_state$userInfo3", "form", "useForm", "dispatch", "teamId", "state", "userInfo", "getIn", "settingsDrawerTab", "settingsDrawerVisible", "setSettingsDrawerVisible", "_state$userInfo", "userId", "_state$userInfo2", "console", "log", "getPersonInfo", "uc_001_get_person_info", "then", "res", "resultCode", "setFieldValue", "personalInfo", "baseInfo", "for<PERSON>ach", "item", "name", "savePersonInfo", "formData", "getFieldsValue", "uc_002_save_person_info", "updateUserInfo", "isAuthenticated", "children", "className", "style", "marginLeft", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "colon", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "label", "PersonLogoFormItem", "map", "index", "CustomFormItem", "type", "visible", "onClose", "defaultTab", "_c", "value", "onChange", "_s2", "modifyFlag", "setModifyFlag", "modifyRef", "dateFormat", "trigger<PERSON>hange", "changedValue", "current", "focus", "cursor", "modifyClick", "content", "onClick", "modifyBlur", "e", "info", "title", "maskClosable", "okText", "width", "onOk", "target", "genderChange", "datePickerChange", "date", "dateString", "cascaderChange", "selectedOptions", "join", "getEditFormItemValue", "autoComplete", "defaultValue", "ref", "onBlur", "onPressEnter", "placeholder", "format", "split", "options", "getReadFormItemValue", "size", "icon", "_c2", "_s3", "isModalVisible", "setIsModalVisible", "avatarUpload", "link", "deleteLogo", "src", "event", "open", "onCancel", "footer", "ImgUpload", "_c3", "props", "dataSource", "maxCount", "multiple", "showUploadList", "action", "process", "env", "REACT_APP_BASE_URL", "beforeUpload", "file", "isPNG", "error", "LIST_IGNORE", "status", "response", "fileList", "success", "onDrop", "dataTransfer", "files", "onPreview", "url", "Promise", "resolve", "reader", "FileReader", "readAsDataURL", "originFileObj", "onload", "result", "image", "Image", "imgWindow", "window", "document", "write", "outerHTML", "modalClassName", "rotate", "modalTitle", "modalOk", "modalCancel", "color", "marginTop", "_c4", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/personal/views/PersonalData/personalData.jsx"], "sourcesContent": ["import { QuestionsUploadIcon } from \"@components/IconUtil\";\r\nimport {Ava<PERSON>,Button,Cascader,DatePicker,Input,message,Form,Radio,Select,Tag,Upload, Modal} from \"antd\";\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";\r\nimport { EditOutlined, CameraOutlined, CloseCircleFilled } from \"@ant-design/icons\";\r\nimport ImgCrop from \"antd-img-crop\";\r\nimport moment from \"moment\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { team_619_user_info } from \"@common/api/http\";\r\nimport * as http from \"@common/api/http\";\r\nimport { useDispatch, shallowEqual, useSelector } from \"react-redux\";\r\nimport {province, personalList } from \"../../utils/Config\";\r\nimport { setLoginInfo } from \"@/login/store/actionCreators\";\r\nimport \"./personalData.scss\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { NoAvatarIcon } from '@common/components/IconUtil';\r\nimport SettingsDrawer from 'src/settings/views/SettingsDrawer';\r\nimport { useParams } from \"react-router-dom\";\r\n\r\nconst { Dragger } = Upload;\r\nconst { Option } = Select;\r\n\r\n// 个人资料\r\nexport default function PersonalData(params) {\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n    const {teamId} = useParams();\r\n    const state = useSelector((state) => ({\r\n      userInfo: state.getIn([\"login\", \"loginInfo\", \"userInfo\"]),\r\n    }), shallowEqual);\r\n    const settingsDrawerTab = 'personal';\r\n    const [settingsDrawerVisible, setSettingsDrawerVisible] = useState(false);\r\n\r\n    useEffect(() => {\r\n      if(!!state.userInfo?.userId) {\r\n        console.log('用户Id', state.userInfo?.userId);\r\n        getPersonInfo(state.userInfo)\r\n      }\r\n    },[state.userInfo?.userId])\r\n    \r\n    // 获取个人资料\r\n    const getPersonInfo = (userInfo) => {\r\n      http.uc_001_get_person_info({userId: userInfo.userId}).then((res) => {\r\n        if(res.resultCode === 200) {\r\n          // setStatisticalStudy({...res.personalInfo.statisticalStudy})\r\n          // setInterestList([...res.personalInfo.interestList])\r\n          \r\n          form.setFieldValue(\"avatar\", res.personalInfo.baseInfo[\"avatar\"])\r\n          personalList.forEach(item => {\r\n            form.setFieldValue(item.name, res.personalInfo.baseInfo[item.name])\r\n          })\r\n        } else {\r\n          // setStatisticalStudy({})\r\n          // setInterestList([])\r\n\r\n          form.setFieldValue(\"avatar\", \"\")\r\n          personalList.forEach(item => {\r\n            form.setFieldValue(item.name, \"\")\r\n          })\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 保存个人资料\r\n    const savePersonInfo = () => {\r\n      let formData = form.getFieldsValue(true);\r\n      let personalInfo = {\r\n        ...formData\r\n        // avatar: personalObj.avatar,\r\n        // userName: formData.userName.formItemValue,\r\n        // gender: formData.gender.formItemValue,\r\n        // birthday: formData.birthday.formItemValue,\r\n        // job: formData.job.formItemValue,\r\n        // workDate: formData.workDate.formItemValue,\r\n        // city: formData.city.formItemValue,\r\n        // college: formData.college.formItemValue\r\n      }\r\n      http.uc_002_save_person_info(personalInfo).then((res) => {\r\n        if(res.resultCode === 200) {\r\n          getPersonInfo(state.userInfo)\r\n          // fixBUG tmsbug-3347\r\n          updateUserInfo()\r\n        }\r\n      })\r\n    }\r\n\r\n    const updateUserInfo = () => {\r\n      team_619_user_info({}).then(res => {\r\n        if(res.resultCode == 200){\r\n          dispatch(setLoginInfo({\r\n            isAuthenticated: true,\r\n            userInfo: res.userInfo\r\n          }));\r\n        }\r\n      })\r\n    }\r\n\r\n  /*  // 保存用户感兴趣的标签\r\n    const saveUserInterest = (tagList) => {\r\n      http.uc_004_save_user_interest({interestIds: tagList}).then((res) => {\r\n        if(res.resultCode === 200) {\r\n          setAddInterestVisible(false);\r\n          getPersonInfo(state.userInfo.userId)\r\n        }\r\n      })\r\n    }*/\r\n\r\n    return (<>\r\n      <div className=\"PersonalData\">\r\n          <div className=\"PersonalData-left\">\r\n            <span style={{ marginLeft: 14 }}>基本信息</span>\r\n            <Form \r\n              form={form} \r\n              colon={false} \r\n              className=\"PersonalData-form\"\r\n              onFieldsChange={savePersonInfo}>\r\n                <Form.Item label=\"头像\" name=\"avatar\">\r\n                  <PersonLogoFormItem/>\r\n                </Form.Item>\r\n                {personalList.map((item, index) => (\r\n                  <Form.Item key={index} label={item.label} name={item.name}>\r\n                    <CustomFormItem type={item.type} label={item.label} name={item.name} visible={setSettingsDrawerVisible} teamId={teamId}/>\r\n                  </Form.Item>\r\n                ))}\r\n            </Form>\r\n        </div>\r\n          {/* <div className=\"PersonalData-right\">\r\n                <span>学习统计</span>\r\n                <div></div>\r\n            </div> */}\r\n      </div>\r\n          {/* <div className=\"PersonalData-interest\">\r\n              <span>选择感兴趣的内容</span>\r\n              <div className=\"PersonalData-Tag\">\r\n                  {interestList.filter(el => el.checkFlg == 1).map((tag, index) => (\r\n                  <Tag className=\"interestTagCheck\">{tag.name}</Tag>\r\n                  ))}\r\n                  <Tag className=\"interestTagUnchecked\" onClick={() => setAddInterestVisible(true)}>{interestList.filter(el => el.checkFlg == 1).length>0?'+更多':'+添加'}</Tag>\r\n              </div>\r\n              <AddInterestTagModal interestList={interestList} onOk={saveUserInterest} onCancel={() => setAddInterestVisible(false)} visible={addInterestVisible}/>\r\n          </div> */}\r\n          {/* 团队设置/成员管理/应用管理 Drawer */}\r\n      {!!teamId && <SettingsDrawer \r\n        visible={settingsDrawerVisible} \r\n        onClose={()=>setSettingsDrawerVisible(false)} \r\n        teamId={teamId}\r\n        defaultTab={settingsDrawerTab}\r\n      />}\r\n    </>)  \r\n}\r\n\r\n// 自定义FormItem\r\nfunction CustomFormItem({ value='', onChange, type, label, name, visible, teamId}) {\r\n    const [modifyFlag, setModifyFlag] = useState(false);\r\n    const modifyRef = useRef();\r\n    // 日期格式\r\n    const dateFormat = \"YYYY/MM/DD\";\r\n\r\n    const triggerChange = (changedValue) => {\r\n      setModifyFlag(false);\r\n      onChange?.(changedValue);\r\n    };\r\n\r\n    useEffect(() => {\r\n      if (modifyFlag && type !== '') {\r\n        modifyRef.current.focus({ cursor: \"end\" });\r\n      }\r\n    }, [modifyFlag]);\r\n\r\n    const modifyClick = () => {\r\n      setModifyFlag(true);\r\n    };\r\n\r\n    function content(){\r\n      if(!teamId){\r\n        return <div>个人中心的用户名是全局的，如您需要修改您在当前团队的成员名(供其他成员识别), 可返回团队并点击 个人设置(团队内) 进行调整。</div>\r\n      }\r\n      return <div>个人中心的用户名是全局的，如您需要修改您在当前团队的成员名(供其他成员识别), 可点击 <a onClick={()=>{visible(true)}}>个人设置(团队内)</a>  进行调整。</div>\r\n    }\r\n\r\n    const modifyBlur = (e) => {\r\n      Modal.info({\r\n        title: \"友情提示\",\r\n        content: content(),\r\n        maskClosable: true,\r\n        //centered: true, // 居中\r\n        okText: \"我知道了\",\r\n        width: 500,\r\n        onOk: ()=>{\r\n          triggerChange(e.target.value);\r\n        }\r\n      });\r\n    };\r\n\r\n    const genderChange = (e) => {\r\n      triggerChange(e);\r\n    }\r\n\r\n    // 选择日期\r\n    const datePickerChange = (date, dateString) => {\r\n        triggerChange(dateString);\r\n    }\r\n\r\n    // 选择城市\r\n    const cascaderChange = (value, selectedOptions) => {\r\n        triggerChange(value.join('/'));\r\n    }\r\n\r\n    // 获取form item编辑样式\r\n    const getEditFormItemValue = (type,label) => {\r\n        switch (type) {\r\n            case 'input':\r\n                return <Input\r\n                        className=\"MemberFromItem-inpvalue\"\r\n                        autoComplete=\"off\"\r\n                        style={{ width: 240 }}\r\n                        defaultValue={value}\r\n                        ref={modifyRef}\r\n                        onBlur={modifyBlur}\r\n                        onPressEnter={modifyBlur}\r\n                        placeholder={`请输入${label}`}/>\r\n            case 'select':\r\n                return <Select \r\n                        className=\"MemberFromItem-sex\" \r\n                        style={{ width: 240 }} \r\n                        ref={modifyRef}\r\n                        defaultValue={value} \r\n                        onChange={genderChange}\r\n                        onBlur={() => setModifyFlag(false)}\r\n                        placeholder={`请选择${label}`}>\r\n                            <Option value={0}>未知</Option>\r\n                            <Option value={1}>男</Option>\r\n                            <Option value={2}>女</Option>\r\n                        </Select>\r\n            case 'date':\r\n                return <DatePicker\r\n                        style={{ width: 240 }}\r\n                        ref={modifyRef}\r\n                        defaultValue={value!=null&&value!=''?moment(value, dateFormat):null}\r\n                        format={dateFormat}\r\n                        onBlur={() => setModifyFlag(false)}\r\n                        placeholder={`请选择${label}`}\r\n                        onChange={datePickerChange}/>\r\n            case 'cascader':\r\n                return <Cascader \r\n                        style={{ width: 240 }}\r\n                        ref={modifyRef}\r\n                        defaultValue={value != null && value.split('/')}\r\n                        options={province} \r\n                        onBlur={() => setModifyFlag(false)}\r\n                        onChange={cascaderChange} \r\n                        placeholder={`请选择${label}`}/>\r\n            default:\r\n                return <></>\r\n        }\r\n    }\r\n\r\n    // 格式化form item只读状态样式\r\n    const getReadFormItemValue = (name) => {\r\n        switch (name) {\r\n            case 'gender':\r\n                return (value == 1 ? '男' : (value == 2 ? '女' : '未知'))        \r\n            default:\r\n                return value\r\n        }\r\n    }\r\n\r\n    return (\r\n      <div className=\"MemberFromItem\">\r\n        <span>{modifyFlag?getEditFormItemValue(type,label):getReadFormItemValue(name)}</span>\r\n        {!modifyFlag ? (\r\n          <Button\r\n            className=\"CustomFromItem-editIcon\"\r\n            size=\"small\"\r\n            type=\"link\"\r\n            icon={<EditOutlined />}\r\n            onClick={modifyClick}/>\r\n        ):<></>}\r\n      </div>\r\n    );\r\n}\r\n\r\n// 上传头像\r\nfunction PersonLogoFormItem({value,onChange}){\r\n  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑状态\r\n\r\n  // 上传头像\r\n  const avatarUpload = (link) => {\r\n    onChange?.(link)\r\n  }\r\n\r\n  const deleteLogo = () => {\r\n    onChange?.('') //20240313 Jim 从 null 改为 ''\r\n  }\r\n\r\n  return <React.Fragment>\r\n    <div className=\"PersonalData-form-avatar\">\r\n      <Avatar\r\n        className=\"PersonalData-avatar\"\r\n        src={value}\r\n        icon={<NoAvatarIcon/>}/>\r\n        <div className=\"PersonalData-form-upload\">\r\n          <CameraOutlined\r\n            className=\"PersonalData-form-uploadIcon\"\r\n            onClick={(event) => setIsModalVisible(true)}/>\r\n        </div>\r\n        {!!value && <CloseCircleFilled title=\"删除头像\" className=\"delete-PersonalData-avatar\" onClick={deleteLogo}/>}\r\n    </div>\r\n    <DraggablePopUp\r\n      title=\"上传logo\"\r\n      className=\"avatarUpload-modal\"\r\n      open={isModalVisible}\r\n      onCancel={() => setIsModalVisible(false)}\r\n      footer={null}>\r\n      <ImgUpload avatarUpload={avatarUpload} onCancel={() => setIsModalVisible(false)}/>\r\n    </DraggablePopUp>\r\n  </React.Fragment>\r\n}\r\n\r\n// 图片上传\r\nfunction ImgUpload(props) {\r\n  const {avatarUpload,onCancel} = props\r\n  const dataSource = {\r\n    maxCount: 1,\r\n    name: \"file\",\r\n    multiple: false,\r\n    showUploadList: false,\r\n    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,\r\n    beforeUpload: (file) => {\r\n      const isPNG = file.type === \"image/png\" || file.type === 'image/jpeg';\r\n      if (!isPNG) {\r\n        globalUtil.error(`${file.name}不是图片格式`);\r\n      }\r\n      return isPNG || Upload.LIST_IGNORE;\r\n    },\r\n    onChange(info) {\r\n      onCancel()\r\n      const { status, response } = info.file;\r\n      if (status == \"uploading\") {\r\n        console.log(info.file, info.fileList);\r\n      }\r\n      if (status === \"done\") {\r\n        if(response.resultCode == 200) {\r\n          avatarUpload(response.link)\r\n          globalUtil.success('上传成功');\r\n        } else {\r\n          globalUtil.error(\"上传失败\")\r\n        }\r\n      } else if (status === \"error\") {\r\n        globalUtil.error(`${info.file.name} file upload failed.`);\r\n      }\r\n    },\r\n    onDrop(e) {\r\n      console.log(\"Dropped files\", e.dataTransfer.files);\r\n    },\r\n  };\r\n  \r\n  // 预览/裁剪图片\r\n  const onPreview = async (file) => {\r\n    let src = file.url;\r\n    if (!src) {\r\n      src = await new Promise((resolve) => {\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file.originFileObj);\r\n        reader.onload = () => resolve(reader.result);\r\n      });\r\n    }\r\n    const image = new Image();\r\n    image.src = src;\r\n    const imgWindow = window.open(src);\r\n    imgWindow.document.write(image.outerHTML);\r\n  };\r\n  \r\n  return (\r\n    <ImgCrop modalClassName=\"clippingImgCrop\" rotate modalTitle={\"编辑图片\"} modalOk=\"确认\" modalCancel=\"取消\">\r\n      <Dragger {...dataSource} onPreview={onPreview}>\r\n        <p className=\"ant-upload-drag-icon\">\r\n          <QuestionsUploadIcon />\r\n        </p>\r\n        <p className=\"ant-upload-text\">点击或拖动图片至此处</p>\r\n        <p style={{color:'#999',marginTop:2}}>图片格式：jpg、png</p>\r\n      </Dragger>\r\n    </ImgCrop>\r\n  );\r\n}"], "mappings": ";;;;AAAA,SAASA,mBAAmB,QAAQ,sBAAsB;AAC1D,SAAQC,MAAM,EAACC,MAAM,EAACC,QAAQ,EAACC,UAAU,EAACC,KAAK,EAACC,OAAO,EAACC,IAAI,EAACC,KAAK,EAACC,MAAM,EAACC,GAAG,EAACC,MAAM,EAAEC,KAAK,QAAO,MAAM;AACxG,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,mBAAmB;AACnF,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,kBAAkB,QAAQ,kBAAkB;AACrD,OAAO,KAAKC,IAAI,MAAM,kBAAkB;AACxC,SAASC,WAAW,EAAEC,YAAY,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAAQC,QAAQ,EAAEC,YAAY,QAAQ,oBAAoB;AAC1D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAO,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAM;EAAEC;AAAQ,CAAC,GAAG5B,MAAM;AAC1B,MAAM;EAAE6B;AAAO,CAAC,GAAG/B,MAAM;;AAEzB;AACA,eAAe,SAASgC,YAAYA,CAACC,MAAM,EAAE;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACzC,MAAM,CAACC,IAAI,CAAC,GAAGtC,IAAI,CAACuC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAACuB;EAAM,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC5B,MAAMe,KAAK,GAAGtB,WAAW,CAAEsB,KAAK,KAAM;IACpCC,QAAQ,EAAED,KAAK,CAACE,KAAK,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC;EAC1D,CAAC,CAAC,EAAEzB,YAAY,CAAC;EACjB,MAAM0B,iBAAiB,GAAG,UAAU;EACpC,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAEzED,SAAS,CAAC,MAAM;IAAA,IAAAmC,eAAA;IACd,IAAG,CAAC,GAAAA,eAAA,GAACN,KAAK,CAACC,QAAQ,cAAAK,eAAA,eAAdA,eAAA,CAAgBC,MAAM,GAAE;MAAA,IAAAC,gBAAA;MAC3BC,OAAO,CAACC,GAAG,CAAC,MAAM,GAAAF,gBAAA,GAAER,KAAK,CAACC,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBD,MAAM,CAAC;MAC3CI,aAAa,CAACX,KAAK,CAACC,QAAQ,CAAC;IAC/B;EACF,CAAC,EAAC,EAAAN,gBAAA,GAACK,KAAK,CAACC,QAAQ,cAAAN,gBAAA,uBAAdA,gBAAA,CAAgBY,MAAM,CAAC,CAAC;;EAE3B;EACA,MAAMI,aAAa,GAAIV,QAAQ,IAAK;IAClC1B,IAAI,CAACqC,sBAAsB,CAAC;MAACL,MAAM,EAAEN,QAAQ,CAACM;IAAM,CAAC,CAAC,CAACM,IAAI,CAAEC,GAAG,IAAK;MACnE,IAAGA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QACzB;QACA;;QAEAnB,IAAI,CAACoB,aAAa,CAAC,QAAQ,EAAEF,GAAG,CAACG,YAAY,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACjEtC,YAAY,CAACuC,OAAO,CAACC,IAAI,IAAI;UAC3BxB,IAAI,CAACoB,aAAa,CAACI,IAAI,CAACC,IAAI,EAAEP,GAAG,CAACG,YAAY,CAACC,QAAQ,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC;QACrE,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;;QAEAzB,IAAI,CAACoB,aAAa,CAAC,QAAQ,EAAE,EAAE,CAAC;QAChCpC,YAAY,CAACuC,OAAO,CAACC,IAAI,IAAI;UAC3BxB,IAAI,CAACoB,aAAa,CAACI,IAAI,CAACC,IAAI,EAAE,EAAE,CAAC;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,QAAQ,GAAG3B,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAAC;IACxC,IAAIP,YAAY,GAAG;MACjB,GAAGM;MACH;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,CAAC;IACDhD,IAAI,CAACkD,uBAAuB,CAACR,YAAY,CAAC,CAACJ,IAAI,CAAEC,GAAG,IAAK;MACvD,IAAGA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QACzBJ,aAAa,CAACX,KAAK,CAACC,QAAQ,CAAC;QAC7B;QACAyB,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMA,cAAc,GAAGA,CAAA,KAAM;IAC3BpD,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAACuC,IAAI,CAACC,GAAG,IAAI;MACjC,IAAGA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAC;QACvBjB,QAAQ,CAACjB,YAAY,CAAC;UACpB8C,eAAe,EAAE,IAAI;UACrB1B,QAAQ,EAAEa,GAAG,CAACb;QAChB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACJ,CAAC;;EAEH;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEI,oBAAQd,OAAA,CAAAE,SAAA;IAAAuC,QAAA,gBACNzC,OAAA;MAAK0C,SAAS,EAAC,cAAc;MAAAD,QAAA,eACzBzC,OAAA;QAAK0C,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChCzC,OAAA;UAAM2C,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAG,CAAE;UAAAH,QAAA,EAAC;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5ChD,OAAA,CAAC7B,IAAI;UACHsC,IAAI,EAAEA,IAAK;UACXwC,KAAK,EAAE,KAAM;UACbP,SAAS,EAAC,mBAAmB;UAC7BQ,cAAc,EAAEf,cAAe;UAAAM,QAAA,gBAC7BzC,OAAA,CAAC7B,IAAI,CAACgF,IAAI;YAACC,KAAK,EAAC,cAAI;YAAClB,IAAI,EAAC,QAAQ;YAAAO,QAAA,eACjCzC,OAAA,CAACqD,kBAAkB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,EACXvD,YAAY,CAAC6D,GAAG,CAAC,CAACrB,IAAI,EAAEsB,KAAK,kBAC5BvD,OAAA,CAAC7B,IAAI,CAACgF,IAAI;YAAaC,KAAK,EAAEnB,IAAI,CAACmB,KAAM;YAAClB,IAAI,EAAED,IAAI,CAACC,IAAK;YAAAO,QAAA,eACxDzC,OAAA,CAACwD,cAAc;cAACC,IAAI,EAAExB,IAAI,CAACwB,IAAK;cAACL,KAAK,EAAEnB,IAAI,CAACmB,KAAM;cAAClB,IAAI,EAAED,IAAI,CAACC,IAAK;cAACwB,OAAO,EAAExC,wBAAyB;cAACN,MAAM,EAAEA;YAAO;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC,GAD3GO,KAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKH,CAAC,EAYL,CAAC,CAACpC,MAAM,iBAAIZ,OAAA,CAACH,cAAc;MAC1B6D,OAAO,EAAEzC,qBAAsB;MAC/B0C,OAAO,EAAEA,CAAA,KAAIzC,wBAAwB,CAAC,KAAK,CAAE;MAC7CN,MAAM,EAAEA,MAAO;MACfgD,UAAU,EAAE5C;IAAkB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA,eACF,CAAC;AACP;;AAEA;AAAAzC,EAAA,CAhIwBF,YAAY;EAAA,QACjBlC,IAAI,CAACuC,OAAO,EACVrB,WAAW,EACXS,SAAS,EACZP,WAAW;AAAA;AAAAsE,EAAA,GAJLxD,YAAY;AAiIpC,SAASmD,cAAcA,CAAC;EAAEM,KAAK,GAAC,EAAE;EAAEC,QAAQ;EAAEN,IAAI;EAAEL,KAAK;EAAElB,IAAI;EAAEwB,OAAO;EAAE9C;AAAM,CAAC,EAAE;EAAAoD,GAAA;EAC/E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMkF,SAAS,GAAGjF,MAAM,CAAC,CAAC;EAC1B;EACA,MAAMkF,UAAU,GAAG,YAAY;EAE/B,MAAMC,aAAa,GAAIC,YAAY,IAAK;IACtCJ,aAAa,CAAC,KAAK,CAAC;IACpBH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGO,YAAY,CAAC;EAC1B,CAAC;EAEDtF,SAAS,CAAC,MAAM;IACd,IAAIiF,UAAU,IAAIR,IAAI,KAAK,EAAE,EAAE;MAC7BU,SAAS,CAACI,OAAO,CAACC,KAAK,CAAC;QAAEC,MAAM,EAAE;MAAM,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACR,UAAU,CAAC,CAAC;EAEhB,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxBR,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,SAASS,OAAOA,CAAA,EAAE;IAChB,IAAG,CAAC/D,MAAM,EAAC;MACT,oBAAOZ,OAAA;QAAAyC,QAAA,EAAK;MAAgE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IACpF;IACA,oBAAOhD,OAAA;MAAAyC,QAAA,GAAK,iPAA4C,eAAAzC,OAAA;QAAG4E,OAAO,EAAEA,CAAA,KAAI;UAAClB,OAAO,CAAC,IAAI,CAAC;QAAA,CAAE;QAAAjB,QAAA,EAAC;MAAS;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,oCAAO;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACrH;EAEA,MAAM6B,UAAU,GAAIC,CAAC,IAAK;IACxBtG,KAAK,CAACuG,IAAI,CAAC;MACTC,KAAK,EAAE,MAAM;MACbL,OAAO,EAAEA,OAAO,CAAC,CAAC;MAClBM,YAAY,EAAE,IAAI;MAClB;MACAC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAEA,CAAA,KAAI;QACRf,aAAa,CAACS,CAAC,CAACO,MAAM,CAACvB,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwB,YAAY,GAAIR,CAAC,IAAK;IAC1BT,aAAa,CAACS,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMS,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,UAAU,KAAK;IAC3CpB,aAAa,CAACoB,UAAU,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAC5B,KAAK,EAAE6B,eAAe,KAAK;IAC/CtB,aAAa,CAACP,KAAK,CAAC8B,IAAI,CAAC,GAAG,CAAC,CAAC;EAClC,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAACpC,IAAI,EAACL,KAAK,KAAK;IACzC,QAAQK,IAAI;MACR,KAAK,OAAO;QACR,oBAAOzD,OAAA,CAAC/B,KAAK;UACLyE,SAAS,EAAC,yBAAyB;UACnCoD,YAAY,EAAC,KAAK;UAClBnD,KAAK,EAAE;YAAEwC,KAAK,EAAE;UAAI,CAAE;UACtBY,YAAY,EAAEjC,KAAM;UACpBkC,GAAG,EAAE7B,SAAU;UACf8B,MAAM,EAAEpB,UAAW;UACnBqB,YAAY,EAAErB,UAAW;UACzBsB,WAAW,EAAE,MAAM/C,KAAK;QAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MACzC,KAAK,QAAQ;QACT,oBAAOhD,OAAA,CAAC3B,MAAM;UACNqE,SAAS,EAAC,oBAAoB;UAC9BC,KAAK,EAAE;YAAEwC,KAAK,EAAE;UAAI,CAAE;UACtBa,GAAG,EAAE7B,SAAU;UACf4B,YAAY,EAAEjC,KAAM;UACpBC,QAAQ,EAAEuB,YAAa;UACvBW,MAAM,EAAEA,CAAA,KAAM/B,aAAa,CAAC,KAAK,CAAE;UACnCiC,WAAW,EAAE,MAAM/C,KAAK,EAAG;UAAAX,QAAA,gBACvBzC,OAAA,CAACI,MAAM;YAAC0D,KAAK,EAAE,CAAE;YAAArB,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7BhD,OAAA,CAACI,MAAM;YAAC0D,KAAK,EAAE,CAAE;YAAArB,QAAA,EAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5BhD,OAAA,CAACI,MAAM;YAAC0D,KAAK,EAAE,CAAE;YAAArB,QAAA,EAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MACrB,KAAK,MAAM;QACP,oBAAOhD,OAAA,CAAChC,UAAU;UACV2E,KAAK,EAAE;YAAEwC,KAAK,EAAE;UAAI,CAAE;UACtBa,GAAG,EAAE7B,SAAU;UACf4B,YAAY,EAAEjC,KAAK,IAAE,IAAI,IAAEA,KAAK,IAAE,EAAE,GAAChF,MAAM,CAACgF,KAAK,EAAEM,UAAU,CAAC,GAAC,IAAK;UACpEgC,MAAM,EAAEhC,UAAW;UACnB6B,MAAM,EAAEA,CAAA,KAAM/B,aAAa,CAAC,KAAK,CAAE;UACnCiC,WAAW,EAAE,MAAM/C,KAAK,EAAG;UAC3BW,QAAQ,EAAEwB;QAAiB;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MACzC,KAAK,UAAU;QACX,oBAAOhD,OAAA,CAACjC,QAAQ;UACR4E,KAAK,EAAE;YAAEwC,KAAK,EAAE;UAAI,CAAE;UACtBa,GAAG,EAAE7B,SAAU;UACf4B,YAAY,EAAEjC,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACuC,KAAK,CAAC,GAAG,CAAE;UAChDC,OAAO,EAAE9G,QAAS;UAClByG,MAAM,EAAEA,CAAA,KAAM/B,aAAa,CAAC,KAAK,CAAE;UACnCH,QAAQ,EAAE2B,cAAe;UACzBS,WAAW,EAAE,MAAM/C,KAAK;QAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MACzC;QACI,oBAAOhD,OAAA,CAAAE,SAAA,mBAAI,CAAC;IACpB;EACJ,CAAC;;EAED;EACA,MAAMqG,oBAAoB,GAAIrE,IAAI,IAAK;IACnC,QAAQA,IAAI;MACR,KAAK,QAAQ;QACT,OAAQ4B,KAAK,IAAI,CAAC,GAAG,GAAG,GAAIA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAK;MACxD;QACI,OAAOA,KAAK;IACpB;EACJ,CAAC;EAED,oBACE9D,OAAA;IAAK0C,SAAS,EAAC,gBAAgB;IAAAD,QAAA,gBAC7BzC,OAAA;MAAAyC,QAAA,EAAOwB,UAAU,GAAC4B,oBAAoB,CAACpC,IAAI,EAACL,KAAK,CAAC,GAACmD,oBAAoB,CAACrE,IAAI;IAAC;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,EACpF,CAACiB,UAAU,gBACVjE,OAAA,CAAClC,MAAM;MACL4E,SAAS,EAAC,yBAAyB;MACnC8D,IAAI,EAAC,OAAO;MACZ/C,IAAI,EAAC,MAAM;MACXgD,IAAI,eAAEzG,OAAA,CAACtB,YAAY;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACvB4B,OAAO,EAAEF;IAAY;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,gBACzBhD,OAAA,CAAAE,SAAA,mBAAI,CAAC;EAAA;IAAA2C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ;;AAEA;AAAAgB,GAAA,CAlISR,cAAc;AAAAkD,GAAA,GAAdlD,cAAc;AAmIvB,SAASH,kBAAkBA,CAAC;EAACS,KAAK;EAACC;AAAQ,CAAC,EAAC;EAAA4C,GAAA;EAC3C,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG5H,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE7D;EACA,MAAM6H,YAAY,GAAIC,IAAI,IAAK;IAC7BhD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGgD,IAAI,CAAC;EAClB,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBjD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,EAAE,CAAC,EAAC;EACjB,CAAC;EAED,oBAAO/D,OAAA,CAACjB,KAAK,CAACkB,QAAQ;IAAAwC,QAAA,gBACpBzC,OAAA;MAAK0C,SAAS,EAAC,0BAA0B;MAAAD,QAAA,gBACvCzC,OAAA,CAACnC,MAAM;QACL6E,SAAS,EAAC,qBAAqB;QAC/BuE,GAAG,EAAEnD,KAAM;QACX2C,IAAI,eAAEzG,OAAA,CAACJ,YAAY;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACxBhD,OAAA;QAAK0C,SAAS,EAAC,0BAA0B;QAAAD,QAAA,eACvCzC,OAAA,CAACrB,cAAc;UACb+D,SAAS,EAAC,8BAA8B;UACxCkC,OAAO,EAAGsC,KAAK,IAAKL,iBAAiB,CAAC,IAAI;QAAE;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EACL,CAAC,CAACc,KAAK,iBAAI9D,OAAA,CAACpB,iBAAiB;QAACoG,KAAK,EAAC,0BAAM;QAACtC,SAAS,EAAC,4BAA4B;QAACkC,OAAO,EAAEoC;MAAW;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eACNhD,OAAA,CAACvB,cAAc;MACbuG,KAAK,EAAC,kBAAQ;MACdtC,SAAS,EAAC,oBAAoB;MAC9ByE,IAAI,EAAEP,cAAe;MACrBQ,QAAQ,EAAEA,CAAA,KAAMP,iBAAiB,CAAC,KAAK,CAAE;MACzCQ,MAAM,EAAE,IAAK;MAAA5E,QAAA,eACbzC,OAAA,CAACsH,SAAS;QAACR,YAAY,EAAEA,YAAa;QAACM,QAAQ,EAAEA,CAAA,KAAMP,iBAAiB,CAAC,KAAK;MAAE;QAAAhE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AACnB;;AAEA;AAAA2D,GAAA,CApCStD,kBAAkB;AAAAkE,GAAA,GAAlBlE,kBAAkB;AAqC3B,SAASiE,SAASA,CAACE,KAAK,EAAE;EACxB,MAAM;IAACV,YAAY;IAACM;EAAQ,CAAC,GAAGI,KAAK;EACrC,MAAMC,UAAU,GAAG;IACjBC,QAAQ,EAAE,CAAC;IACXxF,IAAI,EAAE,MAAM;IACZyF,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,KAAK;IACrBC,MAAM,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,8BAA8B;IACvEC,YAAY,EAAGC,IAAI,IAAK;MACtB,MAAMC,KAAK,GAAGD,IAAI,CAACzE,IAAI,KAAK,WAAW,IAAIyE,IAAI,CAACzE,IAAI,KAAK,YAAY;MACrE,IAAI,CAAC0E,KAAK,EAAE;QACVxI,UAAU,CAACyI,KAAK,CAAC,GAAGF,IAAI,CAAChG,IAAI,QAAQ,CAAC;MACxC;MACA,OAAOiG,KAAK,IAAI5J,MAAM,CAAC8J,WAAW;IACpC,CAAC;IACDtE,QAAQA,CAACgB,IAAI,EAAE;MACbqC,QAAQ,CAAC,CAAC;MACV,MAAM;QAAEkB,MAAM;QAAEC;MAAS,CAAC,GAAGxD,IAAI,CAACmD,IAAI;MACtC,IAAII,MAAM,IAAI,WAAW,EAAE;QACzBhH,OAAO,CAACC,GAAG,CAACwD,IAAI,CAACmD,IAAI,EAAEnD,IAAI,CAACyD,QAAQ,CAAC;MACvC;MACA,IAAIF,MAAM,KAAK,MAAM,EAAE;QACrB,IAAGC,QAAQ,CAAC3G,UAAU,IAAI,GAAG,EAAE;UAC7BkF,YAAY,CAACyB,QAAQ,CAACxB,IAAI,CAAC;UAC3BpH,UAAU,CAAC8I,OAAO,CAAC,MAAM,CAAC;QAC5B,CAAC,MAAM;UACL9I,UAAU,CAACyI,KAAK,CAAC,MAAM,CAAC;QAC1B;MACF,CAAC,MAAM,IAAIE,MAAM,KAAK,OAAO,EAAE;QAC7B3I,UAAU,CAACyI,KAAK,CAAC,GAAGrD,IAAI,CAACmD,IAAI,CAAChG,IAAI,sBAAsB,CAAC;MAC3D;IACF,CAAC;IACDwG,MAAMA,CAAC5D,CAAC,EAAE;MACRxD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEuD,CAAC,CAAC6D,YAAY,CAACC,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,MAAOX,IAAI,IAAK;IAChC,IAAIjB,GAAG,GAAGiB,IAAI,CAACY,GAAG;IAClB,IAAI,CAAC7B,GAAG,EAAE;MACRA,GAAG,GAAG,MAAM,IAAI8B,OAAO,CAAEC,OAAO,IAAK;QACnC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,aAAa,CAACjB,IAAI,CAACkB,aAAa,CAAC;QACxCH,MAAM,CAACI,MAAM,GAAG,MAAML,OAAO,CAACC,MAAM,CAACK,MAAM,CAAC;MAC9C,CAAC,CAAC;IACJ;IACA,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;IACzBD,KAAK,CAACtC,GAAG,GAAGA,GAAG;IACf,MAAMwC,SAAS,GAAGC,MAAM,CAACvC,IAAI,CAACF,GAAG,CAAC;IAClCwC,SAAS,CAACE,QAAQ,CAACC,KAAK,CAACL,KAAK,CAACM,SAAS,CAAC;EAC3C,CAAC;EAED,oBACE7J,OAAA,CAACnB,OAAO;IAACiL,cAAc,EAAC,iBAAiB;IAACC,MAAM;IAACC,UAAU,EAAE,MAAO;IAACC,OAAO,EAAC,cAAI;IAACC,WAAW,EAAC,cAAI;IAAAzH,QAAA,eAChGzC,OAAA,CAACG,OAAO;MAAA,GAAKsH,UAAU;MAAEoB,SAAS,EAAEA,SAAU;MAAApG,QAAA,gBAC5CzC,OAAA;QAAG0C,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACjCzC,OAAA,CAACpC,mBAAmB;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACJhD,OAAA;QAAG0C,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC7ChD,OAAA;QAAG2C,KAAK,EAAE;UAACwH,KAAK,EAAC,MAAM;UAACC,SAAS,EAAC;QAAC,CAAE;QAAA3H,QAAA,EAAC;MAAY;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEd;AAACqH,GAAA,GAhEQ/C,SAAS;AAAA,IAAAzD,EAAA,EAAA6C,GAAA,EAAAa,GAAA,EAAA8C,GAAA;AAAAC,YAAA,CAAAzG,EAAA;AAAAyG,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}