import { CaretDownOutlined } from "@ant-design/icons";
import { Button, Input, Drawer, Form, Checkbox, Avatar, Select, Space, Dropdown } from "antd";
import DraggablePopUp from "@components/DraggablePopUp";
import React, { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import '../DataPush.scss';
import * as httpQuickAccess from "@common/api/http";
import { globalUtil } from "@common/utils/globalUtil";
import { eNodeTypeId, eNodeType } from "@common/utils/TsbConfig";
import { NoAvatarIcon } from '@common/components/IconUtil';
import TEditor from "@common/components/TEditor/TEditor";
import TextModelModal from './TextModel/TextModelModal';
import PlanModal from './Plan/PlanModal';
import SubscribeContent from './SubscribeContent/SubscribeContent';
import { ePriorityTypeObj, ePriorityType, eMsgChannel, eMsgChannelObj, eExecTimeType } from "src/quickAcess/utils/Config";
import { useQueryTeam599GetDataDictionary } from "src/quickAcess/service/quickHooks";
import { eSelectionListId } from "@common/utils/enum"
import { isEmpty } from "@common/utils/ArrayUtils"
import DraggableDrawer from "@components/DraggableDrawer";

const { TextArea } = Input

// 新建/编辑 系统订阅
export default function SubscribeEdit({ allMember,opType,closeDrawer,modalVisible,editData,isChange,setIsChange,selectedKey,typeList,fromType,ganttName, nodeType }) {
  const { teamId,nodeId } = useParams();
  const [form] = Form.useForm();
  const codeInputRef = useRef(Object.create(null));
  // Form.Item布局样式
  const formItemLayout = { labelCol: { span: 2 } };

  // Form.Item的样式
  const style = { style: {  height: "50%", marginBottom: 20 } };

  const [subscribeDataVisible, setSubscribeDataVisible] = useState(false);
  const [subscribeTextVisible, setSubscribeTextVisible] = useState(false);
  const [subscribePlanVisible, setSubscribePlanVisible] = useState(false);

  const [touids,setTouids] = useState([]); // 收件人
  const [ccuids,setCcuids] = useState([]); // 抄送人
  const [pushwhenEmpty,setPushwhenEmpty] = useState(false);
  const [subscribeData, setSubscribeData] = useState(null);
  const [timedPlan, setTimedPlan] = useState(null);
  const [description, setDescription] = useState(""); // 备注

  const [showTip,setShowTip] = useState(false);

  const { data: { dataDictionaries=[], templateVariables=[], workflowVariables=[] } = { dataDictionaries: [], templateVariables: [], workflowVariables: [] }, isLoading: isLoadingTeamXxx,refetch: refetchTeamXxx} = useQueryTeam599GetDataDictionary({teamId, selectionId: eSelectionListId.Selection_1970_datapush});

  useEffect(()=>{
    if(modalVisible){
      if(!!editData){
        form.setFieldValue('subscrTitle',editData.subscrTitle||'');
        // form.setFieldValue('description',editData.description||'');
        setDescription(editData.description||'');
        let toUids = (editData.toUids||'').split(',').filter(uid => !!uid);
        setTouids([...toUids]);
        let ccUids = (editData.ccUids||'').split(',').filter(uid => !!uid);
        setCcuids([...ccUids]);
        let obj = !!editData.objId ? {objId: editData.objId, objType: editData.objType, objName: editData.objName} : null
        setSubscribeData(obj);
        setPushwhenEmpty(editData.pushWhenEmpty == 1);
        setTimedPlan({cronId: editData.cronId, cronName: editData.cronDesc});
        setTimeout(()=>{
          !!codeInputRef?.current?.setContent && codeInputRef.current.setContent(editData.msgTpl);
        },500);
      }else{
        form.setFieldValue('subscrTitle','');
        // form.setFieldValue('description','');
        setDescription("");
        setTouids([]);
        setCcuids([]);
        setSubscribeData(null);
        setPushwhenEmpty(false);
        setTimedPlan(null);
        setTimeout(()=>{
          //todo 推送文案默认值
          !!codeInputRef?.current?.setContent && codeInputRef.current.setContent("<p>你好，</p><p>%订阅数据%，</p><p>谢谢</p>");
        },500);
      }
    }
  },[modalVisible]);

  function avatarFormat(src, name) {
    return (<Avatar style={{ marginRight: 5 }} src={src} icon={<NoAvatarIcon />} size={24} />);
  }

  function onOk(opType){
    let values = form.getFieldsValue(true);
    setShowTip(false);
    if((touids||[]).length == 0){
      globalUtil.warning('请选择收件人');
      return
    }
    if(!timedPlan){
      globalUtil.warning('请选择定时计划');
      return
    }
    let msgtpl = codeInputRef?.current?.getContent()||'';
    if(opType == 0){
      if(!!subscribeData?.objId){
        if((msgtpl||'').indexOf('%订阅数据%') == -1){
          setShowTip(true);
          return
        }
      }
    }
    let params = {
      teamId: teamId,
      subscrTitle: values.subscrTitle,
      msgChannel: values.msgChannel,
      objId: subscribeData?.objId,
      objType: subscribeData?.objType,
      objName: subscribeData?.objName,
      msgTpl: msgtpl,
      toUids: (touids||[]).join(','),
      ccUids: (ccuids||[]).join(','),
      execTimeType: eExecTimeType.reportSubscribe, // 报表订阅 
      cronId: timedPlan.cronId,
      description: description,
      pushWhenEmpty: pushwhenEmpty ? 1 : 0,
      projectNodeId: nodeId,
      nodeType: nodeType,
    }
    if(!!editData){
      params.nodeId = selectedKey
    }
    httpQuickAccess.team_523_save_sched_task_src(params).then(res => {
      if(res.resultCode == 200){
        closeDrawer(1);
      }
    });
  }

  function _onClose(){
    closeDrawer(0,{
      values: form.getFieldsValue(true), 
      objId: subscribeData?.objId, 
      msgTpl: codeInputRef?.current?.getContent()||'', 
      cronId: timedPlan?.cronId,
      toUids: touids||[],
      ccUids: ccuids||[],
      pushwhenEmpty: pushwhenEmpty ? 1 : 0,
    })
  }

  function _onChange(value,opType){
    if(!value){
      if(opType == 1){ // 编辑
        setTimedPlan(null);
      }
      if(opType == 0){ // 新建
        setSubscribeData(null);
      }
    }
  }

/*  // 模版
  function onClickTemplateVariables (info) {
    let theme = form.getFieldValue("theme")??"";
    console.log("theme", theme, info.key);
    form.setFieldValue("theme", `${theme}${info.key}`);
  }*/

  function onChangeRemark (e) {
   setDescription(e.target.value);
  }

  return (
    <DraggableDrawer
      className="tms-drawer"
      title={`${opType == 0 ? "新建" : '编辑'}${fromType == 'gantt' ? '进度推送' : '报表'}订阅`}
      width={'70%'}
      onClose={() => _onClose()}
      open={modalVisible}
      destroyOnClose //关闭时销毁子元素,避免重新打开数据不会刷新
      closable
      centered
      footer={
        <div style={{display:'flex',alignItems:'center',justifyContent:'end'}}>
          <Space size={20}>
          {!!subscribeData &&
          <Checkbox
            className="subscribe-way-mark-check"
            onChange={(e)=> setPushwhenEmpty(e.target.checked)} 
            checked={pushwhenEmpty}
          >
            订阅数据为空时，依然推送。
          </Checkbox>
          }
          <Input value={description} placeholder="请输入备注" style={{ borderRadius: 5, width:300 }} autoComplete="off" onChange={onChangeRemark}></Input>
          <Button style={{borderRadius:5}} type={'primary'} onClick={()=>{form.submit()}}>提交</Button>
          </Space>
        </div>
      }
    >
      <Form
        className="subscribe-way"
        form={form}
        {...formItemLayout}
        labelAlign="right"
        onFinish={()=>onOk(0)}
        initialValues={{
          msgChannel: eMsgChannel.mail, // 默认邮件
        }}
      >
        <div className="label-header">推送规则</div>
        {/* 订阅名称 */}
        <Form.Item label="名称" rules={[{required: true, message: '名称不能为空'}]} {...style} name='subscrTitle'>
          <Input style={{ width: 300,  borderRadius: 5 }} autoComplete="off"/>
        </Form.Item>
        {/* 定时计划 */}
        <Form.Item label="定时计划" required {...style}>
          <div style={{display:'flex',alignItems:'center'}}>
            <Select
              style={{ width: 300,  }}
              placeholder='请选择'
              value={timedPlan?.cronName}
              suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
              onChange={(value)=>_onChange(value,1)}
              allowClear
              onClick={() => setSubscribePlanVisible(true)} />
            <div style={{color:'#999',marginLeft:10}}>备注：订阅邮件发送时间会有1-5分钟的延迟</div>
          </div>
        </Form.Item>
        <Form.Item label="优先级" {...style} name="priority">
          <Select
            showSearch
            allowClear
            style={{ width: 300 }}
            placeholder='请选择'
            options={[
              ePriorityTypeObj[ePriorityType.high],
              ePriorityTypeObj[ePriorityType.middle],
              ePriorityTypeObj[ePriorityType.low],
            ]}
          />
        </Form.Item>
         <Form.Item label='系统推送通道' {...style} name="msgChannel">
           <Select
            disabled={true}
            showSearch
            allowClear
            style={{ width: 300 }}
            placeholder='请选择'
            options={[
              eMsgChannelObj[eMsgChannel.mail],
              eMsgChannelObj[eMsgChannel.msg],
              eMsgChannelObj[eMsgChannel.voice],
              eMsgChannelObj[eMsgChannel.wechat],
              eMsgChannelObj[eMsgChannel.inSiteMsg],
            ]}
          />
        </Form.Item>
        <div className="label-header">推送内容</div>
        {/* <Form.Item label="主题"  {...style}>
          <Space size={10}>
             <Form.Item name="theme" noStyle>
                <Input style={{ borderRadius: 5, width:300 }} autoComplete="off"/>
             </Form.Item>
             <Dropdown
              menu={{
                items: templateVariables.map(obj => ({
                  key: obj.value,
                  value: obj.value,
                  label: obj.value,
                })),
                onClick: onClickTemplateVariables
              }}
              placement="bottom"
              arrow={{
                pointAtCenter: true,
              }}
            >
            <Button type='link'>选择模版变量</Button>
            </Dropdown>
          </Space>
        </Form.Item> */}
        {/* 收件人 */}
        <Form.Item label="收件人" required {...style}>
          <Select
            showSearch
            allowClear
            // mode='multiple' fix tmsbug-11663:系统订阅，收件人，期望从多选改为单选
            placeholder="请选择收件人"
            value={touids?.[0]}
            onChange={(value) => setTouids( !!value ? [value] : [])}
            optionFilterProp="children"
            dropdownMatchSelectWidth={false}
            filterOption={(input, option) => (option?.key ?? '').toLowerCase().includes(input.toLowerCase())}
            options={allMember.map(user => ({
              value: user.key.toString(),
              label: <div style={{ display: 'flex', alignItems: 'center' }}>{avatarFormat(user.avatar, user.label)}{user.label}</div>,
              key: user.label,
            }))}
          />
        </Form.Item>
        {/* 抄送人 */}
        <Form.Item label="抄送人" {...style}>
          <Select
            showSearch
            mode="multiple"
            allowClear
            placeholder="请选择抄送人"
            value={ccuids}
            onChange={(value) => setCcuids(value)}
            optionFilterProp="children"
            filterOption={(input, option) => (option?.key ?? '').toLowerCase().includes(input.toLowerCase())}
            options={allMember.map(user => ({
              value: user.key.toString(),
              label: <div style={{ display: 'flex', alignItems: 'center' }}>{avatarFormat(user.avatar, user.label)}{user.label}</div>,
              key: user.label,
            }))}
          />
        </Form.Item>
        {/* 订阅数据源 */}
        <Form.Item label="订阅数据源" {...style}>
          <div style={{display:'flex',alignItems:'center'}}>
            <Select
              style={{ width: 300 }}
              open={false}
              placeholder='请选择'
              value={subscribeData?.objName}
              suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
              onChange={(value)=>_onChange(value,0)}
              allowClear
              onClick={() => setSubscribeDataVisible(true)} />
            <div style={{color:'#999',marginLeft:10}}>备注：系统发件时，会以收件人{`${!isEmpty(touids) ? `(${allMember.find(member => member.key == touids[0])?.label})` : ""}`}身份对订阅数据进行过滤。</div>
          </div>
        </Form.Item>
        <Form.Item label="正文" {...style}>
          <div style={{marginTop:5}}>
            <a onClick={() => setSubscribeTextVisible(true)}>选择模版</a>
            <TEditor
                ref={codeInputRef}
                placeholderText={""}
                heightMin={'200px'}
                uploadParams={{
                  teamId: teamId,
                  nodeId: nodeId,
                  moduleName: eNodeType[nodeType]?.nameEn,
                  objType: nodeType
                }}
                autofocus={false}
            />
            <div style={{color:'#999'}}>提示：占位符 <a onClick={()=>codeInputRef.current.insert('%订阅数据%')}>%订阅数据%</a>，代表需要订阅的搜索结果或报表数据，请点击占位符，将其包含进正文。</div>
          </div>
        </Form.Item>
        {/* 备注 */}
        {/* <Form.Item label="备注" {...style} name='description'>
          <TextArea style={{ minHeight: 200, borderRadius: 5 }} autoComplete="off"/>
        </Form.Item> */}
      </Form>
      <SubscribeContent
          allMember={allMember}
          subscribeDataVisible={subscribeDataVisible}
          setSubscribeDataVisible={setSubscribeDataVisible}
          subscribeData={subscribeData}
          setSubscribeData={setSubscribeData}
          typeList={typeList}
          fromType={fromType}
          ganttName={ganttName}
      />
      <TextModelModal
        subscribeTextVisible={subscribeTextVisible}
        setSubscribeTextVisible={setSubscribeTextVisible}
        setPushText={codeInputRef}
      />
      <PlanModal
        subscribePlanVisible={subscribePlanVisible}
        setSubscribePlanVisible={setSubscribePlanVisible}
        timedPlan={timedPlan}
        setTimedPlan={setTimedPlan}
      />
      <DraggablePopUp
        className="tms-modal"
        title='提示'
        centered
        width={300}
        open={isChange}
        onCancel={()=>setIsChange(false)}
        onOk={()=>closeDrawer(0)}
      >
        <div style={{textAlign:'center',margin:'10px 0px'}}>
          您正在编辑订阅，确定放弃编辑？
        </div>
      </DraggablePopUp>
      <DraggablePopUp
        className="tms-modal"
        title='提示'
        centered
        width={600}
        open={showTip}
        cancelText='继续保存'
        onCancel={()=>onOk(1)}
        okText='重新修改'
        onOk={()=>setShowTip(false)}
      >
        <div style={{margin:'10px 0px',fontSize:12}}>
          <div>内容框中，未检测到有 %订阅数据% 占位符，若不含此占位符，订阅邮件中将不会有 搜索或报表结果。</div>
          <div>点击"继续保存"，表示您不需要包含订阅数据，仅以"内容"框中文案作为邮件主体内容；</div>
          <div>点击"重新修改"，停在当前界面，您可在"内容"框中某个位置，点击蓝色%订阅数据%插入占位符。</div>
        </div>
      </DraggablePopUp>
    </DraggableDrawer>
  );
}
