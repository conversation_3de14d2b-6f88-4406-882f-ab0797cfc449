{"ast": null, "code": "import React,{useState,useEffect,useRef}from\"react\";import{Layout,Drawer,Button,Space,Tabs}from\"antd\";import{useParams,Link,useLocation,useNavigate}from\"react-router-dom\";import{shallowEqual,useDispatch,useSelector}from\"react-redux\";import{eMembergrpType}from\"@common/utils/enum\";import CreateInvitedMember from\"src/settings/utils/CreateInvitedMember/CreateInvitedMember\";import SettingsSideBar from\"./SettingsSideBar\";// 导入设置页面组件\nimport BasicInfoTab from\"./basicInfo/BasicInfoTab\";import TeamDomainSmtpTab from\"./basicInfo/SmtpTab\";import Personal_Tab_Subscribe from\"./personal/Manager_Tab_Subscribe\";import Personal_Tab_Data_Import from\"./personal/Personal_Tab_Data_Import\";import Settings_Product from\"./product/Settings_Product\";import MemberTab from\"./user/MemberTab\";import AdminTab from\"./user/AdminTab\";import InviteHistory from\"./user/InviteHistory\";import Personal from\"./personal/Personal\";import SpaceRoleTab from\"./space/SpaceRoleTab\";import SpaceTrashTab from\"./space/SpaceTrashTab\";import SpaceShareLinkHistoryTab from\"./space/SpaceShareLinkHistoryTab\";import MemberTab_Approval from\"./user/MemberTab_Approval\";import DraggablePopUp from'@components/DraggablePopUp';import DraggableDrawer from'@common/components/DraggableDrawer';import{useQuerySetting327_getTeamSpaceAadmin}from\"@common/service/commonHooks\";import\"./SettingsDrawer.scss\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Sider,Content}=Layout;const{TabPane}=Tabs;export default function SettingsDrawer(_ref){let{visible,onClose,teamId,defaultTab,productId}=_ref;//productId: 是打开\"应用管理\"某一个产品的的授权对话框\nconst[approvalNum,setApprovalNum]=useState(0);const[createInvitedMemberVisible,setCreateInvitedMemberVisible]=useState(false);const[currentContent,setCurrentContent]=useState(defaultTab||'basic');const[currentSpaceId,setCurrentSpaceId]=useState(null);const[activeTab,setActiveTab]=useState('base-info');const[closeConfirmVisible,setCloseConfirmVisible]=useState(false);// 新增：用于高亮“申请加入”分组\nconst[approvalGroupId,setApprovalGroupId]=useState(null);const[approvalGroupType,setApprovalGroupType]=useState(null);// 新增：动态设置浏览器标题\nconst prevTitle=useRef(document.title);useEffect(()=>{if(visible){prevTitle.current=document.title;document.title=\"团队设置\";}else{document.title=prevTitle.current;}return()=>{document.title=prevTitle.current;};},[visible]);// 监听defaultTab变化，动态切换tab\nuseEffect(()=>{if(visible&&defaultTab){setCurrentContent(defaultTab);// 根据tab类型设置activeTab\nif(defaultTab==='basic')setActiveTab('base-info');if(defaultTab==='user')setActiveTab('member');if(defaultTab==='product')setActiveTab('product');}},[visible,defaultTab]);const state=useSelector(state=>({userGrpList:state.getIn([\"workSetUp\",\"userGrpList\"])}),shallowEqual);const userGrpList=state.userGrpList||[];//const { spaceId } = useParams();\n// 提前解构，避免未初始化时被访问\nconst{data:{teamAdminFlag:isManager,spaceAdminFlag:isSpaceManager,teamSpaceAdminFlag}//teamSpaceAdminFlag表示，只要是团队中的“某一个群”的管理员，它就为true\n={teamAdminFlag:undefined,spaceAdminFlag:undefined,teamSpaceAdminFlag:undefined}}=useQuerySetting327_getTeamSpaceAadmin({teamId,spaceId:currentSpaceId,enabled:true});// 自动切换Tab副作用，避免在renderContent中setState\nuseEffect(()=>{if(currentContent==='space'&&!(isManager||isSpaceManager||teamSpaceAdminFlag)&&activeTab!=='member'){setActiveTab('member');}},[currentContent,isManager,isSpaceManager,teamSpaceAdminFlag,activeTab]);// 设置申请加入数量\nuseEffect(()=>{console.log('SettingsDrawer: userGrpList changed:',userGrpList);if((userGrpList||[]).length>0){var _userGrpList$find;const approvalCount=((_userGrpList$find=userGrpList.find(e=>e.membergrpType===eMembergrpType.grp_2_apply_join))===null||_userGrpList$find===void 0?void 0:_userGrpList$find.membergrpNum)||0;console.log('SettingsDrawer: setting approvalNum to:',approvalCount);setApprovalNum(approvalCount);}},[userGrpList]);// 处理申请加入点击 - 显示申请列表并高亮分组\nconst handleApprovalClick=()=>{setCurrentContent('user');// 找到“申请加入”分组\nconst approvalGroup=userGrpList.find(item=>item.membergrpType===2);// eMembergrpType.grp_2_apply_join = 2\nif(approvalGroup){setApprovalGroupId(approvalGroup.groupId);setApprovalGroupType(approvalGroup.membergrpType);}};// 处理SettingsSideBar的内容变化\nconst handleContentChange=function(content){let spaceId=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;let tab=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;console.log('SettingsDrawer: content changed to',content,spaceId,tab);setCurrentContent(content);setCurrentSpaceId(spaceId);if(tab){setActiveTab(tab);}else if(content==='basic'){// 当选择\"基础设置\"时，默认选中\"基本设置\"Tab\nsetActiveTab('base-info');}else if(content==='user'){// 当选择\"成员及管理员\"时，默认选中\"成员\"Tab\nsetActiveTab('member');}else if(content==='space'){// 当选择协作群时，默认选中\"角色权限\"Tab\nsetActiveTab('role');}};// 渲染右侧内容\nconst renderContent=()=>{var _userGrpList$find2,_userGrpList$find3,_userGrpList$find4,_userGrpList$find5;switch(currentContent){case'basic':return/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:[/*#__PURE__*/_jsx(TabPane,{tab:\"\\u57FA\\u672C\\u8BBE\\u7F6E\",children:/*#__PURE__*/_jsx(BasicInfoTab,{teamId:teamId})},\"base-info\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u57DF\\u540D&SMTP\\u670D\\u52A1\\u5668\",children:/*#__PURE__*/_jsx(TeamDomainSmtpTab,{teamId:teamId})},\"smtp\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u7CFB\\u7EDF\\u63A8\\u9001\\u8BBE\\u7F6E\",children:/*#__PURE__*/_jsx(Personal_Tab_Subscribe,{teamId:teamId})},\"subscribe\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u6570\\u636E\\u5BFC\\u5165\",children:/*#__PURE__*/_jsx(Personal_Tab_Data_Import,{teamId:teamId})},\"import\")]});case'product':return/*#__PURE__*/_jsx(Settings_Product,{teamId:teamId,productId:productId,visible:visible});case'user':return/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:[/*#__PURE__*/_jsx(TabPane,{tab:\"\\u6210\\u5458\",children:/*#__PURE__*/_jsx(MemberTab,{teamId:teamId,spaceId:currentSpaceId,isInDrawer:true,initialGroupId:approvalGroupId||((_userGrpList$find2=userGrpList.find(item=>item.membergrpType===eMembergrpType.grp_1_all_members))===null||_userGrpList$find2===void 0?void 0:_userGrpList$find2.groupId),initialMembergrpType:approvalGroupType||((_userGrpList$find3=userGrpList.find(item=>item.membergrpType===eMembergrpType.grp_1_all_members))===null||_userGrpList$find3===void 0?void 0:_userGrpList$find3.membergrpType)})},\"member\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u7BA1\\u7406\\u5458\",children:/*#__PURE__*/_jsx(AdminTab,{teamId:teamId,spaceId:currentSpaceId,isInDrawer:true})},\"admin\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u9080\\u8BF7\\u5386\\u53F2\",children:/*#__PURE__*/_jsx(InviteHistory,{teamId:teamId,spaceId:currentSpaceId})},\"invite\")]});case'personal':return/*#__PURE__*/_jsx(Personal,{teamId:teamId});case'space':// 非管理员只显示成员Tab\nif(!(isManager||isSpaceManager||teamSpaceAdminFlag)){const allMemberGroup=userGrpList.find(item=>item.membergrpType===eMembergrpType.grp_1_all_members);return/*#__PURE__*/_jsx(Tabs,{activeKey:'member',onChange:setActiveTab,children:/*#__PURE__*/_jsx(TabPane,{tab:\"\\u6210\\u5458\",children:/*#__PURE__*/_jsx(MemberTab,{teamId:teamId,spaceId:currentSpaceId,isInDrawer:true,initialGroupId:allMemberGroup===null||allMemberGroup===void 0?void 0:allMemberGroup.groupId,initialMembergrpType:allMemberGroup===null||allMemberGroup===void 0?void 0:allMemberGroup.membergrpType})},\"member\")});}// 管理员显示全部Tab\nreturn/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:[/*#__PURE__*/_jsx(TabPane,{tab:\"\\u89D2\\u8272\\u6743\\u9650\",children:/*#__PURE__*/_jsx(SpaceRoleTab,{teamId:teamId,spaceId:currentSpaceId,isInDrawer:true})},\"role\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u6210\\u5458\",children:/*#__PURE__*/_jsx(MemberTab,{teamId:teamId,spaceId:currentSpaceId,isInDrawer:true,initialGroupId:(_userGrpList$find4=userGrpList.find(item=>item.membergrpType===eMembergrpType.grp_1_all_members))===null||_userGrpList$find4===void 0?void 0:_userGrpList$find4.groupId,initialMembergrpType:(_userGrpList$find5=userGrpList.find(item=>item.membergrpType===eMembergrpType.grp_1_all_members))===null||_userGrpList$find5===void 0?void 0:_userGrpList$find5.membergrpType})},\"member\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u56DE\\u6536\\u7AD9\",children:/*#__PURE__*/_jsx(SpaceTrashTab,{teamId:teamId,spaceId:currentSpaceId})},\"trash\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u5916\\u94FE\\u5206\\u4EAB\",children:/*#__PURE__*/_jsx(SpaceShareLinkHistoryTab,{teamId:teamId,spaceId:currentSpaceId})},\"shareLinkHistory\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u9080\\u8BF7\\u5386\\u53F2\",children:/*#__PURE__*/_jsx(InviteHistory,{teamId:teamId,spaceId:currentSpaceId})},\"invite\")]});case'approval':const approvalGroup=userGrpList.find(item=>item.membergrpType===eMembergrpType.grp_2_apply_join);if(approvalGroup){return/*#__PURE__*/_jsx(MemberTab_Approval,{teamId:teamId,spaceId:null,selectedGroupId:approvalGroup.groupId,selectedMembergrpType:approvalGroup.membergrpType});}else{return/*#__PURE__*/_jsx(\"div\",{children:\"\\u672A\\u627E\\u5230\\u7533\\u8BF7\\u52A0\\u5165\\u5206\\u7EC4\"});}default:return/*#__PURE__*/_jsx(\"div\",{children:\"\\u8BF7\\u9009\\u62E9\\u8BBE\\u7F6E\\u9879\"});}};// 新的关闭逻辑\nconst handleDrawerClose=()=>{setCloseConfirmVisible(true);};const handleCloseConfirm=()=>{setCloseConfirmVisible(false);onClose&&onClose();};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(DraggableDrawer,{title:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u56E2\\u961F\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsxs(Space,{size:20,children:[!!approvalNum&&/*#__PURE__*/_jsxs(Button,{type:\"link\",onClick:handleApprovalClick,style:{padding:0},children:[\"\\u7533\\u8BF7\\u52A0\\u5165(\",approvalNum,\")\"]}),(isManager||isSpaceManager||teamSpaceAdminFlag)&&/*#__PURE__*/// 团队管理员 && 当前群管理员 &&   团队中某一个群管理员即为true\n_jsx(Button,{type:\"primary\",className:\"defaultBtn\",onClick:()=>setCreateInvitedMemberVisible(true),children:\"\\u9080\\u8BF7\\u6210\\u5458\"})]})]}),placement:\"right\",width:\"85%\",minWidth:\"40%\",maxWidth:\"95%\",draggableFlag:true,open:visible,onClose:handleDrawerClose,className:\"settings-drawer\",children:[/*#__PURE__*/_jsxs(Layout,{className:\"WorkSetUp-layout\",children:[/*#__PURE__*/_jsx(Sider,{width:260,className:\"WorkSetUp-layout-sider\",children:/*#__PURE__*/_jsx(SettingsSideBar,{teamId:teamId,isInDrawer:true,onContentChange:handleContentChange,activeKey:currentContent})}),/*#__PURE__*/_jsx(Content,{className:\"WorkSetUp-layout-content\",children:/*#__PURE__*/_jsx(\"div\",{className:\"WorkSetUp-layout-content-inner\",style:{padding:'20px 20px'},children:renderContent()})})]}),/*#__PURE__*/_jsx(CreateInvitedMember,{addPopVisible:createInvitedMemberVisible,onClose:()=>setCreateInvitedMemberVisible(false)})]}),/*#__PURE__*/_jsx(DraggablePopUp,{className:\"settings-drawer-close-confirm\",title:\"\\u63D0\\u793A\",icon:\"warning\",width:300,destroyOnClose:true,open:closeConfirmVisible,onOk:handleCloseConfirm,onCancel:()=>setCloseConfirmVisible(false),content:/*#__PURE__*/_jsx(\"span\",{children:\"\\u662F\\u5426\\u786E\\u5B9A\\u5173\\u95ED\\u56E2\\u961F\\u8BBE\\u7F6E\\u5BF9\\u8BDD\\u6846?\"})})]});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}