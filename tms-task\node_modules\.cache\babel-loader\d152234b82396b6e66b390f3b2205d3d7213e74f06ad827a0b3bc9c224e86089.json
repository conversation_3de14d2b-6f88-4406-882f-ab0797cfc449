{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\doc\\\\views\\\\doc\\\\DocRead.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable jsx-a11y/anchor-is-valid */\nimport { ExclamationCircleOutlined, FullscreenExitOutlined } from '@ant-design/icons';\nimport CommonComment from '@components/CommonComment/CommonComment';\nimport { TEditorPreview, TMarkdownPreview } from '@components/TEditor/TEditor';\nimport { useQuerySetting202_getTeamAllUsers } from \"@common/service/commonHooks\";\nimport { getUserNameById, isEmpty, twoTimeInterval, checkDate } from '@common/utils/ArrayUtils';\nimport { eDocEditOpType } from \"@common/utils/enum\";\nimport * as toolUtil from \"@common/utils/toolUtil\";\nimport { Button, Drawer, Dropdown, Menu, message, Modal, Popover, Skeleton, Space, Tooltip, Typography } from 'antd';\nimport { useEffect, useRef, useState } from \"react\";\nimport { FullScreen, useFullScreenHandle } from \"react-full-screen\";\nimport { Navigate, useLocation, useNavigate, useParams, useAsyncValue } from \"react-router-dom\";\nimport GeneralFooter from '@components/GeneralFooter/GeneralFooter';\nimport SharePopover from '@/share/views/components/SharePopover';\nimport * as httpDoc from \"@common/api/http\";\nimport { useQueryDoc005GetDocDetail, useMutationDOC003GetVersionList } from \"@/doc/service/docHooks\";\nimport DocHistory from \"./DocHistory\";\nimport { useMutation, useQueries, useQuery, useQueryClient } from \"@tanstack/react-query\";\nimport { doc003, doc004, doc005 } from \"@common/utils/ApiPath\";\nimport './DocRead.scss';\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { setting_320_get_node_priv_query, doc_004_get_tutorial_info_query, doc_005_get_doc_detail_query, team_530_get_obj_node_info_query, setting_get_team_import_setting_query } from \"@common/api/query/query\";\nimport { DATE_FORMAT_MICRO } from \"@/quickAcess/utils/Config\";\nimport DateUtil from \"@common/utils/dateUtil\";\nimport PageTitle from '@components/PageTitle';\nimport { eNodeTypeId, eNodeType } from \"@common/utils/TsbConfig\";\nimport { DocEditDrawer } from \"./DocEditDrawer\";\nimport NoviceGuide from '@components/NoviceGuide'; // 文档-新手引导\nimport { useOutletContext } from \"react-router-dom\";\nimport { useThrottle } from \"@common/hook\";\nimport * as clipboard from \"clipboard-polyfill\";\nimport { getUrl, reftechTeam504GetCommentList, reftechGeneralFooter } from '@common/utils/logicUtils';\nimport { jsPDF } from \"jspdf\";\nimport HTMLtoDOCX from \"html-to-docx\";\nimport axios from 'axios';\nimport jQuery from 'jquery';\nimport { saveAs } from 'file-saver';\nimport mammoth from 'mammoth';\nimport DraggableDrawer from \"@components/DraggableDrawer\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\n\n// 文档查看 \nexport default function DocRead() {\n  _s();\n  var _tutorialList$find, _tutorialList$find2, _eNodeType$nodeData$n, _eNodeType$nodeData$n2;\n  const navigate = useNavigate();\n  const params = useParams();\n  const {\n    teamId,\n    nodeId,\n    nid: objNodeId\n  } = params;\n  const location = useLocation();\n  //const { team530Result, setting320Result, team504Result, doc005Result } = useAsyncValue();\n\n  const {\n    searchQuery,\n    loading\n  } = useOutletContext() || {};\n\n  // const { nodeType } = team530Result || {}; // 左侧树文档nodeType: 311 和文档库下的文档nodeType 31201, nodeType不一致,在对象关联和被对象关联时需要区分, by bill 2023-03-10\n  // console.log(\"team530Result\", team530Result);\n  // console.log(\"nodeType\", nodeType);\n\n  const key = objNodeId || nodeId;\n  const [visibleVersion, setVisibleVersion] = useState(false);\n  const [visibleVersionEdit, setVisibleVersionEdit] = useState(false); //版本历史查看是否显示\n  const [visibleBeforeVerison, setVisibleBeforeVerison] = useState(false); //前一个版本按钮是否显示\n  const [visibleNextVerison, setVisibleNextVerison] = useState(false); //后一个版本按钮是否显示\n  const [currentVersion, setCurrentVersion] = useState(\"\"); //当前版本\n  const [lastVersion, setLastVersion] = useState(\"\"); //最新版本\n  const [beforeVersion, setBeforeVersion] = useState(\"\"); //前一个版本\n  const [nextVersion, setNextVersion] = useState(\"\"); //后一个版本\n  const [docVerionList, setDocVerionList] = useState([]); //版本历史内容\n  const [versionId, setVersionId] = useState(); // 版本id\n  const [docDrawerVisibleFlg, setDocDrawerVisibleFlg] = useState(false);\n  const [objNode, setobjNode] = useState({});\n  const [tutorialList, setTutorialList] = useState([]);\n\n  // 定义full变量，为的是兼容全屏和非全屏的样式，比如full的时候高度为200，非full高度为100\n  const [full, setFull] = useState(false);\n  // 创建一个fullScreen的handle\n  const handle = useFullScreenHandle();\n  const {\n    data: userList,\n    isLoading: isLoadingTeamAllUsers,\n    refetch: refetch202\n  } = useQuerySetting202_getTeamAllUsers(teamId);\n  // const { data : docDetail = {} , isLoading: isLoadingGetDocDetail, refetch: refetchGetDocDetail  } = useQueryDoc005GetDocDetail(teamId, key, versionId, true);\n\n  const {\n    data: setting320Result,\n    isLoading: isLoadingGetNodePriv,\n    refetch: refetchGetNodePriv\n  } = useQuery({\n    ...setting_320_get_node_priv_query(teamId, key, !!key)\n  });\n  const {\n    data: nodeData = {},\n    isLoading: isLoadingGetObjNodeInfo,\n    refetch: refetchGetObjNodeInfo\n  } = useQuery({\n    ...team_530_get_obj_node_info_query(teamId, key)\n  });\n  const {\n    data: {\n      rootNodeUi,\n      treeKeyList,\n      spaceId\n    } = {\n      rootNodeUi: null,\n      treeKeyList: []\n    },\n    dataUpdatedAt: dataUpdatedAtDoc004,\n    refetch: refetchGetTutorialInfo\n  } = useQuery({\n    ...doc_004_get_tutorial_info_query(teamId, nodeId, searchQuery, !!objNodeId && !loading)\n  });\n  const {\n    data: {\n      docDetail\n    } = {},\n    isLoading: isLoadingGetDocDetail,\n    refetch: refetchGetDocDetail\n  } = useQuery({\n    ...doc_005_get_doc_detail_query(teamId, key, versionId)\n  });\n  const {\n    data: {\n      docImportFlg\n    } = {\n      docImportFlg: 0\n    }\n  } = useQuery({\n    ...setting_get_team_import_setting_query(teamId, nodeId)\n  });\n  const {\n    onDOC003GetVersionList,\n    isMutating\n  } = useMutationDOC003GetVersionList();\n\n  // 文档详情返回的id就是objId, objNodeId就是objNodeId\n  const {\n    id: objId,\n    title,\n    content,\n    creatorUid,\n    adminCreatorUid,\n    updateUid,\n    createDt,\n    adminCreateDt,\n    updateDt,\n    docNo\n  } = docDetail || {};\n  useEffect(() => {\n    if (dataUpdatedAtDoc004) {\n      let _docList = docListFormat(rootNodeUi.children || [], []);\n      setTutorialList(_docList);\n    }\n  }, [dataUpdatedAtDoc004]);\n  function docListFormat(dataList, list) {\n    dataList.forEach(doc => {\n      if (doc.children.length > 0) {\n        return docListFormat(doc.children, list);\n      } else {\n        if (doc.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc || doc.nodeType == eNodeTypeId.nt_31202_objtype_docs_excel) {\n          list.push(doc);\n        }\n      }\n    });\n    return list;\n  }\n\n  // // 滚动至锚点\n  // const scrollToAnchor = () => {\n  //   setTimeout(() => {\n  //     let anchorName = location.hash;\n  //     if (anchorName) {\n  //       let anchorElement = document.querySelector(anchorName);\n  //       if (anchorElement) {\n  //         // 添加focus样式\n  //         anchorElement.classList.add(\"focus\")\n  //         anchorElement.scrollIntoView({\n  //           behavior: \"smooth\",  // 平滑过渡\n  //           block: \"start\"\n  //         });\n  //       }\n  //     }\n  //   }, 500);\n  // }\n\n  // 编辑\n  const onEditBtnClick = () => {\n    // const search = !isEmpty(objNodeId) ? `docs=${nodeId}&objNodeId=${objNodeId}&idType=${eDocEditOpType.EDIT_DOC}` : `objNodeId=${nodeId}&idType=${eDocEditOpType.EDIT_DOC}`\n    // navigate({\n    //   pathname: `/team/${teamId}/docEdit`,\n    //   search: search\n    // })\n    let spaceId = docDetail !== null && docDetail !== void 0 && docDetail.spaceId ? docDetail.spaceId : -999;\n    setobjNode(!isEmpty(objNodeId) ? {\n      docs: nodeId,\n      objNodeId: objNodeId,\n      idType: eDocEditOpType.EDIT_DOC,\n      spaceId: spaceId\n    } : {\n      objNodeId: nodeId,\n      idType: eDocEditOpType.EDIT_DOC,\n      spaceId: spaceId\n    });\n    setDocDrawerVisibleFlg(true);\n  };\n\n  // doc-003 get_version_list 获取版本列表\n  const getVerionList = () => {\n    const request = {\n      teamId: teamId,\n      objNodeId: key\n    };\n    onDOC003GetVersionList(request, {\n      onSuccess: (res, vars) => {\n        if (res.resultCode == 200) {\n          let data = res.versionList;\n          changeVersionList(data);\n          let lastVersion = !isEmpty(data) ? data[0].versionNo : \"0\";\n          setLastVersion(lastVersion); //最新版本\n          setDocVerionList(data);\n        }\n      },\n      onError: error => {\n        console.log(error);\n      }\n    });\n  };\n\n  // 获取userName\n  const changeVersionList = data => {\n    data.forEach(item => {\n      item.key = item.versionId;\n      item.userName = getUserNameById(userList, item.creatorUid);\n    });\n  };\n\n  // 版本历史\n  const historyVersionClick = () => {\n    getVerionList();\n    visibleVersionEditClick(false);\n    visibleVersionClick(true);\n  };\n\n  // 版本切换\n  function versionChange(currentVersion, item) {\n    // 点击当前版本，不显示查看历史版本编辑按钮\n    setVisibleVersionEdit(true);\n    versionViewChange(currentVersion);\n    // 关闭历史版本弹窗\n    visibleVersionClick(false);\n    setVersionId(item.versionId);\n  }\n\n  // 前一个，后一个版本信息变化\n  function versionViewChange(currentVersion) {\n    const dataSource = docVerionList;\n    const currentVersionIndex = dataSource.findIndex(item => item.versionNo === currentVersion);\n    // 当前version是第一个版本不显示前一个版本\n    let visibleBeforeVerison = currentVersionIndex !== dataSource.length - 1;\n    // 当前version是最新版本不显示后一个版本\n    let visibleNextVerison = currentVersionIndex !== 0;\n    let beforeVersion = visibleBeforeVerison ? dataSource[currentVersionIndex + 1].versionNo : \"\";\n    // 后一个版本：如果为当前版本,则显示当前\n    let nextVersion = currentVersionIndex - 1 === 0 ? dataSource[currentVersionIndex - 1].versionNo + \" 当前\" : currentVersionIndex - 1 < 0 ? \"\" : dataSource[currentVersionIndex - 1].versionNo;\n    // 当前版本为最新版本，则隐藏历史版本编辑按钮\n    /*  if (currentVersionIndex === 0) {\r\n      visibleVersionEditClick(false);\r\n    } */\n    setCurrentVersion(currentVersion);\n    setVisibleBeforeVerison(visibleBeforeVerison);\n    setVisibleNextVerison(visibleNextVerison);\n    setBeforeVersion(beforeVersion);\n    setNextVersion(nextVersion);\n  }\n\n  // 历史版本编辑按钮\n  function visibleVersionEditClick(data) {\n    setVisibleVersionEdit(data);\n  }\n\n  // 查看历史版本弹窗\n  function visibleVersionClick(data) {\n    setVisibleVersion(data);\n  }\n\n  // 返回最新版本\n  function backLastVerison() {\n    setCurrentVersion(lastVersion);\n    visibleVersionEditClick(false);\n    setVersionId(null);\n  }\n\n  // 恢复当前版本\n  function reverseCurrentVerison() {\n    const versionId = findVersionId();\n    restoreFromVersionIdConfirm(versionId);\n  }\n\n  // 恢复历史版本确认弹窗\n  const restoreFromVersionIdConfirm = versionId => {\n    const text = \"是否要恢复该版本？\";\n    Modal.confirm({\n      title: \"提醒\",\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }, this),\n      content: text,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: () => {\n        restoreFromVersionId(versionId);\n      }\n    });\n  };\n\n  // doc-011 restore_doc_from_version 文档版本恢复\n  // TODO:恢复历史版本后，左侧树怎么恢复文档名称？\n  const restoreFromVersionId = versionId => {\n    let request = {\n      teamId: teamId,\n      restoreVersionId: versionId,\n      objNodeId: key\n    };\n    httpDoc.doc_011_restore_doc_from_version(request).then(res => {\n      if (res.resultCode == 200) {\n        // setVersionId();//获取最新文档信息\n        // visibleVersionEditClick(false);\n        // visibleVersionClick(false);\n        globalUtil.success(\"恢复版本成功\"); // message\n        navigate({\n          pathname: location.pathname,\n          search: location.search,\n          hash: location.hash\n        }, {\n          state: {\n            refresh: true\n          }\n        });\n      }\n    }).catch(e => {\n      console.warn(e);\n    });\n  };\n\n  // 前一个/后一个\n  function navigateVersion(isBefore) {\n    let currentVersionIndex = docVerionList.findIndex(item => item.versionNo === currentVersion);\n    let data = {};\n    if (isBefore) {\n      data = docVerionList[++currentVersionIndex];\n    } else {\n      data = docVerionList[--currentVersionIndex];\n    }\n    const {\n      versionId,\n      versionNo\n    } = data;\n    setVersionId(versionId);\n    versionViewChange(versionNo);\n  }\n\n  // 找到versionId\n  const findVersionId = () => {\n    let currentVersionIndex = docVerionList.findIndex(item => item.versionNo === currentVersion);\n    const {\n      versionId,\n      versionNo\n    } = docVerionList[currentVersionIndex];\n    return versionId;\n  };\n\n  // 全屏\n  const fullScreenClick = () => {\n    setFull(true); // 点击设置full为true，接着调用handle的enter方法，进入全屏模式\n    handle.enter();\n  };\n  function describeFormat(creator, createDate, updator, updateDate, realCreateDt) {\n    if (!!realCreateDt) {\n      return format(creator, createDate, updator, updateDate, realCreateDt);\n    }\n    return format(creator, createDate, updator, updateDate);\n  }\n  function format(creator, createDate, updator, updateDate, realCreateDt) {\n    if ((!!realCreateDt ? realCreateDt : updateDate) != createDate) {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"fontcolor-light\",\n          children: [\"\\u7531\", getUserNameById(userList, creator), \"\\u521B\\u5EFA\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"fontcolor-light\",\n          children: [getUserNameById(userList, updator), \"\\u4FEE\\u6539\\u4E8E\", /*#__PURE__*/_jsxDEV(\"span\", {\n            children: checkDate(!!realCreateDt ? realCreateDt : updateDate, new DateUtil().getNow(DATE_FORMAT_MICRO), 1) ? /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [twoTimeInterval(!!realCreateDt ? realCreateDt : updateDate, new DateUtil().getNow(DATE_FORMAT_MICRO)), \"\\u524D\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this) : !!realCreateDt ? realCreateDt : updateDate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true);\n    } else {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"fontcolor-light\",\n          children: [\"\\u7531\", getUserNameById(userList, creator), \"\\u521B\\u5EFA\\u4E8E\", realCreateDt ? realCreateDt : createDate]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)\n      }, void 0, false);\n    }\n  }\n  const prevAndNextClick = useThrottle(_nodeId => {\n    navigate(`/team/${teamId}/docs/${nodeId}/doc/${_nodeId}`);\n  }, 500, []);\n\n  //复制链接地址\n  function copyLink() {\n    try {\n      const copyUrl = getUrl(teamId, {\n        ...nodeData,\n        anchorNodeId: nodeId\n      });\n      clipboard.writeText(copyUrl).then(() => {\n        globalUtil.success(\"链接已复制至剪贴板\");\n      }).catch(err => {\n        globalUtil.error(\"链接复制失败\");\n        console.error(\"Failed to copy text: \", err);\n      });\n    } catch (e) {\n      globalUtil.success(\"链接复制失败\");\n    }\n  }\n  function refetchData() {\n    refetchGetDocDetail();\n    refetch202();\n    reftechTeam504GetCommentList({\n      teamId,\n      nodeId: key\n    });\n    reftechGeneralFooter({\n      teamId,\n      nodeId: key,\n      objId: objId,\n      nodeType: nodeData.nodeType\n    });\n  }\n  function escapeHtml(value) {\n    return value.replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n  }\n  function readFileInputEventAsArrayBuffer(event, callback) {\n    var file = event.target.files[0];\n    var reader = new FileReader();\n    reader.onload = function (loadEvent) {\n      var arrayBuffer = loadEvent.target.result;\n      callback(arrayBuffer);\n    };\n    reader.readAsArrayBuffer(file);\n  }\n  function displayResult(result) {\n    document.querySelector(\".docread-body\").innerHTML = result.value;\n    var messageHtml = result.messages.map(function (message) {\n      return '<li class=\"' + message.type + '\">' + escapeHtml(message.message) + \"</li>\";\n    }).join(\"\");\n    document.querySelector(\".docread-comment\").innerHTML = \"<ul>\" + messageHtml + \"</ul>\";\n  }\n  function handleFileSelect(event) {\n    readFileInputEventAsArrayBuffer(event, function (arrayBuffer) {\n      mammoth.convertToHtml({\n        arrayBuffer: arrayBuffer\n      }).then(displayResult, function (error) {\n        console.error(error);\n      });\n    });\n  }\n\n  // function initExportHtml() {\n\n  // }\n\n  // 是否有编辑权限 当权限privWrite大于0则可以编辑\n  const editBtn = ((setting320Result === null || setting320Result === void 0 ? void 0 : setting320Result.privWrite) || 0) > 0 ? /*#__PURE__*/_jsxDEV(Button, {\n    type: \"primary\",\n    className: \"docread-header-title-right-btn\",\n    onClick: onEditBtnClick,\n    children: \"\\u7F16\\u8F91\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 426,\n    columnNumber: 56\n  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n\n  // 分享\n  const shareBtn = ((setting320Result === null || setting320Result === void 0 ? void 0 : setting320Result.privWrite) || 0) > 0 ? /*#__PURE__*/_jsxDEV(Popover, {\n    overlayClassName: \"sharePopver\",\n    destroyTooltipOnHide: true,\n    content: /*#__PURE__*/_jsxDEV(SharePopover, {\n      teamId: teamId,\n      nodeId: key\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 127\n    }, this),\n    title: \"\\u5916\\u94FE\\u5206\\u4EAB\",\n    trigger: \"click\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      title: \"\\u5916\\u94FE\\u5206\\u4EAB\",\n      type: \"text\",\n      icon: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"iconfont waibufenxiang fontsize-18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 44\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 429,\n    columnNumber: 57\n  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n  const exportBtn = /*#__PURE__*/_jsxDEV(Dropdown, {\n    menu: {\n      items: [{\n        key: 'pdf',\n        label: '导出为PDF'\n      }, {\n        key: 'word',\n        label: '导出为Word'\n      }\n      // { key: 'import', label: 'DOC文件导入' },\n      ],\n      onClick: async info => {\n        if (info.key === 'pdf') {\n          axios.get('/fonts/simhei.txt').then(result => result.data).then(font => {\n            // http://raw.githack.com/MrRio/jsPDF/master/docs/jsPDF.html\n            var doc = new jsPDF({\n              orientation: \"portrait\",\n              unit: \"px\",\n              format: \"a4\",\n              hotfixes: [\"px_scaling\"]\n            });\n            doc.addFileToVFS('Roboto-Regular-normal.ttf', font);\n            doc.addFont('Roboto-Regular-normal.ttf', 'Roboto', 'normal');\n            doc.setFont('Roboto', 'normal');\n            let $ = jQuery;\n            let $html = $('.docread-body');\n\n            // let cloneHtml = $html.clone();\n            $html.find('*').each((index, el) => {\n              $(el).css('font-family', '');\n            });\n            $html.width(doc.internal.pageSize.getWidth() - 60);\n            doc.html($html[0], {\n              margin: [10, 10],\n              callback: function (doc) {\n                doc.save(`${docDetail.title}.pdf`);\n                $html.width('100%');\n              }\n            });\n          });\n        } else if (info.key === 'word') {\n          let $ = jQuery;\n          let $html = $('.docread-body').find(\".fr-view\");\n          const htmlString = `<!DOCTYPE html>\n            <html lang=\"en\">\n              <head>\n                <meta charset=\"UTF-8\" />\n                <title>Document</title>\n              </head>\n              <body>\n                ${$html.prop('outerHTML')}\n              </body>\n            </html>`;\n          console.log(htmlString);\n          let fileBuffer = await HTMLtoDOCX(htmlString, null, {\n            table: {\n              row: {\n                cantSplit: true\n              }\n            },\n            footer: true,\n            pageNumber: true\n          });\n          saveAs(fileBuffer, `${docDetail.title}.docx`);\n        } /* else if(info.key === 'import') {\r\n          let input = document.createElement('input');\r\n          input.type = 'file';\r\n          input.addEventListener('change', handleFileSelect, false)\r\n          input.click();\r\n          } */\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      children: \"\\u5BFC\\u51FA\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 433,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"docread flex-column-parent\",\n      style: {\n        height: \"100%\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [visibleVersionEdit ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mock-definition-bar-version\",\n          children: [/*#__PURE__*/_jsxDEV(Space, {\n            size: 20,\n            className: \"ant-page-header-heading\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              onClick: () => backLastVerison(),\n              children: [\"\\u8FD4\\u56DE\\u6700\\u65B0\\u7248\\u672Cv\", lastVersion]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this), visibleNextVerison && /*#__PURE__*/_jsxDEV(\"a\", {\n              onClick: () => reverseCurrentVerison(),\n              children: \"\\u6062\\u590D\\u8BE5\\u7248\\u672C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 40\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              onClick: () => {\n                getVerionList();\n                visibleVersionEditClick(false);\n                visibleVersionClick(true);\n              },\n              children: \"\\u67E5\\u770B\\u7248\\u672C\\u5386\\u53F2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mock-definition-bar-btns\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              size: 25,\n              children: [\n              // 没有前一个版本，则不显示\n              visibleBeforeVerison && /*#__PURE__*/_jsxDEV(Button, {\n                className: \"ant-btn ant-btn-sm ant-btn-primary\",\n                onClick: () => navigateVersion(true),\n                children: [\"\\u524D\\u4E00\\u4E2A(v\", beforeVersion, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"v\", currentVersion]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this), visibleNextVerison && /*#__PURE__*/_jsxDEV(Button, {\n                className: \"ant-btn ant-btn-sm ant-btn-primary\",\n                onClick: () => navigateVersion(false),\n                children: [\"\\u4E0B\\u4E00\\u4E2A(v\", nextVersion, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"docread-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"docread-header-title\",\n            children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n              teamId: teamId,\n              nodeId: key,\n              powerLock: true,\n              powerShare: nodeData.nodeType == eNodeTypeId.nt_311_objtype_doc,\n              refetchData: refetchData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"docread-header-title-right\",\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                size: 5,\n                children: [nodeData.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc && (tutorialList !== null && tutorialList !== void 0 && (_tutorialList$find = tutorialList.find(tutorial => tutorial.nodeId == (docDetail === null || docDetail === void 0 ? void 0 : docDetail.objNodeId))) !== null && _tutorialList$find !== void 0 && _tutorialList$find.prevNodeId ? /*#__PURE__*/_jsxDEV(\"a\", {\n                  style: {\n                    fontSize: 12,\n                    marginRight: 10\n                  },\n                  onClick: () => prevAndNextClick(tutorialList.find(tutorial => tutorial.nodeId == docDetail.objNodeId).prevNodeId),\n                  children: '<上一篇'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 169\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: 12,\n                    marginRight: 10,\n                    color: '#CCCCCC'\n                  },\n                  children: '<上一篇'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 337\n                }, this)), nodeData.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc && (tutorialList !== null && tutorialList !== void 0 && (_tutorialList$find2 = tutorialList.find(tutorial => tutorial.nodeId == (docDetail === null || docDetail === void 0 ? void 0 : docDetail.objNodeId))) !== null && _tutorialList$find2 !== void 0 && _tutorialList$find2.nextNodeId ? /*#__PURE__*/_jsxDEV(\"a\", {\n                  style: {\n                    fontSize: 12,\n                    marginRight: 10\n                  },\n                  onClick: () => prevAndNextClick(tutorialList.find(tutorial => tutorial.nodeId == docDetail.objNodeId).nextNodeId),\n                  children: '下一篇>'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 169\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: 12,\n                    marginRight: 10,\n                    color: '#CCCCCC'\n                  },\n                  children: '下一篇>'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 337\n                }, this)), docImportFlg != 1 && visibleVersionEdit ? null : editBtn, shareBtn, exportBtn, /*#__PURE__*/_jsxDEV(Button, {\n                  title: \"\\u5168\\u5C4F\\u67E5\\u770B\",\n                  type: \"text\",\n                  icon: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"iconfont quanping1 fontsize-16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 58\n                  }, this),\n                  onClick: () => {\n                    fullScreenClick();\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  title: \"\\u7248\\u672C\\u5386\\u53F2\",\n                  type: \"text\",\n                  icon: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"iconfont lishiguiji fontsize-16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 60\n                  }, this),\n                  onClick: historyVersionClick\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"docread-header-des\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              size: 15,\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                onClick: () => copyLink(),\n                children: docNo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 17\n              }, this), describeFormat(creatorUid, createDt, updateUid, updateDt, adminCreateDt)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-column-child\",\n          id: \"docRead\",\n          children: [/*#__PURE__*/_jsxDEV(FullScreen, {\n            handle: handle,\n            onChange: setFull,\n            style: {\n              background: \"#ffffff\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"docread-body\",\n              style: {\n                fontFamily: 'Roboto'\n              },\n              children: [/* 全屏时显示退出全屏按钮 */\n              full && /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u9000\\u51FA\\u5168\\u5C4F\",\n                placement: \"bottom\",\n                children: /*#__PURE__*/_jsxDEV(FullscreenExitOutlined, {\n                  className: \"fullscreen-exit\"\n                  // 退出全屏模式并把full设置为false\n                  ,\n                  onClick: () => {\n                    setFull(false);\n                    handle.exit();\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 27\n              }, this), (docDetail === null || docDetail === void 0 ? void 0 : docDetail.editorType) === 1 ? /*#__PURE__*/_jsxDEV(TMarkdownPreview, {\n                content: docDetail === null || docDetail === void 0 ? void 0 : docDetail.content,\n                isedit: true,\n                uploadParams: {\n                  teamId\n                },\n                onRefresh: () => refetchGetDocDetail()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 47\n              }, this) : /*#__PURE__*/_jsxDEV(TEditorPreview, {\n                content: docDetail === null || docDetail === void 0 ? void 0 : docDetail.content,\n                isedit: true,\n                uploadParams: {\n                  teamId\n                },\n                onRefresh: () => refetchGetDocDetail()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 13\n          }, this), objId && key && /*#__PURE__*/_jsxDEV(GeneralFooter, {\n            objId: objId,\n            objNodeId: key,\n            objType: nodeData.nodeType,\n            moduleName: (_eNodeType$nodeData$n = eNodeType[nodeData.nodeType]) === null || _eNodeType$nodeData$n === void 0 ? void 0 : _eNodeType$nodeData$n.nameEn\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 32\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"docread-comment\",\n            id: `comment-${objNodeId}`,\n            children: /*#__PURE__*/_jsxDEV(CommonComment, {\n              uploadParams: {\n                teamId,\n                anchorNodeId: nodeId,\n                nodeId: key,\n                moduleName: (_eNodeType$nodeData$n2 = eNodeType[nodeData.nodeType]) === null || _eNodeType$nodeData$n2 === void 0 ? void 0 : _eNodeType$nodeData$n2.nameEn,\n                objType: nodeData.nodeType\n              },\n              showEditorVisible: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n        className: \"tms-drawer \",\n        width: \"65%\",\n        onClose: () => visibleVersionClick(false),\n        open: visibleVersion,\n        title: title || '' + (!!(title || '') ? ' - 版本历史' : ''),\n        closable: true,\n        footer: null,\n        children: /*#__PURE__*/_jsxDEV(Skeleton, {\n          loading: isMutating,\n          children: /*#__PURE__*/_jsxDEV(DocHistory, {\n            setting320Result: setting320Result,\n            currentVersion: currentVersion,\n            versionChange: versionChange,\n            docVerionList: docVerionList,\n            restoreFromVersionIdConfirm: restoreFromVersionIdConfirm\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 7\n      }, this), docDrawerVisibleFlg && /*#__PURE__*/_jsxDEV(DocEditDrawer, {\n        objNode: objNode,\n        setDocDrawerVisibleFlg: setDocDrawerVisibleFlg,\n        onRefresh: () => refetchGetDocDetail(),\n        visibleVersionEdit: visibleVersionEdit,\n        versionId: versionId,\n        _docImportFlg: docImportFlg,\n        docDrawerVisibleFlg: docDrawerVisibleFlg\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(NoviceGuide, {\n      nodeType: nodeData.nodeType,\n      awakeFlg: 0\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n}\n_s(DocRead, \"ZU4QBXCI0NAzzhgubPuWOefSgUE=\", false, function () {\n  return [useNavigate, useParams, useLocation, useOutletContext, useFullScreenHandle, useQuerySetting202_getTeamAllUsers, useQuery, useQuery, useQuery, useQuery, useQuery, useMutationDOC003GetVersionList, useThrottle];\n});\n_c = DocRead;\nvar _c;\n$RefreshReg$(_c, \"DocRead\");", "map": {"version": 3, "names": ["ExclamationCircleOutlined", "FullscreenExitOutlined", "CommonComment", "TEditorPreview", "TMarkdownPreview", "useQuerySetting202_getTeamAllUsers", "getUserNameById", "isEmpty", "twoTimeInterval", "checkDate", "eDocEditOpType", "toolUtil", "<PERSON><PERSON>", "Drawer", "Dropdown", "<PERSON><PERSON>", "message", "Modal", "Popover", "Skeleton", "Space", "<PERSON><PERSON><PERSON>", "Typography", "useEffect", "useRef", "useState", "FullScreen", "useFullScreenHandle", "Navigate", "useLocation", "useNavigate", "useParams", "useAsyncValue", "General<PERSON><PERSON>er", "SharePopover", "httpDoc", "useQueryDoc005GetDocDetail", "useMutationDOC003GetVersionList", "DocHistory", "useMutation", "useQueries", "useQuery", "useQueryClient", "doc003", "doc004", "doc005", "globalUtil", "setting_320_get_node_priv_query", "doc_004_get_tutorial_info_query", "doc_005_get_doc_detail_query", "team_530_get_obj_node_info_query", "setting_get_team_import_setting_query", "DATE_FORMAT_MICRO", "DateUtil", "Page<PERSON><PERSON>le", "eNodeTypeId", "eNodeType", "DocEditDrawer", "NoviceGuide", "useOutletContext", "useThrottle", "clipboard", "getUrl", "reftechTeam504GetCommentList", "reft<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsPDF", "HTMLtoDOCX", "axios", "j<PERSON><PERSON><PERSON>", "saveAs", "mammoth", "DraggableDrawer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "DocRead", "_s", "_tutorialList$find", "_tutorialList$find2", "_eNodeType$nodeData$n", "_eNodeType$nodeData$n2", "navigate", "params", "teamId", "nodeId", "nid", "objNodeId", "location", "searchQuery", "loading", "key", "visibleVersion", "setVisibleVersion", "visibleVersionEdit", "setVisibleVersionEdit", "visibleBefore<PERSON><PERSON>son", "setVisibleBeforeVerison", "visibleNextVerison", "setVisibleNextVerison", "currentVersion", "setCurrentVersion", "lastVersion", "setLastVersion", "beforeVersion", "setBeforeVersion", "nextVersion", "setNextVersion", "docVerionList", "setDocVerionList", "versionId", "setVersionId", "docDrawerVisibleFlg", "setDocDrawerVisibleFlg", "objNode", "setobjNode", "tutorialList", "setTutorialList", "full", "setFull", "handle", "data", "userList", "isLoading", "isLoadingTeamAllUsers", "refetch", "refetch202", "setting320Result", "isLoadingGetNodePriv", "refetchGetNodePriv", "nodeData", "isLoadingGetObjNodeInfo", "refetchGetObjNodeInfo", "rootNodeUi", "treeKeyList", "spaceId", "dataUpdatedAt", "dataUpdatedAtDoc004", "refetchGetTutorialInfo", "docDetail", "isLoadingGetDocDetail", "refetchGetDocDetail", "docImportFlg", "onDOC003GetVersionList", "isMutating", "id", "objId", "title", "content", "creator<PERSON><PERSON>", "adminCreatorUid", "updateUid", "createDt", "adminCreateDt", "updateDt", "docNo", "_docList", "docListFormat", "children", "dataList", "list", "for<PERSON>ach", "doc", "length", "nodeType", "nt_31201_objtype_docs_doc", "nt_31202_objtype_docs_excel", "push", "onEditBtnClick", "docs", "idType", "EDIT_DOC", "getVerionList", "request", "onSuccess", "res", "vars", "resultCode", "versionList", "changeVersionList", "versionNo", "onError", "error", "console", "log", "item", "userName", "historyVersionClick", "visibleVersionEditClick", "visibleVersionClick", "versionChange", "versionViewChange", "dataSource", "currentVersionIndex", "findIndex", "backLast<PERSON><PERSON><PERSON>", "reverseCurrentVerison", "findVersionId", "restoreFromVersionIdConfirm", "text", "confirm", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "okText", "cancelText", "onOk", "restoreFromVersionId", "restoreVersionId", "doc_011_restore_doc_from_version", "then", "success", "pathname", "search", "hash", "state", "refresh", "catch", "e", "warn", "navigateVersion", "isBefore", "fullScreenClick", "enter", "describeFormat", "creator", "createDate", "updator", "updateDate", "realCreateDt", "format", "className", "getNow", "prevAndNextClick", "_nodeId", "copyLink", "copyUrl", "anchorNodeId", "writeText", "err", "refetchData", "escapeHtml", "value", "replace", "readFileInputEventAsArrayBuffer", "event", "callback", "file", "target", "files", "reader", "FileReader", "onload", "loadEvent", "arrayBuffer", "result", "readAsA<PERSON>y<PERSON><PERSON>er", "displayResult", "document", "querySelector", "innerHTML", "messageHtml", "messages", "map", "type", "join", "handleFileSelect", "convertToHtml", "editBtn", "privWrite", "onClick", "shareBtn", "overlayClassName", "destroyTooltipOnHide", "trigger", "exportBtn", "menu", "items", "label", "info", "get", "font", "orientation", "unit", "hotfixes", "addFileToVFS", "addFont", "setFont", "$", "$html", "find", "each", "index", "el", "css", "width", "internal", "pageSize", "getWidth", "html", "margin", "save", "htmlString", "prop", "fileBuffer", "table", "row", "cantSplit", "footer", "pageNumber", "style", "height", "size", "powerLock", "powerShare", "nt_311_objtype_doc", "tutorial", "prevNodeId", "fontSize", "marginRight", "color", "nextNodeId", "onChange", "background", "fontFamily", "placement", "exit", "editorType", "isedit", "uploadParams", "onRefresh", "objType", "moduleName", "nameEn", "showEditorVisible", "onClose", "open", "closable", "_docImportFlg", "awakeFlg", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/doc/views/doc/DocRead.jsx"], "sourcesContent": ["/* eslint-disable jsx-a11y/anchor-is-valid */\r\nimport { ExclamationCircleOutlined, FullscreenExitOutlined } from '@ant-design/icons';\r\nimport CommonComment from '@components/CommonComment/CommonComment';\r\nimport { TEditorPreview, TMarkdownPreview } from '@components/TEditor/TEditor';\r\nimport { useQuerySetting202_getTeamAllUsers } from \"@common/service/commonHooks\";\r\nimport { getUserNameById, isEmpty, twoTimeInterval, checkDate  } from '@common/utils/ArrayUtils';\r\nimport { eDocEditOpType } from \"@common/utils/enum\";\r\nimport * as toolUtil from \"@common/utils/toolUtil\";\r\nimport { Button, Drawer, Dropdown, Menu, message, Modal, Popover, Skeleton, Space, Tooltip, Typography } from 'antd';\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { FullScreen, useFullScreenHandle } from \"react-full-screen\";\r\nimport { Navigate, useLocation, useNavigate, useParams,useAsyncValue } from \"react-router-dom\";\r\nimport GeneralFooter from '@components/GeneralFooter/GeneralFooter';\r\nimport SharePopover from '@/share/views/components/SharePopover';\r\nimport * as httpDoc from \"@common/api/http\";\r\nimport { useQueryDoc005GetDocDetail, useMutationDOC003GetVersionList } from \"@/doc/service/docHooks\";\r\nimport DocHistory from \"./DocHistory\";\r\nimport { useMutation, useQueries, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { doc003, doc004, doc005 } from \"@common/utils/ApiPath\";\r\nimport './DocRead.scss';\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { setting_320_get_node_priv_query,doc_004_get_tutorial_info_query,doc_005_get_doc_detail_query,team_530_get_obj_node_info_query, setting_get_team_import_setting_query } from \"@common/api/query/query\"\r\nimport { DATE_FORMAT_MICRO } from \"@/quickAcess/utils/Config\";\r\nimport DateUtil from \"@common/utils/dateUtil\";\r\nimport PageTitle from '@components/PageTitle';\r\nimport { eNodeTypeId, eNodeType } from \"@common/utils/TsbConfig\";\r\nimport {DocEditDrawer} from \"./DocEditDrawer\";\r\nimport NoviceGuide from '@components/NoviceGuide'; // 文档-新手引导\r\nimport { useOutletContext } from \"react-router-dom\";\r\nimport {useThrottle} from \"@common/hook\";\r\nimport * as clipboard from \"clipboard-polyfill\";\r\nimport { getUrl, reftechTeam504GetCommentList, reftechGeneralFooter } from '@common/utils/logicUtils';\r\nimport { jsPDF } from \"jspdf\";\r\nimport HTMLtoDOCX from \"html-to-docx\";\r\nimport axios from 'axios';\r\nimport jQuery from 'jquery';\r\nimport { saveAs } from 'file-saver';\r\nimport mammoth from 'mammoth';\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\n\r\nconst { Title } = Typography;\r\n\r\n// 文档查看 \r\nexport default function DocRead() {\r\n  const navigate = useNavigate()\r\n  const params = useParams()\r\n  const { teamId, nodeId, nid:objNodeId } = params;\r\n  const location = useLocation();\r\n  //const { team530Result, setting320Result, team504Result, doc005Result } = useAsyncValue();\r\n\r\n  const { searchQuery, loading } = useOutletContext() || {};\r\n\r\n  // const { nodeType } = team530Result || {}; // 左侧树文档nodeType: 311 和文档库下的文档nodeType 31201, nodeType不一致,在对象关联和被对象关联时需要区分, by bill 2023-03-10\r\n  // console.log(\"team530Result\", team530Result);\r\n  // console.log(\"nodeType\", nodeType);\r\n\r\n  const key = objNodeId || nodeId;\r\n\r\n  const [visibleVersion, setVisibleVersion] = useState(false);\r\n  const [visibleVersionEdit, setVisibleVersionEdit] = useState(false); //版本历史查看是否显示\r\n  const [visibleBeforeVerison, setVisibleBeforeVerison] = useState(false); //前一个版本按钮是否显示\r\n  const [visibleNextVerison, setVisibleNextVerison] = useState(false); //后一个版本按钮是否显示\r\n  const [currentVersion, setCurrentVersion] = useState(\"\"); //当前版本\r\n  const [lastVersion, setLastVersion] = useState(\"\"); //最新版本\r\n  const [beforeVersion, setBeforeVersion] = useState(\"\"); //前一个版本\r\n  const [nextVersion, setNextVersion] = useState(\"\"); //后一个版本\r\n  const [docVerionList, setDocVerionList] = useState([]); //版本历史内容\r\n  const [versionId, setVersionId] = useState(); // 版本id\r\n  const [docDrawerVisibleFlg,setDocDrawerVisibleFlg] = useState(false);\r\n  const [objNode,setobjNode] = useState({});\r\n  const [tutorialList,setTutorialList] = useState([]);\r\n\r\n  // 定义full变量，为的是兼容全屏和非全屏的样式，比如full的时候高度为200，非full高度为100\r\n  const [full, setFull] = useState(false);\r\n  // 创建一个fullScreen的handle\r\n  const handle = useFullScreenHandle();\r\n\r\n  const { data: userList, isLoading: isLoadingTeamAllUsers, refetch: refetch202 } = useQuerySetting202_getTeamAllUsers(teamId);\r\n  // const { data : docDetail = {} , isLoading: isLoadingGetDocDetail, refetch: refetchGetDocDetail  } = useQueryDoc005GetDocDetail(teamId, key, versionId, true);\r\n\r\n  const {data: setting320Result, isLoading: isLoadingGetNodePriv, refetch: refetchGetNodePriv} = useQuery({\r\n    ...setting_320_get_node_priv_query(teamId,key,!!key)\r\n  })\r\n\r\n  const {data: nodeData = {}, isLoading: isLoadingGetObjNodeInfo, refetch: refetchGetObjNodeInfo} = useQuery({\r\n    ...team_530_get_obj_node_info_query(teamId,key)\r\n  })\r\n\r\n  const { data : { rootNodeUi, treeKeyList, spaceId } = { rootNodeUi: null, treeKeyList: [] }, dataUpdatedAt: dataUpdatedAtDoc004, refetch: refetchGetTutorialInfo } = useQuery({\r\n    ...doc_004_get_tutorial_info_query(teamId, nodeId, searchQuery, !!objNodeId && !loading)\r\n  })\r\n  const {data: {docDetail}= {}, isLoading: isLoadingGetDocDetail, refetch: refetchGetDocDetail } = useQuery({\r\n    ...doc_005_get_doc_detail_query(teamId, key, versionId)\r\n  })\r\n\r\n  const { data: {docImportFlg} = {docImportFlg: 0} } = useQuery({...setting_get_team_import_setting_query(teamId, nodeId)});\r\n\r\n  const { onDOC003GetVersionList, isMutating } = useMutationDOC003GetVersionList();\r\n  \r\n  // 文档详情返回的id就是objId, objNodeId就是objNodeId\r\n  const { id: objId, title, content, creatorUid, adminCreatorUid, updateUid, createDt, adminCreateDt, updateDt, docNo } = docDetail || {};\r\n\r\n  useEffect(()=>{\r\n    if(dataUpdatedAtDoc004){\r\n      let _docList = docListFormat(rootNodeUi.children || [], []);\r\n      setTutorialList(_docList);\r\n    }\r\n  },[dataUpdatedAtDoc004]);\r\n\r\n  function docListFormat(dataList,list){\r\n    dataList.forEach(doc => {\r\n      if(doc.children.length > 0){\r\n        return docListFormat(doc.children,list)\r\n      }else{\r\n        if(doc.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc || doc.nodeType == eNodeTypeId.nt_31202_objtype_docs_excel){\r\n          list.push(doc)\r\n        }\r\n      }\r\n    })\r\n    return list\r\n  }\r\n\r\n  // // 滚动至锚点\r\n  // const scrollToAnchor = () => {\r\n  //   setTimeout(() => {\r\n  //     let anchorName = location.hash;\r\n  //     if (anchorName) {\r\n  //       let anchorElement = document.querySelector(anchorName);\r\n  //       if (anchorElement) {\r\n  //         // 添加focus样式\r\n  //         anchorElement.classList.add(\"focus\")\r\n  //         anchorElement.scrollIntoView({\r\n  //           behavior: \"smooth\",  // 平滑过渡\r\n  //           block: \"start\"\r\n  //         });\r\n  //       }\r\n  //     }\r\n  //   }, 500);\r\n  // }\r\n\r\n  // 编辑\r\n  const onEditBtnClick = () => {\r\n    // const search = !isEmpty(objNodeId) ? `docs=${nodeId}&objNodeId=${objNodeId}&idType=${eDocEditOpType.EDIT_DOC}` : `objNodeId=${nodeId}&idType=${eDocEditOpType.EDIT_DOC}`\r\n    // navigate({\r\n    //   pathname: `/team/${teamId}/docEdit`,\r\n    //   search: search\r\n    // })\r\n    let spaceId = docDetail?.spaceId ? docDetail.spaceId : -999\r\n    setobjNode(!isEmpty(objNodeId) ? {docs: nodeId, objNodeId: objNodeId, idType: eDocEditOpType.EDIT_DOC, spaceId: spaceId} : {objNodeId: nodeId, idType: eDocEditOpType.EDIT_DOC, spaceId: spaceId});\r\n    setDocDrawerVisibleFlg(true);\r\n  }\r\n\r\n  // doc-003 get_version_list 获取版本列表\r\n  const getVerionList = () => {\r\n    const request = {\r\n      teamId: teamId,\r\n      objNodeId: key,\r\n    }\r\n    onDOC003GetVersionList(request, {\r\n      onSuccess: (res, vars) => {\r\n          if (res.resultCode == 200) {\r\n            let data = res.versionList;\r\n            changeVersionList(data);\r\n            let lastVersion = !isEmpty(data) ? data[0].versionNo : \"0\";\r\n            setLastVersion(lastVersion); //最新版本\r\n            setDocVerionList(data);\r\n          }\r\n      },\r\n      onError: (error) => {\r\n          console.log(error)\r\n      }\r\n    });\r\n  }\r\n\r\n  // 获取userName\r\n  const changeVersionList = (data) => {\r\n    data.forEach(item => {\r\n      item.key = item.versionId;\r\n      item.userName = getUserNameById(userList, item.creatorUid)\r\n    });\r\n  }\r\n\r\n  // 版本历史\r\n  const historyVersionClick = () => {\r\n    getVerionList();\r\n    visibleVersionEditClick(false);\r\n    visibleVersionClick(true);\r\n  }\r\n\r\n  // 版本切换\r\n  function versionChange(currentVersion, item) {\r\n    // 点击当前版本，不显示查看历史版本编辑按钮\r\n    setVisibleVersionEdit(true);\r\n    versionViewChange(currentVersion);\r\n    // 关闭历史版本弹窗\r\n    visibleVersionClick(false);\r\n    setVersionId(item.versionId);\r\n  }\r\n\r\n  // 前一个，后一个版本信息变化\r\n  function versionViewChange(currentVersion) {\r\n    const dataSource = docVerionList;\r\n    const currentVersionIndex = dataSource.findIndex(\r\n      (item) => item.versionNo === currentVersion\r\n    );\r\n    // 当前version是第一个版本不显示前一个版本\r\n    let visibleBeforeVerison = currentVersionIndex !== dataSource.length - 1;\r\n    // 当前version是最新版本不显示后一个版本\r\n    let visibleNextVerison = currentVersionIndex !== 0;\r\n    let beforeVersion = visibleBeforeVerison ? dataSource[currentVersionIndex + 1].versionNo : \"\";\r\n    // 后一个版本：如果为当前版本,则显示当前\r\n    let nextVersion = currentVersionIndex - 1 === 0\r\n      ? dataSource[currentVersionIndex - 1].versionNo + \" 当前\"\r\n      : currentVersionIndex - 1 < 0\r\n        ? \"\"\r\n        : dataSource[currentVersionIndex - 1].versionNo;\r\n    // 当前版本为最新版本，则隐藏历史版本编辑按钮\r\n    /*  if (currentVersionIndex === 0) {\r\n      visibleVersionEditClick(false);\r\n    } */\r\n    setCurrentVersion(currentVersion)\r\n    setVisibleBeforeVerison(visibleBeforeVerison);\r\n    setVisibleNextVerison(visibleNextVerison);\r\n    setBeforeVersion(beforeVersion);\r\n    setNextVersion(nextVersion);\r\n  }\r\n\r\n  // 历史版本编辑按钮\r\n  function visibleVersionEditClick(data) {\r\n    setVisibleVersionEdit(data);\r\n  }\r\n\r\n  // 查看历史版本弹窗\r\n  function visibleVersionClick(data) {\r\n    setVisibleVersion(data);\r\n  }\r\n\r\n  // 返回最新版本\r\n  function backLastVerison() {\r\n    setCurrentVersion(lastVersion);\r\n    visibleVersionEditClick(false);\r\n    setVersionId(null);\r\n  }\r\n\r\n  // 恢复当前版本\r\n  function reverseCurrentVerison() {\r\n    const versionId = findVersionId();\r\n    restoreFromVersionIdConfirm(versionId);\r\n  }\r\n\r\n  // 恢复历史版本确认弹窗\r\n  const restoreFromVersionIdConfirm = (versionId) => {\r\n    const text = \"是否要恢复该版本？\"\r\n    Modal.confirm({\r\n      title: \"提醒\",\r\n      icon: <ExclamationCircleOutlined />,\r\n      content: text,\r\n      okText: '确认',\r\n      cancelText: '取消',\r\n      onOk: () => { restoreFromVersionId(versionId) }\r\n    });\r\n  }\r\n\r\n  // doc-011 restore_doc_from_version 文档版本恢复\r\n  // TODO:恢复历史版本后，左侧树怎么恢复文档名称？\r\n  const restoreFromVersionId = (versionId) => {\r\n    let request = {\r\n      teamId: teamId,\r\n      restoreVersionId: versionId,\r\n      objNodeId: key,\r\n    }\r\n    httpDoc.doc_011_restore_doc_from_version(request).then((res) => {\r\n      if (res.resultCode == 200) {\r\n        // setVersionId();//获取最新文档信息\r\n        // visibleVersionEditClick(false);\r\n        // visibleVersionClick(false);\r\n        globalUtil.success(\"恢复版本成功\"); // message\r\n        navigate({\r\n          pathname: location.pathname,\r\n          search: location.search,\r\n          hash: location.hash,\r\n        },{\r\n          state: {\r\n            refresh: true\r\n          }\r\n        })\r\n\r\n      }\r\n    }).catch(e => {\r\n      console.warn(e);\r\n    });\r\n  }\r\n\r\n  // 前一个/后一个\r\n  function navigateVersion(isBefore) {\r\n    let currentVersionIndex = docVerionList.findIndex(\r\n      (item) => item.versionNo === currentVersion\r\n    );\r\n    let data = {};\r\n    if (isBefore) {\r\n      data = docVerionList[++currentVersionIndex];\r\n    } else {\r\n      data = docVerionList[--currentVersionIndex];\r\n    }\r\n    const { versionId, versionNo } = data\r\n    setVersionId(versionId)\r\n    versionViewChange(versionNo);\r\n  }\r\n\r\n  // 找到versionId\r\n  const findVersionId = () => {\r\n    let currentVersionIndex = docVerionList.findIndex((item) => item.versionNo === currentVersion);\r\n    const { versionId, versionNo } = docVerionList[currentVersionIndex];\r\n    return versionId\r\n  }\r\n\r\n  // 全屏\r\n  const fullScreenClick = () => {\r\n    setFull(true);  // 点击设置full为true，接着调用handle的enter方法，进入全屏模式\r\n    handle.enter();\r\n  }\r\n\r\n  function describeFormat(creator,createDate,updator,updateDate,realCreateDt){\r\n    if(!!realCreateDt){\r\n      return format(creator,createDate,updator,updateDate,realCreateDt);\r\n    }\r\n    return format(creator,createDate,updator,updateDate);\r\n  }\r\n\r\n  function format(creator,createDate,updator,updateDate,realCreateDt){\r\n    if((!!realCreateDt ? realCreateDt : updateDate) != createDate){\r\n      return (\r\n        <>\r\n          <span className=\"fontcolor-light\">由{getUserNameById(userList, creator)}创建</span>\r\n          {/* tmsbug-3404:小于24小时显示时间差，大于24小时显示修改时间 */}\r\n          <span className=\"fontcolor-light\">{getUserNameById(userList, updator)}修改于\r\n            <span>\r\n              { checkDate((!!realCreateDt ? realCreateDt : updateDate), new DateUtil().getNow(DATE_FORMAT_MICRO), 1) ? \r\n              <span>{twoTimeInterval((!!realCreateDt ? realCreateDt : updateDate), new DateUtil().getNow(DATE_FORMAT_MICRO))}前</span>\r\n              : \r\n              (!!realCreateDt ? realCreateDt : updateDate)}\r\n            </span>\r\n          </span>\r\n        </>\r\n      );\r\n    }else{\r\n      return (\r\n        <>\r\n          <span className=\"fontcolor-light\">由{getUserNameById(userList, creator)}创建于{realCreateDt ? realCreateDt : createDate}</span>\r\n        </>\r\n      );\r\n    }\r\n  }\r\n\r\n  const prevAndNextClick = useThrottle((_nodeId) => {\r\n    navigate(`/team/${teamId}/docs/${nodeId}/doc/${_nodeId}`);\r\n  }, 500, [])\r\n\r\n  //复制链接地址\r\n  function copyLink() {\r\n    try {\r\n      const copyUrl = getUrl(teamId, {...nodeData, anchorNodeId: nodeId})\r\n      clipboard.writeText(copyUrl).then(() => {\r\n        globalUtil.success(\"链接已复制至剪贴板\")\r\n      }).catch((err) => {\r\n        globalUtil.error(\"链接复制失败\");\r\n        console.error(\"Failed to copy text: \", err);\r\n      });\r\n    } catch (e) {\r\n      globalUtil.success(\"链接复制失败\");\r\n    }\r\n  }\r\n\r\n  function refetchData () {\r\n    refetchGetDocDetail();\r\n    refetch202();\r\n    reftechTeam504GetCommentList({teamId, nodeId: key});\r\n    reftechGeneralFooter({teamId, nodeId: key, objId: objId, nodeType: nodeData.nodeType },);\r\n  } \r\n\r\n  function escapeHtml(value) {\r\n    return value\r\n        .replace(/&/g, '&amp;')\r\n        .replace(/\"/g, '&quot;')\r\n        .replace(/</g, '&lt;')\r\n        .replace(/>/g, '&gt;');\r\n  }\r\n\r\n  function readFileInputEventAsArrayBuffer(event, callback) {\r\n    var file = event.target.files[0];\r\n\r\n    var reader = new FileReader();\r\n\r\n    reader.onload = function(loadEvent) {\r\n        var arrayBuffer = loadEvent.target.result;\r\n        callback(arrayBuffer);\r\n    };\r\n\r\n    reader.readAsArrayBuffer(file);\r\n  }\r\n\r\n  function displayResult(result) {\r\n    document.querySelector(\".docread-body\").innerHTML = result.value;\r\n\r\n    var messageHtml = result.messages.map(function(message) {\r\n        return '<li class=\"' + message.type + '\">' + escapeHtml(message.message) + \"</li>\";\r\n    }).join(\"\");\r\n\r\n    document.querySelector(\".docread-comment\").innerHTML = \"<ul>\" + messageHtml + \"</ul>\";\r\n  }\r\n\r\n  function handleFileSelect(event) {\r\n    readFileInputEventAsArrayBuffer(event, function(arrayBuffer) {\r\n        mammoth.convertToHtml({arrayBuffer: arrayBuffer})\r\n            .then(displayResult, function(error) {\r\n                console.error(error);\r\n            });\r\n    });\r\n  }\r\n\r\n  // function initExportHtml() {\r\n    \r\n  // }\r\n\r\n  // 是否有编辑权限 当权限privWrite大于0则可以编辑\r\n  const editBtn = (setting320Result?.privWrite||0) > 0?<Button type=\"primary\" className=\"docread-header-title-right-btn\" onClick={onEditBtnClick}>编辑</Button>:<></>\r\n\r\n  // 分享\r\n  const shareBtn = (setting320Result?.privWrite||0) > 0?<Popover overlayClassName='sharePopver' destroyTooltipOnHide content={<SharePopover teamId={teamId} nodeId={key} />} title=\"外链分享\" trigger=\"click\">\r\n    <Button title=\"外链分享\" type=\"text\" icon={<span className=\"iconfont waibufenxiang fontsize-18\"></span>} />\r\n  </Popover>:<></>\r\n\r\n  const exportBtn = <Dropdown menu={{\r\n      items: [\r\n        { key: 'pdf', label: '导出为PDF' },\r\n        { key: 'word', label: '导出为Word' },\r\n        // { key: 'import', label: 'DOC文件导入' },\r\n      ],\r\n      onClick: async (info) => {\r\n        if(info.key === 'pdf') {\r\n          axios.get('/fonts/simhei.txt')\r\n            .then(result => result.data)\r\n            .then(font => {\r\n              // http://raw.githack.com/MrRio/jsPDF/master/docs/jsPDF.html\r\n              var doc = new jsPDF({\r\n                orientation: \"portrait\",\r\n                unit: \"px\",\r\n                format: \"a4\",\r\n                hotfixes: [\"px_scaling\"],\r\n              });\r\n\r\n              doc.addFileToVFS('Roboto-Regular-normal.ttf', font);\r\n              doc.addFont('Roboto-Regular-normal.ttf', 'Roboto', 'normal');\r\n              doc.setFont('Roboto', 'normal');\r\n              \r\n              let $ = jQuery;\r\n              let $html = $('.docread-body');\r\n\r\n              // let cloneHtml = $html.clone();\r\n              $html.find('*').each((index, el) => {\r\n                $(el).css('font-family', '');\r\n              })\r\n              \r\n              $html.width(doc.internal.pageSize.getWidth() - 60);\r\n\r\n              doc.html($html[0], {\r\n                margin: [10, 10],\r\n                callback: function (doc) {\r\n                  doc.save(`${docDetail.title}.pdf`);\r\n                  $html.width('100%');\r\n                },\r\n             });\r\n\r\n            })\r\n        } else if(info.key === 'word') {\r\n          let $ = jQuery;\r\n          let $html = $('.docread-body').find(\".fr-view\");\r\n\r\n          const htmlString = `<!DOCTYPE html>\r\n            <html lang=\"en\">\r\n              <head>\r\n                <meta charset=\"UTF-8\" />\r\n                <title>Document</title>\r\n              </head>\r\n              <body>\r\n                ${$html.prop('outerHTML')}\r\n              </body>\r\n            </html>`;\r\n          console.log(htmlString);\r\n          \r\n          let fileBuffer = await HTMLtoDOCX(htmlString, null, {\r\n            table: { row: { cantSplit: true } },\r\n            footer: true,\r\n            pageNumber: true,\r\n          })\r\n          saveAs(fileBuffer, `${docDetail.title}.docx`);\r\n        } /* else if(info.key === 'import') {\r\n          let input = document.createElement('input');\r\n          input.type = 'file';\r\n          input.addEventListener('change', handleFileSelect, false)\r\n          input.click();\r\n        } */\r\n      }\r\n    }}>\r\n    <Button type='link'>导出</Button>\r\n  </Dropdown>\r\n  \r\n\r\n  return <>\r\n    <div className=\"docread flex-column-parent\" style={{height: \"100%\"}}>\r\n      <>\r\n          {/* 版本历史查看 */}\r\n          {visibleVersionEdit ? (\r\n            <div className=\"mock-definition-bar-version\">\r\n              <Space size={20} className=\"ant-page-header-heading\">\r\n                <a onClick={() => backLastVerison()}>返回最新版本v{lastVersion}</a>\r\n                {/* 当前版本无需恢复该版本 */}\r\n                {visibleNextVerison && <a onClick={() => reverseCurrentVerison()}>恢复该版本</a>}\r\n                <a\r\n                  onClick={() => {\r\n                    getVerionList();\r\n                    visibleVersionEditClick(false);\r\n                    visibleVersionClick(true);\r\n                  }}\r\n                >\r\n                  查看版本历史\r\n                </a>\r\n              </Space>\r\n              <div className=\"mock-definition-bar-btns\">\r\n                <Space size={25}>\r\n                  {\r\n                    // 没有前一个版本，则不显示\r\n                    visibleBeforeVerison && (\r\n                      <Button\r\n                        className=\"ant-btn ant-btn-sm ant-btn-primary\"\r\n                        onClick={() => navigateVersion(true)}\r\n                      >\r\n                        前一个(v{beforeVersion})\r\n                      </Button>\r\n                    )\r\n                  }\r\n                  <span>v{currentVersion}</span>\r\n                  {\r\n                    visibleNextVerison && (\r\n                      <Button\r\n                        className=\"ant-btn ant-btn-sm ant-btn-primary\"\r\n                        onClick={() => navigateVersion(false)}\r\n                      >\r\n                        下一个(v{nextVersion})\r\n                      </Button>\r\n                    )\r\n                  }\r\n\r\n                </Space>\r\n              </div>\r\n            </div>\r\n          ) : null}\r\n\r\n          <div className=\"docread-header\">\r\n            <div className=\"docread-header-title\">\r\n              <PageTitle teamId={teamId} nodeId={key} powerLock powerShare={nodeData.nodeType == eNodeTypeId.nt_311_objtype_doc} refetchData={refetchData}/>\r\n              <div className=\"docread-header-title-right\">\r\n                <Space size={5}>\r\n                  {nodeData.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc && (tutorialList?.find(tutorial => tutorial.nodeId == docDetail?.objNodeId)?.prevNodeId ? <a style={{fontSize:12,marginRight:10}} onClick={()=>prevAndNextClick(tutorialList.find(tutorial => tutorial.nodeId == docDetail.objNodeId).prevNodeId)}>{'<上一篇'}</a> : <div style={{fontSize:12,marginRight:10,color:'#CCCCCC'}}>{'<上一篇'}</div>)}\r\n                  {nodeData.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc && (tutorialList?.find(tutorial => tutorial.nodeId == docDetail?.objNodeId)?.nextNodeId ? <a style={{fontSize:12,marginRight:10}} onClick={()=>prevAndNextClick(tutorialList.find(tutorial => tutorial.nodeId == docDetail.objNodeId).nextNodeId)}>{'下一篇>'}</a> : <div style={{fontSize:12,marginRight:10,color:'#CCCCCC'}}>{'下一篇>'}</div>)}\r\n                  {/* 编辑 */}\r\n                  {(docImportFlg != 1 && visibleVersionEdit) ? null : editBtn}\r\n                  {/* 分享 */}\r\n                  {shareBtn}\r\n                  {/* 导出 */}\r\n                  {exportBtn}\r\n                  <Button title=\"全屏查看\" type=\"text\" icon={<span className=\"iconfont quanping1 fontsize-16\"></span>} onClick={() => { fullScreenClick() }} />\r\n                  {/* <Dropdown overlay={moreMenuContent} placement=\"bottomLeft\" arrow> */}\r\n                    <Button title='版本历史' type=\"text\" icon={<span className=\"iconfont lishiguiji fontsize-16\"></span>} onClick={historyVersionClick} />\r\n                  {/* </Dropdown> */}\r\n                </Space>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"docread-header-des\">\r\n              <Space size={15}>\r\n                <a onClick={() => copyLink()}>{docNo}</a>\r\n                {describeFormat(creatorUid,createDt,updateUid,updateDt,adminCreateDt)}\r\n              </Space>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex-column-child\" id=\"docRead\">\r\n            <FullScreen\r\n              handle={handle}\r\n              onChange={setFull}\r\n              style={{ background: \"#ffffff\" }}>\r\n              <div className=\"docread-body\" style={{fontFamily: 'Roboto'}}>\r\n                {\r\n                  /* 全屏时显示退出全屏按钮 */\r\n                  full && <Tooltip title=\"退出全屏\" placement=\"bottom\">\r\n                    <FullscreenExitOutlined\r\n                      className=\"fullscreen-exit\"\r\n                      // 退出全屏模式并把full设置为false\r\n                      onClick={() => {\r\n                        setFull(false);\r\n                        handle.exit();\r\n                      }}\r\n                    />\r\n                  </Tooltip>\r\n                }\r\n                {/* refetchGetDocDetail: 文档任务编辑时调用 TMarkdownPreview*/}\r\n                {docDetail?.editorType === 1? <TMarkdownPreview content={docDetail?.content} isedit uploadParams={{teamId}} onRefresh={()=> refetchGetDocDetail()}/>:\r\n                  <TEditorPreview content={docDetail?.content} isedit uploadParams={{teamId}} onRefresh={()=> refetchGetDocDetail()}/>}\r\n              </div>\r\n            </FullScreen>\r\n            {/* objNodeId有值时显示 */}\r\n            {(objId && key) && <GeneralFooter objId={objId} objNodeId={key} objType={nodeData.nodeType} moduleName={eNodeType[nodeData.nodeType]?.nameEn} />}\r\n            {/* id 用于评论锚点，取值objNodeId */}\r\n            <div className=\"docread-comment\" id={`comment-${objNodeId}`}>\r\n              <CommonComment uploadParams={{ teamId, anchorNodeId: nodeId, nodeId: key, moduleName: eNodeType[nodeData.nodeType]?.nameEn, objType: nodeData.nodeType}} showEditorVisible={true}/>\r\n            </div>\r\n          </div>\r\n        </>\r\n      {/* 版本历史 */}\r\n      <DraggableDrawer\r\n        className=\"tms-drawer \"\r\n        width={\"65%\"}\r\n        onClose={() => visibleVersionClick(false)}\r\n        open={visibleVersion}\r\n        title={title||'' + (!!(title||'') ? ' - 版本历史' : '')}\r\n        closable={true}\r\n        footer={null}>\r\n        <Skeleton loading={isMutating}>\r\n          <DocHistory\r\n            setting320Result={setting320Result}\r\n            currentVersion={currentVersion}\r\n            versionChange={versionChange}\r\n            docVerionList={docVerionList}\r\n            restoreFromVersionIdConfirm={restoreFromVersionIdConfirm}\r\n          />\r\n        </Skeleton>\r\n      </DraggableDrawer>\r\n      {docDrawerVisibleFlg &&\r\n      <DocEditDrawer\r\n          objNode={objNode}\r\n          setDocDrawerVisibleFlg={setDocDrawerVisibleFlg}\r\n          onRefresh={()=> refetchGetDocDetail()} \r\n          visibleVersionEdit={visibleVersionEdit}\r\n          versionId={versionId}\r\n          _docImportFlg={docImportFlg}\r\n          docDrawerVisibleFlg={docDrawerVisibleFlg}/>\r\n      }\r\n    </div>\r\n    {/* 新手引导 */}\r\n    <NoviceGuide nodeType={nodeData.nodeType} awakeFlg={0}/>\r\n  </>\r\n}"], "mappings": ";;AAAA;AACA,SAASA,yBAAyB,EAAEC,sBAAsB,QAAQ,mBAAmB;AACrF,OAAOC,aAAa,MAAM,yCAAyC;AACnE,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,6BAA6B;AAC9E,SAASC,kCAAkC,QAAQ,6BAA6B;AAChF,SAASC,eAAe,EAAEC,OAAO,EAAEC,eAAe,EAAEC,SAAS,QAAS,0BAA0B;AAChG,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AACpH,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,UAAU,EAAEC,mBAAmB,QAAQ,mBAAmB;AACnE,SAASC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAACC,aAAa,QAAQ,kBAAkB;AAC9F,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAO,KAAKC,OAAO,MAAM,kBAAkB;AAC3C,SAASC,0BAA0B,EAAEC,+BAA+B,QAAQ,wBAAwB;AACpG,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,uBAAuB;AACzF,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAQ,uBAAuB;AAC9D,OAAO,gBAAgB;AACvB,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,+BAA+B,EAACC,+BAA+B,EAACC,4BAA4B,EAACC,gCAAgC,EAAEC,qCAAqC,QAAQ,yBAAyB;AAC9M,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,WAAW,EAAEC,SAAS,QAAQ,yBAAyB;AAChE,SAAQC,aAAa,QAAO,iBAAiB;AAC7C,OAAOC,WAAW,MAAM,yBAAyB,CAAC,CAAC;AACnD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAAQC,WAAW,QAAO,cAAc;AACxC,OAAO,KAAKC,SAAS,MAAM,oBAAoB;AAC/C,SAASC,MAAM,EAAEC,4BAA4B,EAAEC,oBAAoB,QAAQ,0BAA0B;AACrG,SAASC,KAAK,QAAQ,OAAO;AAC7B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,MAAM,QAAQ,YAAY;AACnC,OAAOC,OAAO,MAAM,SAAS;AAC7B,OAAOC,eAAe,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAM;EAAEC;AAAM,CAAC,GAAGtD,UAAU;;AAE5B;AACA,eAAe,SAASuD,OAAOA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAChC,MAAMC,QAAQ,GAAGrD,WAAW,CAAC,CAAC;EAC9B,MAAMsD,MAAM,GAAGrD,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEsD,MAAM;IAAEC,MAAM;IAAEC,GAAG,EAACC;EAAU,CAAC,GAAGJ,MAAM;EAChD,MAAMK,QAAQ,GAAG5D,WAAW,CAAC,CAAC;EAC9B;;EAEA,MAAM;IAAE6D,WAAW;IAAEC;EAAQ,CAAC,GAAGhC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC;;EAEzD;EACA;EACA;;EAEA,MAAMiC,GAAG,GAAGJ,SAAS,IAAIF,MAAM;EAE/B,MAAM,CAACO,cAAc,EAAEC,iBAAiB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrE,MAAM,CAACwE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzE,MAAM,CAAC0E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgF,aAAa,EAAEC,gBAAgB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACkF,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM,CAACoF,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACsF,SAAS,EAAEC,YAAY,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACwF,mBAAmB,EAACC,sBAAsB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACpE,MAAM,CAAC0F,OAAO,EAACC,UAAU,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC,CAAC;EACzC,MAAM,CAAC4F,YAAY,EAACC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;;EAEnD;EACA,MAAM,CAAC8F,IAAI,EAAEC,OAAO,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACvC;EACA,MAAMgG,MAAM,GAAG9F,mBAAmB,CAAC,CAAC;EAEpC,MAAM;IAAE+F,IAAI,EAAEC,QAAQ;IAAEC,SAAS,EAAEC,qBAAqB;IAAEC,OAAO,EAAEC;EAAW,CAAC,GAAG1H,kCAAkC,CAACgF,MAAM,CAAC;EAC5H;;EAEA,MAAM;IAACqC,IAAI,EAAEM,gBAAgB;IAAEJ,SAAS,EAAEK,oBAAoB;IAAEH,OAAO,EAAEI;EAAkB,CAAC,GAAGzF,QAAQ,CAAC;IACtG,GAAGM,+BAA+B,CAACsC,MAAM,EAACO,GAAG,EAAC,CAAC,CAACA,GAAG;EACrD,CAAC,CAAC;EAEF,MAAM;IAAC8B,IAAI,EAAES,QAAQ,GAAG,CAAC,CAAC;IAAEP,SAAS,EAAEQ,uBAAuB;IAAEN,OAAO,EAAEO;EAAqB,CAAC,GAAG5F,QAAQ,CAAC;IACzG,GAAGS,gCAAgC,CAACmC,MAAM,EAACO,GAAG;EAChD,CAAC,CAAC;EAEF,MAAM;IAAE8B,IAAI,EAAG;MAAEY,UAAU;MAAEC,WAAW;MAAEC;IAAQ,CAAC,GAAG;MAAEF,UAAU,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAG,CAAC;IAAEE,aAAa,EAAEC,mBAAmB;IAAEZ,OAAO,EAAEa;EAAuB,CAAC,GAAGlG,QAAQ,CAAC;IAC5K,GAAGO,+BAA+B,CAACqC,MAAM,EAAEC,MAAM,EAAEI,WAAW,EAAE,CAAC,CAACF,SAAS,IAAI,CAACG,OAAO;EACzF,CAAC,CAAC;EACF,MAAM;IAAC+B,IAAI,EAAE;MAACkB;IAAS,CAAC,GAAE,CAAC,CAAC;IAAEhB,SAAS,EAAEiB,qBAAqB;IAAEf,OAAO,EAAEgB;EAAoB,CAAC,GAAGrG,QAAQ,CAAC;IACxG,GAAGQ,4BAA4B,CAACoC,MAAM,EAAEO,GAAG,EAAEmB,SAAS;EACxD,CAAC,CAAC;EAEF,MAAM;IAAEW,IAAI,EAAE;MAACqB;IAAY,CAAC,GAAG;MAACA,YAAY,EAAE;IAAC;EAAE,CAAC,GAAGtG,QAAQ,CAAC;IAAC,GAAGU,qCAAqC,CAACkC,MAAM,EAAEC,MAAM;EAAC,CAAC,CAAC;EAEzH,MAAM;IAAE0D,sBAAsB;IAAEC;EAAW,CAAC,GAAG5G,+BAA+B,CAAC,CAAC;;EAEhF;EACA,MAAM;IAAE6G,EAAE,EAAEC,KAAK;IAAEC,KAAK;IAAEC,OAAO;IAAEC,UAAU;IAAEC,eAAe;IAAEC,SAAS;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGhB,SAAS,IAAI,CAAC,CAAC;EAEvIrH,SAAS,CAAC,MAAI;IACZ,IAAGmH,mBAAmB,EAAC;MACrB,IAAImB,QAAQ,GAAGC,aAAa,CAACxB,UAAU,CAACyB,QAAQ,IAAI,EAAE,EAAE,EAAE,CAAC;MAC3DzC,eAAe,CAACuC,QAAQ,CAAC;IAC3B;EACF,CAAC,EAAC,CAACnB,mBAAmB,CAAC,CAAC;EAExB,SAASoB,aAAaA,CAACE,QAAQ,EAACC,IAAI,EAAC;IACnCD,QAAQ,CAACE,OAAO,CAACC,GAAG,IAAI;MACtB,IAAGA,GAAG,CAACJ,QAAQ,CAACK,MAAM,GAAG,CAAC,EAAC;QACzB,OAAON,aAAa,CAACK,GAAG,CAACJ,QAAQ,EAACE,IAAI,CAAC;MACzC,CAAC,MAAI;QACH,IAAGE,GAAG,CAACE,QAAQ,IAAI9G,WAAW,CAAC+G,yBAAyB,IAAIH,GAAG,CAACE,QAAQ,IAAI9G,WAAW,CAACgH,2BAA2B,EAAC;UAClHN,IAAI,CAACO,IAAI,CAACL,GAAG,CAAC;QAChB;MACF;IACF,CAAC,CAAC;IACF,OAAOF,IAAI;EACb;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA;IACA;IACA;IACA;IACA,IAAIjC,OAAO,GAAGI,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEJ,OAAO,GAAGI,SAAS,CAACJ,OAAO,GAAG,CAAC,GAAG;IAC3DpB,UAAU,CAAC,CAAC7G,OAAO,CAACiF,SAAS,CAAC,GAAG;MAACkF,IAAI,EAAEpF,MAAM;MAAEE,SAAS,EAAEA,SAAS;MAAEmF,MAAM,EAAEjK,cAAc,CAACkK,QAAQ;MAAEpC,OAAO,EAAEA;IAAO,CAAC,GAAG;MAAChD,SAAS,EAAEF,MAAM;MAAEqF,MAAM,EAAEjK,cAAc,CAACkK,QAAQ;MAAEpC,OAAO,EAAEA;IAAO,CAAC,CAAC;IAClMtB,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM2D,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,OAAO,GAAG;MACdzF,MAAM,EAAEA,MAAM;MACdG,SAAS,EAAEI;IACb,CAAC;IACDoD,sBAAsB,CAAC8B,OAAO,EAAE;MAC9BC,SAAS,EAAEA,CAACC,GAAG,EAAEC,IAAI,KAAK;QACtB,IAAID,GAAG,CAACE,UAAU,IAAI,GAAG,EAAE;UACzB,IAAIxD,IAAI,GAAGsD,GAAG,CAACG,WAAW;UAC1BC,iBAAiB,CAAC1D,IAAI,CAAC;UACvB,IAAInB,WAAW,GAAG,CAAChG,OAAO,CAACmH,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC2D,SAAS,GAAG,GAAG;UAC1D7E,cAAc,CAACD,WAAW,CAAC,CAAC,CAAC;UAC7BO,gBAAgB,CAACY,IAAI,CAAC;QACxB;MACJ,CAAC;MACD4D,OAAO,EAAGC,KAAK,IAAK;QAChBC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMH,iBAAiB,GAAI1D,IAAI,IAAK;IAClCA,IAAI,CAACwC,OAAO,CAACwB,IAAI,IAAI;MACnBA,IAAI,CAAC9F,GAAG,GAAG8F,IAAI,CAAC3E,SAAS;MACzB2E,IAAI,CAACC,QAAQ,GAAGrL,eAAe,CAACqH,QAAQ,EAAE+D,IAAI,CAACpC,UAAU,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMsC,mBAAmB,GAAGA,CAAA,KAAM;IAChCf,aAAa,CAAC,CAAC;IACfgB,uBAAuB,CAAC,KAAK,CAAC;IAC9BC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,SAASC,aAAaA,CAAC1F,cAAc,EAAEqF,IAAI,EAAE;IAC3C;IACA1F,qBAAqB,CAAC,IAAI,CAAC;IAC3BgG,iBAAiB,CAAC3F,cAAc,CAAC;IACjC;IACAyF,mBAAmB,CAAC,KAAK,CAAC;IAC1B9E,YAAY,CAAC0E,IAAI,CAAC3E,SAAS,CAAC;EAC9B;;EAEA;EACA,SAASiF,iBAAiBA,CAAC3F,cAAc,EAAE;IACzC,MAAM4F,UAAU,GAAGpF,aAAa;IAChC,MAAMqF,mBAAmB,GAAGD,UAAU,CAACE,SAAS,CAC7CT,IAAI,IAAKA,IAAI,CAACL,SAAS,KAAKhF,cAC/B,CAAC;IACD;IACA,IAAIJ,oBAAoB,GAAGiG,mBAAmB,KAAKD,UAAU,CAAC7B,MAAM,GAAG,CAAC;IACxE;IACA,IAAIjE,kBAAkB,GAAG+F,mBAAmB,KAAK,CAAC;IAClD,IAAIzF,aAAa,GAAGR,oBAAoB,GAAGgG,UAAU,CAACC,mBAAmB,GAAG,CAAC,CAAC,CAACb,SAAS,GAAG,EAAE;IAC7F;IACA,IAAI1E,WAAW,GAAGuF,mBAAmB,GAAG,CAAC,KAAK,CAAC,GAC3CD,UAAU,CAACC,mBAAmB,GAAG,CAAC,CAAC,CAACb,SAAS,GAAG,KAAK,GACrDa,mBAAmB,GAAG,CAAC,GAAG,CAAC,GACzB,EAAE,GACFD,UAAU,CAACC,mBAAmB,GAAG,CAAC,CAAC,CAACb,SAAS;IACnD;IACA;AACJ;AACA;IACI/E,iBAAiB,CAACD,cAAc,CAAC;IACjCH,uBAAuB,CAACD,oBAAoB,CAAC;IAC7CG,qBAAqB,CAACD,kBAAkB,CAAC;IACzCO,gBAAgB,CAACD,aAAa,CAAC;IAC/BG,cAAc,CAACD,WAAW,CAAC;EAC7B;;EAEA;EACA,SAASkF,uBAAuBA,CAACnE,IAAI,EAAE;IACrC1B,qBAAqB,CAAC0B,IAAI,CAAC;EAC7B;;EAEA;EACA,SAASoE,mBAAmBA,CAACpE,IAAI,EAAE;IACjC5B,iBAAiB,CAAC4B,IAAI,CAAC;EACzB;;EAEA;EACA,SAAS0E,eAAeA,CAAA,EAAG;IACzB9F,iBAAiB,CAACC,WAAW,CAAC;IAC9BsF,uBAAuB,CAAC,KAAK,CAAC;IAC9B7E,YAAY,CAAC,IAAI,CAAC;EACpB;;EAEA;EACA,SAASqF,qBAAqBA,CAAA,EAAG;IAC/B,MAAMtF,SAAS,GAAGuF,aAAa,CAAC,CAAC;IACjCC,2BAA2B,CAACxF,SAAS,CAAC;EACxC;;EAEA;EACA,MAAMwF,2BAA2B,GAAIxF,SAAS,IAAK;IACjD,MAAMyF,IAAI,GAAG,WAAW;IACxBvL,KAAK,CAACwL,OAAO,CAAC;MACZrD,KAAK,EAAE,IAAI;MACXsD,IAAI,eAAEjI,OAAA,CAACzE,yBAAyB;QAAA2M,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCzD,OAAO,EAAEmD,IAAI;MACbO,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEA,CAAA,KAAM;QAAEC,oBAAoB,CAACnG,SAAS,CAAC;MAAC;IAChD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA;EACA,MAAMmG,oBAAoB,GAAInG,SAAS,IAAK;IAC1C,IAAI+D,OAAO,GAAG;MACZzF,MAAM,EAAEA,MAAM;MACd8H,gBAAgB,EAAEpG,SAAS;MAC3BvB,SAAS,EAAEI;IACb,CAAC;IACDzD,OAAO,CAACiL,gCAAgC,CAACtC,OAAO,CAAC,CAACuC,IAAI,CAAErC,GAAG,IAAK;MAC9D,IAAIA,GAAG,CAACE,UAAU,IAAI,GAAG,EAAE;QACzB;QACA;QACA;QACApI,UAAU,CAACwK,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9BnI,QAAQ,CAAC;UACPoI,QAAQ,EAAE9H,QAAQ,CAAC8H,QAAQ;UAC3BC,MAAM,EAAE/H,QAAQ,CAAC+H,MAAM;UACvBC,IAAI,EAAEhI,QAAQ,CAACgI;QACjB,CAAC,EAAC;UACAC,KAAK,EAAE;YACLC,OAAO,EAAE;UACX;QACF,CAAC,CAAC;MAEJ;IACF,CAAC,CAAC,CAACC,KAAK,CAACC,CAAC,IAAI;MACZrC,OAAO,CAACsC,IAAI,CAACD,CAAC,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,SAASE,eAAeA,CAACC,QAAQ,EAAE;IACjC,IAAI9B,mBAAmB,GAAGrF,aAAa,CAACsF,SAAS,CAC9CT,IAAI,IAAKA,IAAI,CAACL,SAAS,KAAKhF,cAC/B,CAAC;IACD,IAAIqB,IAAI,GAAG,CAAC,CAAC;IACb,IAAIsG,QAAQ,EAAE;MACZtG,IAAI,GAAGb,aAAa,CAAC,EAAEqF,mBAAmB,CAAC;IAC7C,CAAC,MAAM;MACLxE,IAAI,GAAGb,aAAa,CAAC,EAAEqF,mBAAmB,CAAC;IAC7C;IACA,MAAM;MAAEnF,SAAS;MAAEsE;IAAU,CAAC,GAAG3D,IAAI;IACrCV,YAAY,CAACD,SAAS,CAAC;IACvBiF,iBAAiB,CAACX,SAAS,CAAC;EAC9B;;EAEA;EACA,MAAMiB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIJ,mBAAmB,GAAGrF,aAAa,CAACsF,SAAS,CAAET,IAAI,IAAKA,IAAI,CAACL,SAAS,KAAKhF,cAAc,CAAC;IAC9F,MAAM;MAAEU,SAAS;MAAEsE;IAAU,CAAC,GAAGxE,aAAa,CAACqF,mBAAmB,CAAC;IACnE,OAAOnF,SAAS;EAClB,CAAC;;EAED;EACA,MAAMkH,eAAe,GAAGA,CAAA,KAAM;IAC5BzG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAE;IAChBC,MAAM,CAACyG,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,SAASC,cAAcA,CAACC,OAAO,EAACC,UAAU,EAACC,OAAO,EAACC,UAAU,EAACC,YAAY,EAAC;IACzE,IAAG,CAAC,CAACA,YAAY,EAAC;MAChB,OAAOC,MAAM,CAACL,OAAO,EAACC,UAAU,EAACC,OAAO,EAACC,UAAU,EAACC,YAAY,CAAC;IACnE;IACA,OAAOC,MAAM,CAACL,OAAO,EAACC,UAAU,EAACC,OAAO,EAACC,UAAU,CAAC;EACtD;EAEA,SAASE,MAAMA,CAACL,OAAO,EAACC,UAAU,EAACC,OAAO,EAACC,UAAU,EAACC,YAAY,EAAC;IACjE,IAAG,CAAC,CAAC,CAACA,YAAY,GAAGA,YAAY,GAAGD,UAAU,KAAKF,UAAU,EAAC;MAC5D,oBACE5J,OAAA,CAAAE,SAAA;QAAAoF,QAAA,gBACEtF,OAAA;UAAMiK,SAAS,EAAC,iBAAiB;UAAA3E,QAAA,GAAC,QAAC,EAACzJ,eAAe,CAACqH,QAAQ,EAAEyG,OAAO,CAAC,EAAC,cAAE;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEhFrI,OAAA;UAAMiK,SAAS,EAAC,iBAAiB;UAAA3E,QAAA,GAAEzJ,eAAe,CAACqH,QAAQ,EAAE2G,OAAO,CAAC,EAAC,oBACpE,eAAA7J,OAAA;YAAAsF,QAAA,EACItJ,SAAS,CAAE,CAAC,CAAC+N,YAAY,GAAGA,YAAY,GAAGD,UAAU,EAAG,IAAIlL,QAAQ,CAAC,CAAC,CAACsL,MAAM,CAACvL,iBAAiB,CAAC,EAAE,CAAC,CAAC,gBACtGqB,OAAA;cAAAsF,QAAA,GAAOvJ,eAAe,CAAE,CAAC,CAACgO,YAAY,GAAGA,YAAY,GAAGD,UAAU,EAAG,IAAIlL,QAAQ,CAAC,CAAC,CAACsL,MAAM,CAACvL,iBAAiB,CAAC,CAAC,EAAC,QAAC;YAAA;cAAAuJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,GAEtH,CAAC,CAAC0B,YAAY,GAAGA,YAAY,GAAGD;UAAW;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACP,CAAC;IAEP,CAAC,MAAI;MACH,oBACErI,OAAA,CAAAE,SAAA;QAAAoF,QAAA,eACEtF,OAAA;UAAMiK,SAAS,EAAC,iBAAiB;UAAA3E,QAAA,GAAC,QAAC,EAACzJ,eAAe,CAACqH,QAAQ,EAAEyG,OAAO,CAAC,EAAC,oBAAG,EAACI,YAAY,GAAGA,YAAY,GAAGH,UAAU;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC,gBAC3H,CAAC;IAEP;EACF;EAEA,MAAM8B,gBAAgB,GAAGhL,WAAW,CAAEiL,OAAO,IAAK;IAChD1J,QAAQ,CAAC,SAASE,MAAM,SAASC,MAAM,QAAQuJ,OAAO,EAAE,CAAC;EAC3D,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;;EAEX;EACA,SAASC,QAAQA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMC,OAAO,GAAGjL,MAAM,CAACuB,MAAM,EAAE;QAAC,GAAG8C,QAAQ;QAAE6G,YAAY,EAAE1J;MAAM,CAAC,CAAC;MACnEzB,SAAS,CAACoL,SAAS,CAACF,OAAO,CAAC,CAAC1B,IAAI,CAAC,MAAM;QACtCvK,UAAU,CAACwK,OAAO,CAAC,WAAW,CAAC;MACjC,CAAC,CAAC,CAACM,KAAK,CAAEsB,GAAG,IAAK;QAChBpM,UAAU,CAACyI,KAAK,CAAC,QAAQ,CAAC;QAC1BC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAE2D,GAAG,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOrB,CAAC,EAAE;MACV/K,UAAU,CAACwK,OAAO,CAAC,QAAQ,CAAC;IAC9B;EACF;EAEA,SAAS6B,WAAWA,CAAA,EAAI;IACtBrG,mBAAmB,CAAC,CAAC;IACrBf,UAAU,CAAC,CAAC;IACZhE,4BAA4B,CAAC;MAACsB,MAAM;MAAEC,MAAM,EAAEM;IAAG,CAAC,CAAC;IACnD5B,oBAAoB,CAAC;MAACqB,MAAM;MAAEC,MAAM,EAAEM,GAAG;MAAEuD,KAAK,EAAEA,KAAK;MAAEkB,QAAQ,EAAElC,QAAQ,CAACkC;IAAS,CAAE,CAAC;EAC1F;EAEA,SAAS+E,UAAUA,CAACC,KAAK,EAAE;IACzB,OAAOA,KAAK,CACPC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CACvBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;EAC5B;EAEA,SAASC,+BAA+BA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACxD,IAAIC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAEhC,IAAIC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAE7BD,MAAM,CAACE,MAAM,GAAG,UAASC,SAAS,EAAE;MAChC,IAAIC,WAAW,GAAGD,SAAS,CAACL,MAAM,CAACO,MAAM;MACzCT,QAAQ,CAACQ,WAAW,CAAC;IACzB,CAAC;IAEDJ,MAAM,CAACM,iBAAiB,CAACT,IAAI,CAAC;EAChC;EAEA,SAASU,aAAaA,CAACF,MAAM,EAAE;IAC7BG,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAC,CAACC,SAAS,GAAGL,MAAM,CAACb,KAAK;IAEhE,IAAImB,WAAW,GAAGN,MAAM,CAACO,QAAQ,CAACC,GAAG,CAAC,UAAS1P,OAAO,EAAE;MACpD,OAAO,aAAa,GAAGA,OAAO,CAAC2P,IAAI,GAAG,IAAI,GAAGvB,UAAU,CAACpO,OAAO,CAACA,OAAO,CAAC,GAAG,OAAO;IACtF,CAAC,CAAC,CAAC4P,IAAI,CAAC,EAAE,CAAC;IAEXP,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC,CAACC,SAAS,GAAG,MAAM,GAAGC,WAAW,GAAG,OAAO;EACvF;EAEA,SAASK,gBAAgBA,CAACrB,KAAK,EAAE;IAC/BD,+BAA+B,CAACC,KAAK,EAAE,UAASS,WAAW,EAAE;MACzD3L,OAAO,CAACwM,aAAa,CAAC;QAACb,WAAW,EAAEA;MAAW,CAAC,CAAC,CAC5C5C,IAAI,CAAC+C,aAAa,EAAE,UAAS7E,KAAK,EAAE;QACjCC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;MACxB,CAAC,CAAC;IACV,CAAC,CAAC;EACJ;;EAEA;;EAEA;;EAEA;EACA,MAAMwF,OAAO,GAAG,CAAC,CAAA/I,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgJ,SAAS,KAAE,CAAC,IAAI,CAAC,gBAACvM,OAAA,CAAC7D,MAAM;IAAC+P,IAAI,EAAC,SAAS;IAACjC,SAAS,EAAC,gCAAgC;IAACuC,OAAO,EAAExG,cAAe;IAAAV,QAAA,EAAC;EAAE;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC,gBAACrI,OAAA,CAAAE,SAAA,mBAAI,CAAC;;EAEjK;EACA,MAAMuM,QAAQ,GAAG,CAAC,CAAAlJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgJ,SAAS,KAAE,CAAC,IAAI,CAAC,gBAACvM,OAAA,CAACvD,OAAO;IAACiQ,gBAAgB,EAAC,aAAa;IAACC,oBAAoB;IAAC/H,OAAO,eAAE5E,OAAA,CAACvC,YAAY;MAACmD,MAAM,EAAEA,MAAO;MAACC,MAAM,EAAEM;IAAI;MAAA+G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;IAAC1D,KAAK,EAAC,0BAAM;IAACiI,OAAO,EAAC,OAAO;IAAAtH,QAAA,eACrMtF,OAAA,CAAC7D,MAAM;MAACwI,KAAK,EAAC,0BAAM;MAACuH,IAAI,EAAC,MAAM;MAACjE,IAAI,eAAEjI,OAAA;QAAMiK,SAAS,EAAC;MAAoC;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChG,CAAC,gBAACrI,OAAA,CAAAE,SAAA,mBAAI,CAAC;EAEhB,MAAM2M,SAAS,gBAAG7M,OAAA,CAAC3D,QAAQ;IAACyQ,IAAI,EAAE;MAC9BC,KAAK,EAAE,CACL;QAAE5L,GAAG,EAAE,KAAK;QAAE6L,KAAK,EAAE;MAAS,CAAC,EAC/B;QAAE7L,GAAG,EAAE,MAAM;QAAE6L,KAAK,EAAE;MAAU;MAChC;MAAA,CACD;MACDR,OAAO,EAAE,MAAOS,IAAI,IAAK;QACvB,IAAGA,IAAI,CAAC9L,GAAG,KAAK,KAAK,EAAE;UACrBzB,KAAK,CAACwN,GAAG,CAAC,mBAAmB,CAAC,CAC3BtE,IAAI,CAAC6C,MAAM,IAAIA,MAAM,CAACxI,IAAI,CAAC,CAC3B2F,IAAI,CAACuE,IAAI,IAAI;YACZ;YACA,IAAIzH,GAAG,GAAG,IAAIlG,KAAK,CAAC;cAClB4N,WAAW,EAAE,UAAU;cACvBC,IAAI,EAAE,IAAI;cACVrD,MAAM,EAAE,IAAI;cACZsD,QAAQ,EAAE,CAAC,YAAY;YACzB,CAAC,CAAC;YAEF5H,GAAG,CAAC6H,YAAY,CAAC,2BAA2B,EAAEJ,IAAI,CAAC;YACnDzH,GAAG,CAAC8H,OAAO,CAAC,2BAA2B,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC5D9H,GAAG,CAAC+H,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAE/B,IAAIC,CAAC,GAAG/N,MAAM;YACd,IAAIgO,KAAK,GAAGD,CAAC,CAAC,eAAe,CAAC;;YAE9B;YACAC,KAAK,CAACC,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAACC,KAAK,EAAEC,EAAE,KAAK;cAClCL,CAAC,CAACK,EAAE,CAAC,CAACC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC;YAC9B,CAAC,CAAC;YAEFL,KAAK,CAACM,KAAK,CAACvI,GAAG,CAACwI,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;YAElD1I,GAAG,CAAC2I,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC,EAAE;cACjBW,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;cAChBtD,QAAQ,EAAE,SAAAA,CAAUtF,GAAG,EAAE;gBACvBA,GAAG,CAAC6I,IAAI,CAAC,GAAGpK,SAAS,CAACQ,KAAK,MAAM,CAAC;gBAClCgJ,KAAK,CAACM,KAAK,CAAC,MAAM,CAAC;cACrB;YACH,CAAC,CAAC;UAEH,CAAC,CAAC;QACN,CAAC,MAAM,IAAGhB,IAAI,CAAC9L,GAAG,KAAK,MAAM,EAAE;UAC7B,IAAIuM,CAAC,GAAG/N,MAAM;UACd,IAAIgO,KAAK,GAAGD,CAAC,CAAC,eAAe,CAAC,CAACE,IAAI,CAAC,UAAU,CAAC;UAE/C,MAAMY,UAAU,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBb,KAAK,CAACc,IAAI,CAAC,WAAW,CAAC;AACzC;AACA,oBAAoB;UACV1H,OAAO,CAACC,GAAG,CAACwH,UAAU,CAAC;UAEvB,IAAIE,UAAU,GAAG,MAAMjP,UAAU,CAAC+O,UAAU,EAAE,IAAI,EAAE;YAClDG,KAAK,EAAE;cAAEC,GAAG,EAAE;gBAAEC,SAAS,EAAE;cAAK;YAAE,CAAC;YACnCC,MAAM,EAAE,IAAI;YACZC,UAAU,EAAE;UACd,CAAC,CAAC;UACFnP,MAAM,CAAC8O,UAAU,EAAE,GAAGvK,SAAS,CAACQ,KAAK,OAAO,CAAC;QAC/C,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;MACM;IACF,CAAE;IAAAW,QAAA,eACFtF,OAAA,CAAC7D,MAAM;MAAC+P,IAAI,EAAC,MAAM;MAAA5G,QAAA,EAAC;IAAE;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;EAGX,oBAAOrI,OAAA,CAAAE,SAAA;IAAAoF,QAAA,gBACLtF,OAAA;MAAKiK,SAAS,EAAC,4BAA4B;MAAC+E,KAAK,EAAE;QAACC,MAAM,EAAE;MAAM,CAAE;MAAA3J,QAAA,gBAClEtF,OAAA,CAAAE,SAAA;QAAAoF,QAAA,GAEKhE,kBAAkB,gBACjBtB,OAAA;UAAKiK,SAAS,EAAC,6BAA6B;UAAA3E,QAAA,gBAC1CtF,OAAA,CAACrD,KAAK;YAACuS,IAAI,EAAE,EAAG;YAACjF,SAAS,EAAC,yBAAyB;YAAA3E,QAAA,gBAClDtF,OAAA;cAAGwM,OAAO,EAAEA,CAAA,KAAM7E,eAAe,CAAC,CAAE;cAAArC,QAAA,GAAC,uCAAO,EAACxD,WAAW;YAAA;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAE5D3G,kBAAkB,iBAAI1B,OAAA;cAAGwM,OAAO,EAAEA,CAAA,KAAM5E,qBAAqB,CAAC,CAAE;cAAAtC,QAAA,EAAC;YAAK;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3ErI,OAAA;cACEwM,OAAO,EAAEA,CAAA,KAAM;gBACbpG,aAAa,CAAC,CAAC;gBACfgB,uBAAuB,CAAC,KAAK,CAAC;gBAC9BC,mBAAmB,CAAC,IAAI,CAAC;cAC3B,CAAE;cAAA/B,QAAA,EACH;YAED;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACRrI,OAAA;YAAKiK,SAAS,EAAC,0BAA0B;YAAA3E,QAAA,eACvCtF,OAAA,CAACrD,KAAK;cAACuS,IAAI,EAAE,EAAG;cAAA5J,QAAA;cAEZ;cACA9D,oBAAoB,iBAClBxB,OAAA,CAAC7D,MAAM;gBACL8N,SAAS,EAAC,oCAAoC;gBAC9CuC,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAAC,IAAI,CAAE;gBAAAhE,QAAA,GACtC,sBACM,EAACtD,aAAa,EAAC,GACtB;cAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,eAEHrI,OAAA;gBAAAsF,QAAA,GAAM,GAAC,EAAC1D,cAAc;cAAA;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAE5B3G,kBAAkB,iBAChB1B,OAAA,CAAC7D,MAAM;gBACL8N,SAAS,EAAC,oCAAoC;gBAC9CuC,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAAC,KAAK,CAAE;gBAAAhE,QAAA,GACvC,sBACM,EAACpD,WAAW,EAAC,GACpB;cAAA;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ,IAAI,eAERrI,OAAA;UAAKiK,SAAS,EAAC,gBAAgB;UAAA3E,QAAA,gBAC7BtF,OAAA;YAAKiK,SAAS,EAAC,sBAAsB;YAAA3E,QAAA,gBACnCtF,OAAA,CAACnB,SAAS;cAAC+B,MAAM,EAAEA,MAAO;cAACC,MAAM,EAAEM,GAAI;cAACgO,SAAS;cAACC,UAAU,EAAE1L,QAAQ,CAACkC,QAAQ,IAAI9G,WAAW,CAACuQ,kBAAmB;cAAC3E,WAAW,EAAEA;YAAY;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC9IrI,OAAA;cAAKiK,SAAS,EAAC,4BAA4B;cAAA3E,QAAA,eACzCtF,OAAA,CAACrD,KAAK;gBAACuS,IAAI,EAAE,CAAE;gBAAA5J,QAAA,GACZ5B,QAAQ,CAACkC,QAAQ,IAAI9G,WAAW,CAAC+G,yBAAyB,KAAKjD,YAAY,aAAZA,YAAY,gBAAAtC,kBAAA,GAAZsC,YAAY,CAAEgL,IAAI,CAAC0B,QAAQ,IAAIA,QAAQ,CAACzO,MAAM,KAAIsD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEpD,SAAS,EAAC,cAAAT,kBAAA,eAAvEA,kBAAA,CAAyEiP,UAAU,gBAAGvP,OAAA;kBAAGgP,KAAK,EAAE;oBAACQ,QAAQ,EAAC,EAAE;oBAACC,WAAW,EAAC;kBAAE,CAAE;kBAACjD,OAAO,EAAEA,CAAA,KAAIrC,gBAAgB,CAACvH,YAAY,CAACgL,IAAI,CAAC0B,QAAQ,IAAIA,QAAQ,CAACzO,MAAM,IAAIsD,SAAS,CAACpD,SAAS,CAAC,CAACwO,UAAU,CAAE;kBAAAjK,QAAA,EAAE;gBAAM;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gBAAGrI,OAAA;kBAAKgP,KAAK,EAAE;oBAACQ,QAAQ,EAAC,EAAE;oBAACC,WAAW,EAAC,EAAE;oBAACC,KAAK,EAAC;kBAAS,CAAE;kBAAApK,QAAA,EAAE;gBAAM;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,CAAC,EACtY3E,QAAQ,CAACkC,QAAQ,IAAI9G,WAAW,CAAC+G,yBAAyB,KAAKjD,YAAY,aAAZA,YAAY,gBAAArC,mBAAA,GAAZqC,YAAY,CAAEgL,IAAI,CAAC0B,QAAQ,IAAIA,QAAQ,CAACzO,MAAM,KAAIsD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEpD,SAAS,EAAC,cAAAR,mBAAA,eAAvEA,mBAAA,CAAyEoP,UAAU,gBAAG3P,OAAA;kBAAGgP,KAAK,EAAE;oBAACQ,QAAQ,EAAC,EAAE;oBAACC,WAAW,EAAC;kBAAE,CAAE;kBAACjD,OAAO,EAAEA,CAAA,KAAIrC,gBAAgB,CAACvH,YAAY,CAACgL,IAAI,CAAC0B,QAAQ,IAAIA,QAAQ,CAACzO,MAAM,IAAIsD,SAAS,CAACpD,SAAS,CAAC,CAAC4O,UAAU,CAAE;kBAAArK,QAAA,EAAE;gBAAM;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gBAAGrI,OAAA;kBAAKgP,KAAK,EAAE;oBAACQ,QAAQ,EAAC,EAAE;oBAACC,WAAW,EAAC,EAAE;oBAACC,KAAK,EAAC;kBAAS,CAAE;kBAAApK,QAAA,EAAE;gBAAM;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,CAAC,EAErY/D,YAAY,IAAI,CAAC,IAAIhD,kBAAkB,GAAI,IAAI,GAAGgL,OAAO,EAE1DG,QAAQ,EAERI,SAAS,eACV7M,OAAA,CAAC7D,MAAM;kBAACwI,KAAK,EAAC,0BAAM;kBAACuH,IAAI,EAAC,MAAM;kBAACjE,IAAI,eAAEjI,OAAA;oBAAMiK,SAAS,EAAC;kBAAgC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAE;kBAACmE,OAAO,EAAEA,CAAA,KAAM;oBAAEhD,eAAe,CAAC,CAAC;kBAAC;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEvIrI,OAAA,CAAC7D,MAAM;kBAACwI,KAAK,EAAC,0BAAM;kBAACuH,IAAI,EAAC,MAAM;kBAACjE,IAAI,eAAEjI,OAAA;oBAAMiK,SAAS,EAAC;kBAAiC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAE;kBAACmE,OAAO,EAAErF;gBAAoB;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE/H;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrI,OAAA;YAAKiK,SAAS,EAAC,oBAAoB;YAAA3E,QAAA,eACjCtF,OAAA,CAACrD,KAAK;cAACuS,IAAI,EAAE,EAAG;cAAA5J,QAAA,gBACdtF,OAAA;gBAAGwM,OAAO,EAAEA,CAAA,KAAMnC,QAAQ,CAAC,CAAE;gBAAA/E,QAAA,EAAEH;cAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACxCqB,cAAc,CAAC7E,UAAU,EAACG,QAAQ,EAACD,SAAS,EAACG,QAAQ,EAACD,aAAa,CAAC;YAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrI,OAAA;UAAKiK,SAAS,EAAC,mBAAmB;UAACxF,EAAE,EAAC,SAAS;UAAAa,QAAA,gBAC7CtF,OAAA,CAAC/C,UAAU;YACT+F,MAAM,EAAEA,MAAO;YACf4M,QAAQ,EAAE7M,OAAQ;YAClBiM,KAAK,EAAE;cAAEa,UAAU,EAAE;YAAU,CAAE;YAAAvK,QAAA,eACjCtF,OAAA;cAAKiK,SAAS,EAAC,cAAc;cAAC+E,KAAK,EAAE;gBAACc,UAAU,EAAE;cAAQ,CAAE;cAAAxK,QAAA,GAExD;cACAxC,IAAI,iBAAI9C,OAAA,CAACpD,OAAO;gBAAC+H,KAAK,EAAC,0BAAM;gBAACoL,SAAS,EAAC,QAAQ;gBAAAzK,QAAA,eAC9CtF,OAAA,CAACxE,sBAAsB;kBACrByO,SAAS,EAAC;kBACV;kBAAA;kBACAuC,OAAO,EAAEA,CAAA,KAAM;oBACbzJ,OAAO,CAAC,KAAK,CAAC;oBACdC,MAAM,CAACgN,IAAI,CAAC,CAAC;kBACf;gBAAE;kBAAA9H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,EAGX,CAAAlE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE8L,UAAU,MAAK,CAAC,gBAAEjQ,OAAA,CAACrE,gBAAgB;gBAACiJ,OAAO,EAAET,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,OAAQ;gBAACsL,MAAM;gBAACC,YAAY,EAAE;kBAACvP;gBAAM,CAAE;gBAACwP,SAAS,EAAEA,CAAA,KAAK/L,mBAAmB,CAAC;cAAE;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,gBAClJrI,OAAA,CAACtE,cAAc;gBAACkJ,OAAO,EAAET,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,OAAQ;gBAACsL,MAAM;gBAACC,YAAY,EAAE;kBAACvP;gBAAM,CAAE;gBAACwP,SAAS,EAAEA,CAAA,KAAK/L,mBAAmB,CAAC;cAAE;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAEX3D,KAAK,IAAIvD,GAAG,iBAAKnB,OAAA,CAACxC,aAAa;YAACkH,KAAK,EAAEA,KAAM;YAAC3D,SAAS,EAAEI,GAAI;YAACkP,OAAO,EAAE3M,QAAQ,CAACkC,QAAS;YAAC0K,UAAU,GAAA9P,qBAAA,GAAEzB,SAAS,CAAC2E,QAAQ,CAACkC,QAAQ,CAAC,cAAApF,qBAAA,uBAA5BA,qBAAA,CAA8B+P;UAAO;YAAArI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEhJrI,OAAA;YAAKiK,SAAS,EAAC,iBAAiB;YAACxF,EAAE,EAAE,WAAW1D,SAAS,EAAG;YAAAuE,QAAA,eAC1DtF,OAAA,CAACvE,aAAa;cAAC0U,YAAY,EAAE;gBAAEvP,MAAM;gBAAE2J,YAAY,EAAE1J,MAAM;gBAAEA,MAAM,EAAEM,GAAG;gBAAEmP,UAAU,GAAA7P,sBAAA,GAAE1B,SAAS,CAAC2E,QAAQ,CAACkC,QAAQ,CAAC,cAAAnF,sBAAA,uBAA5BA,sBAAA,CAA8B8P,MAAM;gBAAEF,OAAO,EAAE3M,QAAQ,CAACkC;cAAQ,CAAE;cAAC4K,iBAAiB,EAAE;YAAK;cAAAtI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CAAC,eAELrI,OAAA,CAACF,eAAe;QACdmK,SAAS,EAAC,aAAa;QACvBgE,KAAK,EAAE,KAAM;QACbwC,OAAO,EAAEA,CAAA,KAAMpJ,mBAAmB,CAAC,KAAK,CAAE;QAC1CqJ,IAAI,EAAEtP,cAAe;QACrBuD,KAAK,EAAEA,KAAK,IAAE,EAAE,IAAI,CAAC,EAAEA,KAAK,IAAE,EAAE,CAAC,GAAG,SAAS,GAAG,EAAE,CAAE;QACpDgM,QAAQ,EAAE,IAAK;QACf7B,MAAM,EAAE,IAAK;QAAAxJ,QAAA,eACbtF,OAAA,CAACtD,QAAQ;UAACwE,OAAO,EAAEsD,UAAW;UAAAc,QAAA,eAC5BtF,OAAA,CAACnC,UAAU;YACT0F,gBAAgB,EAAEA,gBAAiB;YACnC3B,cAAc,EAAEA,cAAe;YAC/B0F,aAAa,EAAEA,aAAc;YAC7BlF,aAAa,EAAEA,aAAc;YAC7B0F,2BAA2B,EAAEA;UAA4B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EACjB7F,mBAAmB,iBACpBxC,OAAA,CAAChB,aAAa;QACV0D,OAAO,EAAEA,OAAQ;QACjBD,sBAAsB,EAAEA,sBAAuB;QAC/C2N,SAAS,EAAEA,CAAA,KAAK/L,mBAAmB,CAAC,CAAE;QACtC/C,kBAAkB,EAAEA,kBAAmB;QACvCgB,SAAS,EAAEA,SAAU;QACrBsO,aAAa,EAAEtM,YAAa;QAC5B9B,mBAAmB,EAAEA;MAAoB;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE5C,CAAC,eAENrI,OAAA,CAACf,WAAW;MAAC2G,QAAQ,EAAElC,QAAQ,CAACkC,QAAS;MAACiL,QAAQ,EAAE;IAAE;MAAA3I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC;EAAA,eACxD,CAAC;AACL;AAAChI,EAAA,CAhmBuBD,OAAO;EAAA,QACZ/C,WAAW,EACbC,SAAS,EAEPF,WAAW,EAGK8B,gBAAgB,EAyBlChC,mBAAmB,EAEgDtB,kCAAkC,EAGrBoC,QAAQ,EAILA,QAAQ,EAI2DA,QAAQ,EAG5EA,QAAQ,EAIpDA,QAAQ,EAEdJ,+BAA+B,EAiQrDuB,WAAW;AAAA;AAAA2R,EAAA,GAvTd1Q,OAAO;AAAA,IAAA0Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}