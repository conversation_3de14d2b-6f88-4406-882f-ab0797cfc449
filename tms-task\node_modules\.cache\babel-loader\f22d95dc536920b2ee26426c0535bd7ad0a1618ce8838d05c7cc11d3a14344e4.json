{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { isArray, isTypedArray, isObject, isUndefinedOrNull } from './types.js';\nexport function deepClone(obj) {\n  if (!obj || typeof obj !== 'object') {\n    return obj;\n  }\n  if (obj instanceof RegExp) {\n    // See https://github.com/microsoft/TypeScript/issues/10990\n    return obj;\n  }\n  const result = Array.isArray(obj) ? [] : {};\n  Object.keys(obj).forEach(key => {\n    if (obj[key] && typeof obj[key] === 'object') {\n      result[key] = deepClone(obj[key]);\n    } else {\n      result[key] = obj[key];\n    }\n  });\n  return result;\n}\nexport function deepFreeze(obj) {\n  if (!obj || typeof obj !== 'object') {\n    return obj;\n  }\n  const stack = [obj];\n  while (stack.length > 0) {\n    const obj = stack.shift();\n    Object.freeze(obj);\n    for (const key in obj) {\n      if (_hasOwnProperty.call(obj, key)) {\n        const prop = obj[key];\n        if (typeof prop === 'object' && !Object.isFrozen(prop) && !isTypedArray(prop)) {\n          stack.push(prop);\n        }\n      }\n    }\n  }\n  return obj;\n}\nconst _hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function cloneAndChange(obj, changer) {\n  return _cloneAndChange(obj, changer, new Set());\n}\nfunction _cloneAndChange(obj, changer, seen) {\n  if (isUndefinedOrNull(obj)) {\n    return obj;\n  }\n  const changed = changer(obj);\n  if (typeof changed !== 'undefined') {\n    return changed;\n  }\n  if (isArray(obj)) {\n    const r1 = [];\n    for (const e of obj) {\n      r1.push(_cloneAndChange(e, changer, seen));\n    }\n    return r1;\n  }\n  if (isObject(obj)) {\n    if (seen.has(obj)) {\n      throw new Error('Cannot clone recursive data-structure');\n    }\n    seen.add(obj);\n    const r2 = {};\n    for (const i2 in obj) {\n      if (_hasOwnProperty.call(obj, i2)) {\n        r2[i2] = _cloneAndChange(obj[i2], changer, seen);\n      }\n    }\n    seen.delete(obj);\n    return r2;\n  }\n  return obj;\n}\n/**\n * Copies all properties of source into destination. The optional parameter \"overwrite\" allows to control\n * if existing properties on the destination should be overwritten or not. Defaults to true (overwrite).\n */\nexport function mixin(destination, source) {\n  let overwrite = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (!isObject(destination)) {\n    return source;\n  }\n  if (isObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (key in destination) {\n        if (overwrite) {\n          if (isObject(destination[key]) && isObject(source[key])) {\n            mixin(destination[key], source[key], overwrite);\n          } else {\n            destination[key] = source[key];\n          }\n        }\n      } else {\n        destination[key] = source[key];\n      }\n    });\n  }\n  return destination;\n}\nexport function equals(one, other) {\n  if (one === other) {\n    return true;\n  }\n  if (one === null || one === undefined || other === null || other === undefined) {\n    return false;\n  }\n  if (typeof one !== typeof other) {\n    return false;\n  }\n  if (typeof one !== 'object') {\n    return false;\n  }\n  if (Array.isArray(one) !== Array.isArray(other)) {\n    return false;\n  }\n  let i;\n  let key;\n  if (Array.isArray(one)) {\n    if (one.length !== other.length) {\n      return false;\n    }\n    for (i = 0; i < one.length; i++) {\n      if (!equals(one[i], other[i])) {\n        return false;\n      }\n    }\n  } else {\n    const oneKeys = [];\n    for (key in one) {\n      oneKeys.push(key);\n    }\n    oneKeys.sort();\n    const otherKeys = [];\n    for (key in other) {\n      otherKeys.push(key);\n    }\n    otherKeys.sort();\n    if (!equals(oneKeys, otherKeys)) {\n      return false;\n    }\n    for (i = 0; i < oneKeys.length; i++) {\n      if (!equals(one[oneKeys[i]], other[oneKeys[i]])) {\n        return false;\n      }\n    }\n  }\n  return true;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}