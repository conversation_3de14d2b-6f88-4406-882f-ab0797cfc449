{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { SimpleWorkerServer } from '../base/common/worker/simpleWorker.js';\nimport { EditorSimpleWorker } from './common/services/editorSimpleWorker.js';\nlet initialized = false;\nexport function initialize(foreignModule) {\n  if (initialized) {\n    return;\n  }\n  initialized = true;\n  const simpleWorker = new SimpleWorkerServer(msg => {\n    self.postMessage(msg);\n  }, host => new EditorSimpleWorker(host, foreignModule));\n  self.onmessage = e => {\n    simpleWorker.onmessage(e.data);\n  };\n}\nself.onmessage = e => {\n  // Ignore first message in this case and initialize if not yet initialized\n  if (!initialized) {\n    initialize(null);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}