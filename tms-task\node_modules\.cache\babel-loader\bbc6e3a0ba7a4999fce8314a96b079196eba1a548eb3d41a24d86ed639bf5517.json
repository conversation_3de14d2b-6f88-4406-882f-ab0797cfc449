{"ast": null, "code": "/**\r\n * @description 本地\r\n */\nimport * as httpBase from \"../utils/httpBase\";\n\n// cdb-102 operate_connection 操作conn连接（测试、保存）  https://confluence.ficent.com/pages/viewpage.action?pageId=78361269\nexport const local_check_version_update = data => httpBase.get('/version.json', data, undefined, false, {\n  ignore: true\n});\n\n// 获取更新日志\nexport const local_get_changelog = data => httpBase.get('/changelog.json', data, undefined, false, {\n  ignore: true\n});", "map": {"version": 3, "names": ["httpBase", "local_check_version_update", "data", "get", "undefined", "ignore", "local_get_changelog"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/api/http_local.js"], "sourcesContent": ["/**\r\n * @description 本地\r\n */\r\nimport * as httpBase from \"../utils/httpBase\";\r\n\r\n// cdb-102 operate_connection 操作conn连接（测试、保存）  https://confluence.ficent.com/pages/viewpage.action?pageId=78361269\r\nexport const local_check_version_update=(data) => httpBase.get('/version.json',data,undefined,false,{ignore: true})\r\n\r\n// 获取更新日志\r\nexport const local_get_changelog=(data) => httpBase.get('/changelog.json',data,undefined,false,{ignore: true})"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,KAAKA,QAAQ,MAAM,mBAAmB;;AAE7C;AACA,OAAO,MAAMC,0BAA0B,GAAEC,IAAI,IAAKF,QAAQ,CAACG,GAAG,CAAC,eAAe,EAACD,IAAI,EAACE,SAAS,EAAC,KAAK,EAAC;EAACC,MAAM,EAAE;AAAI,CAAC,CAAC;;AAEnH;AACA,OAAO,MAAMC,mBAAmB,GAAEJ,IAAI,IAAKF,QAAQ,CAACG,GAAG,CAAC,iBAAiB,EAACD,IAAI,EAACE,SAAS,EAAC,KAAK,EAAC;EAACC,MAAM,EAAE;AAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}