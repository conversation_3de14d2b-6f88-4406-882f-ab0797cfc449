{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\issueTrack\\\\views\\\\IssueKanban\\\\IssueKanban.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { Button, Drawer, Space, Layout, Modal } from \"antd\";\nimport { useParams, useOutletContext, useNavigate, useLocation, useSearchParams } from \"react-router-dom\";\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport { useQueryClient } from \"@tanstack/react-query\";\nimport { getIconValueByIdType } from \"src/issueTrack/utils/ArrayUtils\";\nimport { eConsoleNodeId, ePagination } from \"@common/utils/enum\";\nimport * as httpIssue from \"@common/api/http\";\nimport { useQueryIssue500getIssueView } from \"src/issueTrack/service/issueHooks\";\nimport avatarImg from \"@common/assets/images/avatar.png\";\nimport noAvatarImg from \"@common/assets/images/noAvatar.png\";\nimport IssueDetail from \"../IssueDetail/IssueDetail\";\nimport { useIssueSearchParams } from \"src/issueTrack/service/issueSearchHooks\";\nimport { track012 } from '@common/utils/ApiPath';\nimport { isEmpty } from \"@common/utils/ArrayUtils\";\nimport \"./IssueKanban.scss\";\nimport TLoading from \"@common/components/TLoading\";\nimport { ExclamationCircleOutlined } from '@ant-design/icons';\nimport { priPermission, getNodeNameByNodeId } from \"@common/utils/logicUtils\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport DraggableDrawer from \"@components/DraggableDrawer\";\n\n// TODO:看板的issueId没有更改\n// 看板 kanban.js\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Kanban,\n  Toolbar,\n  defaultCardShape,\n  getDefaultCardMenuItems\n} = window.kanban;\nconst {\n  Content\n} = Layout;\nexport default function IssueKanban() {\n  _s();\n  var _ref, _ref$find;\n  const board = useRef({});\n  const scrollRef = useRef();\n  const {\n    teamId,\n    nodeId: issueListNodeId\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const createFlg = searchParams.get(\"createFlg\"); //是否创建\n  const open = searchParams.get(\"open\"); //是否打开详情\n\n  const {\n    subclassAttrList,\n    userList,\n    spaceUserList,\n    selectionList,\n    objInfo,\n    keywords,\n    searchQuery,\n    viewMode,\n    projectInfo,\n    setCurrentIssueIdx,\n    setting320Result,\n    finalCreateFlg\n  } = useOutletContext();\n  const queryClient = useQueryClient();\n  const {\n    issueNodeId,\n    queryId,\n    setIssueSearchParams\n  } = useIssueSearchParams(); // issue路由配置，页面刷新\n\n  // 看板列表\n  const [kanbanPageNo, setKanbanPageNo] = useState(1); // 看板页码\n  const {\n    kanbanTotal = 0,\n    kanbanMaxCount = 0,\n    kanbanGroupList = [],\n    iconSelectionLid,\n    isLoading: isLoadingGetIssueView\n  } = useQueryIssue500getIssueView(teamId, issueListNodeId, \"\", \"\", searchQuery, keywords, kanbanPageNo, ePagination.PageSize_30);\n  const [refreshingFlg, setRefreshingFlg] = useState(false); //是否正在\"刷新中\"\n  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);\n  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);\n  const [addCard, setAddCard] = useState(false);\n  const [currentKanbanIssueIdx, setCurrentKanbanIssueIdx] = useState(0); // 看板选中index\n  const [currentKanbanGroup, setCurrentKanbanGroup] = useState(); //记忆用户点击了看板的哪一个组，对应IssueKanban中的 columnId: _group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题\n  const [columns, setColumns] = useState([]);\n  const [cards, setCards] = useState([]);\n  //以最长的那个分组的issue长度来进行判断是否会有下一页\n  const [issueDetailDrawerVisibleFlg, setIssueDetailDrawerVisibleFlg] = useState(false);\n  const [initLoading, setInitLoading] = useState(true); // 初始化加载\n  const [dataSourceKey, setDataSourceKey] = useState(0); // 数据源变化标识\n\n  console.log(\"kanbanTotal,kanbanMaxCount,kanbanGroupList,kanbanPageNo, initLoading, cards\", kanbanTotal, kanbanMaxCount, kanbanGroupList, kanbanPageNo, initLoading, cards);\n\n  // const [kanbanGroupList, setKanbanGroupList] = useState([]); //看板视图时，分组列表以及在其内部的issueList (数据源)\n\n  //issue编号发生变化, 判定是否可以上一条和下一条\n  //不能使用currentIssueIdx来判定，因为鼠标点击某一个issue时，currentIssueId发生变化，需要重新计算其对应的index\n  useEffect(() => {\n    if (!isEmpty(cards) && !!issueNodeId) {\n      handleByObjNodeId();\n    }\n  }, [cards, issueNodeId]);\n\n  // isLoadingGetIssueView 下拉加载都会调用，因此需要判断是否是第一次加载\n  useEffect(() => {\n    if (!isLoadingGetIssueView) {\n      setInitLoading(false);\n    }\n  }, [isLoadingGetIssueView]);\n  useEffect(() => {\n    if (scrollRef.current) {\n      var _scrollRef$current, _scrollRef$current$el;\n      scrollRef === null || scrollRef === void 0 ? void 0 : (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : (_scrollRef$current$el = _scrollRef$current.el) === null || _scrollRef$current$el === void 0 ? void 0 : _scrollRef$current$el.addEventListener('scroll', onScrollListener);\n    }\n    return () => {\n      var _scrollRef$current2, _scrollRef$current2$e;\n      scrollRef === null || scrollRef === void 0 ? void 0 : (_scrollRef$current2 = scrollRef.current) === null || _scrollRef$current2 === void 0 ? void 0 : (_scrollRef$current2$e = _scrollRef$current2.el) === null || _scrollRef$current2$e === void 0 ? void 0 : _scrollRef$current2$e.removeEventListener('scroll', onScrollListener);\n    };\n  }, [scrollRef]);\n  useEffect(() => {\n    if (createFlg) {\n      setIssueSearchParams({\n        queryId: -1\n      });\n      setTimeout(() => {\n        // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑\n        // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；\n        openCreateIssueModal();\n      }, 2000);\n    }\n  }, [createFlg]);\n  function openCreateIssueModal() {\n    globalEventBus.emit(\"openCreateIssueModalEvent\", \"\", {\n      nodeId: issueListNodeId,\n      projectInfo,\n      callback: onPostIssueCreated\n    });\n  }\n  function handleByObjNodeId() {\n    try {\n      const {\n        idx,\n        kanban\n      } = findIndexByObjNodeId(cards, issueNodeId);\n      console.log(\"kanban\", kanban, idx);\n      if (!!kanban) {\n        setPreviousIssueLinkDisabledFlg(idx == 0);\n        setNextIssueLinkDisabledFlg(idx == kanban.issueTotalNum - 1);\n      }\n    } catch (e) {\n      console.error(\"handleByObjNodeId\", e);\n    }\n  }\n\n  // 根据objNodeId查询数据\n  function findIndexByObjNodeId(cards, issueNodeId) {\n    const idx = cards.findIndex(_issue => _issue.nodeId == issueNodeId);\n    const kanban = cards.find(_issue => _issue.nodeId == issueNodeId);\n    return {\n      idx,\n      kanban\n    };\n  }\n  function gotoKanbanNextIssue(currentGroupCards) {\n    // 当前已加载的选中看板组总数\n    let currentGroupCardsLength = currentGroupCards.length;\n    console.log(\"currentKanbanIssueIdx\", currentKanbanIssueIdx, currentGroupCardsLength);\n    // 当前选中idx为当前看板组最后一条，判断是否有下一页\n    if (currentKanbanIssueIdx == currentGroupCardsLength - 1) {\n      let totalIssueCnt = kanbanGroupList.find(group => group.type == currentKanbanGroup).issueTotalNum;\n      console.log(\"totalIssueCnt\", totalIssueCnt);\n      if (currentGroupCardsLength < totalIssueCnt) {\n        //可以继续往后翻页\n        loadKanbanIssueList();\n        setCurrentKanbanIssueIdx(currentKanbanIssueIdx + 1);\n      }\n    } else {\n      var _currentGroupCards;\n      console.log(\"currentGroupCardscurrentGroupCards\", currentGroupCards[currentKanbanIssueIdx + 1]);\n      const issueNodeId = (_currentGroupCards = currentGroupCards[currentKanbanIssueIdx + 1]) === null || _currentGroupCards === void 0 ? void 0 : _currentGroupCards.nodeId;\n      setCurrentKanbanIssueIdx(currentKanbanIssueIdx + 1);\n      // 触发kanban组件选择card事件\n      board.current.selectCard(currentGroupCards[currentKanbanIssueIdx + 1]);\n      setIssueSearchParams({\n        viewMode,\n        issueNodeId,\n        queryId\n      }); // 更改路由issueId\n    }\n  }\n\n  //看板上一条issue\n  function gotoKanbanPreviousIssue() {\n    if (currentKanbanIssueIdx > 0) {\n      var _groupCards;\n      let groupCards = board.current.getAreaCards(currentKanbanGroup);\n      const issueNodeId = (_groupCards = groupCards[currentKanbanIssueIdx - 1]) === null || _groupCards === void 0 ? void 0 : _groupCards.nodeId;\n      setCurrentKanbanIssueIdx(currentKanbanIssueIdx - 1);\n      // 触发kanban组件选择card事件\n      board.current.selectCard(groupCards[currentKanbanIssueIdx - 1]);\n      setIssueSearchParams({\n        viewMode,\n        issueNodeId,\n        queryId\n      }); // 更改路由issueId\n    }\n  }\n  // 下一条\n  function _nextIssue() {\n    let groupCards = board.current.getAreaCards(currentKanbanGroup);\n    console.log(\"groupCards\", groupCards);\n    gotoKanbanNextIssue(groupCards); //传回当前列的现有长度，以让IssueHome中判定是否需要翻页加载\n  }\n\n  //刷新issue列表\n  function refreshIssueList() {\n    // 如果当前是第一页强制刷新，非第一页则刷新第一页\n    if (kanbanPageNo == 1) {\n      queryClient.invalidateQueries([track012]);\n    } else {\n      setKanbanPageNo(1);\n    }\n    setTimeout(() => {\n      setRefreshingFlg(false);\n    }, 500);\n  }\n  // card属性\n  const cardShape = {\n    ...defaultCardShape,\n    label: true,\n    description: true,\n    progress: true,\n    start_date: true,\n    end_date: true,\n    priority: true,\n    color: true,\n    cover: true,\n    attached: true,\n    menu: {\n      show: false\n    }\n  };\n  const columnShape = {\n    menu: {\n      show: true,\n      items: ({\n        column,\n        columnIndex,\n        store\n      }) => {\n        console.log(column, columnIndex, store);\n        return [{\n          id: \"set-edit\",\n          icon: \"wxi-edit\",\n          label: \"Rename\"\n        }, {\n          id: \"delete-column\",\n          icon: \"wxi-delete\",\n          label: \"Delete\"\n        }, {\n          id: \"add-card\",\n          icon: \"wxi-plus\",\n          label: \"Add new card\"\n        }, {\n          id: \"move-column:left\",\n          icon: \"wxi-arrow-left\",\n          label: \"Move left\",\n          disabled: true\n        }, {\n          id: \"move-column:right\",\n          icon: \"wxi-arrow-right\",\n          label: \"Move right\",\n          disabled: true\n        }];\n      }\n    }\n  };\n\n  // card 模板\n  function cardTemplate({\n    cardFields,\n    selected,\n    dragging,\n    cardShape\n  }) {\n    const {\n      label,\n      prefix,\n      planFinishDt,\n      icon,\n      userName,\n      avatar\n    } = cardFields;\n    // TODO:userName和avatar接口返回，无数据\n    return `\n        <div class=\"custom-card\">\n          <div class=\"custom-card-content\">\n            <div>${icon}</div>\n            <a class=\"fontsize-12\">${prefix}</a>\n          </div>\n          <div>\n            <div class=\"issueTitle\">${label}</div>\n          </div>\n          <div class=\"issueinfo\">\n            <div style=\"color:#999\">\n              <span class=\"iconfont rili fontsize-12\"></span>\n              <span class=\"fontsize-12\"> ${planFinishDt ? planFinishDt : \"\"}</span>\n            </div>\n            <div class=\"user\">\n              <div class=\"fontsize-12\">${userName ? userName : \"\"}</div>\n              <div class=\"avatar\">\n                <img src=${avatar ? avatar : userName ? avatarImg : noAvatarImg}></img>\n              </div>\n            </div>\n          </div>\n        </div>\n    `;\n  }\n\n  // 看板滚动加载数据\n  function loadKanbanIssueList() {\n    setKanbanPageNo(kanbanPageNo + 1);\n  }\n  // 看板移动卡片\n  function onKanbanColumnMoved({\n    id,\n    columnId\n  }) {\n    const [nodeId] = Array.isArray(id) ? id : id.split(\",\");\n    let request = {\n      teamId: teamId,\n      nodeId: nodeId,\n      [eConsoleNodeId.Nid_11110_Issue_Status]: columnId\n    };\n    httpIssue.track_007_edit_issue(request).then(res => {\n      if (res.resultCode === 200) {\n        queryClient.invalidateQueries([track012]);\n        // board.current.updateCard({\n        // })\n      }\n    });\n  }\n\n  // 选择卡片 \n  function onKanbanCellSelected({\n    id,\n    groupMode\n  }) {\n    if (!id) {\n      // FIXME: 点击空白区域会触发该事件\n      return;\n    }\n    const [nodeId, group] = id.split(\",\");\n    if (groupMode === false) {\n      let idx = kanbanGroupList.find(_group => _group.type == group).issueList.findIndex(_issue => _issue.nodeId == nodeId);\n      setCurrentKanbanGroup(group);\n      setCurrentKanbanIssueIdx(idx);\n      setIssueDetailDrawerVisibleFlg(true);\n      setIssueSearchParams({\n        viewMode,\n        issueNodeId: nodeId,\n        queryId\n      }); // 更改路由issueId\n    }\n  }\n\n  // card找当前\n  const findCard = id => {\n    let findItem = cards.findIndex(el => el.id == id);\n    return findItem;\n  };\n\n  // 清理看板容器的函数\n  const cleanupKanban = () => {\n    const kanbanElement = document.getElementById('issuekanban');\n    if (kanbanElement) {\n      // 清理容器内容\n      kanbanElement.innerHTML = '';\n      console.log('清理看板容器内容');\n    }\n\n    // 销毁看板实例\n    if (!isEmpty(board.current)) {\n      try {\n        board.current.destroy();\n        console.log('销毁看板实例');\n      } catch (e) {\n        console.log('销毁看板实例时出错:', e);\n      }\n      board.current = {};\n    }\n  };\n\n  //初始化\n  useEffect(() => {\n    if (!initLoading && kanbanTotal != 0) {\n      // 使用清理函数彻底清理\n      cleanupKanban();\n\n      // 重新创建看板\n      board.current = new Kanban(\"#issuekanban\", {\n        columns,\n        cards,\n        cardShape,\n        cardTemplate: window.kanban.template(card => cardTemplate(card)),\n        // 版本更新的写法\n        readonly: {\n          edit: false,\n          add: false\n        },\n        columnShape: {\n          fixedHeaders: true\n        }\n        // scrollType: \"column\",\n      }); // columnShape,\n    }\n    // setTimeout(()=>{\n    //   adjustScrollPosition();\n    // },500)\n  }, [initLoading, kanbanTotal, dataSourceKey]);\n\n  //数据绑定\n  useEffect(() => {\n    if (!initLoading) {\n      // 如果第一页 并且没有新增\n      if (kanbanPageNo == 1 && (kanbanGroupList === null || kanbanGroupList === void 0 ? void 0 : kanbanGroupList.length) > 0 && !addCard) {\n        const columns = [];\n        kanbanGroupList.map((group, index) => {\n          if (group.name != null) {\n            let columnObj = {\n              id: group.type,\n              //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题\n              label: group.name + \"(\" + group.issueTotalNum + \")\"\n            };\n            columns.push(columnObj);\n          }\n        });\n        // 看板卡片\n        let cardsList = [];\n        kanbanGroupList.map(_group => {\n          (_group.issueList || []).map(_issue => {\n            if (_issue.id) {\n              let cardObj = {\n                ..._issue,\n                column: _group.type,\n                id: [_issue.nodeId, _group.type].toString(),\n                //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(\",\")解开id,type\n                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),\n                label: _issue.title\n              };\n              cardsList.push(cardObj);\n            }\n          });\n        });\n        console.log(\"columns,cardsList\", columns, cardsList);\n        setCards(cardsList);\n\n        // 如果看板已存在，使用parse更新；否则让初始化useEffect处理\n        if (!isEmpty(board.current) && board.current.parse) {\n          board.current.parse({\n            columns: columns,\n            cards: cardsList\n          });\n          board.current.events.on(\"move-card\", onKanbanColumnMoved);\n          board.current.events.on(\"select-card\", onKanbanCellSelected);\n        }\n      }\n      // 如果第一页 并且新增，只要插入新增项\n      if ((kanbanGroupList === null || kanbanGroupList === void 0 ? void 0 : kanbanGroupList.length) > 0 && addCard) {\n        // 看板列表\n        const columns = [];\n        kanbanGroupList.map((group, index) => {\n          if (group.name != null) {\n            let columnObj = {\n              id: group.type,\n              //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题\n              label: group.name + \"(\" + group.issueTotalNum + \")\"\n            };\n            columns.push(columnObj);\n          }\n        });\n        kanbanGroupList.map(_group => {\n          (_group.issueList || []).map(_issue => {\n            if (_issue.id) {\n              let cardObj = {\n                ..._issue,\n                column: _group.type,\n                id: [_issue.nodeId, _group.type].toString(),\n                //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(\",\")解开id,type\n                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),\n                label: _issue.title\n              };\n              let findIndex = findCard([_issue.nodeId, _group.type].toString());\n              // fix tmsbug-10144:看板视图下创建问题，提交之后 页面没刷新\n              if (findIndex == -1) {\n                cards.splice(0, 0, cardObj);\n              }\n            }\n          });\n        });\n        console.log(\"columns,cards\", columns, cards);\n        // parse\n        board.current.parse({\n          columns: columns,\n          cards: cards\n        });\n        setAddCard(false);\n        setCards([...cards]);\n      }\n      // 看板页码不为1，并且非新增\n      if ((kanbanGroupList === null || kanbanGroupList === void 0 ? void 0 : kanbanGroupList.length) > 0 && kanbanPageNo != 1 && !addCard) {\n        // 看板列表\n        const columns = [];\n        kanbanGroupList.map((group, index) => {\n          if (group.name != null) {\n            let columnObj = {\n              id: group.type,\n              //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题\n              label: group.name + \"(\" + group.issueTotalNum + \")\"\n            };\n            columns.push(columnObj);\n          }\n        });\n        // 看板卡片\n        kanbanGroupList.map(_group => {\n          (_group.issueList || []).map(_issue => {\n            if (_issue.id) {\n              let cardObj = {\n                ..._issue,\n                column: _group.type,\n                id: [_issue.nodeId, _group.type].toString(),\n                //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(\",\")解开id,type\n                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),\n                label: _issue.title\n              };\n              let findIndex = findCard([_issue.nodeId, _group.type].toString());\n              if (findIndex >= 0) {\n                cards[findIndex] = cardObj;\n              } else {\n                cards.push(cardObj);\n              }\n              console.log(\"findCard(_issue.id)\", findIndex);\n            }\n          });\n        });\n        // parse\n        board.current.parse({\n          columns: columns,\n          cards: cards\n        });\n        setCards([...cards]);\n      }\n    }\n  }, [initLoading, kanbanTotal]);\n\n  // 监听搜索条件变化，触发重新初始化\n  useEffect(() => {\n    if (!initLoading) {\n      // 使用清理函数彻底清理\n      cleanupKanban();\n\n      // 触发重新初始化\n      setDataSourceKey(prev => prev + 1);\n      console.log('搜索条件变化，触发重新初始化');\n    }\n  }, [searchQuery, keywords]);\n\n  // issue创建完成后，页面刷新\n  function onPostIssueCreated() {\n    setAddCard(true);\n    refreshIssueList();\n  }\n\n  // \n  function onScrollListener(e) {\n    console.log(\"onScrollListener start\");\n    let headerElem = document.querySelector(\".wx-header\");\n    if (headerElem && e !== null && e !== void 0 && e.target) {\n      headerElem.style.top = e.target.scrollTop + \"px\";\n    }\n  }\n  async function addClick() {\n    var _await$getNodeNameByN;\n    return Modal.confirm({\n      title: \"提示\",\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 15\n      }, this),\n      centered: true,\n      content: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: `当前列表页为\"查询\"页，将转至“${(_await$getNodeNameByN = await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)) !== null && _await$getNodeNameByN !== void 0 ? _await$getNodeNameByN : \"全部\"}”页创建问题。`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 18\n      }, this),\n      okText: \" 好的，继续新建\",\n      cancelText: \"取消\",\n      onOk: () => {\n        if (!!(projectInfo !== null && projectInfo !== void 0 && projectInfo.allIssueNodeId)) {\n          navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}/kanban?createFlg=true`);\n        }\n      },\n      onCancel: () => {\n        console.log(\"Cancel\");\n      }\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(Content, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flexBetween\",\n      style: {\n        height: 45\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: 10,\n        children: [(searchQuery === null || searchQuery === void 0 ? void 0 : searchQuery.length) > 0 || keywords ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            paddingLeft: 15,\n            fontSize: 12,\n            color: \"#666\"\n          },\n          children: [\"\\u7ED3\\u679C(\", kanbanTotal, \"\\u6761)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            paddingLeft: 15,\n            fontSize: 12,\n            color: \"#666\"\n          },\n          children: [\"\\u5168\\u90E8(\", kanbanTotal, \"\\u6761)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this), finalCreateFlgpriPermission(setting320Result.privWrite) && /*#__PURE__*/_jsxDEV(Button, {\n          className: \"defaultBtn\",\n          type: \"primary\",\n          onClick: () => !!(objInfo !== null && objInfo !== void 0 && objInfo.objId) ? addClick() : openCreateIssueModal(),\n          children: [\"+ \", projectInfo.issueAlias]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"scrollableDiv\",\n      className: \"kanban-scroll\",\n      children: /*#__PURE__*/_jsxDEV(InfiniteScroll, {\n        ref: scrollRef,\n        style: {\n          flex: \"auto\"\n        },\n        scrollableTarget: \"scrollableDiv\",\n        dataLength: kanbanPageNo * ePagination.PageSize_30,\n        next: e => loadKanbanIssueList(),\n        hasMore: kanbanPageNo * ePagination.PageSize_30 < kanbanMaxCount\n        // onScroll={onScrollListener}\n        // loader={<Skeleton avatar paragraph={{ rows: 1,}} active loading={loading}/>}\n        // endMessage={<div>数据加载完毕!</div>}\n        ,\n        children: initLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"blank-page\",\n          children: [\" \", /*#__PURE__*/_jsxDEV(TLoading, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 61\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 32\n        }, this) :\n        // Issue总数为0、且初始化（页码为1）时，显示空白页\n        kanbanPageNo == 1 && kanbanTotal == 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            // textAlign: 'center',\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"blank-page\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"blank-page-title\",\n              children: \"\\u8FD9\\u91CC\\u662F\\u7A7A\\u7684\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 24\n            }, this), priPermission(setting320Result.privWrite) ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"blank-page-des fontsize-12 flexCenter\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  paddingRight: 5\n                },\n                children: \"\\u4F60\\u53EF\\u4EE5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 27\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                onClick: () => openCreateIssueModal(),\n                children: [\"\\u65B0\\u5EFA\", projectInfo.issueAlias]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 27\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 25\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 22\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 20\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"issuekanban\",\n          className: \"issuekanban\",\n          style: {\n            minHeight: '400px',\n            display: 'block'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n      destroyOnClose: true,\n      className: \"kanban-detail\",\n      width: '60%',\n      minWidth: '50%',\n      maxWidth: '90%',\n      onClose: () => setIssueDetailDrawerVisibleFlg(false),\n      open: issueDetailDrawerVisibleFlg,\n      closable: true,\n      title: `${projectInfo.issueAlias}详情`,\n      children: /*#__PURE__*/_jsxDEV(IssueDetail, {\n        viewMode: viewMode,\n        selectionList: selectionList,\n        userList: userList,\n        spaceUserList: spaceUserList,\n        gotoPreviousIssue: gotoKanbanPreviousIssue,\n        gotoNextIssue: _nextIssue,\n        previousIssueLinkDisabledFlg: previousIssueLinkDisabledFlg,\n        nextIssueLinkDisabledFlg: nextIssueLinkDisabledFlg,\n        projectInfo: projectInfo,\n        setCurrentIssueIdx: setCurrentIssueIdx,\n        issueList: ((_ref = kanbanGroupList || []) === null || _ref === void 0 ? void 0 : (_ref$find = _ref.find(kanban => kanban.type == currentKanbanGroup)) === null || _ref$find === void 0 ? void 0 : _ref$find.issueList) || []\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 493,\n    columnNumber: 10\n  }, this);\n}\n_s(IssueKanban, \"+ALulTz6A8UI4B0HC6ZugvE0iBs=\", false, function () {\n  return [useParams, useNavigate, useLocation, useSearchParams, useOutletContext, useQueryClient, useIssueSearchParams, useQueryIssue500getIssueView];\n});\n_c = IssueKanban;\nvar _c;\n$RefreshReg$(_c, \"IssueKanban\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "<PERSON><PERSON>", "Drawer", "Space", "Layout", "Modal", "useParams", "useOutletContext", "useNavigate", "useLocation", "useSearchParams", "InfiniteScroll", "useQueryClient", "getIconValueByIdType", "eConsoleNodeId", "ePagination", "httpIssue", "useQueryIssue500getIssueView", "avatarImg", "noAvatarImg", "IssueDetail", "useIssueSearchParams", "track012", "isEmpty", "TLoading", "ExclamationCircleOutlined", "priPermission", "getNodeNameByNodeId", "globalEventBus", "DraggableDrawer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Ka<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "defaultCardShape", "getDefaultCardMenuItems", "window", "kanban", "Content", "IssueKanban", "_s", "_ref", "_ref$find", "board", "scrollRef", "teamId", "nodeId", "issueListNodeId", "navigate", "location", "searchParams", "setSearchParams", "createFlg", "get", "open", "subclassAttrList", "userList", "spaceUserList", "selectionList", "objInfo", "keywords", "searchQuery", "viewMode", "projectInfo", "setCurrentIssueIdx", "setting320Result", "finalCreateFlg", "queryClient", "issueNodeId", "queryId", "setIssueSearchParams", "kanbanPageNo", "setKanbanPageNo", "kanbanTotal", "kanbanMaxCount", "kanbanGroupList", "iconSelectionLid", "isLoading", "isLoadingGetIssueView", "PageSize_30", "refreshingFlg", "setRefreshingFlg", "previousIssueLinkDisabledFlg", "setPreviousIssueLinkDisabledFlg", "nextIssueLinkDisabledFlg", "setNextIssueLinkDisabledFlg", "addCard", "setAddCard", "currentKanbanIssueIdx", "setCurrentKanbanIssueIdx", "currentKanbanGroup", "setCurrentKanbanGroup", "columns", "setColumns", "cards", "setCards", "issueDetailDrawerVisibleFlg", "setIssueDetailDrawerVisibleFlg", "initLoading", "setInitLoading", "dataSourceKey", "setDataSourceKey", "console", "log", "handleByObjNodeId", "current", "_scrollRef$current", "_scrollRef$current$el", "el", "addEventListener", "onScrollListener", "_scrollRef$current2", "_scrollRef$current2$e", "removeEventListener", "setTimeout", "openCreateIssueModal", "emit", "callback", "onPostIssueCreated", "idx", "findIndexByObjNodeId", "issueTotalNum", "e", "error", "findIndex", "_issue", "find", "gotoKanbanNextIssue", "currentGroupCards", "currentGroupCardsLength", "length", "totalIssueCnt", "group", "type", "loadKanbanIssueList", "_currentGroupCards", "selectCard", "gotoKanbanPreviousIssue", "_groupCards", "groupCards", "getAreaCards", "_nextIssue", "refreshIssueList", "invalidateQueries", "cardShape", "label", "description", "progress", "start_date", "end_date", "priority", "color", "cover", "attached", "menu", "show", "columnShape", "items", "column", "columnIndex", "store", "id", "icon", "disabled", "cardTemplate", "cardFields", "selected", "dragging", "prefix", "planFinishDt", "userName", "avatar", "onKanbanColumnMoved", "columnId", "Array", "isArray", "split", "request", "Nid_11110_Issue_Status", "track_007_edit_issue", "then", "res", "resultCode", "onKanbanCellSelected", "groupMode", "_group", "issueList", "findCard", "findItem", "cleanup<PERSON><PERSON>ban", "kanbanElement", "document", "getElementById", "innerHTML", "destroy", "template", "card", "readonly", "edit", "add", "fixedHeaders", "map", "index", "name", "columnObj", "push", "cardsList", "cardObj", "toString", "issueType", "title", "parse", "events", "on", "splice", "prev", "headerElem", "querySelector", "target", "style", "top", "scrollTop", "addClick", "_await$getNodeNameByN", "confirm", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "centered", "content", "children", "allIssueNodeId", "okText", "cancelText", "onOk", "onCancel", "className", "height", "size", "paddingLeft", "fontSize", "finalCreateFlgpriPermission", "privWrite", "onClick", "objId", "issueAlias", "ref", "flex", "scrollableTarget", "dataLength", "next", "hasMore", "paddingRight", "minHeight", "display", "destroyOnClose", "width", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "onClose", "closable", "gotoPreviousIssue", "gotoNextIssue", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/issueTrack/views/IssueKanban/IssueKanban.jsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { But<PERSON>, Drawer, Space, Layout, Modal } from \"antd\";\r\nimport { useParams, useOutletContext, useNavigate, useLocation, useSearchParams } from \"react-router-dom\";\r\nimport InfiniteScroll from 'react-infinite-scroll-component';\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { getIconValueByIdType } from \"src/issueTrack/utils/ArrayUtils\";\r\nimport { eConsoleNodeId, ePagination } from \"@common/utils/enum\";\r\nimport * as httpIssue from \"@common/api/http\";\r\nimport { useQueryIssue500getIssueView } from \"src/issueTrack/service/issueHooks\";\r\nimport avatarImg from \"@common/assets/images/avatar.png\";\r\nimport noAvatarImg from \"@common/assets/images/noAvatar.png\";\r\nimport IssueDetail from \"../IssueDetail/IssueDetail\";\r\nimport { useIssueSearchParams } from \"src/issueTrack/service/issueSearchHooks\";\r\nimport { track012 } from '@common/utils/ApiPath';\r\nimport { isEmpty } from \"@common/utils/ArrayUtils\";\r\nimport \"./IssueKanban.scss\"\r\nimport TLoading from \"@common/components/TLoading\";\r\nimport { ExclamationCircleOutlined } from '@ant-design/icons';\r\nimport { priPermission, getNodeNameByNodeId } from \"@common/utils/logicUtils\";\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\n\r\n// TODO:看板的issueId没有更改\r\n// 看板 kanban.js\r\nconst { Kanban, Toolbar, defaultCardShape, getDefaultCardMenuItems } = window.kanban\r\nconst { Content } = Layout\r\n\r\nexport default function IssueKanban() {\r\n  const board = useRef({});\r\n  const scrollRef = useRef();\r\n  const { teamId, nodeId: issueListNodeId } = useParams();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [searchParams, setSearchParams] = useSearchParams();\r\n  const createFlg = searchParams.get(\"createFlg\"); //是否创建\r\n  const open = searchParams.get(\"open\"); //是否打开详情\r\n\r\n  const { subclassAttrList, userList, spaceUserList, selectionList, objInfo, keywords, searchQuery, viewMode, projectInfo, setCurrentIssueIdx, setting320Result, finalCreateFlg } = useOutletContext();\r\n  const queryClient = useQueryClient();\r\n  const { issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams();// issue路由配置，页面刷新\r\n\r\n  // 看板列表\r\n  const [kanbanPageNo, setKanbanPageNo] = useState(1); // 看板页码\r\n  const { kanbanTotal = 0, kanbanMaxCount = 0, kanbanGroupList = [], iconSelectionLid, isLoading: isLoadingGetIssueView } = useQueryIssue500getIssueView(teamId, issueListNodeId, \"\", \"\", searchQuery, keywords, kanbanPageNo, ePagination.PageSize_30)\r\n  const [refreshingFlg, setRefreshingFlg] = useState(false); //是否正在\"刷新中\"\r\n  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);\r\n  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);\r\n  const [addCard, setAddCard] = useState(false);\r\n  const [currentKanbanIssueIdx, setCurrentKanbanIssueIdx] = useState(0); // 看板选中index\r\n  const [currentKanbanGroup, setCurrentKanbanGroup] = useState(); //记忆用户点击了看板的哪一个组，对应IssueKanban中的 columnId: _group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题\r\n  const [columns, setColumns] = useState([]);\r\n  const [cards, setCards] = useState([]);\r\n  //以最长的那个分组的issue长度来进行判断是否会有下一页\r\n  const [issueDetailDrawerVisibleFlg, setIssueDetailDrawerVisibleFlg] = useState(false);\r\n  const [initLoading, setInitLoading] = useState(true); // 初始化加载\r\n  const [dataSourceKey, setDataSourceKey] = useState(0); // 数据源变化标识\r\n\r\n  console.log(\"kanbanTotal,kanbanMaxCount,kanbanGroupList,kanbanPageNo, initLoading, cards\", kanbanTotal, kanbanMaxCount, kanbanGroupList, kanbanPageNo, initLoading, cards)\r\n\r\n  // const [kanbanGroupList, setKanbanGroupList] = useState([]); //看板视图时，分组列表以及在其内部的issueList (数据源)\r\n\r\n  //issue编号发生变化, 判定是否可以上一条和下一条\r\n  //不能使用currentIssueIdx来判定，因为鼠标点击某一个issue时，currentIssueId发生变化，需要重新计算其对应的index\r\n  useEffect(() => {\r\n    if (!isEmpty(cards) && !!issueNodeId) {\r\n      handleByObjNodeId();\r\n    }\r\n  }, [cards, issueNodeId])\r\n\r\n  // isLoadingGetIssueView 下拉加载都会调用，因此需要判断是否是第一次加载\r\n  useEffect(() => {\r\n    if (!isLoadingGetIssueView) {\r\n      setInitLoading(false);\r\n    }\r\n  }, [isLoadingGetIssueView])\r\n\r\n  useEffect(() => {\r\n    if (scrollRef.current) {\r\n      scrollRef?.current?.el?.addEventListener('scroll', onScrollListener)\r\n    }\r\n    return () => {\r\n      scrollRef?.current?.el?.removeEventListener('scroll', onScrollListener);\r\n    }\r\n  }, [scrollRef])\r\n\r\n \r\n  useEffect(()=>{\r\n    if(createFlg){\r\n      setIssueSearchParams({queryId: -1});\r\n      setTimeout(()=>{\r\n        // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑\r\n        // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；\r\n       openCreateIssueModal()\r\n      }, 2000)\r\n    } \r\n  },[createFlg])\r\n\r\n  function openCreateIssueModal () {\r\n    globalEventBus.emit(\"openCreateIssueModalEvent\", \"\", {\r\n      nodeId: issueListNodeId, projectInfo, callback: onPostIssueCreated\r\n    })\r\n  }\r\n\r\n  function handleByObjNodeId() {\r\n    try {\r\n      const { idx, kanban } = findIndexByObjNodeId(cards, issueNodeId)\r\n      console.log(\"kanban\", kanban, idx)\r\n      if(!!kanban){\r\n        setPreviousIssueLinkDisabledFlg(idx == 0);\r\n        setNextIssueLinkDisabledFlg(idx == kanban.issueTotalNum - 1);\r\n      }\r\n    } catch (e) {\r\n      console.error(\"handleByObjNodeId\", e)\r\n    }\r\n  }\r\n\r\n  // 根据objNodeId查询数据\r\n  function findIndexByObjNodeId(cards, issueNodeId) {\r\n    const idx = cards.findIndex(_issue => _issue.nodeId == issueNodeId);\r\n    const kanban = cards.find(_issue => _issue.nodeId == issueNodeId);\r\n    return { idx, kanban }\r\n  }\r\n\r\n  function gotoKanbanNextIssue(currentGroupCards) {\r\n    // 当前已加载的选中看板组总数\r\n    let currentGroupCardsLength = currentGroupCards.length\r\n    console.log(\"currentKanbanIssueIdx\", currentKanbanIssueIdx, currentGroupCardsLength)\r\n    // 当前选中idx为当前看板组最后一条，判断是否有下一页\r\n    if (currentKanbanIssueIdx == currentGroupCardsLength - 1) {\r\n      let totalIssueCnt = kanbanGroupList.find(group => group.type == currentKanbanGroup).issueTotalNum;\r\n      console.log(\"totalIssueCnt\", totalIssueCnt)\r\n      if (currentGroupCardsLength < totalIssueCnt) { //可以继续往后翻页\r\n        loadKanbanIssueList();\r\n        setCurrentKanbanIssueIdx(currentKanbanIssueIdx + 1);\r\n      }\r\n    } else {\r\n      console.log(\"currentGroupCardscurrentGroupCards\", currentGroupCards[currentKanbanIssueIdx + 1])\r\n      const issueNodeId = currentGroupCards[currentKanbanIssueIdx + 1]?.nodeId;\r\n      setCurrentKanbanIssueIdx(currentKanbanIssueIdx + 1);\r\n      // 触发kanban组件选择card事件\r\n      board.current.selectCard(currentGroupCards[currentKanbanIssueIdx + 1]);\r\n      setIssueSearchParams({ viewMode, issueNodeId, queryId }); // 更改路由issueId\r\n    }\r\n  }\r\n\r\n  //看板上一条issue\r\n  function gotoKanbanPreviousIssue() {\r\n    if (currentKanbanIssueIdx > 0) {\r\n      let groupCards = board.current.getAreaCards(currentKanbanGroup);\r\n      const issueNodeId = groupCards[currentKanbanIssueIdx - 1]?.nodeId;\r\n      setCurrentKanbanIssueIdx(currentKanbanIssueIdx - 1);\r\n      // 触发kanban组件选择card事件\r\n      board.current.selectCard(groupCards[currentKanbanIssueIdx - 1]);\r\n      setIssueSearchParams({ viewMode, issueNodeId, queryId }); // 更改路由issueId\r\n    }\r\n  }\r\n  // 下一条\r\n  function _nextIssue() {\r\n    let groupCards = board.current.getAreaCards(currentKanbanGroup);\r\n    console.log(\"groupCards\", groupCards)\r\n    gotoKanbanNextIssue(groupCards); //传回当前列的现有长度，以让IssueHome中判定是否需要翻页加载\r\n  }\r\n\r\n  //刷新issue列表\r\n  function refreshIssueList() {\r\n    // 如果当前是第一页强制刷新，非第一页则刷新第一页\r\n    if (kanbanPageNo == 1) {\r\n      queryClient.invalidateQueries([track012])\r\n    } else {\r\n      setKanbanPageNo(1)\r\n    }\r\n    setTimeout(() => {\r\n      setRefreshingFlg(false);\r\n    }, 500);\r\n  }\r\n  // card属性\r\n  const cardShape = {\r\n    ...defaultCardShape,\r\n    label: true, description: true, progress: true, start_date: true, end_date: true,\r\n    priority: true, color: true, cover: true, attached: true,\r\n    menu: { show: false },\r\n  };\r\n\r\n  const columnShape = {\r\n    menu: {\r\n      show: true,\r\n      items: ({ column, columnIndex, store }) => {\r\n        console.log(column, columnIndex, store)\r\n        return [\r\n          { id: \"set-edit\", icon: \"wxi-edit\", label: \"Rename\" },\r\n          { id: \"delete-column\", icon: \"wxi-delete\", label: \"Delete\" },\r\n          { id: \"add-card\", icon: \"wxi-plus\", label: \"Add new card\" },\r\n          { id: \"move-column:left\", icon: \"wxi-arrow-left\", label: \"Move left\", disabled: true },\r\n          { id: \"move-column:right\", icon: \"wxi-arrow-right\", label: \"Move right\", disabled: true }\r\n        ]\r\n      }\r\n    }\r\n  };\r\n\r\n  // card 模板\r\n  function cardTemplate({ cardFields, selected, dragging, cardShape }) {\r\n    const { label, prefix, planFinishDt, icon, userName, avatar } = cardFields;\r\n    // TODO:userName和avatar接口返回，无数据\r\n    return `\r\n        <div class=\"custom-card\">\r\n          <div class=\"custom-card-content\">\r\n            <div>${icon}</div>\r\n            <a class=\"fontsize-12\">${prefix}</a>\r\n          </div>\r\n          <div>\r\n            <div class=\"issueTitle\">${label}</div>\r\n          </div>\r\n          <div class=\"issueinfo\">\r\n            <div style=\"color:#999\">\r\n              <span class=\"iconfont rili fontsize-12\"></span>\r\n              <span class=\"fontsize-12\"> ${planFinishDt ? planFinishDt : \"\"}</span>\r\n            </div>\r\n            <div class=\"user\">\r\n              <div class=\"fontsize-12\">${userName ? userName : \"\"}</div>\r\n              <div class=\"avatar\">\r\n                <img src=${avatar ? avatar : (userName ? avatarImg : noAvatarImg)}></img>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n    `;\r\n  }\r\n\r\n  // 看板滚动加载数据\r\n  function loadKanbanIssueList() {\r\n    setKanbanPageNo(kanbanPageNo + 1)\r\n  }\r\n  // 看板移动卡片\r\n  function onKanbanColumnMoved({ id, columnId }) {\r\n    const [nodeId,] = Array.isArray(id) ? id : id.split(\",\");\r\n    let request = {\r\n      teamId: teamId,\r\n      nodeId: nodeId,\r\n      [eConsoleNodeId.Nid_11110_Issue_Status]: columnId\r\n    };\r\n\r\n    httpIssue.track_007_edit_issue(request)\r\n      .then((res) => {\r\n        if (res.resultCode === 200) {\r\n          queryClient.invalidateQueries([track012])\r\n          // board.current.updateCard({\r\n          // })\r\n        }\r\n      })\r\n  }\r\n\r\n  // 选择卡片 \r\n  function onKanbanCellSelected({ id, groupMode }) {\r\n    if(!id){ // FIXME: 点击空白区域会触发该事件\r\n      return;\r\n    }\r\n    const [nodeId, group] = id.split(\",\");\r\n    if (groupMode === false) {\r\n      let idx = kanbanGroupList.find(_group => _group.type == group).issueList.findIndex(_issue => _issue.nodeId == nodeId);\r\n      setCurrentKanbanGroup(group);\r\n      setCurrentKanbanIssueIdx(idx);\r\n      setIssueDetailDrawerVisibleFlg(true);\r\n      setIssueSearchParams({ viewMode, issueNodeId: nodeId, queryId }); // 更改路由issueId\r\n    }\r\n  }\r\n\r\n  // card找当前\r\n  const findCard = (id) => {\r\n    let findItem = cards.findIndex(el => el.id == id)\r\n    return findItem\r\n  }\r\n\r\n  // 清理看板容器的函数\r\n  const cleanupKanban = () => {\r\n    const kanbanElement = document.getElementById('issuekanban');\r\n    if (kanbanElement) {\r\n      // 清理容器内容\r\n      kanbanElement.innerHTML = '';\r\n      console.log('清理看板容器内容');\r\n    }\r\n    \r\n    // 销毁看板实例\r\n    if (!isEmpty(board.current)) {\r\n      try {\r\n        board.current.destroy();\r\n        console.log('销毁看板实例');\r\n      } catch (e) {\r\n        console.log('销毁看板实例时出错:', e);\r\n      }\r\n      board.current = {};\r\n    }\r\n  };\r\n\r\n\r\n\r\n  //初始化\r\n  useEffect(() => {\r\n    if (!initLoading && kanbanTotal != 0) {\r\n      // 使用清理函数彻底清理\r\n      cleanupKanban();\r\n      \r\n      // 重新创建看板\r\n      board.current = new Kanban(\"#issuekanban\", {\r\n        columns, cards, cardShape,\r\n        cardTemplate: window.kanban.template(card => cardTemplate(card)), // 版本更新的写法\r\n        readonly: { edit: false, add: false },\r\n        columnShape: { fixedHeaders: true }\r\n        // scrollType: \"column\",\r\n      }); // columnShape,\r\n      \r\n\r\n    }\r\n    // setTimeout(()=>{\r\n    //   adjustScrollPosition();\r\n    // },500)\r\n  }, [initLoading, kanbanTotal, dataSourceKey]);\r\n\r\n  //数据绑定\r\n  useEffect(() => {\r\n    if (!initLoading) {\r\n      // 如果第一页 并且没有新增\r\n      if (kanbanPageNo == 1 && kanbanGroupList?.length > 0 && !addCard) {\r\n        const columns = []\r\n        kanbanGroupList.map((group, index) => {\r\n          if (group.name != null) {\r\n            let columnObj = {\r\n              id: group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题\r\n              label: group.name + \"(\" + group.issueTotalNum + \")\"\r\n            }\r\n            columns.push(columnObj)\r\n          }\r\n        });\r\n        // 看板卡片\r\n        let cardsList = []\r\n        kanbanGroupList.map((_group) => {\r\n          (_group.issueList || []).map(_issue => {\r\n            if (_issue.id) {\r\n              let cardObj = {\r\n                ..._issue,\r\n                column: _group.type,\r\n                id: [_issue.nodeId, _group.type].toString(), //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(\",\")解开id,type\r\n                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),\r\n                label: _issue.title,\r\n              }\r\n              cardsList.push(cardObj)\r\n            }\r\n          })\r\n        });\r\n        console.log(\"columns,cardsList\", columns, cardsList)\r\n        setCards(cardsList)\r\n        \r\n        // 如果看板已存在，使用parse更新；否则让初始化useEffect处理\r\n        if (!isEmpty(board.current) && board.current.parse) {\r\n          board.current.parse({\r\n            columns: columns,\r\n            cards: cardsList,\r\n          });\r\n          board.current.events.on(\"move-card\", onKanbanColumnMoved);\r\n          board.current.events.on(\"select-card\", onKanbanCellSelected);\r\n        }\r\n      }\r\n      // 如果第一页 并且新增，只要插入新增项\r\n      if (kanbanGroupList?.length > 0 && addCard) {\r\n        // 看板列表\r\n        const columns = []\r\n        kanbanGroupList.map((group, index) => {\r\n          if (group.name != null) {\r\n            let columnObj = {\r\n              id: group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题\r\n              label: group.name + \"(\" + group.issueTotalNum + \")\"\r\n            }\r\n            columns.push(columnObj)\r\n          }\r\n        });\r\n        kanbanGroupList.map((_group) => {\r\n          (_group.issueList || []).map(_issue => {\r\n            if (_issue.id) {\r\n              let cardObj = {\r\n                ..._issue,\r\n                column: _group.type,\r\n                id: [_issue.nodeId, _group.type].toString(), //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(\",\")解开id,type\r\n                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),\r\n                label: _issue.title,\r\n              }\r\n              let findIndex = findCard([_issue.nodeId, _group.type].toString())\r\n              // fix tmsbug-10144:看板视图下创建问题，提交之后 页面没刷新\r\n              if (findIndex == -1) {\r\n                cards.splice(0, 0, cardObj)\r\n              }\r\n            }\r\n          })\r\n        });\r\n        console.log(\"columns,cards\", columns, cards)\r\n        // parse\r\n        board.current.parse({\r\n          columns: columns,\r\n          cards: cards,\r\n        });\r\n        setAddCard(false)\r\n        setCards([...cards])\r\n      }\r\n      // 看板页码不为1，并且非新增\r\n      if (kanbanGroupList?.length > 0 && kanbanPageNo != 1 && !addCard) {\r\n        // 看板列表\r\n        const columns = []\r\n        kanbanGroupList.map((group, index) => {\r\n          if (group.name != null) {\r\n            let columnObj = {\r\n              id: group.type, //0:未开始，1:进行中，2:已修复 3:已关闭 4:不是问题\r\n              label: group.name + \"(\" + group.issueTotalNum + \")\"\r\n            }\r\n            columns.push(columnObj)\r\n          }\r\n        });\r\n        // 看板卡片\r\n        kanbanGroupList.map((_group) => {\r\n          (_group.issueList || []).map(_issue => {\r\n            if (_issue.id) {\r\n              let cardObj = {\r\n                ..._issue,\r\n                column: _group.type,\r\n                id: [_issue.nodeId, _group.type].toString(), //使用数组的方式 ,可以在onKanbanCellSelected中将参数id在split(\",\")解开id,type\r\n                icon: getIconValueByIdType(selectionList, iconSelectionLid, _issue.issueType),\r\n                label: _issue.title,\r\n              }\r\n              let findIndex = findCard([_issue.nodeId, _group.type].toString())\r\n              if (findIndex >= 0) {\r\n                cards[findIndex] = cardObj\r\n              } else {\r\n                cards.push(cardObj)\r\n              }\r\n              console.log(\"findCard(_issue.id)\", findIndex)\r\n            }\r\n          })\r\n        });\r\n        // parse\r\n        board.current.parse({\r\n          columns: columns,\r\n          cards: cards,\r\n        });\r\n        setCards([...cards])\r\n      }\r\n    }\r\n  }, [initLoading, kanbanTotal]);\r\n\r\n  // 监听搜索条件变化，触发重新初始化\r\n  useEffect(() => {\r\n    if (!initLoading) {\r\n      // 使用清理函数彻底清理\r\n      cleanupKanban();\r\n      \r\n      // 触发重新初始化\r\n      setDataSourceKey(prev => prev + 1);\r\n      console.log('搜索条件变化，触发重新初始化');\r\n    }\r\n  }, [searchQuery, keywords]);\r\n\r\n  // issue创建完成后，页面刷新\r\n  function onPostIssueCreated() {\r\n    setAddCard(true)\r\n    refreshIssueList()\r\n  }\r\n\r\n  // \r\n  function onScrollListener(e) {\r\n    console.log(\"onScrollListener start\");\r\n    let headerElem = document.querySelector(\".wx-header\");\r\n    if (headerElem && e?.target) {\r\n      headerElem.style.top = e.target.scrollTop + \"px\";\r\n    }\r\n  }\r\n\r\n async function addClick(){\r\n    return Modal.confirm({\r\n        title: \"提示\",\r\n        icon: <ExclamationCircleOutlined />,\r\n        centered: true,\r\n        content: <p>{`当前列表页为\"查询\"页，将转至“${await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)??\"全部\"}”页创建问题。`}</p>,\r\n        okText: \" 好的，继续新建\",\r\n        cancelText: \"取消\",\r\n        onOk: () => {\r\n          if(!!projectInfo?.allIssueNodeId){\r\n            navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}/kanban?createFlg=true`);\r\n          }\r\n        },\r\n        onCancel: () => {\r\n          console.log(\"Cancel\");\r\n        },\r\n      });\r\n  }\r\n\r\n  return <Content>\r\n    <div className=\"flexBetween\" style={{ height: 45 }}>\r\n      <Space size={10}>\r\n        {searchQuery?.length > 0 || keywords ?\r\n          <div style={{ paddingLeft:15, fontSize: 12, color: \"#666\" }}>结果({kanbanTotal}条)</div>\r\n          : <div style={{ paddingLeft:15,fontSize: 12, color: \"#666\" }}>全部({kanbanTotal}条)</div>\r\n        }\r\n        {/* <Button\r\n          className={refreshingFlg && 'refresh-icon'}\r\n          style={{ position: 'relative', color: '#999' }}\r\n          type=\"link\"\r\n          icon={<span className=\"refresh-position fontsize-14 iconfont shuaxin1\" />}\r\n          onClick={() => {\r\n            setRefreshingFlg(true);\r\n            refreshIssueList()\r\n          }}\r\n        /> */}\r\n        {finalCreateFlgpriPermission(setting320Result.privWrite) && (\r\n          <Button className=\"defaultBtn\" type=\"primary\" onClick={() => !!objInfo?.objId ? addClick():openCreateIssueModal()}>+ {projectInfo.issueAlias}</Button>\r\n        )}\r\n      </Space>\r\n    </div>\r\n    <div id='scrollableDiv' className=\"kanban-scroll\">\r\n      {\r\n        <InfiniteScroll\r\n              ref={scrollRef}\r\n              style={{ flex: \"auto\" }}\r\n              scrollableTarget=\"scrollableDiv\"\r\n              dataLength={kanbanPageNo * ePagination.PageSize_30}\r\n              next={(e) => loadKanbanIssueList()}\r\n              hasMore={kanbanPageNo * ePagination.PageSize_30 < kanbanMaxCount}\r\n              // onScroll={onScrollListener}\r\n              // loader={<Skeleton avatar paragraph={{ rows: 1,}} active loading={loading}/>}\r\n              // endMessage={<div>数据加载完毕!</div>}\r\n            >\r\n              {\r\n                 initLoading ? <div className=\"blank-page\"> <TLoading /></div> :\r\n                 // Issue总数为0、且初始化（页码为1）时，显示空白页\r\n                 kanbanPageNo == 1 && kanbanTotal == 0 ?\r\n                   <div\r\n                     style={{\r\n                       // textAlign: 'center',\r\n                     }}\r\n                   >\r\n                     <div className=\"blank-page\">\r\n                       <div className=\"blank-page-title\">这里是空的</div>\r\n                       {\r\n                        priPermission(setting320Result.privWrite) ?  \r\n                        <div className=\"blank-page-des fontsize-12 flexCenter\">\r\n                          <span style={{ paddingRight: 5 }}>你可以</span>\r\n                          <a onClick={() =>openCreateIssueModal()}>新建{projectInfo.issueAlias}</a>\r\n                        </div> : <></>\r\n                       }\r\n                     </div>\r\n                   </div> : <div id=\"issuekanban\" className=\"issuekanban\" style={{ minHeight: '400px', display: 'block' }} />\r\n              }\r\n            </InfiniteScroll>\r\n      }\r\n    </div>\r\n    <DraggableDrawer\r\n      destroyOnClose\r\n      className=\"kanban-detail\"\r\n      width={'60%'}\r\n      minWidth={'50%'}\r\n      maxWidth={'90%'}\r\n      onClose={() => setIssueDetailDrawerVisibleFlg(false)}\r\n      open={issueDetailDrawerVisibleFlg}\r\n      closable={true}\r\n      title={`${projectInfo.issueAlias}详情`}\r\n    >\r\n      <IssueDetail\r\n        viewMode={viewMode}\r\n        selectionList={selectionList}\r\n        userList={userList}\r\n        spaceUserList={spaceUserList}\r\n        gotoPreviousIssue={gotoKanbanPreviousIssue}\r\n        gotoNextIssue={_nextIssue}\r\n        previousIssueLinkDisabledFlg={previousIssueLinkDisabledFlg}\r\n        nextIssueLinkDisabledFlg={nextIssueLinkDisabledFlg}\r\n        projectInfo={projectInfo}\r\n        setCurrentIssueIdx={setCurrentIssueIdx}\r\n        issueList={(kanbanGroupList || [])?.find(kanban => kanban.type == currentKanbanGroup)?.issueList || []}\r\n      />\r\n    </DraggableDrawer>\r\n  </Content>\r\n}"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAC3D,SAASC,SAAS,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AACzG,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,cAAc,EAAEC,WAAW,QAAQ,oBAAoB;AAChE,OAAO,KAAKC,SAAS,MAAM,kBAAkB;AAC7C,SAASC,4BAA4B,QAAQ,mCAAmC;AAChF,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAO,oBAAoB;AAC3B,OAAOC,QAAQ,MAAM,6BAA6B;AAClD,SAASC,yBAAyB,QAAQ,mBAAmB;AAC7D,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,0BAA0B;AAC7E,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAOC,eAAe,MAAM,6BAA6B;;AAEzD;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC,gBAAgB;EAAEC;AAAwB,CAAC,GAAGC,MAAM,CAACC,MAAM;AACpF,MAAM;EAAEC;AAAQ,CAAC,GAAGpC,MAAM;AAE1B,eAAe,SAASqC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,IAAA,EAAAC,SAAA;EACpC,MAAMC,KAAK,GAAG9C,MAAM,CAAC,CAAC,CAAC,CAAC;EACxB,MAAM+C,SAAS,GAAG/C,MAAM,CAAC,CAAC;EAC1B,MAAM;IAAEgD,MAAM;IAAEC,MAAM,EAAEC;EAAgB,CAAC,GAAG3C,SAAS,CAAC,CAAC;EACvD,MAAM4C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,eAAe,CAAC,CAAC;EACzD,MAAM4C,SAAS,GAAGF,YAAY,CAACG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;EACjD,MAAMC,IAAI,GAAGJ,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;;EAEvC,MAAM;IAAEE,gBAAgB;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,aAAa;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAG7D,gBAAgB,CAAC,CAAC;EACpM,MAAM8D,WAAW,GAAGzD,cAAc,CAAC,CAAC;EACpC,MAAM;IAAE0D,WAAW;IAAEC,OAAO;IAAEC;EAAqB,CAAC,GAAGnD,oBAAoB,CAAC,CAAC,CAAC;;EAE9E;EACA,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,MAAM;IAAE2E,WAAW,GAAG,CAAC;IAAEC,cAAc,GAAG,CAAC;IAAEC,eAAe,GAAG,EAAE;IAAEC,gBAAgB;IAAEC,SAAS,EAAEC;EAAsB,CAAC,GAAG/D,4BAA4B,CAAC8B,MAAM,EAAEE,eAAe,EAAE,EAAE,EAAE,EAAE,EAAEc,WAAW,EAAED,QAAQ,EAAEW,YAAY,EAAE1D,WAAW,CAACkE,WAAW,CAAC;EACrP,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACoF,4BAA4B,EAAEC,+BAA+B,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACvF,MAAM,CAACsF,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACwF,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0F,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACvE,MAAM,CAAC4F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC8F,OAAO,EAAEC,UAAU,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgG,KAAK,EAAEC,QAAQ,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EACtC;EACA,MAAM,CAACkG,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACrF,MAAM,CAACoG,WAAW,EAAEC,cAAc,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACtD,MAAM,CAACsG,aAAa,EAAEC,gBAAgB,CAAC,GAAGvG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvDwG,OAAO,CAACC,GAAG,CAAC,6EAA6E,EAAE9B,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEJ,YAAY,EAAE2B,WAAW,EAAEJ,KAAK,CAAC;;EAE1K;;EAEA;EACA;EACAlG,SAAS,CAAC,MAAM;IACd,IAAI,CAACyB,OAAO,CAACyE,KAAK,CAAC,IAAI,CAAC,CAAC1B,WAAW,EAAE;MACpCoC,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACV,KAAK,EAAE1B,WAAW,CAAC,CAAC;;EAExB;EACAxE,SAAS,CAAC,MAAM;IACd,IAAI,CAACkF,qBAAqB,EAAE;MAC1BqB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACrB,qBAAqB,CAAC,CAAC;EAE3BlF,SAAS,CAAC,MAAM;IACd,IAAIgD,SAAS,CAAC6D,OAAO,EAAE;MAAA,IAAAC,kBAAA,EAAAC,qBAAA;MACrB/D,SAAS,aAATA,SAAS,wBAAA8D,kBAAA,GAAT9D,SAAS,CAAE6D,OAAO,cAAAC,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBE,EAAE,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,gBAAgB,CAAC,QAAQ,EAAEC,gBAAgB,CAAC;IACtE;IACA,OAAO,MAAM;MAAA,IAAAC,mBAAA,EAAAC,qBAAA;MACXpE,SAAS,aAATA,SAAS,wBAAAmE,mBAAA,GAATnE,SAAS,CAAE6D,OAAO,cAAAM,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBH,EAAE,cAAAI,qBAAA,uBAAtBA,qBAAA,CAAwBC,mBAAmB,CAAC,QAAQ,EAAEH,gBAAgB,CAAC;IACzE,CAAC;EACH,CAAC,EAAE,CAAClE,SAAS,CAAC,CAAC;EAGfhD,SAAS,CAAC,MAAI;IACZ,IAAGwD,SAAS,EAAC;MACXkB,oBAAoB,CAAC;QAACD,OAAO,EAAE,CAAC;MAAC,CAAC,CAAC;MACnC6C,UAAU,CAAC,MAAI;QACb;QACA;QACDC,oBAAoB,CAAC,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAC,CAAC/D,SAAS,CAAC,CAAC;EAEd,SAAS+D,oBAAoBA,CAAA,EAAI;IAC/BzF,cAAc,CAAC0F,IAAI,CAAC,2BAA2B,EAAE,EAAE,EAAE;MACnDtE,MAAM,EAAEC,eAAe;MAAEgB,WAAW;MAAEsD,QAAQ,EAAEC;IAClD,CAAC,CAAC;EACJ;EAEA,SAASd,iBAAiBA,CAAA,EAAG;IAC3B,IAAI;MACF,MAAM;QAAEe,GAAG;QAAElF;MAAO,CAAC,GAAGmF,oBAAoB,CAAC1B,KAAK,EAAE1B,WAAW,CAAC;MAChEkC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAElE,MAAM,EAAEkF,GAAG,CAAC;MAClC,IAAG,CAAC,CAAClF,MAAM,EAAC;QACV8C,+BAA+B,CAACoC,GAAG,IAAI,CAAC,CAAC;QACzClC,2BAA2B,CAACkC,GAAG,IAAIlF,MAAM,CAACoF,aAAa,GAAG,CAAC,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVpB,OAAO,CAACqB,KAAK,CAAC,mBAAmB,EAAED,CAAC,CAAC;IACvC;EACF;;EAEA;EACA,SAASF,oBAAoBA,CAAC1B,KAAK,EAAE1B,WAAW,EAAE;IAChD,MAAMmD,GAAG,GAAGzB,KAAK,CAAC8B,SAAS,CAACC,MAAM,IAAIA,MAAM,CAAC/E,MAAM,IAAIsB,WAAW,CAAC;IACnE,MAAM/B,MAAM,GAAGyD,KAAK,CAACgC,IAAI,CAACD,MAAM,IAAIA,MAAM,CAAC/E,MAAM,IAAIsB,WAAW,CAAC;IACjE,OAAO;MAAEmD,GAAG;MAAElF;IAAO,CAAC;EACxB;EAEA,SAAS0F,mBAAmBA,CAACC,iBAAiB,EAAE;IAC9C;IACA,IAAIC,uBAAuB,GAAGD,iBAAiB,CAACE,MAAM;IACtD5B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEf,qBAAqB,EAAEyC,uBAAuB,CAAC;IACpF;IACA,IAAIzC,qBAAqB,IAAIyC,uBAAuB,GAAG,CAAC,EAAE;MACxD,IAAIE,aAAa,GAAGxD,eAAe,CAACmD,IAAI,CAACM,KAAK,IAAIA,KAAK,CAACC,IAAI,IAAI3C,kBAAkB,CAAC,CAAC+B,aAAa;MACjGnB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE4B,aAAa,CAAC;MAC3C,IAAIF,uBAAuB,GAAGE,aAAa,EAAE;QAAE;QAC7CG,mBAAmB,CAAC,CAAC;QACrB7C,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;MACrD;IACF,CAAC,MAAM;MAAA,IAAA+C,kBAAA;MACLjC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEyB,iBAAiB,CAACxC,qBAAqB,GAAG,CAAC,CAAC,CAAC;MAC/F,MAAMpB,WAAW,IAAAmE,kBAAA,GAAGP,iBAAiB,CAACxC,qBAAqB,GAAG,CAAC,CAAC,cAAA+C,kBAAA,uBAA5CA,kBAAA,CAA8CzF,MAAM;MACxE2C,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;MACnD;MACA7C,KAAK,CAAC8D,OAAO,CAAC+B,UAAU,CAACR,iBAAiB,CAACxC,qBAAqB,GAAG,CAAC,CAAC,CAAC;MACtElB,oBAAoB,CAAC;QAAER,QAAQ;QAAEM,WAAW;QAAEC;MAAQ,CAAC,CAAC,CAAC,CAAC;IAC5D;EACF;;EAEA;EACA,SAASoE,uBAAuBA,CAAA,EAAG;IACjC,IAAIjD,qBAAqB,GAAG,CAAC,EAAE;MAAA,IAAAkD,WAAA;MAC7B,IAAIC,UAAU,GAAGhG,KAAK,CAAC8D,OAAO,CAACmC,YAAY,CAAClD,kBAAkB,CAAC;MAC/D,MAAMtB,WAAW,IAAAsE,WAAA,GAAGC,UAAU,CAACnD,qBAAqB,GAAG,CAAC,CAAC,cAAAkD,WAAA,uBAArCA,WAAA,CAAuC5F,MAAM;MACjE2C,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;MACnD;MACA7C,KAAK,CAAC8D,OAAO,CAAC+B,UAAU,CAACG,UAAU,CAACnD,qBAAqB,GAAG,CAAC,CAAC,CAAC;MAC/DlB,oBAAoB,CAAC;QAAER,QAAQ;QAAEM,WAAW;QAAEC;MAAQ,CAAC,CAAC,CAAC,CAAC;IAC5D;EACF;EACA;EACA,SAASwE,UAAUA,CAAA,EAAG;IACpB,IAAIF,UAAU,GAAGhG,KAAK,CAAC8D,OAAO,CAACmC,YAAY,CAAClD,kBAAkB,CAAC;IAC/DY,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEoC,UAAU,CAAC;IACrCZ,mBAAmB,CAACY,UAAU,CAAC,CAAC,CAAC;EACnC;;EAEA;EACA,SAASG,gBAAgBA,CAAA,EAAG;IAC1B;IACA,IAAIvE,YAAY,IAAI,CAAC,EAAE;MACrBJ,WAAW,CAAC4E,iBAAiB,CAAC,CAAC3H,QAAQ,CAAC,CAAC;IAC3C,CAAC,MAAM;MACLoD,eAAe,CAAC,CAAC,CAAC;IACpB;IACA0C,UAAU,CAAC,MAAM;MACfjC,gBAAgB,CAAC,KAAK,CAAC;IACzB,CAAC,EAAE,GAAG,CAAC;EACT;EACA;EACA,MAAM+D,SAAS,GAAG;IAChB,GAAG9G,gBAAgB;IACnB+G,KAAK,EAAE,IAAI;IAAEC,WAAW,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAChFC,QAAQ,EAAE,IAAI;IAAEC,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAE,IAAI;IACxDC,IAAI,EAAE;MAAEC,IAAI,EAAE;IAAM;EACtB,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBF,IAAI,EAAE;MACJC,IAAI,EAAE,IAAI;MACVE,KAAK,EAAEA,CAAC;QAAEC,MAAM;QAAEC,WAAW;QAAEC;MAAM,CAAC,KAAK;QACzC1D,OAAO,CAACC,GAAG,CAACuD,MAAM,EAAEC,WAAW,EAAEC,KAAK,CAAC;QACvC,OAAO,CACL;UAAEC,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,UAAU;UAAEjB,KAAK,EAAE;QAAS,CAAC,EACrD;UAAEgB,EAAE,EAAE,eAAe;UAAEC,IAAI,EAAE,YAAY;UAAEjB,KAAK,EAAE;QAAS,CAAC,EAC5D;UAAEgB,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,UAAU;UAAEjB,KAAK,EAAE;QAAe,CAAC,EAC3D;UAAEgB,EAAE,EAAE,kBAAkB;UAAEC,IAAI,EAAE,gBAAgB;UAAEjB,KAAK,EAAE,WAAW;UAAEkB,QAAQ,EAAE;QAAK,CAAC,EACtF;UAAEF,EAAE,EAAE,mBAAmB;UAAEC,IAAI,EAAE,iBAAiB;UAAEjB,KAAK,EAAE,YAAY;UAAEkB,QAAQ,EAAE;QAAK,CAAC,CAC1F;MACH;IACF;EACF,CAAC;;EAED;EACA,SAASC,YAAYA,CAAC;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,QAAQ;IAAEvB;EAAU,CAAC,EAAE;IACnE,MAAM;MAAEC,KAAK;MAAEuB,MAAM;MAAEC,YAAY;MAAEP,IAAI;MAAEQ,QAAQ;MAAEC;IAAO,CAAC,GAAGN,UAAU;IAC1E;IACA,OAAO;AACX;AACA;AACA,mBAAmBH,IAAI;AACvB,qCAAqCM,MAAM;AAC3C;AACA;AACA,sCAAsCvB,KAAK;AAC3C;AACA;AACA;AACA;AACA,2CAA2CwB,YAAY,GAAGA,YAAY,GAAG,EAAE;AAC3E;AACA;AACA,yCAAyCC,QAAQ,GAAGA,QAAQ,GAAG,EAAE;AACjE;AACA,2BAA2BC,MAAM,GAAGA,MAAM,GAAID,QAAQ,GAAG1J,SAAS,GAAGC,WAAY;AACjF;AACA;AACA;AACA;AACA,KAAK;EACH;;EAEA;EACA,SAASqH,mBAAmBA,CAAA,EAAG;IAC7B9D,eAAe,CAACD,YAAY,GAAG,CAAC,CAAC;EACnC;EACA;EACA,SAASqG,mBAAmBA,CAAC;IAAEX,EAAE;IAAEY;EAAS,CAAC,EAAE;IAC7C,MAAM,CAAC/H,MAAM,CAAE,GAAGgI,KAAK,CAACC,OAAO,CAACd,EAAE,CAAC,GAAGA,EAAE,GAAGA,EAAE,CAACe,KAAK,CAAC,GAAG,CAAC;IACxD,IAAIC,OAAO,GAAG;MACZpI,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA,MAAM;MACd,CAAClC,cAAc,CAACsK,sBAAsB,GAAGL;IAC3C,CAAC;IAED/J,SAAS,CAACqK,oBAAoB,CAACF,OAAO,CAAC,CACpCG,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1BnH,WAAW,CAAC4E,iBAAiB,CAAC,CAAC3H,QAAQ,CAAC,CAAC;QACzC;QACA;MACF;IACF,CAAC,CAAC;EACN;;EAEA;EACA,SAASmK,oBAAoBA,CAAC;IAAEtB,EAAE;IAAEuB;EAAU,CAAC,EAAE;IAC/C,IAAG,CAACvB,EAAE,EAAC;MAAE;MACP;IACF;IACA,MAAM,CAACnH,MAAM,EAAEsF,KAAK,CAAC,GAAG6B,EAAE,CAACe,KAAK,CAAC,GAAG,CAAC;IACrC,IAAIQ,SAAS,KAAK,KAAK,EAAE;MACvB,IAAIjE,GAAG,GAAG5C,eAAe,CAACmD,IAAI,CAAC2D,MAAM,IAAIA,MAAM,CAACpD,IAAI,IAAID,KAAK,CAAC,CAACsD,SAAS,CAAC9D,SAAS,CAACC,MAAM,IAAIA,MAAM,CAAC/E,MAAM,IAAIA,MAAM,CAAC;MACrH6C,qBAAqB,CAACyC,KAAK,CAAC;MAC5B3C,wBAAwB,CAAC8B,GAAG,CAAC;MAC7BtB,8BAA8B,CAAC,IAAI,CAAC;MACpC3B,oBAAoB,CAAC;QAAER,QAAQ;QAAEM,WAAW,EAAEtB,MAAM;QAAEuB;MAAQ,CAAC,CAAC,CAAC,CAAC;IACpE;EACF;;EAEA;EACA,MAAMsH,QAAQ,GAAI1B,EAAE,IAAK;IACvB,IAAI2B,QAAQ,GAAG9F,KAAK,CAAC8B,SAAS,CAAChB,EAAE,IAAIA,EAAE,CAACqD,EAAE,IAAIA,EAAE,CAAC;IACjD,OAAO2B,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC5D,IAAIF,aAAa,EAAE;MACjB;MACAA,aAAa,CAACG,SAAS,GAAG,EAAE;MAC5B3F,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACzB;;IAEA;IACA,IAAI,CAAClF,OAAO,CAACsB,KAAK,CAAC8D,OAAO,CAAC,EAAE;MAC3B,IAAI;QACF9D,KAAK,CAAC8D,OAAO,CAACyF,OAAO,CAAC,CAAC;QACvB5F,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACvB,CAAC,CAAC,OAAOmB,CAAC,EAAE;QACVpB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEmB,CAAC,CAAC;MAC9B;MACA/E,KAAK,CAAC8D,OAAO,GAAG,CAAC,CAAC;IACpB;EACF,CAAC;;EAID;EACA7G,SAAS,CAAC,MAAM;IACd,IAAI,CAACsG,WAAW,IAAIzB,WAAW,IAAI,CAAC,EAAE;MACpC;MACAoH,aAAa,CAAC,CAAC;;MAEf;MACAlJ,KAAK,CAAC8D,OAAO,GAAG,IAAIzE,MAAM,CAAC,cAAc,EAAE;QACzC4D,OAAO;QAAEE,KAAK;QAAEkD,SAAS;QACzBoB,YAAY,EAAEhI,MAAM,CAACC,MAAM,CAAC8J,QAAQ,CAACC,IAAI,IAAIhC,YAAY,CAACgC,IAAI,CAAC,CAAC;QAAE;QAClEC,QAAQ,EAAE;UAAEC,IAAI,EAAE,KAAK;UAAEC,GAAG,EAAE;QAAM,CAAC;QACrC3C,WAAW,EAAE;UAAE4C,YAAY,EAAE;QAAK;QAClC;MACF,CAAC,CAAC,CAAC,CAAC;IAGN;IACA;IACA;IACA;EACF,CAAC,EAAE,CAACtG,WAAW,EAAEzB,WAAW,EAAE2B,aAAa,CAAC,CAAC;;EAE7C;EACAxG,SAAS,CAAC,MAAM;IACd,IAAI,CAACsG,WAAW,EAAE;MAChB;MACA,IAAI3B,YAAY,IAAI,CAAC,IAAI,CAAAI,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuD,MAAM,IAAG,CAAC,IAAI,CAAC5C,OAAO,EAAE;QAChE,MAAMM,OAAO,GAAG,EAAE;QAClBjB,eAAe,CAAC8H,GAAG,CAAC,CAACrE,KAAK,EAAEsE,KAAK,KAAK;UACpC,IAAItE,KAAK,CAACuE,IAAI,IAAI,IAAI,EAAE;YACtB,IAAIC,SAAS,GAAG;cACd3C,EAAE,EAAE7B,KAAK,CAACC,IAAI;cAAE;cAChBY,KAAK,EAAEb,KAAK,CAACuE,IAAI,GAAG,GAAG,GAAGvE,KAAK,CAACX,aAAa,GAAG;YAClD,CAAC;YACD7B,OAAO,CAACiH,IAAI,CAACD,SAAS,CAAC;UACzB;QACF,CAAC,CAAC;QACF;QACA,IAAIE,SAAS,GAAG,EAAE;QAClBnI,eAAe,CAAC8H,GAAG,CAAEhB,MAAM,IAAK;UAC9B,CAACA,MAAM,CAACC,SAAS,IAAI,EAAE,EAAEe,GAAG,CAAC5E,MAAM,IAAI;YACrC,IAAIA,MAAM,CAACoC,EAAE,EAAE;cACb,IAAI8C,OAAO,GAAG;gBACZ,GAAGlF,MAAM;gBACTiC,MAAM,EAAE2B,MAAM,CAACpD,IAAI;gBACnB4B,EAAE,EAAE,CAACpC,MAAM,CAAC/E,MAAM,EAAE2I,MAAM,CAACpD,IAAI,CAAC,CAAC2E,QAAQ,CAAC,CAAC;gBAAE;gBAC7C9C,IAAI,EAAEvJ,oBAAoB,CAAC+C,aAAa,EAAEkB,gBAAgB,EAAEiD,MAAM,CAACoF,SAAS,CAAC;gBAC7EhE,KAAK,EAAEpB,MAAM,CAACqF;cAChB,CAAC;cACDJ,SAAS,CAACD,IAAI,CAACE,OAAO,CAAC;YACzB;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;QACFzG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEX,OAAO,EAAEkH,SAAS,CAAC;QACpD/G,QAAQ,CAAC+G,SAAS,CAAC;;QAEnB;QACA,IAAI,CAACzL,OAAO,CAACsB,KAAK,CAAC8D,OAAO,CAAC,IAAI9D,KAAK,CAAC8D,OAAO,CAAC0G,KAAK,EAAE;UAClDxK,KAAK,CAAC8D,OAAO,CAAC0G,KAAK,CAAC;YAClBvH,OAAO,EAAEA,OAAO;YAChBE,KAAK,EAAEgH;UACT,CAAC,CAAC;UACFnK,KAAK,CAAC8D,OAAO,CAAC2G,MAAM,CAACC,EAAE,CAAC,WAAW,EAAEzC,mBAAmB,CAAC;UACzDjI,KAAK,CAAC8D,OAAO,CAAC2G,MAAM,CAACC,EAAE,CAAC,aAAa,EAAE9B,oBAAoB,CAAC;QAC9D;MACF;MACA;MACA,IAAI,CAAA5G,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuD,MAAM,IAAG,CAAC,IAAI5C,OAAO,EAAE;QAC1C;QACA,MAAMM,OAAO,GAAG,EAAE;QAClBjB,eAAe,CAAC8H,GAAG,CAAC,CAACrE,KAAK,EAAEsE,KAAK,KAAK;UACpC,IAAItE,KAAK,CAACuE,IAAI,IAAI,IAAI,EAAE;YACtB,IAAIC,SAAS,GAAG;cACd3C,EAAE,EAAE7B,KAAK,CAACC,IAAI;cAAE;cAChBY,KAAK,EAAEb,KAAK,CAACuE,IAAI,GAAG,GAAG,GAAGvE,KAAK,CAACX,aAAa,GAAG;YAClD,CAAC;YACD7B,OAAO,CAACiH,IAAI,CAACD,SAAS,CAAC;UACzB;QACF,CAAC,CAAC;QACFjI,eAAe,CAAC8H,GAAG,CAAEhB,MAAM,IAAK;UAC9B,CAACA,MAAM,CAACC,SAAS,IAAI,EAAE,EAAEe,GAAG,CAAC5E,MAAM,IAAI;YACrC,IAAIA,MAAM,CAACoC,EAAE,EAAE;cACb,IAAI8C,OAAO,GAAG;gBACZ,GAAGlF,MAAM;gBACTiC,MAAM,EAAE2B,MAAM,CAACpD,IAAI;gBACnB4B,EAAE,EAAE,CAACpC,MAAM,CAAC/E,MAAM,EAAE2I,MAAM,CAACpD,IAAI,CAAC,CAAC2E,QAAQ,CAAC,CAAC;gBAAE;gBAC7C9C,IAAI,EAAEvJ,oBAAoB,CAAC+C,aAAa,EAAEkB,gBAAgB,EAAEiD,MAAM,CAACoF,SAAS,CAAC;gBAC7EhE,KAAK,EAAEpB,MAAM,CAACqF;cAChB,CAAC;cACD,IAAItF,SAAS,GAAG+D,QAAQ,CAAC,CAAC9D,MAAM,CAAC/E,MAAM,EAAE2I,MAAM,CAACpD,IAAI,CAAC,CAAC2E,QAAQ,CAAC,CAAC,CAAC;cACjE;cACA,IAAIpF,SAAS,IAAI,CAAC,CAAC,EAAE;gBACnB9B,KAAK,CAACwH,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEP,OAAO,CAAC;cAC7B;YACF;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;QACFzG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEX,OAAO,EAAEE,KAAK,CAAC;QAC5C;QACAnD,KAAK,CAAC8D,OAAO,CAAC0G,KAAK,CAAC;UAClBvH,OAAO,EAAEA,OAAO;UAChBE,KAAK,EAAEA;QACT,CAAC,CAAC;QACFP,UAAU,CAAC,KAAK,CAAC;QACjBQ,QAAQ,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC;MACtB;MACA;MACA,IAAI,CAAAnB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuD,MAAM,IAAG,CAAC,IAAI3D,YAAY,IAAI,CAAC,IAAI,CAACe,OAAO,EAAE;QAChE;QACA,MAAMM,OAAO,GAAG,EAAE;QAClBjB,eAAe,CAAC8H,GAAG,CAAC,CAACrE,KAAK,EAAEsE,KAAK,KAAK;UACpC,IAAItE,KAAK,CAACuE,IAAI,IAAI,IAAI,EAAE;YACtB,IAAIC,SAAS,GAAG;cACd3C,EAAE,EAAE7B,KAAK,CAACC,IAAI;cAAE;cAChBY,KAAK,EAAEb,KAAK,CAACuE,IAAI,GAAG,GAAG,GAAGvE,KAAK,CAACX,aAAa,GAAG;YAClD,CAAC;YACD7B,OAAO,CAACiH,IAAI,CAACD,SAAS,CAAC;UACzB;QACF,CAAC,CAAC;QACF;QACAjI,eAAe,CAAC8H,GAAG,CAAEhB,MAAM,IAAK;UAC9B,CAACA,MAAM,CAACC,SAAS,IAAI,EAAE,EAAEe,GAAG,CAAC5E,MAAM,IAAI;YACrC,IAAIA,MAAM,CAACoC,EAAE,EAAE;cACb,IAAI8C,OAAO,GAAG;gBACZ,GAAGlF,MAAM;gBACTiC,MAAM,EAAE2B,MAAM,CAACpD,IAAI;gBACnB4B,EAAE,EAAE,CAACpC,MAAM,CAAC/E,MAAM,EAAE2I,MAAM,CAACpD,IAAI,CAAC,CAAC2E,QAAQ,CAAC,CAAC;gBAAE;gBAC7C9C,IAAI,EAAEvJ,oBAAoB,CAAC+C,aAAa,EAAEkB,gBAAgB,EAAEiD,MAAM,CAACoF,SAAS,CAAC;gBAC7EhE,KAAK,EAAEpB,MAAM,CAACqF;cAChB,CAAC;cACD,IAAItF,SAAS,GAAG+D,QAAQ,CAAC,CAAC9D,MAAM,CAAC/E,MAAM,EAAE2I,MAAM,CAACpD,IAAI,CAAC,CAAC2E,QAAQ,CAAC,CAAC,CAAC;cACjE,IAAIpF,SAAS,IAAI,CAAC,EAAE;gBAClB9B,KAAK,CAAC8B,SAAS,CAAC,GAAGmF,OAAO;cAC5B,CAAC,MAAM;gBACLjH,KAAK,CAAC+G,IAAI,CAACE,OAAO,CAAC;cACrB;cACAzG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEqB,SAAS,CAAC;YAC/C;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;QACF;QACAjF,KAAK,CAAC8D,OAAO,CAAC0G,KAAK,CAAC;UAClBvH,OAAO,EAAEA,OAAO;UAChBE,KAAK,EAAEA;QACT,CAAC,CAAC;QACFC,QAAQ,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE,CAACI,WAAW,EAAEzB,WAAW,CAAC,CAAC;;EAE9B;EACA7E,SAAS,CAAC,MAAM;IACd,IAAI,CAACsG,WAAW,EAAE;MAChB;MACA2F,aAAa,CAAC,CAAC;;MAEf;MACAxF,gBAAgB,CAACkH,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MAClCjH,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC/B;EACF,CAAC,EAAE,CAAC1C,WAAW,EAAED,QAAQ,CAAC,CAAC;;EAE3B;EACA,SAAS0D,kBAAkBA,CAAA,EAAG;IAC5B/B,UAAU,CAAC,IAAI,CAAC;IAChBuD,gBAAgB,CAAC,CAAC;EACpB;;EAEA;EACA,SAAShC,gBAAgBA,CAACY,CAAC,EAAE;IAC3BpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAIiH,UAAU,GAAGzB,QAAQ,CAAC0B,aAAa,CAAC,YAAY,CAAC;IACrD,IAAID,UAAU,IAAI9F,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEgG,MAAM,EAAE;MAC3BF,UAAU,CAACG,KAAK,CAACC,GAAG,GAAGlG,CAAC,CAACgG,MAAM,CAACG,SAAS,GAAG,IAAI;IAClD;EACF;EAED,eAAeC,QAAQA,CAAA,EAAE;IAAA,IAAAC,qBAAA;IACtB,OAAO5N,KAAK,CAAC6N,OAAO,CAAC;MACjBd,KAAK,EAAE,IAAI;MACXhD,IAAI,eAAErI,OAAA,CAACN,yBAAyB;QAAA0M,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,QAAQ,EAAE,IAAI;MACdC,OAAO,eAAEzM,OAAA;QAAA0M,QAAA,EAAI,oBAAAR,qBAAA,GAAmB,MAAMtM,mBAAmB,CAACoB,MAAM,EAAEkB,WAAW,CAACyK,cAAc,CAAC,cAAAT,qBAAA,cAAAA,qBAAA,GAAE,IAAI;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;MACjHK,MAAM,EAAE,UAAU;MAClBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEA,CAAA,KAAM;QACV,IAAG,CAAC,EAAC5K,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEyK,cAAc,GAAC;UAC/BxL,QAAQ,CAAC,SAASH,MAAM,WAAWkB,WAAW,CAACyK,cAAc,wBAAwB,CAAC;QACxF;MACF,CAAC;MACDI,QAAQ,EAAEA,CAAA,KAAM;QACdtI,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC,CAAC;EACN;EAEA,oBAAO1E,OAAA,CAACS,OAAO;IAAAiM,QAAA,gBACb1M,OAAA;MAAKgN,SAAS,EAAC,aAAa;MAAClB,KAAK,EAAE;QAAEmB,MAAM,EAAE;MAAG,CAAE;MAAAP,QAAA,eACjD1M,OAAA,CAAC5B,KAAK;QAAC8O,IAAI,EAAE,EAAG;QAAAR,QAAA,GACb,CAAA1K,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqE,MAAM,IAAG,CAAC,IAAItE,QAAQ,gBAClC/B,OAAA;UAAK8L,KAAK,EAAE;YAAEqB,WAAW,EAAC,EAAE;YAAEC,QAAQ,EAAE,EAAE;YAAE1F,KAAK,EAAE;UAAO,CAAE;UAAAgF,QAAA,GAAC,eAAG,EAAC9J,WAAW,EAAC,SAAE;QAAA;UAAAwJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBACnFvM,OAAA;UAAK8L,KAAK,EAAE;YAAEqB,WAAW,EAAC,EAAE;YAACC,QAAQ,EAAE,EAAE;YAAE1F,KAAK,EAAE;UAAO,CAAE;UAAAgF,QAAA,GAAC,eAAG,EAAC9J,WAAW,EAAC,SAAE;QAAA;UAAAwJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAYvFc,2BAA2B,CAACjL,gBAAgB,CAACkL,SAAS,CAAC,iBACtDtN,OAAA,CAAC9B,MAAM;UAAC8O,SAAS,EAAC,YAAY;UAACxG,IAAI,EAAC,SAAS;UAAC+G,OAAO,EAAEA,CAAA,KAAM,CAAC,EAACzL,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE0L,KAAK,IAAGvB,QAAQ,CAAC,CAAC,GAAC3G,oBAAoB,CAAC,CAAE;UAAAoH,QAAA,GAAC,IAAE,EAACxK,WAAW,CAACuL,UAAU;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CACtJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNvM,OAAA;MAAKoI,EAAE,EAAC,eAAe;MAAC4E,SAAS,EAAC,eAAe;MAAAN,QAAA,eAE7C1M,OAAA,CAACpB,cAAc;QACT8O,GAAG,EAAE3M,SAAU;QACf+K,KAAK,EAAE;UAAE6B,IAAI,EAAE;QAAO,CAAE;QACxBC,gBAAgB,EAAC,eAAe;QAChCC,UAAU,EAAEnL,YAAY,GAAG1D,WAAW,CAACkE,WAAY;QACnD4K,IAAI,EAAGjI,CAAC,IAAKY,mBAAmB,CAAC,CAAE;QACnCsH,OAAO,EAAErL,YAAY,GAAG1D,WAAW,CAACkE,WAAW,GAAGL;QAClD;QACA;QACA;QAAA;QAAA6J,QAAA,EAGGrI,WAAW,gBAAGrE,OAAA;UAAKgN,SAAS,EAAC,YAAY;UAAAN,QAAA,GAAC,GAAC,eAAA1M,OAAA,CAACP,QAAQ;YAAA2M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;QAC7D;QACA7J,YAAY,IAAI,CAAC,IAAIE,WAAW,IAAI,CAAC,gBACnC5C,OAAA;UACE8L,KAAK,EAAE;YACL;UAAA,CACA;UAAAY,QAAA,eAEF1M,OAAA;YAAKgN,SAAS,EAAC,YAAY;YAAAN,QAAA,gBACzB1M,OAAA;cAAKgN,SAAS,EAAC,kBAAkB;cAAAN,QAAA,EAAC;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAE5C5M,aAAa,CAACyC,gBAAgB,CAACkL,SAAS,CAAC,gBACzCtN,OAAA;cAAKgN,SAAS,EAAC,uCAAuC;cAAAN,QAAA,gBACpD1M,OAAA;gBAAM8L,KAAK,EAAE;kBAAEkC,YAAY,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,EAAC;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CvM,OAAA;gBAAGuN,OAAO,EAAEA,CAAA,KAAKjI,oBAAoB,CAAC,CAAE;gBAAAoH,QAAA,GAAC,cAAE,EAACxK,WAAW,CAACuL,UAAU;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,gBAAGvM,OAAA,CAAAE,SAAA,mBAAI,CAAC;UAAA;YAAAkM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAAGvM,OAAA;UAAKoI,EAAE,EAAC,aAAa;UAAC4E,SAAS,EAAC,aAAa;UAAClB,KAAK,EAAE;YAAEmC,SAAS,EAAE,OAAO;YAAEC,OAAO,EAAE;UAAQ;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEjG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEpB,CAAC,eACNvM,OAAA,CAACF,eAAe;MACdqO,cAAc;MACdnB,SAAS,EAAC,eAAe;MACzBoB,KAAK,EAAE,KAAM;MACbC,QAAQ,EAAE,KAAM;MAChBC,QAAQ,EAAE,KAAM;MAChBC,OAAO,EAAEA,CAAA,KAAMnK,8BAA8B,CAAC,KAAK,CAAE;MACrD3C,IAAI,EAAE0C,2BAA4B;MAClCqK,QAAQ,EAAE,IAAK;MACfnD,KAAK,EAAE,GAAGnJ,WAAW,CAACuL,UAAU,IAAK;MAAAf,QAAA,eAErC1M,OAAA,CAACX,WAAW;QACV4C,QAAQ,EAAEA,QAAS;QACnBJ,aAAa,EAAEA,aAAc;QAC7BF,QAAQ,EAAEA,QAAS;QACnBC,aAAa,EAAEA,aAAc;QAC7B6M,iBAAiB,EAAE7H,uBAAwB;QAC3C8H,aAAa,EAAE1H,UAAW;QAC1B3D,4BAA4B,EAAEA,4BAA6B;QAC3DE,wBAAwB,EAAEA,wBAAyB;QACnDrB,WAAW,EAAEA,WAAY;QACzBC,kBAAkB,EAAEA,kBAAmB;QACvC0H,SAAS,EAAE,EAAAjJ,IAAA,GAACkC,eAAe,IAAI,EAAE,cAAAlC,IAAA,wBAAAC,SAAA,GAAtBD,IAAA,CAAyBqF,IAAI,CAACzF,MAAM,IAAIA,MAAM,CAACgG,IAAI,IAAI3C,kBAAkB,CAAC,cAAAhD,SAAA,uBAA1EA,SAAA,CAA4EgJ,SAAS,KAAI;MAAG;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AACZ;AAAC5L,EAAA,CAriBuBD,WAAW;EAAA,QAGWnC,SAAS,EACpCE,WAAW,EACXC,WAAW,EACYC,eAAe,EAI2HH,gBAAgB,EAC9KK,cAAc,EACqBS,oBAAoB,EAI+CJ,4BAA4B;AAAA;AAAAyP,EAAA,GAhBhIjO,WAAW;AAAA,IAAAiO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}