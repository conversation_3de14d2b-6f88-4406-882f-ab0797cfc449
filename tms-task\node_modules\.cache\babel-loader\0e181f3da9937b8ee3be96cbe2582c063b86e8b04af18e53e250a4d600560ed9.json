{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\quickAcess\\\\views\\\\Search\\\\SearchDetail\\\\SearchEditTable.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\n/* eslint-disable jsx-a11y/anchor-is-valid */\n/* eslint-disable react-hooks/exhaustive-deps */\n/*\r\n * @Author: Walt <EMAIL>\r\n * @Date: 2023-03-10 10:46:54\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2025-01-23 19:07:27\r\n * @Description: 高级搜索编辑表格\r\n */\nimport { DeleteOutlined, MinusOutlined, PlusOutlined, EditOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined, ArrowUpOutlined, ArrowDownOutlined, MenuOutlined } from \"@ant-design/icons\";\nimport { CRITERIA_RELOP_TYPE_LIST, eConsoleUiControl, CRITERIA_RELOP_TYPE, eDynamicConditionNodeId, eDynamicCondition, eQueryType, eEnTag, eMoveType } from \"@common/utils/enum\";\nimport * as toolUtil from \"@common/utils/toolUtil\";\nimport { Button, DatePicker, Form, Input, Space, Select, Table } from \"antd\";\nimport moment from \"moment\";\nimport { nanoid } from \"nanoid\";\nimport * as qs from 'qs';\nimport React, { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState } from \"react\";\nimport { isAllEmptyObjValue } from 'src/quickAcess/utils/ArrayUtils';\nimport { getRelOpTypeTitle } from 'src/quickAcess/utils/ViewUtils';\nimport { ATTRVALUE_LIST, ATTR_NID, ATTR_VALUE, DATE_FORMAT, LEFT_BRACKET, LEFT_BRACKET_STR, LOGICAL_OP_TYPE, RELOP_LIST, REL_OP_TYPE, RIGHT_BRACKET, RIGHT_BRACKET_STR, eDynamicVarType, eAttrSiteType } from \"src/quickAcess/utils/Config\";\nimport { getPropValueList } from '@common/utils/ViewUtils';\nimport { getOptionsView } from \"@common/utils/ViewUtils\";\nimport SearchCodeEditorInput from \"@components/SearchCodeEditorInput\";\nimport cloneDeep from 'lodash/cloneDeep';\nimport SearchCodeEditorModal from \"./SearchCodeEditorModal\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport \"./SearchEditTable.scss\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { isEmpty, handleMoveAction } from \"@common/utils/ArrayUtils\";\nimport { DndContext } from '@dnd-kit/core';\nimport { restrictToVerticalAxis } from '@dnd-kit/modifiers';\nimport { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { eRegionType } from \"src/inspect/utils/enum\";\nimport { getExprs } from \"@common/utils/logicUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EditableContext = /*#__PURE__*/React.createContext(null);\nconst DragCustomerFormRow = ({\n  children,\n  ...props\n}) => {\n  _s();\n  const [form] = Form.useForm();\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n    transition,\n    isDragging\n  } = useSortable({\n    id: props['data-row-key']\n  });\n  const style = {\n    ...props.style,\n    transform: CSS.Transform.toString(transform && {\n      ...transform,\n      scaleY: 1\n    }),\n    transition,\n    ...(isDragging ? {\n      position: 'relative',\n      zIndex: 9999\n    } : {})\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    component: false,\n    children: /*#__PURE__*/_jsxDEV(EditableContext.Provider, {\n      value: form,\n      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n        ...props,\n        ref: setNodeRef,\n        style: style,\n        ...attributes,\n        children: React.Children.map(children, child => {\n          if (child.key === \"operation\" && !child.props.record.isDefault) {\n            // 操作列，且非默认行\n            return {\n              ...child,\n              props: {\n                ...child.props,\n                render: (value, row, index) => /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    ref: setActivatorNodeRef,\n                    icon: /*#__PURE__*/_jsxDEV(MenuOutlined, {\n                      title: \"\\u62D6\\u62FD\\u6392\\u5E8F\",\n                      style: {\n                        color: \"#0077f2\",\n                        touchAction: 'none',\n                        cursor: 'move',\n                        fontSize: 12\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 75,\n                      columnNumber: 74\n                    }, this),\n                    ...listeners\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 75,\n                    columnNumber: 22\n                  }, this), child.props.render && child.props.render(value, row, index)]\n                }, void 0, true)\n              }\n            };\n          }\n          return child;\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(DragCustomerFormRow, \"CRNAa9fwKPsQLsXOYgJzKmidj/U=\", false, function () {\n  return [Form.useForm, useSortable];\n});\n_c = DragCustomerFormRow;\nconst EditableRow = ({\n  index,\n  ...props\n}) => {\n  _s2();\n  const [form] = Form.useForm();\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    component: false,\n    children: /*#__PURE__*/_jsxDEV(EditableContext.Provider, {\n      value: form,\n      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s2(EditableRow, \"rI7DrJIrFu7YmlGWYiMFTzs8jF0=\", false, function () {\n  return [Form.useForm];\n});\n_c2 = EditableRow;\nconst EditableCell = ({\n  title,\n  editable,\n  children,\n  dataIndex,\n  record,\n  handleSave,\n  attrNodeList,\n  _exprs,\n  envList,\n  suggestionList,\n  opreateList,\n  queryType,\n  ...restProps\n}) => {\n  _s3();\n  const [editing, setEditing] = useState(false);\n  const inputRef = useRef(Object.create(null));\n  const codeInputRef = useRef(Object.create(null));\n  const form = useContext(EditableContext);\n  useEffect(() => {\n    if (editing) {\n      if (record.attrNid == eDynamicConditionNodeId) {\n        // TODO: select和focus\n      } else {\n        // inputRef.current.select();\n        // inputRef.current.foucs();\n      }\n    }\n  }, [editing]);\n\n  // 根据record更新值\n  useEffect(() => {\n    if (dataIndex) {\n      setTableFieldsValue(record, dataIndex);\n    }\n  }, [record]);\n  const toggleEdit = () => {\n    setEditing(!editing);\n    setTableFieldsValue(record, dataIndex);\n  };\n  const toggleSearchCodeInputEdit = e => {\n    e.stopPropagation();\n    globalEventBus.emit(\"openSearchCodeEditorModalEvent\", \"\", {\n      attrNodeList,\n      exprs: _exprs,\n      value: record[dataIndex],\n      envList,\n      suggestionList,\n      opreateList,\n      record,\n      queryType,\n      callback: codeInputSave.bind(this)\n    });\n  };\n\n  // 刷新数据\n  const setTableFieldsValue = (data, dataIndex) => {\n    form.setFieldsValue({\n      [dataIndex]: data[dataIndex]\n    });\n  };\n\n  // 保存\n  const save = async () => {\n    try {\n      const values = await form.validateFields();\n      if (values[ATTR_NID] != record[ATTR_NID]) {\n        // 字段更新时同时也需要更新字段类型\n        //  t.clone is not a function\n        let properType = attrNodeList.find(item => item.nodeId === values[ATTR_NID]);\n        if (!!properType) {\n          // 动态条件不在attrNodeList中，TODO:待优化，实际上需要解决的是动态条件的值添加后更新到form中\n          const {\n            dataType,\n            list,\n            relopList\n          } = properType;\n          values.dataType = dataType; // 字段类型\n          values[ATTRVALUE_LIST] = list; // 值\n          values[ATTR_VALUE] = undefined; // 重置值\n          values[RELOP_LIST] = relopList; // 连接符\n          setTableFieldsValue(values, ATTR_VALUE);\n        }\n      }\n      if (record.dataType == 'ListBox' && values[REL_OP_TYPE] == '1' && (values[ATTR_VALUE] || []).length > 1) {\n        values[REL_OP_TYPE] = '17';\n      }\n      const newData = {\n        ...record,\n        ...values\n      };\n      handleSave(newData); // 合并原有的row和新的values值\n      setTableFieldsValue(newData, dataIndex);\n    } catch (errInfo) {\n      console.log(\"Save failed:\", errInfo);\n    }\n  };\n\n  // Input的保存，需要切换UI,与Select和Date类型处理方式不同\n  const codeInputSave = async ({\n    originValue\n  }) => {\n    console.log(\"save\", originValue);\n    try {\n      //如果值没有更改的话，则不触发保存方法\n      const newData = {\n        ...record,\n        [dataIndex]: originValue\n      };\n      handleSave(newData); // 合并原有的row和新的values值\n      codeInputRef.current.setValue(originValue);\n    } catch (errInfo) {\n      console.log(\"Save failed:\", errInfo);\n    }\n  };\n\n  // Input的保存，需要切换UI,与Select和Date类型处理方式不同\n  const inputSave = async ({\n    originValue\n  }) => {\n    console.log(\"save\", originValue);\n    try {\n      //如果值没有更改的话，则不触发保存方法\n      handleSave({\n        ...record,\n        [dataIndex]: originValue\n      }); // 合并原有的row和新的values值\n      toggleEdit();\n    } catch (errInfo) {\n      console.log(\"Save failed:\", errInfo);\n    }\n  };\n\n  // 值渲染的UI\n  const getValueInputUI = () => {\n    if (record.attrNid == eDynamicConditionNodeId) {\n      // 动态条件\n      if (editing) {\n        childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            margin: 0\n          },\n          name: dataIndex,\n          children: /*#__PURE__*/_jsxDEV(SearchCodeEditorInput, {\n            ref: codeInputRef,\n            envList: envList,\n            suggestionList: suggestionList,\n            onPressEnter: inputSave,\n            onBlur: inputSave\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this);\n      } else {\n        childNode = /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-editble\",\n          onClick: toggleEdit,\n          children: [/*#__PURE__*/_jsxDEV(SearchCodeEditorInput, {\n            ref: codeInputRef,\n            envList: envList,\n            suggestionList: suggestionList,\n            readOnly: true,\n            value: record[dataIndex]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"editble-icon\",\n            children: /*#__PURE__*/_jsxDEV(EditOutlined, {\n              onClick: toggleSearchCodeInputEdit\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this);\n      }\n    } else if (record.dataType === eConsoleUiControl.TextBox) {\n      // input类型\n      if (editing) {\n        childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            margin: 0\n          },\n          name: dataIndex,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            ref: inputRef,\n            autoComplete: \"off\",\n            placeholder: placeholder,\n            onPressEnter: e => {\n              inputSave({\n                originValue: e.target.value\n              });\n            },\n            onBlur: e => {\n              inputSave({\n                originValue: e.target.value\n              });\n            },\n            className: \"search-table-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this);\n      } else {\n        //非编辑状态，如果没有值，则显示默认值placeholder，需要创建引用变量data，因为children只读，不能更改\n        let data = [...children];\n        let value = !isEmpty(children[1]) ? children[1] : placeholder;\n        data[1] = value;\n        let className = !isEmpty(children[1]) ? \"editable-cell-value-wrap\" : \"editable-cell-value-wrap-none\";\n        childNode = /*#__PURE__*/_jsxDEV(\"div\", {\n          className: className,\n          onClick: toggleEdit,\n          children: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this);\n      }\n    } else if (record.dataType === eConsoleUiControl.ListBox) {\n      childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          margin: 0\n        },\n        name: dataIndex,\n        children: /*#__PURE__*/_jsxDEV(Select\n        // open={true} //默认展开，单击即可展开\n        // dropdownClassName=\"table-select-option\"\n        , {\n          dropdownMatchSelectWidth: false,\n          bordered: false,\n          mode: \"multiple\" //值可以多选\n          ,\n          className: \"search-table-select\",\n          popupClassName: \"search-table-select-pop\",\n          optionFilterProp: \"propValue\",\n          onBlur: save //失去焦点回调\n          ,\n          onChange: save //改变后回调\n          ,\n          allowClear: true,\n          showSearch: true,\n          fieldNames: {\n            label: \"propValue\",\n            value: \"propType\"\n          },\n          options: record[ATTRVALUE_LIST]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this);\n    } else if (record.dataType === eConsoleUiControl.Date) {\n      childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          margin: 0\n        },\n        name: dataIndex,\n        children: /*#__PURE__*/_jsxDEV(DatePicker, {\n          format: DATE_FORMAT,\n          onChange: save,\n          className: \"search-table-datepicker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this);\n    }\n    return childNode;\n  };\n  let childNode = children;\n  let placeholder = \"请输入\" + title;\n  const fieldObjectType = useMemo(() => {\n    return (attrNodeList || []).reduce((pre, cur) => {\n      const curObj = {\n        key: cur.nodeId,\n        value: cur.nodeId,\n        label: cur.nodeName,\n        dataType: cur.dataType\n      };\n      return pre.concat(curObj);\n    }, []);\n  }, [attrNodeList]);\n\n  // 下拉列表的值\n  let objectTypeOptions;\n  switch (dataIndex) {\n    case ATTR_NID:\n      // 字段\n      // objectTypeSelect = getOptionsView(fieldObjectType);\n      objectTypeOptions = fieldObjectType;\n      break;\n    case LOGICAL_OP_TYPE:\n      // 连接符\n      // objectTypeSelect = getOptionsView(CRITERIA_RELOP_TYPE_LIST);\n      objectTypeOptions = CRITERIA_RELOP_TYPE_LIST;\n      break;\n    case REL_OP_TYPE:\n      // 条件\n      // objectTypeSelect = getPropValueList(record[RELOP_LIST])\n      objectTypeOptions = (record[RELOP_LIST] || []).map(relop => ({\n        key: relop.propType,\n        value: relop.propType,\n        label: relop.propValue\n      }));\n      break;\n    default:\n      break;\n  }\n  if (editable) {\n    if (dataIndex == ATTR_NID || dataIndex == LOGICAL_OP_TYPE || dataIndex == REL_OP_TYPE) {\n      if (record[ATTR_NID] == eDynamicConditionNodeId && dataIndex == REL_OP_TYPE) {\n        // 动态字段没有条件\n        childNode = /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"editable-cell-disabled\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this);\n      } else if (record[ATTR_NID] == eDynamicConditionNodeId && dataIndex == ATTR_NID) {\n        // 动态字段\n        childNode = /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u52A8\\u6001\\u6761\\u4EF6\\uFF08\", /*#__PURE__*/_jsxDEV(FunctionOutlined, {\n            className: \"color-blue\",\n            style: {\n              margin: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 18\n          }, this), \"\\uFF09\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this);\n      } else {\n        childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            margin: 0\n          },\n          name: dataIndex,\n          children: /*#__PURE__*/_jsxDEV(Select\n          // open={true} //默认展开，单击即可展开\n          , {\n            dropdownMatchSelectWidth: false,\n            bordered: false,\n            className: \"search-table-select\",\n            popupClassName: \"search-table-select-pop\"\n            // dropdownClassName=\"table-select-option\"\n            ,\n            optionFilterProp: \"label\",\n            onBlur: save //失去焦点回调\n            ,\n            onChange: save //改变后回调\n            ,\n            allowClear: dataIndex == ATTR_NID,\n            showSearch: dataIndex == ATTR_NID,\n            options: objectTypeOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this);\n      }\n    } else {\n      // 值\n      childNode = getValueInputUI();\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"td\", {\n    ...restProps,\n    children: childNode\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 407,\n    columnNumber: 10\n  }, this);\n};\n\n/**\r\n * TODO: 编辑器值fontsize-12,color:#333\r\n * 高级搜索-搜索条件表格\r\n *  FIXME: 父组件获取子组件实例关键点:\r\n * 1.useImperativeHandle/forwardRef \r\n * 2.ref不是结构赋值，而是直接取值\r\n * @param {queryType} 是否是Kpi模块 \r\n * @param {*} ref \r\n * @returns \r\n */\n// TODO:此组件为了方便kpi和高级搜索，将数据处理逻辑合并到该组件，需要注意数据处理不能影响原数据，因此全部采用map遍历的形式，可能并不是好的思路，需要注意\n_s3(EditableCell, \"wupz0ybPaPXsmTiYSZvlbxQefsU=\");\n_c3 = EditableCell;\nfunction SearchEditTable({\n  attrNodeList = [],\n  criteriaList = [],\n  selectionList,\n  exprs,\n  queryType,\n  loading\n}, ref) {\n  _s4();\n  const [initDataSource, setInitDataSource] = useState([]); //初始化数据\n  const [dataSource, setDataSource] = useState([]); //修改后的数据\n  const [_exprs, setExprs] = useState([]); // 变量\n\n  // 可以让你在使用 ref 时自定义暴露给父组件的实例值\n  useImperativeHandle(ref, () => ({\n    // 获取列表内容:过滤掉默认选项\n    getCriteriaListForUI: () => getCriteriaListForUI(dataSource),\n    getInitDataSource: () => getCriteriaListForUI(initDataSource),\n    getCriteriaListForBackend: () => getCriteriaListForBackend(),\n    addSearchCode: () => addSearchCode()\n  }));\n  useEffect(() => {\n    if (!isEmpty(attrNodeList)) {\n      initData();\n    }\n  }, [qs.stringify(attrNodeList), qs.stringify(criteriaList), qs.stringify(exprs)]);\n\n  // 获取表格属性\n  const getCriteriaListForUI = data => {\n    try {\n      // 注意：该方法会走两遍,不能更改原有数据结构，所以需要使用map，不能使用forEach改变原有数据结构，也不能使用json.parse()\n      data = data.filter(item => !item.isDefault);\n      return (data || []).map(item => {\n        let attrValue = item.attrValue;\n        switch (item.dataType) {\n          case eConsoleUiControl.ListBox:\n            // 多选框，需要将多选数组转化为字符串逗号拼接 \n            attrValue = !isEmpty(item.attrValue) ? item.attrValue.join(\",\") : item.attrValue;\n            break;\n          case eConsoleUiControl.Date:\n            // 时间需要转为字符串\n            attrValue = !isEmpty(item.attrValue) ? item.attrValue.format(DATE_FORMAT) : item.attrValue;\n            break;\n          default:\n            break;\n        }\n        return {\n          ...item,\n          ...{\n            attrValue\n          }\n        };\n      });\n    } catch (error) {\n      globalUtil.warning(\"保存失败\");\n      return [];\n    }\n  };\n\n  // 获取高级搜索自定义表单接口数据\n  const getCriteriaListForBackend = () => {\n    try {\n      let data = getCriteriaListForUI(dataSource);\n      // 获取表格内容\n      data = data.map(item => {\n        var _item$LEFT_BRACKET, _item$RIGHT_BRACKET;\n        return {\n          \"leftBracketCnt\": (_item$LEFT_BRACKET = item[LEFT_BRACKET]) === null || _item$LEFT_BRACKET === void 0 ? void 0 : _item$LEFT_BRACKET.length,\n          \"attrNid\": item[ATTR_NID],\n          \"relOpType\": item[REL_OP_TYPE],\n          \"attrValue\": item[ATTR_VALUE],\n          \"logicalOpType\": item[LOGICAL_OP_TYPE] || \"0\",\n          \"rightBracketCnt\": (_item$RIGHT_BRACKET = item[RIGHT_BRACKET]) === null || _item$RIGHT_BRACKET === void 0 ? void 0 : _item$RIGHT_BRACKET.length,\n          \"isDisableDelete\": item.isDisableDelete\n        };\n      });\n      return data || [];\n    } catch (e) {\n      globalUtil.warning(\"保存失败\");\n      console.warn(\"高级搜索自定义表单保存失败\", e);\n      // message.warn(\"高级搜索自定义表单保存失败\");\n      return [];\n    }\n  };\n  const relOpTypeTitle = useMemo(() => getRelOpTypeTitle(selectionList), []);\n  const getColumnsOperate = (text, record, index, bracketCnt, bracketStr) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-table-opreate\",\n      children: [/*#__PURE__*/_jsxDEV(\"a\", {\n        onClick: () => {\n          reduce(record, index, bracketCnt, bracketStr);\n        },\n        className: \"minus-btn \",\n        children: /*#__PURE__*/_jsxDEV(MinusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        onClick: () => {\n          add(record, index, bracketCnt, bracketStr);\n        },\n        className: \"plus-btn\",\n        children: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 12\n    }, this);\n  };\n  const columnsRow = [{\n    title: \"左(\",\n    dataIndex: LEFT_BRACKET,\n    // colunms的render不会随着数据的刷新而刷新,FIXME:改成function函数式组件后，可以刷新了，说明Class的刷新机制和function不太一致\n    render: (text, record, index) => getColumnsOperate(text, record, index, LEFT_BRACKET, LEFT_BRACKET_STR),\n    width: 60\n  }, {\n    title: \"字段\",\n    dataIndex: ATTR_NID,\n    editable: true,\n    width: 130\n  }, {\n    title: relOpTypeTitle,\n    dataIndex: REL_OP_TYPE,\n    editable: true,\n    width: 80\n  }, {\n    title: \"值\",\n    dataIndex: ATTR_VALUE,\n    editable: true\n  }, {\n    title: \"右)\",\n    dataIndex: RIGHT_BRACKET,\n    render: (text, record, index) => getColumnsOperate(text, record, index, RIGHT_BRACKET, RIGHT_BRACKET_STR),\n    width: 60\n  }, {\n    title: \"连接符\",\n    dataIndex: LOGICAL_OP_TYPE,\n    editable: true,\n    width: 60\n  }, {\n    title: \"操作\",\n    dataIndex: \"operation\",\n    width: 100,\n    render: (_, record, index) => {\n      if (record.isDefault) {\n        return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n      }\n      return /*#__PURE__*/_jsxDEV(Space, {\n        size: 10,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 21\n          }, this),\n          onClick: () => {\n            handleDelete(record.key);\n          },\n          size: \"small\",\n          disabled: record.isDefault || record.isDisableDelete //默认行、不可删除\n          ,\n          title: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n\n  // 获取默认值\n  const getDefaultRow = () => {\n    return {\n      key: nanoid(),\n      leftBracket: \"\",\n      //左括号(\n      attrNid: \"\",\n      //字段\n      relOpType: undefined,\n      //条件\n      attrValue: undefined,\n      //值\n      rightBracket: \"\",\n      //右括号）\n      logicalOpType: \"\",\n      //连接符\n      dataType: eConsoleUiControl.TextBox,\n      //字段类型:默认文本\n      attrValueList: [],\n      //值Select的List\n      isDefault: true\n    };\n  };\n\n  // 初始化数据\n  const initData = () => {\n    let initData1 = assembleInitTableData([...criteriaList]);\n    setInitDataSource([...initData1]); //记录初始化数据\n    let data = addDefaultRow([...initData1]);\n    setDataSource(cloneDeep(data));\n    const _exprs = getExprs(attrNodeList, exprs);\n    setExprs(_exprs);\n  };\n\n  // 初始化数据\n  const assembleInitTableData = criteriaList => {\n    try {\n      if (isEmpty(criteriaList)) return [];\n      // TODO:288个条件直接卡死\n      return criteriaList.map(item => {\n        var _item$REL_OP_TYPE;\n        let newItem = {};\n        let properType = getProperType(item.attrNid);\n        const {\n          dataType,\n          list,\n          relopList\n        } = properType;\n        newItem.key = toolUtil.guid(); //本地数据没有id,需要guid作为key\n        newItem.dataType = dataType;\n        newItem[ATTRVALUE_LIST] = list;\n        newItem[RELOP_LIST] = relopList; //连接符\n        newItem[REL_OP_TYPE] = (_item$REL_OP_TYPE = item[REL_OP_TYPE]) === null || _item$REL_OP_TYPE === void 0 ? void 0 : _item$REL_OP_TYPE.toString(); //连接符,需要转为String\n        if (dataType == eConsoleUiControl.ListBox) {\n          newItem.attrValue = item.attrValue.split(\",\"); //List\n        } else if (dataType == eConsoleUiControl.Date) {\n          newItem.attrValue = moment(item.attrValue, DATE_FORMAT); //日期类型格式化\n        }\n        return {\n          ...item,\n          ...newItem\n        };\n      }) || [];\n    } catch (e) {\n      globalUtil.warning('自定义表单数据初始化失败！');\n      console.log('自定义表单数据初始化失败！', e);\n    }\n  };\n\n  // 根据attrNid获取下拉List\n  const getProperType = attrNid => {\n    return [...attrNodeList, eDynamicCondition].find(item => item.nodeId === attrNid) || {};\n  };\n\n  //添加默认项\n  const addDefaultRow = obj => {\n    let defaultIndex = obj.findIndex(item => item.isDefault);\n    if (defaultIndex === -1) {\n      let defaultRow = getDefaultRow();\n      obj.push(defaultRow);\n    }\n    return obj;\n  };\n\n  // 减少左括号/增加右括号\n  const reduce = (record, index, bracketCnt, bracketStr) => {\n    const newDataSource = [...dataSource];\n    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt].replace(bracketStr, \"\");\n    setDataSource(cloneDeep(newDataSource));\n  };\n  const add = (record, index, bracketCnt, bracketStr) => {\n    const newDataSource = [...dataSource];\n    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt] + bracketStr;\n    setDataSource(cloneDeep(newDataSource));\n  };\n  const handleDelete = key => {\n    const newDataSource = [...dataSource];\n    setDataSource(cloneDeep(newDataSource.filter(item => item.key !== key)));\n  };\n\n  // 上移下移 type\n  const handleMove = (record, type) => {\n    const data = handleMoveAction(dataSource, record, type);\n    setDataSource(data);\n  };\n\n  // 当输入新的一行内容后，上一行的“连接符”默认给 “且”，因为这个是用户必选的项，又经常容易忘了设置。\n  const handleAddLogicalOpType = dataSource => {\n    dataSource.forEach((field, index) => {\n      if (!field.isDefault && index != dataSource.length - 2 && (field[LOGICAL_OP_TYPE] == CRITERIA_RELOP_TYPE.op_place || isEmpty(field[LOGICAL_OP_TYPE]))) {\n        field[LOGICAL_OP_TYPE] = CRITERIA_RELOP_TYPE.op_and;\n      }\n    });\n    setDataSource(cloneDeep(dataSource));\n  };\n  const handleSave = row => {\n    // 判断row是否有数据\n    const newData = [...dataSource];\n    const index = newData.findIndex(item => row.key === item.key);\n    const item = newData[index];\n    if (row.isDefault && !isAllEmptyObjValue(row)) row.isDefault = false; //将默认行且非空则更改为非默认行\n    newData.splice(index, 1, {\n      ...item,\n      ...row\n    });\n    let data = addDefaultRow([...newData]);\n    handleAddLogicalOpType(data);\n  };\n\n  // 变量\n  const envList = useMemo(() => {\n    // 搜索模块，需要去除kpiTag为isKpi的变量\n    let _envList = (_exprs || []).filter(item => item.attrType == eDynamicVarType.variable && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType)).map(attrNode => ({\n      varKey: attrNode.attrDisplayValue,\n      varValue: attrNode.attrValue\n    }));\n    return [..._envList];\n  }, [attrNodeList, _exprs, queryType]);\n\n  // 函数\n  const suggestionList = useMemo(() => {\n    const _dynamicCondition = (_exprs || []).filter(item => item.attrType == eDynamicVarType.func && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType));\n    let _suggestionList1 = _dynamicCondition.map(attrNode => ({\n      varKey: attrNode.attrDisplayValue,\n      varValue: attrNode.attrValue,\n      functionName: attrNode.functionName\n    }));\n    return [..._suggestionList1];\n  }, [attrNodeList, _exprs, queryType]);\n\n  // 操作符\n  const opreateList = useMemo(() => {\n    const _dynamicCondition = (_exprs || []).filter(item => item.attrType == eDynamicVarType.operator && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType));\n    let _operatorList = _dynamicCondition.map(attrNode => ({\n      varKey: attrNode.attrDisplayValue,\n      varValue: attrNode.attrValue,\n      functionName: attrNode.functionName\n    }));\n    return [..._operatorList];\n  }, [attrNodeList, _exprs, queryType]);\n\n  // 添加动态条件\n  const addSearchCode = () => {\n    globalEventBus.emit(\"openSearchCodeEditorModalEvent\", \"\", {\n      attrNodeList,\n      exprs: _exprs,\n      value: \"\",\n      envList,\n      suggestionList,\n      opreateList,\n      queryType,\n      callback: addSearchCodeCallback\n    });\n  };\n\n  // 添加动态条件回调\n  const addSearchCodeCallback = ({\n    originValue\n  }) => {\n    const newData = [...dataSource];\n    const newValue = {\n      key: nanoid(),\n      leftBracket: \"\",\n      //左括号(\n      attrNid: eDynamicConditionNodeId,\n      //字段\n      relOpType: undefined,\n      //条件\n      attrValue: originValue,\n      //值\n      rightBracket: \"\",\n      //右括号）\n      logicalOpType: \"\",\n      //连接符\n      dataType: eConsoleUiControl.TextBox,\n      //字段类型:默认文本\n      attrValueList: [],\n      //值Select的List\n      isDefault: false\n    };\n    // 数组倒数第二位插入\n    newData.splice(newData.length - 1, 0, newValue);\n    handleAddLogicalOpType(newData); // 添加动态条件也需要检查连接符\n  };\n\n  // 拖拽排序\n  const onDragEnd = ({\n    active,\n    over\n  }) => {\n    if (active !== null && active !== void 0 && active.id && over !== null && over !== void 0 && over.id && (active === null || active === void 0 ? void 0 : active.id) !== (over === null || over === void 0 ? void 0 : over.id)) {\n      let _dataSource = [...dataSource];\n      const activeIndex = _dataSource.findIndex(i => i.key == (active === null || active === void 0 ? void 0 : active.id));\n      const overIndex = _dataSource.findIndex(i => i.key == (over === null || over === void 0 ? void 0 : over.id));\n      _dataSource = arrayMove(_dataSource, activeIndex, overIndex);\n      setDataSource(cloneDeep(_dataSource));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(DndContext, {\n      modifiers: [restrictToVerticalAxis],\n      onDragEnd: onDragEnd,\n      children: /*#__PURE__*/_jsxDEV(SortableContext, {\n        items: dataSource.map(i => i.key) // 需要全部数据的key,否则无法拖拽\n        ,\n        strategy: verticalListSortingStrategy,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          components: {\n            body: {\n              row: DragCustomerFormRow,\n              cell: EditableCell\n            }\n          },\n          rowKey: \"key\" // 注意不能是key,也不能不填\n          ,\n          rowClassName: () => \"editable-row\",\n          className: \"search-table custome-table custom-table-border\",\n          bordered: true,\n          loading: loading,\n          dataSource: dataSource,\n          columns: columnsRow.map(col => {\n            if (!col.editable) {\n              return col;\n            }\n            return {\n              ...col,\n              onCell: record => ({\n                record,\n                editable: col.editable,\n                dataIndex: col.dataIndex,\n                title: col.title,\n                handleSave: handleSave,\n                attrNodeList: attrNodeList,\n                _exprs: _exprs,\n                envList: envList,\n                suggestionList: suggestionList,\n                opreateList: opreateList,\n                queryType: queryType\n              })\n            };\n          }),\n          pagination: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 749,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SearchCodeEditorModal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 793,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 748,\n    columnNumber: 5\n  }, this);\n}\n_s4(SearchEditTable, \"RzWW+N7NczmjB6RsJzUM5bxlEtQ=\");\n_c4 = SearchEditTable;\nexport default _c5 = /*#__PURE__*/forwardRef(SearchEditTable);\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"DragCustomerFormRow\");\n$RefreshReg$(_c2, \"EditableRow\");\n$RefreshReg$(_c3, \"EditableCell\");\n$RefreshReg$(_c4, \"SearchEditTable\");\n$RefreshReg$(_c5, \"%default%\");", "map": {"version": 3, "names": ["DeleteOutlined", "MinusOutlined", "PlusOutlined", "EditOutlined", "FunctionOutlined", "QuestionCircleOutlined", "SearchOutlined", "ArrowUpOutlined", "ArrowDownOutlined", "MenuOutlined", "CRITERIA_RELOP_TYPE_LIST", "eConsoleUiControl", "CRITERIA_RELOP_TYPE", "eDynamicConditionNodeId", "eDynamicCondition", "eQueryType", "eEnTag", "eMoveType", "toolUtil", "<PERSON><PERSON>", "DatePicker", "Form", "Input", "Space", "Select", "Table", "moment", "nanoid", "qs", "React", "forwardRef", "useContext", "useEffect", "useImperativeHandle", "useMemo", "useRef", "useState", "isAllEmptyObjValue", "getRelOpTypeTitle", "ATTRVALUE_LIST", "ATTR_NID", "ATTR_VALUE", "DATE_FORMAT", "LEFT_BRACKET", "LEFT_BRACKET_STR", "LOGICAL_OP_TYPE", "RELOP_LIST", "REL_OP_TYPE", "RIGHT_BRACKET", "RIGHT_BRACKET_STR", "eDynamicVarType", "eAttrSiteType", "getPropValueList", "getOptionsView", "SearchCodeEditorInput", "cloneDeep", "SearchCodeEditorModal", "globalEventBus", "globalUtil", "isEmpty", "handleMoveAction", "DndContext", "restrictToVerticalAxis", "arrayMove", "SortableContext", "useSortable", "verticalListSortingStrategy", "CSS", "eRegionType", "getExprs", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditableContext", "createContext", "DragCustomerFormRow", "children", "props", "_s", "form", "useForm", "attributes", "listeners", "setNodeRef", "setActivatorNodeRef", "transform", "transition", "isDragging", "id", "style", "Transform", "toString", "scaleY", "position", "zIndex", "component", "Provider", "value", "ref", "Children", "map", "child", "key", "record", "isDefault", "render", "row", "index", "type", "icon", "title", "color", "touchAction", "cursor", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "EditableRow", "_s2", "_c2", "EditableCell", "editable", "dataIndex", "handleSave", "attrNodeList", "_exprs", "envList", "suggestionList", "opreateList", "queryType", "restProps", "_s3", "editing", "setEditing", "inputRef", "Object", "create", "codeInputRef", "attrNid", "setTableFieldsValue", "toggleEdit", "toggleSearchCodeInputEdit", "e", "stopPropagation", "emit", "exprs", "callback", "codeInputSave", "bind", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save", "values", "validateFields", "properType", "find", "item", "nodeId", "dataType", "list", "relopList", "undefined", "length", "newData", "errInfo", "console", "log", "originValue", "current", "setValue", "inputSave", "getValueInputUI", "childNode", "<PERSON><PERSON>", "margin", "name", "onPressEnter", "onBlur", "className", "onClick", "readOnly", "TextBox", "autoComplete", "placeholder", "target", "ListBox", "dropdownMatchSelectWidth", "bordered", "mode", "popupClassName", "optionFilterProp", "onChange", "allowClear", "showSearch", "fieldNames", "label", "options", "Date", "format", "fieldObjectType", "reduce", "pre", "cur", "curObj", "nodeName", "concat", "objectTypeOptions", "relop", "propType", "propValue", "_c3", "SearchEditTable", "criteriaList", "selectionList", "loading", "_s4", "initDataSource", "setInitDataSource", "dataSource", "setDataSource", "setExprs", "getCriteriaListForUI", "getInitDataSource", "getCriteriaListForBackend", "addSearchCode", "initData", "stringify", "filter", "attrValue", "join", "error", "warning", "_item$LEFT_BRACKET", "_item$RIGHT_BRACKET", "isDisableDelete", "warn", "relOpTypeTitle", "getColumnsOperate", "text", "bracketCnt", "bracketStr", "add", "columnsRow", "width", "_", "size", "handleDelete", "disabled", "getDefaultRow", "leftBracket", "relOpType", "rightBracket", "logicalOpType", "attrValueList", "initData1", "assembleInitTableData", "addDefaultRow", "_item$REL_OP_TYPE", "newItem", "getProperType", "guid", "split", "obj", "defaultIndex", "findIndex", "defaultRow", "push", "newDataSource", "replace", "handleMove", "handleAddLogicalOpType", "for<PERSON>ach", "field", "op_place", "op_and", "splice", "_envList", "attrType", "variable", "isNotKpi", "attrNode", "<PERSON><PERSON><PERSON>", "attrDisplayValue", "varValue", "_dynamicCondition", "func", "_suggestionList1", "functionName", "operator", "_operatorList", "addSearchCodeCallback", "newValue", "onDragEnd", "active", "over", "_dataSource", "activeIndex", "i", "overIndex", "modifiers", "items", "strategy", "components", "body", "cell", "<PERSON><PERSON><PERSON>", "rowClassName", "columns", "col", "onCell", "pagination", "_c4", "_c5", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/quickAcess/views/Search/SearchDetail/SearchEditTable.jsx"], "sourcesContent": ["/* eslint-disable jsx-a11y/anchor-is-valid */\r\n/* eslint-disable react-hooks/exhaustive-deps */\r\n/*\r\n * @Author: Walt <EMAIL>\r\n * @Date: 2023-03-10 10:46:54\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2025-01-23 19:07:27\r\n * @Description: 高级搜索编辑表格\r\n */\r\nimport { DeleteOutlined, MinusOutlined, PlusOutlined, EditOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined, ArrowUpOutlined, ArrowDownOutlined, MenuOutlined } from \"@ant-design/icons\";\r\nimport { CRITERIA_RELOP_TYPE_LIST, eConsoleUiControl, CRITERIA_RELOP_TYPE, eDynamicConditionNodeId, eDynamicCondition, eQueryType, eEnTag,  eMoveType } from \"@common/utils/enum\";\r\nimport * as toolUtil from \"@common/utils/toolUtil\";\r\nimport { Button, DatePicker, Form, Input, Space, Select, Table } from \"antd\";\r\nimport moment from \"moment\";\r\nimport { nanoid } from \"nanoid\";\r\nimport * as qs from 'qs';\r\nimport React, { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState, } from \"react\";\r\nimport { isAllEmptyObjValue } from 'src/quickAcess/utils/ArrayUtils';\r\nimport { getRelOpTypeTitle } from 'src/quickAcess/utils/ViewUtils';\r\nimport {\r\n  ATTRVALUE_LIST, ATTR_NID, ATTR_VALUE, DATE_FORMAT,\r\n  LEFT_BRACKET, LEFT_BRACKET_STR,\r\n  LOGICAL_OP_TYPE, RELOP_LIST, REL_OP_TYPE, RIGHT_BRACKET, RIGHT_BRACKET_STR, eDynamicVarType, eAttrSiteType,\r\n} from \"src/quickAcess/utils/Config\";\r\nimport { getPropValueList } from '@common/utils/ViewUtils';\r\nimport { getOptionsView } from \"@common/utils/ViewUtils\";\r\nimport SearchCodeEditorInput from \"@components/SearchCodeEditorInput\";\r\nimport cloneDeep from 'lodash/cloneDeep';\r\nimport SearchCodeEditorModal from \"./SearchCodeEditorModal\"\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport \"./SearchEditTable.scss\"\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { isEmpty, handleMoveAction } from \"@common/utils/ArrayUtils\";\r\nimport { DndContext } from '@dnd-kit/core';\r\nimport { restrictToVerticalAxis } from '@dnd-kit/modifiers';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  useSortable,\r\n  verticalListSortingStrategy\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\nimport { eRegionType } from \"src/inspect/utils/enum\";\r\nimport {getExprs} from \"@common/utils/logicUtils\"\r\n\r\nconst EditableContext = React.createContext(null);\r\n\r\nconst DragCustomerFormRow = ({ children, ...props }) => {\r\n  const [form] = Form.useForm();\r\n  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({\r\n    id: props['data-row-key'],\r\n  });\r\n  const style = {\r\n    ...props.style,\r\n    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1, }),\r\n    transition,\r\n    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),\r\n  };\r\n  return (\r\n    <Form form={form} component={false}>\r\n      <EditableContext.Provider value={form}>\r\n        <tr {...props} ref={setNodeRef} style={style} {...attributes}>\r\n          {React.Children.map(children, (child) => {\r\n            if (child.key === \"operation\" && !child.props.record.isDefault) { // 操作列，且非默认行\r\n              return {\r\n                ...child,\r\n                props: {\r\n                  ...child.props,\r\n                  render: (value, row, index) => <>\r\n                    {/* {React.cloneElement(<span></span>, {\r\n                      children: (\r\n                        <MenuOutlined title=\"拖拽排序\" ref={setActivatorNodeRef} style={{ color: \"#0077f2\", touchAction: 'none', cursor: 'move' }} {...listeners} />\r\n                      ),\r\n                    })} */}\r\n                     <Button type=\"link\" ref={setActivatorNodeRef} icon={<MenuOutlined title=\"拖拽排序\"  style={{ color: \"#0077f2\", touchAction: 'none', cursor: 'move', fontSize: 12 }}  />} {...listeners}/>\r\n                    {child.props.render && child.props.render(value, row, index)}\r\n                  </>\r\n                }\r\n              }\r\n            }\r\n            return child;\r\n          })}\r\n        </tr>\r\n      </EditableContext.Provider>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst EditableRow = ({ index, ...props }) => {\r\n  const [form] = Form.useForm();\r\n  return (\r\n    <Form form={form} component={false}>\r\n      <EditableContext.Provider value={form}>\r\n        <tr {...props} />\r\n      </EditableContext.Provider>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst EditableCell = ({\r\n  title,\r\n  editable,\r\n  children,\r\n  dataIndex,\r\n  record,\r\n  handleSave,\r\n  attrNodeList,\r\n  _exprs,\r\n  envList,\r\n  suggestionList,\r\n  opreateList,\r\n  queryType,\r\n  ...restProps\r\n}) => {\r\n\r\n  const [editing, setEditing] = useState(false);\r\n  const inputRef = useRef(Object.create(null));\r\n  const codeInputRef = useRef(Object.create(null));\r\n  const form = useContext(EditableContext);\r\n\r\n  useEffect(() => {\r\n    if (editing) {\r\n      if (record.attrNid == eDynamicConditionNodeId) {\r\n        // TODO: select和focus\r\n      } else {\r\n        // inputRef.current.select();\r\n        // inputRef.current.foucs();\r\n      }\r\n    }\r\n  }, [editing]);\r\n\r\n  // 根据record更新值\r\n  useEffect(() => {\r\n    if (dataIndex) {\r\n      setTableFieldsValue(record, dataIndex);\r\n    }\r\n  }, [record])\r\n\r\n  const toggleEdit = () => {\r\n    setEditing(!editing);\r\n    setTableFieldsValue(record, dataIndex);\r\n  };\r\n\r\n  const toggleSearchCodeInputEdit = (e) => {\r\n    e.stopPropagation();\r\n    globalEventBus.emit(\"openSearchCodeEditorModalEvent\", \"\", { attrNodeList, exprs: _exprs, value: record[dataIndex], envList, suggestionList, opreateList, record,  queryType, callback: codeInputSave.bind(this) });\r\n  }\r\n\r\n  // 刷新数据\r\n  const setTableFieldsValue = (data, dataIndex) => {\r\n    form.setFieldsValue({\r\n      [dataIndex]: data[dataIndex],\r\n    });\r\n  }\r\n\r\n  // 保存\r\n  const save = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      if (values[ATTR_NID] != record[ATTR_NID]) { // 字段更新时同时也需要更新字段类型\r\n        //  t.clone is not a function\r\n        let properType = attrNodeList.find(item => item.nodeId === values[ATTR_NID]);\r\n        if(!!properType){ // 动态条件不在attrNodeList中，TODO:待优化，实际上需要解决的是动态条件的值添加后更新到form中\r\n          const { dataType, list, relopList } = properType\r\n          values.dataType = dataType;     // 字段类型\r\n          values[ATTRVALUE_LIST] = list;  // 值\r\n          values[ATTR_VALUE] = undefined; // 重置值\r\n          values[RELOP_LIST] = relopList; // 连接符\r\n          setTableFieldsValue(values, ATTR_VALUE);\r\n        }\r\n      }\r\n      if (record.dataType == 'ListBox' && values[REL_OP_TYPE] == '1' && (values[ATTR_VALUE] || []).length > 1) {\r\n        values[REL_OP_TYPE] = '17'\r\n      }\r\n      const newData = { ...record, ...values };\r\n      handleSave(newData); // 合并原有的row和新的values值\r\n      setTableFieldsValue(newData, dataIndex);\r\n    } catch (errInfo) {\r\n      console.log(\"Save failed:\", errInfo);\r\n    }\r\n  };\r\n\r\n  // Input的保存，需要切换UI,与Select和Date类型处理方式不同\r\n  const codeInputSave = async ({ originValue }) => {\r\n    console.log(\"save\", originValue)\r\n    try {\r\n      //如果值没有更改的话，则不触发保存方法\r\n      const newData = { ...record, [dataIndex]: originValue };\r\n      handleSave(newData); // 合并原有的row和新的values值\r\n      codeInputRef.current.setValue(originValue);\r\n    } catch (errInfo) {\r\n      console.log(\"Save failed:\", errInfo);\r\n    }\r\n  };\r\n\r\n  // Input的保存，需要切换UI,与Select和Date类型处理方式不同\r\n  const inputSave = async ({ originValue }) => {\r\n    console.log(\"save\", originValue)\r\n    try {\r\n      //如果值没有更改的话，则不触发保存方法\r\n      handleSave({ ...record, [dataIndex]: originValue }); // 合并原有的row和新的values值\r\n      toggleEdit();\r\n    } catch (errInfo) {\r\n      console.log(\"Save failed:\", errInfo);\r\n    }\r\n  };\r\n\r\n  // 值渲染的UI\r\n  const getValueInputUI = () => {\r\n    if (record.attrNid == eDynamicConditionNodeId) { // 动态条件\r\n      if (editing) {\r\n        childNode = (\r\n          <Form.Item\r\n            style={{\r\n              margin: 0,\r\n            }}\r\n            name={dataIndex}\r\n          >\r\n            <SearchCodeEditorInput\r\n              ref={codeInputRef}\r\n              envList={envList}\r\n              suggestionList={suggestionList}\r\n              onPressEnter={inputSave}\r\n              onBlur={inputSave}\r\n            />\r\n          </Form.Item>\r\n        );\r\n      } else {\r\n        childNode = (\r\n          <div className=\"search-input-editble\" onClick={toggleEdit}>\r\n            <SearchCodeEditorInput\r\n              ref={codeInputRef}\r\n              envList={envList}\r\n              suggestionList={suggestionList}\r\n              readOnly={true}\r\n              value={record[dataIndex]}\r\n            />\r\n            <span className=\"editble-icon\"><EditOutlined onClick={toggleSearchCodeInputEdit} /></span>\r\n          </div>\r\n        );\r\n      }\r\n    } else if (record.dataType === eConsoleUiControl.TextBox) {\r\n      // input类型\r\n      if (editing) {\r\n        childNode = (\r\n          <Form.Item\r\n            style={{\r\n              margin: 0,\r\n            }}\r\n            name={dataIndex}\r\n          >\r\n            <Input\r\n              ref={inputRef}\r\n              autoComplete=\"off\"\r\n              placeholder={placeholder}\r\n              onPressEnter={(e) => { inputSave({ originValue: e.target.value }) }}\r\n              onBlur={(e) => { inputSave({ originValue: e.target.value }) }}\r\n              className=\"search-table-input\"\r\n            />\r\n          </Form.Item>\r\n        );\r\n      } else {\r\n        //非编辑状态，如果没有值，则显示默认值placeholder，需要创建引用变量data，因为children只读，不能更改\r\n        let data = [...children];\r\n        let value = !isEmpty(children[1]) ? children[1] : placeholder;\r\n        data[1] = value;\r\n        let className = !isEmpty(children[1])\r\n          ? \"editable-cell-value-wrap\"\r\n          : \"editable-cell-value-wrap-none\";\r\n        childNode = (\r\n          <div className={className} onClick={toggleEdit}>\r\n            {data}\r\n          </div>\r\n        );\r\n      }\r\n    } else if (record.dataType === eConsoleUiControl.ListBox) {\r\n      childNode = (\r\n        <Form.Item\r\n          style={{\r\n            margin: 0,\r\n          }}\r\n          name={dataIndex}\r\n        >\r\n          <Select\r\n            // open={true} //默认展开，单击即可展开\r\n            // dropdownClassName=\"table-select-option\"\r\n            dropdownMatchSelectWidth={false}\r\n            bordered={false}\r\n            mode=\"multiple\" //值可以多选\r\n            className=\"search-table-select\"\r\n            popupClassName=\"search-table-select-pop\"\r\n            optionFilterProp=\"propValue\"\r\n            onBlur={save} //失去焦点回调\r\n            onChange={save} //改变后回调\r\n            allowClear\r\n            showSearch\r\n            fieldNames={{\r\n              label: \"propValue\",\r\n              value: \"propType\"\r\n            }}\r\n            options={record[ATTRVALUE_LIST]}\r\n          >\r\n          </Select>\r\n        </Form.Item>\r\n      );\r\n    } else if (record.dataType === eConsoleUiControl.Date) {\r\n      childNode =\r\n        <Form.Item\r\n          style={{\r\n            margin: 0,\r\n          }}\r\n          name={dataIndex}\r\n        >\r\n          <DatePicker format={DATE_FORMAT} onChange={save} className=\"search-table-datepicker\"/>\r\n        </Form.Item>\r\n    }\r\n    return childNode;\r\n  }\r\n\r\n  let childNode = children;\r\n  let placeholder = \"请输入\" + title;\r\n\r\n  const fieldObjectType = useMemo(() => {\r\n    return (attrNodeList || []).reduce((pre, cur) => {\r\n      const curObj = {\r\n        key: cur.nodeId,\r\n        value: cur.nodeId,\r\n        label: cur.nodeName,\r\n        dataType: cur.dataType\r\n      };\r\n      return pre.concat(curObj);\r\n    }, []);\r\n  }, [attrNodeList]);\r\n\r\n  // 下拉列表的值\r\n  let objectTypeOptions;\r\n  switch (dataIndex) {\r\n    case ATTR_NID:\r\n      // 字段\r\n      // objectTypeSelect = getOptionsView(fieldObjectType);\r\n      objectTypeOptions = fieldObjectType;\r\n      break;\r\n    case LOGICAL_OP_TYPE:\r\n      // 连接符\r\n      // objectTypeSelect = getOptionsView(CRITERIA_RELOP_TYPE_LIST);\r\n      objectTypeOptions = CRITERIA_RELOP_TYPE_LIST;\r\n      break;\r\n    case REL_OP_TYPE:\r\n      // 条件\r\n      // objectTypeSelect = getPropValueList(record[RELOP_LIST])\r\n      objectTypeOptions =  (record[RELOP_LIST] || []).map(relop =>({\r\n        key: relop.propType,\r\n        value: relop.propType,\r\n        label: relop.propValue\r\n      }))\r\n      break;\r\n    default:\r\n      break;\r\n  }\r\n\r\n  if (editable) {\r\n    if (dataIndex == ATTR_NID ||\r\n      dataIndex == LOGICAL_OP_TYPE || dataIndex == REL_OP_TYPE) {\r\n      if (record[ATTR_NID] == eDynamicConditionNodeId && dataIndex == REL_OP_TYPE) { // 动态字段没有条件\r\n        childNode = (\r\n          <div className=\"editable-cell-disabled\">\r\n          </div>\r\n        );\r\n      } else if (record[ATTR_NID] == eDynamicConditionNodeId && dataIndex == ATTR_NID) { // 动态字段\r\n        childNode = (\r\n          <div>\r\n            动态条件（<FunctionOutlined className=\"color-blue\" style={{margin: 0}}/>）\r\n          </div>\r\n        );\r\n      } else {\r\n        childNode = (\r\n          <Form.Item\r\n            style={{\r\n              margin: 0,\r\n            }}\r\n            name={dataIndex}\r\n          >\r\n            <Select\r\n              // open={true} //默认展开，单击即可展开\r\n              dropdownMatchSelectWidth={false}\r\n              bordered={false}\r\n              className=\"search-table-select\"\r\n              popupClassName=\"search-table-select-pop\"\r\n              // dropdownClassName=\"table-select-option\"\r\n              optionFilterProp=\"label\" \r\n              onBlur={save} //失去焦点回调\r\n              onChange={save} //改变后回调\r\n              allowClear={dataIndex == ATTR_NID}\r\n              showSearch={dataIndex == ATTR_NID}\r\n              options={objectTypeOptions}\r\n            >\r\n            </Select>\r\n          </Form.Item>\r\n        );\r\n      }\r\n    } else {\r\n      // 值\r\n      childNode = getValueInputUI();\r\n    }\r\n  }\r\n\r\n  return <td {...restProps}>{childNode}</td>;\r\n};\r\n\r\n/**\r\n * TODO: 编辑器值fontsize-12,color:#333\r\n * 高级搜索-搜索条件表格\r\n *  FIXME: 父组件获取子组件实例关键点:\r\n * 1.useImperativeHandle/forwardRef \r\n * 2.ref不是结构赋值，而是直接取值\r\n * @param {queryType} 是否是Kpi模块 \r\n * @param {*} ref \r\n * @returns \r\n */\r\n// TODO:此组件为了方便kpi和高级搜索，将数据处理逻辑合并到该组件，需要注意数据处理不能影响原数据，因此全部采用map遍历的形式，可能并不是好的思路，需要注意\r\nfunction SearchEditTable({ attrNodeList = [], criteriaList = [], selectionList, exprs, queryType, loading }, ref,) {\r\n\r\n  const [initDataSource, setInitDataSource] = useState([])//初始化数据\r\n  const [dataSource, setDataSource] = useState([]) //修改后的数据\r\n  const [_exprs, setExprs] = useState([]) // 变量\r\n\r\n  // 可以让你在使用 ref 时自定义暴露给父组件的实例值\r\n  useImperativeHandle(ref, () => ({\r\n    // 获取列表内容:过滤掉默认选项\r\n    getCriteriaListForUI: () => getCriteriaListForUI(dataSource),\r\n    getInitDataSource: () => getCriteriaListForUI(initDataSource),\r\n    getCriteriaListForBackend: () => getCriteriaListForBackend(),\r\n    addSearchCode: () => addSearchCode(),\r\n  }));\r\n\r\n  useEffect(() => {\r\n    if (!isEmpty(attrNodeList)) {\r\n      initData();\r\n    }\r\n  }, [qs.stringify(attrNodeList), qs.stringify(criteriaList), qs.stringify(exprs)]);\r\n\r\n  // 获取表格属性\r\n  const getCriteriaListForUI = (data) => {\r\n    try {\r\n      // 注意：该方法会走两遍,不能更改原有数据结构，所以需要使用map，不能使用forEach改变原有数据结构，也不能使用json.parse()\r\n      data = data.filter(item => !item.isDefault);\r\n      return (data || []).map(item => {\r\n        let attrValue = item.attrValue;\r\n        switch (item.dataType) {\r\n          case eConsoleUiControl.ListBox:\r\n            // 多选框，需要将多选数组转化为字符串逗号拼接 \r\n            attrValue = !isEmpty(item.attrValue) ? item.attrValue.join(\",\") : item.attrValue;\r\n            break;\r\n          case eConsoleUiControl.Date:\r\n            // 时间需要转为字符串\r\n            attrValue = !isEmpty(item.attrValue) ? item.attrValue.format(DATE_FORMAT) : item.attrValue;\r\n            break;\r\n          default:\r\n            break;\r\n        }\r\n        return { ...item, ...{ attrValue } }\r\n      });\r\n    } catch (error) {\r\n      globalUtil.warning(\"保存失败\");\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // 获取高级搜索自定义表单接口数据\r\n  const getCriteriaListForBackend = () => {\r\n    try {\r\n      let data = getCriteriaListForUI(dataSource);\r\n      // 获取表格内容\r\n      data = data.map(item => (\r\n        {\r\n          \"leftBracketCnt\": item[LEFT_BRACKET]?.length,\r\n          \"attrNid\": item[ATTR_NID],\r\n          \"relOpType\": item[REL_OP_TYPE],\r\n          \"attrValue\": item[ATTR_VALUE],\r\n          \"logicalOpType\": item[LOGICAL_OP_TYPE] || \"0\",\r\n          \"rightBracketCnt\": item[RIGHT_BRACKET]?.length,\r\n          \"isDisableDelete\": item.isDisableDelete,\r\n        }\r\n      ))\r\n      return data || [];\r\n    } catch (e) {\r\n      globalUtil.warning(\"保存失败\");\r\n      console.warn(\"高级搜索自定义表单保存失败\", e);\r\n      // message.warn(\"高级搜索自定义表单保存失败\");\r\n      return []\r\n    }\r\n  }\r\n\r\n  const relOpTypeTitle = useMemo(() => (getRelOpTypeTitle(selectionList)), []);\r\n\r\n  const getColumnsOperate = (text, record, index, bracketCnt, bracketStr) => {\r\n    return <div className=\"search-table-opreate\">\r\n      <a\r\n        onClick={() => {\r\n          reduce(record, index, bracketCnt, bracketStr);\r\n        }}\r\n        className=\"minus-btn \"\r\n      ><MinusOutlined /></a>\r\n      <span>{text}</span>\r\n      <a\r\n        onClick={() => {\r\n          add(record, index, bracketCnt, bracketStr);\r\n        }}\r\n        className=\"plus-btn\"\r\n      ><PlusOutlined /></a>\r\n    </div>\r\n  }\r\n\r\n  const columnsRow = [\r\n    {\r\n      title: \"左(\",\r\n      dataIndex: LEFT_BRACKET,\r\n      // colunms的render不会随着数据的刷新而刷新,FIXME:改成function函数式组件后，可以刷新了，说明Class的刷新机制和function不太一致\r\n      render: (text, record, index) => (getColumnsOperate(text, record, index, LEFT_BRACKET, LEFT_BRACKET_STR)),\r\n      width: 60,\r\n    },\r\n    {\r\n      title: \"字段\",\r\n      dataIndex: ATTR_NID,\r\n      editable: true,\r\n      width: 130,\r\n    },\r\n    {\r\n      title: relOpTypeTitle,\r\n      dataIndex: REL_OP_TYPE,\r\n      editable: true,\r\n      width: 80,\r\n    },\r\n    {\r\n      title: \"值\",\r\n      dataIndex: ATTR_VALUE,\r\n      editable: true,\r\n    },\r\n    {\r\n      title: \"右)\",\r\n      dataIndex: RIGHT_BRACKET,\r\n      render: (text, record, index) => (getColumnsOperate(text, record, index, RIGHT_BRACKET, RIGHT_BRACKET_STR)),\r\n      width: 60,\r\n    },\r\n    {\r\n      title: \"连接符\",\r\n      dataIndex: LOGICAL_OP_TYPE,\r\n      editable: true,\r\n      width: 60,\r\n    },\r\n    {\r\n      title: \"操作\",\r\n      dataIndex: \"operation\",\r\n      width: 100,\r\n      render: (_, record, index) => {\r\n        if (record.isDefault) {\r\n          return <></>\r\n        }\r\n        return (\r\n          <Space size={10}>\r\n            <Button\r\n              type=\"link\"\r\n              icon={<DeleteOutlined />}\r\n              onClick={() => {\r\n                handleDelete(record.key);\r\n              }}\r\n              size=\"small\"\r\n              disabled={record.isDefault || record.isDisableDelete} //默认行、不可删除\r\n              title=\"删除\"\r\n            ></Button>\r\n          </Space>\r\n        )\r\n      }\r\n    },\r\n  ];\r\n\r\n  // 获取默认值\r\n  const getDefaultRow = () => {\r\n    return {\r\n      key: nanoid(),\r\n      leftBracket: \"\",        //左括号(\r\n      attrNid: \"\",            //字段\r\n      relOpType: undefined,   //条件\r\n      attrValue: undefined,   //值\r\n      rightBracket: \"\",       //右括号）\r\n      logicalOpType: \"\",      //连接符\r\n      dataType: eConsoleUiControl.TextBox, //字段类型:默认文本\r\n      attrValueList: [],                    //值Select的List\r\n      isDefault: true,\r\n    };\r\n  };\r\n\r\n  // 初始化数据\r\n  const initData = () => {\r\n    let initData1 = assembleInitTableData([...criteriaList]);\r\n    setInitDataSource([...initData1]); //记录初始化数据\r\n    let data = addDefaultRow([...initData1]);\r\n    setDataSource(cloneDeep(data));\r\n    const _exprs = getExprs(attrNodeList, exprs);\r\n    setExprs(_exprs);\r\n  }\r\n\r\n  // 初始化数据\r\n  const assembleInitTableData = (criteriaList) => {\r\n    try {\r\n      if (isEmpty(criteriaList)) return [];\r\n      // TODO:288个条件直接卡死\r\n      return criteriaList.map(item => {\r\n        let newItem = {}\r\n        let properType = getProperType(item.attrNid);\r\n        const { dataType, list, relopList } = properType;\r\n        newItem.key = toolUtil.guid(); //本地数据没有id,需要guid作为key\r\n        newItem.dataType = dataType;\r\n        newItem[ATTRVALUE_LIST] = list;\r\n        newItem[RELOP_LIST] = relopList; //连接符\r\n        newItem[REL_OP_TYPE] = item[REL_OP_TYPE]?.toString(); //连接符,需要转为String\r\n        if (dataType == eConsoleUiControl.ListBox) {\r\n          newItem.attrValue = item.attrValue.split(\",\");  //List\r\n        } else if (dataType == eConsoleUiControl.Date) {\r\n          newItem.attrValue = moment(item.attrValue, DATE_FORMAT) //日期类型格式化\r\n        }\r\n        return { ...item, ...newItem };\r\n      }) || []\r\n    } catch (e) {\r\n      globalUtil.warning('自定义表单数据初始化失败！');\r\n      console.log('自定义表单数据初始化失败！', e);\r\n    }\r\n  }\r\n\r\n  // 根据attrNid获取下拉List\r\n  const getProperType = (attrNid) => {\r\n    return ([...attrNodeList, eDynamicCondition]).find(item => item.nodeId === attrNid) || {};\r\n  }\r\n\r\n  //添加默认项\r\n  const addDefaultRow = (obj) => {\r\n    let defaultIndex = obj.findIndex((item) => item.isDefault);\r\n    if (defaultIndex === -1) {\r\n      let defaultRow = getDefaultRow();\r\n      obj.push(defaultRow);\r\n    }\r\n    return obj\r\n  };\r\n\r\n  // 减少左括号/增加右括号\r\n  const reduce = (record, index, bracketCnt, bracketStr) => {\r\n    const newDataSource = [...dataSource];\r\n    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt].replace(bracketStr, \"\");\r\n    setDataSource(cloneDeep(newDataSource));\r\n  };\r\n\r\n  const add = (record, index, bracketCnt, bracketStr) => {\r\n    const newDataSource = [...dataSource];\r\n    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt] + bracketStr;\r\n    setDataSource(cloneDeep(newDataSource));\r\n  };\r\n\r\n  const handleDelete = (key) => {\r\n    const newDataSource = [...dataSource];\r\n    setDataSource(cloneDeep(newDataSource.filter((item) => item.key !== key)));\r\n  };\r\n\r\n  // 上移下移 type\r\n  const handleMove = (record, type) => {\r\n    const data = handleMoveAction(dataSource, record, type);\r\n    setDataSource(data);\r\n  };\r\n\r\n  // 当输入新的一行内容后，上一行的“连接符”默认给 “且”，因为这个是用户必选的项，又经常容易忘了设置。\r\n  const handleAddLogicalOpType = (dataSource) => {\r\n    dataSource.forEach((field, index) => {\r\n      if (!field.isDefault && index != dataSource.length - 2 && (field[LOGICAL_OP_TYPE] == CRITERIA_RELOP_TYPE.op_place || isEmpty(field[LOGICAL_OP_TYPE]))) {\r\n        field[LOGICAL_OP_TYPE] = CRITERIA_RELOP_TYPE.op_and\r\n      }\r\n    });\r\n    setDataSource(cloneDeep(dataSource))\r\n  }\r\n\r\n  const handleSave = (row) => {\r\n    // 判断row是否有数据\r\n    const newData = [...dataSource];\r\n    const index = newData.findIndex((item) => row.key === item.key);\r\n    const item = newData[index];\r\n    if (row.isDefault && !isAllEmptyObjValue(row)) row.isDefault = false; //将默认行且非空则更改为非默认行\r\n    newData.splice(index, 1, { ...item, ...row });\r\n    let data = addDefaultRow([...newData]);\r\n    handleAddLogicalOpType(data);\r\n  };\r\n\r\n  // 变量\r\n  const envList = useMemo(() => {\r\n    // 搜索模块，需要去除kpiTag为isKpi的变量\r\n    let _envList = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.variable && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) )).map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue,}));\r\n    return [..._envList]\r\n  }, [attrNodeList, _exprs, queryType]);\r\n\r\n  // 函数\r\n  const suggestionList = useMemo(() => {\r\n    const _dynamicCondition = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.func &&  (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) ));\r\n    let _suggestionList1 = _dynamicCondition.map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue, functionName: attrNode.functionName }));\r\n    return [..._suggestionList1,]\r\n  }, [attrNodeList, _exprs, queryType]);\r\n\r\n  // 操作符\r\n  const opreateList = useMemo(() => {\r\n    const _dynamicCondition = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.operator &&  (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) ));\r\n    let _operatorList = _dynamicCondition.map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue, functionName: attrNode.functionName }));\r\n    return [..._operatorList,]\r\n  }, [attrNodeList, _exprs, queryType]);\r\n\r\n  // 添加动态条件\r\n  const addSearchCode = () => {\r\n    globalEventBus.emit(\"openSearchCodeEditorModalEvent\", \"\", { attrNodeList, exprs: _exprs, value: \"\", envList, suggestionList, opreateList, queryType, callback: addSearchCodeCallback });\r\n  }\r\n\r\n  // 添加动态条件回调\r\n  const addSearchCodeCallback = ({ originValue }) => {\r\n    const newData = [...dataSource];\r\n    const newValue = {\r\n      key: nanoid(),\r\n      leftBracket: \"\",                  //左括号(\r\n      attrNid: eDynamicConditionNodeId, //字段\r\n      relOpType: undefined,             //条件\r\n      attrValue: originValue,           //值\r\n      rightBracket: \"\",                 //右括号）\r\n      logicalOpType: \"\",                //连接符\r\n      dataType: eConsoleUiControl.TextBox, //字段类型:默认文本\r\n      attrValueList: [],                    //值Select的List\r\n      isDefault: false,\r\n    };\r\n    // 数组倒数第二位插入\r\n    newData.splice(newData.length - 1, 0, newValue);\r\n    handleAddLogicalOpType(newData); // 添加动态条件也需要检查连接符\r\n  }\r\n\r\n  // 拖拽排序\r\n  const onDragEnd = ({ active, over }) => {\r\n    if ((active?.id && over?.id) && (active?.id !== over?.id)) {\r\n      let _dataSource = [...dataSource];\r\n      const activeIndex = _dataSource.findIndex((i) => i.key == active?.id);\r\n      const overIndex = _dataSource.findIndex((i) => i.key == over?.id);\r\n      _dataSource = arrayMove(_dataSource, activeIndex, overIndex);\r\n      setDataSource(cloneDeep(_dataSource))\r\n    }\r\n  };\r\n    \r\n  return (\r\n    <div>\r\n      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>\r\n        <SortableContext\r\n          items={dataSource.map((i) => i.key)} // 需要全部数据的key,否则无法拖拽\r\n          strategy={verticalListSortingStrategy}\r\n        >\r\n        <Table\r\n          components={{\r\n            body: {\r\n              row: DragCustomerFormRow,\r\n              cell: EditableCell,\r\n            },\r\n          }}\r\n          rowKey=\"key\" // 注意不能是key,也不能不填\r\n          rowClassName={() => \"editable-row\"}\r\n          className=\"search-table custome-table custom-table-border\"\r\n          bordered\r\n          loading={loading}\r\n          dataSource={dataSource}\r\n          columns={columnsRow.map((col) => {\r\n            if (!col.editable) {\r\n              return col;\r\n            }\r\n\r\n            return {\r\n              ...col,\r\n              onCell: (record) => ({\r\n                record,\r\n                editable: col.editable,\r\n                dataIndex: col.dataIndex,\r\n                title: col.title,\r\n                handleSave: handleSave,\r\n                attrNodeList: attrNodeList,\r\n                _exprs: _exprs,\r\n                envList: envList,\r\n                suggestionList: suggestionList,\r\n                opreateList: opreateList,\r\n                queryType: queryType,\r\n              }),\r\n            };\r\n          })}\r\n          pagination={false}\r\n        />\r\n        </SortableContext>\r\n      </DndContext>\r\n      <SearchCodeEditorModal />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default forwardRef(SearchEditTable);\r\n"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAc,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,mBAAmB;AACzM,SAASC,wBAAwB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,MAAM,EAAGC,SAAS,QAAQ,oBAAoB;AACjL,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,SAASC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAC5E,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAO,KAAKC,EAAE,MAAM,IAAI;AACxB,OAAOC,KAAK,IAAIC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAS,OAAO;AACjH,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SACEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EACjDC,YAAY,EAAEC,gBAAgB,EAC9BC,eAAe,EAAEC,UAAU,EAAEC,WAAW,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,aAAa,QACrG,6BAA6B;AACpC,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAOC,qBAAqB,MAAM,mCAAmC;AACrE,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAO,wBAAwB;AAC/B,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,OAAO,EAAEC,gBAAgB,QAAQ,0BAA0B;AACpE,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,sBAAsB,QAAQ,oBAAoB;AAC3D,SACEC,SAAS,EACTC,eAAe,EACfC,WAAW,EACXC,2BAA2B,QACtB,mBAAmB;AAC1B,SAASC,GAAG,QAAQ,oBAAoB;AACxC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAAQC,QAAQ,QAAO,0BAA0B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,eAAe,gBAAG7C,KAAK,CAAC8C,aAAa,CAAC,IAAI,CAAC;AAEjD,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAE,GAAGC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM,CAACC,IAAI,CAAC,GAAG3D,IAAI,CAAC4D,OAAO,CAAC,CAAC;EAC7B,MAAM;IAAEC,UAAU;IAAEC,SAAS;IAAEC,UAAU;IAAEC,mBAAmB;IAAEC,SAAS;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGvB,WAAW,CAAC;IAChHwB,EAAE,EAAEX,KAAK,CAAC,cAAc;EAC1B,CAAC,CAAC;EACF,MAAMY,KAAK,GAAG;IACZ,GAAGZ,KAAK,CAACY,KAAK;IACdJ,SAAS,EAAEnB,GAAG,CAACwB,SAAS,CAACC,QAAQ,CAACN,SAAS,IAAI;MAAE,GAAGA,SAAS;MAAEO,MAAM,EAAE;IAAG,CAAC,CAAC;IAC5EN,UAAU;IACV,IAAIC,UAAU,GAAG;MAAEM,QAAQ,EAAE,UAAU;MAAEC,MAAM,EAAE;IAAK,CAAC,GAAG,CAAC,CAAC;EAC9D,CAAC;EACD,oBACExB,OAAA,CAAClD,IAAI;IAAC2D,IAAI,EAAEA,IAAK;IAACgB,SAAS,EAAE,KAAM;IAAAnB,QAAA,eACjCN,OAAA,CAACG,eAAe,CAACuB,QAAQ;MAACC,KAAK,EAAElB,IAAK;MAAAH,QAAA,eACpCN,OAAA;QAAA,GAAQO,KAAK;QAAEqB,GAAG,EAAEf,UAAW;QAACM,KAAK,EAAEA,KAAM;QAAA,GAAKR,UAAU;QAAAL,QAAA,EACzDhD,KAAK,CAACuE,QAAQ,CAACC,GAAG,CAACxB,QAAQ,EAAGyB,KAAK,IAAK;UACvC,IAAIA,KAAK,CAACC,GAAG,KAAK,WAAW,IAAI,CAACD,KAAK,CAACxB,KAAK,CAAC0B,MAAM,CAACC,SAAS,EAAE;YAAE;YAChE,OAAO;cACL,GAAGH,KAAK;cACRxB,KAAK,EAAE;gBACL,GAAGwB,KAAK,CAACxB,KAAK;gBACd4B,MAAM,EAAEA,CAACR,KAAK,EAAES,GAAG,EAAEC,KAAK,kBAAKrC,OAAA,CAAAE,SAAA;kBAAAI,QAAA,gBAM5BN,OAAA,CAACpD,MAAM;oBAAC0F,IAAI,EAAC,MAAM;oBAACV,GAAG,EAAEd,mBAAoB;oBAACyB,IAAI,eAAEvC,OAAA,CAAC9D,YAAY;sBAACsG,KAAK,EAAC,0BAAM;sBAAErB,KAAK,EAAE;wBAAEsB,KAAK,EAAE,SAAS;wBAAEC,WAAW,EAAE,MAAM;wBAAEC,MAAM,EAAE,MAAM;wBAAEC,QAAQ,EAAE;sBAAG;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAE;oBAAA,GAAKpC;kBAAS;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACrLjB,KAAK,CAACxB,KAAK,CAAC4B,MAAM,IAAIJ,KAAK,CAACxB,KAAK,CAAC4B,MAAM,CAACR,KAAK,EAAES,GAAG,EAAEC,KAAK,CAAC;gBAAA,eAC5D;cACJ;YACF,CAAC;UACH;UACA,OAAON,KAAK;QACd,CAAC;MAAC;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACmB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;AAEX,CAAC;AAACxC,EAAA,CAvCIH,mBAAmB;EAAA,QACRvD,IAAI,CAAC4D,OAAO,EAC2EhB,WAAW;AAAA;AAAAuD,EAAA,GAF7G5C,mBAAmB;AAyCzB,MAAM6C,WAAW,GAAGA,CAAC;EAAEb,KAAK;EAAE,GAAG9B;AAAM,CAAC,KAAK;EAAA4C,GAAA;EAC3C,MAAM,CAAC1C,IAAI,CAAC,GAAG3D,IAAI,CAAC4D,OAAO,CAAC,CAAC;EAC7B,oBACEV,OAAA,CAAClD,IAAI;IAAC2D,IAAI,EAAEA,IAAK;IAACgB,SAAS,EAAE,KAAM;IAAAnB,QAAA,eACjCN,OAAA,CAACG,eAAe,CAACuB,QAAQ;MAACC,KAAK,EAAElB,IAAK;MAAAH,QAAA,eACpCN,OAAA;QAAA,GAAQO;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;AAEX,CAAC;AAACG,GAAA,CATID,WAAW;EAAA,QACApG,IAAI,CAAC4D,OAAO;AAAA;AAAA0C,GAAA,GADvBF,WAAW;AAWjB,MAAMG,YAAY,GAAGA,CAAC;EACpBb,KAAK;EACLc,QAAQ;EACRhD,QAAQ;EACRiD,SAAS;EACTtB,MAAM;EACNuB,UAAU;EACVC,YAAY;EACZC,MAAM;EACNC,OAAO;EACPC,cAAc;EACdC,WAAW;EACXC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EAAAC,GAAA;EAEJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMsG,QAAQ,GAAGvG,MAAM,CAACwG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EAC5C,MAAMC,YAAY,GAAG1G,MAAM,CAACwG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EAChD,MAAM5D,IAAI,GAAGjD,UAAU,CAAC2C,eAAe,CAAC;EAExC1C,SAAS,CAAC,MAAM;IACd,IAAIwG,OAAO,EAAE;MACX,IAAIhC,MAAM,CAACsC,OAAO,IAAIjI,uBAAuB,EAAE;QAC7C;MAAA,CACD,MAAM;QACL;QACA;MAAA;IAEJ;EACF,CAAC,EAAE,CAAC2H,OAAO,CAAC,CAAC;;EAEb;EACAxG,SAAS,CAAC,MAAM;IACd,IAAI8F,SAAS,EAAE;MACbiB,mBAAmB,CAACvC,MAAM,EAAEsB,SAAS,CAAC;IACxC;EACF,CAAC,EAAE,CAACtB,MAAM,CAAC,CAAC;EAEZ,MAAMwC,UAAU,GAAGA,CAAA,KAAM;IACvBP,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBO,mBAAmB,CAACvC,MAAM,EAAEsB,SAAS,CAAC;EACxC,CAAC;EAED,MAAMmB,yBAAyB,GAAIC,CAAC,IAAK;IACvCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB1F,cAAc,CAAC2F,IAAI,CAAC,gCAAgC,EAAE,EAAE,EAAE;MAAEpB,YAAY;MAAEqB,KAAK,EAAEpB,MAAM;MAAE/B,KAAK,EAAEM,MAAM,CAACsB,SAAS,CAAC;MAAEI,OAAO;MAAEC,cAAc;MAAEC,WAAW;MAAE5B,MAAM;MAAG6B,SAAS;MAAEiB,QAAQ,EAAEC,aAAa,CAACC,IAAI,CAAC,IAAI;IAAE,CAAC,CAAC;EACpN,CAAC;;EAED;EACA,MAAMT,mBAAmB,GAAGA,CAACU,IAAI,EAAE3B,SAAS,KAAK;IAC/C9C,IAAI,CAAC0E,cAAc,CAAC;MAClB,CAAC5B,SAAS,GAAG2B,IAAI,CAAC3B,SAAS;IAC7B,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM6B,IAAI,GAAG,MAAAA,CAAA,KAAY;IACvB,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM5E,IAAI,CAAC6E,cAAc,CAAC,CAAC;MAC1C,IAAID,MAAM,CAACpH,QAAQ,CAAC,IAAIgE,MAAM,CAAChE,QAAQ,CAAC,EAAE;QAAE;QAC1C;QACA,IAAIsH,UAAU,GAAG9B,YAAY,CAAC+B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKL,MAAM,CAACpH,QAAQ,CAAC,CAAC;QAC5E,IAAG,CAAC,CAACsH,UAAU,EAAC;UAAE;UAChB,MAAM;YAAEI,QAAQ;YAAEC,IAAI;YAAEC;UAAU,CAAC,GAAGN,UAAU;UAChDF,MAAM,CAACM,QAAQ,GAAGA,QAAQ,CAAC,CAAK;UAChCN,MAAM,CAACrH,cAAc,CAAC,GAAG4H,IAAI,CAAC,CAAE;UAChCP,MAAM,CAACnH,UAAU,CAAC,GAAG4H,SAAS,CAAC,CAAC;UAChCT,MAAM,CAAC9G,UAAU,CAAC,GAAGsH,SAAS,CAAC,CAAC;UAChCrB,mBAAmB,CAACa,MAAM,EAAEnH,UAAU,CAAC;QACzC;MACF;MACA,IAAI+D,MAAM,CAAC0D,QAAQ,IAAI,SAAS,IAAIN,MAAM,CAAC7G,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC6G,MAAM,CAACnH,UAAU,CAAC,IAAI,EAAE,EAAE6H,MAAM,GAAG,CAAC,EAAE;QACvGV,MAAM,CAAC7G,WAAW,CAAC,GAAG,IAAI;MAC5B;MACA,MAAMwH,OAAO,GAAG;QAAE,GAAG/D,MAAM;QAAE,GAAGoD;MAAO,CAAC;MACxC7B,UAAU,CAACwC,OAAO,CAAC,CAAC,CAAC;MACrBxB,mBAAmB,CAACwB,OAAO,EAAEzC,SAAS,CAAC;IACzC,CAAC,CAAC,OAAO0C,OAAO,EAAE;MAChBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMjB,aAAa,GAAG,MAAAA,CAAO;IAAEoB;EAAY,CAAC,KAAK;IAC/CF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEC,WAAW,CAAC;IAChC,IAAI;MACF;MACA,MAAMJ,OAAO,GAAG;QAAE,GAAG/D,MAAM;QAAE,CAACsB,SAAS,GAAG6C;MAAY,CAAC;MACvD5C,UAAU,CAACwC,OAAO,CAAC,CAAC,CAAC;MACrB1B,YAAY,CAAC+B,OAAO,CAACC,QAAQ,CAACF,WAAW,CAAC;IAC5C,CAAC,CAAC,OAAOH,OAAO,EAAE;MAChBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMM,SAAS,GAAG,MAAAA,CAAO;IAAEH;EAAY,CAAC,KAAK;IAC3CF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEC,WAAW,CAAC;IAChC,IAAI;MACF;MACA5C,UAAU,CAAC;QAAE,GAAGvB,MAAM;QAAE,CAACsB,SAAS,GAAG6C;MAAY,CAAC,CAAC,CAAC,CAAC;MACrD3B,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOwB,OAAO,EAAE;MAChBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMO,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIvE,MAAM,CAACsC,OAAO,IAAIjI,uBAAuB,EAAE;MAAE;MAC/C,IAAI2H,OAAO,EAAE;QACXwC,SAAS,gBACPzG,OAAA,CAAClD,IAAI,CAAC4J,IAAI;UACRvF,KAAK,EAAE;YACLwF,MAAM,EAAE;UACV,CAAE;UACFC,IAAI,EAAErD,SAAU;UAAAjD,QAAA,eAEhBN,OAAA,CAACjB,qBAAqB;YACpB6C,GAAG,EAAE0C,YAAa;YAClBX,OAAO,EAAEA,OAAQ;YACjBC,cAAc,EAAEA,cAAe;YAC/BiD,YAAY,EAAEN,SAAU;YACxBO,MAAM,EAAEP;UAAU;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MACH,CAAC,MAAM;QACLyD,SAAS,gBACPzG,OAAA;UAAK+G,SAAS,EAAC,sBAAsB;UAACC,OAAO,EAAEvC,UAAW;UAAAnE,QAAA,gBACxDN,OAAA,CAACjB,qBAAqB;YACpB6C,GAAG,EAAE0C,YAAa;YAClBX,OAAO,EAAEA,OAAQ;YACjBC,cAAc,EAAEA,cAAe;YAC/BqD,QAAQ,EAAE,IAAK;YACftF,KAAK,EAAEM,MAAM,CAACsB,SAAS;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACFhD,OAAA;YAAM+G,SAAS,EAAC,cAAc;YAAAzG,QAAA,eAACN,OAAA,CAACpE,YAAY;cAACoL,OAAO,EAAEtC;YAA0B;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CACN;MACH;IACF,CAAC,MAAM,IAAIf,MAAM,CAAC0D,QAAQ,KAAKvJ,iBAAiB,CAAC8K,OAAO,EAAE;MACxD;MACA,IAAIjD,OAAO,EAAE;QACXwC,SAAS,gBACPzG,OAAA,CAAClD,IAAI,CAAC4J,IAAI;UACRvF,KAAK,EAAE;YACLwF,MAAM,EAAE;UACV,CAAE;UACFC,IAAI,EAAErD,SAAU;UAAAjD,QAAA,eAEhBN,OAAA,CAACjD,KAAK;YACJ6E,GAAG,EAAEuC,QAAS;YACdgD,YAAY,EAAC,KAAK;YAClBC,WAAW,EAAEA,WAAY;YACzBP,YAAY,EAAGlC,CAAC,IAAK;cAAE4B,SAAS,CAAC;gBAAEH,WAAW,EAAEzB,CAAC,CAAC0C,MAAM,CAAC1F;cAAM,CAAC,CAAC;YAAC,CAAE;YACpEmF,MAAM,EAAGnC,CAAC,IAAK;cAAE4B,SAAS,CAAC;gBAAEH,WAAW,EAAEzB,CAAC,CAAC0C,MAAM,CAAC1F;cAAM,CAAC,CAAC;YAAC,CAAE;YAC9DoF,SAAS,EAAC;UAAoB;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MACH,CAAC,MAAM;QACL;QACA,IAAIkC,IAAI,GAAG,CAAC,GAAG5E,QAAQ,CAAC;QACxB,IAAIqB,KAAK,GAAG,CAACvC,OAAO,CAACkB,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAG8G,WAAW;QAC7DlC,IAAI,CAAC,CAAC,CAAC,GAAGvD,KAAK;QACf,IAAIoF,SAAS,GAAG,CAAC3H,OAAO,CAACkB,QAAQ,CAAC,CAAC,CAAC,CAAC,GACjC,0BAA0B,GAC1B,+BAA+B;QACnCmG,SAAS,gBACPzG,OAAA;UAAK+G,SAAS,EAAEA,SAAU;UAACC,OAAO,EAAEvC,UAAW;UAAAnE,QAAA,EAC5C4E;QAAI;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MACH;IACF,CAAC,MAAM,IAAIf,MAAM,CAAC0D,QAAQ,KAAKvJ,iBAAiB,CAACkL,OAAO,EAAE;MACxDb,SAAS,gBACPzG,OAAA,CAAClD,IAAI,CAAC4J,IAAI;QACRvF,KAAK,EAAE;UACLwF,MAAM,EAAE;QACV,CAAE;QACFC,IAAI,EAAErD,SAAU;QAAAjD,QAAA,eAEhBN,OAAA,CAAC/C;QACC;QACA;QAAA;UACAsK,wBAAwB,EAAE,KAAM;UAChCC,QAAQ,EAAE,KAAM;UAChBC,IAAI,EAAC,UAAU,CAAC;UAAA;UAChBV,SAAS,EAAC,qBAAqB;UAC/BW,cAAc,EAAC,yBAAyB;UACxCC,gBAAgB,EAAC,WAAW;UAC5Bb,MAAM,EAAE1B,IAAK,CAAC;UAAA;UACdwC,QAAQ,EAAExC,IAAK,CAAC;UAAA;UAChByC,UAAU;UACVC,UAAU;UACVC,UAAU,EAAE;YACVC,KAAK,EAAE,WAAW;YAClBrG,KAAK,EAAE;UACT,CAAE;UACFsG,OAAO,EAAEhG,MAAM,CAACjE,cAAc;QAAE;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACZ;IACH,CAAC,MAAM,IAAIf,MAAM,CAAC0D,QAAQ,KAAKvJ,iBAAiB,CAAC8L,IAAI,EAAE;MACrDzB,SAAS,gBACPzG,OAAA,CAAClD,IAAI,CAAC4J,IAAI;QACRvF,KAAK,EAAE;UACLwF,MAAM,EAAE;QACV,CAAE;QACFC,IAAI,EAAErD,SAAU;QAAAjD,QAAA,eAEhBN,OAAA,CAACnD,UAAU;UAACsL,MAAM,EAAEhK,WAAY;UAACyJ,QAAQ,EAAExC,IAAK;UAAC2B,SAAS,EAAC;QAAyB;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC;IAChB;IACA,OAAOyD,SAAS;EAClB,CAAC;EAED,IAAIA,SAAS,GAAGnG,QAAQ;EACxB,IAAI8G,WAAW,GAAG,KAAK,GAAG5E,KAAK;EAE/B,MAAM4F,eAAe,GAAGzK,OAAO,CAAC,MAAM;IACpC,OAAO,CAAC8F,YAAY,IAAI,EAAE,EAAE4E,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAC/C,MAAMC,MAAM,GAAG;QACbxG,GAAG,EAAEuG,GAAG,CAAC7C,MAAM;QACf/D,KAAK,EAAE4G,GAAG,CAAC7C,MAAM;QACjBsC,KAAK,EAAEO,GAAG,CAACE,QAAQ;QACnB9C,QAAQ,EAAE4C,GAAG,CAAC5C;MAChB,CAAC;MACD,OAAO2C,GAAG,CAACI,MAAM,CAACF,MAAM,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,EAAE,CAAC/E,YAAY,CAAC,CAAC;;EAElB;EACA,IAAIkF,iBAAiB;EACrB,QAAQpF,SAAS;IACf,KAAKtF,QAAQ;MACX;MACA;MACA0K,iBAAiB,GAAGP,eAAe;MACnC;IACF,KAAK9J,eAAe;MAClB;MACA;MACAqK,iBAAiB,GAAGxM,wBAAwB;MAC5C;IACF,KAAKqC,WAAW;MACd;MACA;MACAmK,iBAAiB,GAAI,CAAC1G,MAAM,CAAC1D,UAAU,CAAC,IAAI,EAAE,EAAEuD,GAAG,CAAC8G,KAAK,KAAI;QAC3D5G,GAAG,EAAE4G,KAAK,CAACC,QAAQ;QACnBlH,KAAK,EAAEiH,KAAK,CAACC,QAAQ;QACrBb,KAAK,EAAEY,KAAK,CAACE;MACf,CAAC,CAAC,CAAC;MACH;IACF;MACE;EACJ;EAEA,IAAIxF,QAAQ,EAAE;IACZ,IAAIC,SAAS,IAAItF,QAAQ,IACvBsF,SAAS,IAAIjF,eAAe,IAAIiF,SAAS,IAAI/E,WAAW,EAAE;MAC1D,IAAIyD,MAAM,CAAChE,QAAQ,CAAC,IAAI3B,uBAAuB,IAAIiH,SAAS,IAAI/E,WAAW,EAAE;QAAE;QAC7EiI,SAAS,gBACPzG,OAAA;UAAK+G,SAAS,EAAC;QAAwB;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACN;MACH,CAAC,MAAM,IAAIf,MAAM,CAAChE,QAAQ,CAAC,IAAI3B,uBAAuB,IAAIiH,SAAS,IAAItF,QAAQ,EAAE;QAAE;QACjFwI,SAAS,gBACPzG,OAAA;UAAAM,QAAA,GAAK,gCACE,eAAAN,OAAA,CAACnE,gBAAgB;YAACkL,SAAS,EAAC,YAAY;YAAC5F,KAAK,EAAE;cAACwF,MAAM,EAAE;YAAC;UAAE;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,UACrE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MACH,CAAC,MAAM;QACLyD,SAAS,gBACPzG,OAAA,CAAClD,IAAI,CAAC4J,IAAI;UACRvF,KAAK,EAAE;YACLwF,MAAM,EAAE;UACV,CAAE;UACFC,IAAI,EAAErD,SAAU;UAAAjD,QAAA,eAEhBN,OAAA,CAAC/C;UACC;UAAA;YACAsK,wBAAwB,EAAE,KAAM;YAChCC,QAAQ,EAAE,KAAM;YAChBT,SAAS,EAAC,qBAAqB;YAC/BW,cAAc,EAAC;YACf;YAAA;YACAC,gBAAgB,EAAC,OAAO;YACxBb,MAAM,EAAE1B,IAAK,CAAC;YAAA;YACdwC,QAAQ,EAAExC,IAAK,CAAC;YAAA;YAChByC,UAAU,EAAEtE,SAAS,IAAItF,QAAS;YAClC6J,UAAU,EAAEvE,SAAS,IAAItF,QAAS;YAClCgK,OAAO,EAAEU;UAAkB;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACZ;MACH;IACF,CAAC,MAAM;MACL;MACAyD,SAAS,GAAGD,eAAe,CAAC,CAAC;IAC/B;EACF;EAEA,oBAAOxG,OAAA;IAAA,GAAQ+D,SAAS;IAAAzD,QAAA,EAAGmG;EAAS;IAAA5D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAAgB,GAAA,CAhUMX,YAAY;AAAA0F,GAAA,GAAZ1F,YAAY;AAiUlB,SAAS2F,eAAeA,CAAC;EAAEvF,YAAY,GAAG,EAAE;EAAEwF,YAAY,GAAG,EAAE;EAAEC,aAAa;EAAEpE,KAAK;EAAEhB,SAAS;EAAEqF;AAAQ,CAAC,EAAEvH,GAAG,EAAG;EAAAwH,GAAA;EAEjH,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzL,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0L,UAAU,EAAEC,aAAa,CAAC,GAAG3L,QAAQ,CAAC,EAAE,CAAC,EAAC;EACjD,MAAM,CAAC6F,MAAM,EAAE+F,QAAQ,CAAC,GAAG5L,QAAQ,CAAC,EAAE,CAAC,EAAC;;EAExC;EACAH,mBAAmB,CAACkE,GAAG,EAAE,OAAO;IAC9B;IACA8H,oBAAoB,EAAEA,CAAA,KAAMA,oBAAoB,CAACH,UAAU,CAAC;IAC5DI,iBAAiB,EAAEA,CAAA,KAAMD,oBAAoB,CAACL,cAAc,CAAC;IAC7DO,yBAAyB,EAAEA,CAAA,KAAMA,yBAAyB,CAAC,CAAC;IAC5DC,aAAa,EAAEA,CAAA,KAAMA,aAAa,CAAC;EACrC,CAAC,CAAC,CAAC;EAEHpM,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,OAAO,CAACqE,YAAY,CAAC,EAAE;MAC1BqG,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACzM,EAAE,CAAC0M,SAAS,CAACtG,YAAY,CAAC,EAAEpG,EAAE,CAAC0M,SAAS,CAACd,YAAY,CAAC,EAAE5L,EAAE,CAAC0M,SAAS,CAACjF,KAAK,CAAC,CAAC,CAAC;;EAEjF;EACA,MAAM4E,oBAAoB,GAAIxE,IAAI,IAAK;IACrC,IAAI;MACF;MACAA,IAAI,GAAGA,IAAI,CAAC8E,MAAM,CAACvE,IAAI,IAAI,CAACA,IAAI,CAACvD,SAAS,CAAC;MAC3C,OAAO,CAACgD,IAAI,IAAI,EAAE,EAAEpD,GAAG,CAAC2D,IAAI,IAAI;QAC9B,IAAIwE,SAAS,GAAGxE,IAAI,CAACwE,SAAS;QAC9B,QAAQxE,IAAI,CAACE,QAAQ;UACnB,KAAKvJ,iBAAiB,CAACkL,OAAO;YAC5B;YACA2C,SAAS,GAAG,CAAC7K,OAAO,CAACqG,IAAI,CAACwE,SAAS,CAAC,GAAGxE,IAAI,CAACwE,SAAS,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGzE,IAAI,CAACwE,SAAS;YAChF;UACF,KAAK7N,iBAAiB,CAAC8L,IAAI;YACzB;YACA+B,SAAS,GAAG,CAAC7K,OAAO,CAACqG,IAAI,CAACwE,SAAS,CAAC,GAAGxE,IAAI,CAACwE,SAAS,CAAC9B,MAAM,CAAChK,WAAW,CAAC,GAAGsH,IAAI,CAACwE,SAAS;YAC1F;UACF;YACE;QACJ;QACA,OAAO;UAAE,GAAGxE,IAAI;UAAE,GAAG;YAAEwE;UAAU;QAAE,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdhL,UAAU,CAACiL,OAAO,CAAC,MAAM,CAAC;MAC1B,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMR,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI;MACF,IAAI1E,IAAI,GAAGwE,oBAAoB,CAACH,UAAU,CAAC;MAC3C;MACArE,IAAI,GAAGA,IAAI,CAACpD,GAAG,CAAC2D,IAAI;QAAA,IAAA4E,kBAAA,EAAAC,mBAAA;QAAA,OAClB;UACE,gBAAgB,GAAAD,kBAAA,GAAE5E,IAAI,CAACrH,YAAY,CAAC,cAAAiM,kBAAA,uBAAlBA,kBAAA,CAAoBtE,MAAM;UAC5C,SAAS,EAAEN,IAAI,CAACxH,QAAQ,CAAC;UACzB,WAAW,EAAEwH,IAAI,CAACjH,WAAW,CAAC;UAC9B,WAAW,EAAEiH,IAAI,CAACvH,UAAU,CAAC;UAC7B,eAAe,EAAEuH,IAAI,CAACnH,eAAe,CAAC,IAAI,GAAG;UAC7C,iBAAiB,GAAAgM,mBAAA,GAAE7E,IAAI,CAAChH,aAAa,CAAC,cAAA6L,mBAAA,uBAAnBA,mBAAA,CAAqBvE,MAAM;UAC9C,iBAAiB,EAAEN,IAAI,CAAC8E;QAC1B,CAAC;MAAA,CACF,CAAC;MACF,OAAOrF,IAAI,IAAI,EAAE;IACnB,CAAC,CAAC,OAAOP,CAAC,EAAE;MACVxF,UAAU,CAACiL,OAAO,CAAC,MAAM,CAAC;MAC1BlE,OAAO,CAACsE,IAAI,CAAC,eAAe,EAAE7F,CAAC,CAAC;MAChC;MACA,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAM8F,cAAc,GAAG9M,OAAO,CAAC,MAAOI,iBAAiB,CAACmL,aAAa,CAAE,EAAE,EAAE,CAAC;EAE5E,MAAMwB,iBAAiB,GAAGA,CAACC,IAAI,EAAE1I,MAAM,EAAEI,KAAK,EAAEuI,UAAU,EAAEC,UAAU,KAAK;IACzE,oBAAO7K,OAAA;MAAK+G,SAAS,EAAC,sBAAsB;MAAAzG,QAAA,gBAC1CN,OAAA;QACEgH,OAAO,EAAEA,CAAA,KAAM;UACbqB,MAAM,CAACpG,MAAM,EAAEI,KAAK,EAAEuI,UAAU,EAAEC,UAAU,CAAC;QAC/C,CAAE;QACF9D,SAAS,EAAC,YAAY;QAAAzG,QAAA,eACvBN,OAAA,CAACtE,aAAa;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACtBhD,OAAA;QAAAM,QAAA,EAAOqK;MAAI;QAAA9H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnBhD,OAAA;QACEgH,OAAO,EAAEA,CAAA,KAAM;UACb8D,GAAG,CAAC7I,MAAM,EAAEI,KAAK,EAAEuI,UAAU,EAAEC,UAAU,CAAC;QAC5C,CAAE;QACF9D,SAAS,EAAC,UAAU;QAAAzG,QAAA,eACrBN,OAAA,CAACrE,YAAY;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EACR,CAAC;EAED,MAAM+H,UAAU,GAAG,CACjB;IACEvI,KAAK,EAAE,IAAI;IACXe,SAAS,EAAEnF,YAAY;IACvB;IACA+D,MAAM,EAAEA,CAACwI,IAAI,EAAE1I,MAAM,EAAEI,KAAK,KAAMqI,iBAAiB,CAACC,IAAI,EAAE1I,MAAM,EAAEI,KAAK,EAAEjE,YAAY,EAAEC,gBAAgB,CAAE;IACzG2M,KAAK,EAAE;EACT,CAAC,EACD;IACExI,KAAK,EAAE,IAAI;IACXe,SAAS,EAAEtF,QAAQ;IACnBqF,QAAQ,EAAE,IAAI;IACd0H,KAAK,EAAE;EACT,CAAC,EACD;IACExI,KAAK,EAAEiI,cAAc;IACrBlH,SAAS,EAAE/E,WAAW;IACtB8E,QAAQ,EAAE,IAAI;IACd0H,KAAK,EAAE;EACT,CAAC,EACD;IACExI,KAAK,EAAE,GAAG;IACVe,SAAS,EAAErF,UAAU;IACrBoF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXe,SAAS,EAAE9E,aAAa;IACxB0D,MAAM,EAAEA,CAACwI,IAAI,EAAE1I,MAAM,EAAEI,KAAK,KAAMqI,iBAAiB,CAACC,IAAI,EAAE1I,MAAM,EAAEI,KAAK,EAAE5D,aAAa,EAAEC,iBAAiB,CAAE;IAC3GsM,KAAK,EAAE;EACT,CAAC,EACD;IACExI,KAAK,EAAE,KAAK;IACZe,SAAS,EAAEjF,eAAe;IAC1BgF,QAAQ,EAAE,IAAI;IACd0H,KAAK,EAAE;EACT,CAAC,EACD;IACExI,KAAK,EAAE,IAAI;IACXe,SAAS,EAAE,WAAW;IACtByH,KAAK,EAAE,GAAG;IACV7I,MAAM,EAAEA,CAAC8I,CAAC,EAAEhJ,MAAM,EAAEI,KAAK,KAAK;MAC5B,IAAIJ,MAAM,CAACC,SAAS,EAAE;QACpB,oBAAOlC,OAAA,CAAAE,SAAA,mBAAI,CAAC;MACd;MACA,oBACEF,OAAA,CAAChD,KAAK;QAACkO,IAAI,EAAE,EAAG;QAAA5K,QAAA,eACdN,OAAA,CAACpD,MAAM;UACL0F,IAAI,EAAC,MAAM;UACXC,IAAI,eAAEvC,OAAA,CAACvE,cAAc;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBgE,OAAO,EAAEA,CAAA,KAAM;YACbmE,YAAY,CAAClJ,MAAM,CAACD,GAAG,CAAC;UAC1B,CAAE;UACFkJ,IAAI,EAAC,OAAO;UACZE,QAAQ,EAAEnJ,MAAM,CAACC,SAAS,IAAID,MAAM,CAACsI,eAAgB,CAAC;UAAA;UACtD/H,KAAK,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEZ;EACF,CAAC,CACF;;EAED;EACA,MAAMqI,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO;MACLrJ,GAAG,EAAE5E,MAAM,CAAC,CAAC;MACbkO,WAAW,EAAE,EAAE;MAAS;MACxB/G,OAAO,EAAE,EAAE;MAAa;MACxBgH,SAAS,EAAEzF,SAAS;MAAI;MACxBmE,SAAS,EAAEnE,SAAS;MAAI;MACxB0F,YAAY,EAAE,EAAE;MAAQ;MACxBC,aAAa,EAAE,EAAE;MAAO;MACxB9F,QAAQ,EAAEvJ,iBAAiB,CAAC8K,OAAO;MAAE;MACrCwE,aAAa,EAAE,EAAE;MAAqB;MACtCxJ,SAAS,EAAE;IACb,CAAC;EACH,CAAC;;EAED;EACA,MAAM4H,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI6B,SAAS,GAAGC,qBAAqB,CAAC,CAAC,GAAG3C,YAAY,CAAC,CAAC;IACxDK,iBAAiB,CAAC,CAAC,GAAGqC,SAAS,CAAC,CAAC,CAAC,CAAC;IACnC,IAAIzG,IAAI,GAAG2G,aAAa,CAAC,CAAC,GAAGF,SAAS,CAAC,CAAC;IACxCnC,aAAa,CAACxK,SAAS,CAACkG,IAAI,CAAC,CAAC;IAC9B,MAAMxB,MAAM,GAAG5D,QAAQ,CAAC2D,YAAY,EAAEqB,KAAK,CAAC;IAC5C2E,QAAQ,CAAC/F,MAAM,CAAC;EAClB,CAAC;;EAED;EACA,MAAMkI,qBAAqB,GAAI3C,YAAY,IAAK;IAC9C,IAAI;MACF,IAAI7J,OAAO,CAAC6J,YAAY,CAAC,EAAE,OAAO,EAAE;MACpC;MACA,OAAOA,YAAY,CAACnH,GAAG,CAAC2D,IAAI,IAAI;QAAA,IAAAqG,iBAAA;QAC9B,IAAIC,OAAO,GAAG,CAAC,CAAC;QAChB,IAAIxG,UAAU,GAAGyG,aAAa,CAACvG,IAAI,CAAClB,OAAO,CAAC;QAC5C,MAAM;UAAEoB,QAAQ;UAAEC,IAAI;UAAEC;QAAU,CAAC,GAAGN,UAAU;QAChDwG,OAAO,CAAC/J,GAAG,GAAGrF,QAAQ,CAACsP,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/BF,OAAO,CAACpG,QAAQ,GAAGA,QAAQ;QAC3BoG,OAAO,CAAC/N,cAAc,CAAC,GAAG4H,IAAI;QAC9BmG,OAAO,CAACxN,UAAU,CAAC,GAAGsH,SAAS,CAAC,CAAC;QACjCkG,OAAO,CAACvN,WAAW,CAAC,IAAAsN,iBAAA,GAAGrG,IAAI,CAACjH,WAAW,CAAC,cAAAsN,iBAAA,uBAAjBA,iBAAA,CAAmBzK,QAAQ,CAAC,CAAC,CAAC,CAAC;QACtD,IAAIsE,QAAQ,IAAIvJ,iBAAiB,CAACkL,OAAO,EAAE;UACzCyE,OAAO,CAAC9B,SAAS,GAAGxE,IAAI,CAACwE,SAAS,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAE;QAClD,CAAC,MAAM,IAAIvG,QAAQ,IAAIvJ,iBAAiB,CAAC8L,IAAI,EAAE;UAC7C6D,OAAO,CAAC9B,SAAS,GAAG9M,MAAM,CAACsI,IAAI,CAACwE,SAAS,EAAE9L,WAAW,CAAC,EAAC;QAC1D;QACA,OAAO;UAAE,GAAGsH,IAAI;UAAE,GAAGsG;QAAQ,CAAC;MAChC,CAAC,CAAC,IAAI,EAAE;IACV,CAAC,CAAC,OAAOpH,CAAC,EAAE;MACVxF,UAAU,CAACiL,OAAO,CAAC,eAAe,CAAC;MACnClE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAExB,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMqH,aAAa,GAAIzH,OAAO,IAAK;IACjC,OAAQ,CAAC,GAAGd,YAAY,EAAElH,iBAAiB,CAAC,CAAEiJ,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKnB,OAAO,CAAC,IAAI,CAAC,CAAC;EAC3F,CAAC;;EAED;EACA,MAAMsH,aAAa,GAAIM,GAAG,IAAK;IAC7B,IAAIC,YAAY,GAAGD,GAAG,CAACE,SAAS,CAAE5G,IAAI,IAAKA,IAAI,CAACvD,SAAS,CAAC;IAC1D,IAAIkK,YAAY,KAAK,CAAC,CAAC,EAAE;MACvB,IAAIE,UAAU,GAAGjB,aAAa,CAAC,CAAC;MAChCc,GAAG,CAACI,IAAI,CAACD,UAAU,CAAC;IACtB;IACA,OAAOH,GAAG;EACZ,CAAC;;EAED;EACA,MAAM9D,MAAM,GAAGA,CAACpG,MAAM,EAAEI,KAAK,EAAEuI,UAAU,EAAEC,UAAU,KAAK;IACxD,MAAM2B,aAAa,GAAG,CAAC,GAAGjD,UAAU,CAAC;IACrCiD,aAAa,CAACnK,KAAK,CAAC,CAACuI,UAAU,CAAC,GAAG4B,aAAa,CAACnK,KAAK,CAAC,CAACuI,UAAU,CAAC,CAAC6B,OAAO,CAAC5B,UAAU,EAAE,EAAE,CAAC;IAC3FrB,aAAa,CAACxK,SAAS,CAACwN,aAAa,CAAC,CAAC;EACzC,CAAC;EAED,MAAM1B,GAAG,GAAGA,CAAC7I,MAAM,EAAEI,KAAK,EAAEuI,UAAU,EAAEC,UAAU,KAAK;IACrD,MAAM2B,aAAa,GAAG,CAAC,GAAGjD,UAAU,CAAC;IACrCiD,aAAa,CAACnK,KAAK,CAAC,CAACuI,UAAU,CAAC,GAAG4B,aAAa,CAACnK,KAAK,CAAC,CAACuI,UAAU,CAAC,GAAGC,UAAU;IAChFrB,aAAa,CAACxK,SAAS,CAACwN,aAAa,CAAC,CAAC;EACzC,CAAC;EAED,MAAMrB,YAAY,GAAInJ,GAAG,IAAK;IAC5B,MAAMwK,aAAa,GAAG,CAAC,GAAGjD,UAAU,CAAC;IACrCC,aAAa,CAACxK,SAAS,CAACwN,aAAa,CAACxC,MAAM,CAAEvE,IAAI,IAAKA,IAAI,CAACzD,GAAG,KAAKA,GAAG,CAAC,CAAC,CAAC;EAC5E,CAAC;;EAED;EACA,MAAM0K,UAAU,GAAGA,CAACzK,MAAM,EAAEK,IAAI,KAAK;IACnC,MAAM4C,IAAI,GAAG7F,gBAAgB,CAACkK,UAAU,EAAEtH,MAAM,EAAEK,IAAI,CAAC;IACvDkH,aAAa,CAACtE,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMyH,sBAAsB,GAAIpD,UAAU,IAAK;IAC7CA,UAAU,CAACqD,OAAO,CAAC,CAACC,KAAK,EAAExK,KAAK,KAAK;MACnC,IAAI,CAACwK,KAAK,CAAC3K,SAAS,IAAIG,KAAK,IAAIkH,UAAU,CAACxD,MAAM,GAAG,CAAC,KAAK8G,KAAK,CAACvO,eAAe,CAAC,IAAIjC,mBAAmB,CAACyQ,QAAQ,IAAI1N,OAAO,CAACyN,KAAK,CAACvO,eAAe,CAAC,CAAC,CAAC,EAAE;QACrJuO,KAAK,CAACvO,eAAe,CAAC,GAAGjC,mBAAmB,CAAC0Q,MAAM;MACrD;IACF,CAAC,CAAC;IACFvD,aAAa,CAACxK,SAAS,CAACuK,UAAU,CAAC,CAAC;EACtC,CAAC;EAED,MAAM/F,UAAU,GAAIpB,GAAG,IAAK;IAC1B;IACA,MAAM4D,OAAO,GAAG,CAAC,GAAGuD,UAAU,CAAC;IAC/B,MAAMlH,KAAK,GAAG2D,OAAO,CAACqG,SAAS,CAAE5G,IAAI,IAAKrD,GAAG,CAACJ,GAAG,KAAKyD,IAAI,CAACzD,GAAG,CAAC;IAC/D,MAAMyD,IAAI,GAAGO,OAAO,CAAC3D,KAAK,CAAC;IAC3B,IAAID,GAAG,CAACF,SAAS,IAAI,CAACpE,kBAAkB,CAACsE,GAAG,CAAC,EAAEA,GAAG,CAACF,SAAS,GAAG,KAAK,CAAC,CAAC;IACtE8D,OAAO,CAACgH,MAAM,CAAC3K,KAAK,EAAE,CAAC,EAAE;MAAE,GAAGoD,IAAI;MAAE,GAAGrD;IAAI,CAAC,CAAC;IAC7C,IAAI8C,IAAI,GAAG2G,aAAa,CAAC,CAAC,GAAG7F,OAAO,CAAC,CAAC;IACtC2G,sBAAsB,CAACzH,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMvB,OAAO,GAAGhG,OAAO,CAAC,MAAM;IAC5B;IACA,IAAIsP,QAAQ,GAAG,CAACvJ,MAAM,IAAI,EAAE,EAAEsG,MAAM,CAACvE,IAAI,IAAKA,IAAI,CAACyH,QAAQ,IAAIvO,eAAe,CAACwO,QAAQ,KAAK1H,IAAI,CAAC3B,SAAS,IAAItH,UAAU,CAAC4Q,QAAQ,IAAI3H,IAAI,CAAC3B,SAAS,IAAIA,SAAS,CAAG,CAAC,CAAChC,GAAG,CAACuL,QAAQ,KAAK;MAAEC,MAAM,EAAED,QAAQ,CAACE,gBAAgB;MAAEC,QAAQ,EAAEH,QAAQ,CAACpD;IAAU,CAAC,CAAC,CAAC;IAC3P,OAAO,CAAC,GAAGgD,QAAQ,CAAC;EACtB,CAAC,EAAE,CAACxJ,YAAY,EAAEC,MAAM,EAAEI,SAAS,CAAC,CAAC;;EAErC;EACA,MAAMF,cAAc,GAAGjG,OAAO,CAAC,MAAM;IACnC,MAAM8P,iBAAiB,GAAG,CAAC/J,MAAM,IAAI,EAAE,EAAEsG,MAAM,CAACvE,IAAI,IAAKA,IAAI,CAACyH,QAAQ,IAAIvO,eAAe,CAAC+O,IAAI,KAAMjI,IAAI,CAAC3B,SAAS,IAAItH,UAAU,CAAC4Q,QAAQ,IAAI3H,IAAI,CAAC3B,SAAS,IAAIA,SAAS,CAAG,CAAC;IAC5K,IAAI6J,gBAAgB,GAAGF,iBAAiB,CAAC3L,GAAG,CAACuL,QAAQ,KAAK;MAAEC,MAAM,EAAED,QAAQ,CAACE,gBAAgB;MAAEC,QAAQ,EAAEH,QAAQ,CAACpD,SAAS;MAAE2D,YAAY,EAAEP,QAAQ,CAACO;IAAa,CAAC,CAAC,CAAC;IACpK,OAAO,CAAC,GAAGD,gBAAgB,CAAE;EAC/B,CAAC,EAAE,CAAClK,YAAY,EAAEC,MAAM,EAAEI,SAAS,CAAC,CAAC;;EAErC;EACA,MAAMD,WAAW,GAAGlG,OAAO,CAAC,MAAM;IAChC,MAAM8P,iBAAiB,GAAG,CAAC/J,MAAM,IAAI,EAAE,EAAEsG,MAAM,CAACvE,IAAI,IAAKA,IAAI,CAACyH,QAAQ,IAAIvO,eAAe,CAACkP,QAAQ,KAAMpI,IAAI,CAAC3B,SAAS,IAAItH,UAAU,CAAC4Q,QAAQ,IAAI3H,IAAI,CAAC3B,SAAS,IAAIA,SAAS,CAAG,CAAC;IAChL,IAAIgK,aAAa,GAAGL,iBAAiB,CAAC3L,GAAG,CAACuL,QAAQ,KAAK;MAAEC,MAAM,EAAED,QAAQ,CAACE,gBAAgB;MAAEC,QAAQ,EAAEH,QAAQ,CAACpD,SAAS;MAAE2D,YAAY,EAAEP,QAAQ,CAACO;IAAa,CAAC,CAAC,CAAC;IACjK,OAAO,CAAC,GAAGE,aAAa,CAAE;EAC5B,CAAC,EAAE,CAACrK,YAAY,EAAEC,MAAM,EAAEI,SAAS,CAAC,CAAC;;EAErC;EACA,MAAM+F,aAAa,GAAGA,CAAA,KAAM;IAC1B3K,cAAc,CAAC2F,IAAI,CAAC,gCAAgC,EAAE,EAAE,EAAE;MAAEpB,YAAY;MAAEqB,KAAK,EAAEpB,MAAM;MAAE/B,KAAK,EAAE,EAAE;MAAEgC,OAAO;MAAEC,cAAc;MAAEC,WAAW;MAAEC,SAAS;MAAEiB,QAAQ,EAAEgJ;IAAsB,CAAC,CAAC;EACzL,CAAC;;EAED;EACA,MAAMA,qBAAqB,GAAGA,CAAC;IAAE3H;EAAY,CAAC,KAAK;IACjD,MAAMJ,OAAO,GAAG,CAAC,GAAGuD,UAAU,CAAC;IAC/B,MAAMyE,QAAQ,GAAG;MACfhM,GAAG,EAAE5E,MAAM,CAAC,CAAC;MACbkO,WAAW,EAAE,EAAE;MAAmB;MAClC/G,OAAO,EAAEjI,uBAAuB;MAAE;MAClCiP,SAAS,EAAEzF,SAAS;MAAc;MAClCmE,SAAS,EAAE7D,WAAW;MAAY;MAClCoF,YAAY,EAAE,EAAE;MAAkB;MAClCC,aAAa,EAAE,EAAE;MAAiB;MAClC9F,QAAQ,EAAEvJ,iBAAiB,CAAC8K,OAAO;MAAE;MACrCwE,aAAa,EAAE,EAAE;MAAqB;MACtCxJ,SAAS,EAAE;IACb,CAAC;IACD;IACA8D,OAAO,CAACgH,MAAM,CAAChH,OAAO,CAACD,MAAM,GAAG,CAAC,EAAE,CAAC,EAAEiI,QAAQ,CAAC;IAC/CrB,sBAAsB,CAAC3G,OAAO,CAAC,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMiI,SAAS,GAAGA,CAAC;IAAEC,MAAM;IAAEC;EAAK,CAAC,KAAK;IACtC,IAAKD,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEhN,EAAE,IAAIiN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEjN,EAAE,IAAM,CAAAgN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhN,EAAE,OAAKiN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjN,EAAE,CAAC,EAAE;MACzD,IAAIkN,WAAW,GAAG,CAAC,GAAG7E,UAAU,CAAC;MACjC,MAAM8E,WAAW,GAAGD,WAAW,CAAC/B,SAAS,CAAEiC,CAAC,IAAKA,CAAC,CAACtM,GAAG,KAAIkM,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhN,EAAE,EAAC;MACrE,MAAMqN,SAAS,GAAGH,WAAW,CAAC/B,SAAS,CAAEiC,CAAC,IAAKA,CAAC,CAACtM,GAAG,KAAImM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjN,EAAE,EAAC;MACjEkN,WAAW,GAAG5O,SAAS,CAAC4O,WAAW,EAAEC,WAAW,EAAEE,SAAS,CAAC;MAC5D/E,aAAa,CAACxK,SAAS,CAACoP,WAAW,CAAC,CAAC;IACvC;EACF,CAAC;EAED,oBACEpO,OAAA;IAAAM,QAAA,gBACEN,OAAA,CAACV,UAAU;MAACkP,SAAS,EAAE,CAACjP,sBAAsB,CAAE;MAAC0O,SAAS,EAAEA,SAAU;MAAA3N,QAAA,eACpEN,OAAA,CAACP,eAAe;QACdgP,KAAK,EAAElF,UAAU,CAACzH,GAAG,CAAEwM,CAAC,IAAKA,CAAC,CAACtM,GAAG,CAAE,CAAC;QAAA;QACrC0M,QAAQ,EAAE/O,2BAA4B;QAAAW,QAAA,eAExCN,OAAA,CAAC9C,KAAK;UACJyR,UAAU,EAAE;YACVC,IAAI,EAAE;cACJxM,GAAG,EAAE/B,mBAAmB;cACxBwO,IAAI,EAAExL;YACR;UACF,CAAE;UACFyL,MAAM,EAAC,KAAK,CAAC;UAAA;UACbC,YAAY,EAAEA,CAAA,KAAM,cAAe;UACnChI,SAAS,EAAC,gDAAgD;UAC1DS,QAAQ;UACR2B,OAAO,EAAEA,OAAQ;UACjBI,UAAU,EAAEA,UAAW;UACvByF,OAAO,EAAEjE,UAAU,CAACjJ,GAAG,CAAEmN,GAAG,IAAK;YAC/B,IAAI,CAACA,GAAG,CAAC3L,QAAQ,EAAE;cACjB,OAAO2L,GAAG;YACZ;YAEA,OAAO;cACL,GAAGA,GAAG;cACNC,MAAM,EAAGjN,MAAM,KAAM;gBACnBA,MAAM;gBACNqB,QAAQ,EAAE2L,GAAG,CAAC3L,QAAQ;gBACtBC,SAAS,EAAE0L,GAAG,CAAC1L,SAAS;gBACxBf,KAAK,EAAEyM,GAAG,CAACzM,KAAK;gBAChBgB,UAAU,EAAEA,UAAU;gBACtBC,YAAY,EAAEA,YAAY;gBAC1BC,MAAM,EAAEA,MAAM;gBACdC,OAAO,EAAEA,OAAO;gBAChBC,cAAc,EAAEA,cAAc;gBAC9BC,WAAW,EAAEA,WAAW;gBACxBC,SAAS,EAAEA;cACb,CAAC;YACH,CAAC;UACH,CAAC,CAAE;UACHqL,UAAU,EAAE;QAAM;UAAAtM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACe;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACbhD,OAAA,CAACf,qBAAqB;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEV;AAACoG,GAAA,CAvXQJ,eAAe;AAAAoG,GAAA,GAAfpG,eAAe;AAyXxB,eAAAqG,GAAA,gBAAe9R,UAAU,CAACyL,eAAe,CAAC;AAAC,IAAA/F,EAAA,EAAAG,GAAA,EAAA2F,GAAA,EAAAqG,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAArM,EAAA;AAAAqM,YAAA,CAAAlM,GAAA;AAAAkM,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}