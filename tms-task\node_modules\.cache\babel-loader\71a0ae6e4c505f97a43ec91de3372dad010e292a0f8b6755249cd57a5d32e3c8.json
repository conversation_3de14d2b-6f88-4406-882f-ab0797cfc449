{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\issueTrack\\\\views\\\\IssueHome\\\\IssueHome.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable no-use-before-define */\nimport { track_008_get_issue_list_query, track_002_get_issuegrp_info_query, setting_105_get_team_detail_query } from \"@common/api/query/query\";\nimport { useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\nimport { useQuerySetting320GetNodePrivQuery } from \"@common/service/commonHooks\";\nimport { eConsoleNodeId, eIssueViewMode, ePagination } from \"@common/utils/enum\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport * as toolUtil from \"@common/utils/toolUtil\";\nimport { eNodeTypeId } from \"@common/utils/TsbConfig\";\nimport { priSettingPermission } from \"@common/utils/logicUtils\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport { eCtxTypeId, eTreeOpType } from \"@common/utils/TsbConfig\";\nimport PageTitle from \"@components/PageTitle\";\nimport { Row, Space, Button } from \"antd\";\nimport { UnorderedListOutlined, MenuOutlined, AppstoreAddOutlined } from '@ant-design/icons';\nimport NoviceGuide from \"@components/NoviceGuide\"; // issue-新手引导\nimport { useQuery } from \"@tanstack/react-query\";\nimport { Layout, Spin } from \"antd\";\nimport { useEffect, useState, useRef } from \"react\";\nimport { Outlet, useAsyncValue, useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { useSearchQuery, useIssueSearchParams } from \"src/issueTrack/service/issueSearchHooks\";\nimport { useQueryIssue511_getPageById, useQuerySetting409_getTeamAttrgrpProps, useQueryTrack019GetPartitionDetail } from \"../../service/issueHooks\";\nimport \"./IssueHome.scss\";\nimport IssueTopSearchBar from \"./IssueTopSearchBar\";\nimport { track010 } from '@common/utils/ApiPath';\nimport { useImmer } from \"use-immer\";\nimport { objNodeListMoreOps } from \"@common/service/objNodeListMoreOps\";\nimport ContextBoard from \"@common/components/ContextBoard\";\nimport { formatSvg, getIconValueByIdType, getViewModePath, getPropValueByIdType } from \"src/issueTrack/utils/ArrayUtils\";\nimport { isContain, scrollToAnchor } from \"@common/utils/ViewUtils\";\nimport { useQuerySetting234_getTeamMbrUserInfo } from \"@common/service/commonHooks\";\nimport { isEmpty } from '@common/utils/ArrayUtils';\nimport ScrollableHeader from \"@components/ScrollableHeader\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function IssueHome() {\n  _s();\n  var _nodeItem;\n  const location = useLocation();\n  //const navigate = useNavigate();\n  const contextboardRef = useRef(null);\n  const {\n    teamId,\n    nodeId: issueListNodeId\n  } = useParams();\n  const {\n    setting320Result,\n    issue506Result,\n    userList = [],\n    spaceUserList = [],\n    allSpaceUserList = [],\n    queryKeywords,\n    objInfo,\n    criteriaList: _searchQuery\n  } = useAsyncValue(); //预加载获取的数据\n  const {\n    data: selectionList,\n    isLoading: isLoadingCodeValueList,\n    dataUpdatedAt: dataUpdatedAtSetting407,\n    refetch: refechCodeValueList\n  } = useQuerySetting407_getCodeValueList(teamId); //字典数据\n\n  //自定义表单属性列表\n  const {\n    subclassAttrList,\n    isLoading: isLoadingGetSubclassAttrs\n  } = useQuerySetting409_getTeamAttrgrpProps(teamId, issueListNodeId, issue506Result === null || issue506Result === void 0 ? void 0 : issue506Result.subclassNid, !!(issue506Result !== null && issue506Result !== void 0 && issue506Result.subclassNid));\n  //获取项目详情\n  const {\n    data: projectInfo = {},\n    isLoading\n  } = useQuery(track_002_get_issuegrp_info_query(teamId, issueListNodeId, issue506Result === null || issue506Result === void 0 ? void 0 : issue506Result.projectId, !!(issue506Result !== null && issue506Result !== void 0 && issue506Result.projectId)));\n  const {\n    data: {\n      issueViewType\n    } = {},\n    isFetching: isLoadingGetTeamMbrUserInfo\n  } = useQuerySetting234_getTeamMbrUserInfo(teamId); // 团队个人基本信息\n\n  const {\n    attrList = [],\n    criteriaList = [],\n    bizNodeId,\n    objType,\n    createFlg,\n    isLoading: isLoadingGetGetPartitionDetail\n  } = useQueryTrack019GetPartitionDetail(teamId, (_nodeItem = nodeItem) === null || _nodeItem === void 0 ? void 0 : _nodeItem.nodeId, modalKey, nodeType == eNodeTypeId.nt_31704_objtype_issue_item);\n  const viewMode = getViewModePath(location.pathname); //视图回显\n\n  // 权限相关\n  const {\n    data: {\n      privSetting\n    }\n  } = useQuerySetting320GetNodePrivQuery({\n    teamId,\n    nodeId: issueListNodeId\n  });\n  const _issueViewType = viewMode == eIssueViewMode.ViewMode_1_ListDetail && !isEmpty(issueViewType) ? issueViewType : viewMode;\n\n  // console.log(\"viewMode\", viewMode);\n  // console.log(\"issueViewType\", issueViewType);\n  // console.log(\"_issueViewType\", _issueViewType);\n\n  // console.log(\"isLoadingCodeValueList\", isLoadingCodeValueList);\n\n  const [_issueList, setIssueList] = useImmer(null); //issue长短列表数据\n  const [_totalCnt, setTotalCnt] = useState(0); //查询结果集总的记录数，分页加载数据\n  const [iconSelectionLid, setIconSelectionLid] = useState(null); //icon列表id\n\n  // 搜索条件相关\n  const {\n    issueNodeId,\n    queryId,\n    setIssueSearchParams\n  } = useIssueSearchParams(); //issue路由配置，页面刷新\n  let pageSize = ePagination.PageSize_30; //默认pageSize为30\n  const {\n    searchQuery,\n    updateSearchQuery,\n    loading\n  } = useSearchQuery({\n    _searchQuery\n  }); //查询条件列表\n  // const [pageNo, setPageNo] = useState(1); //页码\n  const [keywords, setKeywords] = useState(\"\"); //关键字\n  const [orderBy, setOrderBy] = useState(eConsoleNodeId.Nid_11118_Issue_CreateDate); //排序，创建时间\n  const [ascendingFlg, setAscendingFlg] = useState(false); //false:倒序,true:正序 \n\n  const [contextKey, setContextKey] = useState([]); // 右键菜单选中的key\n\n  // TODO:当前issue过滤后会跳转至第一页，不能保留当前页码\n  // 定位信息，查询条件及页码：不监听issueNodeId，只在初始化和路由跳转时调用（需要先找到当前路由的页数）\n  const {\n    data: pageNo = 1,\n    isLoading: isLoadingGetPageById\n  } = useQueryIssue511_getPageById(teamId, issueListNodeId, issueNodeId, searchQuery, pageSize, keywords, orderBy, ascendingFlg, !loading); // 只在初始化时获取页码\n  const [currentIssueIdx, setCurrentIssueIdx] = useState(0); // 用于切换页码时，加载数据前，先设置选中的issue索引,上一条为当前页的最后一条，下一条为当前页的第一条\n  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);\n  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);\n  const [selectedKeys, setSelectedKeys] = useState([]); // 选中节点\n\n  // 无需依赖的，useEffect已做好依赖处理, \n  // 监听isFetchingIssueList，需要注意enabled不能依赖\n  const {\n    data: {\n      issueList,\n      tableColumn,\n      totalCnt\n    } = {},\n    isLoading: isLoadingIssueList,\n    dataUpdatedAt: dataUpdatedAtTrack008,\n    refetch: refetchIssueList\n  } = useQuery(track_008_get_issue_list_query(teamId, issueListNodeId, pageNo, pageSize, searchQuery, keywords, orderBy, ascendingFlg, !isLoadingGetPageById && !loading));\n\n  // 监听issueNodeId\n  useEffect(() => {\n    if (!!issueNodeId) {\n      setSelectedKeys([issueNodeId === null || issueNodeId === void 0 ? void 0 : issueNodeId.toString()]);\n    } else {\n      setSelectedKeys([]);\n    }\n  }, [issueNodeId]);\n\n  // 监听queryKeywords\n  useEffect(() => {\n    setKeywords(queryKeywords);\n  }, [queryKeywords]);\n\n  // 找到当前issue详情\n  function findByIssueId(data, nodeId) {\n    let nodeItem = data.find(el => el.nodeId == nodeId);\n    return nodeItem;\n  }\n  useEffect(() => {\n    // undefined时，不执行, []执行\n    if (dataUpdatedAtSetting407 && dataUpdatedAtTrack008 && issueList) {\n      var _tableColumn$find;\n      // 保存类别iconSelectionLid\n      let iconSelectionLid;\n      tableColumn.forEach(el => {\n        if (el.attrNid == eConsoleNodeId.Nid_11109_Issue_Type) {\n          iconSelectionLid = el.selectionLid;\n        }\n      });\n      let statusSelectionLid = (_tableColumn$find = tableColumn.find(column => column.attrNid == eConsoleNodeId.Nid_11110_Issue_Status)) === null || _tableColumn$find === void 0 ? void 0 : _tableColumn$find.selectionLid;\n      const processedIssueList = issueList.map(_issue => ({\n        ..._issue,\n        key: _issue.nodeId,\n        //增加key属性，IssueLongList表格需要用key属性 \n        anchorNodeId: issueListNodeId,\n        // 项目nodeId\n        realNodeId: _issue.nodeId,\n        // 对象nodeId，兼容快捷方式\n        label: _issue.title,\n        icon: formatSvg(getIconValueByIdType(selectionList, iconSelectionLid, _issue[eConsoleNodeId.Nid_11109_Issue_Type])),\n        // 图标\n        showMoreIcon: true,\n        // 显示更多...\n        statusCode: _issue[eConsoleNodeId.Nid_11110_Issue_Status],\n        // 状态名称\n        statusValue: getPropValueByIdType(selectionList, statusSelectionLid, _issue[eConsoleNodeId.Nid_11110_Issue_Status]) // 状态名称\n      }));\n      setIconSelectionLid(iconSelectionLid);\n      setIssueList(processedIssueList);\n      setTotalCnt(totalCnt);\n    }\n  }, [dataUpdatedAtSetting407, dataUpdatedAtTrack008, issueList]);\n\n  //issue编号发生变化, 判定是否可以上一条和下一条\n  //不能使用currentIssueIdx来判定，因为鼠标点击某一个issue时，currentIssueId发生变化，需要重新计算其对应的index\n  useEffect(() => {\n    if (_issueList && !isLoadingGetTeamMbrUserInfo) {\n      // issueNodeId有值时需要等isLoadingGetPageById加载结束\n      if (viewMode != eIssueViewMode.ViewMode_3_Kanban) {\n        // _issueList 无数据，则进行提示\n        if (totalCnt == 0) {\n          //globalUtil.info(\"暂无数据!\"); tmsbug-8034 无需提示\n          setIssueSearchParams({\n            viewMode: _issueViewType,\n            issueNodeId: null,\n            queryId\n          });\n        } else {\n          var _issueList$currentIss;\n          // issueList 有数据，则进行数据处理\n          // 根据issueId 路由跳转issue详情\n          if (issueNodeId) {\n            // 查找列表中是否有issueId\n            let foundNode = findByIssueId(_issueList, issueNodeId);\n            if (foundNode) {\n              scrollToSelectedKey(issueNodeId);\n              // tmsbug-7811:评论对应的“链接”点击或嵌入到富文本里，原本定位到评论的功能缺失 解决：加载记忆视图只需要在初始化时进行，路由中存在issueId,默认为非初始化，无需加载记忆视图\n              // setIssueSearchParams({viewMode:_issueViewType, issueNodeId: issueNodeId, queryId})\n              return;\n            }\n          }\n          let _issueNodeId = (_issueList$currentIss = _issueList[currentIssueIdx]) === null || _issueList$currentIss === void 0 ? void 0 : _issueList$currentIss.nodeId;\n          if (!_issueNodeId) {\n            if (currentIssueIdx - 1 >= 0) {\n              var _issueList2;\n              // 找不到则取上一个；\n              _issueNodeId = (_issueList2 = _issueList[currentIssueIdx - 1]) === null || _issueList2 === void 0 ? void 0 : _issueList2.nodeId;\n            } else {\n              var _issueList$;\n              _issueNodeId = ((_issueList$ = _issueList[0]) === null || _issueList$ === void 0 ? void 0 : _issueList$.nodeId) || null; // 找不到上一个则默认选中第一个，全部没有则清空\n            }\n          }\n          setIssueSearchParams({\n            viewMode: _issueViewType,\n            issueNodeId: _issueNodeId,\n            queryId\n          });\n          scrollToSelectedKey(_issueNodeId);\n        }\n      }\n    }\n  }, [_issueList, isLoadingGetTeamMbrUserInfo]);\n  useEffect(() => {\n    if (_issueList && issueNodeId) {\n      if (viewMode != eIssueViewMode.ViewMode_3_Kanban) {\n        let _idx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);\n        setPreviousIssueLinkDisabledFlg(pageNo == 1 && _idx == 0);\n        setNextIssueLinkDisabledFlg(pageNo == Math.ceil(totalCnt / pageSize) && _idx == (_issueList === null || _issueList === void 0 ? void 0 : _issueList.length) - 1);\n      }\n    }\n  }, [_issueList, issueNodeId]);\n\n  //  滚动至选中的节点\n  const scrollToSelectedKey = objNodeId => {\n    if (!!objNodeId) {\n      setTimeout(() => {\n        let target = document.querySelector(`div[data-content-key=\"${objNodeId}\"]`); // 节点 兼容长短列表, 看板视图不支持\n        if (!isContain(target)) {\n          scrollToAnchor(target);\n        }\n      }, 100); //20250522 500ms -> 100ms\n    }\n  };\n\n  // 处理定位图标点击事件\n  const onPositioningClick = nodeId => {\n    if (issueNodeId) {\n      scrollToSelectedKey(issueNodeId);\n    }\n  };\n\n  // 上一条\n  function gotoPreviousIssue() {\n    const currentIssueIdx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);\n    if (currentIssueIdx == 0) {\n      //当前页的最后一行记录，再往前即需要向前翻页\n      if (pageNo > 1) {\n        // setPageNo(pageNo - 1);\n        globalUtil.getQueryClient().setQueryData([track010, teamId, issueListNodeId], pageNo => pageNo - 1); //触发 useQueryIssue017_getIssueList 再次加载\n        setCurrentIssueIdx(ePagination.PageSize_30 - 1);\n      }\n    } else {\n      setIssueSearchParams({\n        viewMode: _issueViewType,\n        issueNodeId: _issueList[currentIssueIdx - 1].nodeId\n      });\n    }\n  }\n\n  // 下一条\n  function gotoNextIssue() {\n    const currentIssueIdx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);\n    if (currentIssueIdx == pageSize - 1) {\n      //当前页的最后一行记录，再往后即需要向后翻页\n      if (pageNo < Math.ceil(_totalCnt / pageSize)) {\n        // setPageNo(pageNo + 1);\n        const queryClient = globalUtil.getQueryClient();\n        queryClient.setQueryData([track010, teamId, issueListNodeId], pageNo => pageNo + 1); //触发 useQueryIssue017_getIssueList 再次加载\n        setCurrentIssueIdx(0);\n      }\n    } else {\n      setIssueSearchParams({\n        viewMode: _issueViewType,\n        issueNodeId: _issueList[currentIssueIdx + 1].nodeId\n      });\n    }\n  }\n\n  // 响应右击菜单中的 二级菜单项，比如 偏好设置或创建快捷方式至\n  const onMoreBtnClick = ({\n    nodeItem,\n    ctxType,\n    ...args\n  }) => {\n    //设置 图标颜色/文案颜色/快捷至桌面等 二级菜单项 点击响应\n    objNodeListMoreOps({\n      teamId,\n      nodeType: eNodeTypeId.nt_31704_objtype_issue_item,\n      objNode: {\n        ...nodeItem,\n        key: nodeItem.nodeId\n      },\n      ctxType,\n      setRootNode: setIssueList,\n      setTotal: setTotalCnt,\n      setCurrentIssueIdx: setCurrentIssueIdx,\n      args\n    });\n  };\n\n  //参考 IssueItem.jsx 右击，回调到此函数\n  const onShowMoreButtonClick = info => {\n    var _info$node$nodeId;\n    console.log(\"onShowMoreButtonClick info\", info);\n    contextboardRef.current.showContextBoard(info.event.nativeEvent || info.event, {\n      ...info.node,\n      nodeType: eNodeTypeId.nt_31704_objtype_issue_item\n    }, info.node.nodeId);\n    setSelectedKeys([...selectedKeys, (_info$node$nodeId = info.node.nodeId) === null || _info$node$nodeId === void 0 ? void 0 : _info$node$nodeId.toString()]); // 右击菜单显示时，选中当前节点\n    setContextKey(info.node.nodeId);\n  };\n\n  // react-contexify 隐藏事件，v6.0版本:废弃onHidden,改用onVisibilityChange属性，参考：https://github.com/fkhadra/react-contexify/pull/220\n  function handleOnVisibilityChange(isVisible) {\n    if (!isVisible && contextKey != issueNodeId) {\n      // 右击菜单隐藏时，取消选中当前节点\n      let _selectedKeys = selectedKeys.filter(selectedKey => selectedKey != contextKey);\n      setSelectedKeys([..._selectedKeys]);\n    }\n  }\n\n  // 新建采集口径设置\n  const openCreateIssuePartitionEvent = objInfo => {\n    globalEventBus.emit(\"openCreateIssuePartition\", \"\", {\n      ...objInfo\n    });\n  };\n\n  // 显示视图模式\n  const showViewMode = viewMode => {\n    setIssueSearchParams({\n      viewMode,\n      issueNodeId: null,\n      queryId\n    });\n  };\n\n  // Excel导出模态框状态\n  const [excelExportModalVisibleFlg, setExcelExportModalVisibleFlg] = useState(false);\n  if (isLoadingGetSubclassAttrs || isLoadingCodeValueList) {\n    return;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ScrollableHeader, {\n      title: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n          teamId: teamId,\n          nodeId: issueListNodeId,\n          powerLock: true,\n          refetchData: refetchIssueList,\n          onPositioningClick: onPositioningClick\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: 10,\n          children: true ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [objInfo.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp && priSettingPermission(privSetting) ? /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"fontsize-12 fontcolor-normal\",\n              onClick: () => openCreateIssuePartitionEvent(objInfo),\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                size: 2,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"iconfont shezhixitongshezhigongnengshezhishuxing fontsize-12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u91C7\\u96C6\\u53E3\\u5F84\\u8BBE\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"fontsize-12 fontcolor-normal\",\n              onClick: e => setExcelExportModalVisibleFlg(true),\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                size: 2,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"iconfont Excelup fontsize-12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Excel\\u5BFC\\u51FA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              className: \"issuehome-header-btns\",\n              size: 2,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                size: \"middle\",\n                type: viewMode != eIssueViewMode.ViewMode_2_ListOnly && viewMode != eIssueViewMode.ViewMode_3_Kanban ? \"primary\" : \"\",\n                title: \"\\u77ED\\u5217\\u8868\",\n                icon: /*#__PURE__*/_jsxDEV(UnorderedListOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 25\n                }, this),\n                onClick: () => showViewMode(eIssueViewMode.ViewMode_1_ListDetail)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"middle\",\n                type: viewMode == eIssueViewMode.ViewMode_2_ListOnly ? \"primary\" : \"\",\n                title: \"\\u957F\\u5217\\u8868\",\n                icon: /*#__PURE__*/_jsxDEV(MenuOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 25\n                }, this),\n                onClick: () => {\n                  showViewMode(eIssueViewMode.ViewMode_2_ListOnly);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"middle\",\n                type: viewMode == eIssueViewMode.ViewMode_3_Kanban ? \"primary\" : \"\",\n                title: \"\\u770B\\u677F\",\n                icon: /*#__PURE__*/_jsxDEV(AppstoreAddOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 25\n                }, this),\n                onClick: () => {\n                  showViewMode(eIssueViewMode.ViewMode_3_Kanban);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 7\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(IssueTopSearchBar, {\n        subclassAttrList: subclassAttrList,\n        subclassNid: issue506Result === null || issue506Result === void 0 ? void 0 : issue506Result.subclassNid\n        // userList={userList}\n        ,\n        allSpaceUserList: allSpaceUserList,\n        spaceUserList: spaceUserList,\n        selectionList: selectionList,\n        objInfo: objInfo,\n        projectId: issue506Result === null || issue506Result === void 0 ? void 0 : issue506Result.projectId,\n        projectNodeId: issue506Result === null || issue506Result === void 0 ? void 0 : issue506Result.projectNodeId,\n        showAllSet: true //区分阶段和issue\n        ,\n        setting320Result: setting320Result,\n        keywords: keywords,\n        setKeywords: setKeywords\n        // setPageNo={setPageNo}\n        // _criteriaList={_criteriaList}\n        ,\n        refechCodeValueList: refechCodeValueList,\n        criteriaList: searchQuery,\n        updateSearchQuery: updateSearchQuery,\n        projectInfo: projectInfo,\n        refetchIssueList: refetchIssueList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        flex: \"auto\",\n        backgroundColor: \"#fff\",\n        height: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: isLoadingIssueList,\n        wrapperClassName: \"issueSpin\",\n        children: /*#__PURE__*/_jsxDEV(Outlet, {\n          context: {\n            issue506Result,\n            subclassAttrList,\n            userList,\n            spaceUserList,\n            selectionList,\n            objInfo,\n            issueList: _issueList,\n            attrList: tableColumn,\n            totalCnt: _totalCnt,\n            pageNo,\n            /* setPageNo,  */\n            searchQuery,\n            iconSelectionLid,\n            setCurrentIssueIdx,\n            previousIssueLinkDisabledFlg,\n            nextIssueLinkDisabledFlg,\n            keywords,\n            gotoPreviousIssue,\n            gotoNextIssue,\n            ascendingFlg,\n            setAscendingFlg,\n            orderBy,\n            setOrderBy,\n            viewMode: _issueViewType,\n            refetchIssueList,\n            projectInfo,\n            setting320Result,\n            selectedKeys,\n            onMoreBtnClick,\n            onShowMoreButtonClick,\n            onPositioningClick\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(ContextBoard, {\n      ref: contextboardRef,\n      teamId: teamId,\n      onMoreBtnClick: onMoreBtnClick\n      // onCreateBtnClick={onCreateBtnClick} \n      ,\n      id: \"issue-context-menu\",\n      handleOnVisibilityChange: handleOnVisibilityChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(NoviceGuide, {\n      nodeType: eNodeTypeId.nt_31705_objtype_issue_list,\n      awakeFlg: 0\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n}\n_s(IssueHome, \"U+LBrkHdT3sSc1n2z/dNeVJqXyc=\", false, function () {\n  return [useLocation, useParams, useAsyncValue, useQuerySetting407_getCodeValueList, useQuerySetting409_getTeamAttrgrpProps, useQuery, useQuerySetting234_getTeamMbrUserInfo, useQueryTrack019GetPartitionDetail, useQuerySetting320GetNodePrivQuery, useImmer, useIssueSearchParams, useSearchQuery, useQueryIssue511_getPageById, useQuery];\n});\n_c = IssueHome;\nvar _c;\n$RefreshReg$(_c, \"IssueHome\");", "map": {"version": 3, "names": ["track_008_get_issue_list_query", "track_002_get_issuegrp_info_query", "setting_105_get_team_detail_query", "useQuerySetting407_getCodeValueList", "useQuerySetting320GetNodePrivQuery", "eConsoleNodeId", "eIssueViewMode", "ePagination", "globalUtil", "toolUtil", "eNodeTypeId", "priSettingPermission", "globalEventBus", "eCtxTypeId", "eTreeOpType", "Page<PERSON><PERSON>le", "Row", "Space", "<PERSON><PERSON>", "UnorderedListOutlined", "MenuOutlined", "AppstoreAddOutlined", "NoviceGuide", "useQuery", "Layout", "Spin", "useEffect", "useState", "useRef", "Outlet", "useAsyncValue", "useLocation", "useNavigate", "useParams", "useSearchQuery", "useIssueSearchParams", "useQueryIssue511_getPageById", "useQuerySetting409_getTeamAttrgrpProps", "useQueryTrack019GetPartitionDetail", "IssueTopSearchBar", "track010", "useImmer", "objNodeListMoreOps", "ContextBoard", "formatSvg", "getIconValueByIdType", "getViewModePath", "getPropValueByIdType", "isContain", "scrollToAnchor", "useQuerySetting234_getTeamMbrUserInfo", "isEmpty", "ScrollableHeader", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "IssueHome", "_s", "_nodeItem", "location", "contextboardRef", "teamId", "nodeId", "issueListNodeId", "setting320Result", "issue506Result", "userList", "spaceUserList", "allSpaceUserList", "queryKeywords", "objInfo", "criteriaList", "_searchQuery", "data", "selectionList", "isLoading", "isLoadingCodeValueList", "dataUpdatedAt", "dataUpdatedAtSetting407", "refetch", "refechCodeValueList", "subclassAttrList", "isLoadingGetSubclassAttrs", "subclassNid", "projectInfo", "projectId", "issueViewType", "isFetching", "isLoadingGetTeamMbrUserInfo", "attrList", "bizNodeId", "objType", "createFlg", "isLoadingGetGetPartitionDetail", "nodeItem", "modalKey", "nodeType", "nt_31704_objtype_issue_item", "viewMode", "pathname", "privSetting", "_issueViewType", "ViewMode_1_ListDetail", "_issueList", "setIssueList", "_totalCnt", "setTotalCnt", "iconSelectionLid", "setIconSelectionLid", "issueNodeId", "queryId", "setIssueSearchParams", "pageSize", "PageSize_30", "searchQuery", "updateSearchQuery", "loading", "keywords", "setKeywords", "orderBy", "setOrderBy", "Nid_11118_Issue_CreateDate", "ascendingFlg", "setAscendingFlg", "<PERSON><PERSON>ey", "setContextKey", "pageNo", "isLoadingGetPageById", "currentIssueIdx", "setCurrentIssueIdx", "previousIssueLinkDisabledFlg", "setPreviousIssueLinkDisabledFlg", "nextIssueLinkDisabledFlg", "setNextIssueLinkDisabledFlg", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedKeys", "issueList", "tableColumn", "totalCnt", "isLoadingIssueList", "dataUpdatedAtTrack008", "refetchIssueList", "toString", "findByIssueId", "find", "el", "_tableColumn$find", "for<PERSON>ach", "attrNid", "Nid_11109_Issue_Type", "selectionLid", "statusSelectionLid", "column", "Nid_11110_Issue_Status", "processedIssueList", "map", "_issue", "key", "anchorNodeId", "realNodeId", "label", "title", "icon", "showMoreIcon", "statusCode", "statusValue", "ViewMode_3_Kanban", "_issueList$currentIss", "foundNode", "scrollToSelectedKey", "_issueNodeId", "_issueList2", "_issueList$", "_idx", "findIndex", "Math", "ceil", "length", "objNodeId", "setTimeout", "target", "document", "querySelector", "onPositioningClick", "gotoPreviousIssue", "getQueryClient", "setQueryData", "gotoNextIssue", "queryClient", "onMoreBtnClick", "ctxType", "args", "objNode", "setRootNode", "setTotal", "onShowMoreButtonClick", "info", "_info$node$nodeId", "console", "log", "current", "showContextBoard", "event", "nativeEvent", "node", "handleOnVisibilityChange", "isVisible", "_selected<PERSON><PERSON><PERSON>", "filter", "<PERSON><PERSON><PERSON>", "openCreateIssuePartitionEvent", "emit", "showViewMode", "excelExportModalVisibleFlg", "setExcelExportModalVisibleFlg", "children", "justify", "align", "powerLock", "refetchData", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "nt_31703_objtype_issue_project_adhoc_grp", "className", "onClick", "e", "type", "ViewMode_2_ListOnly", "projectNodeId", "showAllSet", "style", "flex", "backgroundColor", "height", "spinning", "wrapperClassName", "context", "ref", "id", "nt_31705_objtype_issue_list", "awakeFlg", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/issueTrack/views/IssueHome/IssueHome.jsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n/* eslint-disable no-use-before-define */\r\nimport { track_008_get_issue_list_query, track_002_get_issuegrp_info_query, setting_105_get_team_detail_query } from \"@common/api/query/query\";\r\nimport { useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\r\nimport { useQuerySetting320GetNodePrivQuery } from \"@common/service/commonHooks\";\r\nimport { eConsoleNodeId, eIssueViewMode, ePagination } from \"@common/utils/enum\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport * as toolUtil from \"@common/utils/toolUtil\";\r\nimport { eNodeTypeId } from \"@common/utils/TsbConfig\";\r\nimport { priSettingPermission } from \"@common/utils/logicUtils\";\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport { eCtxTypeId, eTreeOpType } from \"@common/utils/TsbConfig\";\r\nimport PageTitle from \"@components/PageTitle\";\r\nimport { Row, Space, Button } from \"antd\";\r\nimport { UnorderedListOutlined, MenuOutlined, AppstoreAddOutlined } from '@ant-design/icons';\r\nimport NoviceGuide from \"@components/NoviceGuide\"; // issue-新手引导\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { Layout, Spin } from \"antd\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { Outlet, useAsyncValue, useLocation, useNavigate, useParams } from \"react-router-dom\";\r\nimport { useSearchQuery, useIssueSearchParams } from \"src/issueTrack/service/issueSearchHooks\";\r\nimport { useQueryIssue511_getPageById, useQuerySetting409_getTeamAttrgrpProps, useQueryTrack019GetPartitionDetail   } from \"../../service/issueHooks\";\r\nimport \"./IssueHome.scss\";\r\nimport IssueTopSearchBar from \"./IssueTopSearchBar\";\r\nimport { track010 } from '@common/utils/ApiPath';\r\nimport { useImmer } from \"use-immer\";\r\nimport { objNodeListMoreOps } from \"@common/service/objNodeListMoreOps\";\r\nimport ContextBoard from \"@common/components/ContextBoard\";\r\nimport { formatSvg, getIconValueByIdType, getViewModePath, getPropValueByIdType } from \"src/issueTrack/utils/ArrayUtils\";\r\nimport { isContain, scrollToAnchor } from \"@common/utils/ViewUtils\";\r\nimport { useQuerySetting234_getTeamMbrUserInfo } from \"@common/service/commonHooks\";\r\nimport { isEmpty } from '@common/utils/ArrayUtils';\r\nimport ScrollableHeader from \"@components/ScrollableHeader\";\r\n\r\nexport default function IssueHome() {\r\n  const location = useLocation();\r\n  //const navigate = useNavigate();\r\n  const contextboardRef = useRef(null)\r\n  const { teamId, nodeId: issueListNodeId } = useParams();\r\n  const { setting320Result, issue506Result, userList = [], spaceUserList = [], allSpaceUserList = [], queryKeywords, objInfo, criteriaList: _searchQuery }\r\n    = useAsyncValue(); //预加载获取的数据\r\n  const { data: selectionList, isLoading: isLoadingCodeValueList, dataUpdatedAt:dataUpdatedAtSetting407, refetch: refechCodeValueList }\r\n    = useQuerySetting407_getCodeValueList(teamId); //字典数据\r\n\r\n  //自定义表单属性列表\r\n  const { subclassAttrList, isLoading: isLoadingGetSubclassAttrs }\r\n    = useQuerySetting409_getTeamAttrgrpProps(teamId, issueListNodeId, issue506Result?.subclassNid, !!issue506Result?.subclassNid);\r\n  //获取项目详情\r\n  const {data: projectInfo = {}, isLoading }\r\n    = useQuery(track_002_get_issuegrp_info_query(teamId, issueListNodeId, issue506Result?.projectId, !!issue506Result?.projectId))\r\n\r\n  const { data: { issueViewType } = {}, isFetching: isLoadingGetTeamMbrUserInfo }\r\n    = useQuerySetting234_getTeamMbrUserInfo(teamId); // 团队个人基本信息\r\n\r\n  const { attrList=[], criteriaList=[], bizNodeId, objType, createFlg, isLoading: isLoadingGetGetPartitionDetail,  } = useQueryTrack019GetPartitionDetail(teamId, nodeItem?.nodeId, modalKey, nodeType == eNodeTypeId.nt_31704_objtype_issue_item );\r\n\r\n  const viewMode = getViewModePath(location.pathname); //视图回显\r\n  \r\n  // 权限相关\r\n  const { data: { privSetting } } = useQuerySetting320GetNodePrivQuery({ teamId, nodeId: issueListNodeId });\r\n\r\n  const _issueViewType = (viewMode == eIssueViewMode.ViewMode_1_ListDetail && !isEmpty(issueViewType)) ? issueViewType : viewMode;\r\n\r\n  // console.log(\"viewMode\", viewMode);\r\n  // console.log(\"issueViewType\", issueViewType);\r\n  // console.log(\"_issueViewType\", _issueViewType);\r\n\r\n  // console.log(\"isLoadingCodeValueList\", isLoadingCodeValueList);\r\n \r\n  const [_issueList, setIssueList] = useImmer(null); //issue长短列表数据\r\n  const [_totalCnt, setTotalCnt] = useState(0); //查询结果集总的记录数，分页加载数据\r\n  const [iconSelectionLid, setIconSelectionLid] = useState(null); //icon列表id\r\n\r\n  // 搜索条件相关\r\n  const { issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams(); //issue路由配置，页面刷新\r\n  let pageSize = ePagination.PageSize_30; //默认pageSize为30\r\n  const {searchQuery, updateSearchQuery, loading } = useSearchQuery({ _searchQuery }); //查询条件列表\r\n  // const [pageNo, setPageNo] = useState(1); //页码\r\n  const [keywords, setKeywords] = useState(\"\"); //关键字\r\n  const [orderBy, setOrderBy] = useState(eConsoleNodeId.Nid_11118_Issue_CreateDate); //排序，创建时间\r\n  const [ascendingFlg, setAscendingFlg] = useState(false); //false:倒序,true:正序 \r\n\r\n  const [contextKey, setContextKey] = useState([]); // 右键菜单选中的key\r\n\r\n  // TODO:当前issue过滤后会跳转至第一页，不能保留当前页码\r\n  // 定位信息，查询条件及页码：不监听issueNodeId，只在初始化和路由跳转时调用（需要先找到当前路由的页数）\r\n  const {data: pageNo = 1, isLoading: isLoadingGetPageById }\r\n    = useQueryIssue511_getPageById(teamId, issueListNodeId, issueNodeId, searchQuery, pageSize, keywords, orderBy, ascendingFlg, !loading ); // 只在初始化时获取页码\r\n  const [currentIssueIdx, setCurrentIssueIdx] = useState(0); // 用于切换页码时，加载数据前，先设置选中的issue索引,上一条为当前页的最后一条，下一条为当前页的第一条\r\n  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);\r\n  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);\r\n  const [selectedKeys, setSelectedKeys] = useState([]); // 选中节点\r\n\r\n  // 无需依赖的，useEffect已做好依赖处理, \r\n  // 监听isFetchingIssueList，需要注意enabled不能依赖\r\n  const { \r\n    data: {issueList, tableColumn, totalCnt} = {}, \r\n    isLoading: isLoadingIssueList, \r\n    dataUpdatedAt: dataUpdatedAtTrack008, \r\n    refetch:refetchIssueList \r\n  } = useQuery(track_008_get_issue_list_query(teamId, issueListNodeId, pageNo, pageSize, searchQuery, keywords, orderBy, ascendingFlg, !isLoadingGetPageById && !loading))\r\n\r\n  // 监听issueNodeId\r\n  useEffect(() => {\r\n    if(!!issueNodeId){\r\n      setSelectedKeys([issueNodeId?.toString()]);\r\n    } else {\r\n      setSelectedKeys([]);\r\n    }\r\n  }, [issueNodeId])\r\n\r\n  // 监听queryKeywords\r\n  useEffect(() => {\r\n    setKeywords(queryKeywords)\r\n  }, [queryKeywords])\r\n  \r\n  // 找到当前issue详情\r\n  function findByIssueId(data, nodeId) {\r\n    let nodeItem = data.find((el) => el.nodeId == nodeId)\r\n    return nodeItem;\r\n  }\r\n\r\n  useEffect(() => {\r\n    // undefined时，不执行, []执行\r\n    if (dataUpdatedAtSetting407 && dataUpdatedAtTrack008 && issueList) {\r\n       // 保存类别iconSelectionLid\r\n       let iconSelectionLid;\r\n       tableColumn.forEach(el => {\r\n        if (el.attrNid == eConsoleNodeId.Nid_11109_Issue_Type) {\r\n          iconSelectionLid = el.selectionLid\r\n        }\r\n      })\r\n      let statusSelectionLid = tableColumn.find(column => column.attrNid == eConsoleNodeId.Nid_11110_Issue_Status)?.selectionLid;\r\n      const processedIssueList = issueList.map(_issue => ({\r\n        ..._issue,\r\n        key: _issue.nodeId, //增加key属性，IssueLongList表格需要用key属性 \r\n        anchorNodeId: issueListNodeId, // 项目nodeId\r\n        realNodeId: _issue.nodeId,     // 对象nodeId，兼容快捷方式\r\n        label: _issue.title,\r\n        icon: formatSvg(getIconValueByIdType(selectionList, iconSelectionLid, _issue[eConsoleNodeId.Nid_11109_Issue_Type])), // 图标\r\n        showMoreIcon: true, // 显示更多...\r\n        statusCode: _issue[eConsoleNodeId.Nid_11110_Issue_Status], // 状态名称\r\n        statusValue: getPropValueByIdType(selectionList, statusSelectionLid, _issue[eConsoleNodeId.Nid_11110_Issue_Status]) // 状态名称\r\n      }));\r\n      setIconSelectionLid(iconSelectionLid)\r\n      setIssueList(processedIssueList)\r\n      setTotalCnt(totalCnt)\r\n    }\r\n  }, [dataUpdatedAtSetting407, dataUpdatedAtTrack008, issueList]);\r\n\r\n  //issue编号发生变化, 判定是否可以上一条和下一条\r\n  //不能使用currentIssueIdx来判定，因为鼠标点击某一个issue时，currentIssueId发生变化，需要重新计算其对应的index\r\n  useEffect(() => {\r\n    if (_issueList  && !isLoadingGetTeamMbrUserInfo ) { // issueNodeId有值时需要等isLoadingGetPageById加载结束\r\n      if(viewMode != eIssueViewMode.ViewMode_3_Kanban){\r\n      // _issueList 无数据，则进行提示\r\n      if (totalCnt == 0) {\r\n        //globalUtil.info(\"暂无数据!\"); tmsbug-8034 无需提示\r\n        setIssueSearchParams({viewMode:_issueViewType, issueNodeId: null, queryId})\r\n      } else {\r\n        // issueList 有数据，则进行数据处理\r\n        // 根据issueId 路由跳转issue详情\r\n        if (issueNodeId) {\r\n          // 查找列表中是否有issueId\r\n          let foundNode = findByIssueId(_issueList, (issueNodeId))\r\n          if (foundNode) {\r\n            scrollToSelectedKey(issueNodeId);\r\n            // tmsbug-7811:评论对应的“链接”点击或嵌入到富文本里，原本定位到评论的功能缺失 解决：加载记忆视图只需要在初始化时进行，路由中存在issueId,默认为非初始化，无需加载记忆视图\r\n            // setIssueSearchParams({viewMode:_issueViewType, issueNodeId: issueNodeId, queryId})\r\n            return;\r\n          } \r\n        } \r\n        let _issueNodeId = _issueList[currentIssueIdx]?.nodeId;\r\n        if(!_issueNodeId){\r\n          if(currentIssueIdx-1 >= 0) { // 找不到则取上一个；\r\n            _issueNodeId = _issueList[currentIssueIdx-1]?.nodeId\r\n          } else {\r\n            _issueNodeId = _issueList[0]?.nodeId || null; // 找不到上一个则默认选中第一个，全部没有则清空\r\n          }\r\n        }\r\n        setIssueSearchParams({viewMode:_issueViewType, issueNodeId: _issueNodeId, queryId})\r\n        scrollToSelectedKey(_issueNodeId);\r\n      }\r\n    }\r\n  }\r\n  }, [_issueList, isLoadingGetTeamMbrUserInfo])\r\n\r\n  useEffect(() => {\r\n    if( _issueList && issueNodeId){\r\n      if(viewMode != eIssueViewMode.ViewMode_3_Kanban){\r\n        let _idx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);\r\n        setPreviousIssueLinkDisabledFlg(pageNo == 1 && _idx == 0);\r\n        setNextIssueLinkDisabledFlg(pageNo == Math.ceil(totalCnt / pageSize) && _idx == _issueList?.length - 1);\r\n      }\r\n    } \r\n  }, [_issueList, issueNodeId])\r\n  \r\n  //  滚动至选中的节点\r\n  const scrollToSelectedKey = (objNodeId) => {\r\n    if (!!objNodeId) {\r\n      setTimeout(() => {\r\n        let target = document.querySelector(`div[data-content-key=\"${objNodeId}\"]`); // 节点 兼容长短列表, 看板视图不支持\r\n        if (!isContain(target)) {\r\n          scrollToAnchor(target);\r\n        }\r\n      },100); //20250522 500ms -> 100ms\r\n    }\r\n  }\r\n\r\n  // 处理定位图标点击事件\r\n  const onPositioningClick = (nodeId) => {\r\n    if (issueNodeId) {\r\n      scrollToSelectedKey(issueNodeId);\r\n    }\r\n  }\r\n\r\n  // 上一条\r\n  function gotoPreviousIssue() {\r\n    const currentIssueIdx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);\r\n    if (currentIssueIdx == 0) {  //当前页的最后一行记录，再往前即需要向前翻页\r\n      if (pageNo > 1) {\r\n        // setPageNo(pageNo - 1);\r\n        globalUtil.getQueryClient().setQueryData([track010, teamId, issueListNodeId], (pageNo)=>(pageNo - 1)); //触发 useQueryIssue017_getIssueList 再次加载\r\n        setCurrentIssueIdx(ePagination.PageSize_30 - 1);\r\n      }\r\n    } else {\r\n      setIssueSearchParams({viewMode:_issueViewType, issueNodeId: _issueList[currentIssueIdx - 1].nodeId});\r\n    }\r\n  }\r\n\r\n  // 下一条\r\n  function gotoNextIssue() {\r\n    const currentIssueIdx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);\r\n    if (currentIssueIdx == pageSize - 1) { //当前页的最后一行记录，再往后即需要向后翻页\r\n      if (pageNo < Math.ceil(_totalCnt / pageSize)) {\r\n        // setPageNo(pageNo + 1);\r\n        const queryClient = globalUtil.getQueryClient();\r\n        queryClient.setQueryData([track010, teamId, issueListNodeId], (pageNo)=>(pageNo + 1)); //触发 useQueryIssue017_getIssueList 再次加载\r\n        setCurrentIssueIdx(0);\r\n      }\r\n    } else {\r\n      setIssueSearchParams({viewMode:_issueViewType, issueNodeId: _issueList[currentIssueIdx + 1].nodeId});\r\n    }\r\n  }\r\n\r\n  // 响应右击菜单中的 二级菜单项，比如 偏好设置或创建快捷方式至\r\n  const onMoreBtnClick = ({ nodeItem, ctxType, ...args }) => {\r\n    //设置 图标颜色/文案颜色/快捷至桌面等 二级菜单项 点击响应\r\n    objNodeListMoreOps({\r\n      teamId,\r\n      nodeType: eNodeTypeId.nt_31704_objtype_issue_item,\r\n      objNode: {...nodeItem, key: nodeItem.nodeId },\r\n      ctxType,\r\n      setRootNode: setIssueList,\r\n      setTotal: setTotalCnt,\r\n      setCurrentIssueIdx: setCurrentIssueIdx,\r\n      args,\r\n    });\r\n  }\r\n\r\n  //参考 IssueItem.jsx 右击，回调到此函数\r\n  const onShowMoreButtonClick = (info) => {\r\n    console.log(\"onShowMoreButtonClick info\", info)\r\n    contextboardRef.current.showContextBoard(info.event.nativeEvent || info.event, {...info.node, nodeType: eNodeTypeId.nt_31704_objtype_issue_item}, info.node.nodeId, )\r\n    setSelectedKeys([...selectedKeys, info.node.nodeId?.toString()]);// 右击菜单显示时，选中当前节点\r\n    setContextKey(info.node.nodeId);\r\n  }\r\n\r\n   // react-contexify 隐藏事件，v6.0版本:废弃onHidden,改用onVisibilityChange属性，参考：https://github.com/fkhadra/react-contexify/pull/220\r\n   function handleOnVisibilityChange(isVisible) {\r\n    \r\n    if(!isVisible && contextKey != issueNodeId){  // 右击菜单隐藏时，取消选中当前节点\r\n      let _selectedKeys = selectedKeys.filter(selectedKey => selectedKey != contextKey);\r\n      setSelectedKeys([..._selectedKeys]);\r\n    }\r\n  }\r\n\r\n  // 新建采集口径设置\r\n  const openCreateIssuePartitionEvent = (objInfo) => {\r\n    globalEventBus.emit(\"openCreateIssuePartition\", \"\", { ...objInfo });\r\n  }\r\n\r\n  // 显示视图模式\r\n  const showViewMode = (viewMode) => {\r\n    setIssueSearchParams({ viewMode, issueNodeId: null, queryId });\r\n  }\r\n\r\n  // Excel导出模态框状态\r\n  const [excelExportModalVisibleFlg, setExcelExportModalVisibleFlg] = useState(false);\r\n\r\n  if (isLoadingGetSubclassAttrs || isLoadingCodeValueList) {\r\n    return\r\n  }\r\n\r\n  return <>\r\n\r\n    {/* 头部搜索栏 */}\r\n    <ScrollableHeader title={\r\n      <Row justify=\"space-between\" align=\"middle\">\r\n        <PageTitle teamId={teamId} nodeId={issueListNodeId} powerLock refetchData={refetchIssueList} onPositioningClick={onPositioningClick}/>\r\n        <Space size={10}>\r\n          {true ?\r\n            <>\r\n              {/* 如果有privWrite修改权限，显示设置  -- 23/1/5 xuying */}\r\n              {objInfo.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp && priSettingPermission(privSetting) ?\r\n                <a className='fontsize-12 fontcolor-normal' onClick={() => openCreateIssuePartitionEvent(objInfo)} >\r\n                  <Space size={2}>\r\n                    <span className=\"iconfont shezhixitongshezhigongnengshezhishuxing fontsize-12\"></span>\r\n                    <span>采集口径设置</span>\r\n                  </Space>\r\n                </a>\r\n                : <></>\r\n              }\r\n              <a className='fontsize-12 fontcolor-normal' onClick={(e) => setExcelExportModalVisibleFlg(true)} >\r\n                <Space size={2}>\r\n                  <span className=\"iconfont Excelup fontsize-12\"></span>\r\n                  <span>Excel导出</span>\r\n                </Space>\r\n              </a>\r\n              <Space className=\"issuehome-header-btns\" size={2}>\r\n                <Button size=\"middle\" type={(viewMode != eIssueViewMode.ViewMode_2_ListOnly && viewMode != eIssueViewMode.ViewMode_3_Kanban) ? \"primary\" : \"\"}\r\n                  title=\"短列表\"\r\n                  icon={<UnorderedListOutlined />}\r\n                  onClick={() => showViewMode(eIssueViewMode.ViewMode_1_ListDetail)}\r\n                />\r\n                <Button size=\"middle\" type={viewMode == eIssueViewMode.ViewMode_2_ListOnly ? \"primary\" : \"\"}\r\n                  title=\"长列表\"\r\n                  icon={< MenuOutlined />}\r\n                  onClick={() => { showViewMode(eIssueViewMode.ViewMode_2_ListOnly) }}\r\n                />\r\n                <Button size=\"middle\" type={viewMode == eIssueViewMode.ViewMode_3_Kanban ? \"primary\" : \"\"}\r\n                  title=\"看板\"\r\n                  icon={<AppstoreAddOutlined />}\r\n                  onClick={() => {\r\n                    showViewMode(eIssueViewMode.ViewMode_3_Kanban)\r\n                  }}\r\n                />\r\n              </Space>\r\n            </>\r\n            : <></>\r\n          }\r\n        </Space>\r\n      </Row>\r\n    }>\r\n      <IssueTopSearchBar\r\n        subclassAttrList={subclassAttrList}\r\n        subclassNid={issue506Result?.subclassNid}\r\n        // userList={userList}\r\n        allSpaceUserList={allSpaceUserList}\r\n        spaceUserList={spaceUserList}\r\n        selectionList={selectionList}\r\n        objInfo={objInfo}\r\n        projectId={issue506Result?.projectId}\r\n        projectNodeId={issue506Result?.projectNodeId}\r\n        showAllSet={true} //区分阶段和issue\r\n        setting320Result={setting320Result}\r\n        keywords={keywords}\r\n        setKeywords={setKeywords}\r\n        // setPageNo={setPageNo}\r\n        // _criteriaList={_criteriaList}\r\n        refechCodeValueList={refechCodeValueList}\r\n        criteriaList={searchQuery}\r\n        updateSearchQuery={updateSearchQuery}\r\n        projectInfo={projectInfo}\r\n        refetchIssueList={refetchIssueList}\r\n      />\r\n    </ScrollableHeader>\r\n    {/* 短列表/长列表/看板 视图 */}\r\n    <Layout style={{ flex: \"auto\", backgroundColor: \"#fff\", height: 0}}>\r\n      <Spin spinning={isLoadingIssueList} wrapperClassName=\"issueSpin\">\r\n        <Outlet context={{\r\n          issue506Result, subclassAttrList, userList, spaceUserList, selectionList, objInfo, issueList: _issueList, attrList: tableColumn, totalCnt: _totalCnt, pageNo, /* setPageNo,  */\r\n          searchQuery,\r\n          iconSelectionLid, setCurrentIssueIdx, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, keywords, gotoPreviousIssue, gotoNextIssue,\r\n          ascendingFlg, setAscendingFlg, orderBy, setOrderBy, viewMode: _issueViewType, refetchIssueList, projectInfo, setting320Result,\r\n          selectedKeys, onMoreBtnClick, onShowMoreButtonClick, onPositioningClick\r\n        }} />\r\n      </Spin>\r\n    </Layout>\r\n     {/* 右击菜单 */}\r\n     <ContextBoard\r\n        ref={contextboardRef}\r\n        teamId={teamId}\r\n        onMoreBtnClick={onMoreBtnClick}\r\n        // onCreateBtnClick={onCreateBtnClick} \r\n        id={\"issue-context-menu\"}\r\n        handleOnVisibilityChange={handleOnVisibilityChange}\r\n      />\r\n    {/* 新手引导 */}\r\n    <NoviceGuide nodeType={eNodeTypeId.nt_31705_objtype_issue_list} awakeFlg={0}/>\r\n  \r\n  </>;\r\n}"], "mappings": ";;AAAA;AACA;AACA,SAASA,8BAA8B,EAAEC,iCAAiC,EAAEC,iCAAiC,QAAQ,yBAAyB;AAC9I,SAASC,mCAAmC,QAAQ,6BAA6B;AACjF,SAASC,kCAAkC,QAAQ,6BAA6B;AAChF,SAASC,cAAc,EAAEC,cAAc,EAAEC,WAAW,QAAQ,oBAAoB;AAChF,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,UAAU,EAAEC,WAAW,QAAQ,yBAAyB;AACjE,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,GAAG,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AACzC,SAASC,qBAAqB,EAAEC,YAAY,EAAEC,mBAAmB,QAAQ,mBAAmB;AAC5F,OAAOC,WAAW,MAAM,yBAAyB,CAAC,CAAC;AACnD,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AACnC,SAASC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAC7F,SAASC,cAAc,EAAEC,oBAAoB,QAAQ,yCAAyC;AAC9F,SAASC,4BAA4B,EAAEC,sCAAsC,EAAEC,kCAAkC,QAAU,0BAA0B;AACrJ,OAAO,kBAAkB;AACzB,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,eAAe,EAAEC,oBAAoB,QAAQ,iCAAiC;AACxH,SAASC,SAAS,EAAEC,cAAc,QAAQ,yBAAyB;AACnE,SAASC,qCAAqC,QAAQ,6BAA6B;AACnF,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAOC,gBAAgB,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,eAAe,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,SAAA;EAClC,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM8B,eAAe,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM;IAAEkC,MAAM;IAAEC,MAAM,EAAEC;EAAgB,CAAC,GAAG/B,SAAS,CAAC,CAAC;EACvD,MAAM;IAAEgC,gBAAgB;IAAEC,cAAc;IAAEC,QAAQ,GAAG,EAAE;IAAEC,aAAa,GAAG,EAAE;IAAEC,gBAAgB,GAAG,EAAE;IAAEC,aAAa;IAAEC,OAAO;IAAEC,YAAY,EAAEC;EAAa,CAAC,GACpJ3C,aAAa,CAAC,CAAC,CAAC,CAAC;EACrB,MAAM;IAAE4C,IAAI,EAAEC,aAAa;IAAEC,SAAS,EAAEC,sBAAsB;IAAEC,aAAa,EAACC,uBAAuB;IAAEC,OAAO,EAAEC;EAAoB,CAAC,GACjI9E,mCAAmC,CAAC2D,MAAM,CAAC,CAAC,CAAC;;EAEjD;EACA,MAAM;IAAEoB,gBAAgB;IAAEN,SAAS,EAAEO;EAA0B,CAAC,GAC5D9C,sCAAsC,CAACyB,MAAM,EAAEE,eAAe,EAAEE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEkB,WAAW,EAAE,CAAC,EAAClB,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEkB,WAAW,EAAC;EAC/H;EACA,MAAM;IAACV,IAAI,EAAEW,WAAW,GAAG,CAAC,CAAC;IAAET;EAAU,CAAC,GACtCrD,QAAQ,CAACtB,iCAAiC,CAAC6D,MAAM,EAAEE,eAAe,EAAEE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEoB,SAAS,EAAE,CAAC,EAACpB,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEoB,SAAS,EAAC,CAAC;EAEhI,MAAM;IAAEZ,IAAI,EAAE;MAAEa;IAAc,CAAC,GAAG,CAAC,CAAC;IAAEC,UAAU,EAAEC;EAA4B,CAAC,GAC3EvC,qCAAqC,CAACY,MAAM,CAAC,CAAC,CAAC;;EAEnD,MAAM;IAAE4B,QAAQ,GAAC,EAAE;IAAElB,YAAY,GAAC,EAAE;IAAEmB,SAAS;IAAEC,OAAO;IAAEC,SAAS;IAAEjB,SAAS,EAAEkB;EAAiC,CAAC,GAAGxD,kCAAkC,CAACwB,MAAM,GAAAH,SAAA,GAAEoC,QAAQ,cAAApC,SAAA,uBAARA,SAAA,CAAUI,MAAM,EAAEiC,QAAQ,EAAEC,QAAQ,IAAIvF,WAAW,CAACwF,2BAA4B,CAAC;EAEjP,MAAMC,QAAQ,GAAGrD,eAAe,CAACc,QAAQ,CAACwC,QAAQ,CAAC,CAAC,CAAC;;EAErD;EACA,MAAM;IAAE1B,IAAI,EAAE;MAAE2B;IAAY;EAAE,CAAC,GAAGjG,kCAAkC,CAAC;IAAE0D,MAAM;IAAEC,MAAM,EAAEC;EAAgB,CAAC,CAAC;EAEzG,MAAMsC,cAAc,GAAIH,QAAQ,IAAI7F,cAAc,CAACiG,qBAAqB,IAAI,CAACpD,OAAO,CAACoC,aAAa,CAAC,GAAIA,aAAa,GAAGY,QAAQ;;EAE/H;EACA;EACA;;EAEA;;EAEA,MAAM,CAACK,UAAU,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACnD,MAAM,CAACiE,SAAS,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACiF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAM;IAAEmF,WAAW;IAAEC,OAAO;IAAEC;EAAqB,CAAC,GAAG7E,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC/E,IAAI8E,QAAQ,GAAG1G,WAAW,CAAC2G,WAAW,CAAC,CAAC;EACxC,MAAM;IAACC,WAAW;IAAEC,iBAAiB;IAAEC;EAAQ,CAAC,GAAGnF,cAAc,CAAC;IAAEuC;EAAa,CAAC,CAAC,CAAC,CAAC;EACrF;EACA,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC6F,OAAO,EAAEC,UAAU,CAAC,GAAG9F,QAAQ,CAACtB,cAAc,CAACqH,0BAA0B,CAAC,CAAC,CAAC;EACnF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAElD;EACA;EACA,MAAM;IAAC+C,IAAI,EAAEqD,MAAM,GAAG,CAAC;IAAEnD,SAAS,EAAEoD;EAAqB,CAAC,GACtD5F,4BAA4B,CAAC0B,MAAM,EAAEE,eAAe,EAAE8C,WAAW,EAAEK,WAAW,EAAEF,QAAQ,EAAEK,QAAQ,EAAEE,OAAO,EAAEG,YAAY,EAAE,CAACN,OAAQ,CAAC,CAAC,CAAC;EAC3I,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGvG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACwG,4BAA4B,EAAEC,+BAA+B,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EACvF,MAAM,CAAC0G,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAAC4G,YAAY,EAAEC,eAAe,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEtD;EACA;EACA,MAAM;IACJ+C,IAAI,EAAE;MAAC+D,SAAS;MAAEC,WAAW;MAAEC;IAAQ,CAAC,GAAG,CAAC,CAAC;IAC7C/D,SAAS,EAAEgE,kBAAkB;IAC7B9D,aAAa,EAAE+D,qBAAqB;IACpC7D,OAAO,EAAC8D;EACV,CAAC,GAAGvH,QAAQ,CAACvB,8BAA8B,CAAC8D,MAAM,EAAEE,eAAe,EAAE+D,MAAM,EAAEd,QAAQ,EAAEE,WAAW,EAAEG,QAAQ,EAAEE,OAAO,EAAEG,YAAY,EAAE,CAACK,oBAAoB,IAAI,CAACX,OAAO,CAAC,CAAC;;EAExK;EACA3F,SAAS,CAAC,MAAM;IACd,IAAG,CAAC,CAACoF,WAAW,EAAC;MACf0B,eAAe,CAAC,CAAC1B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLP,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC,EAAE,CAAC1B,WAAW,CAAC,CAAC;;EAEjB;EACApF,SAAS,CAAC,MAAM;IACd6F,WAAW,CAACjD,aAAa,CAAC;EAC5B,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,SAAS0E,aAAaA,CAACtE,IAAI,EAAEX,MAAM,EAAE;IACnC,IAAIgC,QAAQ,GAAGrB,IAAI,CAACuE,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACnF,MAAM,IAAIA,MAAM,CAAC;IACrD,OAAOgC,QAAQ;EACjB;EAEArE,SAAS,CAAC,MAAM;IACd;IACA,IAAIqD,uBAAuB,IAAI8D,qBAAqB,IAAIJ,SAAS,EAAE;MAAA,IAAAU,iBAAA;MAChE;MACA,IAAIvC,gBAAgB;MACpB8B,WAAW,CAACU,OAAO,CAACF,EAAE,IAAI;QACzB,IAAIA,EAAE,CAACG,OAAO,IAAIhJ,cAAc,CAACiJ,oBAAoB,EAAE;UACrD1C,gBAAgB,GAAGsC,EAAE,CAACK,YAAY;QACpC;MACF,CAAC,CAAC;MACF,IAAIC,kBAAkB,IAAAL,iBAAA,GAAGT,WAAW,CAACO,IAAI,CAACQ,MAAM,IAAIA,MAAM,CAACJ,OAAO,IAAIhJ,cAAc,CAACqJ,sBAAsB,CAAC,cAAAP,iBAAA,uBAAnFA,iBAAA,CAAqFI,YAAY;MAC1H,MAAMI,kBAAkB,GAAGlB,SAAS,CAACmB,GAAG,CAACC,MAAM,KAAK;QAClD,GAAGA,MAAM;QACTC,GAAG,EAAED,MAAM,CAAC9F,MAAM;QAAE;QACpBgG,YAAY,EAAE/F,eAAe;QAAE;QAC/BgG,UAAU,EAAEH,MAAM,CAAC9F,MAAM;QAAM;QAC/BkG,KAAK,EAAEJ,MAAM,CAACK,KAAK;QACnBC,IAAI,EAAEvH,SAAS,CAACC,oBAAoB,CAAC8B,aAAa,EAAEiC,gBAAgB,EAAEiD,MAAM,CAACxJ,cAAc,CAACiJ,oBAAoB,CAAC,CAAC,CAAC;QAAE;QACrHc,YAAY,EAAE,IAAI;QAAE;QACpBC,UAAU,EAAER,MAAM,CAACxJ,cAAc,CAACqJ,sBAAsB,CAAC;QAAE;QAC3DY,WAAW,EAAEvH,oBAAoB,CAAC4B,aAAa,EAAE6E,kBAAkB,EAAEK,MAAM,CAACxJ,cAAc,CAACqJ,sBAAsB,CAAC,CAAC,CAAC;MACtH,CAAC,CAAC,CAAC;MACH7C,mBAAmB,CAACD,gBAAgB,CAAC;MACrCH,YAAY,CAACkD,kBAAkB,CAAC;MAChChD,WAAW,CAACgC,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAAC5D,uBAAuB,EAAE8D,qBAAqB,EAAEJ,SAAS,CAAC,CAAC;;EAE/D;EACA;EACA/G,SAAS,CAAC,MAAM;IACd,IAAI8E,UAAU,IAAK,CAACf,2BAA2B,EAAG;MAAE;MAClD,IAAGU,QAAQ,IAAI7F,cAAc,CAACiK,iBAAiB,EAAC;QAChD;QACA,IAAI5B,QAAQ,IAAI,CAAC,EAAE;UACjB;UACA3B,oBAAoB,CAAC;YAACb,QAAQ,EAACG,cAAc;YAAEQ,WAAW,EAAE,IAAI;YAAEC;UAAO,CAAC,CAAC;QAC7E,CAAC,MAAM;UAAA,IAAAyD,qBAAA;UACL;UACA;UACA,IAAI1D,WAAW,EAAE;YACf;YACA,IAAI2D,SAAS,GAAGzB,aAAa,CAACxC,UAAU,EAAGM,WAAY,CAAC;YACxD,IAAI2D,SAAS,EAAE;cACbC,mBAAmB,CAAC5D,WAAW,CAAC;cAChC;cACA;cACA;YACF;UACF;UACA,IAAI6D,YAAY,IAAAH,qBAAA,GAAGhE,UAAU,CAACyB,eAAe,CAAC,cAAAuC,qBAAA,uBAA3BA,qBAAA,CAA6BzG,MAAM;UACtD,IAAG,CAAC4G,YAAY,EAAC;YACf,IAAG1C,eAAe,GAAC,CAAC,IAAI,CAAC,EAAE;cAAA,IAAA2C,WAAA;cAAE;cAC3BD,YAAY,IAAAC,WAAA,GAAGpE,UAAU,CAACyB,eAAe,GAAC,CAAC,CAAC,cAAA2C,WAAA,uBAA7BA,WAAA,CAA+B7G,MAAM;YACtD,CAAC,MAAM;cAAA,IAAA8G,WAAA;cACLF,YAAY,GAAG,EAAAE,WAAA,GAAArE,UAAU,CAAC,CAAC,CAAC,cAAAqE,WAAA,uBAAbA,WAAA,CAAe9G,MAAM,KAAI,IAAI,CAAC,CAAC;YAChD;UACF;UACAiD,oBAAoB,CAAC;YAACb,QAAQ,EAACG,cAAc;YAAEQ,WAAW,EAAE6D,YAAY;YAAE5D;UAAO,CAAC,CAAC;UACnF2D,mBAAmB,CAACC,YAAY,CAAC;QACnC;MACF;IACF;EACA,CAAC,EAAE,CAACnE,UAAU,EAAEf,2BAA2B,CAAC,CAAC;EAE7C/D,SAAS,CAAC,MAAM;IACd,IAAI8E,UAAU,IAAIM,WAAW,EAAC;MAC5B,IAAGX,QAAQ,IAAI7F,cAAc,CAACiK,iBAAiB,EAAC;QAC9C,IAAIO,IAAI,GAAGtE,UAAU,CAACuE,SAAS,CAAClB,MAAM,IAAIA,MAAM,CAAC9F,MAAM,IAAI+C,WAAW,CAAC;QACvEsB,+BAA+B,CAACL,MAAM,IAAI,CAAC,IAAI+C,IAAI,IAAI,CAAC,CAAC;QACzDxC,2BAA2B,CAACP,MAAM,IAAIiD,IAAI,CAACC,IAAI,CAACtC,QAAQ,GAAG1B,QAAQ,CAAC,IAAI6D,IAAI,IAAI,CAAAtE,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0E,MAAM,IAAG,CAAC,CAAC;MACzG;IACF;EACF,CAAC,EAAE,CAAC1E,UAAU,EAAEM,WAAW,CAAC,CAAC;;EAE7B;EACA,MAAM4D,mBAAmB,GAAIS,SAAS,IAAK;IACzC,IAAI,CAAC,CAACA,SAAS,EAAE;MACfC,UAAU,CAAC,MAAM;QACf,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,yBAAyBJ,SAAS,IAAI,CAAC,CAAC,CAAC;QAC7E,IAAI,CAACnI,SAAS,CAACqI,MAAM,CAAC,EAAE;UACtBpI,cAAc,CAACoI,MAAM,CAAC;QACxB;MACF,CAAC,EAAC,GAAG,CAAC,CAAC,CAAC;IACV;EACF,CAAC;;EAED;EACA,MAAMG,kBAAkB,GAAIzH,MAAM,IAAK;IACrC,IAAI+C,WAAW,EAAE;MACf4D,mBAAmB,CAAC5D,WAAW,CAAC;IAClC;EACF,CAAC;;EAED;EACA,SAAS2E,iBAAiBA,CAAA,EAAG;IAC3B,MAAMxD,eAAe,GAAGzB,UAAU,CAACuE,SAAS,CAAClB,MAAM,IAAIA,MAAM,CAAC9F,MAAM,IAAI+C,WAAW,CAAC;IACpF,IAAImB,eAAe,IAAI,CAAC,EAAE;MAAG;MAC3B,IAAIF,MAAM,GAAG,CAAC,EAAE;QACd;QACAvH,UAAU,CAACkL,cAAc,CAAC,CAAC,CAACC,YAAY,CAAC,CAACnJ,QAAQ,EAAEsB,MAAM,EAAEE,eAAe,CAAC,EAAG+D,MAAM,IAAIA,MAAM,GAAG,CAAE,CAAC,CAAC,CAAC;QACvGG,kBAAkB,CAAC3H,WAAW,CAAC2G,WAAW,GAAG,CAAC,CAAC;MACjD;IACF,CAAC,MAAM;MACLF,oBAAoB,CAAC;QAACb,QAAQ,EAACG,cAAc;QAAEQ,WAAW,EAAEN,UAAU,CAACyB,eAAe,GAAG,CAAC,CAAC,CAAClE;MAAM,CAAC,CAAC;IACtG;EACF;;EAEA;EACA,SAAS6H,aAAaA,CAAA,EAAG;IACvB,MAAM3D,eAAe,GAAGzB,UAAU,CAACuE,SAAS,CAAClB,MAAM,IAAIA,MAAM,CAAC9F,MAAM,IAAI+C,WAAW,CAAC;IACpF,IAAImB,eAAe,IAAIhB,QAAQ,GAAG,CAAC,EAAE;MAAE;MACrC,IAAIc,MAAM,GAAGiD,IAAI,CAACC,IAAI,CAACvE,SAAS,GAAGO,QAAQ,CAAC,EAAE;QAC5C;QACA,MAAM4E,WAAW,GAAGrL,UAAU,CAACkL,cAAc,CAAC,CAAC;QAC/CG,WAAW,CAACF,YAAY,CAAC,CAACnJ,QAAQ,EAAEsB,MAAM,EAAEE,eAAe,CAAC,EAAG+D,MAAM,IAAIA,MAAM,GAAG,CAAE,CAAC,CAAC,CAAC;QACvFG,kBAAkB,CAAC,CAAC,CAAC;MACvB;IACF,CAAC,MAAM;MACLlB,oBAAoB,CAAC;QAACb,QAAQ,EAACG,cAAc;QAAEQ,WAAW,EAAEN,UAAU,CAACyB,eAAe,GAAG,CAAC,CAAC,CAAClE;MAAM,CAAC,CAAC;IACtG;EACF;;EAEA;EACA,MAAM+H,cAAc,GAAGA,CAAC;IAAE/F,QAAQ;IAAEgG,OAAO;IAAE,GAAGC;EAAK,CAAC,KAAK;IACzD;IACAtJ,kBAAkB,CAAC;MACjBoB,MAAM;MACNmC,QAAQ,EAAEvF,WAAW,CAACwF,2BAA2B;MACjD+F,OAAO,EAAE;QAAC,GAAGlG,QAAQ;QAAE+D,GAAG,EAAE/D,QAAQ,CAAChC;MAAO,CAAC;MAC7CgI,OAAO;MACPG,WAAW,EAAEzF,YAAY;MACzB0F,QAAQ,EAAExF,WAAW;MACrBuB,kBAAkB,EAAEA,kBAAkB;MACtC8D;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,qBAAqB,GAAIC,IAAI,IAAK;IAAA,IAAAC,iBAAA;IACtCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEH,IAAI,CAAC;IAC/CxI,eAAe,CAAC4I,OAAO,CAACC,gBAAgB,CAACL,IAAI,CAACM,KAAK,CAACC,WAAW,IAAIP,IAAI,CAACM,KAAK,EAAE;MAAC,GAAGN,IAAI,CAACQ,IAAI;MAAE5G,QAAQ,EAAEvF,WAAW,CAACwF;IAA2B,CAAC,EAAEmG,IAAI,CAACQ,IAAI,CAAC9I,MAAQ,CAAC;IACrKyE,eAAe,CAAC,CAAC,GAAGD,YAAY,GAAA+D,iBAAA,GAAED,IAAI,CAACQ,IAAI,CAAC9I,MAAM,cAAAuI,iBAAA,uBAAhBA,iBAAA,CAAkBvD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACjEjB,aAAa,CAACuE,IAAI,CAACQ,IAAI,CAAC9I,MAAM,CAAC;EACjC,CAAC;;EAEA;EACA,SAAS+I,wBAAwBA,CAACC,SAAS,EAAE;IAE5C,IAAG,CAACA,SAAS,IAAIlF,UAAU,IAAIf,WAAW,EAAC;MAAG;MAC5C,IAAIkG,aAAa,GAAGzE,YAAY,CAAC0E,MAAM,CAACC,WAAW,IAAIA,WAAW,IAAIrF,UAAU,CAAC;MACjFW,eAAe,CAAC,CAAC,GAAGwE,aAAa,CAAC,CAAC;IACrC;EACF;;EAEA;EACA,MAAMG,6BAA6B,GAAI5I,OAAO,IAAK;IACjD3D,cAAc,CAACwM,IAAI,CAAC,0BAA0B,EAAE,EAAE,EAAE;MAAE,GAAG7I;IAAQ,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAM8I,YAAY,GAAIlH,QAAQ,IAAK;IACjCa,oBAAoB,CAAC;MAAEb,QAAQ;MAAEW,WAAW,EAAE,IAAI;MAAEC;IAAQ,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAM,CAACuG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG5L,QAAQ,CAAC,KAAK,CAAC;EAEnF,IAAIwD,yBAAyB,IAAIN,sBAAsB,EAAE;IACvD;EACF;EAEA,oBAAOvB,OAAA,CAAAE,SAAA;IAAAgK,QAAA,gBAGLlK,OAAA,CAACF,gBAAgB;MAAC8G,KAAK,eACrB5G,OAAA,CAACtC,GAAG;QAACyM,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAF,QAAA,gBACzClK,OAAA,CAACvC,SAAS;UAAC+C,MAAM,EAAEA,MAAO;UAACC,MAAM,EAAEC,eAAgB;UAAC2J,SAAS;UAACC,WAAW,EAAE9E,gBAAiB;UAAC0C,kBAAkB,EAAEA;QAAmB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACtI1K,OAAA,CAACrC,KAAK;UAACgN,IAAI,EAAE,EAAG;UAAAT,QAAA,EACb,IAAI,gBACHlK,OAAA,CAAAE,SAAA;YAAAgK,QAAA,GAEGjJ,OAAO,CAAC0B,QAAQ,IAAIvF,WAAW,CAACwN,wCAAwC,IAAIvN,oBAAoB,CAAC0F,WAAW,CAAC,gBAC5G/C,OAAA;cAAG6K,SAAS,EAAC,8BAA8B;cAACC,OAAO,EAAEA,CAAA,KAAMjB,6BAA6B,CAAC5I,OAAO,CAAE;cAAAiJ,QAAA,eAChGlK,OAAA,CAACrC,KAAK;gBAACgN,IAAI,EAAE,CAAE;gBAAAT,QAAA,gBACblK,OAAA;kBAAM6K,SAAS,EAAC;gBAA8D;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtF1K,OAAA;kBAAAkK,QAAA,EAAM;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,gBACF1K,OAAA,CAAAE,SAAA,mBAAI,CAAC,eAETF,OAAA;cAAG6K,SAAS,EAAC,8BAA8B;cAACC,OAAO,EAAGC,CAAC,IAAKd,6BAA6B,CAAC,IAAI,CAAE;cAAAC,QAAA,eAC9FlK,OAAA,CAACrC,KAAK;gBAACgN,IAAI,EAAE,CAAE;gBAAAT,QAAA,gBACblK,OAAA;kBAAM6K,SAAS,EAAC;gBAA8B;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtD1K,OAAA;kBAAAkK,QAAA,EAAM;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACJ1K,OAAA,CAACrC,KAAK;cAACkN,SAAS,EAAC,uBAAuB;cAACF,IAAI,EAAE,CAAE;cAAAT,QAAA,gBAC/ClK,OAAA,CAACpC,MAAM;gBAAC+M,IAAI,EAAC,QAAQ;gBAACK,IAAI,EAAGnI,QAAQ,IAAI7F,cAAc,CAACiO,mBAAmB,IAAIpI,QAAQ,IAAI7F,cAAc,CAACiK,iBAAiB,GAAI,SAAS,GAAG,EAAG;gBAC5IL,KAAK,EAAC,oBAAK;gBACXC,IAAI,eAAE7G,OAAA,CAACnC,qBAAqB;kBAAA0M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAChCI,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAAC/M,cAAc,CAACiG,qBAAqB;cAAE;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACF1K,OAAA,CAACpC,MAAM;gBAAC+M,IAAI,EAAC,QAAQ;gBAACK,IAAI,EAAEnI,QAAQ,IAAI7F,cAAc,CAACiO,mBAAmB,GAAG,SAAS,GAAG,EAAG;gBAC1FrE,KAAK,EAAC,oBAAK;gBACXC,IAAI,eAAE7G,OAAA,CAAElC,YAAY;kBAAAyM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxBI,OAAO,EAAEA,CAAA,KAAM;kBAAEf,YAAY,CAAC/M,cAAc,CAACiO,mBAAmB,CAAC;gBAAC;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACF1K,OAAA,CAACpC,MAAM;gBAAC+M,IAAI,EAAC,QAAQ;gBAACK,IAAI,EAAEnI,QAAQ,IAAI7F,cAAc,CAACiK,iBAAiB,GAAG,SAAS,GAAG,EAAG;gBACxFL,KAAK,EAAC,cAAI;gBACVC,IAAI,eAAE7G,OAAA,CAACjC,mBAAmB;kBAAAwM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BI,OAAO,EAAEA,CAAA,KAAM;kBACbf,YAAY,CAAC/M,cAAc,CAACiK,iBAAiB,CAAC;gBAChD;cAAE;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,eACR,CAAC,gBACD1K,OAAA,CAAAE,SAAA,mBAAI;QAAC;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;MAAAR,QAAA,eACClK,OAAA,CAACf,iBAAiB;QAChB2C,gBAAgB,EAAEA,gBAAiB;QACnCE,WAAW,EAAElB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEkB;QAC7B;QAAA;QACAf,gBAAgB,EAAEA,gBAAiB;QACnCD,aAAa,EAAEA,aAAc;QAC7BO,aAAa,EAAEA,aAAc;QAC7BJ,OAAO,EAAEA,OAAQ;QACjBe,SAAS,EAAEpB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEoB,SAAU;QACrCkJ,aAAa,EAAEtK,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEsK,aAAc;QAC7CC,UAAU,EAAE,IAAK,CAAC;QAAA;QAClBxK,gBAAgB,EAAEA,gBAAiB;QACnCqD,QAAQ,EAAEA,QAAS;QACnBC,WAAW,EAAEA;QACb;QACA;QAAA;QACAtC,mBAAmB,EAAEA,mBAAoB;QACzCT,YAAY,EAAE2C,WAAY;QAC1BC,iBAAiB,EAAEA,iBAAkB;QACrC/B,WAAW,EAAEA,WAAY;QACzByD,gBAAgB,EAAEA;MAAiB;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAEnB1K,OAAA,CAAC9B,MAAM;MAACkN,KAAK,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,eAAe,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAC,CAAE;MAAArB,QAAA,eACjElK,OAAA,CAAC7B,IAAI;QAACqN,QAAQ,EAAElG,kBAAmB;QAACmG,gBAAgB,EAAC,WAAW;QAAAvB,QAAA,eAC9DlK,OAAA,CAACzB,MAAM;UAACmN,OAAO,EAAE;YACf9K,cAAc;YAAEgB,gBAAgB;YAAEf,QAAQ;YAAEC,aAAa;YAAEO,aAAa;YAAEJ,OAAO;YAAEkE,SAAS,EAAEjC,UAAU;YAAEd,QAAQ,EAAEgD,WAAW;YAAEC,QAAQ,EAAEjC,SAAS;YAAEqB,MAAM;YAAE;YAC9JZ,WAAW;YACXP,gBAAgB;YAAEsB,kBAAkB;YAAEC,4BAA4B;YAAEE,wBAAwB;YAAEf,QAAQ;YAAEmE,iBAAiB;YAAEG,aAAa;YACxIjE,YAAY;YAAEC,eAAe;YAAEJ,OAAO;YAAEC,UAAU;YAAEtB,QAAQ,EAAEG,cAAc;YAAEwC,gBAAgB;YAAEzD,WAAW;YAAEpB,gBAAgB;YAC7HsE,YAAY;YAAEuD,cAAc;YAAEM,qBAAqB;YAAEZ;UACvD;QAAE;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER1K,OAAA,CAACX,YAAY;MACVsM,GAAG,EAAEpL,eAAgB;MACrBC,MAAM,EAAEA,MAAO;MACfgI,cAAc,EAAEA;MAChB;MAAA;MACAoD,EAAE,EAAE,oBAAqB;MACzBpC,wBAAwB,EAAEA;IAAyB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eAEJ1K,OAAA,CAAChC,WAAW;MAAC2E,QAAQ,EAAEvF,WAAW,CAACyO,2BAA4B;MAACC,QAAQ,EAAE;IAAE;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC;EAAA,eAE9E,CAAC;AACL;AAACtK,EAAA,CAtWuBD,SAAS;EAAA,QACd1B,WAAW,EAGgBE,SAAS,EAEjDH,aAAa,EAEb3B,mCAAmC,EAInCkC,sCAAsC,EAGtCd,QAAQ,EAGR2B,qCAAqC,EAE4EZ,kCAAkC,EAKrHlC,kCAAkC,EAUjCqC,QAAQ,EAKYN,oBAAoB,EAExBD,cAAc,EAW7DE,4BAA4B,EAa5Bb,QAAQ;AAAA;AAAA8N,EAAA,GAlEU5L,SAAS;AAAA,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}