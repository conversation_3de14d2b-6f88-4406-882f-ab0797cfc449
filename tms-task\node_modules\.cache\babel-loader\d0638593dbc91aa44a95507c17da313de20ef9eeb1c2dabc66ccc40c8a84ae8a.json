{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CharacterClassifier } from '../core/characterClassifier.js';\nexport class Uint8Matrix {\n  constructor(rows, cols, defaultValue) {\n    const data = new Uint8Array(rows * cols);\n    for (let i = 0, len = rows * cols; i < len; i++) {\n      data[i] = defaultValue;\n    }\n    this._data = data;\n    this.rows = rows;\n    this.cols = cols;\n  }\n  get(row, col) {\n    return this._data[row * this.cols + col];\n  }\n  set(row, col, value) {\n    this._data[row * this.cols + col] = value;\n  }\n}\nexport class StateMachine {\n  constructor(edges) {\n    let maxCharCode = 0;\n    let maxState = 0 /* State.Invalid */;\n    for (let i = 0, len = edges.length; i < len; i++) {\n      const [from, chCode, to] = edges[i];\n      if (chCode > maxCharCode) {\n        maxCharCode = chCode;\n      }\n      if (from > maxState) {\n        maxState = from;\n      }\n      if (to > maxState) {\n        maxState = to;\n      }\n    }\n    maxCharCode++;\n    maxState++;\n    const states = new Uint8Matrix(maxState, maxCharCode, 0 /* State.Invalid */);\n    for (let i = 0, len = edges.length; i < len; i++) {\n      const [from, chCode, to] = edges[i];\n      states.set(from, chCode, to);\n    }\n    this._states = states;\n    this._maxCharCode = maxCharCode;\n  }\n  nextState(currentState, chCode) {\n    if (chCode < 0 || chCode >= this._maxCharCode) {\n      return 0 /* State.Invalid */;\n    }\n    return this._states.get(currentState, chCode);\n  }\n}\n// State machine for http:// or https:// or file://\nlet _stateMachine = null;\nfunction getStateMachine() {\n  if (_stateMachine === null) {\n    _stateMachine = new StateMachine([[1 /* State.Start */, 104 /* CharCode.h */, 2 /* State.H */], [1 /* State.Start */, 72 /* CharCode.H */, 2 /* State.H */], [1 /* State.Start */, 102 /* CharCode.f */, 6 /* State.F */], [1 /* State.Start */, 70 /* CharCode.F */, 6 /* State.F */], [2 /* State.H */, 116 /* CharCode.t */, 3 /* State.HT */], [2 /* State.H */, 84 /* CharCode.T */, 3 /* State.HT */], [3 /* State.HT */, 116 /* CharCode.t */, 4 /* State.HTT */], [3 /* State.HT */, 84 /* CharCode.T */, 4 /* State.HTT */], [4 /* State.HTT */, 112 /* CharCode.p */, 5 /* State.HTTP */], [4 /* State.HTT */, 80 /* CharCode.P */, 5 /* State.HTTP */], [5 /* State.HTTP */, 115 /* CharCode.s */, 9 /* State.BeforeColon */], [5 /* State.HTTP */, 83 /* CharCode.S */, 9 /* State.BeforeColon */], [5 /* State.HTTP */, 58 /* CharCode.Colon */, 10 /* State.AfterColon */], [6 /* State.F */, 105 /* CharCode.i */, 7 /* State.FI */], [6 /* State.F */, 73 /* CharCode.I */, 7 /* State.FI */], [7 /* State.FI */, 108 /* CharCode.l */, 8 /* State.FIL */], [7 /* State.FI */, 76 /* CharCode.L */, 8 /* State.FIL */], [8 /* State.FIL */, 101 /* CharCode.e */, 9 /* State.BeforeColon */], [8 /* State.FIL */, 69 /* CharCode.E */, 9 /* State.BeforeColon */], [9 /* State.BeforeColon */, 58 /* CharCode.Colon */, 10 /* State.AfterColon */], [10 /* State.AfterColon */, 47 /* CharCode.Slash */, 11 /* State.AlmostThere */], [11 /* State.AlmostThere */, 47 /* CharCode.Slash */, 12 /* State.End */]]);\n  }\n  return _stateMachine;\n}\nlet _classifier = null;\nfunction getClassifier() {\n  if (_classifier === null) {\n    _classifier = new CharacterClassifier(0 /* CharacterClass.None */);\n    // allow-any-unicode-next-line\n    const FORCE_TERMINATION_CHARACTERS = ' \\t<>\\'\\\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…';\n    for (let i = 0; i < FORCE_TERMINATION_CHARACTERS.length; i++) {\n      _classifier.set(FORCE_TERMINATION_CHARACTERS.charCodeAt(i), 1 /* CharacterClass.ForceTermination */);\n    }\n    const CANNOT_END_WITH_CHARACTERS = '.,;:';\n    for (let i = 0; i < CANNOT_END_WITH_CHARACTERS.length; i++) {\n      _classifier.set(CANNOT_END_WITH_CHARACTERS.charCodeAt(i), 2 /* CharacterClass.CannotEndIn */);\n    }\n  }\n  return _classifier;\n}\nexport class LinkComputer {\n  static _createLink(classifier, line, lineNumber, linkBeginIndex, linkEndIndex) {\n    // Do not allow to end link in certain characters...\n    let lastIncludedCharIndex = linkEndIndex - 1;\n    do {\n      const chCode = line.charCodeAt(lastIncludedCharIndex);\n      const chClass = classifier.get(chCode);\n      if (chClass !== 2 /* CharacterClass.CannotEndIn */) {\n        break;\n      }\n      lastIncludedCharIndex--;\n    } while (lastIncludedCharIndex > linkBeginIndex);\n    // Handle links enclosed in parens, square brackets and curlys.\n    if (linkBeginIndex > 0) {\n      const charCodeBeforeLink = line.charCodeAt(linkBeginIndex - 1);\n      const lastCharCodeInLink = line.charCodeAt(lastIncludedCharIndex);\n      if (charCodeBeforeLink === 40 /* CharCode.OpenParen */ && lastCharCodeInLink === 41 /* CharCode.CloseParen */ || charCodeBeforeLink === 91 /* CharCode.OpenSquareBracket */ && lastCharCodeInLink === 93 /* CharCode.CloseSquareBracket */ || charCodeBeforeLink === 123 /* CharCode.OpenCurlyBrace */ && lastCharCodeInLink === 125 /* CharCode.CloseCurlyBrace */) {\n        // Do not end in ) if ( is before the link start\n        // Do not end in ] if [ is before the link start\n        // Do not end in } if { is before the link start\n        lastIncludedCharIndex--;\n      }\n    }\n    return {\n      range: {\n        startLineNumber: lineNumber,\n        startColumn: linkBeginIndex + 1,\n        endLineNumber: lineNumber,\n        endColumn: lastIncludedCharIndex + 2\n      },\n      url: line.substring(linkBeginIndex, lastIncludedCharIndex + 1)\n    };\n  }\n  static computeLinks(model) {\n    let stateMachine = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getStateMachine();\n    const classifier = getClassifier();\n    const result = [];\n    for (let i = 1, lineCount = model.getLineCount(); i <= lineCount; i++) {\n      const line = model.getLineContent(i);\n      const len = line.length;\n      let j = 0;\n      let linkBeginIndex = 0;\n      let linkBeginChCode = 0;\n      let state = 1 /* State.Start */;\n      let hasOpenParens = false;\n      let hasOpenSquareBracket = false;\n      let inSquareBrackets = false;\n      let hasOpenCurlyBracket = false;\n      while (j < len) {\n        let resetStateMachine = false;\n        const chCode = line.charCodeAt(j);\n        if (state === 13 /* State.Accept */) {\n          let chClass;\n          switch (chCode) {\n            case 40 /* CharCode.OpenParen */:\n              hasOpenParens = true;\n              chClass = 0 /* CharacterClass.None */;\n              break;\n            case 41 /* CharCode.CloseParen */:\n              chClass = hasOpenParens ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */;\n              break;\n            case 91 /* CharCode.OpenSquareBracket */:\n              inSquareBrackets = true;\n              hasOpenSquareBracket = true;\n              chClass = 0 /* CharacterClass.None */;\n              break;\n            case 93 /* CharCode.CloseSquareBracket */:\n              inSquareBrackets = false;\n              chClass = hasOpenSquareBracket ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */;\n              break;\n            case 123 /* CharCode.OpenCurlyBrace */:\n              hasOpenCurlyBracket = true;\n              chClass = 0 /* CharacterClass.None */;\n              break;\n            case 125 /* CharCode.CloseCurlyBrace */:\n              chClass = hasOpenCurlyBracket ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */;\n              break;\n            /* The following three rules make it that ' or \" or ` are allowed inside links if the link didn't begin with them */\n            case 39 /* CharCode.SingleQuote */:\n              chClass = linkBeginChCode === 39 /* CharCode.SingleQuote */ ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n              break;\n            case 34 /* CharCode.DoubleQuote */:\n              chClass = linkBeginChCode === 34 /* CharCode.DoubleQuote */ ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n              break;\n            case 96 /* CharCode.BackTick */:\n              chClass = linkBeginChCode === 96 /* CharCode.BackTick */ ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n              break;\n            case 42 /* CharCode.Asterisk */:\n              // `*` terminates a link if the link began with `*`\n              chClass = linkBeginChCode === 42 /* CharCode.Asterisk */ ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n              break;\n            case 124 /* CharCode.Pipe */:\n              // `|` terminates a link if the link began with `|`\n              chClass = linkBeginChCode === 124 /* CharCode.Pipe */ ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n              break;\n            case 32 /* CharCode.Space */:\n              // ` ` allow space in between [ and ]\n              chClass = inSquareBrackets ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */;\n              break;\n            default:\n              chClass = classifier.get(chCode);\n          }\n          // Check if character terminates link\n          if (chClass === 1 /* CharacterClass.ForceTermination */) {\n            result.push(LinkComputer._createLink(classifier, line, i, linkBeginIndex, j));\n            resetStateMachine = true;\n          }\n        } else if (state === 12 /* State.End */) {\n          let chClass;\n          if (chCode === 91 /* CharCode.OpenSquareBracket */) {\n            // Allow for the authority part to contain ipv6 addresses which contain [ and ]\n            hasOpenSquareBracket = true;\n            chClass = 0 /* CharacterClass.None */;\n          } else {\n            chClass = classifier.get(chCode);\n          }\n          // Check if character terminates link\n          if (chClass === 1 /* CharacterClass.ForceTermination */) {\n            resetStateMachine = true;\n          } else {\n            state = 13 /* State.Accept */;\n          }\n        } else {\n          state = stateMachine.nextState(state, chCode);\n          if (state === 0 /* State.Invalid */) {\n            resetStateMachine = true;\n          }\n        }\n        if (resetStateMachine) {\n          state = 1 /* State.Start */;\n          hasOpenParens = false;\n          hasOpenSquareBracket = false;\n          hasOpenCurlyBracket = false;\n          // Record where the link started\n          linkBeginIndex = j + 1;\n          linkBeginChCode = chCode;\n        }\n        j++;\n      }\n      if (state === 13 /* State.Accept */) {\n        result.push(LinkComputer._createLink(classifier, line, i, linkBeginIndex, len));\n      }\n    }\n    return result;\n  }\n}\n/**\n * Returns an array of all links contains in the provided\n * document. *Note* that this operation is computational\n * expensive and should not run in the UI thread.\n */\nexport function computeLinks(model) {\n  if (!model || typeof model.getLineCount !== 'function' || typeof model.getLineContent !== 'function') {\n    // Unknown caller!\n    return [];\n  }\n  return LinkComputer.computeLinks(model);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}