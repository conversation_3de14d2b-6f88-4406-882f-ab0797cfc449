/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-03-10 10:46:54
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2024-01-31 16:53:46
 * @Description: 被对象关联(只读页面，没有操作)
 */

import AppNodeResourceIcon from "@components/AppNodeResourceIcon";
import { generateRoutePathByNodeType } from "@common/router/RouterRegister";
import {
  useQueryTeam551GetReledObjList
} from "@common/service/commonHooks";
import { isEmpty } from "@common/utils/ArrayUtils";
import { Table, Space } from "antd";
import { Link, useParams } from "react-router-dom";
import "./ReledObjList.scss";

//被对象关联
export default function ReledObjList({ objId, objNodeId, objType, moduleName,}) {
  const { teamId } = useParams()
  const { data: reledObjList } = useQueryTeam551GetReledObjList(teamId, objId, objType, !!objId); // 被对象关联

   // 被对象关联列表
   const columns = [
    {
      title: '名称', dataIndex: 'name', key: 'name', width: "24%",
      render: (value, row, index) => {
        let { pathname, search } = generateRoutePathByNodeType(row.objType, {
          teamId,
          nodeId: row.anchorNodeId,
          nid: row.nodeId,
        })
        return <div style={{display:'flex',alignItems:'center'}}>
          <AppNodeResourceIcon nodeType={row.objType} className="fontsize-14" style={{ color: "#3279fe", opacity: 0.4,marginRight:5 }}/>
          <Link to={pathname+search} target="_blank">
            {value}
          </Link>
        </div>
      }
    },
    { title: '类型', dataIndex: 'objTypeName', key: 'objTypeName', width: "10%", },
    { title: '创建人', dataIndex: 'userName', key: 'userName', width: "10%", },
    { title: '创建时间', dataIndex: 'createDt', key: 'createDt', width: "13%" },
    { title: '关联人', dataIndex: 'refName', key: 'refName', width: "10%", },
    { title: '关联时间', dataIndex: 'refDt', key: 'refDt', width: "13%" },
    { title: '备注', dataIndex: 'description', key: 'description', width: "10%", render: (description) => <div title={description} className='text-overflow' style={{width: 130}}>{description}</div>},
    // 暂无操作
    {
      title: '', dataIndex: 'operation', key: 'operation', align: "center", width: "10%", disabled: true,
    }
  ];
  
  return (
    <>
      {!isEmpty(reledObjList) &&
        <div className="resource-reference">
           <Space className="resource-reference-title">
                <span>被对象关联</span>
            </Space>
          <Table className="custome-table padding-0"
            // columns={columns.filter(column => (editable || !column.disabled))}
            columns={columns}
            dataSource={reledObjList}
            size={"small"}
            style={{ padding: "0px 20px" }} 
            pagination={
              {
                position: ['bottomCenter'],
                size: 'small',
                showQuickJumper: true,
                showSizeChanger: true,
                showTitle: true,
                total: reledObjList?.length,
                hideOnSinglePage: true,
                showTotal: (total) =>{
                  return `总共 ${total} 条`
                },
              } 
            }  
            />
        </div >
      }
    </>
  )
}
