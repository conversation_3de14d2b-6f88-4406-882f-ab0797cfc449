{"ast": null, "code": "/*\r\n * @Author: <PERSON> <EMAIL>\r\n * @Date: 2025-01-21 11:34:18\r\n * @LastEditors: <PERSON> <EMAIL>\r\n * @LastEditTime: 2025-08-05 14:22:17\r\n * @Description: 新建\"事件推送\"\r\n */import{ExclamationCircleOutlined}from\"@ant-design/icons\";import{Button,Input,Drawer,Form,Checkbox,Avatar,Select,Space,Dropdown,Cascader,Modal}from\"antd\";import DraggablePopUp from\"@components/DraggablePopUp\";import React,{useEffect,useRef,useState,useMemo}from\"react\";import{useParams}from\"react-router-dom\";import'../DataPush.scss';import*as httpQuickAccess from\"@common/api/http\";import{globalUtil}from\"@common/utils/globalUtil\";import{eNodeTypeId,eNodeType}from\"@common/utils/TsbConfig\";import{NoAvatarIcon}from'@common/components/IconUtil';import TextModelModal from'./TextModel/TextModelModal';import PlanModal from'./Plan/PlanModal';import SubscribeContent from'./SubscribeContent/SubscribeContent';import{ePriorityTypeObj,ePriorityType,eMsgChannel,eMsgChannelObj,eTriggerTime,eTriggerTimeObj,eExecTimeType,eEventParentType}from\"src/quickAcess/utils/Config\";import{useQueryTeam599GetDataDictionary}from\"src/quickAcess/service/quickHooks\";import{eSelectionListId}from\"@common/utils/enum\";import{getEventParentType}from\"@common/utils/logicUtils\";import Conditions from\"./Conditions\";import TemplateDrawer from\"./TemplateDrawer/TemplateDrawer\";import{globalEventBus}from\"@common/utils/eventBus\";import FormEditor from\"@components/FormEditor/FormEditor\";import DraggableDrawer from\"@components/DraggableDrawer\";// 新建/编辑 事件推送\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";export default function OpDataPush(_ref){var _eNodeType$nodeType;let{allMember,opType,closeDrawer,setModalVisible,modalVisible,editData,isChange,setIsChange,selectedKey,typeList,fromType,ganttName,nodeType}=_ref;const{teamId,nodeId}=useParams();const[form]=Form.useForm();// Form.Item布局样式\nconst formItemLayout={labelCol:{span:2}};// Form.Item的样式\nconst style={style:{height:\"50%\",marginBottom:20}};const[pushwhenEmpty,setPushwhenEmpty]=useState(false);const[subscribeData,setSubscribeData]=useState(null);// 先决条件\nconst[description,setDescription]=useState(\"\");// 备注\nconst[msgTplDetail,setMsgTplDetail]=useState();// 模版\nconst{data:{dataDictionaries=[],templateVariables=[]}={dataDictionaries:[],templateVariables:[],workflowVariables:[]},isLoading:isLoadingTeamXxx,refetch:refetchTeamXxx}=useQueryTeam599GetDataDictionary({teamId,selectionId:eSelectionListId.Selection_1970_datapush});useEffect(()=>{if(modalVisible){if(!!editData){form.setFieldsValue({subscrTitle:editData.subscrTitle||'',// toUids: (editData.toUids||'').split(',').filter(uid => !!uid),\ntoUids:editData.toUids,ccUids:(editData.ccUids||'').split(',').filter(uid=>!!uid),msgChannel:editData.msgChannel,priorityNo:editData.priorityNo,tplTitle:editData.tplTitle,// msgTpl: editData.msgTpl,\nmsgTpl:editData.tplBody,attachmentFlg:!!editData.attachmentFlg,objRefFlg:!!editData.objRefFlg});setDescription(editData.description||'');let obj=!!editData.objId?{objId:editData.objId,objType:editData.objType,objName:editData.objName,objNodeId:editData.objNodeId}:null;setSubscribeData(obj);setPushwhenEmpty(editData.pushWhenEmpty==1);setMsgTplDetail({msgTplId:editData.msgTplId});}else{form.resetFields();}}},[modalVisible]);useEffect(()=>{if(!isLoadingTeamXxx&&!!editData){let{eventType}=editData;if(!eventType){return;}const eventTypeParentNode=dataDictionaries.find(data=>data.children.some(item=>item.code==eventType));const eventTypeNode=((eventTypeParentNode===null||eventTypeParentNode===void 0?void 0:eventTypeParentNode.children)||[]).find(item=>item.code==eventType);const eventTypeList=[eventTypeParentNode.code,eventTypeNode.code];form.setFieldValue(\"eventType\",eventTypeList);}},[isLoadingTeamXxx,editData]);function avatarFormat(src,name){return/*#__PURE__*/_jsx(Avatar,{style:{marginRight:5},src:src,icon:/*#__PURE__*/_jsx(NoAvatarIcon,{}),size:24});}function onFinish(opType){debugger;let values=form.getFieldsValue(true);console.log(\"values\",values);const{msgTpl,toUids,ccUids,priorityNo}=values;// if(opType == 0){\n//   if(!!subscribeData?.objId){\n//     if((msgTpl||'').indexOf('%订阅数据%') == -1){\n//      return Modal.confirm({\n//         title: \"提示\",\n//         icon: <ExclamationCircleOutlined />,\n//         content: <div style={{margin:'10px 0px',fontSize:12}}>\n//         <div>内容框中，未检测到有 %订阅数据% 占位符，若不含此占位符，订阅邮件中将不会有 搜索或报表结果。</div>\n//         <div>点击\"继续保存\"，表示您不需要包含订阅数据，仅以\"内容\"框中文案作为邮件主体内容；</div>\n//         <div>点击\"重新修改\"，停在当前界面，您可在\"内容\"框中某个位置，点击蓝色%订阅数据%插入占位符。</div>\n//       </div>,\n//         okText: \"继续保存\", \n//         cancelText: \"重新修改\",\n//         onOk: () => {\n//           onFinish(1)\n//         },\n//         onCancel: () => {\n//           console.log(\"Cancel\");\n//         },\n//       });\n//     }\n//   }\n// }\nlet params={teamId:teamId,subscrTitle:values.subscrTitle,msgChannel:values.msgChannel,objId:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objId,// 先决条件保存的 搜索queryId \nobjType:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objType,// objType固定传 9\nobjName:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objName,// msgTpl: values.msgTpl,\nmsgTplId:msgTplDetail.tplId,// toUids: (toUids||[]).join(','),\ntoUids:toUids,ccUids:(ccUids||[]).join(','),execTimeType:eExecTimeType.dataPush,eventType:eventType,priorityNo:priorityNo,description:description,pushWhenEmpty:pushwhenEmpty?1:0,projectNodeId:nodeId,nodeType:parseInt(nodeType)};if(!!editData){params.nodeId=selectedKey;}httpQuickAccess.team_523_save_sched_task_src(params).then(res=>{if(res.resultCode==200){closeDrawer(1);}});}function _onClose(){let values=form.getFieldsValue(true);closeDrawer(0,{values:values,objId:subscribeData===null||subscribeData===void 0?void 0:subscribeData.objId,msgTpl:values.msgTpl||'',// toUids: values.toUids||[],\ntoUids:values.toUids,ccUids:values.ccUids||[],pushwhenEmpty:pushwhenEmpty?1:0});}// 模版\nfunction onClickTemplateVariables(formKey,info){let formValue=form.getFieldValue(formKey);if(formKey==\"tplTitle\"){form.setFieldValue(formKey,`${formValue!==null&&formValue!==void 0?formValue:\"\"}${info.key}`);}else if(formKey==\"msgTpl\"){form.setFieldValue(formKey,`${formValue!==null&&formValue!==void 0?formValue:\"\"}${info.key}`);}else{var _info$key;form.setFieldValue(formKey,[...(formValue!==null&&formValue!==void 0?formValue:[]),(_info$key=info.key)!==null&&_info$key!==void 0?_info$key:\"\"]);}}function onChangeRemark(e){setDescription(e.target.value);}function treeFormat(){let list=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];return list.map(tree=>{let _tree={label:tree.value,value:tree.code};if((tree.children||[]).length>0){_tree.children=treeFormat(tree.children);}return _tree;});}const eventTypeList=Form.useWatch(\"eventType\",form);const msgChannel=Form.useWatch(\"msgChannel\",form);const eventType=eventTypeList?eventTypeList[eventTypeList.length-1]:undefined;const eventParentType=useMemo(()=>{return getEventParentType(dataDictionaries,eventType);},[dataDictionaries,eventType]);const dataDictionariesOptions=useMemo(()=>{return treeFormat(dataDictionaries);},[dataDictionaries]);const selectDataSource=()=>{globalEventBus.emit(\"openTemplateDrawerEvent\",\"\",{msgChannel,callback:selectDataSourceCallback});};function selectDataSourceCallback(msgChannel,checkMsgTpl){console.log(\"checkMsgTpl\",checkMsgTpl);if(!!checkMsgTpl){setMsgTplDetail(checkMsgTpl);form.setFieldsValue({msgChannel,// 系统推送通道\n\"tplTitle\":checkMsgTpl.tplTitle,\"msgTpl\":checkMsgTpl.tplBody,\"attachmentFlg\":!!checkMsgTpl.attachmentFlg,// 附件\n\"objRefFlg\":!!checkMsgTpl.objRefFlg// 对象关联\n});}}function onClickSubmit(){if(!msgTplDetail){return globalUtil.warning(\"请选择模版\");}form.submit();}const selectTemplateView=formKey=>{return/*#__PURE__*/_jsx(Dropdown,{menu:{items:templateVariables.map(obj=>({key:obj.value,value:obj.value,label:obj.value})),onClick:info=>onClickTemplateVariables(formKey,info)},placement:\"bottom\",arrow:{pointAtCenter:true},children:/*#__PURE__*/_jsx(Button,{type:\"link\",children:\"\\u9009\\u62E9\\u6A21\\u7248\\u53D8\\u91CF\"})});};return/*#__PURE__*/_jsxs(DraggableDrawer,{className:\"tms-drawer\",title:`${opType==0?\"新建\":'编辑'}${fromType=='gantt'?'进度推送订阅':'事件推送'}`,width:'70%',onClose:()=>_onClose(),open:modalVisible,destroyOnClose:true//关闭时销毁子元素,避免重新打开数据不会刷新\n,preserve:false,closable:true,centered:true,footer:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'end'},children:/*#__PURE__*/_jsxs(Space,{size:20,children:[!!subscribeData&&/*#__PURE__*/_jsx(Checkbox,{className:\"subscribe-way-mark-check\",onChange:e=>setPushwhenEmpty(e.target.checked),checked:pushwhenEmpty,children:\"\\u8BA2\\u9605\\u6570\\u636E\\u4E3A\\u7A7A\\u65F6\\uFF0C\\u4F9D\\u7136\\u63A8\\u9001\\u3002\"}),/*#__PURE__*/_jsx(Input,{value:description,placeholder:\"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\",style:{borderRadius:5,width:300},autoComplete:\"off\",onChange:onChangeRemark}),/*#__PURE__*/_jsx(Button,{style:{borderRadius:5},type:'primary',onClick:()=>{onClickSubmit();},children:\"\\u63D0\\u4EA4\"})]})}),children:[/*#__PURE__*/_jsxs(Form,{className:\"subscribe-way\",form:form,...formItemLayout,labelAlign:\"right\",onFinish:()=>onFinish(0),initialValues:{msgChannel:eMsgChannel.mail,// 默认邮件\ntriggerTime:eTriggerTime.immediately// 默认即时触发\n},children:[/*#__PURE__*/_jsx(\"div\",{className:\"label-header\",children:\"\\u63A8\\u9001\\u89C4\\u5219\"}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u540D\\u79F0\",rules:[{required:true,message:'名称不能为空'}],...style,name:\"subscrTitle\",children:/*#__PURE__*/_jsx(Input,{style:{width:300,borderRadius:5},autoComplete:\"off\"})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u9009\\u62E9\\u4E8B\\u4EF6\",rules:[{required:true,message:'事件不能为空'}],...style,name:\"eventType\",children:/*#__PURE__*/_jsx(Cascader,{style:{width:300,borderRadius:3}// changeOnSelect\n,allowClear:true,popupClassName:\"tms-cascader-popup\",expandTrigger:\"hover\",options:dataDictionariesOptions,placeholder:`请选择事件`,displayRender:(label,selectedOptions)=>label.join(\"-\"),showSearch:{filter:(inputValue,path)=>path.some(option=>option.label.toLowerCase().indexOf(inputValue.toLowerCase())>-1)}})}),(eventParentType==eEventParentType.businessOp||eventParentType==eEventParentType.electronStream)&&/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5148\\u51B3\\u6761\\u4EF6\",...style,children:/*#__PURE__*/_jsx(Conditions,{subscribeData:subscribeData,setSubscribeData:setSubscribeData,eventParentType:eventParentType})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u89E6\\u53D1\\u65F6\\u673A\",...style,name:\"triggerTime\",children:/*#__PURE__*/_jsx(Select,{showSearch:true,allowClear:true,style:{width:300},placeholder:\"\\u8BF7\\u9009\\u62E9\\u89E6\\u53D1\\u65F6\\u673A\",disabled:true,options:[eTriggerTimeObj[eTriggerTime.immediately]]})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u4F18\\u5148\\u7EA7\",...style,name:\"priorityNo\",children:/*#__PURE__*/_jsx(Select,{showSearch:true,allowClear:true,style:{width:300},placeholder:\"\\u8BF7\\u9009\\u62E9\\u4F18\\u5148\\u7EA7\",options:[ePriorityTypeObj[ePriorityType.high],ePriorityTypeObj[ePriorityType.middle],ePriorityTypeObj[ePriorityType.low]]})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u7CFB\\u7EDF\\u63A8\\u9001\\u901A\\u9053\",...style,name:\"msgChannel\",children:/*#__PURE__*/_jsx(Select// disabled={true}\n,{showSearch:true,allowClear:true,style:{width:300},placeholder:\"\\u8BF7\\u9009\\u62E9\",options:[eMsgChannelObj[eMsgChannel.mail],eMsgChannelObj[eMsgChannel.msg],eMsgChannelObj[eMsgChannel.voice],eMsgChannelObj[eMsgChannel.wechat],eMsgChannelObj[eMsgChannel.inSiteMsg]]})}),/*#__PURE__*/_jsx(\"div\",{className:\"label-header\",children:\"\\u63A8\\u9001\\u5185\\u5BB9\"}),/*#__PURE__*/_jsxs(Form.Item,{label:\"\\u6536\\u4EF6\\u4EBA\",required:true,...style,children:[/*#__PURE__*/_jsx(Form.Item,{name:\"toUids\",rules:[{required:true,message:'收件人不能为空'}],noStyle:true,children:/*#__PURE__*/_jsx(Select,{showSearch:true,allowClear:true// mode='multiple' fix tmsbug-11663:系统订阅，收件人，期望从多选改为单选\n,placeholder:\"\\u8BF7\\u9009\\u62E9\\u6536\\u4EF6\\u4EBA\",dropdownMatchSelectWidth:false,style:{borderRadius:5,width:300},filterOption:(input,option)=>{var _option$name;return((_option$name=option===null||option===void 0?void 0:option.name)!==null&&_option$name!==void 0?_option$name:'').toLowerCase().includes(input.toLowerCase());},options:allMember.map(user=>({key:user.key.toString(),value:user.key.toString(),icon:avatarFormat(user.avatar,user.label),label:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[avatarFormat(user.avatar,user.label),user.label]}),name:user.label})).concat(templateVariables.map(obj=>({key:obj.value,value:obj.value,label:obj.value,name:obj.value})))})}),selectTemplateView(\"toUids\")]}),/*#__PURE__*/_jsxs(Form.Item,{label:\"\\u6284\\u9001\\u4EBA\",...style,children:[/*#__PURE__*/_jsx(Form.Item,{name:\"ccUids\",noStyle:true,children:/*#__PURE__*/_jsx(Select,{showSearch:true,mode:\"multiple\",allowClear:true,placeholder:\"\\u8BF7\\u9009\\u62E9\\u6284\\u9001\\u4EBA\",style:{borderRadius:5,width:300},filterOption:(input,option)=>{var _option$name2;return((_option$name2=option===null||option===void 0?void 0:option.name)!==null&&_option$name2!==void 0?_option$name2:'').toLowerCase().includes(input.toLowerCase());},options:allMember.map(user=>({key:user.key.toString(),value:user.key.toString(),label:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[avatarFormat(user.avatar,user.label),user.label]}),name:user.label})).concat(templateVariables.map(obj=>({key:obj.value,value:obj.value,label:obj.value,name:obj.value})))})}),selectTemplateView(\"ccUids\")]}),/*#__PURE__*/_jsxs(Form.Item,{label:\"\\u4E3B\\u9898\",...style,children:[/*#__PURE__*/_jsx(Form.Item,{name:\"tplTitle\",noStyle:true,children:/*#__PURE__*/_jsx(Input,{style:{borderRadius:5,width:300},autoComplete:\"off\",disabled:true})}),/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:selectDataSource,children:\"\\u9009\\u62E9\\u6A21\\u7248\"})]}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u6B63\\u6587\",...style,children:/*#__PURE__*/_jsx(\"div\",{style:{marginTop:5},children:/*#__PURE__*/_jsx(Form.Item,{name:\"msgTpl\",noStyle:true,children:/*#__PURE__*/_jsx(FormEditor,{disabled:true,placeholder:\"\",editorProps:{uploadParams:{teamId:teamId,nodeId:nodeId,moduleName:(_eNodeType$nodeType=eNodeType[nodeType])===null||_eNodeType$nodeType===void 0?void 0:_eNodeType$nodeType.nameEn,objType:nodeType},heightMin:'200px',autofocus:false}})})})}),(eventParentType==eEventParentType.businessOp||eventParentType==eEventParentType.electronStream)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Form.Item,{label:\"\\u9644\\u4EF6\",...style,name:\"attachmentFlg\",valuePropName:\"checked\",children:/*#__PURE__*/_jsx(Checkbox,{disabled:true})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BF9\\u8C61\\u5173\\u8054\",...style,name:\"objRefFlg\",valuePropName:\"checked\",children:/*#__PURE__*/_jsx(Checkbox,{disabled:true})})]})]}),/*#__PURE__*/_jsx(TemplateDrawer,{}),/*#__PURE__*/_jsx(DraggablePopUp,{className:\"tms-modal\",title:\"\\u63D0\\u793A\",centered:true,width:300,open:isChange,onCancel:()=>setIsChange(false),onOk:()=>{setIsChange(false);setModalVisible(false);},children:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',margin:'10px 0px'},children:\"\\u60A8\\u6B63\\u5728\\u7F16\\u8F91\\\"\\u4E8B\\u4EF6\\u63A8\\u9001\\\"\\uFF0C\\u786E\\u5B9A\\u653E\\u5F03\\u7F16\\u8F91\\uFF1F\"})})]});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}