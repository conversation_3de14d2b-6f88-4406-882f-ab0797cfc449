{"ast": null, "code": "import{CheckOutlined,RightOutlined}from'@ant-design/icons';import{team_036_get_node_ctx_options}from\"@common/api/http\";import AppNodeResourceIcon from\"@components/AppNodeResourceIcon\";import{useQuerySetting327_getTeamSpaceAadmin,useQuerySetting407_getCodeValueList}from\"@common/service/commonHooks\";import{isEmpty,treeToArray,getSysIconList,assembleGroup}from\"@common/utils/ArrayUtils\";import{eCtxOptionType,eEnableFlg,eProductId}from\"@common/utils/enum\";import{eCtxTypeId,eMenuStatus,getCtxIconByType,getMenuStatusIcon,eNameTextFontType}from\"@common/utils/TsbConfig\";import{expiredModal,resourceMaxModal,unVipModal}from\"@common/utils/ViewUtils\";import{Checkbox,Space,Skeleton,Modal}from\"antd\";import React,{forwardRef,useImperativeHandle,useRef,useState,useEffect}from\"react\";import{Item as CtxMenuItem,Menu as CtxMenu,Submenu as CtxSubMenu,useContextMenu,Separator}from\"react-contexify\";import{formatSvg}from\"@common/utils/ViewUtils\";import\"react-contexify/ReactContexify.css\";import{useMutation}from'@tanstack/react-query';import\"./ContextBoard.scss\";import*as https from\"@common/api/http\";import{setting_320_get_node_priv_query}from\"@common/api/query/query_setting\";import{globalUtil}from\"@common/utils/globalUtil\";// 图标颜色\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";function ColorSelectedMenu(_ref){let{selectValue,onChange,colorOptionsList,actionType}=_ref;const _onChanged=checkedValue=>{let value=checkedValue.length?checkedValue[checkedValue.length-1]:\"\";// 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\nonChange&&onChange({id:actionType+\"&\"+value});};return/*#__PURE__*/_jsx(\"div\",{style:{display:\"inline-block\"},children:/*#__PURE__*/_jsx(Checkbox.Group,{value:[selectValue],onChange:_onChanged,children:colorOptionsList.map(item=>/*#__PURE__*/_jsx(Checkbox,{name:\"xxx\",value:item.type.toString(),className:item.className},item.type.toString()))})});}// 标题颜色\nfunction TitleSelectedMenu(_ref2){let{selectValue,onChange,colorOptionsList,actionType}=_ref2;const _onChanged=checkedValue=>{let value=checkedValue.length?checkedValue[checkedValue.length-1]:\"\";// 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\nonChange&&onChange({id:actionType+\"&\"+value});};return/*#__PURE__*/_jsx(\"div\",{style:{display:\"inline-block\"},children:/*#__PURE__*/_jsx(Checkbox.Group,{value:[selectValue],onChange:_onChanged,children:colorOptionsList.map(item=>/*#__PURE__*/_jsx(Checkbox,{name:\"xxx\",value:item.type.toString(),className:item.className,children:selectValue==item.type?\"\":item.title},item.type.toString()))})});}// 字体个性化设置，前段需要固定识别，存储的是type值\nfunction FontSelectedMenu(_ref3){let{onChange,optionList,actionType}=_ref3;const _onChanged=checkedValue=>{onChange&&onChange({id:actionType+\"&\"+checkedValue});};return/*#__PURE__*/_jsx(Space,{size:10,children:optionList.map(option=>/*#__PURE__*/_jsx(\"div\",{onClick:()=>_onChanged(option===null||option===void 0?void 0:option.type),className:option.className,children:formatSvg(option.icon)},option===null||option===void 0?void 0:option.type))});}// 图标设置，无需固定，有多少显示多少，存储的是value值\nfunction IconSelectedMenu(_ref4){let{onChange,optionList,actionType}=_ref4;const _onChanged=checkedValue=>{onChange&&onChange({id:actionType+\"&\"+checkedValue});};return/*#__PURE__*/_jsx(Space,{size:10,children:optionList.map(option=>/*#__PURE__*/_jsx(\"div\",{onClick:()=>_onChanged(option===null||option===void 0?void 0:option.value),className:option.className,children:formatSvg(option.icon)},option===null||option===void 0?void 0:option.type))});}/**\r\n * @description 文档图标\r\n *//*function TitleIconSelectedMenu({ selectValue, onChange, colorOptionsList }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: eCtxTypeId.ctx_42_set_icon + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n  <Checkbox.Group value={[selectValue]} onChange={_onChanged}>\r\n    {colorOptionsList.map(option => (\r\n      <Checkbox\r\n        key={option.value?.toString()} \r\n        value={option.value?.toString()}\r\n        className=\"checkbox-bage\"\r\n      >\r\n        <div className=\"checkbox-bage-icon\">\r\n          {formatSvg(option.icon)}\r\n        </div>\r\n      </Checkbox>\r\n    ))}\r\n  </Checkbox.Group>\r\n</div>\r\n}*//*// checkbox 选中状态\r\nconst getCheckboxItem = (flag, label, color, className, actionType) => {\r\n  return <div style={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"baseline\" }}>\r\n    {/!* tmsbug-2622：删除线文案本身添加删除线 *!/}\r\n    <span style={{ color: color }} className={`${actionType == eCtxTypeId.ctx_46_text_font ? \"tree-dir-title-delete\" : \"\"}`}>{label}</span>\r\n    <Checkbox checked={flag} className={className} />\r\n  </div>\r\n}*/// 收藏选中状态\nconst getCheckItem=(flag,label,color)=>{return/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",color},children:[/*#__PURE__*/_jsx(\"span\",{children:label}),flag?/*#__PURE__*/_jsx(CheckOutlined,{style:{color:\"#70b603\"}}):/*#__PURE__*/_jsx(_Fragment,{})]});};// 自定义延迟子菜单组件\nconst DelayedCtxSubMenu=_ref5=>{let{children,label,arrow,...props}=_ref5;const timeoutRef=useRef(null);const[isVisible,setIsVisible]=useState(false);const menuRef=useRef(null);const subMenuRef=useRef(null);const isMouseInMenu=useRef(false);const[subMenuPosition,setSubMenuPosition]=useState({top:0});const[subMenuHeight,setSubMenuHeight]=useState(0);// 计算预估的子菜单高度\nconst calculateEstimatedHeight=()=>{// 获取子菜单项的数量\nconst menuItems=React.Children.toArray(children);const itemCount=menuItems.length;// 单个菜单项的高度（包括padding和border）\nconst ITEM_HEIGHT=28;// 根据实际样式调整\n// 子菜单的padding\nconst MENU_PADDING=8;// 上下padding各4px\nreturn itemCount*ITEM_HEIGHT+MENU_PADDING*2;};// 添加 ResizeObserver 监听子菜单高度变化\nuseEffect(()=>{if(!subMenuRef.current)return;const resizeObserver=new ResizeObserver(entries=>{for(let entry of entries){setSubMenuHeight(entry.contentRect.height);}});resizeObserver.observe(subMenuRef.current);return()=>{resizeObserver.disconnect();};},[isVisible]);const showSubMenu=()=>{isMouseInMenu.current=true;if(timeoutRef.current){clearTimeout(timeoutRef.current);}// 计算子菜单位置，防止被屏幕底部遮挡\nif(menuRef.current){const menuRect=menuRef.current.getBoundingClientRect();const estimatedHeight=calculateEstimatedHeight();const windowHeight=window.innerHeight;// 默认与父菜单项顶部对齐，并往上偏移50px\nlet topPosition=-50;// 检查子菜单底部是否会超出屏幕\nif(menuRect.top+estimatedHeight>windowHeight){// 如果会超出屏幕底部，调整位置\nconst overflow=menuRect.top+estimatedHeight-windowHeight;topPosition=Math.max(-menuRect.top,-overflow-10);// 至少留10px的边距\n}setSubMenuPosition({top:topPosition});}setIsVisible(true);};const hideSubMenu=()=>{if(timeoutRef.current){clearTimeout(timeoutRef.current);}timeoutRef.current=setTimeout(()=>{if(!isMouseInMenu.current){setIsVisible(false);}},1000);};// 检查是否是有效的DOM节点，解决截图时焦点失去报错问题；\nconst isValidNode=node=>{try{return node&&node instanceof Node;}catch(e){return false;}};const handleMouseEnter=()=>{showSubMenu();};const handleMouseLeave=e=>{var _menuRef$current;// 检查鼠标是否移动到子菜单\nif(subMenuRef.current&&isValidNode(e.relatedTarget)&&subMenuRef.current.contains(e.relatedTarget)){return;}// 检查鼠标是否移动到其他父菜单\nconst parentMenu=(_menuRef$current=menuRef.current)===null||_menuRef$current===void 0?void 0:_menuRef$current.parentElement;if(parentMenu&&isValidNode(e.relatedTarget)&&parentMenu.contains(e.relatedTarget)){return;}isMouseInMenu.current=false;hideSubMenu();};const handleSubMenuMouseEnter=()=>{showSubMenu();};const handleSubMenuMouseLeave=e=>{var _menuRef$current2;// 检查鼠标是否移动到主菜单\nif(menuRef.current&&isValidNode(e.relatedTarget)&&menuRef.current.contains(e.relatedTarget)){return;}// 检查鼠标是否移动到其他父菜单\nconst parentMenu=(_menuRef$current2=menuRef.current)===null||_menuRef$current2===void 0?void 0:_menuRef$current2.parentElement;if(parentMenu&&isValidNode(e.relatedTarget)&&parentMenu.contains(e.relatedTarget)){return;}isMouseInMenu.current=false;hideSubMenu();};React.useEffect(()=>{return()=>{if(timeoutRef.current){clearTimeout(timeoutRef.current);}};},[]);return/*#__PURE__*/_jsx(\"div\",{ref:menuRef,onMouseEnter:handleMouseEnter,onMouseLeave:handleMouseLeave,style:{position:'relative'},children:/*#__PURE__*/_jsx(CtxSubMenu,{...props,label:label,arrow:arrow,style:{...props.style,position:'absolute',left:'100%',top:subMenuPosition.top,zIndex:999,display:isVisible?'block':'none'},children:/*#__PURE__*/_jsx(\"div\",{ref:subMenuRef,onMouseEnter:handleSubMenuMouseEnter,onMouseLeave:handleSubMenuMouseLeave,style:{width:'100%',height:'100%',position:'relative',zIndex:1000},children:children})})});};/**\r\n * 右击菜单 \r\n * @param teamId 团队Id\r\n * @param onMoreBtnClick 非新建操作回调\r\n * @param onCreateBtnClick 新建等操作回调\r\n * @param id 菜单id\r\n * @param callbackParams 是 onMoreBtnClick 和 onCreateBtnClick 回调返回参数\r\n */function ContextBoard(_ref6,ref){let{teamId,onMoreBtnClick,onCreateBtnClick,id,handleOnVisibilityChange,...callbackParams}=_ref6;// const [createTypeList, setCreateTypeList] = useState([]); // tree right click context menu\n// const [nodeData,setNodeData] = useState(null); \nconst{data:{userId,teamAdminFlag:managerFlag}={userId:undefined,teamAdminFlag:undefined}}=useQuerySetting327_getTeamSpaceAadmin({teamId,enabled:true});// 判断登录人员是否是团队管理员\nconst{data:selectionList}=useQuerySetting407_getCodeValueList(teamId);//字典数据\nconst{isLoading:isCtxLoading,data:createTypeList=[],mutateAsync}=useMutation({mutationFn:_ref7=>{let{nodeData,nodeId,filterActionTypes,ctxOptionList,nodeType}=_ref7;return loadContextMenuList(nodeData,nodeId,filterActionTypes,ctxOptionList,nodeType);}});const setting334Mutation=useMutation({mutationFn:https.setting_334_apply_authorization});//产品申请开通授权\nconst applyProductAuthorize=productId=>{if(!!productId){setting334Mutation.mutate({teamId,productId},{onSuccess:result=>{if(result.resultCode===200){//globalUtil.success(\"提交申请成功！\");\nModal.info({title:\"提示\",content:\"提交成功，管理员会接收到申请信息，请您耐心等候\",maskClosable:true,//centered: true, // 居中\nokText:\"我知道了\",width:500});}}});}};const cacheRef=useRef({});const{show}=useContextMenu({id:id});useImperativeHandle(ref,()=>({/// const nodeData = {\n///   nodeId,\n///   rightFlgIconType： 图标颜色\n///   nameTextColorType：名称颜色\n///   nameTextStrikeFlg：删除线\n/// }\nshowContextBoard:(e,nodeData,nodeId,filterActionTypes,ctxOptionList,nodeType)=>_onShow(e,nodeData,nodeId,filterActionTypes,ctxOptionList,nodeType)}));// load current node menu context and show\n// ctxOptionList：自定义传入ctxOptionList，无需接口获取\nconst _onShow=async(e,nodeData,nodeId,filterActionTypes,ctxOptionList,nodeType)=>{// 计算菜单位置\nconst menuHeight=300;// 预估菜单高度\nconst menuWidth=200;// 预估菜单宽度\nconst windowHeight=window.innerHeight;const windowWidth=window.innerWidth;const clickY=e.clientY;const clickX=e.clientX;// 如果点击位置靠近底部，向上偏移\nconst adjustedY=clickY+menuHeight>windowHeight?Math.max(0,windowHeight-menuHeight-10):clickY;// 如果点击位置靠近右侧，向左偏移\nconst adjustedX=clickX+menuWidth>windowWidth?Math.max(0,windowWidth-menuWidth-10):clickX;// 使用 react-contexify 的默认定位机制，但添加位置调整\nshow({event:e,props:nodeData,position:{x:adjustedX,y:adjustedY}});mutateAsync({nodeData,nodeId,filterActionTypes,ctxOptionList,nodeType});cacheRef.current.nodeData=nodeData;};// 特殊逻辑:1.由于报告模块同一份报告针对不同的人是不同类型的信件，根据nodeId无法区分出是发件箱,收件箱还是垃圾箱，所以需要前端传参nodeType来区分右击菜单\n// tmsbug-8830: 新建接口定义的菜单项位置调整，已经如果已匹配过，则不需要呈现\nconst loadContextMenuList=async(nodeData,nodeId,filterActionTypes,ctxOptionList,nodeType)=>{let result=!isEmpty(ctxOptionList)?{resultCode:200,ctxOptionList}:await team_036_get_node_ctx_options({teamId,nodeId,nodeType});if(result.resultCode==200){let{ctxOptionList,favoriteFlg}=result;if(!isEmpty(filterActionTypes))ctxOptionList=ctxOptionList.filter(ctxOption=>!(filterActionTypes||[]).some(actionType=>ctxOption.actionType==actionType));// 无需新建\nctxOptionList.forEach(ctxOption=>{switch(+ctxOption.actionType){case eCtxTypeId.ctx_38_create:// 新建操作\nctxOption.children=assembleCreateTypeList(ctxOption.children);break;case eCtxTypeId.ctx_39_personalization:// 个性化设置\nctxOption.children=assemblePersonalizationList(ctxOption.children,nodeData);break;case eCtxTypeId.ctx_60_flag_mail:// 标记邮件\nctxOption.children=flagMailList(ctxOption.children,nodeData);break;case eCtxTypeId.ctx_37_favorite:// 收藏\nconst favoriteFlag=favoriteFlg==eEnableFlg.enable;ctxOption.colorType=favoriteFlag?eEnableFlg.disable:eEnableFlg.enable;ctxOption.name=getCheckItem(favoriteFlag,ctxOption.name);break;case eCtxTypeId.ctx_40_top:// 置顶（报告独有）\nconst topFlag=nodeData.topFlg==eEnableFlg.enable;ctxOption.colorType=topFlag?eEnableFlg.disable:eEnableFlg.enable;ctxOption.name=getCheckItem(topFlag,ctxOption.name);break;case eCtxTypeId.ctx_41_read_op:// 已读/未读（报告独有）\nconst readFlag=nodeData.readFlg==eEnableFlg.enable;ctxOption.colorType=readFlag?eEnableFlg.disable:eEnableFlg.enable;ctxOption.name=getCheckItem(readFlag,ctxOption.name);break;default:ctxOption.children=ctxOption.children.map(_sub=>({..._sub,actionType:ctxOption.actionType,colorType:_sub.actionType}));break;}});return ctxOptionList;}else{return[];}};// 给新建菜单增加分组\nconst assembleCreateTypeList=createTypeList=>{let _createTypeList=assembleGroup(createTypeList,false);_createTypeList=treeToArray(_createTypeList);_createTypeList.forEach(_createType=>{_createType.colorType=_createType.actionType;_createType.actionType=eCtxTypeId.ctx_38_create;});return _createTypeList;};// 个性化设置\nconst assemblePersonalizationList=(personalizationList,nodeData)=>{let _personalizationList=assembleGroup(personalizationList,true);_personalizationList=_personalizationList.map(_personalization=>{if(_personalization.actionType==eCtxTypeId.ctx_18_set_figure_tag){var _nodeData$rightFlgIco;// 图标颜色\nconst iconColorOptions=(_personalization.children||[]).map(_child=>{try{return JSON.parse(_child.name);}catch(error){console.error(\"右击菜单个性化设置JSON格式不合法,请检查\",_child.name);return{};}});_personalization.children=[{actionType:eCtxTypeId.ctx_18_set_figure_tag,name:/*#__PURE__*/_jsx(ColorSelectedMenu,{selectValue:(_nodeData$rightFlgIco=nodeData.rightFlgIconType)===null||_nodeData$rightFlgIco===void 0?void 0:_nodeData$rightFlgIco.toString(),onChange:onClick,colorOptionsList:iconColorOptions,actionType:eCtxTypeId.ctx_18_set_figure_tag})}];return _personalization;}else if(_personalization.actionType==eCtxTypeId.ctx_3_color_txt){var _nodeData$nameTextCol;// 标题颜色\nconst textColorOptions=(_personalization.children||[]).map(_child=>{try{let option=JSON.parse(_child.name);return option;}catch(error){console.error(\"右击菜单个性化设置JSON格式不合法,请检查\",_child.name);return{};}});/*  _personalization.children = textColorOptions.map((option, index) => {\r\n          let flag = option.type == nodeData.nameTextColorType;\r\n          return {\r\n            actionType: eCtxTypeId.ctx_3_color_txt,\r\n            colorType: flag ? 0 : option.type,\r\n            name: getCheckboxItem(flag, option.title, option.value, option.className, eCtxTypeId.ctx_3_color_txt),\r\n          }\r\n        }); */console.log(\"colorOptionsList\",textColorOptions);_personalization.children=[{actionType:eCtxTypeId.ctx_3_color_txt,name:/*#__PURE__*/_jsx(TitleSelectedMenu,{selectValue:(_nodeData$nameTextCol=nodeData.nameTextColorType)===null||_nodeData$nameTextCol===void 0?void 0:_nodeData$nameTextCol.toString(),onChange:onClick,colorOptionsList:textColorOptions,actionType:eCtxTypeId.ctx_3_color_txt})}];return _personalization;}else if(_personalization.actionType==eCtxTypeId.ctx_46_text_font){var _nodeData$nodeIconTyp;// 设置字体个性化\nlet textFontTypeOptions=(_personalization.children||[]).map(_child=>{try{var _sysIconList$find,_nodeData$nameTextFon;let option=JSON.parse(_child.name);let sysIconList=getSysIconList(selectionList);option.icon=(_sysIconList$find=sysIconList.find(sys=>sys.propType==option.value))===null||_sysIconList$find===void 0?void 0:_sysIconList$find.propValue;const nameTextFontTypeList=((_nodeData$nameTextFon=nodeData.nameTextFontType)===null||_nodeData$nameTextFon===void 0?void 0:_nodeData$nameTextFon.split(\",\"))||[];// 字体字段\noption.className=nameTextFontTypeList[eNameTextFontType[option.type].idx]==eEnableFlg.enable?`checked-gray-box`:\"\";return option;}catch(error){console.error(\"右击菜单个性化设置JSON格式不合法,请检查\",_child.name);return{};}});console.log(\"textFontTypeOptions\",textFontTypeOptions);_personalization.children=[{actionType:_personalization.actionType,name:/*#__PURE__*/_jsx(FontSelectedMenu,{selectValue:(_nodeData$nodeIconTyp=nodeData.nodeIconType)===null||_nodeData$nodeIconTyp===void 0?void 0:_nodeData$nodeIconTyp.toString(),onChange:onClick,optionList:textFontTypeOptions,actionType:_personalization.actionType})}];return _personalization;}else if(_personalization.actionType==eCtxTypeId.ctx_42_set_icon){var _nodeData$nodeIconTyp3;// 设置图标\nlet titleIconOptions=(_personalization.children||[]).map(_child=>{try{var _sysIconList$find2,_nodeData$nodeIconTyp2;let option=JSON.parse(_child.name);let sysIconList=getSysIconList(selectionList);option.icon=(_sysIconList$find2=sysIconList.find(sys=>sys.propType==option.value))===null||_sysIconList$find2===void 0?void 0:_sysIconList$find2.propValue;option.className=(((_nodeData$nodeIconTyp2=nodeData.nodeIconType)===null||_nodeData$nodeIconTyp2===void 0?void 0:_nodeData$nodeIconTyp2.split(\",\"))||[]).some(nodeIcon=>option.value==nodeIcon)?`checked-gray-box`:\"\";return option;}catch(error){console.error(\"右击菜单个性化设置JSON格式不合法,请检查\",_child.name);return{};}});_personalization.children=[{actionType:_personalization.actionType,name:/*#__PURE__*/_jsx(IconSelectedMenu,{selectValue:(_nodeData$nodeIconTyp3=nodeData.nodeIconType)===null||_nodeData$nodeIconTyp3===void 0?void 0:_nodeData$nodeIconTyp3.toString(),onChange:onClick,optionList:titleIconOptions,actionType:_personalization.actionType})}];return _personalization;}else{return _personalization;}});_personalizationList=treeToArray(_personalizationList);return _personalizationList;};// 标记邮件\nconst flagMailList=(personalizationList,nodeData)=>{let _personalizationList=assembleGroup(personalizationList,true);_personalizationList=_personalizationList.map(_personalization=>{if(_personalization.actionType==eCtxTypeId.ctx_61_flag_color){var _nodeData$tagColor;// 标记颜色\nconst iconColorOptions=(_personalization.children||[]).map(_child=>{try{return JSON.parse(_child.name);}catch(error){console.error(\"右击菜单个性化设置JSON格式不合法,请检查\",_child.name);return{};}});_personalization.children=[{actionType:_personalization.actionType,name:/*#__PURE__*/_jsx(ColorSelectedMenu,{selectValue:(_nodeData$tagColor=nodeData.tagColor)===null||_nodeData$tagColor===void 0?void 0:_nodeData$tagColor.toString(),onChange:onClick,colorOptionsList:iconColorOptions,actionType:_personalization.actionType})}];return _personalization;}else if(_personalization.actionType==eCtxTypeId.ctx_62_flag_img){var _nodeData$tagColor3;// 标记图标\nlet titleIconOptions=(_personalization.children||[]).map(_child=>{try{var _getSysIconList$find,_nodeData$tagColor2;let option=JSON.parse(_child.name);option.icon=(_getSysIconList$find=getSysIconList(selectionList).find(sys=>sys.propType==option.value))===null||_getSysIconList$find===void 0?void 0:_getSysIconList$find.propValue;option.className=(((_nodeData$tagColor2=nodeData.tagColor)===null||_nodeData$tagColor2===void 0?void 0:_nodeData$tagColor2.split(\",\"))||[]).some(nodeIcon=>option.value==nodeIcon)?`checked-gray-box`:\"\";return option;}catch(error){console.error(\"右击菜单个性化设置JSON格式不合法,请检查\",_child.name);return{};}});_personalization.children=[{actionType:_personalization.actionType,name:/*#__PURE__*/_jsx(IconSelectedMenu,{selectValue:(_nodeData$tagColor3=nodeData.tagColor)===null||_nodeData$tagColor3===void 0?void 0:_nodeData$tagColor3.toString(),onChange:onClick,optionList:titleIconOptions,actionType:_personalization.actionType})}];return _personalization;}else{return _personalization;}});_personalizationList=_personalizationList.flatMap(taskGroup=>taskGroup.children);return _personalizationList;};// menu item click\nconst onClick=_ref8=>{let{id,props,data,triggerEvent,...args}=_ref8;let arr=id.split(\"&\");// 存在快捷方式-999的nodeType\nlet callbackData={actionType:arr[0],colorType:arr[1],objType:data===null||data===void 0?void 0:data.objType,productId:data===null||data===void 0?void 0:data.productId//20250724 Jim Song 后端多加一个参数productId,用来表征如果menuStatus异常时，知晓是哪一个产品id\n};console.log(callbackData);onContextMenuClick(callbackData);};const _onMoreBtnClick=e=>{onMoreBtnClick&&onMoreBtnClick({nodeItem:cacheRef.current.nodeData,ctxType:e.actionType,colorType:e.colorType,...callbackParams});};const _onCreateBtnClick=e=>{onCreateBtnClick&&onCreateBtnClick({nodeItem:cacheRef.current.nodeData,nodeType:e.colorType,...callbackParams});};const onContextMenuClick=_ref9=>{let{actionType,colorType,productId}=_ref9;if(actionType==eCtxTypeId.ctx_38_create){// 新建操作\n// 注意:设置图标颜色无法查询出来\nconst node=findByActionAndColorType(createTypeList,actionType,colorType);if(node.menuStatus==eMenuStatus.status_1_Free_QuotaExceed){// 免费对象数已达上限\nreturn resourceMaxModal(teamId,managerFlag,node.name,node.menuStatus,node.expirationDt,productId);}if(node.menuStatus==eMenuStatus.status_3_Vip_Unauthorized){// Vip未授权\nreturn unVipModal(teamId,managerFlag,node.name,node.menuStatus,node.expirationDt,productId,applyProductAuthorize);}if(node.menuStatus==eMenuStatus.status_4_Vip_Expired){// Vip已过期\nreturn expiredModal(teamId,managerFlag,node.name,node.menuStatus,node.expirationDt,productId);}_onCreateBtnClick({colorType});}else{_onMoreBtnClick({actionType,colorType});}};// 根据actionType和colorType查找节点\nfunction findByActionAndColorType(list,actionType,colorType){for(let i in list){if(list[i].actionType==actionType&&list[i].colorType==colorType){return list[i];}if(list[i].children){let node=findByActionAndColorType(list[i].children,actionType,colorType);if(node){return node;}}}}// 右击菜单\n// CtxMenuItem、CtxSubmenu <'fade' | 'scale' | 'flip' | 'slide'>\nreturn/*#__PURE__*/_jsxs(CtxMenu,{id:id,animation:{enter:false,exit:'slide'},onVisibilityChange:handleOnVisibilityChange,className:\"\",children:[isCtxLoading&&/*#__PURE__*/_jsx(ContextLoadingBoard,{}),!isCtxLoading&&createTypeList.length==0&&/*#__PURE__*/_jsx(CtxMenuItem,{disabled:true,children:\" \\u65E0\\u53EF\\u7528\\u9009\\u9879 \"}),!isCtxLoading&&createTypeList.length>0&&createTypeList.map(el=>{const key=`${el.actionType}&${el.colorType}`;if(!isEmpty(el.children)){return/*#__PURE__*/_jsx(DelayedCtxSubMenu,{label:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"iconfont \"+getCtxIconByType(el.actionType)+\" fontsize-12 fontcolor-normal marginRight-5\"}),/*#__PURE__*/_jsx(\"span\",{children:el.name})]})// 子级菜单箭头\n,arrow:/*#__PURE__*/_jsx(RightOutlined,{}),children:/*#__PURE__*/_jsx(\"div\",{className:el.actionType==eCtxTypeId.ctx_38_create?\"horizontal-submenu-panel\":\"\",children:el.actionType==eCtxTypeId.ctx_38_create?// 新建菜单：按分组显示\n(()=>{const items=[];let currentGroupItems=[];el.children.forEach((sub,index)=>{const key=`${sub.actionType}&${sub.colorType}`;// 如果是分组标题\nif(sub.type==eCtxOptionType.eGroup){// 如果有累积的分组内容，先渲染它们\nif(currentGroupItems.length>0){items.push(/*#__PURE__*/_jsx(\"div\",{className:\"group-content-row\",children:currentGroupItems},`group-${index}-content`));currentGroupItems=[];}// 添加分组标题\nitems.push(/*#__PURE__*/_jsx(\"div\",{className:\"group-title-row\",children:/*#__PURE__*/_jsx(\"div\",{className:\"group-title\",children:sub.name})},index));}else{// 如果是分组内容\ncurrentGroupItems.push(/*#__PURE__*/_jsx(\"div\",{className:\"submenu-item-wrapper normal-menu-item\",children:/*#__PURE__*/_jsxs(CtxMenuItem,{id:key,data:sub,disabled:sub.disabled,onClick:onClick,children:[sub.actionType==eCtxTypeId.ctx_38_create?/*#__PURE__*/_jsx(AppNodeResourceIcon,{nodeType:sub.colorType,className:\"fontsize-12 fontcolor-normal marginRight-5\",style:{lineHeight:\"24px\"}}):sub.actionType==eCtxTypeId.ctx_12_create_shortcut?getCtxIconByType(sub.colorType)&&/*#__PURE__*/_jsx(\"span\",{className:\"iconfont \"+getCtxIconByType(sub.colorType)+\" fontsize-12 fontcolor-normal marginRight-5\"}):/*#__PURE__*/_jsx(\"span\",{}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\"},children:sub.name}),/*#__PURE__*/_jsx(\"span\",{className:`iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `,style:{color:getMenuStatusIcon(sub.menuStatus).iconColor},title:sub.menuStatus==eMenuStatus.status_1_Free_QuotaExceed?'应用免费额度已用完':sub.menuStatus==eMenuStatus.status_3_Vip_Unauthorized?'应用未对您授权':sub.menuStatus==eMenuStatus.status_4_Vip_Expired?'应用已过期':''}),/*#__PURE__*/_jsx(\"span\",{})]})},index));}});// 处理最后的分组内容\nif(currentGroupItems.length>0){items.push(/*#__PURE__*/_jsx(\"div\",{className:\"group-content-row\",children:currentGroupItems},\"group-last-content\"));}return items;})():// 其他菜单：保持原有布局\nel.children.map((sub,index)=>{const key=`${sub.actionType}&${sub.colorType}`;return/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsxs(CtxMenuItem,{id:key,data:sub,disabled:sub.disabled,onClick:sub.actionType==eCtxTypeId.ctx_18_set_figure_tag||sub.actionType==eCtxTypeId.ctx_3_color_txt||sub.actionType==eCtxTypeId.ctx_42_set_icon||sub.actionType==eCtxTypeId.ctx_46_text_font||sub.actionType==eCtxTypeId.ctx_61_flag_color||sub.actionType==eCtxTypeId.ctx_62_flag_img?()=>{}:onClick,style:sub.type!=eCtxOptionType.eGroup?{paddingLeft:\"24px\"}:{},className:sub.actionType==eCtxTypeId.ctx_18_set_figure_tag||sub.actionType==eCtxTypeId.ctx_3_color_txt||sub.actionType==eCtxTypeId.ctx_42_set_icon||sub.actionType==eCtxTypeId.ctx_46_text_font||sub.actionType==eCtxTypeId.ctx_61_flag_color||sub.actionType==eCtxTypeId.ctx_62_flag_img?\"context-item-not-focus\":\"\",children:[sub.actionType==eCtxTypeId.ctx_38_create?/*#__PURE__*/_jsx(AppNodeResourceIcon,{nodeType:sub.colorType,className:\"fontsize-12 fontcolor-normal marginRight-5\",style:{lineHeight:\"24px\"}}):sub.actionType==eCtxTypeId.ctx_12_create_shortcut?getCtxIconByType(sub.colorType)&&/*#__PURE__*/_jsx(\"span\",{className:\"iconfont \"+getCtxIconByType(sub.colorType)+\" fontsize-12 fontcolor-normal marginRight-5\"}):/*#__PURE__*/_jsx(\"span\",{}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\"},children:sub.name}),/*#__PURE__*/_jsx(\"span\",{className:`iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `,style:{color:getMenuStatusIcon(sub.menuStatus).iconColor},title:sub.menuStatus==eMenuStatus.status_1_Free_QuotaExceed?'应用免费额度已用完':sub.menuStatus==eMenuStatus.status_3_Vip_Unauthorized?'应用未对您授权':sub.menuStatus==eMenuStatus.status_4_Vip_Expired?'应用已过期':''}),/*#__PURE__*/_jsx(\"span\",{})]})},index);})})},key);}else{return/*#__PURE__*/_jsxs(CtxMenuItem,{id:key,onClick:onClick,children:[el.actionType==28?/*#__PURE__*/_jsx(\"span\",{className:\"iconfont \"+getCtxIconByType(el.actionType)+' fontsize-12 fontcolor-normal',style:{marginLeft:-5,marginRight:5}}):/*#__PURE__*/_jsx(\"span\",{className:\"iconfont \"+getCtxIconByType(el.actionType)+\" fontsize-12 fontcolor-normal marginRight-5\"}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\"},children:el.name})]},key);}})]});}function ContextLoadingBoard(){return/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsx(CtxMenuItem,{disabled:true,children:/*#__PURE__*/_jsx(Skeleton.Input,{active:true,style:{height:\"24px\",overflow:\"hidden\"}})}),/*#__PURE__*/_jsx(CtxMenuItem,{disabled:true,children:/*#__PURE__*/_jsx(Skeleton.Input,{active:true,style:{height:\"24px\",overflow:\"hidden\"}})}),/*#__PURE__*/_jsx(CtxMenuItem,{disabled:true,children:/*#__PURE__*/_jsx(Skeleton.Input,{active:true,style:{height:\"24px\",overflow:\"hidden\"}})}),/*#__PURE__*/_jsx(CtxMenuItem,{disabled:true,children:/*#__PURE__*/_jsx(Skeleton.Input,{active:true,style:{height:\"24px\",overflow:\"hidden\"}})}),/*#__PURE__*/_jsx(CtxMenuItem,{disabled:true,children:/*#__PURE__*/_jsx(Skeleton.Input,{active:true,style:{height:\"24px\",overflow:\"hidden\"}})})]});}export default/*#__PURE__*/forwardRef(ContextBoard);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}