{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class BasicInplaceReplace {\n  constructor() {\n    this._defaultValueSet = [['true', 'false'], ['True', 'False'], ['Private', 'Public', 'Friend', 'ReadOnly', 'Partial', 'Protected', 'WriteOnly'], ['public', 'protected', 'private']];\n  }\n  navigateValueSet(range1, text1, range2, text2, up) {\n    if (range1 && text1) {\n      const result = this.doNavigateValueSet(text1, up);\n      if (result) {\n        return {\n          range: range1,\n          value: result\n        };\n      }\n    }\n    if (range2 && text2) {\n      const result = this.doNavigateValueSet(text2, up);\n      if (result) {\n        return {\n          range: range2,\n          value: result\n        };\n      }\n    }\n    return null;\n  }\n  doNavigateValueSet(text, up) {\n    const numberResult = this.numberReplace(text, up);\n    if (numberResult !== null) {\n      return numberResult;\n    }\n    return this.textReplace(text, up);\n  }\n  numberReplace(value, up) {\n    const precision = Math.pow(10, value.length - (value.lastIndexOf('.') + 1));\n    let n1 = Number(value);\n    const n2 = parseFloat(value);\n    if (!isNaN(n1) && !isNaN(n2) && n1 === n2) {\n      if (n1 === 0 && !up) {\n        return null; // don't do negative\n        //\t\t\t} else if(n1 === 9 && up) {\n        //\t\t\t\treturn null; // don't insert 10 into a number\n      } else {\n        n1 = Math.floor(n1 * precision);\n        n1 += up ? precision : -precision;\n        return String(n1 / precision);\n      }\n    }\n    return null;\n  }\n  textReplace(value, up) {\n    return this.valueSetsReplace(this._defaultValueSet, value, up);\n  }\n  valueSetsReplace(valueSets, value, up) {\n    let result = null;\n    for (let i = 0, len = valueSets.length; result === null && i < len; i++) {\n      result = this.valueSetReplace(valueSets[i], value, up);\n    }\n    return result;\n  }\n  valueSetReplace(valueSet, value, up) {\n    let idx = valueSet.indexOf(value);\n    if (idx >= 0) {\n      idx += up ? +1 : -1;\n      if (idx < 0) {\n        idx = valueSet.length - 1;\n      } else {\n        idx %= valueSet.length;\n      }\n      return valueSet[idx];\n    }\n    return null;\n  }\n}\nBasicInplaceReplace.INSTANCE = new BasicInplaceReplace();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}