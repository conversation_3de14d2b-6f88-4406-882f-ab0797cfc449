{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\settings\\\\views\\\\product\\\\Settings_Product.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport * as httpSettings from \"@common/api/http\";\nimport { Switch, Table, Select, Skeleton, Button, Popover } from \"antd\";\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport moment from \"moment\";\nimport CreateTeamModal, { CREATETYPE_UPGRADE } from \"@components/CreateTeam\";\nimport ProdAuthMbrsDrawer from \"../user/ProdAuthMbrsDrawer\";\nimport MbrAuthProdsDrawer from \"../user/MbrAuthProdsDrawer\";\nimport { CloudDownloadBuy } from \"@/settings/utils/CloudDownloadDraggable\";\nimport ProductVip from \"../../../personal/assets/images/productVip.png\";\nimport ProductVip_x from \"../../../personal/assets/images/productVip_x.png\";\nimport { eOrderStatus, eProductGroupId, eProductId, eProductStatus } from \"@common/utils/enum\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Column,\n  ColumnGroup\n} = Table;\nexport default function Settings_Product({\n  teamId: propsTeamId,\n  productId: propsProductId,\n  visible: propsVisible\n}) {\n  _s();\n  //const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const paramsTeamId = useParams().teamId;\n  const teamId = propsTeamId || paramsTeamId; // 优先使用props，否则使用路由参数\n  const paramsProductId = useParams().productId;\n  const _prodId = propsProductId || paramsProductId; // 优先用props，其次用路由\n  const [productsLoading, setProductsLoading] = useState(false);\n  const [groupList, setGroupList] = useState([]);\n  const [productsForUi, setProductsForUi] = useState([]); //UI呈现的产品列表(会有类型过滤导致显示的产品列表有筛选)\n  const [productsFinal, setProductsFinal] = useState([]); //实际完整的产品列表\n  const [createTeamModalVisible, setCreateTeamModalVisible] = useState(false);\n  const [typeValue, setTypeValue] = useState(0);\n  const [listHeight, setListHeight] = useState(0);\n  const [prodAuthMbrsDrawerVisible, setProdAuthMbrsDrawerVisible] = useState(false); //显示授权穿梭框\n  const [currentAuthProd, setCurrentAuthProd] = useState(null); //\"当前\"点击的授权应用\n  const [historyVisible, setHistoryVisible] = useState(false);\n  const [historyLoading, setHistoryLoading] = useState(false);\n  const [historyList, setHistoryList] = useState([]);\n  const [historyItem, setHistoryItem] = useState(null);\n  const [cloudBuyVisible, setCloudBuyVisible] = useState(false);\n  const [tipsShow, setTipsShow] = useState(false);\n  const [expireVisible, setExpireVisible] = useState(false);\n  const [expireItem, setExpireItem] = useState(null);\n  const [productId, setProductId] = useState(_prodId);\n  const [allUsers, setAllUsers] = useState([]); // 所有用户列表\n  const [authDrawerVisible, setAuthDrawerVisible] = useState(false); // 成员授权应用抽屉\n  const [currentMember, setCurrentMember] = useState(null); // 当前选中的成员\n\n  useEffect(() => {\n    loadTeamProductList();\n    loadUserList();\n  }, []);\n  useEffect(() => {\n    changeListHeight();\n    window.addEventListener(\"resize\", changeListHeight);\n    return () => {\n      window.removeEventListener(\"resize\", changeListHeight);\n    };\n  }, []);\n\n  // 最大高度\n  const changeListHeight = () => {\n    // topbar、标题、页面header、页面footer、table表头页脚\n    let listHeight = document.documentElement.clientHeight - 320;\n    setListHeight(listHeight);\n  };\n\n  //20250621 跳转到产品页时，打开授权对话框\n  useEffect(() => {\n    if (productId && productsFinal.length > 0 && (propsVisible && !paramsProductId || !propsVisible && paramsProductId)) {\n      let item = productsFinal.filter(i => i.productId == productId);\n      if (paramsProductId) {\n        //代表是在 Settings Page形态，而不是 Drawer\n        const newUrl = `/#/${teamId}/settings/product`; //移除 productId\n        window.history.replaceState({}, '', newUrl);\n        setProductId(null);\n      }\n      if (item.length > 0)\n        //打开授权产品对话框\n        authProductToMembers(item[0]);\n    }\n  }, [_prodId, productsFinal, propsVisible]);\n\n  //加载授权产品列表\n  async function loadTeamProductList() {\n    setProductsLoading(true);\n    await httpSettings.team_711_get_team_product_list({\n      teamId: teamId\n    }).then(res => {\n      if (res.resultCode == 200) {\n        productListFormat(res.productList || []);\n      }\n    });\n    setProductsLoading(false);\n  }\n\n  // 加载用户列表\n  async function loadUserList() {\n    try {\n      const res = await httpSettings.setting_202_get_team_allusergrp({\n        teamId: teamId\n      });\n      if (res.resultCode === 200) {\n        const users = res.userList || [];\n        setAllUsers(users);\n      }\n    } catch (error) {\n      console.error('加载用户列表失败:', error);\n    }\n  }\n  function productListFormat(productList = []) {\n    let _prodList = [];\n    productList.forEach(product => {\n      let item = _prodList.find(_product => product.groupId == _product.groupId);\n      if (!item) {\n        _prodList.push({\n          groupName: product.groupName,\n          groupId: product.groupId,\n          products: [product]\n        });\n      } else {\n        item.products.push(product);\n      }\n    });\n    setGroupList([..._prodList]);\n    let _allProds = [];\n    _prodList.forEach(group => {\n      let groupList = group.products.map((_prod, index) => {\n        var _productsFinal$find, _productsFinal$find2;\n        return {\n          ..._prod,\n          key: _prod.productId,\n          isRowSpan: index == 0 ? true : false,\n          groupListLength: index == 0 ? group.products.length : 0,\n          authCntDesc: !!_prod.authCntDesc ? _prod.authCntDesc : ((_productsFinal$find = productsFinal.find(product => product.productId == _prod.productId)) === null || _productsFinal$find === void 0 ? void 0 : _productsFinal$find.authCntDesc) || \"\",\n          objCntDesc: !!_prod.objCntDesc ? _prod.objCntDesc : ((_productsFinal$find2 = productsFinal.find(product => product.productId == _prod.productId)) === null || _productsFinal$find2 === void 0 ? void 0 : _productsFinal$find2.objCntDesc) || \"\"\n        };\n      });\n      _allProds = _allProds.concat(groupList);\n    });\n    setProductsForUi([..._allProds]);\n    setProductsFinal([..._allProds]);\n  }\n\n  //启用/禁用某个应用\n  function setToggle(productId, enableFlg) {\n    httpSettings.team_712_toggle_product({\n      teamId: teamId,\n      productId: productId,\n      enableFlg: enableFlg\n    }).then(res => {\n      if (res.resultCode == 200) {\n        productListFormat(res.productList || []);\n      }\n    });\n  }\n  function authProductToMembers(_product) {\n    //打开授权对话框\n    setCurrentAuthProd(_product);\n    setProdAuthMbrsDrawerVisible(true);\n  }\n  function reGetData() {\n    loadTeamProductList();\n    setCreateTeamModalVisible(false);\n    setTipsShow(true);\n  }\n\n  //显示到期时间\n  function timeFormat(item) {\n    let time = item === null || item === void 0 ? void 0 : item.expirationDt;\n    return (item === null || item === void 0 ? void 0 : item.freeFlg) == 1 ? \"∞\" : time ? time == \"2099-12-31 23:59:59\" ? \"∞\" : moment(time).format(\"YYYY-MM-DD\") : \"-\";\n  }\n  function lineColorFormat(item) {\n    if (item.enableFlg != 1 || item.statusType == eProductStatus.Status_3_Unreleased //即将推出\n    ) {\n      return \"#AAAAAA\";\n    }\n    return \"#000\";\n  }\n  function lineColorXFormat(item, dataIndex) {\n    if (item.productId != eProductId.Pid_11_Explorer && item.productId != eProductId.Pid_12_Space && item.freeFlg == 0 && !!item.expirationDt && moment().isAfter(moment(item.expirationDt))) {\n      //已过期\n      if (item.enableFlg == 1 && dataIndex == \"expirationDt\") {\n        return \"red\"; //未禁用\n      }\n      return \"#AAAAAA\";\n    }\n    return lineColorFormat(item);\n  }\n  function operationFormat(item) {\n    if (item.productId == eProductId.Pid_11_Explorer || item.productId == eProductId.Pid_12_Space || item.statusType == eProductStatus.Status_3_Unreleased) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"-\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 14\n      }, this);\n    }\n    // if(item.statusType == eProductStatus.Status_3_Unreleased ){ //即将推出\n    //   return (\n    //     <>\n    //       <div style={{width:147}}/>\n    //       <div style={{width:50,display:'flex',justifyContent:'center'}}>\n    //         <Switch size=\"small\"\n    //           checkedChildren=\"启用\"\n    //           unCheckedChildren=\"禁用\"\n    //           checked={item.enableFlg == 1}\n    //           onChange={() => item.enableFlg == 1 ? setToggle(item.productId, 0) : setToggle(item.productId, 1)}\n    //         />\n    //       </div>\n    //     </>\n    //   );\n    // }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: 50,\n          textAlign: \"center\"\n        },\n        children: item.freeFlg == 0 && !!item.expirationDt && moment().isAfter(moment(item.expirationDt)) ?\n        /*#__PURE__*/\n        //已过期\n        _jsxDEV(\"a\", {\n          onClick: () => item.productId != eProductId.Pid_13_Cdisk ? setCreateTeamModalVisible(true) : setCloudBuyVisible(true),\n          style: item.enableFlg == 1 ? {\n            color: \"#fff\",\n            backgroundColor: \"red\",\n            borderRadius: 5,\n            padding: \"2px 8px\"\n          } : {\n            color: \"#fff\",\n            backgroundColor: \"#aaa\",\n            borderRadius: 5,\n            padding: \"2px 8px\"\n          },\n          children: \"\\u7EED\\u8D39\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n          onClick: () => item.productId != eProductId.Pid_13_Cdisk ? setCreateTeamModalVisible(true) : setCloudBuyVisible(true),\n          children: \"\\u8D2D\\u4E70\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          margin: \"0px 5px\"\n        },\n        children: \"|\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        style: {\n          width: 70,\n          display: \"flex\",\n          justifyContent: \"center\"\n        },\n        onClick: () => {\n          setHistoryVisible(true);\n          getHistoryList(item);\n          setHistoryItem(item);\n        },\n        children: \"\\u8D2D\\u4E70\\u5386\\u53F2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          margin: \"0px 5px\"\n        },\n        children: \"|\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: 60,\n          display: \"flex\",\n          justifyContent: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Switch, {\n          size: \"small\",\n          disabled:\n          //item.enableFlg != 1 ||   //20250523 Jim 注释掉，不然无法再开启 tmsbug-12291\n          item.statusType == eProductStatus.Status_3_Unreleased,\n          checkedChildren: \"\\u542F\\u7528\",\n          unCheckedChildren: \"\\u7981\\u7528\",\n          checked: item.enableFlg == 1,\n          onChange: () => item.enableFlg == 1 ? setToggle(item.productId, 0) : setToggle(item.productId, 1)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n\n  //单个“应用”的购买历史\n  async function getHistoryList(item) {\n    setHistoryLoading(true);\n    await httpSettings.team_722_get_order_list_by_product_id({\n      teamId,\n      productId: item.productId\n    }).then(res => {\n      if (res.resultCode == 200) {\n        let list = (res.orderList || []).map(history => {\n          history.key = history.id;\n          history.status = history.statusType == eOrderStatus.Status_1_Paid ? \"已支付\" : \"未支付\";\n          return history;\n        });\n        setHistoryList([...list]);\n      }\n    });\n    setHistoryLoading(false);\n  }\n  function expireVisibleChange(visible, item) {\n    setExpireVisible(visible);\n    setExpireItem(item);\n  }\n  const columns = [{\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\"\n      },\n      children: \"\\u7C7B\\u522B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 9\n    }, this),\n    dataIndex: \"groupName\",\n    key: \"groupName\",\n    render: (groupName, item) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\"\n      },\n      children: groupName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 9\n    }, this),\n    onCell: item => {\n      if (item.isRowSpan) {\n        return {\n          rowSpan: item.groupListLength\n        };\n      } else {\n        return {\n          rowSpan: 0\n        };\n      }\n    }\n  }, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\"\n      },\n      children: \"\\u5E94\\u7528\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 9\n    }, this),\n    dataIndex: \"productName\",\n    key: \"productName\",\n    render: (productName, item) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        color: lineColorFormat(item)\n      },\n      children: [productName, item.statusType == eProductStatus.Status_2_QA && /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: \"#70B603\",\n          fontSize: 12,\n          marginLeft: 10\n        },\n        children: \"\\u5185\\u6D4B\\u4E2D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 13\n      }, this), item.statusType == eProductStatus.Status_3_Unreleased && /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: \"#F59A23\",\n          fontSize: 12,\n          marginLeft: 10\n        },\n        children: \"\\u5373\\u5C06\\u63A8\\u51FA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 9\n    }, this),\n    onCell: item => {\n      return {\n        rowSpan: 1\n      };\n    }\n  }, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\"\n      },\n      children: \"\\u6709\\u6548\\u671F\\u81F3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 9\n    }, this),\n    dataIndex: \"expirationDt\",\n    key: \"expirationDt\",\n    render: (expirationDt, item) => {\n      if (item.productId != eProductId.Pid_11_Explorer && item.productId != eProductId.Pid_12_Space && item.freeFlg == 0 && !!item.expirationDt && moment().isAfter(moment(item.expirationDt))) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          title: `应用'${item.productName}'Vip已过期，请续费`,\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            onClick: () => {\n              item.productId == eProductId.Pid_13_Cdisk ? setCloudBuyVisible(true) : setCreateTeamModalVisible(true);\n            },\n            style: {\n              color: lineColorXFormat(item, \"expirationDt\")\n            },\n            children: timeFormat(item)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: lineColorXFormat(item)\n          },\n          children: timeFormat(item)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this);\n    },\n    onCell: item => {\n      return {\n        rowSpan: 1\n      };\n    }\n  }, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\"\n      },\n      children: \"\\u5E94\\u7528\\u7248\\u672C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 9\n    }, this),\n    dataIndex: \"freeFlg\",\n    key: \"freeFlg\",\n    render: (freeFlg, item) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        color: lineColorFormat(item)\n      },\n      children: item.groupId == eProductGroupId.Pgid_1_OS && item.productId != eProductId.Pid_13_Cdisk ? \"-\" : freeFlg == 0 ? !!item.expirationDt && moment().isAfter(moment(item.expirationDt)) ? /*#__PURE__*/_jsxDEV(\"a\", {\n        style: {\n          color: \"inherit\"\n        },\n        onClick: () => item.productId != eProductId.Pid_13_Cdisk ? setCreateTeamModalVisible(true) : setCloudBuyVisible(true),\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          style: {\n            height: 22\n          },\n          src: ProductVip_x\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 15\n      }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n        style: {\n          height: 22\n        },\n        src: ProductVip\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 15\n      }, this) : \"基础版\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 9\n    }, this),\n    onCell: item => {\n      return {\n        rowSpan: 1\n      };\n    }\n  }, {\n    title: /*#__PURE__*/_jsxDEV(Popover, {\n      open: tipsShow,\n      placement: \"bottom\",\n      trigger: \"click\",\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: \"bold\",\n            fontSize: 16\n          },\n          children: \"\\u63D0\\u793A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          style: {\n            marginLeft: 20,\n            color: \"#666\"\n          },\n          onClick: () => setTipsShow(false),\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"iconfont guanbi\",\n            style: {\n              fontSize: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u53EA\\u6709\\u6388\\u6743\\u4E86\\u7684\\u6210\\u5458\\u624D\\u80FD\\u6B63\\u5E38\\u4F7F\\u7528Vip\\u5E94\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u60A8\\u8D2D\\u4E70\\u4E86Vip\\u5E94\\u7528\\uFF0C\\u53EF\\u70B9\\u51FB\\u201C\\u5DF2\\u6388\\u6743/\\u603B\\u6388\\u6743\\u6570\\u201D\\u5217\\u7684\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u84DD\\u8272\\u94FE\\u63A5\\uFF0C\\u8FDB\\u884C\\u5E94\\u7528\\u6388\\u6743\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 30\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            style: {\n              borderRadius: 5\n            },\n            onClick: () => setTipsShow(false),\n            children: \"\\u6211\\u77E5\\u9053\\u4E86\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 13\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"center\"\n        },\n        children: \"\\u5DF2\\u6388\\u6743/\\u603B\\u6388\\u6743\\u6570\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 9\n    }, this),\n    dataIndex: \"authCntDesc\",\n    key: \"authCntDesc\",\n    render: (authCntDesc, _product) => {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: \"flex\",\n            justifyContent: \"center\"\n          },\n          children: _product.groupId != eProductGroupId.Pgid_1_OS ? /*#__PURE__*/_jsxDEV(\"a\", {\n            onClick: () => {\n              !!_product.expirationDt && moment().isAfter(moment(_product.expirationDt)) ? expireVisibleChange(true, _product) : authProductToMembers(_product);\n            },\n            children: authCntDesc || \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 0.5,\n            display: \"flex\",\n            justifyContent: \"end\"\n          },\n          children: _product.groupId != eProductGroupId.Pgid_1_OS && _product.freeFlg == 0 && !!_product.expirationDt && moment().isAfter(moment(_product.expirationDt)) && _product.authUserCnt > _product.authCnt ? /*#__PURE__*/_jsxDEV(\"a\", {\n            style: _product.enableFlg == 1 ? {\n              color: \"red\"\n            } : {\n              color: \"#aaa\"\n            },\n            onClick: () => expireVisibleChange(true, _product),\n            className: \"iconfont guoqitishi\",\n            title: `应用'${_product.productName}'Vip已过期，请续费\\n授权人数超出授权额度，请检查。`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 17\n          }, this) : _product.productId != eProductId.Pid_11_Explorer && _product.productId != eProductId.Pid_12_Space && _product.freeFlg == 0 && !!_product.expirationDt && moment().isAfter(moment(_product.expirationDt)) ? /*#__PURE__*/_jsxDEV(\"a\", {\n            style: _product.enableFlg == 1 ? {\n              color: \"red\"\n            } : {\n              color: \"#aaa\"\n            },\n            onClick: () => expireVisibleChange(true, _product),\n            className: \"iconfont guoqitishi\",\n            title: `应用'${_product.productName}'Vip已过期，请续费`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 17\n          }, this) : _product.authUserCnt > _product.authCnt && _product.freeFlg == 0 && _product.groupId != eProductGroupId.Pgid_1_OS ? /*#__PURE__*/_jsxDEV(\"a\", {\n            style: {\n              color: \"#F59A23\"\n            },\n            onClick: () => authProductToMembers(_product),\n            title: `授权人数超出授权额度，请检查。`,\n            className: \"iconfont guoqitishi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 17\n          }, this) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 11\n      }, this);\n    },\n    onCell: item => {\n      return {\n        rowSpan: 1\n      };\n    }\n  }, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\"\n      },\n      children: \"\\u5DF2\\u4F7F\\u7528/\\u603B\\u91CF\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 9\n    }, this),\n    dataIndex: \"objCntDesc\",\n    key: \"objCntDesc\",\n    render: (objCntDesc, item) => {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          title: item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk ? objCntDesc == \"-\" ? \"\" : objCntDesc : item.totalCntDesc,\n          className: \"tms-text-overflow\",\n          style: {\n            color: lineColorXFormat(item)\n          },\n          children: usedFormat(item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk ? objCntDesc == \"-\" ? \"\" : objCntDesc : item.totalCntDesc)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 13\n        }, this), item.objCntWarningFlg == 1 && (item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk) ? /*#__PURE__*/_jsxDEV(\"a\", {\n          style: {\n            color: \"#F59A23\"\n          },\n          onClick: () => item.productId != eProductId.Pid_13_Cdisk ? setCreateTeamModalVisible(true) : setCloudBuyVisible(true),\n          title: `应用'${item.productName}'免费额度已用完，请购买Vip版本`,\n          className: \"iconfont guoqitishi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 15\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 11\n      }, this);\n    },\n    onCell: item => {\n      return {\n        rowSpan: 1\n      };\n    }\n  }, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\"\n      },\n      children: \"\\u64CD\\u4F5C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 9\n    }, this),\n    dataIndex: \"operation\",\n    key: \"operation\",\n    width: 230,\n    render: (operation, item) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\"\n      },\n      children: operationFormat(item)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 9\n    }, this),\n    onCell: item => {\n      return {\n        rowSpan: 1\n      };\n    }\n  }];\n  function usedFormat(str = \"\") {\n    let singleList = str.split(\"\");\n    let count = singleList.filter(single => single == \"∞\").length;\n    if (count == 1) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [str.substring(0, str.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this), str.substring(str.indexOf(\"∞\") + 1, str.length)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 9\n      }, this);\n    }\n    if (count == 2) {\n      let str2d = str.substring(str.indexOf(\"∞\") + 1, str.length);\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [str.substring(0, str.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this), str2d.substring(0, str2d.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this), str2d.substring(str2d.indexOf(\"∞\") + 1, str2d.length)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 9\n      }, this);\n    }\n    if (count == 3) {\n      let str2d = str.substring(str.indexOf(\"∞\") + 1, str.length);\n      let str3d = str2d.substring(str2d.indexOf(\"∞\") + 1, str2d.length);\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [str.substring(0, str.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this), str2d.substring(0, str2d.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 11\n        }, this), str3d.substring(0, str3d.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this), str3d.substring(str3d.indexOf(\"∞\") + 1, str3d.length)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this);\n    }\n    if (count == 4) {\n      let str2d = str.substring(str.indexOf(\"∞\") + 1, str.length);\n      let str3d = str2d.substring(str2d.indexOf(\"∞\") + 1, str2d.length);\n      let str4d = str3d.substring(str3d.indexOf(\"∞\") + 1, str3d.length);\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [str.substring(0, str.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this), str2d.substring(0, str2d.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 11\n        }, this), str3d.substring(0, str3d.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this), str4d.substring(0, str4d.indexOf(\"∞\")), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16\n          },\n          children: \"\\u221E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this), str4d.substring(str4d.indexOf(\"∞\") + 1, str4d.length)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 9\n      }, this);\n    }\n    return str;\n  }\n\n  //应用类型下拉选择，切换类型\n  function onProductTypeFilterChanged(value) {\n    setTypeValue(value);\n    let filterList = value == 0 ? productsFinal : productsFinal.filter(product => product.groupId == value);\n    setProductsForUi([...filterList]);\n  }\n  function addMonthCntFormat(addMonthCnt) {\n    return addMonthCnt == 6 ? \"半年\" : addMonthCnt == 12 ? \"1年\" : addMonthCnt == 24 ? \"2年\" : addMonthCnt == 36 ? \"3年\" : addMonthCnt == 0 ? \"-\" : addMonthCnt.toString() + \"个月\";\n  }\n  function orderNoFormat(item) {\n    if (!!item.orderDt) {\n      return moment(item.orderDt).format(\"YYYYMMDDHHmmss\");\n    }\n    if (!!item.paidDt) {\n      return moment(item.paidDt).format(\"YYYYMMDDHHmmss\");\n    }\n    return \"-\";\n  }\n  function orderNoClick(item) {\n    window.open(window.location.origin + `/#/personal/myorder/${item.orderId}`);\n  }\n  function sort(a, b) {\n    let _a = orderNoFormat(a);\n    let _b = orderNoFormat(b);\n    if (_a != \"-\" && _b != \"-\") {\n      return parseInt(_a) - parseInt(_b);\n    }\n    if (_a != \"-\" && _b == \"-\") {\n      return parseInt(_a) - 0;\n    }\n    if (_a == \"-\" && _b != \"-\") {\n      return 0 - parseInt(_b);\n    }\n    return 0;\n  }\n  function onClose(saveFlg) {\n    setProdAuthMbrsDrawerVisible(false); //关闭授权弹出框\n  }\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n      loading: productsLoading,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"10px 0px 5px 20px\",\n          fontSize: 14,\n          height: \"100%\"\n        },\n        className: \"product-set\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [\"\\u5E94\\u7528\\u7C7B\\u522B\\uFF1A\", /*#__PURE__*/_jsxDEV(Select, {\n              dropdownMatchSelectWidth: false,\n              value: typeValue,\n              style: {\n                marginLeft: 10\n              },\n              onChange: onProductTypeFilterChanged,\n              children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                value: 0,\n                children: \"\\u5168\\u90E8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 17\n              }, this), groupList.map(group => {\n                return /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: group.groupId,\n                  children: group.groupName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this), !!productsFinal.find(product => product.freeFlg == 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              paddingRight: 20\n            },\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              onClick: () => {\n                navigate(`/personal/myorder`);\n              },\n              children: \"\\u67E5\\u770B\\u8BA2\\u5355\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            marginTop: 10\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            className: \"small-table\",\n            bordered: true,\n            style: {\n              paddingRight: 20\n            },\n            pagination: false,\n            scroll: {\n              // y: \"calc(100vh - 160px)\", // 不支持calc https://github.com/ant-design/ant-design/issues/31909\n              y: listHeight\n            },\n            columns: columns,\n            dataSource: productsForUi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 80\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 10,\n              height: 20,\n              fontSize: 12,\n              color: \"#999\",\n              visibility: \"visible\"\n            },\n            children: \"\\u5E94\\u7528\\u4F9D\\u8D56\\u5173\\u7CFB\\u5907\\u6CE8\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: 10,\n              height: 20,\n              fontSize: 12,\n              color: \"#999\",\n              visibility: \"visible\"\n            },\n            children: \"1. \\u62A5\\u8868\\u3001\\u7CFB\\u7EDF\\u8BA2\\u9605(\\u4F9D\\u8D56\\u4E8E\\u9AD8\\u7EA7\\u641C\\u7D22)\\uFF0C\\u4EEA\\u8868\\u677F(\\u4F9D\\u8D56\\u4E8E\\u9AD8\\u7EA7\\u641C\\u7D22+\\u62A5\\u8868)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: 10,\n              height: 20,\n              fontSize: 12,\n              color: \"#999\",\n              visibility: \"visible\"\n            },\n            children: \"2. \\u8BD5\\u5377(\\u4F9D\\u8D56\\u4E8E\\u9898\\u5E93)\\uFF0C\\u8003\\u8BD5/\\u4F5C\\u4E1A(\\u4F9D\\u8D56\\u4E8E\\u8BD5\\u5377+\\u9898\\u5E93)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 841,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateTeamModal, {\n      teamId: teamId,\n      type: CREATETYPE_UPGRADE,\n      visible: createTeamModalVisible,\n      onCancel: () => setCreateTeamModalVisible(false),\n      onOk: () => reGetData(),\n      productList: productsFinal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 878,\n      columnNumber: 7\n    }, this), !!cloudBuyVisible && /*#__PURE__*/_jsxDEV(CloudDownloadBuy, {\n      visible: cloudBuyVisible,\n      teamId: teamId,\n      onCancel: () => setCloudBuyVisible(false),\n      reGet: () => {\n        loadTeamProductList();\n        setCloudBuyVisible(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 887,\n      columnNumber: 9\n    }, this), !!currentAuthProd && /*#__PURE__*/_jsxDEV(ProdAuthMbrsDrawer, {\n      teamId: teamId,\n      currentAuthProd: currentAuthProd,\n      allUsers: allUsers,\n      prodAuthMbrsDrawerVisible: prodAuthMbrsDrawerVisible,\n      setProdAuthMbrsDrawerVisible: setProdAuthMbrsDrawerVisible,\n      loadTeamMemberList: loadTeamProductList,\n      onOpenMemberAuthDrawer: member => {\n        // 切换到成员授权应用抽屉\n        setCurrentMember(member);\n        setAuthDrawerVisible(true);\n        setProdAuthMbrsDrawerVisible(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 898,\n      columnNumber: 9\n    }, this), !!currentMember && /*#__PURE__*/_jsxDEV(MbrAuthProdsDrawer, {\n      teamId: teamId,\n      currentMember: currentMember,\n      authProdList: currentMember === null || currentMember === void 0 ? void 0 : currentMember.authProductList,\n      loadTeamMemberList: loadTeamProductList,\n      authDrawerVisible: authDrawerVisible,\n      setAuthDrawerVisible: setAuthDrawerVisible,\n      allUsers: allUsers,\n      onMemberChange: member => {\n        setCurrentMember(member);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 914,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      className: \"tms-modal\",\n      title: `购买历史-${historyItem === null || historyItem === void 0 ? void 0 : historyItem.productName}`,\n      open: historyVisible,\n      centered: true,\n      width: 1000,\n      onCancel: () => setHistoryVisible(false),\n      footer: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          style: {\n            borderRadius: 5\n          },\n          onClick: () => setHistoryVisible(false),\n          children: \"\\u6211\\u77E5\\u9053\\u4E86\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 936,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 935,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Skeleton, {\n        loading: historyLoading,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          className: \"before-header\",\n          bordered: true,\n          dataSource: historyList,\n          showSorterTooltip: false,\n          pagination: {\n            position: [\"bottomCenter\"],\n            size: \"small\",\n            pageSize: 10,\n            showQuickJumper: true,\n            showSizeChanger: false,\n            total: historyList.length,\n            showTotal: total => {\n              return `共${total}条`;\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            title: \"#\",\n            dataIndex: \"seqNo\",\n            render: (seqNo, item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: index + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 47\n            }, this)\n          }, \"seqNo\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            title: \"订单号\",\n            dataIndex: \"orderNo\",\n            render: (orderNo, item) => item.ownerFlg == 1 && (!!item.orderDt || !!item.paidDt) ? /*#__PURE__*/_jsxDEV(\"a\", {\n              onClick: () => orderNoClick(item),\n              children: orderNoFormat(item)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: orderNoFormat(item)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 19\n            }, this)\n          }, \"orderNo\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ColumnGroup, {\n            title: \"购买前\",\n            className: \"top-header-a\",\n            children: [/*#__PURE__*/_jsxDEV(Column, {\n              title: (historyItem === null || historyItem === void 0 ? void 0 : historyItem.productId) == eProductId.Pid_13_Cdisk ? \"下载流量\" : \"授权数\",\n              dataIndex: \"authCntBefore\",\n              className: \"top-header-a\",\n              render: authCntBefore => (historyItem === null || historyItem === void 0 ? void 0 : historyItem.productId) == eProductId.Pid_13_Cdisk ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [authCntBefore, \"G\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 21\n              }, this) : authCntBefore > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [authCntBefore, \"\\u4EBA\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 997,\n                columnNumber: 21\n              }, this)\n            }, \"authCntBefore\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              title: \"有效期至\",\n              dataIndex: \"expirationDtBefore\",\n              className: \"top-header-a\",\n              render: expirationDtBefore => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: !!expirationDtBefore ? moment(expirationDtBefore).format(\"YYYY-MM-DD\") : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 19\n              }, this)\n            }, \"expirationDtBefore\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ColumnGroup, {\n            title: \"规格选择\",\n            className: \"top-header-b\",\n            children: [/*#__PURE__*/_jsxDEV(Column, {\n              title: (historyItem === null || historyItem === void 0 ? void 0 : historyItem.productId) == eProductId.Pid_13_Cdisk ? \"下载流量\" : \"增/减员数\",\n              dataIndex: \"adjustAuthCnt\",\n              className: \"top-header-b\",\n              render: adjustAuthCnt => (historyItem === null || historyItem === void 0 ? void 0 : historyItem.productId) == eProductId.Pid_13_Cdisk ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [adjustAuthCnt, \"G\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [adjustAuthCnt < 0 ? \"\" : \"+\", adjustAuthCnt || 0, \"\\u4EBA\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1025,\n                columnNumber: 21\n              }, this)\n            }, \"adjustAuthCnt\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              title: \"购买时长\",\n              dataIndex: \"adjustMonthCnt\",\n              className: \"top-header-b\",\n              render: adjustMonthCnt => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: addMonthCntFormat(adjustMonthCnt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 19\n              }, this)\n            }, \"adjustMonthCnt\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ColumnGroup, {\n            title: \"购买结果\",\n            className: \"top-header-c\",\n            children: [/*#__PURE__*/_jsxDEV(Column, {\n              title: (historyItem === null || historyItem === void 0 ? void 0 : historyItem.productId) == eProductId.Pid_13_Cdisk ? \"下载流量\" : \"授权数\",\n              dataIndex: \"authCnt\",\n              className: \"top-header-c\",\n              render: authCnt => (historyItem === null || historyItem === void 0 ? void 0 : historyItem.productId) == eProductId.Pid_13_Cdisk ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [authCnt, \"G\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [authCnt, \"\\u4EBA\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 21\n              }, this)\n            }, \"authCnt\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              title: \"有效期至\",\n              dataIndex: \"expirationDt\",\n              className: \"top-header-c\",\n              render: expirationDt => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: !!expirationDt ? moment(expirationDt).format(\"YYYY-MM-DD\") : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 19\n              }, this)\n            }, \"expirationDt\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            title: \"下单人\",\n            dataIndex: \"creatorName\",\n            render: creatorName => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: !!creatorName ? creatorName : \"-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this)\n          }, \"creatorName\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 1070,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            title: \"下单时间\",\n            dataIndex: \"orderDt\",\n            render: orderDt => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: !!orderDt ? moment(orderDt).format(\"YYYY-MM-DD HH:mm:ss\") : \"-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1083,\n              columnNumber: 17\n            }, this),\n            sorter: sort,\n            defaultSortOrder: \"descend\"\n          }, \"orderDt\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 1078,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            title: \"状态\",\n            dataIndex: \"status\",\n            render: (status, item) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1096,\n              columnNumber: 41\n            }, this)\n          }, \"status\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 1092,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 946,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 927,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      className: \"tms-modal\",\n      title: \"\\u5E94\\u7528\\u8FC7\\u671F\\u63D0\\u793A\",\n      open: expireVisible && !!expireItem,\n      centered: true,\n      width: 400,\n      maskClosable: false,\n      onCancel: () => expireVisibleChange(false, null),\n      footer: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            borderRadius: 5\n          },\n          onClick: () => expireVisibleChange(false, null),\n          children: \"\\u6211\\u77E5\\u9053\\u4E86\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          style: {\n            borderRadius: 5\n          },\n          onClick: () => {\n            (expireItem === null || expireItem === void 0 ? void 0 : expireItem.productId) == eProductId.Pid_13_Cdisk ? setCloudBuyVisible(true) : setCreateTeamModalVisible(true);\n            expireVisibleChange(false, null);\n          },\n          children: \"\\u7EED\\u8D39\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1117,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1110,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: \"center\"\n        },\n        children: [\"\\u5E94\\u7528(\", expireItem === null || expireItem === void 0 ? void 0 : expireItem.productName, \")\\u6709\\u6548\\u671F\\u81F3\", timeFormat(expireItem), \"\\uFF0C\\u5DF2\\u8FC7\\u671F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1132,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 782,\n    columnNumber: 5\n  }, this);\n}\n_s(Settings_Product, \"uTjvN74I39Y0p7HE7q5powSdIi4=\", false, function () {\n  return [useNavigate, useParams, useParams];\n});\n_c = Settings_Product;\nvar _c;\n$RefreshReg$(_c, \"Settings_Product\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "useNavigate", "httpSettings", "Switch", "Table", "Select", "Skeleton", "<PERSON><PERSON>", "Popover", "DraggablePopUp", "moment", "CreateTeamModal", "CREATETYPE_UPGRADE", "ProdAuthMbrsDrawer", "MbrAuthProdsDrawer", "CloudDownloadBuy", "ProductVip", "ProductVip_x", "eOrderStatus", "eProductGroupId", "eProductId", "eProductStatus", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Column", "ColumnGroup", "Settings_Product", "teamId", "propsTeamId", "productId", "propsProductId", "visible", "propsVisible", "_s", "navigate", "paramsTeamId", "paramsProductId", "_prodId", "productsLoading", "setProductsLoading", "groupList", "setGroupList", "productsForUi", "setProductsForUi", "productsFinal", "setProductsFinal", "createTeamModalVisible", "setCreateTeamModalVisible", "typeValue", "setTypeValue", "listHeight", "setListHeight", "prodAuthMbrsDrawerVisible", "setProdAuthMbrsDrawerVisible", "currentAuthProd", "setCurrentAuthProd", "historyVisible", "setHistoryVisible", "historyLoading", "setHistoryLoading", "historyList", "setHistoryList", "historyItem", "setHistoryItem", "cloudBuyVisible", "setCloudBuyVisible", "tipsShow", "setTipsShow", "expireVisible", "setExpireVisible", "expireItem", "setExpireItem", "setProductId", "allUsers", "setAllUsers", "authDrawerVisible", "setAuthDrawerVisible", "currentMember", "setCurrentMember", "loadTeamProductList", "loadUserList", "changeListHeight", "window", "addEventListener", "removeEventListener", "document", "documentElement", "clientHeight", "length", "item", "filter", "i", "newUrl", "history", "replaceState", "authProductToMembers", "team_711_get_team_product_list", "then", "res", "resultCode", "productListFormat", "productList", "setting_202_get_team_allusergrp", "users", "userList", "error", "console", "_prodList", "for<PERSON>ach", "product", "find", "_product", "groupId", "push", "groupName", "products", "_allProds", "group", "map", "_prod", "index", "_productsFinal$find", "_productsFinal$find2", "key", "isRowSpan", "groupListLength", "authCntDesc", "objCntDesc", "concat", "<PERSON><PERSON><PERSON><PERSON>", "enableFlg", "team_712_toggle_product", "reGetData", "timeFormat", "time", "expirationDt", "freeFlg", "format", "lineColorFormat", "statusType", "Status_3_Unreleased", "lineColorXFormat", "dataIndex", "Pid_11_Explorer", "Pid_12_Space", "isAfter", "operationFormat", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "textAlign", "onClick", "Pid_13_Cdisk", "color", "backgroundColor", "borderRadius", "padding", "margin", "display", "justifyContent", "getHistoryList", "size", "disabled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "checked", "onChange", "team_722_get_order_list_by_product_id", "list", "orderList", "id", "status", "Status_1_Paid", "expireVisibleChange", "columns", "title", "render", "onCell", "rowSpan", "productName", "alignItems", "Status_2_QA", "fontSize", "marginLeft", "Pgid_1_OS", "height", "src", "open", "placement", "trigger", "fontWeight", "className", "content", "marginTop", "type", "flex", "authUserCnt", "authCnt", "totalCntDesc", "usedFormat", "objCntWarningFlg", "operation", "str", "singleList", "split", "count", "single", "substring", "indexOf", "str2d", "str3d", "str4d", "onProductTypeFilterChanged", "value", "filterList", "addMonthCntFormat", "addMonthCnt", "toString", "orderNoFormat", "orderDt", "paidDt", "orderNoClick", "location", "origin", "orderId", "sort", "a", "b", "_a", "_b", "parseInt", "onClose", "saveFlg", "loading", "dropdownMatchSelectWidth", "Option", "paddingRight", "bordered", "pagination", "scroll", "y", "dataSource", "visibility", "onCancel", "onOk", "reGet", "loadTeamMemberList", "onOpenMemberAuthDrawer", "member", "authProdList", "authProductList", "onMemberChange", "centered", "footer", "showSorterTooltip", "position", "pageSize", "showQuickJumper", "showSizeChanger", "total", "showTotal", "seqNo", "orderNo", "ownerFlg", "authCntBefore", "expirationDtBefore", "adjustAuthCnt", "adjustMonthCnt", "<PERSON><PERSON><PERSON>", "sorter", "defaultSortOrder", "maskClosable", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/settings/views/product/Settings_Product.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport * as httpSettings from \"@common/api/http\";\r\nimport { Switch, Table, Select, Skeleton, Button, Popover } from \"antd\";\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";\r\nimport moment from \"moment\";\r\nimport CreateTeamModal, {\r\n  CREATETYPE_UPGRADE,\r\n} from \"@components/CreateTeam\";\r\nimport ProdAuthMbrsDrawer from \"../user/ProdAuthMbrsDrawer\";\r\nimport MbrAuthProdsDrawer from \"../user/MbrAuthProdsDrawer\";\r\nimport { CloudDownloadBuy } from \"@/settings/utils/CloudDownloadDraggable\";\r\nimport ProductVip from \"../../../personal/assets/images/productVip.png\";\r\nimport ProductVip_x from \"../../../personal/assets/images/productVip_x.png\";\r\nimport { eOrderStatus, eProductGroupId, eProductId, eProductStatus } from \"@common/utils/enum\";\r\n\r\nconst { Column, ColumnGroup } = Table;\r\n\r\nexport default function Settings_Product({ teamId: propsTeamId, productId: propsProductId, visible: propsVisible }) {\r\n  //const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const paramsTeamId = useParams().teamId;\r\n  const teamId = propsTeamId || paramsTeamId; // 优先使用props，否则使用路由参数\r\n  const paramsProductId = useParams().productId;\r\n  const _prodId = propsProductId || paramsProductId; // 优先用props，其次用路由\r\n  const [productsLoading, setProductsLoading] = useState(false);\r\n  const [groupList, setGroupList] = useState([]);\r\n  const [productsForUi, setProductsForUi] = useState([]); //UI呈现的产品列表(会有类型过滤导致显示的产品列表有筛选)\r\n  const [productsFinal, setProductsFinal] = useState([]); //实际完整的产品列表\r\n  const [createTeamModalVisible, setCreateTeamModalVisible] = useState(false);\r\n  const [typeValue, setTypeValue] = useState(0);\r\n  const [listHeight, setListHeight] = useState(0);\r\n  const [prodAuthMbrsDrawerVisible, setProdAuthMbrsDrawerVisible] = useState(false); //显示授权穿梭框\r\n  const [currentAuthProd, setCurrentAuthProd] = useState(null); //\"当前\"点击的授权应用\r\n  const [historyVisible, setHistoryVisible] = useState(false);\r\n  const [historyLoading, setHistoryLoading] = useState(false);\r\n  const [historyList, setHistoryList] = useState([]);\r\n  const [historyItem, setHistoryItem] = useState(null);\r\n  const [cloudBuyVisible, setCloudBuyVisible] = useState(false);\r\n  const [tipsShow, setTipsShow] = useState(false);\r\n  const [expireVisible, setExpireVisible] = useState(false);\r\n  const [expireItem, setExpireItem] = useState(null);\r\n  const [productId, setProductId] = useState(_prodId);\r\n  const [allUsers, setAllUsers] = useState([]); // 所有用户列表\r\n  const [authDrawerVisible, setAuthDrawerVisible] = useState(false); // 成员授权应用抽屉\r\n  const [currentMember, setCurrentMember] = useState(null); // 当前选中的成员\r\n\r\n  useEffect(() => {\r\n    loadTeamProductList();\r\n    loadUserList();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    changeListHeight();\r\n    window.addEventListener(\"resize\", changeListHeight);\r\n    return () => {\r\n      window.removeEventListener(\"resize\", changeListHeight);\r\n    };\r\n  }, []);\r\n\r\n  // 最大高度\r\n  const changeListHeight = () => {\r\n    // topbar、标题、页面header、页面footer、table表头页脚\r\n    let listHeight = document.documentElement.clientHeight - 320;\r\n    setListHeight(listHeight);\r\n  };\r\n\r\n  //20250621 跳转到产品页时，打开授权对话框\r\n  useEffect(() => {\r\n    if(productId && productsFinal.length > 0 && ((propsVisible && !paramsProductId) || (!propsVisible && paramsProductId))) {\r\n      let item = productsFinal.filter(i => i.productId == productId);\r\n      if(paramsProductId) { //代表是在 Settings Page形态，而不是 Drawer\r\n        const newUrl = `/#/${teamId}/settings/product`; //移除 productId\r\n        window.history.replaceState({}, '', newUrl);\r\n        setProductId(null);\r\n      }\r\n      if(item.length > 0)  //打开授权产品对话框\r\n        authProductToMembers(item[0]);\r\n    }\r\n  },[_prodId,productsFinal,propsVisible]);\r\n\r\n  //加载授权产品列表\r\n  async function loadTeamProductList() {\r\n    setProductsLoading(true);\r\n    await httpSettings\r\n      .team_711_get_team_product_list({ teamId: teamId })\r\n      .then((res) => {\r\n        if (res.resultCode == 200) {\r\n          productListFormat(res.productList || []);\r\n        }\r\n      });\r\n    setProductsLoading(false);\r\n  }\r\n\r\n  // 加载用户列表\r\n  async function loadUserList() {\r\n    try {\r\n      const res = await httpSettings.setting_202_get_team_allusergrp({\r\n        teamId: teamId\r\n      });\r\n      \r\n      if (res.resultCode === 200) {\r\n        const users = res.userList || [];\r\n        setAllUsers(users);\r\n      }\r\n    } catch (error) {\r\n      console.error('加载用户列表失败:', error);\r\n    }\r\n  }\r\n\r\n  function productListFormat(productList = []) {\r\n    let _prodList = [];\r\n    productList.forEach((product) => {\r\n      let item = _prodList.find(\r\n        (_product) => product.groupId == _product.groupId\r\n      );\r\n      if (!item) {\r\n        _prodList.push({\r\n          groupName: product.groupName,\r\n          groupId: product.groupId,\r\n          products: [product],\r\n        });\r\n      } else {\r\n        item.products.push(product);\r\n      }\r\n    });\r\n    setGroupList([..._prodList]);\r\n    let _allProds = [];\r\n    _prodList.forEach((group) => {\r\n      let groupList = group.products.map((_prod, index) => ({\r\n        ..._prod,\r\n        key: _prod.productId,\r\n        isRowSpan: index == 0 ? true : false,\r\n        groupListLength: index == 0 ? group.products.length : 0,\r\n        authCntDesc: !!_prod.authCntDesc ?\r\n          _prod.authCntDesc : productsFinal.find((product) => product.productId == _prod.productId)?.authCntDesc || \"\",\r\n        objCntDesc: !!_prod.objCntDesc ?\r\n          _prod.objCntDesc : productsFinal.find((product) => product.productId == _prod.productId)?.objCntDesc || \"\",\r\n      }));\r\n      _allProds = _allProds.concat(groupList);\r\n    });\r\n\r\n    setProductsForUi([..._allProds]);\r\n    setProductsFinal([..._allProds]);\r\n  }\r\n\r\n  //启用/禁用某个应用\r\n  function setToggle(productId, enableFlg) {\r\n    httpSettings\r\n      .team_712_toggle_product({\r\n        teamId: teamId,\r\n        productId: productId,\r\n        enableFlg: enableFlg,\r\n      })\r\n      .then((res) => {\r\n        if (res.resultCode == 200) {\r\n          productListFormat(res.productList || []);\r\n        }\r\n      });\r\n  }\r\n\r\n  function authProductToMembers(_product) {  //打开授权对话框\r\n    setCurrentAuthProd(_product);\r\n    setProdAuthMbrsDrawerVisible(true);\r\n  }\r\n\r\n  function reGetData() {\r\n    loadTeamProductList();\r\n    setCreateTeamModalVisible(false);\r\n    setTipsShow(true);\r\n  }\r\n\r\n  //显示到期时间\r\n  function timeFormat(item) {\r\n    let time = item?.expirationDt;\r\n    return item?.freeFlg == 1 ? \"∞\"\r\n      : time ? time == \"2099-12-31 23:59:59\" ? \"∞\"\r\n        : moment(time).format(\"YYYY-MM-DD\")\r\n      : \"-\";\r\n  }\r\n\r\n  function lineColorFormat(item) {\r\n    if (\r\n      item.enableFlg != 1 ||\r\n      item.statusType == eProductStatus.Status_3_Unreleased //即将推出\r\n    ) {\r\n      return \"#AAAAAA\";\r\n    }\r\n    return \"#000\";\r\n  }\r\n\r\n  function lineColorXFormat(item, dataIndex) {\r\n    if (\r\n      item.productId != eProductId.Pid_11_Explorer && item.productId != eProductId.Pid_12_Space &&\r\n      item.freeFlg == 0 &&\r\n      !!item.expirationDt && moment().isAfter(moment(item.expirationDt))\r\n    ) {\r\n      //已过期\r\n      if (item.enableFlg == 1 && dataIndex == \"expirationDt\") {\r\n        return \"red\"; //未禁用\r\n      }\r\n      return \"#AAAAAA\";\r\n    }\r\n    return lineColorFormat(item);\r\n  }\r\n\r\n  function operationFormat(item) {\r\n    if (\r\n      item.productId == eProductId.Pid_11_Explorer || item.productId == eProductId.Pid_12_Space ||\r\n      item.statusType == eProductStatus.Status_3_Unreleased\r\n    ) {\r\n      return <div>-</div>;\r\n    }\r\n    // if(item.statusType == eProductStatus.Status_3_Unreleased ){ //即将推出\r\n    //   return (\r\n    //     <>\r\n    //       <div style={{width:147}}/>\r\n    //       <div style={{width:50,display:'flex',justifyContent:'center'}}>\r\n    //         <Switch size=\"small\"\r\n    //           checkedChildren=\"启用\"\r\n    //           unCheckedChildren=\"禁用\"\r\n    //           checked={item.enableFlg == 1}\r\n    //           onChange={() => item.enableFlg == 1 ? setToggle(item.productId, 0) : setToggle(item.productId, 1)}\r\n    //         />\r\n    //       </div>\r\n    //     </>\r\n    //   );\r\n    // }\r\n    return (\r\n      <>\r\n        {/*第1列 购买/续费*/}\r\n        <div style={{ width: 50, textAlign: \"center\" }}>\r\n          {item.freeFlg == 0 &&\r\n          !!item.expirationDt &&\r\n          moment().isAfter(moment(item.expirationDt)) ? ( //已过期\r\n            <a\r\n              onClick={() =>\r\n                item.productId != eProductId.Pid_13_Cdisk\r\n                  ? setCreateTeamModalVisible(true)\r\n                  : setCloudBuyVisible(true)\r\n              }\r\n              style={\r\n                item.enableFlg == 1\r\n                  ?\r\n                  { color: \"#fff\", backgroundColor: \"red\", borderRadius: 5, padding: \"2px 8px\", }\r\n                  :\r\n                  { color: \"#fff\", backgroundColor: \"#aaa\", borderRadius: 5, padding: \"2px 8px\", }\r\n              }\r\n            >\r\n              续费\r\n            </a>\r\n          ) : (\r\n            <a\r\n              onClick={() =>\r\n                item.productId != eProductId.Pid_13_Cdisk ?\r\n                  setCreateTeamModalVisible(true) : setCloudBuyVisible(true)\r\n              }\r\n            >\r\n              购买\r\n            </a>\r\n          )}\r\n        </div>\r\n        {/*第2列 购买历史*/}\r\n        <div style={{ margin: \"0px 5px\" }}>|</div>\r\n        <a\r\n          style={{ width: 70, display: \"flex\", justifyContent: \"center\" }}\r\n          onClick={() => {\r\n            setHistoryVisible(true);\r\n            getHistoryList(item);\r\n            setHistoryItem(item);\r\n          }}\r\n        >\r\n          购买历史\r\n        </a>\r\n        <div style={{ margin: \"0px 5px\" }}>|</div>\r\n        {/*第3列 启用/禁用*/}\r\n        <div style={{ width: 60, display: \"flex\", justifyContent: \"center\" }}>\r\n          <Switch\r\n            size=\"small\"\r\n            disabled={\r\n              //item.enableFlg != 1 ||   //20250523 Jim 注释掉，不然无法再开启 tmsbug-12291\r\n              item.statusType == eProductStatus.Status_3_Unreleased\r\n            }\r\n            checkedChildren=\"启用\"\r\n            unCheckedChildren=\"禁用\"\r\n            checked={item.enableFlg == 1}\r\n            onChange={() =>\r\n              item.enableFlg == 1\r\n                ?\r\n                setToggle(item.productId, 0)\r\n                :\r\n                setToggle(item.productId, 1)\r\n            }\r\n          />\r\n        </div>\r\n      </>\r\n    );\r\n  }\r\n\r\n  //单个“应用”的购买历史\r\n  async function getHistoryList(item) {\r\n    setHistoryLoading(true);\r\n    await httpSettings\r\n      .team_722_get_order_list_by_product_id({\r\n        teamId,\r\n        productId: item.productId,\r\n      })\r\n      .then((res) => {\r\n        if (res.resultCode == 200) {\r\n          let list = (res.orderList || []).map((history) => {\r\n            history.key = history.id;\r\n            history.status = history.statusType == eOrderStatus.Status_1_Paid ? \"已支付\" : \"未支付\";\r\n            return history;\r\n          });\r\n          setHistoryList([...list]);\r\n        }\r\n      });\r\n    setHistoryLoading(false);\r\n  }\r\n\r\n  function expireVisibleChange(visible, item) {\r\n    setExpireVisible(visible);\r\n    setExpireItem(item);\r\n  }\r\n\r\n  const columns = [\r\n    {\r\n      title: (\r\n        <div style={{ display: \"flex\", justifyContent: \"center\" }}>类别</div>\r\n      ),\r\n      dataIndex: \"groupName\",\r\n      key: \"groupName\",\r\n      render: (groupName, item) => (\r\n        <div style={{ display: \"flex\", justifyContent: \"center\" }}>\r\n          {groupName}\r\n        </div>\r\n      ),\r\n      onCell: (item) => {\r\n        if (item.isRowSpan) {\r\n          return { rowSpan: item.groupListLength };\r\n        } else {\r\n          return { rowSpan: 0 };\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <div style={{ display: \"flex\", justifyContent: \"center\" }}>应用</div>\r\n      ),\r\n      dataIndex: \"productName\",\r\n      key: \"productName\",\r\n      render: (productName, item) => (\r\n        <div\r\n          style={{\r\n            display: \"flex\",\r\n            justifyContent: \"space-between\",\r\n            alignItems: \"center\",\r\n            color: lineColorFormat(item),\r\n          }}\r\n        >\r\n          {productName}\r\n          {item.statusType == eProductStatus.Status_2_QA && (\r\n            <span style={{ color: \"#70B603\", fontSize: 12, marginLeft: 10 }}>\r\n              内测中\r\n            </span>\r\n          )}\r\n          {item.statusType == eProductStatus.Status_3_Unreleased && (\r\n            <span style={{ color: \"#F59A23\", fontSize: 12, marginLeft: 10 }}>\r\n              即将推出\r\n            </span>\r\n          )}\r\n        </div>\r\n      ),\r\n      onCell: (item) => {\r\n        return { rowSpan: 1 };\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <div style={{ display: \"flex\", justifyContent: \"center\" }}>\r\n          有效期至\r\n        </div>\r\n      ),\r\n      dataIndex: \"expirationDt\",\r\n      key: \"expirationDt\",\r\n      render: (expirationDt, item) => {\r\n        if (\r\n          item.productId != eProductId.Pid_11_Explorer &&\r\n          item.productId != eProductId.Pid_12_Space &&\r\n          item.freeFlg == 0 &&\r\n          !!item.expirationDt &&\r\n          moment().isAfter(moment(item.expirationDt))\r\n        ) {\r\n          return (\r\n            <div\r\n              style={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"center\",\r\n              }}\r\n              title={`应用'${item.productName}'Vip已过期，请续费`}\r\n            >\r\n              <a\r\n                onClick={() => {\r\n                  item.productId == eProductId.Pid_13_Cdisk\r\n                    ? setCloudBuyVisible(true)\r\n                    : setCreateTeamModalVisible(true);\r\n                }}\r\n                style={{ color: lineColorXFormat(item, \"expirationDt\") }}\r\n              >\r\n                {timeFormat(item)}\r\n              </a>\r\n            </div>\r\n          );\r\n        }\r\n        return (\r\n          <div\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"center\",\r\n            }}\r\n          >\r\n            <div style={{ color: lineColorXFormat(item) }}>\r\n              {timeFormat(item)}\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n      onCell: (item) => {\r\n        return { rowSpan: 1 };\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <div style={{ display: \"flex\", justifyContent: \"center\" }}>\r\n          应用版本\r\n        </div>\r\n      ),\r\n      dataIndex: \"freeFlg\",\r\n      key: \"freeFlg\",\r\n      render: (freeFlg, item) => (\r\n        <div\r\n          style={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            color: lineColorFormat(item),\r\n          }}\r\n        >\r\n          {item.groupId == eProductGroupId.Pgid_1_OS && item.productId != eProductId.Pid_13_Cdisk ? (\r\n            \"-\"\r\n          ) : freeFlg == 0 ? (\r\n            !!item.expirationDt &&\r\n            moment().isAfter(moment(item.expirationDt)) ? (\r\n              <a\r\n                style={{ color: \"inherit\" }}\r\n                onClick={() =>\r\n                  item.productId != eProductId.Pid_13_Cdisk\r\n                    ? setCreateTeamModalVisible(true)\r\n                    : setCloudBuyVisible(true)\r\n                }\r\n              >\r\n                <img style={{ height: 22 }} src={ProductVip_x} />\r\n              </a>\r\n            ) : (\r\n              <img style={{ height: 22 }} src={ProductVip} />\r\n            )\r\n          ) : (\r\n            \"基础版\"\r\n          )}\r\n        </div>\r\n      ),\r\n      onCell: (item) => {\r\n        return { rowSpan: 1 };\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <Popover\r\n          open={tipsShow}\r\n          placement=\"bottom\"\r\n          trigger={\"click\"}\r\n          title={\r\n            <div\r\n              style={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"space-between\",\r\n              }}\r\n            >\r\n              <span style={{ fontWeight: \"bold\", fontSize: 16 }}>提示</span>\r\n              <a\r\n                style={{ marginLeft: 20, color: \"#666\" }}\r\n                onClick={() => setTipsShow(false)}\r\n              >\r\n                <span className=\"iconfont guanbi\" style={{ fontSize: 16 }} />\r\n              </a>\r\n            </div>\r\n          }\r\n          content={\r\n            <div style={{ textAlign: \"center\" }}>\r\n              <div>只有授权了的成员才能正常使用Vip应用</div>\r\n              <div>您购买了Vip应用，可点击“已授权/总授权数”列的</div>\r\n              <div>蓝色链接，进行应用授权</div>\r\n              <div style={{ marginTop: 30 }}>\r\n                <Button\r\n                  type={\"primary\"}\r\n                  style={{ borderRadius: 5 }}\r\n                  onClick={() => setTipsShow(false)}\r\n                >\r\n                  我知道了\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          }\r\n        >\r\n          <div style={{ display: \"flex\", justifyContent: \"center\" }}>\r\n            已授权/总授权数\r\n          </div>\r\n        </Popover>\r\n      ),\r\n      dataIndex: \"authCntDesc\",\r\n      key: \"authCntDesc\",\r\n      render: (authCntDesc, _product) => {\r\n        return (\r\n          <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n            <div style={{ flex: 0.5 }} />\r\n            <div style={{ flex: 1, display: \"flex\", justifyContent: \"center\" }}>\r\n              {_product.groupId != eProductGroupId.Pgid_1_OS ? (\r\n                <a\r\n                  onClick={() => {\r\n                    !!_product.expirationDt &&\r\n                    moment().isAfter(moment(_product.expirationDt))\r\n                      ? expireVisibleChange(true, _product)\r\n                      : authProductToMembers(_product);\r\n                  }}\r\n                >\r\n                  {/*{item.freeFlg == 0 ? (authCntDesc || \"\") : `${item.authUserCnt||0}人`}*/}\r\n                  {authCntDesc || \"\"}\r\n                </a>\r\n              ) : (\r\n                <div>-</div>\r\n              )}\r\n            </div>\r\n            <div style={{ flex: 0.5, display: \"flex\", justifyContent: \"end\" }}>\r\n              {_product.groupId != eProductGroupId.Pgid_1_OS &&\r\n              _product.freeFlg == 0 &&\r\n              !!_product.expirationDt &&\r\n              moment().isAfter(moment(_product.expirationDt)) &&\r\n              _product.authUserCnt > _product.authCnt ? (\r\n                <a\r\n                  style={\r\n                    _product.enableFlg == 1 ? { color: \"red\" } : { color: \"#aaa\" }\r\n                  }\r\n                  onClick={() => expireVisibleChange(true, _product)}\r\n                  className=\"iconfont guoqitishi\"\r\n                  title={`应用'${_product.productName}'Vip已过期，请续费\\n授权人数超出授权额度，请检查。`}\r\n                />\r\n              ) : _product.productId != eProductId.Pid_11_Explorer &&\r\n                _product.productId != eProductId.Pid_12_Space &&\r\n                _product.freeFlg == 0 &&\r\n                !!_product.expirationDt &&\r\n                moment().isAfter(moment(_product.expirationDt)) ? (\r\n                <a\r\n                  style={\r\n                    _product.enableFlg == 1 ? { color: \"red\" } : { color: \"#aaa\" }\r\n                  }\r\n                  onClick={() => expireVisibleChange(true, _product)}\r\n                  className=\"iconfont guoqitishi\"\r\n                  title={`应用'${_product.productName}'Vip已过期，请续费`}\r\n                />\r\n              ) : _product.authUserCnt > _product.authCnt &&\r\n                _product.freeFlg == 0 &&\r\n                _product.groupId != eProductGroupId.Pgid_1_OS ? (\r\n                <a\r\n                  style={{ color: \"#F59A23\" }}\r\n                  onClick={() => authProductToMembers(_product)}\r\n                  title={`授权人数超出授权额度，请检查。`}\r\n                  className=\"iconfont guoqitishi\"\r\n                />\r\n              ) : null}\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n      onCell: (item) => {\r\n        return { rowSpan: 1 };\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <div style={{ display: \"flex\", justifyContent: \"center\" }}>\r\n          已使用/总量\r\n        </div>\r\n      ),\r\n      dataIndex: \"objCntDesc\",\r\n      key: \"objCntDesc\",\r\n      render: (objCntDesc, item) => {\r\n        return (\r\n          <div\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <div\r\n              title={\r\n                (item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk)\r\n                  ? (objCntDesc == \"-\"\r\n                    ? \"\"\r\n                    : objCntDesc)\r\n                  : item.totalCntDesc\r\n              }\r\n              className=\"tms-text-overflow\"\r\n              style={{ color: lineColorXFormat(item) }}\r\n            >\r\n              {usedFormat((item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk)\r\n                  ? (objCntDesc == \"-\"\r\n                    ? \"\"\r\n                    : objCntDesc)\r\n                  : item.totalCntDesc)}\r\n            </div>\r\n            {item.objCntWarningFlg == 1 &&\r\n            (item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk) ? (\r\n              <a\r\n                style={{ color: \"#F59A23\" }}\r\n                onClick={() =>\r\n                  item.productId != eProductId.Pid_13_Cdisk\r\n                    ? setCreateTeamModalVisible(true)\r\n                    : setCloudBuyVisible(true)\r\n                }\r\n                title={`应用'${item.productName}'免费额度已用完，请购买Vip版本`}\r\n                className=\"iconfont guoqitishi\"\r\n              />\r\n            ) : null}\r\n          </div>\r\n        );\r\n      },\r\n      onCell: (item) => {\r\n        return { rowSpan: 1 };\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <div style={{ display: \"flex\", justifyContent: \"center\" }}>操作</div>\r\n      ),\r\n      dataIndex: \"operation\",\r\n      key: \"operation\",\r\n      width: 230,\r\n      render: (operation, item) => (\r\n        <div\r\n          style={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          {operationFormat(item)}\r\n        </div>\r\n      ),\r\n      onCell: (item) => {\r\n        return { rowSpan: 1 };\r\n      },\r\n    },\r\n  ];\r\n\r\n  function usedFormat(str = \"\") {\r\n    let singleList = str.split(\"\");\r\n    let count = singleList.filter((single) => single == \"∞\").length;\r\n    if (count == 1) {\r\n      return (\r\n        <span>\r\n          {str.substring(0, str.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str.substring(str.indexOf(\"∞\") + 1, str.length)}\r\n        </span>\r\n      );\r\n    }\r\n    if (count == 2) {\r\n      let str2d = str.substring(str.indexOf(\"∞\") + 1, str.length);\r\n      return (\r\n        <span>\r\n          {str.substring(0, str.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str2d.substring(0, str2d.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str2d.substring(str2d.indexOf(\"∞\") + 1, str2d.length)}\r\n        </span>\r\n      );\r\n    }\r\n    if (count == 3) {\r\n      let str2d = str.substring(str.indexOf(\"∞\") + 1, str.length);\r\n      let str3d = str2d.substring(str2d.indexOf(\"∞\") + 1, str2d.length);\r\n      return (\r\n        <span>\r\n          {str.substring(0, str.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str2d.substring(0, str2d.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str3d.substring(0, str3d.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str3d.substring(str3d.indexOf(\"∞\") + 1, str3d.length)}\r\n        </span>\r\n      );\r\n    }\r\n    if (count == 4) {\r\n      let str2d = str.substring(str.indexOf(\"∞\") + 1, str.length);\r\n      let str3d = str2d.substring(str2d.indexOf(\"∞\") + 1, str2d.length);\r\n      let str4d = str3d.substring(str3d.indexOf(\"∞\") + 1, str3d.length);\r\n      return (\r\n        <span>\r\n          {str.substring(0, str.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str2d.substring(0, str2d.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str3d.substring(0, str3d.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str4d.substring(0, str4d.indexOf(\"∞\"))}\r\n          <span style={{ fontSize: 16 }}>∞</span>\r\n          {str4d.substring(str4d.indexOf(\"∞\") + 1, str4d.length)}\r\n        </span>\r\n      );\r\n    }\r\n    return str;\r\n  }\r\n\r\n  //应用类型下拉选择，切换类型\r\n  function onProductTypeFilterChanged(value) {\r\n    setTypeValue(value);\r\n    let filterList =\r\n      value == 0\r\n        ? productsFinal\r\n        : productsFinal.filter((product) => product.groupId == value);\r\n    setProductsForUi([...filterList]);\r\n  }\r\n\r\n  function addMonthCntFormat(addMonthCnt) {\r\n    return addMonthCnt == 6 ? \"半年\"\r\n      : addMonthCnt == 12 ? \"1年\"\r\n      : addMonthCnt == 24 ? \"2年\"\r\n      : addMonthCnt == 36 ? \"3年\"\r\n      : addMonthCnt == 0\r\n      ? \"-\"\r\n      : addMonthCnt.toString() + \"个月\";\r\n  }\r\n\r\n  function orderNoFormat(item) {\r\n    if (!!item.orderDt) {\r\n      return moment(item.orderDt).format(\"YYYYMMDDHHmmss\");\r\n    }\r\n    if (!!item.paidDt) {\r\n      return moment(item.paidDt).format(\"YYYYMMDDHHmmss\");\r\n    }\r\n    return \"-\";\r\n  }\r\n\r\n  function orderNoClick(item) {\r\n    window.open(window.location.origin + `/#/personal/myorder/${item.orderId}`);\r\n  }\r\n\r\n  function sort(a, b) {\r\n    let _a = orderNoFormat(a);\r\n    let _b = orderNoFormat(b);\r\n    if (_a != \"-\" && _b != \"-\") {\r\n      return parseInt(_a) - parseInt(_b);\r\n    }\r\n    if (_a != \"-\" && _b == \"-\") {\r\n      return parseInt(_a) - 0;\r\n    }\r\n    if (_a == \"-\" && _b != \"-\") {\r\n      return 0 - parseInt(_b);\r\n    }\r\n    return 0;\r\n  }\r\n\r\n  function onClose(saveFlg) {\r\n    setProdAuthMbrsDrawerVisible(false); //关闭授权弹出框\r\n  }\r\n\r\n  return (\r\n    <React.Fragment>\r\n      <Skeleton loading={productsLoading}>\r\n        <div\r\n          style={{ padding: \"10px 0px 5px 20px\", fontSize: 14, height: \"100%\" }}\r\n          className=\"product-set\"\r\n        >\r\n          <div\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n              应用类别：\r\n              <Select\r\n                dropdownMatchSelectWidth={false}\r\n                value={typeValue}\r\n                style={{ marginLeft: 10 }}\r\n                onChange={onProductTypeFilterChanged}\r\n              >\r\n                <Select.Option value={0}>全部</Select.Option>\r\n                {groupList.map((group) => {\r\n                  return (\r\n                    <Select.Option value={group.groupId}>\r\n                      {group.groupName}\r\n                    </Select.Option>\r\n                  );\r\n                })}\r\n              </Select>\r\n            </div>\r\n            {!!productsFinal.find((product) => product.freeFlg == 0) && (\r\n              <div style={{ paddingRight: 20 }}>\r\n                <a\r\n                  onClick={() => {\r\n                    navigate(`/personal/myorder`);\r\n                  }}\r\n                >\r\n                  查看订单\r\n                </a>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div style={{ width: \"100%\", marginTop: 10 }}>\r\n            <Table\r\n              size=\"small\"\r\n              className=\"small-table\"\r\n              bordered\r\n              style={{ paddingRight: 20 }}\r\n              pagination={false}\r\n              scroll={{\r\n                // y: \"calc(100vh - 160px)\", // 不支持calc https://github.com/ant-design/ant-design/issues/31909\r\n                y: listHeight,\r\n              }}\r\n              columns={columns}\r\n              dataSource={productsForUi}\r\n            />\r\n          </div>\r\n          <div style={{ height: 80 }}>\r\n            <div\r\n              style={{\r\n                marginTop: 10,\r\n                height: 20,\r\n                fontSize: 12,\r\n                color: \"#999\",\r\n                visibility: \"visible\",\r\n              }}\r\n            >\r\n              应用依赖关系备注：\r\n            </div>\r\n            <div\r\n              style={{\r\n                marginLeft: 10,\r\n                height: 20,\r\n                fontSize: 12,\r\n                color: \"#999\",\r\n                visibility: \"visible\",\r\n              }}\r\n            >\r\n              1. 报表、系统订阅(依赖于高级搜索)，仪表板(依赖于高级搜索+报表)\r\n            </div>\r\n            <div\r\n              style={{\r\n                marginLeft: 10,\r\n                height: 20,\r\n                fontSize: 12,\r\n                color: \"#999\",\r\n                visibility: \"visible\",\r\n              }}\r\n            >\r\n              2. 试卷(依赖于题库)，考试/作业(依赖于试卷+题库)\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </Skeleton>\r\n      <CreateTeamModal\r\n        teamId={teamId}\r\n        type={CREATETYPE_UPGRADE}\r\n        visible={createTeamModalVisible}\r\n        onCancel={() => setCreateTeamModalVisible(false)}\r\n        onOk={() => reGetData()}\r\n        productList={productsFinal}\r\n      />\r\n      {!!cloudBuyVisible && (\r\n        <CloudDownloadBuy\r\n          visible={cloudBuyVisible}\r\n          teamId={teamId}\r\n          onCancel={() => setCloudBuyVisible(false)}\r\n          reGet={() => {\r\n            loadTeamProductList();\r\n            setCloudBuyVisible(false);\r\n          }}\r\n        />\r\n      )}\r\n      {!!currentAuthProd && (\r\n        <ProdAuthMbrsDrawer\r\n          teamId={teamId}\r\n          currentAuthProd={currentAuthProd}\r\n          allUsers={allUsers}\r\n          prodAuthMbrsDrawerVisible={prodAuthMbrsDrawerVisible}\r\n          setProdAuthMbrsDrawerVisible={setProdAuthMbrsDrawerVisible}\r\n          loadTeamMemberList={loadTeamProductList}\r\n          onOpenMemberAuthDrawer={(member) => {\r\n            // 切换到成员授权应用抽屉\r\n            setCurrentMember(member);\r\n            setAuthDrawerVisible(true);\r\n            setProdAuthMbrsDrawerVisible(false);\r\n          }}\r\n        />\r\n      )}\r\n      {!!currentMember && (\r\n        <MbrAuthProdsDrawer\r\n          teamId={teamId}\r\n          currentMember={currentMember}\r\n          authProdList={currentMember?.authProductList}\r\n          loadTeamMemberList={loadTeamProductList}\r\n          authDrawerVisible={authDrawerVisible}\r\n          setAuthDrawerVisible={setAuthDrawerVisible}\r\n          allUsers={allUsers}\r\n          onMemberChange={(member) => {\r\n            setCurrentMember(member);\r\n          }}\r\n        />\r\n      )}\r\n      <DraggablePopUp\r\n        className=\"tms-modal\"\r\n        title={`购买历史-${historyItem?.productName}`}\r\n        open={historyVisible}\r\n        centered\r\n        width={1000}\r\n        onCancel={() => setHistoryVisible(false)}\r\n        footer={\r\n          <div style={{ display: \"flex\", justifyContent: \"center\" }}>\r\n            <Button\r\n              type={\"primary\"}\r\n              style={{ borderRadius: 5 }}\r\n              onClick={() => setHistoryVisible(false)}\r\n            >\r\n              我知道了\r\n            </Button>\r\n          </div>\r\n        }\r\n      >\r\n        <Skeleton loading={historyLoading}>\r\n          <Table\r\n            size=\"small\"\r\n            className=\"before-header\"\r\n            bordered\r\n            dataSource={historyList}\r\n            showSorterTooltip={false}\r\n            pagination={{\r\n              position: [\"bottomCenter\"],\r\n              size: \"small\",\r\n              pageSize: 10,\r\n              showQuickJumper: true,\r\n              showSizeChanger: false,\r\n              total: historyList.length,\r\n              showTotal: (total) => {\r\n                return `共${total}条`;\r\n              },\r\n            }}\r\n          >\r\n            <Column\r\n              title={\"#\"}\r\n              dataIndex={\"seqNo\"}\r\n              key={\"seqNo\"}\r\n              render={(seqNo, item, index) => <div>{index + 1}</div>}\r\n            />\r\n            <Column\r\n              title={\"订单号\"}\r\n              dataIndex={\"orderNo\"}\r\n              key={\"orderNo\"}\r\n              render={(orderNo, item) =>\r\n                item.ownerFlg == 1 && (!!item.orderDt || !!item.paidDt) ? (\r\n                  <a onClick={() => orderNoClick(item)}>\r\n                    {orderNoFormat(item)}\r\n                  </a>\r\n                ) : (\r\n                  <div>{orderNoFormat(item)}</div>\r\n                )\r\n              }\r\n            />\r\n            <ColumnGroup title={\"购买前\"} className=\"top-header-a\">\r\n              <Column\r\n                title={historyItem?.productId == eProductId.Pid_13_Cdisk ? \"下载流量\" : \"授权数\"}\r\n                dataIndex={\"authCntBefore\"}\r\n                key={\"authCntBefore\"}\r\n                className=\"top-header-a\"\r\n                render={(authCntBefore) =>\r\n                  historyItem?.productId == eProductId.Pid_13_Cdisk ? (\r\n                    <div>{authCntBefore}G</div>\r\n                  ) : authCntBefore > 0 ? (\r\n                    <div>{authCntBefore}人</div>\r\n                  ) : (\r\n                    <div>-</div>\r\n                  )\r\n                }\r\n              />\r\n              <Column\r\n                title={\"有效期至\"}\r\n                dataIndex={\"expirationDtBefore\"}\r\n                key={\"expirationDtBefore\"}\r\n                className=\"top-header-a\"\r\n                render={(expirationDtBefore) => (\r\n                  <div>\r\n                    {!!expirationDtBefore\r\n                      ? moment(expirationDtBefore).format(\"YYYY-MM-DD\")\r\n                      : \"-\"}\r\n                  </div>\r\n                )}\r\n              />\r\n            </ColumnGroup>\r\n            <ColumnGroup title={\"规格选择\"} className=\"top-header-b\">\r\n              <Column\r\n                title={historyItem?.productId == eProductId.Pid_13_Cdisk ? \"下载流量\" : \"增/减员数\"}\r\n                dataIndex={\"adjustAuthCnt\"}\r\n                key={\"adjustAuthCnt\"}\r\n                className=\"top-header-b\"\r\n                render={(adjustAuthCnt) =>\r\n                  historyItem?.productId == eProductId.Pid_13_Cdisk ? (\r\n                    <div>{adjustAuthCnt}G</div>\r\n                  ) : (\r\n                    <div>\r\n                      {adjustAuthCnt < 0 ? \"\" : \"+\"}\r\n                      {adjustAuthCnt || 0}人\r\n                    </div>\r\n                  )\r\n                }\r\n              />\r\n              <Column\r\n                title={\"购买时长\"}\r\n                dataIndex={\"adjustMonthCnt\"}\r\n                key={\"adjustMonthCnt\"}\r\n                className=\"top-header-b\"\r\n                render={(adjustMonthCnt) => (\r\n                  <div>{addMonthCntFormat(adjustMonthCnt)}</div>\r\n                )}\r\n              />\r\n            </ColumnGroup>\r\n            <ColumnGroup title={\"购买结果\"} className=\"top-header-c\">\r\n              <Column\r\n                title={historyItem?.productId == eProductId.Pid_13_Cdisk ? \"下载流量\" : \"授权数\"}\r\n                dataIndex={\"authCnt\"}\r\n                key={\"authCnt\"}\r\n                className=\"top-header-c\"\r\n                render={(authCnt) =>\r\n                  historyItem?.productId == eProductId.Pid_13_Cdisk ? (\r\n                    <div>{authCnt}G</div>\r\n                  ) : (\r\n                    <div>{authCnt}人</div>\r\n                  )\r\n                }\r\n              />\r\n              <Column\r\n                title={\"有效期至\"}\r\n                dataIndex={\"expirationDt\"}\r\n                key={\"expirationDt\"}\r\n                className=\"top-header-c\"\r\n                render={(expirationDt) => (\r\n                  <div>\r\n                    {!!expirationDt\r\n                      ? moment(expirationDt).format(\"YYYY-MM-DD\")\r\n                      : \"-\"}\r\n                  </div>\r\n                )}\r\n              />\r\n            </ColumnGroup>\r\n            <Column\r\n              title={\"下单人\"}\r\n              dataIndex={\"creatorName\"}\r\n              key={\"creatorName\"}\r\n              render={(creatorName) => (\r\n                <div>{!!creatorName ? creatorName : \"-\"}</div>\r\n              )}\r\n            />\r\n            <Column\r\n              title={\"下单时间\"}\r\n              dataIndex={\"orderDt\"}\r\n              key={\"orderDt\"}\r\n              render={(orderDt) => (\r\n                <div>\r\n                  {!!orderDt\r\n                    ? moment(orderDt).format(\"YYYY-MM-DD HH:mm:ss\")\r\n                    : \"-\"}\r\n                </div>\r\n              )}\r\n              sorter={sort}\r\n              defaultSortOrder={\"descend\"}\r\n            />\r\n            <Column\r\n              title={\"状态\"}\r\n              dataIndex={\"status\"}\r\n              key={\"status\"}\r\n              render={(status, item) => <div>{status}</div>}\r\n            />\r\n          </Table>\r\n        </Skeleton>\r\n      </DraggablePopUp>\r\n      <DraggablePopUp\r\n        className=\"tms-modal\"\r\n        title=\"应用过期提示\"\r\n        open={expireVisible && !!expireItem}\r\n        centered\r\n        width={400}\r\n        maskClosable={false}\r\n        onCancel={() => expireVisibleChange(false, null)}\r\n        footer={\r\n          <div style={{ display: \"flex\", justifyContent: \"center\" }}>\r\n            <Button\r\n              style={{ borderRadius: 5 }}\r\n              onClick={() => expireVisibleChange(false, null)}\r\n            >\r\n              我知道了\r\n            </Button>\r\n            <Button\r\n              type={\"primary\"}\r\n              style={{ borderRadius: 5 }}\r\n              onClick={() => {\r\n                expireItem?.productId == eProductId.Pid_13_Cdisk\r\n                  ? setCloudBuyVisible(true)\r\n                  : setCreateTeamModalVisible(true);\r\n                expireVisibleChange(false, null);\r\n              }}\r\n            >\r\n              续费\r\n            </Button>\r\n          </div>\r\n        }\r\n      >\r\n        <div style={{ textAlign: \"center\" }}>\r\n          应用({expireItem?.productName})有效期至{timeFormat(expireItem)}\r\n          ，已过期\r\n        </div>\r\n      </DraggablePopUp>\r\n    </React.Fragment>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAO,KAAKC,YAAY,MAAM,kBAAkB;AAChD,SAASC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACvE,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,eAAe,IACpBC,kBAAkB,QACb,wBAAwB;AAC/B,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,OAAOC,UAAU,MAAM,gDAAgD;AACvE,OAAOC,YAAY,MAAM,kDAAkD;AAC3E,SAASC,YAAY,EAAEC,eAAe,EAAEC,UAAU,EAAEC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/F,MAAM;EAAEC,MAAM;EAAEC;AAAY,CAAC,GAAGvB,KAAK;AAErC,eAAe,SAASwB,gBAAgBA,CAAC;EAAEC,MAAM,EAAEC,WAAW;EAAEC,SAAS,EAAEC,cAAc;EAAEC,OAAO,EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAClH;EACA,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,YAAY,GAAGrC,SAAS,CAAC,CAAC,CAAC6B,MAAM;EACvC,MAAMA,MAAM,GAAGC,WAAW,IAAIO,YAAY,CAAC,CAAC;EAC5C,MAAMC,eAAe,GAAGtC,SAAS,CAAC,CAAC,CAAC+B,SAAS;EAC7C,MAAMQ,OAAO,GAAGP,cAAc,IAAIM,eAAe,CAAC,CAAC;EACnD,MAAM,CAACE,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACiD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACuD,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnF,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqE,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgC,SAAS,EAAE2C,YAAY,CAAC,GAAG3E,QAAQ,CAACwC,OAAO,CAAC;EACnD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC8E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnE,MAAM,CAACgF,aAAa,EAAEC,gBAAgB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE1DD,SAAS,CAAC,MAAM;IACdmF,mBAAmB,CAAC,CAAC;IACrBC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAENpF,SAAS,CAAC,MAAM;IACdqF,gBAAgB,CAAC,CAAC;IAClBC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,gBAAgB,CAAC;IACnD,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEH,gBAAgB,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,IAAI/B,UAAU,GAAGmC,QAAQ,CAACC,eAAe,CAACC,YAAY,GAAG,GAAG;IAC5DpC,aAAa,CAACD,UAAU,CAAC;EAC3B,CAAC;;EAED;EACAtD,SAAS,CAAC,MAAM;IACd,IAAGiC,SAAS,IAAIe,aAAa,CAAC4C,MAAM,GAAG,CAAC,KAAMxD,YAAY,IAAI,CAACI,eAAe,IAAM,CAACJ,YAAY,IAAII,eAAgB,CAAC,EAAE;MACtH,IAAIqD,IAAI,GAAG7C,aAAa,CAAC8C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9D,SAAS,IAAIA,SAAS,CAAC;MAC9D,IAAGO,eAAe,EAAE;QAAE;QACpB,MAAMwD,MAAM,GAAG,MAAMjE,MAAM,mBAAmB,CAAC,CAAC;QAChDuD,MAAM,CAACW,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,MAAM,CAAC;QAC3CpB,YAAY,CAAC,IAAI,CAAC;MACpB;MACA,IAAGiB,IAAI,CAACD,MAAM,GAAG,CAAC;QAAG;QACnBO,oBAAoB,CAACN,IAAI,CAAC,CAAC,CAAC,CAAC;IACjC;EACF,CAAC,EAAC,CAACpD,OAAO,EAACO,aAAa,EAACZ,YAAY,CAAC,CAAC;;EAEvC;EACA,eAAe+C,mBAAmBA,CAAA,EAAG;IACnCxC,kBAAkB,CAAC,IAAI,CAAC;IACxB,MAAMvC,YAAY,CACfgG,8BAA8B,CAAC;MAAErE,MAAM,EAAEA;IAAO,CAAC,CAAC,CAClDsE,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAE;QACzBC,iBAAiB,CAACF,GAAG,CAACG,WAAW,IAAI,EAAE,CAAC;MAC1C;IACF,CAAC,CAAC;IACJ9D,kBAAkB,CAAC,KAAK,CAAC;EAC3B;;EAEA;EACA,eAAeyC,YAAYA,CAAA,EAAG;IAC5B,IAAI;MACF,MAAMkB,GAAG,GAAG,MAAMlG,YAAY,CAACsG,+BAA+B,CAAC;QAC7D3E,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAIuE,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1B,MAAMI,KAAK,GAAGL,GAAG,CAACM,QAAQ,IAAI,EAAE;QAChC9B,WAAW,CAAC6B,KAAK,CAAC;MACpB;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF;EAEA,SAASL,iBAAiBA,CAACC,WAAW,GAAG,EAAE,EAAE;IAC3C,IAAIM,SAAS,GAAG,EAAE;IAClBN,WAAW,CAACO,OAAO,CAAEC,OAAO,IAAK;MAC/B,IAAIpB,IAAI,GAAGkB,SAAS,CAACG,IAAI,CACtBC,QAAQ,IAAKF,OAAO,CAACG,OAAO,IAAID,QAAQ,CAACC,OAC5C,CAAC;MACD,IAAI,CAACvB,IAAI,EAAE;QACTkB,SAAS,CAACM,IAAI,CAAC;UACbC,SAAS,EAAEL,OAAO,CAACK,SAAS;UAC5BF,OAAO,EAAEH,OAAO,CAACG,OAAO;UACxBG,QAAQ,EAAE,CAACN,OAAO;QACpB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLpB,IAAI,CAAC0B,QAAQ,CAACF,IAAI,CAACJ,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC;IACFpE,YAAY,CAAC,CAAC,GAAGkE,SAAS,CAAC,CAAC;IAC5B,IAAIS,SAAS,GAAG,EAAE;IAClBT,SAAS,CAACC,OAAO,CAAES,KAAK,IAAK;MAC3B,IAAI7E,SAAS,GAAG6E,KAAK,CAACF,QAAQ,CAACG,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK;QAAA,IAAAC,mBAAA,EAAAC,oBAAA;QAAA,OAAM;UACpD,GAAGH,KAAK;UACRI,GAAG,EAAEJ,KAAK,CAAC1F,SAAS;UACpB+F,SAAS,EAAEJ,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK;UACpCK,eAAe,EAAEL,KAAK,IAAI,CAAC,GAAGH,KAAK,CAACF,QAAQ,CAAC3B,MAAM,GAAG,CAAC;UACvDsC,WAAW,EAAE,CAAC,CAACP,KAAK,CAACO,WAAW,GAC9BP,KAAK,CAACO,WAAW,GAAG,EAAAL,mBAAA,GAAA7E,aAAa,CAACkE,IAAI,CAAED,OAAO,IAAKA,OAAO,CAAChF,SAAS,IAAI0F,KAAK,CAAC1F,SAAS,CAAC,cAAA4F,mBAAA,uBAArEA,mBAAA,CAAuEK,WAAW,KAAI,EAAE;UAC9GC,UAAU,EAAE,CAAC,CAACR,KAAK,CAACQ,UAAU,GAC5BR,KAAK,CAACQ,UAAU,GAAG,EAAAL,oBAAA,GAAA9E,aAAa,CAACkE,IAAI,CAAED,OAAO,IAAKA,OAAO,CAAChF,SAAS,IAAI0F,KAAK,CAAC1F,SAAS,CAAC,cAAA6F,oBAAA,uBAArEA,oBAAA,CAAuEK,UAAU,KAAI;QAC5G,CAAC;MAAA,CAAC,CAAC;MACHX,SAAS,GAAGA,SAAS,CAACY,MAAM,CAACxF,SAAS,CAAC;IACzC,CAAC,CAAC;IAEFG,gBAAgB,CAAC,CAAC,GAAGyE,SAAS,CAAC,CAAC;IAChCvE,gBAAgB,CAAC,CAAC,GAAGuE,SAAS,CAAC,CAAC;EAClC;;EAEA;EACA,SAASa,SAASA,CAACpG,SAAS,EAAEqG,SAAS,EAAE;IACvClI,YAAY,CACTmI,uBAAuB,CAAC;MACvBxG,MAAM,EAAEA,MAAM;MACdE,SAAS,EAAEA,SAAS;MACpBqG,SAAS,EAAEA;IACb,CAAC,CAAC,CACDjC,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAE;QACzBC,iBAAiB,CAACF,GAAG,CAACG,WAAW,IAAI,EAAE,CAAC;MAC1C;IACF,CAAC,CAAC;EACN;EAEA,SAASN,oBAAoBA,CAACgB,QAAQ,EAAE;IAAG;IACzCxD,kBAAkB,CAACwD,QAAQ,CAAC;IAC5B1D,4BAA4B,CAAC,IAAI,CAAC;EACpC;EAEA,SAAS+E,SAASA,CAAA,EAAG;IACnBrD,mBAAmB,CAAC,CAAC;IACrBhC,yBAAyB,CAAC,KAAK,CAAC;IAChCoB,WAAW,CAAC,IAAI,CAAC;EACnB;;EAEA;EACA,SAASkE,UAAUA,CAAC5C,IAAI,EAAE;IACxB,IAAI6C,IAAI,GAAG7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,YAAY;IAC7B,OAAO,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,OAAO,KAAI,CAAC,GAAG,GAAG,GAC3BF,IAAI,GAAGA,IAAI,IAAI,qBAAqB,GAAG,GAAG,GACxC9H,MAAM,CAAC8H,IAAI,CAAC,CAACG,MAAM,CAAC,YAAY,CAAC,GACnC,GAAG;EACT;EAEA,SAASC,eAAeA,CAACjD,IAAI,EAAE;IAC7B,IACEA,IAAI,CAACyC,SAAS,IAAI,CAAC,IACnBzC,IAAI,CAACkD,UAAU,IAAIxH,cAAc,CAACyH,mBAAmB,CAAC;IAAA,EACtD;MACA,OAAO,SAAS;IAClB;IACA,OAAO,MAAM;EACf;EAEA,SAASC,gBAAgBA,CAACpD,IAAI,EAAEqD,SAAS,EAAE;IACzC,IACErD,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC6H,eAAe,IAAItD,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC8H,YAAY,IACzFvD,IAAI,CAAC+C,OAAO,IAAI,CAAC,IACjB,CAAC,CAAC/C,IAAI,CAAC8C,YAAY,IAAI/H,MAAM,CAAC,CAAC,CAACyI,OAAO,CAACzI,MAAM,CAACiF,IAAI,CAAC8C,YAAY,CAAC,CAAC,EAClE;MACA;MACA,IAAI9C,IAAI,CAACyC,SAAS,IAAI,CAAC,IAAIY,SAAS,IAAI,cAAc,EAAE;QACtD,OAAO,KAAK,CAAC,CAAC;MAChB;MACA,OAAO,SAAS;IAClB;IACA,OAAOJ,eAAe,CAACjD,IAAI,CAAC;EAC9B;EAEA,SAASyD,eAAeA,CAACzD,IAAI,EAAE;IAC7B,IACEA,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC6H,eAAe,IAAItD,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC8H,YAAY,IACzFvD,IAAI,CAACkD,UAAU,IAAIxH,cAAc,CAACyH,mBAAmB,EACrD;MACA,oBAAOvH,OAAA;QAAA8H,QAAA,EAAK;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBACElI,OAAA,CAAAE,SAAA;MAAA4H,QAAA,gBAEE9H,OAAA;QAAKmI,KAAK,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAP,QAAA,EAC5C1D,IAAI,CAAC+C,OAAO,IAAI,CAAC,IAClB,CAAC,CAAC/C,IAAI,CAAC8C,YAAY,IACnB/H,MAAM,CAAC,CAAC,CAACyI,OAAO,CAACzI,MAAM,CAACiF,IAAI,CAAC8C,YAAY,CAAC,CAAC;QAAA;QAAK;QAC9ClH,OAAA;UACEsI,OAAO,EAAEA,CAAA,KACPlE,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,GACrC7G,yBAAyB,CAAC,IAAI,CAAC,GAC/BkB,kBAAkB,CAAC,IAAI,CAC5B;UACDuF,KAAK,EACH/D,IAAI,CAACyC,SAAS,IAAI,CAAC,GAEjB;YAAE2B,KAAK,EAAE,MAAM;YAAEC,eAAe,EAAE,KAAK;YAAEC,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAW,CAAC,GAE/E;YAAEH,KAAK,EAAE,MAAM;YAAEC,eAAe,EAAE,MAAM;YAAEC,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAW,CAClF;UAAAb,QAAA,EACF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEJlI,OAAA;UACEsI,OAAO,EAAEA,CAAA,KACPlE,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,GACvC7G,yBAAyB,CAAC,IAAI,CAAC,GAAGkB,kBAAkB,CAAC,IAAI,CAC5D;UAAAkF,QAAA,EACF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlI,OAAA;QAAKmI,KAAK,EAAE;UAAES,MAAM,EAAE;QAAU,CAAE;QAAAd,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1ClI,OAAA;QACEmI,KAAK,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAChER,OAAO,EAAEA,CAAA,KAAM;UACblG,iBAAiB,CAAC,IAAI,CAAC;UACvB2G,cAAc,CAAC3E,IAAI,CAAC;UACpB1B,cAAc,CAAC0B,IAAI,CAAC;QACtB,CAAE;QAAA0D,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJlI,OAAA;QAAKmI,KAAK,EAAE;UAAES,MAAM,EAAE;QAAU,CAAE;QAAAd,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAE1ClI,OAAA;QAAKmI,KAAK,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAhB,QAAA,eACnE9H,OAAA,CAACpB,MAAM;UACLoK,IAAI,EAAC,OAAO;UACZC,QAAQ;UACN;UACA7E,IAAI,CAACkD,UAAU,IAAIxH,cAAc,CAACyH,mBACnC;UACD2B,eAAe,EAAC,cAAI;UACpBC,iBAAiB,EAAC,cAAI;UACtBC,OAAO,EAAEhF,IAAI,CAACyC,SAAS,IAAI,CAAE;UAC7BwC,QAAQ,EAAEA,CAAA,KACRjF,IAAI,CAACyC,SAAS,IAAI,CAAC,GAEjBD,SAAS,CAACxC,IAAI,CAAC5D,SAAS,EAAE,CAAC,CAAC,GAE5BoG,SAAS,CAACxC,IAAI,CAAC5D,SAAS,EAAE,CAAC;QAC9B;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACN,CAAC;EAEP;;EAEA;EACA,eAAea,cAAcA,CAAC3E,IAAI,EAAE;IAClC9B,iBAAiB,CAAC,IAAI,CAAC;IACvB,MAAM3D,YAAY,CACf2K,qCAAqC,CAAC;MACrChJ,MAAM;MACNE,SAAS,EAAE4D,IAAI,CAAC5D;IAClB,CAAC,CAAC,CACDoE,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAE;QACzB,IAAIyE,IAAI,GAAG,CAAC1E,GAAG,CAAC2E,SAAS,IAAI,EAAE,EAAEvD,GAAG,CAAEzB,OAAO,IAAK;UAChDA,OAAO,CAAC8B,GAAG,GAAG9B,OAAO,CAACiF,EAAE;UACxBjF,OAAO,CAACkF,MAAM,GAAGlF,OAAO,CAAC8C,UAAU,IAAI3H,YAAY,CAACgK,aAAa,GAAG,KAAK,GAAG,KAAK;UACjF,OAAOnF,OAAO;QAChB,CAAC,CAAC;QACFhC,cAAc,CAAC,CAAC,GAAG+G,IAAI,CAAC,CAAC;MAC3B;IACF,CAAC,CAAC;IACJjH,iBAAiB,CAAC,KAAK,CAAC;EAC1B;EAEA,SAASsH,mBAAmBA,CAAClJ,OAAO,EAAE0D,IAAI,EAAE;IAC1CpB,gBAAgB,CAACtC,OAAO,CAAC;IACzBwC,aAAa,CAACkB,IAAI,CAAC;EACrB;EAEA,MAAMyF,OAAO,GAAG,CACd;IACEC,KAAK,eACH9J,OAAA;MAAKmI,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAhB,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACnE;IACDT,SAAS,EAAE,WAAW;IACtBnB,GAAG,EAAE,WAAW;IAChByD,MAAM,EAAEA,CAAClE,SAAS,EAAEzB,IAAI,kBACtBpE,OAAA;MAAKmI,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAhB,QAAA,EACvDjC;IAAS;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACN;IACD8B,MAAM,EAAG5F,IAAI,IAAK;MAChB,IAAIA,IAAI,CAACmC,SAAS,EAAE;QAClB,OAAO;UAAE0D,OAAO,EAAE7F,IAAI,CAACoC;QAAgB,CAAC;MAC1C,CAAC,MAAM;QACL,OAAO;UAAEyD,OAAO,EAAE;QAAE,CAAC;MACvB;IACF;EACF,CAAC,EACD;IACEH,KAAK,eACH9J,OAAA;MAAKmI,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAhB,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACnE;IACDT,SAAS,EAAE,aAAa;IACxBnB,GAAG,EAAE,aAAa;IAClByD,MAAM,EAAEA,CAACG,WAAW,EAAE9F,IAAI,kBACxBpE,OAAA;MACEmI,KAAK,EAAE;QACLU,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BqB,UAAU,EAAE,QAAQ;QACpB3B,KAAK,EAAEnB,eAAe,CAACjD,IAAI;MAC7B,CAAE;MAAA0D,QAAA,GAEDoC,WAAW,EACX9F,IAAI,CAACkD,UAAU,IAAIxH,cAAc,CAACsK,WAAW,iBAC5CpK,OAAA;QAAMmI,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAE6B,QAAQ,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAG,CAAE;QAAAxC,QAAA,EAAC;MAEjE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP,EACA9D,IAAI,CAACkD,UAAU,IAAIxH,cAAc,CAACyH,mBAAmB,iBACpDvH,OAAA;QAAMmI,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAE6B,QAAQ,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAG,CAAE;QAAAxC,QAAA,EAAC;MAEjE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;IACD8B,MAAM,EAAG5F,IAAI,IAAK;MAChB,OAAO;QAAE6F,OAAO,EAAE;MAAE,CAAC;IACvB;EACF,CAAC,EACD;IACEH,KAAK,eACH9J,OAAA;MAAKmI,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAhB,QAAA,EAAC;IAE3D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;IACDT,SAAS,EAAE,cAAc;IACzBnB,GAAG,EAAE,cAAc;IACnByD,MAAM,EAAEA,CAAC7C,YAAY,EAAE9C,IAAI,KAAK;MAC9B,IACEA,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC6H,eAAe,IAC5CtD,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC8H,YAAY,IACzCvD,IAAI,CAAC+C,OAAO,IAAI,CAAC,IACjB,CAAC,CAAC/C,IAAI,CAAC8C,YAAY,IACnB/H,MAAM,CAAC,CAAC,CAACyI,OAAO,CAACzI,MAAM,CAACiF,IAAI,CAAC8C,YAAY,CAAC,CAAC,EAC3C;QACA,oBACElH,OAAA;UACEmI,KAAK,EAAE;YACLU,OAAO,EAAE,MAAM;YACfsB,UAAU,EAAE,QAAQ;YACpBrB,cAAc,EAAE;UAClB,CAAE;UACFgB,KAAK,EAAE,MAAM1F,IAAI,CAAC8F,WAAW,aAAc;UAAApC,QAAA,eAE3C9H,OAAA;YACEsI,OAAO,EAAEA,CAAA,KAAM;cACblE,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,GACrC3F,kBAAkB,CAAC,IAAI,CAAC,GACxBlB,yBAAyB,CAAC,IAAI,CAAC;YACrC,CAAE;YACFyG,KAAK,EAAE;cAAEK,KAAK,EAAEhB,gBAAgB,CAACpD,IAAI,EAAE,cAAc;YAAE,CAAE;YAAA0D,QAAA,EAExDd,UAAU,CAAC5C,IAAI;UAAC;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAEV;MACA,oBACElI,OAAA;QACEmI,KAAK,EAAE;UACLU,OAAO,EAAE,MAAM;UACfsB,UAAU,EAAE,QAAQ;UACpBrB,cAAc,EAAE;QAClB,CAAE;QAAAhB,QAAA,eAEF9H,OAAA;UAAKmI,KAAK,EAAE;YAAEK,KAAK,EAAEhB,gBAAgB,CAACpD,IAAI;UAAE,CAAE;UAAA0D,QAAA,EAC3Cd,UAAU,CAAC5C,IAAI;QAAC;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV,CAAC;IACD8B,MAAM,EAAG5F,IAAI,IAAK;MAChB,OAAO;QAAE6F,OAAO,EAAE;MAAE,CAAC;IACvB;EACF,CAAC,EACD;IACEH,KAAK,eACH9J,OAAA;MAAKmI,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAhB,QAAA,EAAC;IAE3D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;IACDT,SAAS,EAAE,SAAS;IACpBnB,GAAG,EAAE,SAAS;IACdyD,MAAM,EAAEA,CAAC5C,OAAO,EAAE/C,IAAI,kBACpBpE,OAAA;MACEmI,KAAK,EAAE;QACLU,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBN,KAAK,EAAEnB,eAAe,CAACjD,IAAI;MAC7B,CAAE;MAAA0D,QAAA,EAED1D,IAAI,CAACuB,OAAO,IAAI/F,eAAe,CAAC2K,SAAS,IAAInG,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,GACrF,GAAG,GACDpB,OAAO,IAAI,CAAC,GACd,CAAC,CAAC/C,IAAI,CAAC8C,YAAY,IACnB/H,MAAM,CAAC,CAAC,CAACyI,OAAO,CAACzI,MAAM,CAACiF,IAAI,CAAC8C,YAAY,CAAC,CAAC,gBACzClH,OAAA;QACEmI,KAAK,EAAE;UAAEK,KAAK,EAAE;QAAU,CAAE;QAC5BF,OAAO,EAAEA,CAAA,KACPlE,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,GACrC7G,yBAAyB,CAAC,IAAI,CAAC,GAC/BkB,kBAAkB,CAAC,IAAI,CAC5B;QAAAkF,QAAA,eAED9H,OAAA;UAAKmI,KAAK,EAAE;YAAEqC,MAAM,EAAE;UAAG,CAAE;UAACC,GAAG,EAAE/K;QAAa;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,gBAEJlI,OAAA;QAAKmI,KAAK,EAAE;UAAEqC,MAAM,EAAE;QAAG,CAAE;QAACC,GAAG,EAAEhL;MAAW;QAAAsI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC/C,GAED;IACD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;IACD8B,MAAM,EAAG5F,IAAI,IAAK;MAChB,OAAO;QAAE6F,OAAO,EAAE;MAAE,CAAC;IACvB;EACF,CAAC,EACD;IACEH,KAAK,eACH9J,OAAA,CAACf,OAAO;MACNyL,IAAI,EAAE7H,QAAS;MACf8H,SAAS,EAAC,QAAQ;MAClBC,OAAO,EAAE,OAAQ;MACjBd,KAAK,eACH9J,OAAA;QACEmI,KAAK,EAAE;UACLU,OAAO,EAAE,MAAM;UACfsB,UAAU,EAAE,QAAQ;UACpBrB,cAAc,EAAE;QAClB,CAAE;QAAAhB,QAAA,gBAEF9H,OAAA;UAAMmI,KAAK,EAAE;YAAE0C,UAAU,EAAE,MAAM;YAAER,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5DlI,OAAA;UACEmI,KAAK,EAAE;YAAEmC,UAAU,EAAE,EAAE;YAAE9B,KAAK,EAAE;UAAO,CAAE;UACzCF,OAAO,EAAEA,CAAA,KAAMxF,WAAW,CAAC,KAAK,CAAE;UAAAgF,QAAA,eAElC9H,OAAA;YAAM8K,SAAS,EAAC,iBAAiB;YAAC3C,KAAK,EAAE;cAAEkC,QAAQ,EAAE;YAAG;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;MACD6C,OAAO,eACL/K,OAAA;QAAKmI,KAAK,EAAE;UAAEE,SAAS,EAAE;QAAS,CAAE;QAAAP,QAAA,gBAClC9H,OAAA;UAAA8H,QAAA,EAAK;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9BlI,OAAA;UAAA8H,QAAA,EAAK;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpClI,OAAA;UAAA8H,QAAA,EAAK;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtBlI,OAAA;UAAKmI,KAAK,EAAE;YAAE6C,SAAS,EAAE;UAAG,CAAE;UAAAlD,QAAA,eAC5B9H,OAAA,CAAChB,MAAM;YACLiM,IAAI,EAAE,SAAU;YAChB9C,KAAK,EAAE;cAAEO,YAAY,EAAE;YAAE,CAAE;YAC3BJ,OAAO,EAAEA,CAAA,KAAMxF,WAAW,CAAC,KAAK,CAAE;YAAAgF,QAAA,EACnC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;MAAAJ,QAAA,eAED9H,OAAA;QAAKmI,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAhB,QAAA,EAAC;MAE3D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACV;IACDT,SAAS,EAAE,aAAa;IACxBnB,GAAG,EAAE,aAAa;IAClByD,MAAM,EAAEA,CAACtD,WAAW,EAAEf,QAAQ,KAAK;MACjC,oBACE1F,OAAA;QAAKmI,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEsB,UAAU,EAAE;QAAS,CAAE;QAAArC,QAAA,gBACpD9H,OAAA;UAAKmI,KAAK,EAAE;YAAE+C,IAAI,EAAE;UAAI;QAAE;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7BlI,OAAA;UAAKmI,KAAK,EAAE;YAAE+C,IAAI,EAAE,CAAC;YAAErC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAhB,QAAA,EAChEpC,QAAQ,CAACC,OAAO,IAAI/F,eAAe,CAAC2K,SAAS,gBAC5CvK,OAAA;YACEsI,OAAO,EAAEA,CAAA,KAAM;cACb,CAAC,CAAC5C,QAAQ,CAACwB,YAAY,IACvB/H,MAAM,CAAC,CAAC,CAACyI,OAAO,CAACzI,MAAM,CAACuG,QAAQ,CAACwB,YAAY,CAAC,CAAC,GAC3C0C,mBAAmB,CAAC,IAAI,EAAElE,QAAQ,CAAC,GACnChB,oBAAoB,CAACgB,QAAQ,CAAC;YACpC,CAAE;YAAAoC,QAAA,EAGDrB,WAAW,IAAI;UAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAEJlI,OAAA;YAAA8H,QAAA,EAAK;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNlI,OAAA;UAAKmI,KAAK,EAAE;YAAE+C,IAAI,EAAE,GAAG;YAAErC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAM,CAAE;UAAAhB,QAAA,EAC/DpC,QAAQ,CAACC,OAAO,IAAI/F,eAAe,CAAC2K,SAAS,IAC9C7E,QAAQ,CAACyB,OAAO,IAAI,CAAC,IACrB,CAAC,CAACzB,QAAQ,CAACwB,YAAY,IACvB/H,MAAM,CAAC,CAAC,CAACyI,OAAO,CAACzI,MAAM,CAACuG,QAAQ,CAACwB,YAAY,CAAC,CAAC,IAC/CxB,QAAQ,CAACyF,WAAW,GAAGzF,QAAQ,CAAC0F,OAAO,gBACrCpL,OAAA;YACEmI,KAAK,EACHzC,QAAQ,CAACmB,SAAS,IAAI,CAAC,GAAG;cAAE2B,KAAK,EAAE;YAAM,CAAC,GAAG;cAAEA,KAAK,EAAE;YAAO,CAC9D;YACDF,OAAO,EAAEA,CAAA,KAAMsB,mBAAmB,CAAC,IAAI,EAAElE,QAAQ,CAAE;YACnDoF,SAAS,EAAC,qBAAqB;YAC/BhB,KAAK,EAAE,MAAMpE,QAAQ,CAACwE,WAAW;UAA+B;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,GACAxC,QAAQ,CAAClF,SAAS,IAAIX,UAAU,CAAC6H,eAAe,IAClDhC,QAAQ,CAAClF,SAAS,IAAIX,UAAU,CAAC8H,YAAY,IAC7CjC,QAAQ,CAACyB,OAAO,IAAI,CAAC,IACrB,CAAC,CAACzB,QAAQ,CAACwB,YAAY,IACvB/H,MAAM,CAAC,CAAC,CAACyI,OAAO,CAACzI,MAAM,CAACuG,QAAQ,CAACwB,YAAY,CAAC,CAAC,gBAC/ClH,OAAA;YACEmI,KAAK,EACHzC,QAAQ,CAACmB,SAAS,IAAI,CAAC,GAAG;cAAE2B,KAAK,EAAE;YAAM,CAAC,GAAG;cAAEA,KAAK,EAAE;YAAO,CAC9D;YACDF,OAAO,EAAEA,CAAA,KAAMsB,mBAAmB,CAAC,IAAI,EAAElE,QAAQ,CAAE;YACnDoF,SAAS,EAAC,qBAAqB;YAC/BhB,KAAK,EAAE,MAAMpE,QAAQ,CAACwE,WAAW;UAAc;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,GACAxC,QAAQ,CAACyF,WAAW,GAAGzF,QAAQ,CAAC0F,OAAO,IACzC1F,QAAQ,CAACyB,OAAO,IAAI,CAAC,IACrBzB,QAAQ,CAACC,OAAO,IAAI/F,eAAe,CAAC2K,SAAS,gBAC7CvK,OAAA;YACEmI,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAC5BF,OAAO,EAAEA,CAAA,KAAM5D,oBAAoB,CAACgB,QAAQ,CAAE;YAC9CoE,KAAK,EAAE,iBAAkB;YACzBgB,SAAS,EAAC;UAAqB;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,GACA;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV,CAAC;IACD8B,MAAM,EAAG5F,IAAI,IAAK;MAChB,OAAO;QAAE6F,OAAO,EAAE;MAAE,CAAC;IACvB;EACF,CAAC,EACD;IACEH,KAAK,eACH9J,OAAA;MAAKmI,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAhB,QAAA,EAAC;IAE3D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;IACDT,SAAS,EAAE,YAAY;IACvBnB,GAAG,EAAE,YAAY;IACjByD,MAAM,EAAEA,CAACrD,UAAU,EAAEtC,IAAI,KAAK;MAC5B,oBACEpE,OAAA;QACEmI,KAAK,EAAE;UACLU,OAAO,EAAE,MAAM;UACfsB,UAAU,EAAE,QAAQ;UACpBrB,cAAc,EAAE;QAClB,CAAE;QAAAhB,QAAA,gBAEF9H,OAAA;UACE8J,KAAK,EACF1F,IAAI,CAAC+C,OAAO,IAAI,CAAC,IAAI/C,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,GAC1D7B,UAAU,IAAI,GAAG,GAChB,EAAE,GACFA,UAAU,GACZtC,IAAI,CAACiH,YACV;UACDP,SAAS,EAAC,mBAAmB;UAC7B3C,KAAK,EAAE;YAAEK,KAAK,EAAEhB,gBAAgB,CAACpD,IAAI;UAAE,CAAE;UAAA0D,QAAA,EAExCwD,UAAU,CAAElH,IAAI,CAAC+C,OAAO,IAAI,CAAC,IAAI/C,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,GACpE7B,UAAU,IAAI,GAAG,GAChB,EAAE,GACFA,UAAU,GACZtC,IAAI,CAACiH,YAAY;QAAC;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,EACL9D,IAAI,CAACmH,gBAAgB,IAAI,CAAC,KAC1BnH,IAAI,CAAC+C,OAAO,IAAI,CAAC,IAAI/C,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,CAAC,gBAC9DvI,OAAA;UACEmI,KAAK,EAAE;YAAEK,KAAK,EAAE;UAAU,CAAE;UAC5BF,OAAO,EAAEA,CAAA,KACPlE,IAAI,CAAC5D,SAAS,IAAIX,UAAU,CAAC0I,YAAY,GACrC7G,yBAAyB,CAAC,IAAI,CAAC,GAC/BkB,kBAAkB,CAAC,IAAI,CAC5B;UACDkH,KAAK,EAAE,MAAM1F,IAAI,CAAC8F,WAAW,mBAAoB;UACjDY,SAAS,EAAC;QAAqB;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,GACA,IAAI;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEV,CAAC;IACD8B,MAAM,EAAG5F,IAAI,IAAK;MAChB,OAAO;QAAE6F,OAAO,EAAE;MAAE,CAAC;IACvB;EACF,CAAC,EACD;IACEH,KAAK,eACH9J,OAAA;MAAKmI,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAhB,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACnE;IACDT,SAAS,EAAE,WAAW;IACtBnB,GAAG,EAAE,WAAW;IAChB8B,KAAK,EAAE,GAAG;IACV2B,MAAM,EAAEA,CAACyB,SAAS,EAAEpH,IAAI,kBACtBpE,OAAA;MACEmI,KAAK,EAAE;QACLU,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBqB,UAAU,EAAE;MACd,CAAE;MAAArC,QAAA,EAEDD,eAAe,CAACzD,IAAI;IAAC;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACN;IACD8B,MAAM,EAAG5F,IAAI,IAAK;MAChB,OAAO;QAAE6F,OAAO,EAAE;MAAE,CAAC;IACvB;EACF,CAAC,CACF;EAED,SAASqB,UAAUA,CAACG,GAAG,GAAG,EAAE,EAAE;IAC5B,IAAIC,UAAU,GAAGD,GAAG,CAACE,KAAK,CAAC,EAAE,CAAC;IAC9B,IAAIC,KAAK,GAAGF,UAAU,CAACrH,MAAM,CAAEwH,MAAM,IAAKA,MAAM,IAAI,GAAG,CAAC,CAAC1H,MAAM;IAC/D,IAAIyH,KAAK,IAAI,CAAC,EAAE;MACd,oBACE5L,OAAA;QAAA8H,QAAA,GACG2D,GAAG,CAACK,SAAS,CAAC,CAAC,EAAEL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC,CAAC,eACnC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtCuD,GAAG,CAACK,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEN,GAAG,CAACtH,MAAM,CAAC;MAAA;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAEX;IACA,IAAI0D,KAAK,IAAI,CAAC,EAAE;MACd,IAAII,KAAK,GAAGP,GAAG,CAACK,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEN,GAAG,CAACtH,MAAM,CAAC;MAC3D,oBACEnE,OAAA;QAAA8H,QAAA,GACG2D,GAAG,CAACK,SAAS,CAAC,CAAC,EAAEL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC,CAAC,eACnC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtC8D,KAAK,CAACF,SAAS,CAAC,CAAC,EAAEE,KAAK,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC,eACvC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtC8D,KAAK,CAACF,SAAS,CAACE,KAAK,CAACD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEC,KAAK,CAAC7H,MAAM,CAAC;MAAA;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAEX;IACA,IAAI0D,KAAK,IAAI,CAAC,EAAE;MACd,IAAII,KAAK,GAAGP,GAAG,CAACK,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEN,GAAG,CAACtH,MAAM,CAAC;MAC3D,IAAI8H,KAAK,GAAGD,KAAK,CAACF,SAAS,CAACE,KAAK,CAACD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEC,KAAK,CAAC7H,MAAM,CAAC;MACjE,oBACEnE,OAAA;QAAA8H,QAAA,GACG2D,GAAG,CAACK,SAAS,CAAC,CAAC,EAAEL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC,CAAC,eACnC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtC8D,KAAK,CAACF,SAAS,CAAC,CAAC,EAAEE,KAAK,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC,eACvC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtC+D,KAAK,CAACH,SAAS,CAAC,CAAC,EAAEG,KAAK,CAACF,OAAO,CAAC,GAAG,CAAC,CAAC,eACvC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtC+D,KAAK,CAACH,SAAS,CAACG,KAAK,CAACF,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEE,KAAK,CAAC9H,MAAM,CAAC;MAAA;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAEX;IACA,IAAI0D,KAAK,IAAI,CAAC,EAAE;MACd,IAAII,KAAK,GAAGP,GAAG,CAACK,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEN,GAAG,CAACtH,MAAM,CAAC;MAC3D,IAAI8H,KAAK,GAAGD,KAAK,CAACF,SAAS,CAACE,KAAK,CAACD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEC,KAAK,CAAC7H,MAAM,CAAC;MACjE,IAAI+H,KAAK,GAAGD,KAAK,CAACH,SAAS,CAACG,KAAK,CAACF,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEE,KAAK,CAAC9H,MAAM,CAAC;MACjE,oBACEnE,OAAA;QAAA8H,QAAA,GACG2D,GAAG,CAACK,SAAS,CAAC,CAAC,EAAEL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC,CAAC,eACnC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtC8D,KAAK,CAACF,SAAS,CAAC,CAAC,EAAEE,KAAK,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC,eACvC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtC+D,KAAK,CAACH,SAAS,CAAC,CAAC,EAAEG,KAAK,CAACF,OAAO,CAAC,GAAG,CAAC,CAAC,eACvC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtCgE,KAAK,CAACJ,SAAS,CAAC,CAAC,EAAEI,KAAK,CAACH,OAAO,CAAC,GAAG,CAAC,CAAC,eACvC/L,OAAA;UAAMmI,KAAK,EAAE;YAAEkC,QAAQ,EAAE;UAAG,CAAE;UAAAvC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtCgE,KAAK,CAACJ,SAAS,CAACI,KAAK,CAACH,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEG,KAAK,CAAC/H,MAAM,CAAC;MAAA;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAEX;IACA,OAAOuD,GAAG;EACZ;;EAEA;EACA,SAASU,0BAA0BA,CAACC,KAAK,EAAE;IACzCxK,YAAY,CAACwK,KAAK,CAAC;IACnB,IAAIC,UAAU,GACZD,KAAK,IAAI,CAAC,GACN7K,aAAa,GACbA,aAAa,CAAC8C,MAAM,CAAEmB,OAAO,IAAKA,OAAO,CAACG,OAAO,IAAIyG,KAAK,CAAC;IACjE9K,gBAAgB,CAAC,CAAC,GAAG+K,UAAU,CAAC,CAAC;EACnC;EAEA,SAASC,iBAAiBA,CAACC,WAAW,EAAE;IACtC,OAAOA,WAAW,IAAI,CAAC,GAAG,IAAI,GAC1BA,WAAW,IAAI,EAAE,GAAG,IAAI,GACxBA,WAAW,IAAI,EAAE,GAAG,IAAI,GACxBA,WAAW,IAAI,EAAE,GAAG,IAAI,GACxBA,WAAW,IAAI,CAAC,GAChB,GAAG,GACHA,WAAW,CAACC,QAAQ,CAAC,CAAC,GAAG,IAAI;EACnC;EAEA,SAASC,aAAaA,CAACrI,IAAI,EAAE;IAC3B,IAAI,CAAC,CAACA,IAAI,CAACsI,OAAO,EAAE;MAClB,OAAOvN,MAAM,CAACiF,IAAI,CAACsI,OAAO,CAAC,CAACtF,MAAM,CAAC,gBAAgB,CAAC;IACtD;IACA,IAAI,CAAC,CAAChD,IAAI,CAACuI,MAAM,EAAE;MACjB,OAAOxN,MAAM,CAACiF,IAAI,CAACuI,MAAM,CAAC,CAACvF,MAAM,CAAC,gBAAgB,CAAC;IACrD;IACA,OAAO,GAAG;EACZ;EAEA,SAASwF,YAAYA,CAACxI,IAAI,EAAE;IAC1BP,MAAM,CAAC6G,IAAI,CAAC7G,MAAM,CAACgJ,QAAQ,CAACC,MAAM,GAAG,uBAAuB1I,IAAI,CAAC2I,OAAO,EAAE,CAAC;EAC7E;EAEA,SAASC,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAClB,IAAIC,EAAE,GAAGV,aAAa,CAACQ,CAAC,CAAC;IACzB,IAAIG,EAAE,GAAGX,aAAa,CAACS,CAAC,CAAC;IACzB,IAAIC,EAAE,IAAI,GAAG,IAAIC,EAAE,IAAI,GAAG,EAAE;MAC1B,OAAOC,QAAQ,CAACF,EAAE,CAAC,GAAGE,QAAQ,CAACD,EAAE,CAAC;IACpC;IACA,IAAID,EAAE,IAAI,GAAG,IAAIC,EAAE,IAAI,GAAG,EAAE;MAC1B,OAAOC,QAAQ,CAACF,EAAE,CAAC,GAAG,CAAC;IACzB;IACA,IAAIA,EAAE,IAAI,GAAG,IAAIC,EAAE,IAAI,GAAG,EAAE;MAC1B,OAAO,CAAC,GAAGC,QAAQ,CAACD,EAAE,CAAC;IACzB;IACA,OAAO,CAAC;EACV;EAEA,SAASE,OAAOA,CAACC,OAAO,EAAE;IACxBvL,4BAA4B,CAAC,KAAK,CAAC,CAAC,CAAC;EACvC;EAEA,oBACEhC,OAAA,CAAC1B,KAAK,CAAC2B,QAAQ;IAAA6H,QAAA,gBACb9H,OAAA,CAACjB,QAAQ;MAACyO,OAAO,EAAEvM,eAAgB;MAAA6G,QAAA,eACjC9H,OAAA;QACEmI,KAAK,EAAE;UAAEQ,OAAO,EAAE,mBAAmB;UAAE0B,QAAQ,EAAE,EAAE;UAAEG,MAAM,EAAE;QAAO,CAAE;QACtEM,SAAS,EAAC,aAAa;QAAAhD,QAAA,gBAEvB9H,OAAA;UACEmI,KAAK,EAAE;YACLU,OAAO,EAAE,MAAM;YACfsB,UAAU,EAAE,QAAQ;YACpBrB,cAAc,EAAE;UAClB,CAAE;UAAAhB,QAAA,gBAEF9H,OAAA;YAAKmI,KAAK,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAEsB,UAAU,EAAE;YAAS,CAAE;YAAArC,QAAA,GAAC,gCAErD,eAAA9H,OAAA,CAAClB,MAAM;cACL2O,wBAAwB,EAAE,KAAM;cAChCrB,KAAK,EAAEzK,SAAU;cACjBwG,KAAK,EAAE;gBAAEmC,UAAU,EAAE;cAAG,CAAE;cAC1BjB,QAAQ,EAAE8C,0BAA2B;cAAArE,QAAA,gBAErC9H,OAAA,CAAClB,MAAM,CAAC4O,MAAM;gBAACtB,KAAK,EAAE,CAAE;gBAAAtE,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,EAC1C/G,SAAS,CAAC8E,GAAG,CAAED,KAAK,IAAK;gBACxB,oBACEhG,OAAA,CAAClB,MAAM,CAAC4O,MAAM;kBAACtB,KAAK,EAAEpG,KAAK,CAACL,OAAQ;kBAAAmC,QAAA,EACjC9B,KAAK,CAACH;gBAAS;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEpB,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACL,CAAC,CAAC3G,aAAa,CAACkE,IAAI,CAAED,OAAO,IAAKA,OAAO,CAAC2B,OAAO,IAAI,CAAC,CAAC,iBACtDnH,OAAA;YAAKmI,KAAK,EAAE;cAAEwF,YAAY,EAAE;YAAG,CAAE;YAAA7F,QAAA,eAC/B9H,OAAA;cACEsI,OAAO,EAAEA,CAAA,KAAM;gBACbzH,QAAQ,CAAC,mBAAmB,CAAC;cAC/B,CAAE;cAAAiH,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENlI,OAAA;UAAKmI,KAAK,EAAE;YAAEC,KAAK,EAAE,MAAM;YAAE4C,SAAS,EAAE;UAAG,CAAE;UAAAlD,QAAA,eAC3C9H,OAAA,CAACnB,KAAK;YACJmK,IAAI,EAAC,OAAO;YACZ8B,SAAS,EAAC,aAAa;YACvB8C,QAAQ;YACRzF,KAAK,EAAE;cAAEwF,YAAY,EAAE;YAAG,CAAE;YAC5BE,UAAU,EAAE,KAAM;YAClBC,MAAM,EAAE;cACN;cACAC,CAAC,EAAElM;YACL,CAAE;YACFgI,OAAO,EAAEA,OAAQ;YACjBmE,UAAU,EAAE3M;UAAc;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlI,OAAA;UAAKmI,KAAK,EAAE;YAAEqC,MAAM,EAAE;UAAG,CAAE;UAAA1C,QAAA,gBACzB9H,OAAA;YACEmI,KAAK,EAAE;cACL6C,SAAS,EAAE,EAAE;cACbR,MAAM,EAAE,EAAE;cACVH,QAAQ,EAAE,EAAE;cACZ7B,KAAK,EAAE,MAAM;cACbyF,UAAU,EAAE;YACd,CAAE;YAAAnG,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlI,OAAA;YACEmI,KAAK,EAAE;cACLmC,UAAU,EAAE,EAAE;cACdE,MAAM,EAAE,EAAE;cACVH,QAAQ,EAAE,EAAE;cACZ7B,KAAK,EAAE,MAAM;cACbyF,UAAU,EAAE;YACd,CAAE;YAAAnG,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlI,OAAA;YACEmI,KAAK,EAAE;cACLmC,UAAU,EAAE,EAAE;cACdE,MAAM,EAAE,EAAE;cACVH,QAAQ,EAAE,EAAE;cACZ7B,KAAK,EAAE,MAAM;cACbyF,UAAU,EAAE;YACd,CAAE;YAAAnG,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACXlI,OAAA,CAACZ,eAAe;MACdkB,MAAM,EAAEA,MAAO;MACf2K,IAAI,EAAE5L,kBAAmB;MACzBqB,OAAO,EAAEe,sBAAuB;MAChCyM,QAAQ,EAAEA,CAAA,KAAMxM,yBAAyB,CAAC,KAAK,CAAE;MACjDyM,IAAI,EAAEA,CAAA,KAAMpH,SAAS,CAAC,CAAE;MACxB/B,WAAW,EAAEzD;IAAc;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,EACD,CAAC,CAACvF,eAAe,iBAChB3C,OAAA,CAACR,gBAAgB;MACfkB,OAAO,EAAEiC,eAAgB;MACzBrC,MAAM,EAAEA,MAAO;MACf4N,QAAQ,EAAEA,CAAA,KAAMtL,kBAAkB,CAAC,KAAK,CAAE;MAC1CwL,KAAK,EAAEA,CAAA,KAAM;QACX1K,mBAAmB,CAAC,CAAC;QACrBd,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EACA,CAAC,CAACjG,eAAe,iBAChBjC,OAAA,CAACV,kBAAkB;MACjBgB,MAAM,EAAEA,MAAO;MACf2B,eAAe,EAAEA,eAAgB;MACjCmB,QAAQ,EAAEA,QAAS;MACnBrB,yBAAyB,EAAEA,yBAA0B;MACrDC,4BAA4B,EAAEA,4BAA6B;MAC3DqM,kBAAkB,EAAE3K,mBAAoB;MACxC4K,sBAAsB,EAAGC,MAAM,IAAK;QAClC;QACA9K,gBAAgB,CAAC8K,MAAM,CAAC;QACxBhL,oBAAoB,CAAC,IAAI,CAAC;QAC1BvB,4BAA4B,CAAC,KAAK,CAAC;MACrC;IAAE;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EACA,CAAC,CAAC1E,aAAa,iBACdxD,OAAA,CAACT,kBAAkB;MACjBe,MAAM,EAAEA,MAAO;MACfkD,aAAa,EAAEA,aAAc;MAC7BgL,YAAY,EAAEhL,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiL,eAAgB;MAC7CJ,kBAAkB,EAAE3K,mBAAoB;MACxCJ,iBAAiB,EAAEA,iBAAkB;MACrCC,oBAAoB,EAAEA,oBAAqB;MAC3CH,QAAQ,EAAEA,QAAS;MACnBsL,cAAc,EAAGH,MAAM,IAAK;QAC1B9K,gBAAgB,CAAC8K,MAAM,CAAC;MAC1B;IAAE;MAAAxG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,eACDlI,OAAA,CAACd,cAAc;MACb4L,SAAS,EAAC,WAAW;MACrBhB,KAAK,EAAE,QAAQrH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyH,WAAW,EAAG;MAC1CQ,IAAI,EAAEvI,cAAe;MACrBwM,QAAQ;MACRvG,KAAK,EAAE,IAAK;MACZ8F,QAAQ,EAAEA,CAAA,KAAM9L,iBAAiB,CAAC,KAAK,CAAE;MACzCwM,MAAM,eACJ5O,OAAA;QAAKmI,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAhB,QAAA,eACxD9H,OAAA,CAAChB,MAAM;UACLiM,IAAI,EAAE,SAAU;UAChB9C,KAAK,EAAE;YAAEO,YAAY,EAAE;UAAE,CAAE;UAC3BJ,OAAO,EAAEA,CAAA,KAAMlG,iBAAiB,CAAC,KAAK,CAAE;UAAA0F,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;MAAAJ,QAAA,eAED9H,OAAA,CAACjB,QAAQ;QAACyO,OAAO,EAAEnL,cAAe;QAAAyF,QAAA,eAChC9H,OAAA,CAACnB,KAAK;UACJmK,IAAI,EAAC,OAAO;UACZ8B,SAAS,EAAC,eAAe;UACzB8C,QAAQ;UACRI,UAAU,EAAEzL,WAAY;UACxBsM,iBAAiB,EAAE,KAAM;UACzBhB,UAAU,EAAE;YACViB,QAAQ,EAAE,CAAC,cAAc,CAAC;YAC1B9F,IAAI,EAAE,OAAO;YACb+F,QAAQ,EAAE,EAAE;YACZC,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE,KAAK;YACtBC,KAAK,EAAE3M,WAAW,CAAC4B,MAAM;YACzBgL,SAAS,EAAGD,KAAK,IAAK;cACpB,OAAO,IAAIA,KAAK,GAAG;YACrB;UACF,CAAE;UAAApH,QAAA,gBAEF9H,OAAA,CAACG,MAAM;YACL2J,KAAK,EAAE,GAAI;YACXrC,SAAS,EAAE,OAAQ;YAEnBsC,MAAM,EAAEA,CAACqF,KAAK,EAAEhL,IAAI,EAAE+B,KAAK,kBAAKnG,OAAA;cAAA8H,QAAA,EAAM3B,KAAK,GAAG;YAAC;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAE,GADlD,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CAAC,eACFlI,OAAA,CAACG,MAAM;YACL2J,KAAK,EAAE,KAAM;YACbrC,SAAS,EAAE,SAAU;YAErBsC,MAAM,EAAEA,CAACsF,OAAO,EAAEjL,IAAI,KACpBA,IAAI,CAACkL,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAClL,IAAI,CAACsI,OAAO,IAAI,CAAC,CAACtI,IAAI,CAACuI,MAAM,CAAC,gBACrD3M,OAAA;cAAGsI,OAAO,EAAEA,CAAA,KAAMsE,YAAY,CAACxI,IAAI,CAAE;cAAA0D,QAAA,EAClC2E,aAAa,CAACrI,IAAI;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,gBAEJlI,OAAA;cAAA8H,QAAA,EAAM2E,aAAa,CAACrI,IAAI;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAElC,GATI,SAAS;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUf,CAAC,eACFlI,OAAA,CAACI,WAAW;YAAC0J,KAAK,EAAE,KAAM;YAACgB,SAAS,EAAC,cAAc;YAAAhD,QAAA,gBACjD9H,OAAA,CAACG,MAAM;cACL2J,KAAK,EAAE,CAAArH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjC,SAAS,KAAIX,UAAU,CAAC0I,YAAY,GAAG,MAAM,GAAG,KAAM;cAC1Ed,SAAS,EAAE,eAAgB;cAE3BqD,SAAS,EAAC,cAAc;cACxBf,MAAM,EAAGwF,aAAa,IACpB,CAAA9M,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjC,SAAS,KAAIX,UAAU,CAAC0I,YAAY,gBAC/CvI,OAAA;gBAAA8H,QAAA,GAAMyH,aAAa,EAAC,GAAC;cAAA;gBAAAxH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GACzBqH,aAAa,GAAG,CAAC,gBACnBvP,OAAA;gBAAA8H,QAAA,GAAMyH,aAAa,EAAC,QAAC;cAAA;gBAAAxH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAE3BlI,OAAA;gBAAA8H,QAAA,EAAK;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAEd,GAVI,eAAe;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWrB,CAAC,eACFlI,OAAA,CAACG,MAAM;cACL2J,KAAK,EAAE,MAAO;cACdrC,SAAS,EAAE,oBAAqB;cAEhCqD,SAAS,EAAC,cAAc;cACxBf,MAAM,EAAGyF,kBAAkB,iBACzBxP,OAAA;gBAAA8H,QAAA,EACG,CAAC,CAAC0H,kBAAkB,GACjBrQ,MAAM,CAACqQ,kBAAkB,CAAC,CAACpI,MAAM,CAAC,YAAY,CAAC,GAC/C;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACL,GARG,oBAAoB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAS1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACdlI,OAAA,CAACI,WAAW;YAAC0J,KAAK,EAAE,MAAO;YAACgB,SAAS,EAAC,cAAc;YAAAhD,QAAA,gBAClD9H,OAAA,CAACG,MAAM;cACL2J,KAAK,EAAE,CAAArH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjC,SAAS,KAAIX,UAAU,CAAC0I,YAAY,GAAG,MAAM,GAAG,OAAQ;cAC5Ed,SAAS,EAAE,eAAgB;cAE3BqD,SAAS,EAAC,cAAc;cACxBf,MAAM,EAAG0F,aAAa,IACpB,CAAAhN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjC,SAAS,KAAIX,UAAU,CAAC0I,YAAY,gBAC/CvI,OAAA;gBAAA8H,QAAA,GAAM2H,aAAa,EAAC,GAAC;cAAA;gBAAA1H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAE3BlI,OAAA;gBAAA8H,QAAA,GACG2H,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,EAC5BA,aAAa,IAAI,CAAC,EAAC,QACtB;cAAA;gBAAA1H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAER,GAXI,eAAe;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYrB,CAAC,eACFlI,OAAA,CAACG,MAAM;cACL2J,KAAK,EAAE,MAAO;cACdrC,SAAS,EAAE,gBAAiB;cAE5BqD,SAAS,EAAC,cAAc;cACxBf,MAAM,EAAG2F,cAAc,iBACrB1P,OAAA;gBAAA8H,QAAA,EAAMwE,iBAAiB,CAACoD,cAAc;cAAC;gBAAA3H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAC7C,GAJG,gBAAgB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACdlI,OAAA,CAACI,WAAW;YAAC0J,KAAK,EAAE,MAAO;YAACgB,SAAS,EAAC,cAAc;YAAAhD,QAAA,gBAClD9H,OAAA,CAACG,MAAM;cACL2J,KAAK,EAAE,CAAArH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjC,SAAS,KAAIX,UAAU,CAAC0I,YAAY,GAAG,MAAM,GAAG,KAAM;cAC1Ed,SAAS,EAAE,SAAU;cAErBqD,SAAS,EAAC,cAAc;cACxBf,MAAM,EAAGqB,OAAO,IACd,CAAA3I,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjC,SAAS,KAAIX,UAAU,CAAC0I,YAAY,gBAC/CvI,OAAA;gBAAA8H,QAAA,GAAMsD,OAAO,EAAC,GAAC;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAErBlI,OAAA;gBAAA8H,QAAA,GAAMsD,OAAO,EAAC,QAAC;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAEvB,GARI,SAAS;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASf,CAAC,eACFlI,OAAA,CAACG,MAAM;cACL2J,KAAK,EAAE,MAAO;cACdrC,SAAS,EAAE,cAAe;cAE1BqD,SAAS,EAAC,cAAc;cACxBf,MAAM,EAAG7C,YAAY,iBACnBlH,OAAA;gBAAA8H,QAAA,EACG,CAAC,CAACZ,YAAY,GACX/H,MAAM,CAAC+H,YAAY,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC,GACzC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACL,GARG,cAAc;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACdlI,OAAA,CAACG,MAAM;YACL2J,KAAK,EAAE,KAAM;YACbrC,SAAS,EAAE,aAAc;YAEzBsC,MAAM,EAAG4F,WAAW,iBAClB3P,OAAA;cAAA8H,QAAA,EAAM,CAAC,CAAC6H,WAAW,GAAGA,WAAW,GAAG;YAAG;cAAA5H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAC7C,GAHG,aAAa;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAInB,CAAC,eACFlI,OAAA,CAACG,MAAM;YACL2J,KAAK,EAAE,MAAO;YACdrC,SAAS,EAAE,SAAU;YAErBsC,MAAM,EAAG2C,OAAO,iBACd1M,OAAA;cAAA8H,QAAA,EACG,CAAC,CAAC4E,OAAO,GACNvN,MAAM,CAACuN,OAAO,CAAC,CAACtF,MAAM,CAAC,qBAAqB,CAAC,GAC7C;YAAG;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACL;YACF0H,MAAM,EAAE5C,IAAK;YACb6C,gBAAgB,EAAE;UAAU,GATvB,SAAS;YAAA9H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUf,CAAC,eACFlI,OAAA,CAACG,MAAM;YACL2J,KAAK,EAAE,IAAK;YACZrC,SAAS,EAAE,QAAS;YAEpBsC,MAAM,EAAEA,CAACL,MAAM,EAAEtF,IAAI,kBAAKpE,OAAA;cAAA8H,QAAA,EAAM4B;YAAM;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAE,GADzC,QAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eACjBlI,OAAA,CAACd,cAAc;MACb4L,SAAS,EAAC,WAAW;MACrBhB,KAAK,EAAC,sCAAQ;MACdY,IAAI,EAAE3H,aAAa,IAAI,CAAC,CAACE,UAAW;MACpC0L,QAAQ;MACRvG,KAAK,EAAE,GAAI;MACX0H,YAAY,EAAE,KAAM;MACpB5B,QAAQ,EAAEA,CAAA,KAAMtE,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAE;MACjDgF,MAAM,eACJ5O,OAAA;QAAKmI,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAhB,QAAA,gBACxD9H,OAAA,CAAChB,MAAM;UACLmJ,KAAK,EAAE;YAAEO,YAAY,EAAE;UAAE,CAAE;UAC3BJ,OAAO,EAAEA,CAAA,KAAMsB,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAE;UAAA9B,QAAA,EACjD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlI,OAAA,CAAChB,MAAM;UACLiM,IAAI,EAAE,SAAU;UAChB9C,KAAK,EAAE;YAAEO,YAAY,EAAE;UAAE,CAAE;UAC3BJ,OAAO,EAAEA,CAAA,KAAM;YACb,CAAArF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEzC,SAAS,KAAIX,UAAU,CAAC0I,YAAY,GAC5C3F,kBAAkB,CAAC,IAAI,CAAC,GACxBlB,yBAAyB,CAAC,IAAI,CAAC;YACnCkI,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC;UAClC,CAAE;UAAA9B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;MAAAJ,QAAA,eAED9H,OAAA;QAAKmI,KAAK,EAAE;UAAEE,SAAS,EAAE;QAAS,CAAE;QAAAP,QAAA,GAAC,eAChC,EAAC7E,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiH,WAAW,EAAC,2BAAK,EAAClD,UAAU,CAAC/D,UAAU,CAAC,EAAC,0BAE3D;MAAA;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAErB;AAACtH,EAAA,CAhmCuBP,gBAAgB;EAAA,QAErB3B,WAAW,EACPD,SAAS,EAENA,SAAS;AAAA;AAAAsR,EAAA,GALX1P,gBAAgB;AAAA,IAAA0P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}