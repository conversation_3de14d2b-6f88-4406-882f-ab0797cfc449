import React, { useEffect,useState } from "react";
import {Button, Checkbox, Table, Select,Popconfirm, Input, Avatar} from "antd";
import DraggablePopUp from "@components/DraggablePopUp";
import { setting_202_get_team_allusergrp, team_570_get_share_user_list, team_568_save_share_user } from "@common/api/http";
import "./PageShare.scss";
import { nanoid } from "nanoid";
import { CaretDownOutlined } from '@ant-design/icons';
import { useNavigate } from "react-router-dom";
import { globalUtil } from "@common/utils/globalUtil";
import { NoAvatarIcon } from '@common/components/IconUtil';
import { useQueryClient } from "@tanstack/react-query";
import { useQuerySetting324_getNodeLockStatus } from "@common/service/commonHooks";

const SHARESTATUS_0 = 0; // 不是管理员，看不见共享图标
const SHARESTATUS_1 = 1; // 灰色共享图标，初始状态，没有共享
const SHARESTATUS_2 = 2; // 绿色共享图标，有共享

/**
 * @description 共享链接点击后弹窗 团队共享
 */
function PageShare({teamId,nodeId,setVisible}) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [finalDataSource,setFinalDataSource] = useState([]);
  const [dataSource,setDataSource] = useState([]);
  const [allUserList,setAllUserList] = useState([]);
  const [teamShareNodeId,setTeamShareNodeId] = useState(null);

  useEffect(()=>{
    getAllUserList()
  },[]);

  function getAllUserList() {
    setting_202_get_team_allusergrp({teamId}).then(res => {
      let list = (res?.userList||[]).filter(user => user.deleteFlg == 0 && user.enableFlg == 1)
      if(res.resultCode == 200){
        setAllUserList(list)
      }
      getShareUserList(list)
    });
  }

  function getShareUserList(memberList){
    team_570_get_share_user_list({teamId,nodeId}).then(res => {
      if(res.resultCode == 200){
        let dataList = (res.userList||[]).filter(user => user.userId).map(user => {
          user.key = user.userId
          user.userName = <div style={{display:'flex',alignItems:'center'}}>{avatarFormat(memberList.find(mem => mem.userId == user.userId)?.avatar,user.userName)}{user.userName}</div>
          user.isNewAdd = false
          return user
        })
        if(dataList.length < memberList.length){
          dataList = [addNewRow()].concat(dataList)
        }
        setFinalDataSource([...dataList])
        setDataSource([...dataList])
        setTeamShareNodeId(res.teamShareNodeId);
        return
      }
      let list = []
      list.push(addNewRow())
      setFinalDataSource([...list])
      setDataSource([...list])
    });
  }

  function avatarFormat(src,name) {
    return (<Avatar style={{marginRight:5}} src={src} icon={<NoAvatarIcon/>} size={24}/>);
  }

  function addNewRow() {
    return {key:nanoid(),userId:null,userName:'',isNewAdd:true}
  }

  function userNameChange(value,name,index,flag) {
    dataSource[index].isNewAdd = false
    dataSource[index].key = value
    dataSource[index].userId = value
    dataSource[index].userName = name
    let list = []
    if(flag != 1){
      list = [addNewRow()].concat(dataSource)
    }else{
      list = dataSource
    }
    setFinalDataSource([...list])
    setDataSource([...list])
  }

  function deleteRow(userId) {
    let list = []
    let filterList = dataSource.filter(data => data.userId != userId)
    if(dataSource.find(data => data.isNewAdd)){
      list = filterList
    }else{
      list = [addNewRow()].concat(filterList)
    }
    setDataSource(list)
    setFinalDataSource(list)
  }

  const columns = [
    {
      title: <div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>成员</div>,
      dataIndex: 'userName',
      key: 'userName',
      width: '20%',
      render: (value, row, rowIndex) => 
      row.isNewAdd ? 
      <Select 
       showSearch
        bordered={false}
        style={{width:120,alignItems:'center'}}
        placeholder='请选择'
        value={value ? value : null}
        //dropdownStyle={allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).length > 6 && {maxHeight:200}}
        optionFilterProp="children"
        filterOption={(input, option) => (option?.key ?? '').toLowerCase().includes(input.toLowerCase())}
        dropdownMatchSelectWidth={false}
        suffixIcon={<CaretDownOutlined style={{pointerEvents:'none'}}/>}
        onChange={(value,item) =>
          userNameChange(value,item.label,rowIndex,
                    allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).length == 1 ? 1 : 2
          )
        }
        options={allUserList.filter(user => !dataSource.find(data => data.userId == user.userId)).map((user,userIndex) => ({
          value: user.userId,
          label: <div style={{display:'flex',alignItems:'center'}}>{avatarFormat(user.avatar,user.userName)}{user.userName}</div>,
          key: user.userName,
        }))}
      />
      :
      <div>{value}</div>
    },
    {
        title: <div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>权限</div>,
        dataIndex: 'priv',
        key: 'priv',
        width: '65%',
        render: (value, row, rowIndex) => 
        row.userId &&
        <div style={{display:'flex',alignItems:'center',justifyContent:'center'}}>
          <Checkbox disabled className='fontsize-12 share-grey-checked-without-border'/>
          <span style={{marginLeft:10}}>读</span>
        </div>
    },
    {
      title: <div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>操作</div>, 
      dataIndex: 'operation', 
      width: '15%', 
      key: 'operation',
      render: (value, row) => 
      row.userId && 
      <div style={{display:'flex',justifyContent:'center',alignItems:'center'}}>
        <Popconfirm title="确定删除吗?" okText={"确定"} cancelText={"取消"} onConfirm={() => deleteRow(row.userId)}>
          <Button className="fontsize-12" type="link" size="small" style={{ borderRadius: 4 }}>移除</Button>
        </Popconfirm>
      </div>
    },
  ];

  function keywordsSelect(e) {
    let keywords = e.target.value
    let dataList = finalDataSource.filter(user => user.userName == '' || (user.userName != '' && user.userName.props.children[1].toLowerCase().includes(keywords.toLowerCase())))
    setDataSource([...dataList])
  }

  //fix tmsbug-6316: 共享对话框，点击“我的”或“团队共享”蓝色按钮，页面出错
  function navigateTo() {
    !!teamShareNodeId && navigate(`/team/${teamId}/explorer/${teamShareNodeId}`)
  }

  function onOk() {
    let params = {
      teamId: teamId,
      nodeId: nodeId,
      userList: dataSource.filter(data => !data.isNewAdd).map(data => ({
                  shareUserId: data.userId
                }))
    }
    team_568_save_share_user(params).then(res => {
      if(res.resultCode == 200){
        globalUtil.success('共享操作成功')
        queryClient.invalidateQueries('use_query_setting_324_get_node_lock_status');
        setVisible(false);
      }
    });
  }

  return (
    <React.Fragment>
      <div style={{display:'flex',alignItems: 'center',marginBottom:10,marginTop:-10}}>
        <span style={{color:'#999',marginRight:10}}>当前对象共享给如下团队成员，共享位置</span>
        <a onClick={navigateTo}>
          {/* fixBUG tmsbug-3670 团队共享，弹框文案修改*/}
          <span className="iconfont chengyuan fontsize-14" />我的&nbsp;-&gt;&nbsp;
          <span className="iconfont fenxiang2 fontsize-14" />团队共享
        </a>
      </div>
      <Input placeholder="关键字模糊搜索" 
             autoComplete="off" 
             style={{borderRadius:5,width:300,marginBottom:10}}
             onChange={keywordsSelect}/>
      <Table 
        bordered
        size="small"
        columns={columns} 
        dataSource={dataSource} 
        pagination={false}
        scroll={dataSource.length > 6 && { y: 260 }}/>
      <div style={{color:'#999',marginTop:10}}>共{dataSource.filter(data => !data.isNewAdd).length}条</div>
      <div style={{color:'#999',marginTop:30}}>
        <div>备注：</div>
        <div>1、当前对象可共享给团队成员(不限于当前协作群的成员)</div>
        <div>2、当前对象节点及其子节点，一并共享</div>
        <div>3、共享的对象不受角色权限的限制，也不受当前“对象设限({<span className="iconfont lock1" style={{color:"#D9001B"}}/>})”的限制</div>
      </div>
      <div style={{display:'flex',justifyContent:'center',alignItems:'center',marginTop:20}}>
        <Button type="default" size="middle" style={{borderRadius:5,marginRight:10}} onClick={()=>setVisible(false)}>取消</Button>
        <Button type="primary" size="middle" style={{borderRadius:5}} onClick={()=>onOk()}>确定</Button>
      </div>
    </React.Fragment>
  );
}

function PowerShare({teamId, nodeId, shareNo}) {
  const [shareStatus,setShareStatus] = useState(SHARESTATUS_0);
  const [visible, setVisible] = useState(false);
  const {data: status = {lockStatus: 0, shareStatus: 0}} = useQuerySetting324_getNodeLockStatus(teamId,nodeId);

  useEffect(() => {
    setShareStatus(status.shareStatus)
  },[status])

  return <React.Fragment>
    { nodeId && shareStatus != SHARESTATUS_0 
      && ( shareNo ? 
        <a onClick={() => setVisible(true)}>{shareNo == '-' ? '' : shareNo}</a>
        :
        <Button 
          type="text" 
          style={{marginLeft: "5px"}}
          className="sidebarTextColor"
          title="团队共享"
          onClick={() => setVisible(true)}
          icon={
            <span 
              className='iconfont fenxiang2'
              style={SHARESTATUS_2 == shareStatus ? {color:"#02dd10"} : {}}>
            </span>}>
        </Button>
      )}
      {nodeId && shareStatus != SHARESTATUS_0 &&
        <DraggablePopUp
          title={<div style={{fontWeight:'bold'}}>团队共享</div>}
          width={700}
          open={visible}
          destroyOnClose
          maskClosable={false}
          footer={null}
          onCancel={() => setVisible(false)}
        >
            <PageShare teamId={teamId} nodeId={nodeId} setVisible={setVisible}/>
      </DraggablePopUp>}
  </React.Fragment>
}

export default React.memo(PowerShare)