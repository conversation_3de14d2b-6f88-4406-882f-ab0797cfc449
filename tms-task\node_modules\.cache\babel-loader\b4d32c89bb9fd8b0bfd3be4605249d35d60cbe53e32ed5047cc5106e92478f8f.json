{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\quickAcess\\\\views\\\\Desktop\\\\OverView\\\\ThumbView.jsx\",\n  _s = $RefreshSig$();\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\nimport { generateRoutePathByNodeType } from \"@common/router/RouterRegister\";\nimport { eEnableFlg } from \"@common/utils/enum\";\nimport { getColorByType } from \"@common/utils/TsbConfig\";\nimport { Link, useNavigate, useParams } from \"react-router-dom\";\nimport \"./ThumbView.scss\";\nimport ObjExplorerTitle from \"@components/ObjExplorer/ObjExplorerTitle\";\nimport { team_036_get_node_ctx_options } from \"@common/api/http\";\n\n// 快捷方式\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ThumbView({\n  objNode: nodeData,\n  onShowMoreButtonClick,\n  onAddClick,\n  iconClassName,\n  shortcutClassName,\n  isDraggable,\n  onMoreBtnClick\n}) {\n  _s();\n  const {\n    teamId\n  } = useParams();\n  const navigate = useNavigate();\n  async function onClick(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.nodeId) == -9999) {\n      return;\n    }\n    let result = await team_036_get_node_ctx_options({\n      teamId,\n      nodeId: nodeData.nodeId\n    });\n    if ((result === null || result === void 0 ? void 0 : result.resultCode) == 10021 || (result === null || result === void 0 ? void 0 : result.resultCode) == 10022) {\n      return;\n    }\n    let url = generateRoutePathByNodeType(nodeData.nodeType, {\n      teamId,\n      nodeId: nodeData.anchorNodeId || nodeData.realNodeId || nodeData.nodeId,\n      // 左树没有anchorNodeId，中树有anchorNodeId\n      nid: nodeData.realNodeId || nodeData.nodeId // 对象真实的nodeId，中树对象直接跳转至真实对象\n    });\n    navigate(url);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bookmark-tag-col ${isDraggable ? \"shortcut-draggable\" : \"\"}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        cursor: 'pointer'\n      },\n      title: nodeData.nodeName,\n      onClick: onClick,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bookmark-tag\",\n        onClick: nodeData.isAdd ? onAddClick : () => {},\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"bookmark-tag-icon-border\",\n          children: /*#__PURE__*/_jsxDEV(AppNodeResourceIcon, {\n            nodeType: nodeData.nodeType,\n            isShortcut: nodeData.isShortcut,\n            className: `bookmark-tag-icon ${iconClassName}`,\n            style: {\n              color: getColorByType(nodeData.rightFlgIconType)\n            },\n            shortcutClassName: shortcutClassName,\n            children: !nodeData.isAdd && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"iconfont a-gengduoshu fontcolor-light bookmark-extra-btn bookmark-extra-btn-big\",\n              onClick: e => {\n                e.preventDefault();\n                e.stopPropagation();\n                onShowMoreButtonClick && onShowMoreButtonClick({\n                  event: e,\n                  node: nodeData\n                });\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 34\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(ObjExplorerTitle, {\n          nodeData: {\n            ...nodeData,\n            showMoreIcon: false\n          },\n          onShowMoreButtonClick: onShowMoreButtonClick,\n          onMoreBtnClick: onMoreBtnClick,\n          showNodeTypeIcon: false,\n          align: \"center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 10\n  }, this);\n}\n_s(ThumbView, \"lpIAiTq5zsbH0RaNL4yHM96/flk=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ThumbView;\nvar _c;\n$RefreshReg$(_c, \"ThumbView\");", "map": {"version": 3, "names": ["AppNodeResourceIcon", "generateRoutePathByNodeType", "eEnableFlg", "getColorByType", "Link", "useNavigate", "useParams", "ObjExplorerTitle", "team_036_get_node_ctx_options", "jsxDEV", "_jsxDEV", "T<PERSON>bView", "objNode", "nodeData", "onShowMoreButtonClick", "onAddClick", "iconClassName", "shortcutClassName", "isDraggable", "onMoreBtnClick", "_s", "teamId", "navigate", "onClick", "e", "preventDefault", "stopPropagation", "nodeId", "result", "resultCode", "url", "nodeType", "anchorNodeId", "realNodeId", "nid", "className", "children", "style", "cursor", "title", "nodeName", "isAdd", "isShortcut", "color", "rightFlgIconType", "event", "node", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showMoreIcon", "showNodeTypeIcon", "align", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/quickAcess/views/Desktop/OverView/ThumbView.jsx"], "sourcesContent": ["import AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\r\nimport { generateRoutePathByNodeType } from \"@common/router/RouterRegister\";\r\nimport { eEnableFlg } from \"@common/utils/enum\";\r\nimport { getColorByType } from \"@common/utils/TsbConfig\";\r\nimport { Link, useNavigate, useParams } from \"react-router-dom\";\r\nimport \"./ThumbView.scss\";\r\nimport ObjExplorerTitle from \"@components/ObjExplorer/ObjExplorerTitle\";\r\nimport { team_036_get_node_ctx_options } from \"@common/api/http\";\r\n\r\n// 快捷方式\r\nexport default function ThumbView({ objNode: nodeData, onShowMoreButtonClick, onAddClick, iconClassName, shortcutClassName, isDraggable, onMoreBtnClick }) {\r\n\r\n  const { teamId } = useParams();\r\n  const navigate = useNavigate();\r\n\r\n  async function onClick(e){\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if(nodeData?.nodeId == -9999){\r\n      return\r\n    }\r\n    let result = await team_036_get_node_ctx_options({teamId, nodeId: nodeData.nodeId});\r\n    if(result?.resultCode == 10021 || result?.resultCode == 10022){\r\n      return\r\n    }\r\n    let url = generateRoutePathByNodeType(nodeData.nodeType, {\r\n                teamId,\r\n                nodeId: nodeData.anchorNodeId || nodeData.realNodeId || nodeData.nodeId, // 左树没有anchorNodeId，中树有anchorNodeId\r\n                nid: nodeData.realNodeId || nodeData.nodeId, // 对象真实的nodeId，中树对象直接跳转至真实对象\r\n              })\r\n    navigate(url);\r\n  }\r\n\r\n  return <div className={`bookmark-tag-col ${isDraggable ? \"shortcut-draggable\" : \"\"}`}>\r\n    {/* <Link\r\n      to={\r\n        generateRoutePathByNodeType(nodeData.nodeType, {\r\n          teamId,\r\n          nodeId: nodeData.anchorNodeId || nodeData.realNodeId || nodeData.nodeId, // 左树没有anchorNodeId，中树有anchorNodeId\r\n          nid: nodeData.realNodeId || nodeData.nodeId, // 对象真实的nodeId，中树对象直接跳转至真实对象\r\n        })}\r\n        title={nodeData.nodeName}\r\n        // target=\"_blank\" rel=\"noopener noreferrer\"\r\n    > */}\r\n    <div style={{cursor:'pointer'}} title={nodeData.nodeName} onClick={onClick}>\r\n      <div className=\"bookmark-tag\" onClick={nodeData.isAdd ? onAddClick : () => { }} >\r\n        <span className=\"bookmark-tag-icon-border\">\r\n          <AppNodeResourceIcon nodeType={nodeData.nodeType} isShortcut={nodeData.isShortcut} className={`bookmark-tag-icon ${iconClassName}`} style={{ color: getColorByType(nodeData.rightFlgIconType) }} shortcutClassName={shortcutClassName}>\r\n            {\r\n              !nodeData.isAdd && <span className=\"iconfont a-gengduoshu fontcolor-light bookmark-extra-btn bookmark-extra-btn-big\" onClick={(e) => {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n                onShowMoreButtonClick && onShowMoreButtonClick({\r\n                  event: e,\r\n                  node: nodeData,\r\n                })\r\n              }}></span>\r\n            }\r\n          </AppNodeResourceIcon>\r\n        </span>\r\n        {/* <div className={`fontcolor-light text-overflow bookmark-tag-name ${nodeData.boldTypeFlg == eEnableFlg.enable ? \"tree-dir-title-bold\" : \"\"} \r\n          ${nodeData.italicTypeFlg == eEnableFlg.enable ? \"tree-dir-title-italic\" : \"\"} \r\n          ${nodeData.underlineTypeFlg == eEnableFlg.enable && nodeData.strikeTypeFlg == eEnableFlg.enable ? \"tree-dir-title-text-decoration\" :\r\n            nodeData.underlineTypeFlg == eEnableFlg.enable ? \"tree-dir-title-underline\" :\r\n              nodeData.strikeTypeFlg == eEnableFlg.enable ? \"tree-dir-title-delete\" :\r\n                \"\"\r\n          }`} style={{ color: getColorByType(nodeData.nameTextColorType) }}>{nodeData.label}\r\n        </div> */}\r\n        <ObjExplorerTitle nodeData={{...nodeData, showMoreIcon: false}} onShowMoreButtonClick={onShowMoreButtonClick} onMoreBtnClick={onMoreBtnClick} showNodeTypeIcon={false} align={\"center\"}/>\r\n      </div>\r\n    {/* </Link > */}\r\n    </div>\r\n  </div>\r\n}"], "mappings": ";;AAAA,OAAOA,mBAAmB,MAAM,iCAAiC;AACjE,SAASC,2BAA2B,QAAQ,+BAA+B;AAC3E,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,IAAI,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAC/D,OAAO,kBAAkB;AACzB,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,SAASC,6BAA6B,QAAQ,kBAAkB;;AAEhE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,eAAe,SAASC,SAASA,CAAC;EAAEC,OAAO,EAAEC,QAAQ;EAAEC,qBAAqB;EAAEC,UAAU;EAAEC,aAAa;EAAEC,iBAAiB;EAAEC,WAAW;EAAEC;AAAe,CAAC,EAAE;EAAAC,EAAA;EAEzJ,MAAM;IAAEC;EAAO,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC9B,MAAMgB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,eAAekB,OAAOA,CAACC,CAAC,EAAC;IACvBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAG,CAAAb,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,MAAM,KAAI,CAAC,IAAI,EAAC;MAC3B;IACF;IACA,IAAIC,MAAM,GAAG,MAAMpB,6BAA6B,CAAC;MAACa,MAAM;MAAEM,MAAM,EAAEd,QAAQ,CAACc;IAAM,CAAC,CAAC;IACnF,IAAG,CAAAC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,UAAU,KAAI,KAAK,IAAI,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,UAAU,KAAI,KAAK,EAAC;MAC5D;IACF;IACA,IAAIC,GAAG,GAAG7B,2BAA2B,CAACY,QAAQ,CAACkB,QAAQ,EAAE;MAC7CV,MAAM;MACNM,MAAM,EAAEd,QAAQ,CAACmB,YAAY,IAAInB,QAAQ,CAACoB,UAAU,IAAIpB,QAAQ,CAACc,MAAM;MAAE;MACzEO,GAAG,EAAErB,QAAQ,CAACoB,UAAU,IAAIpB,QAAQ,CAACc,MAAM,CAAE;IAC/C,CAAC,CAAC;IACZL,QAAQ,CAACQ,GAAG,CAAC;EACf;EAEA,oBAAOpB,OAAA;IAAKyB,SAAS,EAAE,oBAAoBjB,WAAW,GAAG,oBAAoB,GAAG,EAAE,EAAG;IAAAkB,QAAA,eAWnF1B,OAAA;MAAK2B,KAAK,EAAE;QAACC,MAAM,EAAC;MAAS,CAAE;MAACC,KAAK,EAAE1B,QAAQ,CAAC2B,QAAS;MAACjB,OAAO,EAAEA,OAAQ;MAAAa,QAAA,eACzE1B,OAAA;QAAKyB,SAAS,EAAC,cAAc;QAACZ,OAAO,EAAEV,QAAQ,CAAC4B,KAAK,GAAG1B,UAAU,GAAG,MAAM,CAAE,CAAE;QAAAqB,QAAA,gBAC7E1B,OAAA;UAAMyB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACxC1B,OAAA,CAACV,mBAAmB;YAAC+B,QAAQ,EAAElB,QAAQ,CAACkB,QAAS;YAACW,UAAU,EAAE7B,QAAQ,CAAC6B,UAAW;YAACP,SAAS,EAAE,qBAAqBnB,aAAa,EAAG;YAACqB,KAAK,EAAE;cAAEM,KAAK,EAAExC,cAAc,CAACU,QAAQ,CAAC+B,gBAAgB;YAAE,CAAE;YAAC3B,iBAAiB,EAAEA,iBAAkB;YAAAmB,QAAA,EAElO,CAACvB,QAAQ,CAAC4B,KAAK,iBAAI/B,OAAA;cAAMyB,SAAS,EAAC,iFAAiF;cAACZ,OAAO,EAAGC,CAAC,IAAK;gBACnIA,CAAC,CAACC,cAAc,CAAC,CAAC;gBAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;gBACnBZ,qBAAqB,IAAIA,qBAAqB,CAAC;kBAC7C+B,KAAK,EAAErB,CAAC;kBACRsB,IAAI,EAAEjC;gBACR,CAAC,CAAC;cACJ;YAAE;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eASPxC,OAAA,CAACH,gBAAgB;UAACM,QAAQ,EAAE;YAAC,GAAGA,QAAQ;YAAEsC,YAAY,EAAE;UAAK,CAAE;UAACrC,qBAAqB,EAAEA,qBAAsB;UAACK,cAAc,EAAEA,cAAe;UAACiC,gBAAgB,EAAE,KAAM;UAACC,KAAK,EAAE;QAAS;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AACR;AAAC9B,EAAA,CA/DuBT,SAAS;EAAA,QAEZL,SAAS,EACXD,WAAW;AAAA;AAAAiD,EAAA,GAHN3C,SAAS;AAAA,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}