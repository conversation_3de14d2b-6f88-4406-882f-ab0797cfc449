{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\quickAcess\\\\views\\\\DataPush\\\\DataPush.jsx\",\n  _s = $RefreshSig$();\nimport { Layout } from \"antd\";\nimport React, { useEffect, useState } from \"react\";\nimport { Outlet, useNavigate, useParams } from \"react-router-dom\";\nimport \"./DataPush.scss\";\nimport * as httpQuickAccess from \"@common/api/http\";\nimport { eNodeTypeId } from \"@common/utils/TsbConfig\";\nimport NoviceGuide from \"@components/NoviceGuide\"; // 系统订阅-新手引导\nimport { useQuerySetting202_getTeamAllUsers, useQueryTeam571_GetSpaceVaildUserList, useQuerySetting320GetNodePrivQuery } from \"@common/service/commonHooks\";\nimport { eEnableFlg } from \"@common/utils/enum\";\nimport SysSubscribeHeaderSearch from './SubscribeChild/SysSubscribeHeaderSearch';\nimport SysSubscribeSider from './SubscribeChild/SysSubscribeSider';\nimport SubscribeEdit from './EditDrawer/SubscribeEdit';\nimport OpDataPush from './EditDrawer/OpDataPush';\n\n// 订阅类型/ 选择类型\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst typeList = [{\n  key: 1,\n  label: \"搜索\"\n}, {\n  key: 6,\n  label: \"报表\"\n}, {\n  key: 9,\n  label: \"事件\"\n}];\n\n// 系统订阅主页\nexport default function DataPush({\n  fromType,\n  ganttName\n}) {\n  _s();\n  const {\n    teamId,\n    nodeId,\n    nid: objNodeId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    data: userList,\n    dataUpdatedAt: dataUpdatedAtTeam\n  } = useQuerySetting202_getTeamAllUsers(teamId, fromType != 'gantt'); // 人员\n  const {\n    data: _userList,\n    dataUpdatedAt: dataUpdatedAtSpace\n  } = useQueryTeam571_GetSpaceVaildUserList(teamId, nodeId, 0, fromType == 'gantt'); // 人员\n\n  const {\n    data: {\n      privWrite,\n      privDelete\n    } = {},\n    isLoading: isLoadingPriv,\n    refetch: refetchPriv,\n    dataUpdatedAt\n  } = useQuerySetting320GetNodePrivQuery({\n    teamId,\n    nodeId\n  });\n  const [allMember, setAllMember] = useState([]); //所有成员\n\n  //新建弹出框\n  const [modalVisible, setModalVisible] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [dataSource, setDataSource] = useState([]);\n  const [selectedKey, setSelectedKey] = useState(null);\n  const [submitFlag, setSubmitFlag] = useState(true);\n  const [submitFlag1, setSubmitFlag1] = useState(false);\n  const [editSubmitFlag, setEditSubmitFlag] = useState(0);\n  const [editData, setEditData] = useState(null);\n  const [opType, setOpType] = useState(0); // 新建/编辑\n  const [nodeType, setNodeType] = useState(); // 10801(系统订阅) / 10802(消息推送)\n\n  console.log(\"nodeType\", nodeType);\n  const [searchQuery, setSearchQuery] = useState({\n    subscribersKey: null,\n    typeKey: null,\n    keyWordConfirm: '',\n    addresseeKey: null,\n    createDtList: [],\n    entryDtList: [],\n    nextDtList: [],\n    sortValue: 1,\n    sortType: 1,\n    pageNum: 1,\n    isSort: false,\n    pageChange: false,\n    isDeleteOwn: false,\n    isSubmit: false,\n    isClear: false\n  });\n  const [loading, setLoading] = useState(false);\n  const [isChange, setIsChange] = useState(false);\n  useEffect(() => {\n    if (fromType != 'gantt') {\n      let memberList = [];\n      (userList || []).forEach(item => {\n        let obj = {\n          key: item.userId,\n          label: item.userName,\n          avatar: item.avatar,\n          email: item.email\n        };\n        memberList.push(obj);\n      });\n      setAllMember([...memberList]);\n    }\n  }, [dataUpdatedAtTeam]);\n  useEffect(() => {\n    if (fromType == 'gantt') {\n      let memberList = [];\n      (_userList || []).forEach(item => {\n        let obj = {\n          key: item.userId,\n          label: item.userName,\n          avatar: item.avatar,\n          email: item.email\n        };\n        memberList.push(obj);\n      });\n      setAllMember([...memberList]);\n    }\n  }, [dataUpdatedAtSpace]);\n  useEffect(() => {\n    if (allMember.length > 0 && submitFlag && editSubmitFlag != 2 && !searchQuery.isSubmit) {\n      if (searchQuery.isSort || editSubmitFlag == 1) {\n        getData(1, false);\n      } else {\n        getData(0, true);\n      }\n    }\n  }, [allMember, submitFlag, editSubmitFlag, JSON.stringify(searchQuery)]);\n  useEffect(() => {\n    if (!!objNodeId) {\n      getPageNo();\n      setSelectedKey(objNodeId);\n    } else {\n      setSelectedKey(null);\n    }\n  }, [objNodeId]);\n  function getPageNo() {\n    let params = {\n      teamId,\n      nodeId,\n      taskNodeId: objNodeId,\n      pageSize: 30,\n      order: searchQuery.sortValue,\n      desc: searchQuery.sortType\n    };\n    httpQuickAccess.team_598_get_sched_task_page_by_id(params).then(res => {\n      if (res.resultCode == 200) {\n        setSearchQuery({\n          ...searchQuery,\n          pageNum: res.pageNum\n        });\n      }\n    });\n  }\n\n  //获取中树列表\n  async function getData(type, isNeedLoading) {\n    if (isNeedLoading) {\n      setLoading(true);\n    }\n    let params = {\n      teamId,\n      nodeId,\n      pageNum: searchQuery.pageNum,\n      pageSize: 30,\n      order: searchQuery.sortValue,\n      desc: searchQuery.sortType\n    };\n    if (!!searchQuery.subscribersKey) {\n      params.creatorUid = searchQuery.subscribersKey;\n    }\n    if (!!searchQuery.typeKey) {\n      params.objType = searchQuery.typeKey;\n    }\n    if (!!searchQuery.keyWordConfirm) {\n      params.keywords = searchQuery.keyWordConfirm;\n    }\n    if (!!searchQuery.addresseeKey) {\n      params.toUid = searchQuery.addresseeKey;\n    }\n    if (searchQuery.createDtList.length != 0) {\n      params.createDtBegin = searchQuery.createDtList[0] + ' 00:00:00';\n      params.createDtEnd = searchQuery.createDtList[1] + ' 23:59:59';\n    }\n    if (searchQuery.entryDtList.length !== 0) {\n      params.latestExecDtBegin = searchQuery.entryDtList[0] + ' 00:00:00';\n      params.latestExecDtEnd = searchQuery.entryDtList[1] + ' 23:59:59';\n    }\n    if (searchQuery.nextDtList.length !== 0) {\n      params.nextExecDtBegin = searchQuery.nextDtList[0] + ' 00:00:00';\n      params.nextExecDtEnd = searchQuery.nextDtList[1] + ' 23:59:59';\n    }\n    await httpQuickAccess.team_537_get_sched_task_src_list(params).then(res => {\n      if (res.resultCode === 200) {\n        setTotal(res.totalCount);\n        let dataList = (res.schedNodeList || []).map(item => {\n          let creatorItem = allMember.find(element => element.key === item.creatorUid);\n          item.key = item.id;\n          item.avatar = creatorItem === null || creatorItem === void 0 ? void 0 : creatorItem.avatar;\n          return item;\n        });\n        if (type == 0) {\n          if (dataList.length > 0 && (isSearch() || !objNodeId || searchQuery.pageChange || searchQuery.isDeleteOwn || searchQuery.isClear || submitFlag1)) {\n            if (dataList[0].nodeType == eNodeTypeId.nt_10801_quick_access_sys_subscription_single) {\n              navigate(`subscribe/${dataList[0].id}`);\n            } else {\n              navigate(`datapush/${dataList[0].id}`);\n            }\n          }\n        } else {\n          if (!selectedKey && dataList.length > 0) {\n            if (dataList[0].nodeType == eNodeTypeId.nt_10801_quick_access_sys_subscription_single) {\n              navigate(`subscribe/${dataList[0].id}`);\n            } else {\n              navigate(`datapush/${dataList[0].id}`);\n            }\n          }\n        }\n        if (dataList.length == 0) {\n          setSelectedKey(null);\n          navigate(``);\n        }\n        setDataSource([...dataList]);\n      } else {\n        setTotal(0);\n        setDataSource([]);\n      }\n    });\n    setLoading(false);\n    setSubmitFlag1(false);\n    if (isNeedLoading) {\n      setEditSubmitFlag(0);\n    }\n  }\n\n  // 添加订阅\n  function addSubscribeClick(nodeType) {\n    console.log(\"nodeType\", nodeType);\n    setOpType(0);\n    setNodeType(nodeType);\n    setSubmitFlag(false);\n    setModalVisible(true);\n    setEditData(null);\n  }\n  ;\n\n  //关闭新建/编辑弹出层\n  function closeDrawer(tp, obj) {\n    let isSubmit = false;\n    if (tp == 0) {\n      if (!!obj) {\n        return setIsChange(true);\n      }\n      isSubmit = true;\n    }\n    setIsChange(false);\n    if (!submitFlag) {\n      if (tp == 1) {\n        setSearchQuery({\n          ...searchQuery,\n          isSort: false,\n          pageChange: false,\n          isDeleteOwn: false,\n          isSubmit: isSubmit,\n          pageNum: 1\n        });\n        setSubmitFlag1(true);\n      } else {\n        setSearchQuery({\n          ...searchQuery,\n          isSort: false,\n          pageChange: false,\n          isDeleteOwn: false,\n          isSubmit: isSubmit\n        });\n      }\n      setSubmitFlag(true);\n    } else {\n      setSearchQuery({\n        ...searchQuery,\n        isSort: false,\n        pageChange: false,\n        isDeleteOwn: false,\n        isSubmit: isSubmit\n      });\n    }\n    if (editSubmitFlag == 2) {\n      setEditSubmitFlag(1);\n    }\n    setModalVisible(false);\n  }\n\n  //是否处于搜索状态\n  function isSearch() {\n    return !!searchQuery.subscribersKey || !!searchQuery.typeKey || !!searchQuery.addresseeKey || !!searchQuery.keyWordConfirm || searchQuery.createDtList.length > 0 || searchQuery.entryDtList.length > 0 || searchQuery.nextDtList.length > 0;\n  }\n\n  //字体个性化\n  function getTextFontType(nameTextFontType, type) {\n    if (!!nameTextFontType) {\n      if (type == 0) {\n        if (nameTextFontType.split(',')[0] == eEnableFlg.enable) {\n          return true;\n        }\n      }\n      if (type == 1) {\n        if (nameTextFontType.split(',')[1] == eEnableFlg.enable) {\n          return true;\n        }\n      }\n      if (type == 2) {\n        if (nameTextFontType.split(',')[2] == eEnableFlg.enable) {\n          return true;\n        }\n      }\n      if (type == 3) {\n        if (nameTextFontType.split(',')[3] == eEnableFlg.enable) {\n          return true;\n        }\n      }\n      if (type == 4) {\n        if (nameTextFontType.split(',')[2] == eEnableFlg.enable && nameTextFontType.split(',')[3] == eEnableFlg.enable) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Layout, {\n      className: \"subscribe ant-layout-sider-light\",\n      children: [/*#__PURE__*/_jsxDEV(SysSubscribeHeaderSearch, {\n        allMember: allMember,\n        searchQuery: searchQuery,\n        setSearchQuery: setSearchQuery,\n        typeList: typeList,\n        fromType: fromType,\n        getData: getData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        hasSider: true,\n        style: {\n          flex: \"auto\"\n        },\n        className: \"ant-layout-sider-light\",\n        children: [/*#__PURE__*/_jsxDEV(SysSubscribeSider, {\n          privWrite: privWrite,\n          searchQuery: searchQuery,\n          setSearchQuery: setSearchQuery,\n          getData: getData,\n          loading: loading,\n          addSubscribeClick: addSubscribeClick,\n          total: total,\n          dataSource: dataSource,\n          selectedKey: selectedKey,\n          isSearch: isSearch,\n          getTextFontType: getTextFontType\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Outlet, {\n          context: {\n            dataSource,\n            selectedKey,\n            allMember,\n            getData,\n            setOpType,\n            setNodeType,\n            setEditSubmitFlag,\n            setModalVisible,\n            setEditData,\n            getTextFontType,\n            privWrite\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SubscribeEdit, {\n          allMember: allMember,\n          opType: opType,\n          closeDrawer: closeDrawer,\n          modalVisible: nodeType == eNodeTypeId.nt_10801_quick_access_sys_subscription_single && modalVisible,\n          setModalVisible: setModalVisible,\n          editData: editData,\n          isChange: isChange,\n          setIsChange: setIsChange,\n          selectedKey: selectedKey,\n          typeList: typeList,\n          fromType: fromType,\n          ganttName: ganttName,\n          nodeType: nodeType\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(OpDataPush, {\n          allMember: allMember,\n          opType: opType,\n          closeDrawer: closeDrawer,\n          modalVisible: nodeType == eNodeTypeId.nt_10802_quick_access_sys_subscription_single_datapush && modalVisible,\n          setModalVisible: setModalVisible,\n          editData: editData,\n          isChange: isChange,\n          setIsChange: setIsChange,\n          selectedKey: selectedKey,\n          typeList: typeList,\n          fromType: fromType,\n          ganttName: ganttName,\n          nodeType: nodeType\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NoviceGuide, {\n      nodeType: eNodeTypeId.nt_108_quick_access_sys_subscription,\n      awakeFlg: 0\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(DataPush, \"DnfJQ8XLNcyO2umOTDPkv6LMROk=\", false, function () {\n  return [useParams, useNavigate, useQuerySetting202_getTeamAllUsers, useQueryTeam571_GetSpaceVaildUserList, useQuerySetting320GetNodePrivQuery];\n});\n_c = DataPush;\nvar _c;\n$RefreshReg$(_c, \"DataPush\");", "map": {"version": 3, "names": ["Layout", "React", "useEffect", "useState", "Outlet", "useNavigate", "useParams", "httpQuickAccess", "eNodeTypeId", "NoviceGuide", "useQuerySetting202_getTeamAllUsers", "useQueryTeam571_GetSpaceVaildUserList", "useQuerySetting320GetNodePrivQuery", "eEnableFlg", "SysSubscribeHeaderSearch", "SysSubscribeSider", "SubscribeEdit", "OpDataPush", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "typeList", "key", "label", "DataPush", "fromType", "ganttName", "_s", "teamId", "nodeId", "nid", "objNodeId", "navigate", "data", "userList", "dataUpdatedAt", "dataUpdatedAtTeam", "_userList", "dataUpdatedAtSpace", "privWrite", "privDelete", "isLoading", "isLoadingPriv", "refetch", "refetchPriv", "allMember", "setAllMember", "modalVisible", "setModalVisible", "total", "setTotal", "dataSource", "setDataSource", "<PERSON><PERSON><PERSON>", "setSelectedKey", "submitFlag", "setSubmitFlag", "submitFlag1", "setSubmitFlag1", "editSubmitFlag", "setEditSubmitFlag", "editData", "setEditData", "opType", "setOpType", "nodeType", "setNodeType", "console", "log", "searchQuery", "setSearch<PERSON>uery", "subscribersKey", "typeKey", "keyWordConfirm", "<PERSON>ee<PERSON><PERSON>", "createDtList", "entryDtList", "nextDtList", "sortValue", "sortType", "pageNum", "isSort", "pageChange", "isDeleteOwn", "isSubmit", "isClear", "loading", "setLoading", "isChange", "setIsChange", "memberList", "for<PERSON>ach", "item", "obj", "userId", "userName", "avatar", "email", "push", "length", "getData", "JSON", "stringify", "getPageNo", "params", "taskNodeId", "pageSize", "order", "desc", "team_598_get_sched_task_page_by_id", "then", "res", "resultCode", "type", "isNeedLoading", "creator<PERSON><PERSON>", "objType", "keywords", "toUid", "createDtBegin", "createDtEnd", "latestExecDtBegin", "latestExecDtEnd", "nextExecDtBegin", "nextExecDtEnd", "team_537_get_sched_task_src_list", "totalCount", "dataList", "schedNodeList", "map", "creatorItem", "find", "element", "id", "isSearch", "nt_10801_quick_access_sys_subscription_single", "addSubscribeClick", "closeDrawer", "tp", "getTextFontType", "nameTextFontType", "split", "enable", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hasSider", "style", "flex", "context", "nt_10802_quick_access_sys_subscription_single_datapush", "nt_108_quick_access_sys_subscription", "awakeFlg", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/quickAcess/views/DataPush/DataPush.jsx"], "sourcesContent": ["import { Layout } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Outlet, useNavigate, useParams } from \"react-router-dom\";\r\nimport \"./DataPush.scss\";\r\nimport * as httpQuickAccess from \"@common/api/http\";\r\nimport { eNodeTypeId } from \"@common/utils/TsbConfig\";\r\nimport NoviceGuide from \"@components/NoviceGuide\"; // 系统订阅-新手引导\r\nimport { useQuerySetting202_getTeamAllUsers, useQueryTeam571_GetSpaceVaildUserList, useQuerySetting320GetNodePrivQuery } from \"@common/service/commonHooks\";\r\nimport { eEnableFlg } from \"@common/utils/enum\";\r\nimport SysSubscribeHeaderSearch from './SubscribeChild/SysSubscribeHeaderSearch';\r\nimport SysSubscribeSider from './SubscribeChild/SysSubscribeSider';\r\nimport SubscribeEdit from './EditDrawer/SubscribeEdit';\r\nimport OpDataPush from './EditDrawer/OpDataPush';\r\n\r\n// 订阅类型/ 选择类型\r\nconst typeList = [\r\n  { key: 1, label: \"搜索\" },\r\n  { key: 6, label: \"报表\" },\r\n  { key: 9, label: \"事件\" },\r\n];\r\n\r\n// 系统订阅主页\r\nexport default function DataPush({fromType, ganttName}) {\r\n  const { teamId, nodeId, nid: objNodeId } = useParams();\r\n  const navigate = useNavigate();\r\n\r\n  const { data: userList, dataUpdatedAt: dataUpdatedAtTeam } = useQuerySetting202_getTeamAllUsers(teamId,fromType!='gantt');  // 人员\r\n  const { data: _userList, dataUpdatedAt: dataUpdatedAtSpace} = useQueryTeam571_GetSpaceVaildUserList(teamId,nodeId,0,fromType=='gantt');  // 人员\r\n\r\n  const { data: { privWrite, privDelete } = {}, isLoading: isLoadingPriv, refetch: refetchPriv, dataUpdatedAt } = useQuerySetting320GetNodePrivQuery({teamId, nodeId});\r\n\r\n  const [allMember, setAllMember] = useState([])//所有成员\r\n\r\n  //新建弹出框\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n\r\n  const [total,setTotal] = useState(0);\r\n  const [dataSource, setDataSource] = useState([]);\r\n  const [selectedKey,setSelectedKey] = useState(null);\r\n\r\n  const [submitFlag, setSubmitFlag] = useState(true);\r\n  const [submitFlag1, setSubmitFlag1] = useState(false);\r\n  const [editSubmitFlag, setEditSubmitFlag] = useState(0);\r\n\r\n  const [editData, setEditData] = useState(null);\r\n  const [opType, setOpType] = useState(0); // 新建/编辑\r\n  const [nodeType, setNodeType] = useState(); // 10801(系统订阅) / 10802(消息推送)\r\n\r\n  console.log(\"nodeType\", nodeType);\r\n\r\n  const [searchQuery,setSearchQuery] = useState({\r\n    subscribersKey: null,\r\n    typeKey: null,\r\n    keyWordConfirm: '',\r\n    addresseeKey: null,\r\n    createDtList: [],\r\n    entryDtList: [],\r\n    nextDtList: [],\r\n    sortValue: 1,\r\n    sortType: 1,\r\n    pageNum: 1,\r\n    isSort: false,\r\n    pageChange: false,\r\n    isDeleteOwn: false,\r\n    isSubmit: false,\r\n    isClear: false\r\n  });\r\n\r\n  const [loading,setLoading] = useState(false);\r\n  const [isChange,setIsChange] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if(fromType != 'gantt'){\r\n      let memberList = [];\r\n      (userList||[]).forEach(item => {\r\n        let obj = { key: item.userId, label: item.userName, avatar: item.avatar, email: item.email };\r\n        memberList.push(obj);\r\n      });\r\n      setAllMember([...memberList])\r\n    }\r\n  }, [dataUpdatedAtTeam]);\r\n\r\n  useEffect(() => {\r\n    if(fromType == 'gantt'){\r\n      let memberList = [];\r\n      (_userList||[]).forEach(item => {\r\n        let obj = { key: item.userId, label: item.userName, avatar: item.avatar, email: item.email };\r\n        memberList.push(obj);\r\n      });\r\n      setAllMember([...memberList])\r\n    }\r\n  }, [dataUpdatedAtSpace]);\r\n\r\n  useEffect(() => {\r\n    if (allMember.length > 0 && submitFlag && editSubmitFlag != 2 && !searchQuery.isSubmit) {\r\n      if(searchQuery.isSort || editSubmitFlag == 1){\r\n        getData(1,false);\r\n      }else{\r\n        getData(0,true);\r\n      }\r\n    }\r\n  }, [allMember, submitFlag, editSubmitFlag, JSON.stringify(searchQuery)]);\r\n\r\n  useEffect(()=>{\r\n    if(!!objNodeId){\r\n      getPageNo();\r\n      setSelectedKey(objNodeId);\r\n    }else{\r\n      setSelectedKey(null);\r\n    }\r\n  },[objNodeId]);\r\n\r\n  function getPageNo(){\r\n    let params = {\r\n      teamId,\r\n      nodeId,\r\n      taskNodeId: objNodeId,\r\n      pageSize: 30,\r\n      order: searchQuery.sortValue,\r\n      desc: searchQuery.sortType,\r\n    }\r\n    httpQuickAccess.team_598_get_sched_task_page_by_id(params).then(res => {\r\n      if(res.resultCode == 200){\r\n        setSearchQuery({\r\n          ...searchQuery,\r\n          pageNum: res.pageNum,\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  //获取中树列表\r\n  async function getData(type,isNeedLoading){\r\n    if(isNeedLoading){\r\n      setLoading(true);\r\n    }\r\n    let params = {\r\n      teamId,\r\n      nodeId,\r\n      pageNum: searchQuery.pageNum,\r\n      pageSize: 30,\r\n      order: searchQuery.sortValue,\r\n      desc: searchQuery.sortType,\r\n    }\r\n    if (!!searchQuery.subscribersKey) {\r\n      params.creatorUid = searchQuery.subscribersKey\r\n    }\r\n    if (!!searchQuery.typeKey) {\r\n      params.objType = searchQuery.typeKey\r\n    }\r\n    if (!!searchQuery.keyWordConfirm) {\r\n      params.keywords = searchQuery.keyWordConfirm\r\n    }\r\n    if (!!searchQuery.addresseeKey) {\r\n      params.toUid = searchQuery.addresseeKey\r\n    }\r\n    if (searchQuery.createDtList.length != 0) {\r\n      params.createDtBegin = searchQuery.createDtList[0] + ' 00:00:00'\r\n      params.createDtEnd = searchQuery.createDtList[1] + ' 23:59:59'\r\n    }\r\n    if (searchQuery.entryDtList.length !== 0) {\r\n      params.latestExecDtBegin = searchQuery.entryDtList[0] + ' 00:00:00'\r\n      params.latestExecDtEnd = searchQuery.entryDtList[1] + ' 23:59:59'\r\n    }\r\n    if (searchQuery.nextDtList.length !== 0) {\r\n      params.nextExecDtBegin = searchQuery.nextDtList[0] + ' 00:00:00'\r\n      params.nextExecDtEnd = searchQuery.nextDtList[1] + ' 23:59:59'\r\n    }\r\n    await httpQuickAccess.team_537_get_sched_task_src_list(params).then((res) => {\r\n      if (res.resultCode === 200) {\r\n        setTotal(res.totalCount);\r\n        let dataList = (res.schedNodeList||[]).map(item => {\r\n          let creatorItem = allMember.find(element => element.key === item.creatorUid);\r\n          item.key = item.id\r\n          item.avatar = creatorItem?.avatar\r\n          return item\r\n        });\r\n        if(type == 0){\r\n          if(dataList.length > 0 && (isSearch() || !objNodeId || searchQuery.pageChange || searchQuery.isDeleteOwn || searchQuery.isClear || submitFlag1)){\r\n            if(dataList[0].nodeType == eNodeTypeId.nt_10801_quick_access_sys_subscription_single){\r\n              navigate(`subscribe/${dataList[0].id}`);\r\n            } else {\r\n              navigate(`datapush/${dataList[0].id}`);\r\n            }\r\n          }\r\n        }else{\r\n          if(!selectedKey && dataList.length > 0){\r\n            if(dataList[0].nodeType == eNodeTypeId.nt_10801_quick_access_sys_subscription_single){\r\n              navigate(`subscribe/${dataList[0].id}`);\r\n            } else {\r\n              navigate(`datapush/${dataList[0].id}`);\r\n            }\r\n          }\r\n        }\r\n        if(dataList.length == 0){\r\n          setSelectedKey(null);\r\n          navigate(``);\r\n        }\r\n        setDataSource([...dataList]);\r\n      } else {\r\n        setTotal(0);\r\n        setDataSource([]);\r\n      }\r\n    });\r\n    setLoading(false);\r\n    setSubmitFlag1(false);\r\n    if(isNeedLoading){\r\n      setEditSubmitFlag(0);\r\n    }\r\n  }\r\n\r\n  // 添加订阅\r\n  function addSubscribeClick(nodeType){\r\n    console.log(\"nodeType\", nodeType);\r\n    setOpType(0);\r\n    setNodeType(nodeType);\r\n    setSubmitFlag(false);\r\n    setModalVisible(true);\r\n    setEditData(null);\r\n  };\r\n\r\n  //关闭新建/编辑弹出层\r\n  function closeDrawer(tp,obj){\r\n    let isSubmit = false\r\n    if(tp == 0){\r\n      if(!!obj){\r\n        return setIsChange(true);\r\n      }\r\n      isSubmit = true\r\n    }\r\n    setIsChange(false);\r\n    if(!submitFlag){\r\n      if(tp == 1){\r\n        setSearchQuery({...searchQuery,isSort:false,pageChange:false,isDeleteOwn:false,isSubmit:isSubmit,pageNum: 1});\r\n        setSubmitFlag1(true);\r\n      }else{\r\n        setSearchQuery({...searchQuery,isSort:false,pageChange:false,isDeleteOwn:false,isSubmit:isSubmit})\r\n      }\r\n      setSubmitFlag(true);\r\n    }else{\r\n      setSearchQuery({...searchQuery,isSort:false,pageChange:false,isDeleteOwn:false,isSubmit:isSubmit})\r\n    }\r\n    if(editSubmitFlag == 2){\r\n      setEditSubmitFlag(1);\r\n    }\r\n    setModalVisible(false);\r\n  }\r\n\r\n  //是否处于搜索状态\r\n  function isSearch(){\r\n    return (!!searchQuery.subscribersKey || !!searchQuery.typeKey || !!searchQuery.addresseeKey || !!searchQuery.keyWordConfirm || searchQuery.createDtList.length > 0 || searchQuery.entryDtList.length > 0 || searchQuery.nextDtList.length > 0)\r\n  }\r\n\r\n  //字体个性化\r\n  function getTextFontType(nameTextFontType,type){\r\n    if(!!nameTextFontType){\r\n      if(type == 0){\r\n        if(nameTextFontType.split(',')[0] == eEnableFlg.enable){\r\n          return true\r\n        }\r\n      }\r\n      if(type == 1){\r\n        if(nameTextFontType.split(',')[1] == eEnableFlg.enable){\r\n          return true\r\n        }\r\n      }\r\n      if(type == 2){\r\n        if(nameTextFontType.split(',')[2] == eEnableFlg.enable){\r\n          return true\r\n        }\r\n      }\r\n      if(type == 3){\r\n        if(nameTextFontType.split(',')[3] == eEnableFlg.enable){\r\n          return true\r\n        }\r\n      }\r\n      if(type == 4){\r\n        if(nameTextFontType.split(',')[2] == eEnableFlg.enable && nameTextFontType.split(',')[3] == eEnableFlg.enable){\r\n          return true\r\n        }\r\n      }\r\n    }\r\n    return false\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Layout className=\"subscribe ant-layout-sider-light\" >\r\n        {/* 顶部搜索 */}\r\n        <SysSubscribeHeaderSearch\r\n          allMember={allMember}\r\n          searchQuery={searchQuery}\r\n          setSearchQuery={setSearchQuery}\r\n          typeList={typeList}\r\n          fromType={fromType}\r\n          getData={getData}\r\n        />\r\n        <Layout hasSider style={{ flex: \"auto\" }} className=\"ant-layout-sider-light\">\r\n          {/* 中树 */}\r\n          <SysSubscribeSider\r\n            privWrite={privWrite}\r\n            searchQuery={searchQuery}\r\n            setSearchQuery={setSearchQuery}\r\n            getData={getData}\r\n            loading={loading}\r\n            addSubscribeClick={addSubscribeClick}\r\n            total={total}\r\n            dataSource={dataSource}\r\n            selectedKey={selectedKey}\r\n            isSearch={isSearch}\r\n            getTextFontType={getTextFontType}\r\n          />\r\n          {/* 详情 */}\r\n          <Outlet \r\n            context={{\r\n              dataSource,\r\n              selectedKey,\r\n              allMember,\r\n              getData,\r\n              setOpType,\r\n              setNodeType,\r\n              setEditSubmitFlag,\r\n              setModalVisible,\r\n              setEditData,\r\n              getTextFontType,\r\n              privWrite,\r\n            }}\r\n          />\r\n\r\n          {/* 新建/编辑弹出层 */}\r\n          <SubscribeEdit\r\n            allMember={allMember}\r\n            opType={opType}\r\n            closeDrawer={closeDrawer}\r\n            modalVisible={nodeType == eNodeTypeId.nt_10801_quick_access_sys_subscription_single && modalVisible}\r\n            setModalVisible={setModalVisible}\r\n            editData={editData}\r\n            isChange={isChange}\r\n            setIsChange={setIsChange}\r\n            selectedKey={selectedKey}\r\n            typeList={typeList}\r\n            fromType={fromType}\r\n            ganttName={ganttName}\r\n            nodeType={nodeType}\r\n          />\r\n\r\n          <OpDataPush\r\n            allMember={allMember}\r\n            opType={opType}\r\n            closeDrawer={closeDrawer}\r\n            modalVisible={nodeType == eNodeTypeId.nt_10802_quick_access_sys_subscription_single_datapush && modalVisible}\r\n            setModalVisible={setModalVisible}\r\n            editData={editData}\r\n            isChange={isChange}\r\n            setIsChange={setIsChange}\r\n            selectedKey={selectedKey}\r\n            typeList={typeList}\r\n            fromType={fromType}\r\n            ganttName={ganttName}\r\n            nodeType={nodeType}\r\n          />\r\n        </Layout>\r\n      </Layout>\r\n      {/* 新手引导 */}\r\n      <NoviceGuide nodeType={eNodeTypeId.nt_108_quick_access_sys_subscription} awakeFlg={0} />\r\n    </>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,MAAM;AAC7B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACjE,OAAO,iBAAiB;AACxB,OAAO,KAAKC,eAAe,MAAM,kBAAkB;AACnD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,WAAW,MAAM,yBAAyB,CAAC,CAAC;AACnD,SAASC,kCAAkC,EAAEC,qCAAqC,EAAEC,kCAAkC,QAAQ,6BAA6B;AAC3J,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAOC,wBAAwB,MAAM,2CAA2C;AAChF,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,UAAU,MAAM,yBAAyB;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,QAAQ,GAAG,CACf;EAAEC,GAAG,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAK,CAAC,EACvB;EAAED,GAAG,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAK,CAAC,EACvB;EAAED,GAAG,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAK,CAAC,CACxB;;AAED;AACA,eAAe,SAASC,QAAQA,CAAC;EAACC,QAAQ;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACtD,MAAM;IAAEC,MAAM;IAAEC,MAAM;IAAEC,GAAG,EAAEC;EAAU,CAAC,GAAG1B,SAAS,CAAC,CAAC;EACtD,MAAM2B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAE6B,IAAI,EAAEC,QAAQ;IAAEC,aAAa,EAAEC;EAAkB,CAAC,GAAG3B,kCAAkC,CAACmB,MAAM,EAACH,QAAQ,IAAE,OAAO,CAAC,CAAC,CAAE;EAC5H,MAAM;IAAEQ,IAAI,EAAEI,SAAS;IAAEF,aAAa,EAAEG;EAAkB,CAAC,GAAG5B,qCAAqC,CAACkB,MAAM,EAACC,MAAM,EAAC,CAAC,EAACJ,QAAQ,IAAE,OAAO,CAAC,CAAC,CAAE;;EAEzI,MAAM;IAAEQ,IAAI,EAAE;MAAEM,SAAS;MAAEC;IAAW,CAAC,GAAG,CAAC,CAAC;IAAEC,SAAS,EAAEC,aAAa;IAAEC,OAAO,EAAEC,WAAW;IAAET;EAAc,CAAC,GAAGxB,kCAAkC,CAAC;IAACiB,MAAM;IAAEC;EAAM,CAAC,CAAC;EAEpK,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM,CAAC+C,KAAK,EAACC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EACpC,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmD,WAAW,EAACC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAEnD,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC6D,MAAM,EAAEC,SAAS,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5CiE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,QAAQ,CAAC;EAEjC,MAAM,CAACI,WAAW,EAACC,cAAc,CAAC,GAAGpE,QAAQ,CAAC;IAC5CqE,cAAc,EAAE,IAAI;IACpBC,OAAO,EAAE,IAAI;IACbC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAACC,UAAU,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC5C,MAAM,CAACsF,QAAQ,EAACC,WAAW,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACd,IAAGwB,QAAQ,IAAI,OAAO,EAAC;MACrB,IAAIiE,UAAU,GAAG,EAAE;MACnB,CAACxD,QAAQ,IAAE,EAAE,EAAEyD,OAAO,CAACC,IAAI,IAAI;QAC7B,IAAIC,GAAG,GAAG;UAAEvE,GAAG,EAAEsE,IAAI,CAACE,MAAM;UAAEvE,KAAK,EAAEqE,IAAI,CAACG,QAAQ;UAAEC,MAAM,EAAEJ,IAAI,CAACI,MAAM;UAAEC,KAAK,EAAEL,IAAI,CAACK;QAAM,CAAC;QAC5FP,UAAU,CAACQ,IAAI,CAACL,GAAG,CAAC;MACtB,CAAC,CAAC;MACF/C,YAAY,CAAC,CAAC,GAAG4C,UAAU,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACtD,iBAAiB,CAAC,CAAC;EAEvBnC,SAAS,CAAC,MAAM;IACd,IAAGwB,QAAQ,IAAI,OAAO,EAAC;MACrB,IAAIiE,UAAU,GAAG,EAAE;MACnB,CAACrD,SAAS,IAAE,EAAE,EAAEsD,OAAO,CAACC,IAAI,IAAI;QAC9B,IAAIC,GAAG,GAAG;UAAEvE,GAAG,EAAEsE,IAAI,CAACE,MAAM;UAAEvE,KAAK,EAAEqE,IAAI,CAACG,QAAQ;UAAEC,MAAM,EAAEJ,IAAI,CAACI,MAAM;UAAEC,KAAK,EAAEL,IAAI,CAACK;QAAM,CAAC;QAC5FP,UAAU,CAACQ,IAAI,CAACL,GAAG,CAAC;MACtB,CAAC,CAAC;MACF/C,YAAY,CAAC,CAAC,GAAG4C,UAAU,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACpD,kBAAkB,CAAC,CAAC;EAExBrC,SAAS,CAAC,MAAM;IACd,IAAI4C,SAAS,CAACsD,MAAM,GAAG,CAAC,IAAI5C,UAAU,IAAII,cAAc,IAAI,CAAC,IAAI,CAACU,WAAW,CAACe,QAAQ,EAAE;MACtF,IAAGf,WAAW,CAACY,MAAM,IAAItB,cAAc,IAAI,CAAC,EAAC;QAC3CyC,OAAO,CAAC,CAAC,EAAC,KAAK,CAAC;MAClB,CAAC,MAAI;QACHA,OAAO,CAAC,CAAC,EAAC,IAAI,CAAC;MACjB;IACF;EACF,CAAC,EAAE,CAACvD,SAAS,EAAEU,UAAU,EAAEI,cAAc,EAAE0C,IAAI,CAACC,SAAS,CAACjC,WAAW,CAAC,CAAC,CAAC;EAExEpE,SAAS,CAAC,MAAI;IACZ,IAAG,CAAC,CAAC8B,SAAS,EAAC;MACbwE,SAAS,CAAC,CAAC;MACXjD,cAAc,CAACvB,SAAS,CAAC;IAC3B,CAAC,MAAI;MACHuB,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAC,CAACvB,SAAS,CAAC,CAAC;EAEd,SAASwE,SAASA,CAAA,EAAE;IAClB,IAAIC,MAAM,GAAG;MACX5E,MAAM;MACNC,MAAM;MACN4E,UAAU,EAAE1E,SAAS;MACrB2E,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAEtC,WAAW,CAACS,SAAS;MAC5B8B,IAAI,EAAEvC,WAAW,CAACU;IACpB,CAAC;IACDzE,eAAe,CAACuG,kCAAkC,CAACL,MAAM,CAAC,CAACM,IAAI,CAACC,GAAG,IAAI;MACrE,IAAGA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAC;QACvB1C,cAAc,CAAC;UACb,GAAGD,WAAW;UACdW,OAAO,EAAE+B,GAAG,CAAC/B;QACf,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,eAAeoB,OAAOA,CAACa,IAAI,EAACC,aAAa,EAAC;IACxC,IAAGA,aAAa,EAAC;MACf3B,UAAU,CAAC,IAAI,CAAC;IAClB;IACA,IAAIiB,MAAM,GAAG;MACX5E,MAAM;MACNC,MAAM;MACNmD,OAAO,EAAEX,WAAW,CAACW,OAAO;MAC5B0B,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAEtC,WAAW,CAACS,SAAS;MAC5B8B,IAAI,EAAEvC,WAAW,CAACU;IACpB,CAAC;IACD,IAAI,CAAC,CAACV,WAAW,CAACE,cAAc,EAAE;MAChCiC,MAAM,CAACW,UAAU,GAAG9C,WAAW,CAACE,cAAc;IAChD;IACA,IAAI,CAAC,CAACF,WAAW,CAACG,OAAO,EAAE;MACzBgC,MAAM,CAACY,OAAO,GAAG/C,WAAW,CAACG,OAAO;IACtC;IACA,IAAI,CAAC,CAACH,WAAW,CAACI,cAAc,EAAE;MAChC+B,MAAM,CAACa,QAAQ,GAAGhD,WAAW,CAACI,cAAc;IAC9C;IACA,IAAI,CAAC,CAACJ,WAAW,CAACK,YAAY,EAAE;MAC9B8B,MAAM,CAACc,KAAK,GAAGjD,WAAW,CAACK,YAAY;IACzC;IACA,IAAIL,WAAW,CAACM,YAAY,CAACwB,MAAM,IAAI,CAAC,EAAE;MACxCK,MAAM,CAACe,aAAa,GAAGlD,WAAW,CAACM,YAAY,CAAC,CAAC,CAAC,GAAG,WAAW;MAChE6B,MAAM,CAACgB,WAAW,GAAGnD,WAAW,CAACM,YAAY,CAAC,CAAC,CAAC,GAAG,WAAW;IAChE;IACA,IAAIN,WAAW,CAACO,WAAW,CAACuB,MAAM,KAAK,CAAC,EAAE;MACxCK,MAAM,CAACiB,iBAAiB,GAAGpD,WAAW,CAACO,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW;MACnE4B,MAAM,CAACkB,eAAe,GAAGrD,WAAW,CAACO,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW;IACnE;IACA,IAAIP,WAAW,CAACQ,UAAU,CAACsB,MAAM,KAAK,CAAC,EAAE;MACvCK,MAAM,CAACmB,eAAe,GAAGtD,WAAW,CAACQ,UAAU,CAAC,CAAC,CAAC,GAAG,WAAW;MAChE2B,MAAM,CAACoB,aAAa,GAAGvD,WAAW,CAACQ,UAAU,CAAC,CAAC,CAAC,GAAG,WAAW;IAChE;IACA,MAAMvE,eAAe,CAACuH,gCAAgC,CAACrB,MAAM,CAAC,CAACM,IAAI,CAAEC,GAAG,IAAK;MAC3E,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QAC1B9D,QAAQ,CAAC6D,GAAG,CAACe,UAAU,CAAC;QACxB,IAAIC,QAAQ,GAAG,CAAChB,GAAG,CAACiB,aAAa,IAAE,EAAE,EAAEC,GAAG,CAACrC,IAAI,IAAI;UACjD,IAAIsC,WAAW,GAAGrF,SAAS,CAACsF,IAAI,CAACC,OAAO,IAAIA,OAAO,CAAC9G,GAAG,KAAKsE,IAAI,CAACuB,UAAU,CAAC;UAC5EvB,IAAI,CAACtE,GAAG,GAAGsE,IAAI,CAACyC,EAAE;UAClBzC,IAAI,CAACI,MAAM,GAAGkC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAElC,MAAM;UACjC,OAAOJ,IAAI;QACb,CAAC,CAAC;QACF,IAAGqB,IAAI,IAAI,CAAC,EAAC;UACX,IAAGc,QAAQ,CAAC5B,MAAM,GAAG,CAAC,KAAKmC,QAAQ,CAAC,CAAC,IAAI,CAACvG,SAAS,IAAIsC,WAAW,CAACa,UAAU,IAAIb,WAAW,CAACc,WAAW,IAAId,WAAW,CAACgB,OAAO,IAAI5B,WAAW,CAAC,EAAC;YAC9I,IAAGsE,QAAQ,CAAC,CAAC,CAAC,CAAC9D,QAAQ,IAAI1D,WAAW,CAACgI,6CAA6C,EAAC;cACnFvG,QAAQ,CAAC,aAAa+F,QAAQ,CAAC,CAAC,CAAC,CAACM,EAAE,EAAE,CAAC;YACzC,CAAC,MAAM;cACLrG,QAAQ,CAAC,YAAY+F,QAAQ,CAAC,CAAC,CAAC,CAACM,EAAE,EAAE,CAAC;YACxC;UACF;QACF,CAAC,MAAI;UACH,IAAG,CAAChF,WAAW,IAAI0E,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAC;YACrC,IAAG4B,QAAQ,CAAC,CAAC,CAAC,CAAC9D,QAAQ,IAAI1D,WAAW,CAACgI,6CAA6C,EAAC;cACnFvG,QAAQ,CAAC,aAAa+F,QAAQ,CAAC,CAAC,CAAC,CAACM,EAAE,EAAE,CAAC;YACzC,CAAC,MAAM;cACLrG,QAAQ,CAAC,YAAY+F,QAAQ,CAAC,CAAC,CAAC,CAACM,EAAE,EAAE,CAAC;YACxC;UACF;QACF;QACA,IAAGN,QAAQ,CAAC5B,MAAM,IAAI,CAAC,EAAC;UACtB7C,cAAc,CAAC,IAAI,CAAC;UACpBtB,QAAQ,CAAC,EAAE,CAAC;QACd;QACAoB,aAAa,CAAC,CAAC,GAAG2E,QAAQ,CAAC,CAAC;MAC9B,CAAC,MAAM;QACL7E,QAAQ,CAAC,CAAC,CAAC;QACXE,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC,CAAC;IACFmC,UAAU,CAAC,KAAK,CAAC;IACjB7B,cAAc,CAAC,KAAK,CAAC;IACrB,IAAGwD,aAAa,EAAC;MACftD,iBAAiB,CAAC,CAAC,CAAC;IACtB;EACF;;EAEA;EACA,SAAS4E,iBAAiBA,CAACvE,QAAQ,EAAC;IAClCE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,QAAQ,CAAC;IACjCD,SAAS,CAAC,CAAC,CAAC;IACZE,WAAW,CAACD,QAAQ,CAAC;IACrBT,aAAa,CAAC,KAAK,CAAC;IACpBR,eAAe,CAAC,IAAI,CAAC;IACrBc,WAAW,CAAC,IAAI,CAAC;EACnB;EAAC;;EAED;EACA,SAAS2E,WAAWA,CAACC,EAAE,EAAC7C,GAAG,EAAC;IAC1B,IAAIT,QAAQ,GAAG,KAAK;IACpB,IAAGsD,EAAE,IAAI,CAAC,EAAC;MACT,IAAG,CAAC,CAAC7C,GAAG,EAAC;QACP,OAAOJ,WAAW,CAAC,IAAI,CAAC;MAC1B;MACAL,QAAQ,GAAG,IAAI;IACjB;IACAK,WAAW,CAAC,KAAK,CAAC;IAClB,IAAG,CAAClC,UAAU,EAAC;MACb,IAAGmF,EAAE,IAAI,CAAC,EAAC;QACTpE,cAAc,CAAC;UAAC,GAAGD,WAAW;UAACY,MAAM,EAAC,KAAK;UAACC,UAAU,EAAC,KAAK;UAACC,WAAW,EAAC,KAAK;UAACC,QAAQ,EAACA,QAAQ;UAACJ,OAAO,EAAE;QAAC,CAAC,CAAC;QAC7GtB,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAI;QACHY,cAAc,CAAC;UAAC,GAAGD,WAAW;UAACY,MAAM,EAAC,KAAK;UAACC,UAAU,EAAC,KAAK;UAACC,WAAW,EAAC,KAAK;UAACC,QAAQ,EAACA;QAAQ,CAAC,CAAC;MACpG;MACA5B,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAI;MACHc,cAAc,CAAC;QAAC,GAAGD,WAAW;QAACY,MAAM,EAAC,KAAK;QAACC,UAAU,EAAC,KAAK;QAACC,WAAW,EAAC,KAAK;QAACC,QAAQ,EAACA;MAAQ,CAAC,CAAC;IACpG;IACA,IAAGzB,cAAc,IAAI,CAAC,EAAC;MACrBC,iBAAiB,CAAC,CAAC,CAAC;IACtB;IACAZ,eAAe,CAAC,KAAK,CAAC;EACxB;;EAEA;EACA,SAASsF,QAAQA,CAAA,EAAE;IACjB,OAAQ,CAAC,CAACjE,WAAW,CAACE,cAAc,IAAI,CAAC,CAACF,WAAW,CAACG,OAAO,IAAI,CAAC,CAACH,WAAW,CAACK,YAAY,IAAI,CAAC,CAACL,WAAW,CAACI,cAAc,IAAIJ,WAAW,CAACM,YAAY,CAACwB,MAAM,GAAG,CAAC,IAAI9B,WAAW,CAACO,WAAW,CAACuB,MAAM,GAAG,CAAC,IAAI9B,WAAW,CAACQ,UAAU,CAACsB,MAAM,GAAG,CAAC;EAC/O;;EAEA;EACA,SAASwC,eAAeA,CAACC,gBAAgB,EAAC3B,IAAI,EAAC;IAC7C,IAAG,CAAC,CAAC2B,gBAAgB,EAAC;MACpB,IAAG3B,IAAI,IAAI,CAAC,EAAC;QACX,IAAG2B,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIjI,UAAU,CAACkI,MAAM,EAAC;UACrD,OAAO,IAAI;QACb;MACF;MACA,IAAG7B,IAAI,IAAI,CAAC,EAAC;QACX,IAAG2B,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIjI,UAAU,CAACkI,MAAM,EAAC;UACrD,OAAO,IAAI;QACb;MACF;MACA,IAAG7B,IAAI,IAAI,CAAC,EAAC;QACX,IAAG2B,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIjI,UAAU,CAACkI,MAAM,EAAC;UACrD,OAAO,IAAI;QACb;MACF;MACA,IAAG7B,IAAI,IAAI,CAAC,EAAC;QACX,IAAG2B,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIjI,UAAU,CAACkI,MAAM,EAAC;UACrD,OAAO,IAAI;QACb;MACF;MACA,IAAG7B,IAAI,IAAI,CAAC,EAAC;QACX,IAAG2B,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIjI,UAAU,CAACkI,MAAM,IAAIF,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIjI,UAAU,CAACkI,MAAM,EAAC;UAC5G,OAAO,IAAI;QACb;MACF;IACF;IACA,OAAO,KAAK;EACd;EAEA,oBACE5H,OAAA,CAAAE,SAAA;IAAA2H,QAAA,gBACE7H,OAAA,CAACnB,MAAM;MAACiJ,SAAS,EAAC,kCAAkC;MAAAD,QAAA,gBAElD7H,OAAA,CAACL,wBAAwB;QACvBgC,SAAS,EAAEA,SAAU;QACrBwB,WAAW,EAAEA,WAAY;QACzBC,cAAc,EAAEA,cAAe;QAC/BjD,QAAQ,EAAEA,QAAS;QACnBI,QAAQ,EAAEA,QAAS;QACnB2E,OAAO,EAAEA;MAAQ;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACFlI,OAAA,CAACnB,MAAM;QAACsJ,QAAQ;QAACC,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAO,CAAE;QAACP,SAAS,EAAC,wBAAwB;QAAAD,QAAA,gBAE1E7H,OAAA,CAACJ,iBAAiB;UAChByB,SAAS,EAAEA,SAAU;UACrB8B,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/B8B,OAAO,EAAEA,OAAQ;UACjBd,OAAO,EAAEA,OAAQ;UACjBkD,iBAAiB,EAAEA,iBAAkB;UACrCvF,KAAK,EAAEA,KAAM;UACbE,UAAU,EAAEA,UAAW;UACvBE,WAAW,EAAEA,WAAY;UACzBiF,QAAQ,EAAEA,QAAS;UACnBK,eAAe,EAAEA;QAAgB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEFlI,OAAA,CAACf,MAAM;UACLqJ,OAAO,EAAE;YACPrG,UAAU;YACVE,WAAW;YACXR,SAAS;YACTuD,OAAO;YACPpC,SAAS;YACTE,WAAW;YACXN,iBAAiB;YACjBZ,eAAe;YACfc,WAAW;YACX6E,eAAe;YACfpG;UACF;QAAE;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGFlI,OAAA,CAACH,aAAa;UACZ8B,SAAS,EAAEA,SAAU;UACrBkB,MAAM,EAAEA,MAAO;UACf0E,WAAW,EAAEA,WAAY;UACzB1F,YAAY,EAAEkB,QAAQ,IAAI1D,WAAW,CAACgI,6CAA6C,IAAIxF,YAAa;UACpGC,eAAe,EAAEA,eAAgB;UACjCa,QAAQ,EAAEA,QAAS;UACnB2B,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA,WAAY;UACzBpC,WAAW,EAAEA,WAAY;UACzBhC,QAAQ,EAAEA,QAAS;UACnBI,QAAQ,EAAEA,QAAS;UACnBC,SAAS,EAAEA,SAAU;UACrBuC,QAAQ,EAAEA;QAAS;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEFlI,OAAA,CAACF,UAAU;UACT6B,SAAS,EAAEA,SAAU;UACrBkB,MAAM,EAAEA,MAAO;UACf0E,WAAW,EAAEA,WAAY;UACzB1F,YAAY,EAAEkB,QAAQ,IAAI1D,WAAW,CAACkJ,sDAAsD,IAAI1G,YAAa;UAC7GC,eAAe,EAAEA,eAAgB;UACjCa,QAAQ,EAAEA,QAAS;UACnB2B,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA,WAAY;UACzBpC,WAAW,EAAEA,WAAY;UACzBhC,QAAQ,EAAEA,QAAS;UACnBI,QAAQ,EAAEA,QAAS;UACnBC,SAAS,EAAEA,SAAU;UACrBuC,QAAQ,EAAEA;QAAS;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAETlI,OAAA,CAACV,WAAW;MAACyD,QAAQ,EAAE1D,WAAW,CAACmJ,oCAAqC;MAACC,QAAQ,EAAE;IAAE;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACxF,CAAC;AAEP;AAACzH,EAAA,CAzVuBH,QAAQ;EAAA,QACanB,SAAS,EACnCD,WAAW,EAEiCK,kCAAkC,EACjCC,qCAAqC,EAEaC,kCAAkC;AAAA;AAAAiJ,EAAA,GAP5HpI,QAAQ;AAAA,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}