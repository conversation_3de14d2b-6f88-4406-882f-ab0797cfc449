import React, { useState, useEffect, useRef } from "react";
import { Layout, Drawer, Button, Space, Tabs } from "antd";
import { useParams, Link, useLocation, useNavigate } from "react-router-dom";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import { eMembergrpType } from "@common/utils/enum";
import CreateInvitedMember from "src/settings/utils/CreateInvitedMember/CreateInvitedMember";
import SettingsSideBar from "./SettingsSideBar";

// 导入设置页面组件
import BasicInfoTab from "./basicInfo/BasicInfoTab";
import TeamDomainSmtpTab from "./basicInfo/SmtpTab";
import Personal_Tab_Subscribe from "./personal/Manager_Tab_Subscribe";
import Personal_Tab_Data_Import from "./personal/Personal_Tab_Data_Import";
import Settings_Product from "./product/Settings_Product";
import MemberTab from "./user/MemberTab";
import AdminTab from "./user/AdminTab";
import InviteHistory from "./user/InviteHistory";
import Personal from "./personal/Personal";
import SpaceRoleTab from "./space/SpaceRoleTab";
import SpaceTrashTab from "./space/SpaceTrashTab";
import SpaceShareLinkHistoryTab from "./space/SpaceShareLinkHistoryTab";
import MemberTab_Approval from "./user/MemberTab_Approval";
import DraggablePopUp from '@components/DraggablePopUp';
import DraggableDrawer from '@common/components/DraggableDrawer';
import { useQuerySetting327_getTeamSpaceAadmin } from "@common/service/commonHooks";

import "./SettingsDrawer.scss";

const { Sider, Content } = Layout;
const { TabPane } = Tabs;

export default function SettingsDrawer({ visible, onClose, teamId, defaultTab,productId }) { //productId: 是打开"应用管理"某一个产品的的授权对话框
  const [approvalNum, setApprovalNum] = useState(0);
  const [createInvitedMemberVisible, setCreateInvitedMemberVisible] = useState(false);
  const [currentContent, setCurrentContent] = useState(defaultTab || 'basic');
  const [currentSpaceId, setCurrentSpaceId] = useState(null);
  const [activeTab, setActiveTab] = useState('base-info');
  const [closeConfirmVisible, setCloseConfirmVisible] = useState(false);
  // 新增：用于高亮“申请加入”分组
  const [approvalGroupId, setApprovalGroupId] = useState(null);
  const [approvalGroupType, setApprovalGroupType] = useState(null);

  // 新增：动态设置浏览器标题
  const prevTitle = useRef(document.title);
  useEffect(() => {
    if (visible) {
      prevTitle.current = document.title;
      document.title = "团队设置";
    } else {
      document.title = prevTitle.current;
    }
    return () => {
      document.title = prevTitle.current;
    };
  }, [visible]);

  // 监听defaultTab变化，动态切换tab
  useEffect(() => {
    if (visible && defaultTab) {
      setCurrentContent(defaultTab);
      // 根据tab类型设置activeTab
      if (defaultTab === 'basic') setActiveTab('base-info');
      if (defaultTab === 'user') setActiveTab('member');
      if (defaultTab === 'product') setActiveTab('product');
    }
  }, [visible, defaultTab]);

  const state = useSelector((state) => ({
    userGrpList: state.getIn(["workSetUp", "userGrpList"]),
  }), shallowEqual);
  const userGrpList = state.userGrpList || [];
  //const { spaceId } = useParams();
  // 提前解构，避免未初始化时被访问
  const { data: { teamAdminFlag: isManager, spaceAdminFlag: isSpaceManager, teamSpaceAdminFlag } //teamSpaceAdminFlag表示，只要是团队中的“某一个群”的管理员，它就为true
    = { teamAdminFlag: undefined, spaceAdminFlag: undefined, teamSpaceAdminFlag: undefined },
  } = useQuerySetting327_getTeamSpaceAadmin({ teamId, spaceId: currentSpaceId, enabled: true });

  // 自动切换Tab副作用，避免在renderContent中setState
  useEffect(() => {
    if (
      currentContent === 'space' &&
      !(isManager || isSpaceManager || teamSpaceAdminFlag) &&
      activeTab !== 'member'
    ) {
      setActiveTab('member');
    }
  }, [currentContent, isManager, isSpaceManager, teamSpaceAdminFlag, activeTab]);

  // 设置申请加入数量
  useEffect(() => {
    console.log('SettingsDrawer: userGrpList changed:', userGrpList);
    if ((userGrpList || []).length > 0) {
      const approvalCount = userGrpList.find(e => e.membergrpType === eMembergrpType.grp_2_apply_join)?.membergrpNum || 0;
      console.log('SettingsDrawer: setting approvalNum to:', approvalCount);
      setApprovalNum(approvalCount);
    }
  }, [userGrpList]);

  // 处理申请加入点击 - 显示申请列表并高亮分组
  const handleApprovalClick = () => {
    setCurrentContent('user');
    // 找到“申请加入”分组
    const approvalGroup = userGrpList.find(item => item.membergrpType === 2); // eMembergrpType.grp_2_apply_join = 2
    if (approvalGroup) {
      setApprovalGroupId(approvalGroup.groupId);
      setApprovalGroupType(approvalGroup.membergrpType);
    }
  };

  // 处理SettingsSideBar的内容变化
  const handleContentChange = (content, spaceId = null, tab = null) => {
    console.log('SettingsDrawer: content changed to', content, spaceId, tab);
    setCurrentContent(content);
    setCurrentSpaceId(spaceId);
    if (tab) {
      setActiveTab(tab);
    } else if (content === 'basic') {
      // 当选择"基础设置"时，默认选中"基本设置"Tab
      setActiveTab('base-info');
    } else if (content === 'user') {
      // 当选择"成员及管理员"时，默认选中"成员"Tab
      setActiveTab('member');
    } else if (content === 'space') {
      // 当选择协作群时，默认选中"角色权限"Tab
      setActiveTab('role');
    }
  };

  // 渲染右侧内容
  const renderContent = () => {
    switch (currentContent) {
      case 'basic':
        return (
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="基本设置" key="base-info">
              <BasicInfoTab teamId={teamId} />
            </TabPane>
            <TabPane tab="域名&SMTP服务器" key="smtp">
              <TeamDomainSmtpTab teamId={teamId} />
            </TabPane>
            <TabPane tab="系统推送设置" key="subscribe">
              <Personal_Tab_Subscribe teamId={teamId} />
            </TabPane>
            <TabPane tab="数据导入" key="import">
              <Personal_Tab_Data_Import teamId={teamId} />
            </TabPane>
          </Tabs>
        );
      case 'product':
        return <Settings_Product teamId={teamId} productId={productId} visible={visible}/>;
      case 'user':
        return (
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="成员" key="member">
              <MemberTab
                teamId={teamId}
                spaceId={currentSpaceId}
                isInDrawer={true}
                initialGroupId={approvalGroupId || (userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)?.groupId)}
                initialMembergrpType={approvalGroupType || (userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)?.membergrpType)}
              />
            </TabPane>
            <TabPane tab="管理员" key="admin">
              <AdminTab teamId={teamId} spaceId={currentSpaceId} isInDrawer={true} />
            </TabPane>
            <TabPane tab="邀请历史" key="invite">
              <InviteHistory teamId={teamId} spaceId={currentSpaceId} />
            </TabPane>
          </Tabs>
        );
      case 'personal':
        return <Personal teamId={teamId} />;
      case 'space':
        // 非管理员只显示成员Tab
        if (!(isManager || isSpaceManager || teamSpaceAdminFlag)) {
          const allMemberGroup = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members);
          return (
            <Tabs activeKey={'member'} onChange={setActiveTab}>
              <TabPane tab="成员" key="member">
                <MemberTab
                  teamId={teamId}
                  spaceId={currentSpaceId}
                  isInDrawer={true}
                  initialGroupId={allMemberGroup?.groupId}
                  initialMembergrpType={allMemberGroup?.membergrpType}
                />
              </TabPane>
            </Tabs>
          );
        }
        // 管理员显示全部Tab
        return (
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="角色权限" key="role">
              <SpaceRoleTab teamId={teamId} spaceId={currentSpaceId} isInDrawer={true} />
            </TabPane>
            <TabPane tab="成员" key="member">
              <MemberTab 
                teamId={teamId} 
                spaceId={currentSpaceId} 
                isInDrawer={true}
                initialGroupId={userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)?.groupId}
                initialMembergrpType={userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)?.membergrpType}
              />
            </TabPane>
            <TabPane tab="回收站" key="trash">
              <SpaceTrashTab teamId={teamId} spaceId={currentSpaceId} />
            </TabPane>
            <TabPane tab="外链分享" key="shareLinkHistory">
              <SpaceShareLinkHistoryTab teamId={teamId} spaceId={currentSpaceId} />
            </TabPane>
            <TabPane tab="邀请历史" key="invite">
              <InviteHistory teamId={teamId} spaceId={currentSpaceId} />
            </TabPane>
          </Tabs>
        );
      case 'approval':
        const approvalGroup = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_2_apply_join);
        if (approvalGroup) {
          return <MemberTab_Approval 
            teamId={teamId} 
            spaceId={null} 
            selectedGroupId={approvalGroup.groupId}
            selectedMembergrpType={approvalGroup.membergrpType}
          />;
        } else {
          return <div>未找到申请加入分组</div>;
        }
      default:
        return <div>请选择设置项</div>;
    }
  };

  // 新的关闭逻辑
  const handleDrawerClose = () => {
    setCloseConfirmVisible(true);
  };
  const handleCloseConfirm = () => {
    setCloseConfirmVisible(false);
    onClose && onClose();
  };

  return (
    <>
      <DraggableDrawer
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>团队设置</span>
              <Space size={20}>
              {!!approvalNum && (
                <Button 
                  type="link" 
                  onClick={handleApprovalClick}
                  style={{ padding: 0 }}
                >
                  申请加入({approvalNum})
                </Button>
              )}
                {/*tmsbug-12931 非管理员可以看到 邀请成员按钮 https://os.iteam.com/#/team/9176631781/issues/3914480416/issue/103097772323736*/}
              {(isManager || isSpaceManager || teamSpaceAdminFlag) && ( // 团队管理员 && 当前群管理员 &&   团队中某一个群管理员即为true
                <Button type="primary" className="defaultBtn" onClick={()=>setCreateInvitedMemberVisible(true)}>邀请成员</Button>
              )}
              </Space>
          </div>
        }
        placement="right"
        width="85%"
        minWidth="40%"
        maxWidth="95%"
        draggableFlag={true}
        open={visible}
        onClose={handleDrawerClose}
        className="settings-drawer"
      >
        <Layout className="WorkSetUp-layout">
          <Sider width={260} className="WorkSetUp-layout-sider">
            <SettingsSideBar 
              teamId={teamId} 
              isInDrawer={true}
              onContentChange={handleContentChange}
              activeKey={currentContent}
            />
          </Sider>
          <Content className="WorkSetUp-layout-content">
            <div className="WorkSetUp-layout-content-inner" style={{ padding: '20px 20px' }}>
              {renderContent()}
            </div>
          </Content>
        </Layout>
        <CreateInvitedMember addPopVisible={createInvitedMemberVisible} onClose={()=>setCreateInvitedMemberVisible(false)}/>
      </DraggableDrawer>
      <DraggablePopUp
        className="settings-drawer-close-confirm"
        title="提示"
        icon="warning"
        width={300}
        destroyOnClose={true}
        open={closeConfirmVisible}
        onOk={handleCloseConfirm}
        onCancel={() => setCloseConfirmVisible(false)}
        content={<span>是否确定关闭团队设置对话框?</span>}
      />
    </>
  );
} 