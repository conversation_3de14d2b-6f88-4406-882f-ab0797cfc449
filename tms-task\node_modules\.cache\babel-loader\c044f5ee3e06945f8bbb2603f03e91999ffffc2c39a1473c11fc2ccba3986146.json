{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\ContextBoard.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { CheckOutlined, RightOutlined } from '@ant-design/icons';\nimport { team_036_get_node_ctx_options } from \"@common/api/http\";\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\nimport { useQuerySetting327_getTeamSpaceAadmin, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\nimport { isEmpty, treeToArray, getSysIconList, assembleGroup } from \"@common/utils/ArrayUtils\";\nimport { eCtxOptionType, eEnableFlg, eProductId } from \"@common/utils/enum\";\nimport { eCtxTypeId, eMenuStatus, getCtxIconByType, getMenuStatusIcon, eNameTextFontType } from \"@common/utils/TsbConfig\";\nimport { expiredModal, resourceMaxModal, unVipModal } from \"@common/utils/ViewUtils\";\nimport { Checkbox, Space, Skeleton, Modal } from \"antd\";\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from \"react\";\nimport { Item as CtxMenuItem, Menu as CtxMenu, Submenu as CtxSubMenu, useContextMenu, Separator } from \"react-contexify\";\nimport { formatSvg } from \"@common/utils/ViewUtils\";\nimport \"react-contexify/ReactContexify.css\";\nimport { useMutation } from '@tanstack/react-query';\nimport \"./ContextBoard.scss\";\nimport * as https from \"@common/api/http\";\nimport { setting_320_get_node_priv_query } from \"@common/api/query/query_setting\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\n\n// 图标颜色\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ColorSelectedMenu({\n  selectValue,\n  onChange,\n  colorOptionsList,\n  actionType\n}) {\n  const _onChanged = checkedValue => {\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\"; // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\n    onChange && onChange({\n      id: actionType + \"&\" + value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"inline-block\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n      value: [selectValue],\n      onChange: _onChanged,\n      children: colorOptionsList.map(item => /*#__PURE__*/_jsxDEV(Checkbox, {\n        name: \"xxx\",\n        value: item.type.toString(),\n        className: item.className\n      }, item.type.toString(), false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 37\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 10\n  }, this);\n}\n\n// 标题颜色\n_c = ColorSelectedMenu;\nfunction TitleSelectedMenu({\n  selectValue,\n  onChange,\n  colorOptionsList,\n  actionType\n}) {\n  const _onChanged = checkedValue => {\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\"; // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\n    onChange && onChange({\n      id: actionType + \"&\" + value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"inline-block\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n      value: [selectValue],\n      onChange: _onChanged,\n      children: colorOptionsList.map(item => /*#__PURE__*/_jsxDEV(Checkbox, {\n        name: \"xxx\",\n        value: item.type.toString(),\n        className: item.className,\n        children: selectValue == item.type ? \"\" : item.title\n      }, item.type.toString(), false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 37\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 10\n  }, this);\n}\n\n// 字体个性化设置，前段需要固定识别，存储的是type值\n_c2 = TitleSelectedMenu;\nfunction FontSelectedMenu({\n  onChange,\n  optionList,\n  actionType\n}) {\n  const _onChanged = checkedValue => {\n    onChange && onChange({\n      id: actionType + \"&\" + checkedValue\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Space, {\n    size: 10,\n    children: optionList.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => _onChanged(option === null || option === void 0 ? void 0 : option.type),\n      className: option.className,\n      children: formatSvg(option.icon)\n    }, option === null || option === void 0 ? void 0 : option.type, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 10\n  }, this);\n}\n\n// 图标设置，无需固定，有多少显示多少，存储的是value值\n_c3 = FontSelectedMenu;\nfunction IconSelectedMenu({\n  onChange,\n  optionList,\n  actionType\n}) {\n  const _onChanged = checkedValue => {\n    onChange && onChange({\n      id: actionType + \"&\" + checkedValue\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Space, {\n    size: 10,\n    children: optionList.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => _onChanged(option === null || option === void 0 ? void 0 : option.value),\n      className: option.className,\n      children: formatSvg(option.icon)\n    }, option === null || option === void 0 ? void 0 : option.type, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 10\n  }, this);\n}\n\n/**\r\n * @description 文档图标\r\n */\n/*function TitleIconSelectedMenu({ selectValue, onChange, colorOptionsList }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: eCtxTypeId.ctx_42_set_icon + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n  <Checkbox.Group value={[selectValue]} onChange={_onChanged}>\r\n    {colorOptionsList.map(option => (\r\n      <Checkbox\r\n        key={option.value?.toString()} \r\n        value={option.value?.toString()}\r\n        className=\"checkbox-bage\"\r\n      >\r\n        <div className=\"checkbox-bage-icon\">\r\n          {formatSvg(option.icon)}\r\n        </div>\r\n      </Checkbox>\r\n    ))}\r\n  </Checkbox.Group>\r\n</div>\r\n}*/\n\n/*// checkbox 选中状态\r\nconst getCheckboxItem = (flag, label, color, className, actionType) => {\r\n  return <div style={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"baseline\" }}>\r\n    {/!* tmsbug-2622：删除线文案本身添加删除线 *!/}\r\n    <span style={{ color: color }} className={`${actionType == eCtxTypeId.ctx_46_text_font ? \"tree-dir-title-delete\" : \"\"}`}>{label}</span>\r\n    <Checkbox checked={flag} className={className} />\r\n  </div>\r\n}*/\n\n// 收藏选中状态\n_c4 = IconSelectedMenu;\nconst getCheckItem = (flag, label, color) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      color\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 5\n    }, this), flag ? /*#__PURE__*/_jsxDEV(CheckOutlined, {\n      style: {\n        color: \"#70b603\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 10\n  }, this);\n};\n\n// 自定义延迟子菜单组件\nconst DelayedCtxSubMenu = ({\n  children,\n  label,\n  arrow,\n  ...props\n}) => {\n  _s();\n  const timeoutRef = useRef(null);\n  const containerRef = useRef(null);\n  React.useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n    const handleMouseEnter = () => {\n      // 清除隐藏定时器\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n        timeoutRef.current = null;\n      }\n    };\n    const handleMouseLeave = () => {\n      // 设置延迟隐藏定时器\n      timeoutRef.current = setTimeout(() => {\n        // 查找子菜单元素并隐藏\n        const subMenu = container.querySelector('[role=\"menu\"]');\n        if (subMenu) {\n          subMenu.style.display = 'none';\n        }\n      }, 1000); // 1秒延迟\n    };\n\n    // 为容器添加事件监听器\n    container.addEventListener('mouseenter', handleMouseEnter);\n    container.addEventListener('mouseleave', handleMouseLeave);\n\n    // 为子菜单添加事件监听器\n    const observer = new MutationObserver(mutations => {\n      mutations.forEach(mutation => {\n        mutation.addedNodes.forEach(node => {\n          if (node.nodeType === Node.ELEMENT_NODE && node.getAttribute('role') === 'menu') {\n            // 找到子菜单，为其添加鼠标事件\n            node.addEventListener('mouseenter', handleMouseEnter);\n            node.addEventListener('mouseleave', handleMouseLeave);\n\n            // 重置显示状态\n            node.style.display = '';\n          }\n        });\n      });\n    });\n    observer.observe(document.body, {\n      childList: true,\n      subtree: true\n    });\n    return () => {\n      container.removeEventListener('mouseenter', handleMouseEnter);\n      container.removeEventListener('mouseleave', handleMouseLeave);\n      observer.disconnect();\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    children: /*#__PURE__*/_jsxDEV(CtxSubMenu, {\n      ...props,\n      label: label,\n      arrow: arrow,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n};\n\n/**\r\n * 右击菜单 \r\n * @param teamId 团队Id\r\n * @param onMoreBtnClick 非新建操作回调\r\n * @param onCreateBtnClick 新建等操作回调\r\n * @param id 菜单id\r\n * @param callbackParams 是 onMoreBtnClick 和 onCreateBtnClick 回调返回参数\r\n */\n_s(DelayedCtxSubMenu, \"gyuwRsU9J9xwqU5crNNNsl0SKOA=\");\n_c5 = DelayedCtxSubMenu;\nfunction ContextBoard({\n  teamId,\n  onMoreBtnClick,\n  onCreateBtnClick,\n  id,\n  handleOnVisibilityChange,\n  ...callbackParams\n}, ref) {\n  _s2();\n  // const [createTypeList, setCreateTypeList] = useState([]); // tree right click context menu\n  // const [nodeData,setNodeData] = useState(null); \n  const {\n    data: {\n      userId,\n      teamAdminFlag: managerFlag\n    } = {\n      userId: undefined,\n      teamAdminFlag: undefined\n    }\n  } = useQuerySetting327_getTeamSpaceAadmin({\n    teamId,\n    enabled: true\n  }); // 判断登录人员是否是团队管理员\n  const {\n    data: selectionList\n  } = useQuerySetting407_getCodeValueList(teamId); //字典数据\n\n  const {\n    isLoading: isCtxLoading,\n    data: createTypeList = [],\n    mutateAsync\n  } = useMutation({\n    mutationFn: ({\n      nodeData,\n      nodeId,\n      filterActionTypes,\n      ctxOptionList,\n      nodeType\n    }) => loadContextMenuList(nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType)\n  });\n  const setting334Mutation = useMutation({\n    mutationFn: https.setting_334_apply_authorization\n  });\n\n  //产品申请开通授权\n  const applyProductAuthorize = productId => {\n    if (!!productId) {\n      setting334Mutation.mutate({\n        teamId,\n        productId\n      }, {\n        onSuccess: result => {\n          if (result.resultCode === 200) {\n            //globalUtil.success(\"提交申请成功！\");\n            Modal.info({\n              title: \"提示\",\n              content: \"提交成功，管理员会接收到申请信息，请您耐心等候\",\n              maskClosable: true,\n              //centered: true, // 居中\n              okText: \"我知道了\",\n              width: 500\n            });\n          }\n        }\n      });\n    }\n  };\n  const cacheRef = useRef({});\n  const {\n    show\n  } = useContextMenu({\n    id: id\n  });\n  useImperativeHandle(ref, () => ({\n    /// const nodeData = {\n    ///   nodeId,\n    ///   rightFlgIconType： 图标颜色\n    ///   nameTextColorType：名称颜色\n    ///   nameTextStrikeFlg：删除线\n    /// }\n    showContextBoard: (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => _onShow(e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType)\n  }));\n\n  // load current node menu context and show\n  // ctxOptionList：自定义传入ctxOptionList，无需接口获取\n  const _onShow = async (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {\n    // 计算菜单位置\n    const menuHeight = 300; // 预估菜单高度\n    const menuWidth = 200; // 预估菜单宽度\n    const windowHeight = window.innerHeight;\n    const windowWidth = window.innerWidth;\n    const clickY = e.clientY;\n    const clickX = e.clientX;\n\n    // 如果点击位置靠近底部，向上偏移\n    const adjustedY = clickY + menuHeight > windowHeight ? Math.max(0, windowHeight - menuHeight - 10) : clickY;\n\n    // 如果点击位置靠近右侧，向左偏移\n    const adjustedX = clickX + menuWidth > windowWidth ? Math.max(0, windowWidth - menuWidth - 10) : clickX;\n\n    // 使用 react-contexify 的默认定位机制，但添加位置调整\n    show({\n      event: e,\n      props: nodeData,\n      position: {\n        x: adjustedX,\n        y: adjustedY\n      }\n    });\n    mutateAsync({\n      nodeData,\n      nodeId,\n      filterActionTypes,\n      ctxOptionList,\n      nodeType\n    });\n    cacheRef.current.nodeData = nodeData;\n  };\n\n  // 特殊逻辑:1.由于报告模块同一份报告针对不同的人是不同类型的信件，根据nodeId无法区分出是发件箱,收件箱还是垃圾箱，所以需要前端传参nodeType来区分右击菜单\n  // tmsbug-8830: 新建接口定义的菜单项位置调整，已经如果已匹配过，则不需要呈现\n  const loadContextMenuList = async (nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {\n    let result = !isEmpty(ctxOptionList) ? {\n      resultCode: 200,\n      ctxOptionList\n    } : await team_036_get_node_ctx_options({\n      teamId,\n      nodeId,\n      nodeType\n    });\n    if (result.resultCode == 200) {\n      let {\n        ctxOptionList,\n        favoriteFlg\n      } = result;\n      if (!isEmpty(filterActionTypes)) ctxOptionList = ctxOptionList.filter(ctxOption => !(filterActionTypes || []).some(actionType => ctxOption.actionType == actionType)); // 无需新建\n      ctxOptionList.forEach(ctxOption => {\n        switch (+ctxOption.actionType) {\n          case eCtxTypeId.ctx_38_create:\n            // 新建操作\n            ctxOption.children = assembleCreateTypeList(ctxOption.children);\n            break;\n          case eCtxTypeId.ctx_39_personalization:\n            // 个性化设置\n            ctxOption.children = assemblePersonalizationList(ctxOption.children, nodeData);\n            break;\n          case eCtxTypeId.ctx_60_flag_mail:\n            // 标记邮件\n            ctxOption.children = flagMailList(ctxOption.children, nodeData);\n            break;\n          case eCtxTypeId.ctx_37_favorite:\n            // 收藏\n            const favoriteFlag = favoriteFlg == eEnableFlg.enable;\n            ctxOption.colorType = favoriteFlag ? eEnableFlg.disable : eEnableFlg.enable;\n            ctxOption.name = getCheckItem(favoriteFlag, ctxOption.name);\n            break;\n          case eCtxTypeId.ctx_40_top:\n            // 置顶（报告独有）\n            const topFlag = nodeData.topFlg == eEnableFlg.enable;\n            ctxOption.colorType = topFlag ? eEnableFlg.disable : eEnableFlg.enable;\n            ctxOption.name = getCheckItem(topFlag, ctxOption.name);\n            break;\n          case eCtxTypeId.ctx_41_read_op:\n            // 已读/未读（报告独有）\n            const readFlag = nodeData.readFlg == eEnableFlg.enable;\n            ctxOption.colorType = readFlag ? eEnableFlg.disable : eEnableFlg.enable;\n            ctxOption.name = getCheckItem(readFlag, ctxOption.name);\n            break;\n          default:\n            ctxOption.children = ctxOption.children.map(_sub => ({\n              ..._sub,\n              actionType: ctxOption.actionType,\n              colorType: _sub.actionType\n            }));\n            break;\n        }\n      });\n      return ctxOptionList;\n    } else {\n      return [];\n    }\n  };\n\n  // 给新建菜单增加分组\n  const assembleCreateTypeList = createTypeList => {\n    let _createTypeList = assembleGroup(createTypeList, false);\n    _createTypeList = treeToArray(_createTypeList);\n    _createTypeList.forEach(_createType => {\n      _createType.colorType = _createType.actionType;\n      _createType.actionType = eCtxTypeId.ctx_38_create;\n    });\n    return _createTypeList;\n  };\n\n  // 个性化设置\n  const assemblePersonalizationList = (personalizationList, nodeData) => {\n    let _personalizationList = assembleGroup(personalizationList, true);\n    _personalizationList = _personalizationList.map(_personalization => {\n      if (_personalization.actionType == eCtxTypeId.ctx_18_set_figure_tag) {\n        var _nodeData$rightFlgIco;\n        // 图标颜色\n        const iconColorOptions = (_personalization.children || []).map(_child => {\n          try {\n            return JSON.parse(_child.name);\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        _personalization.children = [{\n          actionType: eCtxTypeId.ctx_18_set_figure_tag,\n          name: /*#__PURE__*/_jsxDEV(ColorSelectedMenu, {\n            selectValue: (_nodeData$rightFlgIco = nodeData.rightFlgIconType) === null || _nodeData$rightFlgIco === void 0 ? void 0 : _nodeData$rightFlgIco.toString(),\n            onChange: onClick,\n            colorOptionsList: iconColorOptions,\n            actionType: eCtxTypeId.ctx_18_set_figure_tag\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else if (_personalization.actionType == eCtxTypeId.ctx_3_color_txt) {\n        var _nodeData$nameTextCol;\n        // 标题颜色\n        const textColorOptions = (_personalization.children || []).map(_child => {\n          try {\n            let option = JSON.parse(_child.name);\n            return option;\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        /*  _personalization.children = textColorOptions.map((option, index) => {\r\n           let flag = option.type == nodeData.nameTextColorType;\r\n           return {\r\n             actionType: eCtxTypeId.ctx_3_color_txt,\r\n             colorType: flag ? 0 : option.type,\r\n             name: getCheckboxItem(flag, option.title, option.value, option.className, eCtxTypeId.ctx_3_color_txt),\r\n           }\r\n         }); */\n        console.log(\"colorOptionsList\", textColorOptions);\n        _personalization.children = [{\n          actionType: eCtxTypeId.ctx_3_color_txt,\n          name: /*#__PURE__*/_jsxDEV(TitleSelectedMenu, {\n            selectValue: (_nodeData$nameTextCol = nodeData.nameTextColorType) === null || _nodeData$nameTextCol === void 0 ? void 0 : _nodeData$nameTextCol.toString(),\n            onChange: onClick,\n            colorOptionsList: textColorOptions,\n            actionType: eCtxTypeId.ctx_3_color_txt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else if (_personalization.actionType == eCtxTypeId.ctx_46_text_font) {\n        var _nodeData$nodeIconTyp;\n        // 设置字体个性化\n        let textFontTypeOptions = (_personalization.children || []).map(_child => {\n          try {\n            var _sysIconList$find, _nodeData$nameTextFon;\n            let option = JSON.parse(_child.name);\n            let sysIconList = getSysIconList(selectionList);\n            option.icon = (_sysIconList$find = sysIconList.find(sys => sys.propType == option.value)) === null || _sysIconList$find === void 0 ? void 0 : _sysIconList$find.propValue;\n            const nameTextFontTypeList = ((_nodeData$nameTextFon = nodeData.nameTextFontType) === null || _nodeData$nameTextFon === void 0 ? void 0 : _nodeData$nameTextFon.split(\",\")) || []; // 字体字段\n            option.className = nameTextFontTypeList[eNameTextFontType[option.type].idx] == eEnableFlg.enable ? `checked-gray-box` : \"\";\n            return option;\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        console.log(\"textFontTypeOptions\", textFontTypeOptions);\n        _personalization.children = [{\n          actionType: _personalization.actionType,\n          name: /*#__PURE__*/_jsxDEV(FontSelectedMenu, {\n            selectValue: (_nodeData$nodeIconTyp = nodeData.nodeIconType) === null || _nodeData$nodeIconTyp === void 0 ? void 0 : _nodeData$nodeIconTyp.toString(),\n            onChange: onClick,\n            optionList: textFontTypeOptions,\n            actionType: _personalization.actionType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else if (_personalization.actionType == eCtxTypeId.ctx_42_set_icon) {\n        var _nodeData$nodeIconTyp3;\n        // 设置图标\n        let titleIconOptions = (_personalization.children || []).map(_child => {\n          try {\n            var _sysIconList$find2, _nodeData$nodeIconTyp2;\n            let option = JSON.parse(_child.name);\n            let sysIconList = getSysIconList(selectionList);\n            option.icon = (_sysIconList$find2 = sysIconList.find(sys => sys.propType == option.value)) === null || _sysIconList$find2 === void 0 ? void 0 : _sysIconList$find2.propValue;\n            option.className = (((_nodeData$nodeIconTyp2 = nodeData.nodeIconType) === null || _nodeData$nodeIconTyp2 === void 0 ? void 0 : _nodeData$nodeIconTyp2.split(\",\")) || []).some(nodeIcon => option.value == nodeIcon) ? `checked-gray-box` : \"\";\n            return option;\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        _personalization.children = [{\n          actionType: _personalization.actionType,\n          name: /*#__PURE__*/_jsxDEV(IconSelectedMenu, {\n            selectValue: (_nodeData$nodeIconTyp3 = nodeData.nodeIconType) === null || _nodeData$nodeIconTyp3 === void 0 ? void 0 : _nodeData$nodeIconTyp3.toString(),\n            onChange: onClick,\n            optionList: titleIconOptions,\n            actionType: _personalization.actionType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else {\n        return _personalization;\n      }\n    });\n    _personalizationList = treeToArray(_personalizationList);\n    return _personalizationList;\n  };\n\n  // 标记邮件\n  const flagMailList = (personalizationList, nodeData) => {\n    let _personalizationList = assembleGroup(personalizationList, true);\n    _personalizationList = _personalizationList.map(_personalization => {\n      if (_personalization.actionType == eCtxTypeId.ctx_61_flag_color) {\n        var _nodeData$tagColor;\n        // 标记颜色\n        const iconColorOptions = (_personalization.children || []).map(_child => {\n          try {\n            return JSON.parse(_child.name);\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        _personalization.children = [{\n          actionType: _personalization.actionType,\n          name: /*#__PURE__*/_jsxDEV(ColorSelectedMenu, {\n            selectValue: (_nodeData$tagColor = nodeData.tagColor) === null || _nodeData$tagColor === void 0 ? void 0 : _nodeData$tagColor.toString(),\n            onChange: onClick,\n            colorOptionsList: iconColorOptions,\n            actionType: _personalization.actionType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else if (_personalization.actionType == eCtxTypeId.ctx_62_flag_img) {\n        var _nodeData$tagColor3;\n        // 标记图标\n        let titleIconOptions = (_personalization.children || []).map(_child => {\n          try {\n            var _getSysIconList$find, _nodeData$tagColor2;\n            let option = JSON.parse(_child.name);\n            option.icon = (_getSysIconList$find = getSysIconList(selectionList).find(sys => sys.propType == option.value)) === null || _getSysIconList$find === void 0 ? void 0 : _getSysIconList$find.propValue;\n            option.className = (((_nodeData$tagColor2 = nodeData.tagColor) === null || _nodeData$tagColor2 === void 0 ? void 0 : _nodeData$tagColor2.split(\",\")) || []).some(nodeIcon => option.value == nodeIcon) ? `checked-gray-box` : \"\";\n            return option;\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        _personalization.children = [{\n          actionType: _personalization.actionType,\n          name: /*#__PURE__*/_jsxDEV(IconSelectedMenu, {\n            selectValue: (_nodeData$tagColor3 = nodeData.tagColor) === null || _nodeData$tagColor3 === void 0 ? void 0 : _nodeData$tagColor3.toString(),\n            onChange: onClick,\n            optionList: titleIconOptions,\n            actionType: _personalization.actionType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else {\n        return _personalization;\n      }\n    });\n    _personalizationList = _personalizationList.flatMap(taskGroup => taskGroup.children);\n    return _personalizationList;\n  };\n\n  // menu item click\n  const onClick = ({\n    id,\n    props,\n    data,\n    triggerEvent,\n    ...args\n  }) => {\n    let arr = id.split(\"&\"); // 存在快捷方式-999的nodeType\n    let callbackData = {\n      actionType: arr[0],\n      colorType: arr[1],\n      objType: data === null || data === void 0 ? void 0 : data.objType,\n      productId: data === null || data === void 0 ? void 0 : data.productId //20250724 Jim Song 后端多加一个参数productId,用来表征如果menuStatus异常时，知晓是哪一个产品id\n    };\n    console.log(callbackData);\n    onContextMenuClick(callbackData);\n  };\n  const _onMoreBtnClick = e => {\n    onMoreBtnClick && onMoreBtnClick({\n      nodeItem: cacheRef.current.nodeData,\n      ctxType: e.actionType,\n      colorType: e.colorType,\n      ...callbackParams\n    });\n  };\n  const _onCreateBtnClick = e => {\n    onCreateBtnClick && onCreateBtnClick({\n      nodeItem: cacheRef.current.nodeData,\n      nodeType: e.colorType,\n      ...callbackParams\n    });\n  };\n  const onContextMenuClick = ({\n    actionType,\n    colorType,\n    productId\n  }) => {\n    if (actionType == eCtxTypeId.ctx_38_create) {\n      // 新建操作\n      // 注意:设置图标颜色无法查询出来\n      const node = findByActionAndColorType(createTypeList, actionType, colorType);\n      if (node.menuStatus == eMenuStatus.status_1_Free_QuotaExceed) {\n        // 免费对象数已达上限\n        return resourceMaxModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);\n      }\n      if (node.menuStatus == eMenuStatus.status_3_Vip_Unauthorized) {\n        // Vip未授权\n        return unVipModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId, applyProductAuthorize);\n      }\n      if (node.menuStatus == eMenuStatus.status_4_Vip_Expired) {\n        // Vip已过期\n        return expiredModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);\n      }\n      _onCreateBtnClick({\n        colorType\n      });\n    } else {\n      _onMoreBtnClick({\n        actionType,\n        colorType\n      });\n    }\n  };\n\n  // 根据actionType和colorType查找节点\n  function findByActionAndColorType(list, actionType, colorType) {\n    for (let i in list) {\n      if (list[i].actionType == actionType && list[i].colorType == colorType) {\n        return list[i];\n      }\n      if (list[i].children) {\n        let node = findByActionAndColorType(list[i].children, actionType, colorType);\n        if (node) {\n          return node;\n        }\n      }\n    }\n  }\n\n  // 右击菜单\n  // CtxMenuItem、CtxSubmenu <'fade' | 'scale' | 'flip' | 'slide'>\n  return /*#__PURE__*/_jsxDEV(CtxMenu, {\n    id: id,\n    animation: {\n      enter: false,\n      exit: 'slide'\n    },\n    onVisibilityChange: handleOnVisibilityChange,\n    className: \"\",\n    children: [isCtxLoading && /*#__PURE__*/_jsxDEV(ContextLoadingBoard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 24\n    }, this), !isCtxLoading && createTypeList.length == 0 && /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: \" \\u65E0\\u53EF\\u7528\\u9009\\u9879 \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 55\n    }, this), !isCtxLoading && createTypeList.length > 0 && createTypeList.map(el => {\n      const key = `${el.actionType}&${el.colorType}`;\n      if (!isEmpty(el.children)) {\n        return /*#__PURE__*/_jsxDEV(DelayedCtxSubMenu, {\n          label: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"iconfont \" + getCtxIconByType(el.actionType) + \" fontsize-12 fontcolor-normal marginRight-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: el.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true)\n          // 子级菜单箭头\n          ,\n          arrow: /*#__PURE__*/_jsxDEV(RightOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 20\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: el.actionType == eCtxTypeId.ctx_38_create ? \"horizontal-submenu-panel\" : \"\",\n            children: el.actionType == eCtxTypeId.ctx_38_create ?\n            // 新建菜单：按分组显示\n            (() => {\n              const items = [];\n              let currentGroupItems = [];\n              el.children.forEach((sub, index) => {\n                const key = `${sub.actionType}&${sub.colorType}`;\n\n                // 如果是分组标题\n                if (sub.type == eCtxOptionType.eGroup) {\n                  // 如果有累积的分组内容，先渲染它们\n                  if (currentGroupItems.length > 0) {\n                    items.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"group-content-row\",\n                      children: currentGroupItems\n                    }, `group-${index}-content`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 27\n                    }, this));\n                    currentGroupItems = [];\n                  }\n\n                  // 添加分组标题\n                  items.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group-title-row\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"group-title\",\n                      children: sub.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 25\n                  }, this));\n                } else {\n                  // 如果是分组内容\n                  currentGroupItems.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"submenu-item-wrapper normal-menu-item\",\n                    children: /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n                      id: key,\n                      data: sub,\n                      disabled: sub.disabled,\n                      onClick: onClick,\n                      children: [sub.actionType == eCtxTypeId.ctx_38_create ? /*#__PURE__*/_jsxDEV(AppNodeResourceIcon, {\n                        nodeType: sub.colorType,\n                        className: \"fontsize-12 fontcolor-normal marginRight-5\",\n                        style: {\n                          lineHeight: \"24px\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 603,\n                        columnNumber: 31\n                      }, this) : sub.actionType == eCtxTypeId.ctx_12_create_shortcut ? getCtxIconByType(sub.colorType) && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"iconfont \" + getCtxIconByType(sub.colorType) + \" fontsize-12 fontcolor-normal marginRight-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 605,\n                        columnNumber: 67\n                      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 606,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: \"100%\"\n                        },\n                        children: sub.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `,\n                        style: {\n                          color: getMenuStatusIcon(sub.menuStatus).iconColor\n                        },\n                        title: sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ? '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ? '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ? '应用已过期' : ''\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 611,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 619,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 25\n                  }, this));\n                }\n              });\n\n              // 处理最后的分组内容\n              if (currentGroupItems.length > 0) {\n                items.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"group-content-row\",\n                  children: currentGroupItems\n                }, \"group-last-content\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 23\n                }, this));\n              }\n              return items;\n            })() :\n            // 其他菜单：保持原有布局\n            el.children.map((sub, index) => {\n              const key = `${sub.actionType}&${sub.colorType}`;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n                  id: key,\n                  data: sub,\n                  disabled: sub.disabled,\n                  onClick: sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || sub.actionType == eCtxTypeId.ctx_3_color_txt || sub.actionType == eCtxTypeId.ctx_42_set_icon || sub.actionType == eCtxTypeId.ctx_46_text_font || sub.actionType == eCtxTypeId.ctx_61_flag_color || sub.actionType == eCtxTypeId.ctx_62_flag_img ? () => {} : onClick,\n                  style: sub.type != eCtxOptionType.eGroup ? {\n                    paddingLeft: \"24px\"\n                  } : {},\n                  className: sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || sub.actionType == eCtxTypeId.ctx_3_color_txt || sub.actionType == eCtxTypeId.ctx_42_set_icon || sub.actionType == eCtxTypeId.ctx_46_text_font || sub.actionType == eCtxTypeId.ctx_61_flag_color || sub.actionType == eCtxTypeId.ctx_62_flag_img ? \"context-item-not-focus\" : \"\",\n                  children: [sub.actionType == eCtxTypeId.ctx_38_create ? /*#__PURE__*/_jsxDEV(AppNodeResourceIcon, {\n                    nodeType: sub.colorType,\n                    className: \"fontsize-12 fontcolor-normal marginRight-5\",\n                    style: {\n                      lineHeight: \"24px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 27\n                  }, this) : sub.actionType == eCtxTypeId.ctx_12_create_shortcut ? getCtxIconByType(sub.colorType) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"iconfont \" + getCtxIconByType(sub.colorType) + \" fontsize-12 fontcolor-normal marginRight-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 63\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"100%\"\n                    },\n                    children: sub.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `,\n                    style: {\n                      color: getMenuStatusIcon(sub.menuStatus).iconColor\n                    },\n                    title: sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ? '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ? '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ? '应用已过期' : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 683,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 23\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 18\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n          id: key,\n          onClick: onClick,\n          children: [el.actionType == 28 ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"iconfont \" + getCtxIconByType(el.actionType) + ' fontsize-12 fontcolor-normal',\n            style: {\n              marginLeft: -5,\n              marginRight: 5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"iconfont \" + getCtxIconByType(el.actionType) + \" fontsize-12 fontcolor-normal marginRight-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"100%\"\n            },\n            children: el.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 18\n        }, this);\n      }\n    })]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 546,\n    columnNumber: 5\n  }, this);\n}\n_s2(ContextBoard, \"XucGnAf5rosBS/yyawAlko21CMQ=\", false, function () {\n  return [useQuerySetting327_getTeamSpaceAadmin, useQuerySetting407_getCodeValueList, useMutation, useMutation, useContextMenu];\n});\n_c6 = ContextBoard;\nfunction ContextLoadingBoard() {\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 711,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 719,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 709,\n    columnNumber: 10\n  }, this);\n}\n_c7 = ContextLoadingBoard;\nexport default _c8 = /*#__PURE__*/forwardRef(ContextBoard);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"ColorSelectedMenu\");\n$RefreshReg$(_c2, \"TitleSelectedMenu\");\n$RefreshReg$(_c3, \"FontSelectedMenu\");\n$RefreshReg$(_c4, \"IconSelectedMenu\");\n$RefreshReg$(_c5, \"DelayedCtxSubMenu\");\n$RefreshReg$(_c6, \"ContextBoard\");\n$RefreshReg$(_c7, \"ContextLoadingBoard\");\n$RefreshReg$(_c8, \"%default%\");", "map": {"version": 3, "names": ["CheckOutlined", "RightOutlined", "team_036_get_node_ctx_options", "AppNodeResourceIcon", "useQuerySetting327_getTeamSpaceAadmin", "useQuerySetting407_getCodeValueList", "isEmpty", "treeToArray", "getSysIconList", "assembleGroup", "eCtxOptionType", "eEnableFlg", "eProductId", "eCtxTypeId", "eMenuStatus", "getCtxIconByType", "getMenuStatusIcon", "eNameTextFontType", "expiredModal", "resourceMaxModal", "unVipModal", "Checkbox", "Space", "Skeleton", "Modal", "React", "forwardRef", "useImperativeHandle", "useRef", "useState", "<PERSON><PERSON>", "CtxMenuItem", "<PERSON><PERSON>", "CtxMenu", "Submenu", "CtxSubMenu", "useContextMenu", "Separator", "formatSvg", "useMutation", "https", "setting_320_get_node_priv_query", "globalUtil", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ColorSelectedMenu", "selectValue", "onChange", "colorOptionsList", "actionType", "_onChanged", "checkedValue", "value", "length", "id", "style", "display", "children", "Group", "map", "item", "name", "type", "toString", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "TitleSelectedMenu", "title", "_c2", "FontSelectedMenu", "optionList", "size", "option", "onClick", "icon", "_c3", "IconSelectedMenu", "_c4", "getCheckItem", "flag", "label", "color", "justifyContent", "alignItems", "DelayedCtxSubMenu", "arrow", "props", "_s", "timeoutRef", "containerRef", "useEffect", "container", "current", "handleMouseEnter", "clearTimeout", "handleMouseLeave", "setTimeout", "subMenu", "querySelector", "addEventListener", "observer", "MutationObserver", "mutations", "for<PERSON>ach", "mutation", "addedNodes", "node", "nodeType", "Node", "ELEMENT_NODE", "getAttribute", "observe", "document", "body", "childList", "subtree", "removeEventListener", "disconnect", "ref", "_c5", "ContextBoard", "teamId", "onMoreBtnClick", "onCreateBtnClick", "handleOnVisibilityChange", "callbackP<PERSON>ms", "_s2", "data", "userId", "teamAdminFlag", "manager<PERSON><PERSON>", "undefined", "enabled", "selectionList", "isLoading", "isCtxLoading", "createTypeList", "mutateAsync", "mutationFn", "nodeData", "nodeId", "filterActionTypes", "ctxOptionList", "loadContextMenuList", "setting334Mutation", "setting_334_apply_authorization", "applyProductAuthorize", "productId", "mutate", "onSuccess", "result", "resultCode", "info", "content", "maskClosable", "okText", "width", "cacheRef", "show", "showContextBoard", "e", "_onShow", "menuHeight", "menuWidth", "windowHeight", "window", "innerHeight", "windowWidth", "innerWidth", "clickY", "clientY", "clickX", "clientX", "adjustedY", "Math", "max", "adjustedX", "event", "position", "x", "y", "favoriteFlg", "filter", "ctxOption", "some", "ctx_38_create", "assembleCreateTypeList", "ctx_39_personalization", "assemblePersonalizationList", "ctx_60_flag_mail", "flagMailList", "ctx_37_favorite", "favoriteFlag", "enable", "colorType", "disable", "ctx_40_top", "topFlag", "topFlg", "ctx_41_read_op", "readFlag", "readFlg", "_sub", "_createTypeList", "_createType", "personalizationList", "_personalizationList", "_personalization", "ctx_18_set_figure_tag", "_nodeData$rightFlgIco", "iconColorOptions", "_child", "JSON", "parse", "error", "console", "rightFlgIconType", "ctx_3_color_txt", "_nodeData$nameTextCol", "textColorOptions", "log", "nameTextColorType", "ctx_46_text_font", "_nodeData$nodeIconTyp", "textFontTypeOptions", "_sysIconList$find", "_nodeData$nameTextFon", "sysIconList", "find", "sys", "propType", "propValue", "nameTextFontTypeList", "nameTextFontType", "split", "idx", "nodeIconType", "ctx_42_set_icon", "_nodeData$nodeIconTyp3", "titleIconOptions", "_sysIconList$find2", "_nodeData$nodeIconTyp2", "nodeIcon", "ctx_61_flag_color", "_nodeData$tagColor", "tagColor", "ctx_62_flag_img", "_nodeData$tagColor3", "_getSysIconList$find", "_nodeData$tagColor2", "flatMap", "taskGroup", "triggerEvent", "args", "arr", "callbackData", "objType", "onContextMenuClick", "_onMoreBtnClick", "nodeItem", "ctxType", "_onCreateBtnClick", "findByActionAndColorType", "menuStatus", "status_1_Free_QuotaExceed", "expirationDt", "status_3_Vip_Unauthorized", "status_4_Vip_Expired", "list", "i", "animation", "enter", "exit", "onVisibilityChange", "ContextLoadingBoard", "disabled", "el", "key", "items", "currentGroupItems", "sub", "index", "eGroup", "push", "lineHeight", "ctx_12_create_shortcut", "iconColor", "paddingLeft", "marginLeft", "marginRight", "_c6", "Input", "active", "height", "overflow", "_c7", "_c8", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/ContextBoard.jsx"], "sourcesContent": ["import { CheckOutlined, RightOutlined } from '@ant-design/icons';\r\nimport { team_036_get_node_ctx_options } from \"@common/api/http\";\r\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\r\nimport { useQuerySetting327_getTeamSpaceAadmin, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\r\nimport { isEmpty, treeToArray, getSysIconList, assembleGroup } from \"@common/utils/ArrayUtils\";\r\nimport { eCtxOptionType, eEnableFlg, eProductId } from \"@common/utils/enum\";\r\nimport { eCtxTypeId, eMenuStatus, getCtxIconByType, getMenuStatusIcon, eNameTextFontType } from \"@common/utils/TsbConfig\";\r\nimport { expiredModal, resourceMaxModal, unVipModal } from \"@common/utils/ViewUtils\";\r\nimport { Checkbox, Space, Skeleton, Modal } from \"antd\";\r\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from \"react\";\r\nimport { Item as CtxMenuItem, Menu as CtxMenu, Submenu as CtxSubMenu, useContextMenu, Separator } from \"react-contexify\";\r\nimport { formatSvg } from \"@common/utils/ViewUtils\";\r\nimport \"react-contexify/ReactContexify.css\";\r\nimport { useMutation } from '@tanstack/react-query';\r\nimport \"./ContextBoard.scss\";\r\nimport * as https from \"@common/api/http\";\r\nimport { setting_320_get_node_priv_query } from \"@common/api/query/query_setting\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\n\r\n\r\n// 图标颜色\r\nfunction ColorSelectedMenu({ selectValue, onChange, colorOptionsList, actionType }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: actionType + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n    <Checkbox.Group value={[selectValue]} onChange={_onChanged}>\r\n      {colorOptionsList.map(item => <Checkbox name=\"xxx\" key={item.type.toString()} value={item.type.toString()} className={item.className} ></Checkbox>)}\r\n    </Checkbox.Group>\r\n  </div>\r\n}\r\n\r\n// 标题颜色\r\nfunction TitleSelectedMenu({ selectValue, onChange, colorOptionsList, actionType }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: actionType + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n    <Checkbox.Group value={[selectValue]} onChange={_onChanged} >\r\n      {colorOptionsList.map(item => <Checkbox name=\"xxx\" key={item.type.toString()} value={item.type.toString()} className={item.className} >\r\n      { selectValue == item.type ? \"\" : item.title}\r\n      </Checkbox>)}\r\n    </Checkbox.Group>\r\n  </div>\r\n}\r\n\r\n// 字体个性化设置，前段需要固定识别，存储的是type值\r\nfunction FontSelectedMenu({ onChange, optionList, actionType }) {\r\n  const _onChanged = (checkedValue) => {\r\n    onChange && onChange({ id: actionType + \"&\" + checkedValue })\r\n  }\r\n  return <Space size={10}>\r\n    {optionList.map((option)=>(\r\n      <div key={option?.type} onClick={()=>_onChanged(option?.type)} className={option.className}>\r\n       {formatSvg(option.icon)}\r\n      </div>\r\n    ))}\r\n  </Space>\r\n}\r\n\r\n// 图标设置，无需固定，有多少显示多少，存储的是value值\r\nfunction IconSelectedMenu({ onChange, optionList, actionType }) {\r\n  const _onChanged = (checkedValue) => {\r\n    onChange && onChange({ id: actionType + \"&\" + checkedValue })\r\n  }\r\n  return <Space size={10}>\r\n    {optionList.map((option)=>(\r\n      <div key={option?.type} onClick={()=>_onChanged(option?.value)} className={option.className}>\r\n       {formatSvg(option.icon)}\r\n      </div>\r\n    ))}\r\n  </Space>\r\n}\r\n\r\n/**\r\n * @description 文档图标\r\n */\r\n/*function TitleIconSelectedMenu({ selectValue, onChange, colorOptionsList }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: eCtxTypeId.ctx_42_set_icon + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n  <Checkbox.Group value={[selectValue]} onChange={_onChanged}>\r\n    {colorOptionsList.map(option => (\r\n      <Checkbox\r\n        key={option.value?.toString()} \r\n        value={option.value?.toString()}\r\n        className=\"checkbox-bage\"\r\n      >\r\n        <div className=\"checkbox-bage-icon\">\r\n          {formatSvg(option.icon)}\r\n        </div>\r\n      </Checkbox>\r\n    ))}\r\n  </Checkbox.Group>\r\n</div>\r\n}*/\r\n\r\n/*// checkbox 选中状态\r\nconst getCheckboxItem = (flag, label, color, className, actionType) => {\r\n  return <div style={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"baseline\" }}>\r\n    {/!* tmsbug-2622：删除线文案本身添加删除线 *!/}\r\n    <span style={{ color: color }} className={`${actionType == eCtxTypeId.ctx_46_text_font ? \"tree-dir-title-delete\" : \"\"}`}>{label}</span>\r\n    <Checkbox checked={flag} className={className} />\r\n  </div>\r\n}*/\r\n\r\n// 收藏选中状态\r\nconst getCheckItem = (flag, label, color) => {\r\n  return <div style={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", color }}>\r\n    <span>{label}</span>\r\n    {flag ? <CheckOutlined style={{ color: \"#70b603\" }} /> : <></>}\r\n  </div>\r\n}\r\n\r\n// 自定义延迟子菜单组件\r\nconst DelayedCtxSubMenu = ({ children, label, arrow, ...props }) => {\r\n  const timeoutRef = useRef(null);\r\n  const containerRef = useRef(null);\r\n\r\n  React.useEffect(() => {\r\n    const container = containerRef.current;\r\n    if (!container) return;\r\n\r\n    const handleMouseEnter = () => {\r\n      // 清除隐藏定时器\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n        timeoutRef.current = null;\r\n      }\r\n    };\r\n\r\n    const handleMouseLeave = () => {\r\n      // 设置延迟隐藏定时器\r\n      timeoutRef.current = setTimeout(() => {\r\n        // 查找子菜单元素并隐藏\r\n        const subMenu = container.querySelector('[role=\"menu\"]');\r\n        if (subMenu) {\r\n          subMenu.style.display = 'none';\r\n        }\r\n      }, 1000); // 1秒延迟\r\n    };\r\n\r\n    // 为容器添加事件监听器\r\n    container.addEventListener('mouseenter', handleMouseEnter);\r\n    container.addEventListener('mouseleave', handleMouseLeave);\r\n\r\n    // 为子菜单添加事件监听器\r\n    const observer = new MutationObserver((mutations) => {\r\n      mutations.forEach((mutation) => {\r\n        mutation.addedNodes.forEach((node) => {\r\n          if (node.nodeType === Node.ELEMENT_NODE && node.getAttribute('role') === 'menu') {\r\n            // 找到子菜单，为其添加鼠标事件\r\n            node.addEventListener('mouseenter', handleMouseEnter);\r\n            node.addEventListener('mouseleave', handleMouseLeave);\r\n\r\n            // 重置显示状态\r\n            node.style.display = '';\r\n          }\r\n        });\r\n      });\r\n    });\r\n\r\n    observer.observe(document.body, {\r\n      childList: true,\r\n      subtree: true\r\n    });\r\n\r\n    return () => {\r\n      container.removeEventListener('mouseenter', handleMouseEnter);\r\n      container.removeEventListener('mouseleave', handleMouseLeave);\r\n      observer.disconnect();\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div ref={containerRef}>\r\n      <CtxSubMenu\r\n        {...props}\r\n        label={label}\r\n        arrow={arrow}\r\n      >\r\n        {children}\r\n      </CtxSubMenu>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * 右击菜单 \r\n * @param teamId 团队Id\r\n * @param onMoreBtnClick 非新建操作回调\r\n * @param onCreateBtnClick 新建等操作回调\r\n * @param id 菜单id\r\n * @param callbackParams 是 onMoreBtnClick 和 onCreateBtnClick 回调返回参数\r\n */\r\nfunction ContextBoard({ teamId, onMoreBtnClick, onCreateBtnClick, id, handleOnVisibilityChange, ...callbackParams }, ref) {\r\n  // const [createTypeList, setCreateTypeList] = useState([]); // tree right click context menu\r\n  // const [nodeData,setNodeData] = useState(null); \r\n  const { data: { userId, teamAdminFlag: managerFlag } = {userId: undefined, teamAdminFlag: undefined} }\r\n    = useQuerySetting327_getTeamSpaceAadmin({teamId, enabled: true}); // 判断登录人员是否是团队管理员\r\n  const { data: selectionList } = useQuerySetting407_getCodeValueList(teamId); //字典数据\r\n\r\n  const {isLoading:isCtxLoading, data:createTypeList=[], mutateAsync} = useMutation({\r\n    mutationFn: ({nodeData,nodeId,filterActionTypes,ctxOptionList, nodeType}) => loadContextMenuList(nodeData,nodeId,filterActionTypes,ctxOptionList, nodeType)\r\n  })\r\n\r\n  const setting334Mutation = useMutation({\r\n    mutationFn: https.setting_334_apply_authorization\r\n  })\r\n\r\n  //产品申请开通授权\r\n  const applyProductAuthorize = (productId) => {\r\n    if(!!productId) {\r\n      setting334Mutation.mutate({ teamId, productId }, {\r\n        onSuccess: (result) => {\r\n          if(result.resultCode === 200) {\r\n            //globalUtil.success(\"提交申请成功！\");\r\n            Modal.info({\r\n              title: \"提示\",\r\n              content: \"提交成功，管理员会接收到申请信息，请您耐心等候\",\r\n              maskClosable: true,\r\n              //centered: true, // 居中\r\n              okText: \"我知道了\",\r\n              width: 500,\r\n            });\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  const cacheRef = useRef({});\r\n  const { show } = useContextMenu({ id: id });\r\n\r\n  useImperativeHandle(ref, () => ({\r\n    /// const nodeData = {\r\n    ///   nodeId,\r\n    ///   rightFlgIconType： 图标颜色\r\n    ///   nameTextColorType：名称颜色\r\n    ///   nameTextStrikeFlg：删除线\r\n    /// }\r\n    showContextBoard: (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => _onShow(e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType)\r\n  }));\r\n\r\n  // load current node menu context and show\r\n  // ctxOptionList：自定义传入ctxOptionList，无需接口获取\r\n  const _onShow = async (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {\r\n    // 计算菜单位置\r\n    const menuHeight = 300; // 预估菜单高度\r\n    const menuWidth = 200; // 预估菜单宽度\r\n    const windowHeight = window.innerHeight;\r\n    const windowWidth = window.innerWidth;\r\n    const clickY = e.clientY;\r\n    const clickX = e.clientX;\r\n    \r\n    // 如果点击位置靠近底部，向上偏移\r\n    const adjustedY = clickY + menuHeight > windowHeight ? \r\n      Math.max(0, windowHeight - menuHeight - 10) : \r\n      clickY;\r\n\r\n    // 如果点击位置靠近右侧，向左偏移\r\n    const adjustedX = clickX + menuWidth > windowWidth ?\r\n      Math.max(0, windowWidth - menuWidth - 10) :\r\n      clickX;\r\n\r\n    // 使用 react-contexify 的默认定位机制，但添加位置调整\r\n    show({ \r\n      event: e, \r\n      props: nodeData,\r\n      position: {\r\n        x: adjustedX,\r\n        y: adjustedY\r\n      }\r\n    });\r\n    \r\n    mutateAsync({nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType});\r\n    cacheRef.current.nodeData = nodeData;\r\n  }\r\n\r\n  // 特殊逻辑:1.由于报告模块同一份报告针对不同的人是不同类型的信件，根据nodeId无法区分出是发件箱,收件箱还是垃圾箱，所以需要前端传参nodeType来区分右击菜单\r\n  // tmsbug-8830: 新建接口定义的菜单项位置调整，已经如果已匹配过，则不需要呈现\r\n  const loadContextMenuList = async (nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {\r\n    let result = !isEmpty(ctxOptionList) ? { resultCode: 200, ctxOptionList } : await team_036_get_node_ctx_options({ teamId, nodeId, nodeType})\r\n    if (result.resultCode == 200) {\r\n      let {ctxOptionList, favoriteFlg} = result;\r\n      if(!isEmpty(filterActionTypes)) ctxOptionList = ctxOptionList.filter(ctxOption => !(filterActionTypes || []).some(actionType => ctxOption.actionType == actionType)) // 无需新建\r\n      ctxOptionList.forEach(ctxOption => {\r\n        switch (+ ctxOption.actionType) {\r\n          case eCtxTypeId.ctx_38_create: // 新建操作\r\n            ctxOption.children = assembleCreateTypeList(ctxOption.children);\r\n            break;\r\n          case eCtxTypeId.ctx_39_personalization: // 个性化设置\r\n            ctxOption.children = assemblePersonalizationList(ctxOption.children, nodeData);\r\n            break;\r\n          case eCtxTypeId.ctx_60_flag_mail: // 标记邮件\r\n            ctxOption.children = flagMailList(ctxOption.children, nodeData);\r\n            break;\r\n          case eCtxTypeId.ctx_37_favorite: // 收藏\r\n            const favoriteFlag = favoriteFlg == eEnableFlg.enable;\r\n            ctxOption.colorType = favoriteFlag ? eEnableFlg.disable : eEnableFlg.enable;\r\n            ctxOption.name = getCheckItem(favoriteFlag, ctxOption.name);\r\n            break;\r\n          case eCtxTypeId.ctx_40_top: // 置顶（报告独有）\r\n            const topFlag = nodeData.topFlg == eEnableFlg.enable;\r\n            ctxOption.colorType = topFlag ? eEnableFlg.disable : eEnableFlg.enable;\r\n            ctxOption.name = getCheckItem(topFlag, ctxOption.name);\r\n            break;\r\n          case eCtxTypeId.ctx_41_read_op: // 已读/未读（报告独有）\r\n            const readFlag = nodeData.readFlg == eEnableFlg.enable;\r\n            ctxOption.colorType = readFlag ? eEnableFlg.disable : eEnableFlg.enable;\r\n            ctxOption.name = getCheckItem(readFlag, ctxOption.name);\r\n            break;\r\n          default:\r\n            ctxOption.children = ctxOption.children.map((_sub)=>({..._sub, actionType: ctxOption.actionType, colorType: _sub.actionType}));\r\n            break;\r\n        }\r\n      });\r\n      return ctxOptionList;\r\n    } else {\r\n      return [];\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // 给新建菜单增加分组\r\n  const assembleCreateTypeList = (createTypeList) => {\r\n    let _createTypeList = assembleGroup(createTypeList, false);\r\n    _createTypeList = treeToArray(_createTypeList);\r\n    _createTypeList.forEach((_createType)=>{\r\n      _createType.colorType = _createType.actionType;\r\n      _createType.actionType = eCtxTypeId.ctx_38_create;\r\n    });\r\n    return _createTypeList;     \r\n  }\r\n\r\n  // 个性化设置\r\n  const assemblePersonalizationList = (personalizationList, nodeData) => {\r\n    let _personalizationList = assembleGroup(personalizationList, true);\r\n    _personalizationList = _personalizationList.map( _personalization =>{\r\n      if(_personalization.actionType == eCtxTypeId.ctx_18_set_figure_tag){ // 图标颜色\r\n        const iconColorOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            return JSON.parse(_child.name);\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        _personalization.children = [\r\n          {\r\n            actionType: eCtxTypeId.ctx_18_set_figure_tag,\r\n            name: <ColorSelectedMenu selectValue={nodeData.rightFlgIconType?.toString()} onChange={onClick} colorOptionsList = {iconColorOptions} actionType={eCtxTypeId.ctx_18_set_figure_tag}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else if(_personalization.actionType == eCtxTypeId.ctx_3_color_txt){ // 标题颜色\r\n        const textColorOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            let option = JSON.parse(_child.name);\r\n            return option;\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n       /*  _personalization.children = textColorOptions.map((option, index) => {\r\n          let flag = option.type == nodeData.nameTextColorType;\r\n          return {\r\n            actionType: eCtxTypeId.ctx_3_color_txt,\r\n            colorType: flag ? 0 : option.type,\r\n            name: getCheckboxItem(flag, option.title, option.value, option.className, eCtxTypeId.ctx_3_color_txt),\r\n          }\r\n        }); */\r\n        console.log(\"colorOptionsList\", textColorOptions);\r\n        _personalization.children = [\r\n          {\r\n            actionType: eCtxTypeId.ctx_3_color_txt,\r\n            name: <TitleSelectedMenu selectValue={nodeData.nameTextColorType?.toString()} onChange={onClick} colorOptionsList = {textColorOptions} actionType={eCtxTypeId.ctx_3_color_txt}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else if(_personalization.actionType == eCtxTypeId.ctx_46_text_font ){ // 设置字体个性化\r\n        let textFontTypeOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            let option =  JSON.parse(_child.name);\r\n            let sysIconList = getSysIconList(selectionList); \r\n            option.icon = sysIconList.find(sys => sys.propType == option.value)?.propValue;\r\n            const nameTextFontTypeList = nodeData.nameTextFontType?.split(\",\") || []; // 字体字段\r\n            option.className = nameTextFontTypeList[eNameTextFontType[option.type].idx] == eEnableFlg.enable ? `checked-gray-box` : \"\";\r\n            return option;\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        console.log(\"textFontTypeOptions\", textFontTypeOptions);\r\n        _personalization.children = [\r\n          {\r\n            actionType: _personalization.actionType,\r\n            name: <FontSelectedMenu selectValue={nodeData.nodeIconType?.toString()} onChange={onClick} optionList={textFontTypeOptions} actionType={_personalization.actionType}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else if(_personalization.actionType == eCtxTypeId.ctx_42_set_icon){ // 设置图标\r\n        let titleIconOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            let option =  JSON.parse(_child.name);\r\n            let sysIconList = getSysIconList(selectionList); \r\n            option.icon = sysIconList.find(sys => sys.propType == option.value)?.propValue;\r\n            option.className = (nodeData.nodeIconType?.split(\",\") || []).some(nodeIcon => option.value == nodeIcon ) ? `checked-gray-box` : \"\";\r\n            return option;\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        _personalization.children = [\r\n          {\r\n            actionType: _personalization.actionType,\r\n            name: <IconSelectedMenu selectValue={nodeData.nodeIconType?.toString()} onChange={onClick} optionList={titleIconOptions} actionType={_personalization.actionType}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else  {\r\n        return _personalization;\r\n      }\r\n    })\r\n    _personalizationList = treeToArray(_personalizationList);\r\n    return _personalizationList;\r\n  }\r\n\r\n  // 标记邮件\r\n  const flagMailList = (personalizationList, nodeData) => {\r\n    let _personalizationList = assembleGroup(personalizationList, true);\r\n    _personalizationList = _personalizationList.map( _personalization =>{\r\n      if(_personalization.actionType == eCtxTypeId.ctx_61_flag_color){ // 标记颜色\r\n        const iconColorOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            return JSON.parse(_child.name);\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        _personalization.children = [\r\n          {\r\n            actionType: _personalization.actionType,\r\n            name: <ColorSelectedMenu selectValue={nodeData.tagColor?.toString()} onChange={onClick} colorOptionsList = {iconColorOptions} actionType={_personalization.actionType}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      }  else if(_personalization.actionType == eCtxTypeId.ctx_62_flag_img){ // 标记图标\r\n        let titleIconOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            let option =  JSON.parse(_child.name);\r\n            option.icon = getSysIconList(selectionList).find(sys => sys.propType == option.value)?.propValue;\r\n            option.className = (nodeData.tagColor?.split(\",\") || []).some(nodeIcon => option.value == nodeIcon ) ? `checked-gray-box` : \"\";\r\n            return option;\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        _personalization.children = [\r\n          {\r\n            actionType: _personalization.actionType,\r\n            name: <IconSelectedMenu selectValue={nodeData.tagColor?.toString()} onChange={onClick} optionList={titleIconOptions} actionType={_personalization.actionType}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else  {\r\n        return _personalization;\r\n      }\r\n    })\r\n    _personalizationList =  _personalizationList.flatMap((taskGroup) => taskGroup.children);\r\n    return _personalizationList;\r\n  }\r\n\r\n\r\n  // menu item click\r\n  const onClick = ({ id, props, data, triggerEvent, ...args }) => {\r\n    let arr = id.split(\"&\") // 存在快捷方式-999的nodeType\r\n    let callbackData = {\r\n      actionType: arr[0],\r\n      colorType: arr[1],\r\n      objType: data?.objType,\r\n      productId: data?.productId //20250724 Jim Song 后端多加一个参数productId,用来表征如果menuStatus异常时，知晓是哪一个产品id\r\n    }\r\n    console.log(callbackData)\r\n    onContextMenuClick(callbackData)\r\n  }\r\n\r\n  const _onMoreBtnClick = (e) => {\r\n    onMoreBtnClick && onMoreBtnClick({ nodeItem: cacheRef.current.nodeData, ctxType: e.actionType, colorType: e.colorType, ...callbackParams })\r\n  }\r\n\r\n  const _onCreateBtnClick = (e) => {\r\n    onCreateBtnClick && onCreateBtnClick({ nodeItem: cacheRef.current.nodeData, nodeType: e.colorType, ...callbackParams})\r\n  }\r\n\r\n  const onContextMenuClick = ({ actionType, colorType, productId }) => {\r\n    if (actionType == eCtxTypeId.ctx_38_create) { // 新建操作\r\n      // 注意:设置图标颜色无法查询出来\r\n      const node = findByActionAndColorType(createTypeList, actionType, colorType);\r\n      if(node.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ){ // 免费对象数已达上限\r\n        return resourceMaxModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);\r\n      }\r\n      if(node.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ){ // Vip未授权\r\n        return unVipModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId,applyProductAuthorize);\r\n      }\r\n      if(node.menuStatus == eMenuStatus.status_4_Vip_Expired ){ // Vip已过期\r\n        return expiredModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);\r\n      }\r\n      _onCreateBtnClick({ colorType })\r\n    } else {\r\n      _onMoreBtnClick({ actionType, colorType })\r\n    }\r\n  }\r\n\r\n  // 根据actionType和colorType查找节点\r\n  function findByActionAndColorType(list, actionType, colorType) {\r\n      for (let i in list) {\r\n        if (list[i].actionType == actionType && list[i].colorType == colorType) {\r\n          return list[i];\r\n        }\r\n        if (list[i].children) {\r\n          let node = findByActionAndColorType(list[i].children, actionType, colorType);\r\n          if (node) {\r\n            return node\r\n          }\r\n        }\r\n      }\r\n  }\r\n\r\n  // 右击菜单\r\n  // CtxMenuItem、CtxSubmenu <'fade' | 'scale' | 'flip' | 'slide'>\r\n  return (\r\n    <CtxMenu id={id} animation={{ enter: false, exit: 'slide' }} onVisibilityChange={handleOnVisibilityChange} className=\"\">\r\n      {isCtxLoading && <ContextLoadingBoard />}\r\n      {/* 无可用选项 */}\r\n      {!isCtxLoading && createTypeList.length == 0 && <CtxMenuItem disabled> 无可用选项 </CtxMenuItem>}\r\n      {/* 逻辑确定只有两层所以这么处理 */}\r\n      {!isCtxLoading && createTypeList.length > 0 && createTypeList.map(el => {\r\n        const key = `${el.actionType}&${el.colorType}`;\r\n        if (!isEmpty(el.children)) {\r\n          return <DelayedCtxSubMenu key={key} label={\r\n            <>\r\n              <span className={\"iconfont \" + getCtxIconByType(el.actionType) + \" fontsize-12 fontcolor-normal marginRight-5\"}></span>\r\n              <span>{el.name}</span>\r\n            </>}\r\n            // 子级菜单箭头\r\n            arrow={<RightOutlined />}\r\n          >\r\n            {/* 横向子菜单面板 - 只对新建菜单使用 */}\r\n            <div className={el.actionType == eCtxTypeId.ctx_38_create ? \"horizontal-submenu-panel\" : \"\"}>\r\n              {el.actionType == eCtxTypeId.ctx_38_create ? (\r\n                // 新建菜单：按分组显示\r\n                (() => {\r\n                  const items = [];\r\n                  let currentGroupItems = [];\r\n                  \r\n                  el.children.forEach((sub, index) => {\r\n                    const key = `${sub.actionType}&${sub.colorType}`;\r\n                    \r\n                    // 如果是分组标题\r\n                    if (sub.type == eCtxOptionType.eGroup) {\r\n                      // 如果有累积的分组内容，先渲染它们\r\n                      if (currentGroupItems.length > 0) {\r\n                        items.push(\r\n                          <div key={`group-${index}-content`} className=\"group-content-row\">\r\n                            {currentGroupItems}\r\n                          </div>\r\n                        );\r\n                        currentGroupItems = [];\r\n                      }\r\n                      \r\n                      // 添加分组标题\r\n                      items.push(\r\n                        <div key={index} className=\"group-title-row\">\r\n                          <div className=\"group-title\">{sub.name}</div>\r\n                        </div>\r\n                      );\r\n                    } else {\r\n                      // 如果是分组内容\r\n                      currentGroupItems.push(\r\n                        <div key={index} className=\"submenu-item-wrapper normal-menu-item\">\r\n                          <CtxMenuItem\r\n                            id={key}\r\n                            data={sub}\r\n                            disabled={sub.disabled}\r\n                            onClick={onClick}\r\n                          >\r\n                            {\r\n                              sub.actionType == eCtxTypeId.ctx_38_create ?\r\n                              <AppNodeResourceIcon nodeType={sub.colorType} className=\"fontsize-12 fontcolor-normal marginRight-5\" style={{lineHeight: \"24px\"}}/>: \r\n                              sub.actionType == eCtxTypeId.ctx_12_create_shortcut ?\r\n                              (getCtxIconByType(sub.colorType) && <span className={\"iconfont \" + getCtxIconByType(sub.colorType) + \" fontsize-12 fontcolor-normal marginRight-5\"}></span>) :\r\n                              <span></span>\r\n                            }\r\n                            <div style={{ width: \"100%\" }}>{sub.name}</div>\r\n                            {/* 对象状态图标 */}\r\n                            {\r\n                              <span className = {`iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `}\r\n                               style={{color: getMenuStatusIcon(sub.menuStatus).iconColor}}\r\n                               title={sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ?\r\n                                   '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ?\r\n                                                    '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ?\r\n                                                                   '应用已过期' : ''}>\r\n                              </span>\r\n                            }\r\n                            <span></span>\r\n                          </CtxMenuItem>\r\n                        </div>\r\n                      );\r\n                    }\r\n                  });\r\n                  \r\n                  // 处理最后的分组内容\r\n                  if (currentGroupItems.length > 0) {\r\n                    items.push(\r\n                      <div key=\"group-last-content\" className=\"group-content-row\">\r\n                        {currentGroupItems}\r\n                      </div>\r\n                    );\r\n                  }\r\n                  \r\n                  return items;\r\n                })()\r\n              ) : (\r\n                // 其他菜单：保持原有布局\r\n                el.children.map((sub, index) => {\r\n                  const key = `${sub.actionType}&${sub.colorType}`;\r\n                  return (\r\n                    <div key={index} className=\"\">\r\n                      <CtxMenuItem\r\n                        id={key}\r\n                        data={sub}\r\n                        disabled={sub.disabled}\r\n                        onClick={(\r\n                          sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || \r\n                          sub.actionType == eCtxTypeId.ctx_3_color_txt || \r\n                          sub.actionType == eCtxTypeId.ctx_42_set_icon || \r\n                          sub.actionType == eCtxTypeId.ctx_46_text_font ||\r\n                          sub.actionType == eCtxTypeId.ctx_61_flag_color ||\r\n                          sub.actionType == eCtxTypeId.ctx_62_flag_img\r\n                          ) ? () => {} : onClick\r\n                        }\r\n                        style={sub.type != eCtxOptionType.eGroup ? { paddingLeft: \"24px\" } : {}}\r\n                        className={ (sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || \r\n                          sub.actionType == eCtxTypeId.ctx_3_color_txt || \r\n                          sub.actionType == eCtxTypeId.ctx_42_set_icon || \r\n                          sub.actionType == eCtxTypeId.ctx_46_text_font ||\r\n                          sub.actionType == eCtxTypeId.ctx_61_flag_color ||\r\n                          sub.actionType == eCtxTypeId.ctx_62_flag_img\r\n                          ) ? \"context-item-not-focus\" : \"\" }\r\n                      >\r\n                        {\r\n                          sub.actionType == eCtxTypeId.ctx_38_create ?\r\n                          <AppNodeResourceIcon nodeType={sub.colorType} className=\"fontsize-12 fontcolor-normal marginRight-5\" style={{lineHeight: \"24px\"}}/>: \r\n                          sub.actionType == eCtxTypeId.ctx_12_create_shortcut ?\r\n                          (getCtxIconByType(sub.colorType) && <span className={\"iconfont \" + getCtxIconByType(sub.colorType) + \" fontsize-12 fontcolor-normal marginRight-5\"}></span>) :\r\n                          <span></span>\r\n                        }\r\n                        <div style={{ width: \"100%\" }}>{sub.name}</div>\r\n                        {/* 对象状态图标 */}\r\n                        {\r\n                          <span className = {`iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `}\r\n                           style={{color: getMenuStatusIcon(sub.menuStatus).iconColor}}\r\n                           title={sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ?\r\n                               '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ?\r\n                                                '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ?\r\n                                                               '应用已过期' : ''}>\r\n                          </span>\r\n                        }\r\n                        <span></span>\r\n                      </CtxMenuItem>\r\n                    </div>\r\n                  );\r\n                })\r\n              )}\r\n            </div>\r\n          </DelayedCtxSubMenu>\r\n        } else {\r\n          return <CtxMenuItem key={key} id={key} onClick={onClick} >\r\n            {el.actionType == 28 ?\r\n            <span className={\"iconfont \" + getCtxIconByType(el.actionType) + ' fontsize-12 fontcolor-normal'} style={{marginLeft:-5,marginRight:5}}></span>\r\n            :\r\n            <span className={\"iconfont \" + getCtxIconByType(el.actionType) + \" fontsize-12 fontcolor-normal marginRight-5\"}></span>\r\n            }\r\n            <div style={{ width: \"100%\" }}>{el.name}</div>\r\n          </CtxMenuItem>\r\n        }\r\n      })}\r\n\r\n    </CtxMenu>\r\n  )\r\n}\r\n\r\n\r\nfunction ContextLoadingBoard() {\r\n  return <React.Fragment>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n  </React.Fragment>\r\n}\r\n\r\n\r\nexport default forwardRef(ContextBoard)\r\n\r\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,aAAa,QAAQ,mBAAmB;AAChE,SAASC,6BAA6B,QAAQ,kBAAkB;AAChE,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,SAASC,qCAAqC,EAAEC,mCAAmC,QAAQ,6BAA6B;AACxH,SAASC,OAAO,EAAEC,WAAW,EAAEC,cAAc,EAAEC,aAAa,QAAQ,0BAA0B;AAC9F,SAASC,cAAc,EAAEC,UAAU,EAAEC,UAAU,QAAQ,oBAAoB;AAC3E,SAASC,UAAU,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,iBAAiB,QAAQ,yBAAyB;AACzH,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,yBAAyB;AACpF,SAASC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AACvD,OAAOC,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChF,SAASC,IAAI,IAAIC,WAAW,EAAEC,IAAI,IAAIC,OAAO,EAAEC,OAAO,IAAIC,UAAU,EAAEC,cAAc,EAAEC,SAAS,QAAQ,iBAAiB;AACxH,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,oCAAoC;AAC3C,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAO,qBAAqB;AAC5B,OAAO,KAAKC,KAAK,MAAM,kBAAkB;AACzC,SAASC,+BAA+B,QAAQ,iCAAiC;AACjF,SAASC,UAAU,QAAQ,0BAA0B;;AAGrD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,iBAAiBA,CAAC;EAAEC,WAAW;EAAEC,QAAQ;EAAEC,gBAAgB;EAAEC;AAAW,CAAC,EAAE;EAClF,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnC,IAAIC,KAAK,GAAGD,YAAY,CAACE,MAAM,GAAGF,YAAY,CAACA,YAAY,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,EAAC;IAC7EN,QAAQ,IAAIA,QAAQ,CAAC;MAAEO,EAAE,EAAEL,UAAU,GAAG,GAAG,GAAGG;IAAM,CAAC,CAAC;EACxD,CAAC;EACD,oBAAOV,OAAA;IAAKa,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,eAC7Cf,OAAA,CAACvB,QAAQ,CAACuC,KAAK;MAACN,KAAK,EAAE,CAACN,WAAW,CAAE;MAACC,QAAQ,EAAEG,UAAW;MAAAO,QAAA,EACxDT,gBAAgB,CAACW,GAAG,CAACC,IAAI,iBAAIlB,OAAA,CAACvB,QAAQ;QAAC0C,IAAI,EAAC,KAAK;QAA4BT,KAAK,EAAEQ,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAAE;QAACC,SAAS,EAAEJ,IAAI,CAACI;MAAU,GAA7EJ,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqE,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AACR;;AAEA;AAAAC,EAAA,GAZSxB,iBAAiB;AAa1B,SAASyB,iBAAiBA,CAAC;EAAExB,WAAW;EAAEC,QAAQ;EAAEC,gBAAgB;EAAEC;AAAW,CAAC,EAAE;EAClF,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnC,IAAIC,KAAK,GAAGD,YAAY,CAACE,MAAM,GAAGF,YAAY,CAACA,YAAY,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,EAAC;IAC7EN,QAAQ,IAAIA,QAAQ,CAAC;MAAEO,EAAE,EAAEL,UAAU,GAAG,GAAG,GAAGG;IAAM,CAAC,CAAC;EACxD,CAAC;EACD,oBAAOV,OAAA;IAAKa,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,eAC7Cf,OAAA,CAACvB,QAAQ,CAACuC,KAAK;MAACN,KAAK,EAAE,CAACN,WAAW,CAAE;MAACC,QAAQ,EAAEG,UAAW;MAAAO,QAAA,EACxDT,gBAAgB,CAACW,GAAG,CAACC,IAAI,iBAAIlB,OAAA,CAACvB,QAAQ;QAAC0C,IAAI,EAAC,KAAK;QAA4BT,KAAK,EAAEQ,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAAE;QAACC,SAAS,EAAEJ,IAAI,CAACI,SAAU;QAAAP,QAAA,EACnIX,WAAW,IAAIc,IAAI,CAACE,IAAI,GAAG,EAAE,GAAGF,IAAI,CAACW;MAAK,GADYX,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElE,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AACR;;AAEA;AAAAI,GAAA,GAdSF,iBAAiB;AAe1B,SAASG,gBAAgBA,CAAC;EAAE1B,QAAQ;EAAE2B,UAAU;EAAEzB;AAAW,CAAC,EAAE;EAC9D,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnCJ,QAAQ,IAAIA,QAAQ,CAAC;MAAEO,EAAE,EAAEL,UAAU,GAAG,GAAG,GAAGE;IAAa,CAAC,CAAC;EAC/D,CAAC;EACD,oBAAOT,OAAA,CAACtB,KAAK;IAACuD,IAAI,EAAE,EAAG;IAAAlB,QAAA,EACpBiB,UAAU,CAACf,GAAG,CAAEiB,MAAM,iBACrBlC,OAAA;MAAwBmC,OAAO,EAAEA,CAAA,KAAI3B,UAAU,CAAC0B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEd,IAAI,CAAE;MAACE,SAAS,EAAEY,MAAM,CAACZ,SAAU;MAAAP,QAAA,EACzFrB,SAAS,CAACwC,MAAM,CAACE,IAAI;IAAC,GADdF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEd,IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AACV;;AAEA;AAAAW,GAAA,GAbSN,gBAAgB;AAczB,SAASO,gBAAgBA,CAAC;EAAEjC,QAAQ;EAAE2B,UAAU;EAAEzB;AAAW,CAAC,EAAE;EAC9D,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnCJ,QAAQ,IAAIA,QAAQ,CAAC;MAAEO,EAAE,EAAEL,UAAU,GAAG,GAAG,GAAGE;IAAa,CAAC,CAAC;EAC/D,CAAC;EACD,oBAAOT,OAAA,CAACtB,KAAK;IAACuD,IAAI,EAAE,EAAG;IAAAlB,QAAA,EACpBiB,UAAU,CAACf,GAAG,CAAEiB,MAAM,iBACrBlC,OAAA;MAAwBmC,OAAO,EAAEA,CAAA,KAAI3B,UAAU,CAAC0B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAExB,KAAK,CAAE;MAACY,SAAS,EAAEY,MAAM,CAACZ,SAAU;MAAAP,QAAA,EAC1FrB,SAAS,CAACwC,MAAM,CAACE,IAAI;IAAC,GADdF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEd,IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAAa,GAAA,GA/CSD,gBAAgB;AAgDzB,MAAME,YAAY,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,KAAK;EAC3C,oBAAO3C,OAAA;IAAKa,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAE8B,cAAc,EAAE,eAAe;MAAEC,UAAU,EAAE,QAAQ;MAAEF;IAAM,CAAE;IAAA5B,QAAA,gBACnGf,OAAA;MAAAe,QAAA,EAAO2B;IAAK;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,EACnBe,IAAI,gBAAGzC,OAAA,CAAC5C,aAAa;MAACyD,KAAK,EAAE;QAAE8B,KAAK,EAAE;MAAU;IAAE;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAG1B,OAAA,CAAAE,SAAA,mBAAI,CAAC;EAAA;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3D,CAAC;AACR,CAAC;;AAED;AACA,MAAMoB,iBAAiB,GAAGA,CAAC;EAAE/B,QAAQ;EAAE2B,KAAK;EAAEK,KAAK;EAAE,GAAGC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAMC,UAAU,GAAGlE,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmE,YAAY,GAAGnE,MAAM,CAAC,IAAI,CAAC;EAEjCH,KAAK,CAACuE,SAAS,CAAC,MAAM;IACpB,MAAMC,SAAS,GAAGF,YAAY,CAACG,OAAO;IACtC,IAAI,CAACD,SAAS,EAAE;IAEhB,MAAME,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACA,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBE,YAAY,CAACN,UAAU,CAACI,OAAO,CAAC;QAChCJ,UAAU,CAACI,OAAO,GAAG,IAAI;MAC3B;IACF,CAAC;IAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACAP,UAAU,CAACI,OAAO,GAAGI,UAAU,CAAC,MAAM;QACpC;QACA,MAAMC,OAAO,GAAGN,SAAS,CAACO,aAAa,CAAC,eAAe,CAAC;QACxD,IAAID,OAAO,EAAE;UACXA,OAAO,CAAC9C,KAAK,CAACC,OAAO,GAAG,MAAM;QAChC;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED;IACAuC,SAAS,CAACQ,gBAAgB,CAAC,YAAY,EAAEN,gBAAgB,CAAC;IAC1DF,SAAS,CAACQ,gBAAgB,CAAC,YAAY,EAAEJ,gBAAgB,CAAC;;IAE1D;IACA,MAAMK,QAAQ,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAK;MACnDA,SAAS,CAACC,OAAO,CAAEC,QAAQ,IAAK;QAC9BA,QAAQ,CAACC,UAAU,CAACF,OAAO,CAAEG,IAAI,IAAK;UACpC,IAAIA,IAAI,CAACC,QAAQ,KAAKC,IAAI,CAACC,YAAY,IAAIH,IAAI,CAACI,YAAY,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE;YAC/E;YACAJ,IAAI,CAACP,gBAAgB,CAAC,YAAY,EAAEN,gBAAgB,CAAC;YACrDa,IAAI,CAACP,gBAAgB,CAAC,YAAY,EAAEJ,gBAAgB,CAAC;;YAErD;YACAW,IAAI,CAACvD,KAAK,CAACC,OAAO,GAAG,EAAE;UACzB;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFgD,QAAQ,CAACW,OAAO,CAACC,QAAQ,CAACC,IAAI,EAAE;MAC9BC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,OAAO,MAAM;MACXxB,SAAS,CAACyB,mBAAmB,CAAC,YAAY,EAAEvB,gBAAgB,CAAC;MAC7DF,SAAS,CAACyB,mBAAmB,CAAC,YAAY,EAAErB,gBAAgB,CAAC;MAC7DK,QAAQ,CAACiB,UAAU,CAAC,CAAC;MACrB,IAAI7B,UAAU,CAACI,OAAO,EAAE;QACtBE,YAAY,CAACN,UAAU,CAACI,OAAO,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEtD,OAAA;IAAKgF,GAAG,EAAE7B,YAAa;IAAApC,QAAA,eACrBf,OAAA,CAACT,UAAU;MAAA,GACLyD,KAAK;MACTN,KAAK,EAAEA,KAAM;MACbK,KAAK,EAAEA,KAAM;MAAAhC,QAAA,EAEZA;IAAQ;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPAuB,EAAA,CA3EMH,iBAAiB;AAAAmC,GAAA,GAAjBnC,iBAAiB;AAmFvB,SAASoC,YAAYA,CAAC;EAAEC,MAAM;EAAEC,cAAc;EAAEC,gBAAgB;EAAEzE,EAAE;EAAE0E,wBAAwB;EAAE,GAAGC;AAAe,CAAC,EAAEP,GAAG,EAAE;EAAAQ,GAAA;EACxH;EACA;EACA,MAAM;IAAEC,IAAI,EAAE;MAAEC,MAAM;MAAEC,aAAa,EAAEC;IAAY,CAAC,GAAG;MAACF,MAAM,EAAEG,SAAS;MAAEF,aAAa,EAAEE;IAAS;EAAE,CAAC,GAClGrI,qCAAqC,CAAC;IAAC2H,MAAM;IAAEW,OAAO,EAAE;EAAI,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM;IAAEL,IAAI,EAAEM;EAAc,CAAC,GAAGtI,mCAAmC,CAAC0H,MAAM,CAAC,CAAC,CAAC;;EAE7E,MAAM;IAACa,SAAS,EAACC,YAAY;IAAER,IAAI,EAACS,cAAc,GAAC,EAAE;IAAEC;EAAW,CAAC,GAAGxG,WAAW,CAAC;IAChFyG,UAAU,EAAEA,CAAC;MAACC,QAAQ;MAACC,MAAM;MAACC,iBAAiB;MAACC,aAAa;MAAEnC;IAAQ,CAAC,KAAKoC,mBAAmB,CAACJ,QAAQ,EAACC,MAAM,EAACC,iBAAiB,EAACC,aAAa,EAAEnC,QAAQ;EAC5J,CAAC,CAAC;EAEF,MAAMqC,kBAAkB,GAAG/G,WAAW,CAAC;IACrCyG,UAAU,EAAExG,KAAK,CAAC+G;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,qBAAqB,GAAIC,SAAS,IAAK;IAC3C,IAAG,CAAC,CAACA,SAAS,EAAE;MACdH,kBAAkB,CAACI,MAAM,CAAC;QAAE3B,MAAM;QAAE0B;MAAU,CAAC,EAAE;QAC/CE,SAAS,EAAGC,MAAM,IAAK;UACrB,IAAGA,MAAM,CAACC,UAAU,KAAK,GAAG,EAAE;YAC5B;YACArI,KAAK,CAACsI,IAAI,CAAC;cACTrF,KAAK,EAAE,IAAI;cACXsF,OAAO,EAAE,yBAAyB;cAClCC,YAAY,EAAE,IAAI;cAClB;cACAC,MAAM,EAAE,MAAM;cACdC,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGvI,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3B,MAAM;IAAEwI;EAAK,CAAC,GAAGhI,cAAc,CAAC;IAAEoB,EAAE,EAAEA;EAAG,CAAC,CAAC;EAE3C7B,mBAAmB,CAACiG,GAAG,EAAE,OAAO;IAC9B;IACA;IACA;IACA;IACA;IACA;IACAyC,gBAAgB,EAAEA,CAACC,CAAC,EAAErB,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAEnC,QAAQ,KAAKsD,OAAO,CAACD,CAAC,EAAErB,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAEnC,QAAQ;EAChK,CAAC,CAAC,CAAC;;EAEH;EACA;EACA,MAAMsD,OAAO,GAAG,MAAAA,CAAOD,CAAC,EAAErB,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAEnC,QAAQ,KAAK;IACzF;IACA,MAAMuD,UAAU,GAAG,GAAG,CAAC,CAAC;IACxB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;IACvB,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;IACvC,MAAMC,WAAW,GAAGF,MAAM,CAACG,UAAU;IACrC,MAAMC,MAAM,GAAGT,CAAC,CAACU,OAAO;IACxB,MAAMC,MAAM,GAAGX,CAAC,CAACY,OAAO;;IAExB;IACA,MAAMC,SAAS,GAAGJ,MAAM,GAAGP,UAAU,GAAGE,YAAY,GAClDU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,YAAY,GAAGF,UAAU,GAAG,EAAE,CAAC,GAC3CO,MAAM;;IAER;IACA,MAAMO,SAAS,GAAGL,MAAM,GAAGR,SAAS,GAAGI,WAAW,GAChDO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,WAAW,GAAGJ,SAAS,GAAG,EAAE,CAAC,GACzCQ,MAAM;;IAER;IACAb,IAAI,CAAC;MACHmB,KAAK,EAAEjB,CAAC;MACR1E,KAAK,EAAEqD,QAAQ;MACfuC,QAAQ,EAAE;QACRC,CAAC,EAAEH,SAAS;QACZI,CAAC,EAAEP;MACL;IACF,CAAC,CAAC;IAEFpC,WAAW,CAAC;MAACE,QAAQ;MAAEC,MAAM;MAAEC,iBAAiB;MAAEC,aAAa;MAAEnC;IAAQ,CAAC,CAAC;IAC3EkD,QAAQ,CAACjE,OAAO,CAAC+C,QAAQ,GAAGA,QAAQ;EACtC,CAAC;;EAED;EACA;EACA,MAAMI,mBAAmB,GAAG,MAAAA,CAAOJ,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAEnC,QAAQ,KAAK;IAClG,IAAI2C,MAAM,GAAG,CAACtJ,OAAO,CAAC8I,aAAa,CAAC,GAAG;MAAES,UAAU,EAAE,GAAG;MAAET;IAAc,CAAC,GAAG,MAAMlJ,6BAA6B,CAAC;MAAE6H,MAAM;MAAEmB,MAAM;MAAEjC;IAAQ,CAAC,CAAC;IAC5I,IAAI2C,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,IAAI;QAACT,aAAa;QAAEuC;MAAW,CAAC,GAAG/B,MAAM;MACzC,IAAG,CAACtJ,OAAO,CAAC6I,iBAAiB,CAAC,EAAEC,aAAa,GAAGA,aAAa,CAACwC,MAAM,CAACC,SAAS,IAAI,CAAC,CAAC1C,iBAAiB,IAAI,EAAE,EAAE2C,IAAI,CAAC3I,UAAU,IAAI0I,SAAS,CAAC1I,UAAU,IAAIA,UAAU,CAAC,CAAC,EAAC;MACrKiG,aAAa,CAACvC,OAAO,CAACgF,SAAS,IAAI;QACjC,QAAQ,CAAEA,SAAS,CAAC1I,UAAU;UAC5B,KAAKtC,UAAU,CAACkL,aAAa;YAAE;YAC7BF,SAAS,CAAClI,QAAQ,GAAGqI,sBAAsB,CAACH,SAAS,CAAClI,QAAQ,CAAC;YAC/D;UACF,KAAK9C,UAAU,CAACoL,sBAAsB;YAAE;YACtCJ,SAAS,CAAClI,QAAQ,GAAGuI,2BAA2B,CAACL,SAAS,CAAClI,QAAQ,EAAEsF,QAAQ,CAAC;YAC9E;UACF,KAAKpI,UAAU,CAACsL,gBAAgB;YAAE;YAChCN,SAAS,CAAClI,QAAQ,GAAGyI,YAAY,CAACP,SAAS,CAAClI,QAAQ,EAAEsF,QAAQ,CAAC;YAC/D;UACF,KAAKpI,UAAU,CAACwL,eAAe;YAAE;YAC/B,MAAMC,YAAY,GAAGX,WAAW,IAAIhL,UAAU,CAAC4L,MAAM;YACrDV,SAAS,CAACW,SAAS,GAAGF,YAAY,GAAG3L,UAAU,CAAC8L,OAAO,GAAG9L,UAAU,CAAC4L,MAAM;YAC3EV,SAAS,CAAC9H,IAAI,GAAGqB,YAAY,CAACkH,YAAY,EAAET,SAAS,CAAC9H,IAAI,CAAC;YAC3D;UACF,KAAKlD,UAAU,CAAC6L,UAAU;YAAE;YAC1B,MAAMC,OAAO,GAAG1D,QAAQ,CAAC2D,MAAM,IAAIjM,UAAU,CAAC4L,MAAM;YACpDV,SAAS,CAACW,SAAS,GAAGG,OAAO,GAAGhM,UAAU,CAAC8L,OAAO,GAAG9L,UAAU,CAAC4L,MAAM;YACtEV,SAAS,CAAC9H,IAAI,GAAGqB,YAAY,CAACuH,OAAO,EAAEd,SAAS,CAAC9H,IAAI,CAAC;YACtD;UACF,KAAKlD,UAAU,CAACgM,cAAc;YAAE;YAC9B,MAAMC,QAAQ,GAAG7D,QAAQ,CAAC8D,OAAO,IAAIpM,UAAU,CAAC4L,MAAM;YACtDV,SAAS,CAACW,SAAS,GAAGM,QAAQ,GAAGnM,UAAU,CAAC8L,OAAO,GAAG9L,UAAU,CAAC4L,MAAM;YACvEV,SAAS,CAAC9H,IAAI,GAAGqB,YAAY,CAAC0H,QAAQ,EAAEjB,SAAS,CAAC9H,IAAI,CAAC;YACvD;UACF;YACE8H,SAAS,CAAClI,QAAQ,GAAGkI,SAAS,CAAClI,QAAQ,CAACE,GAAG,CAAEmJ,IAAI,KAAI;cAAC,GAAGA,IAAI;cAAE7J,UAAU,EAAE0I,SAAS,CAAC1I,UAAU;cAAEqJ,SAAS,EAAEQ,IAAI,CAAC7J;YAAU,CAAC,CAAC,CAAC;YAC9H;QACJ;MACF,CAAC,CAAC;MACF,OAAOiG,aAAa;IACtB,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;;EAID;EACA,MAAM4C,sBAAsB,GAAIlD,cAAc,IAAK;IACjD,IAAImE,eAAe,GAAGxM,aAAa,CAACqI,cAAc,EAAE,KAAK,CAAC;IAC1DmE,eAAe,GAAG1M,WAAW,CAAC0M,eAAe,CAAC;IAC9CA,eAAe,CAACpG,OAAO,CAAEqG,WAAW,IAAG;MACrCA,WAAW,CAACV,SAAS,GAAGU,WAAW,CAAC/J,UAAU;MAC9C+J,WAAW,CAAC/J,UAAU,GAAGtC,UAAU,CAACkL,aAAa;IACnD,CAAC,CAAC;IACF,OAAOkB,eAAe;EACxB,CAAC;;EAED;EACA,MAAMf,2BAA2B,GAAGA,CAACiB,mBAAmB,EAAElE,QAAQ,KAAK;IACrE,IAAImE,oBAAoB,GAAG3M,aAAa,CAAC0M,mBAAmB,EAAE,IAAI,CAAC;IACnEC,oBAAoB,GAAGA,oBAAoB,CAACvJ,GAAG,CAAEwJ,gBAAgB,IAAG;MAClE,IAAGA,gBAAgB,CAAClK,UAAU,IAAItC,UAAU,CAACyM,qBAAqB,EAAC;QAAA,IAAAC,qBAAA;QAAE;QACnE,MAAMC,gBAAgB,GAAG,CAACH,gBAAgB,CAAC1J,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAAC4J,MAAM,IAAI;UACvE,IAAI;YACF,OAAOC,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC1J,IAAI,CAAC;UAChC,CAAC,CAAC,OAAO6J,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAAC1J,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACFsJ,gBAAgB,CAAC1J,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAEtC,UAAU,CAACyM,qBAAqB;UAC5CvJ,IAAI,eAAEnB,OAAA,CAACG,iBAAiB;YAACC,WAAW,GAAAuK,qBAAA,GAAEtE,QAAQ,CAAC6E,gBAAgB,cAAAP,qBAAA,uBAAzBA,qBAAA,CAA2BtJ,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAAC7B,gBAAgB,EAAIsK,gBAAiB;YAACrK,UAAU,EAAEtC,UAAU,CAACyM;UAAsB;YAAAnJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACtL,CAAC,CACF;QACD,OAAO+I,gBAAgB;MACzB,CAAC,MAAM,IAAGA,gBAAgB,CAAClK,UAAU,IAAItC,UAAU,CAACkN,eAAe,EAAC;QAAA,IAAAC,qBAAA;QAAE;QACpE,MAAMC,gBAAgB,GAAG,CAACZ,gBAAgB,CAAC1J,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAAC4J,MAAM,IAAI;UACvE,IAAI;YACF,IAAI3I,MAAM,GAAG4I,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC1J,IAAI,CAAC;YACpC,OAAOe,MAAM;UACf,CAAC,CAAC,OAAO8I,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAAC1J,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACH;AACP;AACA;AACA;AACA;AACA;AACA;AACA;QACQ8J,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAED,gBAAgB,CAAC;QACjDZ,gBAAgB,CAAC1J,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAEtC,UAAU,CAACkN,eAAe;UACtChK,IAAI,eAAEnB,OAAA,CAAC4B,iBAAiB;YAACxB,WAAW,GAAAgL,qBAAA,GAAE/E,QAAQ,CAACkF,iBAAiB,cAAAH,qBAAA,uBAA1BA,qBAAA,CAA4B/J,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAAC7B,gBAAgB,EAAI+K,gBAAiB;YAAC9K,UAAU,EAAEtC,UAAU,CAACkN;UAAgB;YAAA5J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACjL,CAAC,CACF;QACD,OAAO+I,gBAAgB;MACzB,CAAC,MAAM,IAAGA,gBAAgB,CAAClK,UAAU,IAAItC,UAAU,CAACuN,gBAAgB,EAAE;QAAA,IAAAC,qBAAA;QAAE;QACtE,IAAIC,mBAAmB,GAAG,CAACjB,gBAAgB,CAAC1J,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAAC4J,MAAM,IAAI;UACxE,IAAI;YAAA,IAAAc,iBAAA,EAAAC,qBAAA;YACF,IAAI1J,MAAM,GAAI4I,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC1J,IAAI,CAAC;YACrC,IAAI0K,WAAW,GAAGjO,cAAc,CAACmI,aAAa,CAAC;YAC/C7D,MAAM,CAACE,IAAI,IAAAuJ,iBAAA,GAAGE,WAAW,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAI9J,MAAM,CAACxB,KAAK,CAAC,cAAAiL,iBAAA,uBAArDA,iBAAA,CAAuDM,SAAS;YAC9E,MAAMC,oBAAoB,GAAG,EAAAN,qBAAA,GAAAvF,QAAQ,CAAC8F,gBAAgB,cAAAP,qBAAA,uBAAzBA,qBAAA,CAA2BQ,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE,CAAC,CAAC;YAC1ElK,MAAM,CAACZ,SAAS,GAAG4K,oBAAoB,CAAC7N,iBAAiB,CAAC6D,MAAM,CAACd,IAAI,CAAC,CAACiL,GAAG,CAAC,IAAItO,UAAU,CAAC4L,MAAM,GAAG,kBAAkB,GAAG,EAAE;YAC1H,OAAOzH,MAAM;UACf,CAAC,CAAC,OAAO8I,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAAC1J,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACF8J,OAAO,CAACK,GAAG,CAAC,qBAAqB,EAAEI,mBAAmB,CAAC;QACvDjB,gBAAgB,CAAC1J,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAEkK,gBAAgB,CAAClK,UAAU;UACvCY,IAAI,eAAEnB,OAAA,CAAC+B,gBAAgB;YAAC3B,WAAW,GAAAqL,qBAAA,GAAEpF,QAAQ,CAACiG,YAAY,cAAAb,qBAAA,uBAArBA,qBAAA,CAAuBpK,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAACH,UAAU,EAAE0J,mBAAoB;YAACnL,UAAU,EAAEkK,gBAAgB,CAAClK;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACvK,CAAC,CACF;QACD,OAAO+I,gBAAgB;MACzB,CAAC,MAAM,IAAGA,gBAAgB,CAAClK,UAAU,IAAItC,UAAU,CAACsO,eAAe,EAAC;QAAA,IAAAC,sBAAA;QAAE;QACpE,IAAIC,gBAAgB,GAAG,CAAChC,gBAAgB,CAAC1J,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAAC4J,MAAM,IAAI;UACrE,IAAI;YAAA,IAAA6B,kBAAA,EAAAC,sBAAA;YACF,IAAIzK,MAAM,GAAI4I,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC1J,IAAI,CAAC;YACrC,IAAI0K,WAAW,GAAGjO,cAAc,CAACmI,aAAa,CAAC;YAC/C7D,MAAM,CAACE,IAAI,IAAAsK,kBAAA,GAAGb,WAAW,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAI9J,MAAM,CAACxB,KAAK,CAAC,cAAAgM,kBAAA,uBAArDA,kBAAA,CAAuDT,SAAS;YAC9E/J,MAAM,CAACZ,SAAS,GAAG,CAAC,EAAAqL,sBAAA,GAAAtG,QAAQ,CAACiG,YAAY,cAAAK,sBAAA,uBAArBA,sBAAA,CAAuBP,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE,EAAElD,IAAI,CAAC0D,QAAQ,IAAI1K,MAAM,CAACxB,KAAK,IAAIkM,QAAS,CAAC,GAAG,kBAAkB,GAAG,EAAE;YAClI,OAAO1K,MAAM;UACf,CAAC,CAAC,OAAO8I,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAAC1J,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACFsJ,gBAAgB,CAAC1J,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAEkK,gBAAgB,CAAClK,UAAU;UACvCY,IAAI,eAAEnB,OAAA,CAACsC,gBAAgB;YAAClC,WAAW,GAAAoM,sBAAA,GAAEnG,QAAQ,CAACiG,YAAY,cAAAE,sBAAA,uBAArBA,sBAAA,CAAuBnL,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAACH,UAAU,EAAEyK,gBAAiB;YAAClM,UAAU,EAAEkK,gBAAgB,CAAClK;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACpK,CAAC,CACF;QACD,OAAO+I,gBAAgB;MACzB,CAAC,MAAO;QACN,OAAOA,gBAAgB;MACzB;IACF,CAAC,CAAC;IACFD,oBAAoB,GAAG7M,WAAW,CAAC6M,oBAAoB,CAAC;IACxD,OAAOA,oBAAoB;EAC7B,CAAC;;EAED;EACA,MAAMhB,YAAY,GAAGA,CAACe,mBAAmB,EAAElE,QAAQ,KAAK;IACtD,IAAImE,oBAAoB,GAAG3M,aAAa,CAAC0M,mBAAmB,EAAE,IAAI,CAAC;IACnEC,oBAAoB,GAAGA,oBAAoB,CAACvJ,GAAG,CAAEwJ,gBAAgB,IAAG;MAClE,IAAGA,gBAAgB,CAAClK,UAAU,IAAItC,UAAU,CAAC4O,iBAAiB,EAAC;QAAA,IAAAC,kBAAA;QAAE;QAC/D,MAAMlC,gBAAgB,GAAG,CAACH,gBAAgB,CAAC1J,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAAC4J,MAAM,IAAI;UACvE,IAAI;YACF,OAAOC,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC1J,IAAI,CAAC;UAChC,CAAC,CAAC,OAAO6J,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAAC1J,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACFsJ,gBAAgB,CAAC1J,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAEkK,gBAAgB,CAAClK,UAAU;UACvCY,IAAI,eAAEnB,OAAA,CAACG,iBAAiB;YAACC,WAAW,GAAA0M,kBAAA,GAAEzG,QAAQ,CAAC0G,QAAQ,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBzL,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAAC7B,gBAAgB,EAAIsK,gBAAiB;YAACrK,UAAU,EAAEkK,gBAAgB,CAAClK;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACzK,CAAC,CACF;QACD,OAAO+I,gBAAgB;MACzB,CAAC,MAAO,IAAGA,gBAAgB,CAAClK,UAAU,IAAItC,UAAU,CAAC+O,eAAe,EAAC;QAAA,IAAAC,mBAAA;QAAE;QACrE,IAAIR,gBAAgB,GAAG,CAAChC,gBAAgB,CAAC1J,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAAC4J,MAAM,IAAI;UACrE,IAAI;YAAA,IAAAqC,oBAAA,EAAAC,mBAAA;YACF,IAAIjL,MAAM,GAAI4I,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC1J,IAAI,CAAC;YACrCe,MAAM,CAACE,IAAI,IAAA8K,oBAAA,GAAGtP,cAAc,CAACmI,aAAa,CAAC,CAAC+F,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAI9J,MAAM,CAACxB,KAAK,CAAC,cAAAwM,oBAAA,uBAAvEA,oBAAA,CAAyEjB,SAAS;YAChG/J,MAAM,CAACZ,SAAS,GAAG,CAAC,EAAA6L,mBAAA,GAAA9G,QAAQ,CAAC0G,QAAQ,cAAAI,mBAAA,uBAAjBA,mBAAA,CAAmBf,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE,EAAElD,IAAI,CAAC0D,QAAQ,IAAI1K,MAAM,CAACxB,KAAK,IAAIkM,QAAS,CAAC,GAAG,kBAAkB,GAAG,EAAE;YAC9H,OAAO1K,MAAM;UACf,CAAC,CAAC,OAAO8I,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAAC1J,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACFsJ,gBAAgB,CAAC1J,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAEkK,gBAAgB,CAAClK,UAAU;UACvCY,IAAI,eAAEnB,OAAA,CAACsC,gBAAgB;YAAClC,WAAW,GAAA6M,mBAAA,GAAE5G,QAAQ,CAAC0G,QAAQ,cAAAE,mBAAA,uBAAjBA,mBAAA,CAAmB5L,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAACH,UAAU,EAAEyK,gBAAiB;YAAClM,UAAU,EAAEkK,gBAAgB,CAAClK;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAChK,CAAC,CACF;QACD,OAAO+I,gBAAgB;MACzB,CAAC,MAAO;QACN,OAAOA,gBAAgB;MACzB;IACF,CAAC,CAAC;IACFD,oBAAoB,GAAIA,oBAAoB,CAAC4C,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAACtM,QAAQ,CAAC;IACvF,OAAOyJ,oBAAoB;EAC7B,CAAC;;EAGD;EACA,MAAMrI,OAAO,GAAGA,CAAC;IAAEvB,EAAE;IAAEoC,KAAK;IAAEyC,IAAI;IAAE6H,YAAY;IAAE,GAAGC;EAAK,CAAC,KAAK;IAC9D,IAAIC,GAAG,GAAG5M,EAAE,CAACwL,KAAK,CAAC,GAAG,CAAC,EAAC;IACxB,IAAIqB,YAAY,GAAG;MACjBlN,UAAU,EAAEiN,GAAG,CAAC,CAAC,CAAC;MAClB5D,SAAS,EAAE4D,GAAG,CAAC,CAAC,CAAC;MACjBE,OAAO,EAAEjI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiI,OAAO;MACtB7G,SAAS,EAAEpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,SAAS,CAAC;IAC7B,CAAC;IACDoE,OAAO,CAACK,GAAG,CAACmC,YAAY,CAAC;IACzBE,kBAAkB,CAACF,YAAY,CAAC;EAClC,CAAC;EAED,MAAMG,eAAe,GAAIlG,CAAC,IAAK;IAC7BtC,cAAc,IAAIA,cAAc,CAAC;MAAEyI,QAAQ,EAAEtG,QAAQ,CAACjE,OAAO,CAAC+C,QAAQ;MAAEyH,OAAO,EAAEpG,CAAC,CAACnH,UAAU;MAAEqJ,SAAS,EAAElC,CAAC,CAACkC,SAAS;MAAE,GAAGrE;IAAe,CAAC,CAAC;EAC7I,CAAC;EAED,MAAMwI,iBAAiB,GAAIrG,CAAC,IAAK;IAC/BrC,gBAAgB,IAAIA,gBAAgB,CAAC;MAAEwI,QAAQ,EAAEtG,QAAQ,CAACjE,OAAO,CAAC+C,QAAQ;MAAEhC,QAAQ,EAAEqD,CAAC,CAACkC,SAAS;MAAE,GAAGrE;IAAc,CAAC,CAAC;EACxH,CAAC;EAED,MAAMoI,kBAAkB,GAAGA,CAAC;IAAEpN,UAAU;IAAEqJ,SAAS;IAAE/C;EAAU,CAAC,KAAK;IACnE,IAAItG,UAAU,IAAItC,UAAU,CAACkL,aAAa,EAAE;MAAE;MAC5C;MACA,MAAM/E,IAAI,GAAG4J,wBAAwB,CAAC9H,cAAc,EAAE3F,UAAU,EAAEqJ,SAAS,CAAC;MAC5E,IAAGxF,IAAI,CAAC6J,UAAU,IAAI/P,WAAW,CAACgQ,yBAAyB,EAAE;QAAE;QAC7D,OAAO3P,gBAAgB,CAAC4G,MAAM,EAAES,WAAW,EAAExB,IAAI,CAACjD,IAAI,EAAEiD,IAAI,CAAC6J,UAAU,EAAE7J,IAAI,CAAC+J,YAAY,EAAEtH,SAAS,CAAC;MACxG;MACA,IAAGzC,IAAI,CAAC6J,UAAU,IAAI/P,WAAW,CAACkQ,yBAAyB,EAAE;QAAE;QAC7D,OAAO5P,UAAU,CAAC2G,MAAM,EAAES,WAAW,EAAExB,IAAI,CAACjD,IAAI,EAAEiD,IAAI,CAAC6J,UAAU,EAAE7J,IAAI,CAAC+J,YAAY,EAAEtH,SAAS,EAACD,qBAAqB,CAAC;MACxH;MACA,IAAGxC,IAAI,CAAC6J,UAAU,IAAI/P,WAAW,CAACmQ,oBAAoB,EAAE;QAAE;QACxD,OAAO/P,YAAY,CAAC6G,MAAM,EAAES,WAAW,EAAExB,IAAI,CAACjD,IAAI,EAAEiD,IAAI,CAAC6J,UAAU,EAAE7J,IAAI,CAAC+J,YAAY,EAAEtH,SAAS,CAAC;MACpG;MACAkH,iBAAiB,CAAC;QAAEnE;MAAU,CAAC,CAAC;IAClC,CAAC,MAAM;MACLgE,eAAe,CAAC;QAAErN,UAAU;QAAEqJ;MAAU,CAAC,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,SAASoE,wBAAwBA,CAACM,IAAI,EAAE/N,UAAU,EAAEqJ,SAAS,EAAE;IAC3D,KAAK,IAAI2E,CAAC,IAAID,IAAI,EAAE;MAClB,IAAIA,IAAI,CAACC,CAAC,CAAC,CAAChO,UAAU,IAAIA,UAAU,IAAI+N,IAAI,CAACC,CAAC,CAAC,CAAC3E,SAAS,IAAIA,SAAS,EAAE;QACtE,OAAO0E,IAAI,CAACC,CAAC,CAAC;MAChB;MACA,IAAID,IAAI,CAACC,CAAC,CAAC,CAACxN,QAAQ,EAAE;QACpB,IAAIqD,IAAI,GAAG4J,wBAAwB,CAACM,IAAI,CAACC,CAAC,CAAC,CAACxN,QAAQ,EAAER,UAAU,EAAEqJ,SAAS,CAAC;QAC5E,IAAIxF,IAAI,EAAE;UACR,OAAOA,IAAI;QACb;MACF;IACF;EACJ;;EAEA;EACA;EACA,oBACEpE,OAAA,CAACX,OAAO;IAACuB,EAAE,EAAEA,EAAG;IAAC4N,SAAS,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAQ,CAAE;IAACC,kBAAkB,EAAErJ,wBAAyB;IAAChE,SAAS,EAAC,EAAE;IAAAP,QAAA,GACpHkF,YAAY,iBAAIjG,OAAA,CAAC4O,mBAAmB;MAAArN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEvC,CAACuE,YAAY,IAAIC,cAAc,CAACvF,MAAM,IAAI,CAAC,iBAAIX,OAAA,CAACb,WAAW;MAAC0P,QAAQ;MAAA9N,QAAA,EAAC;IAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,EAE1F,CAACuE,YAAY,IAAIC,cAAc,CAACvF,MAAM,GAAG,CAAC,IAAIuF,cAAc,CAACjF,GAAG,CAAC6N,EAAE,IAAI;MACtE,MAAMC,GAAG,GAAG,GAAGD,EAAE,CAACvO,UAAU,IAAIuO,EAAE,CAAClF,SAAS,EAAE;MAC9C,IAAI,CAAClM,OAAO,CAACoR,EAAE,CAAC/N,QAAQ,CAAC,EAAE;QACzB,oBAAOf,OAAA,CAAC8C,iBAAiB;UAAWJ,KAAK,eACvC1C,OAAA,CAAAE,SAAA;YAAAa,QAAA,gBACEf,OAAA;cAAMsB,SAAS,EAAE,WAAW,GAAGnD,gBAAgB,CAAC2Q,EAAE,CAACvO,UAAU,CAAC,GAAG;YAA8C;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvH1B,OAAA;cAAAe,QAAA,EAAO+N,EAAE,CAAC3N;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,eACtB;UACF;UAAA;UACAqB,KAAK,eAAE/C,OAAA,CAAC3C,aAAa;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,eAGzBf,OAAA;YAAKsB,SAAS,EAAEwN,EAAE,CAACvO,UAAU,IAAItC,UAAU,CAACkL,aAAa,GAAG,0BAA0B,GAAG,EAAG;YAAApI,QAAA,EACzF+N,EAAE,CAACvO,UAAU,IAAItC,UAAU,CAACkL,aAAa;YACxC;YACA,CAAC,MAAM;cACL,MAAM6F,KAAK,GAAG,EAAE;cAChB,IAAIC,iBAAiB,GAAG,EAAE;cAE1BH,EAAE,CAAC/N,QAAQ,CAACkD,OAAO,CAAC,CAACiL,GAAG,EAAEC,KAAK,KAAK;gBAClC,MAAMJ,GAAG,GAAG,GAAGG,GAAG,CAAC3O,UAAU,IAAI2O,GAAG,CAACtF,SAAS,EAAE;;gBAEhD;gBACA,IAAIsF,GAAG,CAAC9N,IAAI,IAAItD,cAAc,CAACsR,MAAM,EAAE;kBACrC;kBACA,IAAIH,iBAAiB,CAACtO,MAAM,GAAG,CAAC,EAAE;oBAChCqO,KAAK,CAACK,IAAI,cACRrP,OAAA;sBAAoCsB,SAAS,EAAC,mBAAmB;sBAAAP,QAAA,EAC9DkO;oBAAiB,GADV,SAASE,KAAK,UAAU;sBAAA5N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE7B,CACP,CAAC;oBACDuN,iBAAiB,GAAG,EAAE;kBACxB;;kBAEA;kBACAD,KAAK,CAACK,IAAI,cACRrP,OAAA;oBAAiBsB,SAAS,EAAC,iBAAiB;oBAAAP,QAAA,eAC1Cf,OAAA;sBAAKsB,SAAS,EAAC,aAAa;sBAAAP,QAAA,EAAEmO,GAAG,CAAC/N;oBAAI;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC,GADrCyN,KAAK;oBAAA5N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP,CAAC;gBACH,CAAC,MAAM;kBACL;kBACAuN,iBAAiB,CAACI,IAAI,cACpBrP,OAAA;oBAAiBsB,SAAS,EAAC,uCAAuC;oBAAAP,QAAA,eAChEf,OAAA,CAACb,WAAW;sBACVyB,EAAE,EAAEmO,GAAI;sBACRtJ,IAAI,EAAEyJ,GAAI;sBACVL,QAAQ,EAAEK,GAAG,CAACL,QAAS;sBACvB1M,OAAO,EAAEA,OAAQ;sBAAApB,QAAA,GAGfmO,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACkL,aAAa,gBAC1CnJ,OAAA,CAACzC,mBAAmB;wBAAC8G,QAAQ,EAAE6K,GAAG,CAACtF,SAAU;wBAACtI,SAAS,EAAC,4CAA4C;wBAACT,KAAK,EAAE;0BAACyO,UAAU,EAAE;wBAAM;sBAAE;wBAAA/N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,GACnIwN,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACsR,sBAAsB,GAClDpR,gBAAgB,CAAC+Q,GAAG,CAACtF,SAAS,CAAC,iBAAI5J,OAAA;wBAAMsB,SAAS,EAAE,WAAW,GAAGnD,gBAAgB,CAAC+Q,GAAG,CAACtF,SAAS,CAAC,GAAG;sBAA8C;wBAAArI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,gBAC3J1B,OAAA;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAEf1B,OAAA;wBAAKa,KAAK,EAAE;0BAAEyG,KAAK,EAAE;wBAAO,CAAE;wBAAAvG,QAAA,EAAEmO,GAAG,CAAC/N;sBAAI;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAG7C1B,OAAA;wBAAMsB,SAAS,EAAI,YAAYlD,iBAAiB,CAAC8Q,GAAG,CAACjB,UAAU,CAAC,CAAC7L,IAAI,eAAgB;wBACpFvB,KAAK,EAAE;0BAAC8B,KAAK,EAAEvE,iBAAiB,CAAC8Q,GAAG,CAACjB,UAAU,CAAC,CAACuB;wBAAS,CAAE;wBAC5D3N,KAAK,EAAEqN,GAAG,CAACjB,UAAU,IAAI/P,WAAW,CAACgQ,yBAAyB,GAC1D,WAAW,GAAGgB,GAAG,CAACjB,UAAU,IAAI/P,WAAW,CAACkQ,yBAAyB,GACpD,SAAS,GAAGc,GAAG,CAACjB,UAAU,IAAI/P,WAAW,CAACmQ,oBAAoB,GAC/C,OAAO,GAAG;sBAAG;wBAAA9M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,eAET1B,OAAA;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC,GA1BNyN,KAAK;oBAAA5N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA2BV,CACP,CAAC;gBACH;cACF,CAAC,CAAC;;cAEF;cACA,IAAIuN,iBAAiB,CAACtO,MAAM,GAAG,CAAC,EAAE;gBAChCqO,KAAK,CAACK,IAAI,cACRrP,OAAA;kBAA8BsB,SAAS,EAAC,mBAAmB;kBAAAP,QAAA,EACxDkO;gBAAiB,GADX,oBAAoB;kBAAA1N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAExB,CACP,CAAC;cACH;cAEA,OAAOsN,KAAK;YACd,CAAC,EAAE,CAAC;YAEJ;YACAF,EAAE,CAAC/N,QAAQ,CAACE,GAAG,CAAC,CAACiO,GAAG,EAAEC,KAAK,KAAK;cAC9B,MAAMJ,GAAG,GAAG,GAAGG,GAAG,CAAC3O,UAAU,IAAI2O,GAAG,CAACtF,SAAS,EAAE;cAChD,oBACE5J,OAAA;gBAAiBsB,SAAS,EAAC,EAAE;gBAAAP,QAAA,eAC3Bf,OAAA,CAACb,WAAW;kBACVyB,EAAE,EAAEmO,GAAI;kBACRtJ,IAAI,EAAEyJ,GAAI;kBACVL,QAAQ,EAAEK,GAAG,CAACL,QAAS;kBACvB1M,OAAO,EACL+M,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACyM,qBAAqB,IAClDwE,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACkN,eAAe,IAC5C+D,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACsO,eAAe,IAC5C2C,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACuN,gBAAgB,IAC7C0D,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAAC4O,iBAAiB,IAC9CqC,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAAC+O,eAAe,GACxC,MAAM,CAAC,CAAC,GAAG7K,OAChB;kBACDtB,KAAK,EAAEqO,GAAG,CAAC9N,IAAI,IAAItD,cAAc,CAACsR,MAAM,GAAG;oBAAEK,WAAW,EAAE;kBAAO,CAAC,GAAG,CAAC,CAAE;kBACxEnO,SAAS,EAAI4N,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACyM,qBAAqB,IAC7DwE,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACkN,eAAe,IAC5C+D,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACsO,eAAe,IAC5C2C,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACuN,gBAAgB,IAC7C0D,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAAC4O,iBAAiB,IAC9CqC,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAAC+O,eAAe,GACxC,wBAAwB,GAAG,EAAI;kBAAAjM,QAAA,GAGnCmO,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACkL,aAAa,gBAC1CnJ,OAAA,CAACzC,mBAAmB;oBAAC8G,QAAQ,EAAE6K,GAAG,CAACtF,SAAU;oBAACtI,SAAS,EAAC,4CAA4C;oBAACT,KAAK,EAAE;sBAACyO,UAAU,EAAE;oBAAM;kBAAE;oBAAA/N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,GACnIwN,GAAG,CAAC3O,UAAU,IAAItC,UAAU,CAACsR,sBAAsB,GAClDpR,gBAAgB,CAAC+Q,GAAG,CAACtF,SAAS,CAAC,iBAAI5J,OAAA;oBAAMsB,SAAS,EAAE,WAAW,GAAGnD,gBAAgB,CAAC+Q,GAAG,CAACtF,SAAS,CAAC,GAAG;kBAA8C;oBAAArI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,gBAC3J1B,OAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAEf1B,OAAA;oBAAKa,KAAK,EAAE;sBAAEyG,KAAK,EAAE;oBAAO,CAAE;oBAAAvG,QAAA,EAAEmO,GAAG,CAAC/N;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG7C1B,OAAA;oBAAMsB,SAAS,EAAI,YAAYlD,iBAAiB,CAAC8Q,GAAG,CAACjB,UAAU,CAAC,CAAC7L,IAAI,eAAgB;oBACpFvB,KAAK,EAAE;sBAAC8B,KAAK,EAAEvE,iBAAiB,CAAC8Q,GAAG,CAACjB,UAAU,CAAC,CAACuB;oBAAS,CAAE;oBAC5D3N,KAAK,EAAEqN,GAAG,CAACjB,UAAU,IAAI/P,WAAW,CAACgQ,yBAAyB,GAC1D,WAAW,GAAGgB,GAAG,CAACjB,UAAU,IAAI/P,WAAW,CAACkQ,yBAAyB,GACpD,SAAS,GAAGc,GAAG,CAACjB,UAAU,IAAI/P,WAAW,CAACmQ,oBAAoB,GAC/C,OAAO,GAAG;kBAAG;oBAAA9M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eAET1B,OAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GA1CNyN,KAAK;gBAAA5N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2CV,CAAC;YAEV,CAAC;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAvIuBqN,GAAG;UAAAxN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwIf,CAAC;MACtB,CAAC,MAAM;QACL,oBAAO1B,OAAA,CAACb,WAAW;UAAWyB,EAAE,EAAEmO,GAAI;UAAC5M,OAAO,EAAEA,OAAQ;UAAApB,QAAA,GACrD+N,EAAE,CAACvO,UAAU,IAAI,EAAE,gBACpBP,OAAA;YAAMsB,SAAS,EAAE,WAAW,GAAGnD,gBAAgB,CAAC2Q,EAAE,CAACvO,UAAU,CAAC,GAAG,+BAAgC;YAACM,KAAK,EAAE;cAAC6O,UAAU,EAAC,CAAC,CAAC;cAACC,WAAW,EAAC;YAAC;UAAE;YAAApO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE/I1B,OAAA;YAAMsB,SAAS,EAAE,WAAW,GAAGnD,gBAAgB,CAAC2Q,EAAE,CAACvO,UAAU,CAAC,GAAG;UAA8C;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAEvH1B,OAAA;YAAKa,KAAK,EAAE;cAAEyG,KAAK,EAAE;YAAO,CAAE;YAAAvG,QAAA,EAAE+N,EAAE,CAAC3N;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GANvBqN,GAAG;UAAAxN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOf,CAAC;MAChB;IACF,CAAC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEK,CAAC;AAEd;AAAC8D,GAAA,CAtfQN,YAAY;EAAA,QAIf1H,qCAAqC,EACTC,mCAAmC,EAEGkC,WAAW,EAItDA,WAAW,EA0BrBH,cAAc;AAAA;AAAAoQ,GAAA,GArCxB1K,YAAY;AAyfrB,SAAS0J,mBAAmBA,CAAA,EAAG;EAC7B,oBAAO5O,OAAA,CAACnB,KAAK,CAACoB,QAAQ;IAAAc,QAAA,gBACpBf,OAAA,CAACb,WAAW;MAAC0P,QAAQ;MAAA9N,QAAA,eACnBf,OAAA,CAACrB,QAAQ,CAACkR,KAAK;QAACC,MAAM,EAAE,IAAK;QAACjP,KAAK,EAAE;UAACkP,MAAM,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAQ;MAAE;QAAAzO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACd1B,OAAA,CAACb,WAAW;MAAC0P,QAAQ;MAAA9N,QAAA,eACnBf,OAAA,CAACrB,QAAQ,CAACkR,KAAK;QAACC,MAAM,EAAE,IAAK;QAACjP,KAAK,EAAE;UAACkP,MAAM,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAQ;MAAE;QAAAzO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACd1B,OAAA,CAACb,WAAW;MAAC0P,QAAQ;MAAA9N,QAAA,eACnBf,OAAA,CAACrB,QAAQ,CAACkR,KAAK;QAACC,MAAM,EAAE,IAAK;QAACjP,KAAK,EAAE;UAACkP,MAAM,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAQ;MAAE;QAAAzO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACd1B,OAAA,CAACb,WAAW;MAAC0P,QAAQ;MAAA9N,QAAA,eACnBf,OAAA,CAACrB,QAAQ,CAACkR,KAAK;QAACC,MAAM,EAAE,IAAK;QAACjP,KAAK,EAAE;UAACkP,MAAM,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAQ;MAAE;QAAAzO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACd1B,OAAA,CAACb,WAAW;MAAC0P,QAAQ;MAAA9N,QAAA,eACnBf,OAAA,CAACrB,QAAQ,CAACkR,KAAK;QAACC,MAAM,EAAE,IAAK;QAACjP,KAAK,EAAE;UAACkP,MAAM,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAQ;MAAE;QAAAzO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AACnB;AAACuO,GAAA,GAlBQrB,mBAAmB;AAqB5B,eAAAsB,GAAA,gBAAepR,UAAU,CAACoG,YAAY,CAAC;AAAA,IAAAvD,EAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAA0C,GAAA,EAAA2K,GAAA,EAAAK,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAxO,EAAA;AAAAwO,YAAA,CAAArO,GAAA;AAAAqO,YAAA,CAAA9N,GAAA;AAAA8N,YAAA,CAAA5N,GAAA;AAAA4N,YAAA,CAAAlL,GAAA;AAAAkL,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}