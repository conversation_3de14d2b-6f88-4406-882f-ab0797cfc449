{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CharacterClassifier } from './characterClassifier.js';\nexport class WordCharacterClassifier extends CharacterClassifier {\n  constructor(wordSeparators) {\n    super(0 /* WordCharacterClass.Regular */);\n    for (let i = 0, len = wordSeparators.length; i < len; i++) {\n      this.set(wordSeparators.charCodeAt(i), 2 /* WordCharacterClass.WordSeparator */);\n    }\n    this.set(32 /* CharCode.Space */, 1 /* WordCharacterClass.Whitespace */);\n    this.set(9 /* CharCode.Tab */, 1 /* WordCharacterClass.Whitespace */);\n  }\n}\nfunction once(computeFn) {\n  const cache = {}; // TODO@Alex unbounded cache\n  return input => {\n    if (!cache.hasOwnProperty(input)) {\n      cache[input] = computeFn(input);\n    }\n    return cache[input];\n  };\n}\nexport const getMapForWordSeparators = once(input => new WordCharacterClassifier(input));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}