{"ast": null, "code": "import React,{useState,useEffect,useMemo}from\"react\";import{Bad<PERSON>,Button,Checkbox,Drawer,Dropdown,List,Menu,Popover,Space,Modal,Avatar,Popconfirm}from\"antd\";import{useNavigate,useParams,Link}from\"react-router-dom\";import{useQuery,useQueryClient}from\"@tanstack/react-query\";import{getOptionByType}from\"@common/utils/TsbConfig\";import*as httpCommon from\"@common/api/http\";import\"./AppNoticeIcon.scss\";import AppNoticeIconAllModal from\"./AppNoticeIconAllModal\";import{globalUtil}from\"@common/utils/globalUtil\";import{useQuerySetting407_getCodeValueList}from\"@common/service/commonHooks\";import CreateTeamModal,{CREATETYPE_UPGRADE}from\"@components/CreateTeam\";import moment from\"moment\";import{getUrlByModule}from'@common/utils/logicUtils';import{useDispatch}from\"react-redux\";import{getTeamList}from\"src/team/store/actionCreators\";import{CheckCircleOutlined}from'@ant-design/icons';import{team_578_get_notify_query,/* setting_125_refresh_team_query */useQuery_setting_126_get_setting_teams}from\"@common/api/query/query\";import{useThrottle}from\"@common/hook/useThrottle\";import{eProductGroupId,eProductStatus,eSysNotifierOpType}from\"@common/utils/enum\";import DraggableDrawer from\"@components/DraggableDrawer\";import SettingsDrawer from\"@/settings/views/SettingsDrawer\";//系统通知 \nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function AppNoticeIcon(props){const{noticeVisible,setNoticeVisible}=props;//const queryClient = useQueryClient();\nconst navigate=useNavigate();const dispatch=useDispatch();const{teamId}=useParams();const[showUnReadView,setShowUnReadView]=useState(true);// const {data: selectionList, isLoading: isLoadingCodeValueList} = useQuerySetting407_getCodeValueList(teamId); // 字典数据\n//const { data, isLoading } = useQueryTeam557GetSysNotification()\nconst{data:teamData}=useQuery({...useQuery_setting_126_get_setting_teams()});const teamList=(teamData===null||teamData===void 0?void 0:teamData.teams)||[];const{data:notifyData,refetch:refetchNotify,dataUpdatedAt}=useQuery({//isLoading,\n...team_578_get_notify_query(teamId)});// const { data: setting125Data } = useQuery({\n//   ...setting_125_refresh_team_query(teamId),\n//   enabled: false\n// })\nconst[appNoticeIconVisible,setAppNoticeIconVisible]=useState(false);const[showMessage,_setShowMessage]=useState(false);const[dropDownOpenId,setDropDownOpenId]=useState(null);const[createTeamModalVisible,setCreateTeamModalVisible]=useState(false);// const [systemUpdateTips,setSystemUpdateTips] = useState(true);\nconst[productListFinal,setProductListFinal]=useState([]);//const autoClosePopRef = useRef(false);\nconst[settingsDrawerVisible,setSettingsDrawerVisible]=useState(false);//打开团队设置->应用管理\nconst[settingsDrawerTab,setSettingsDrawerTab]=useState('product');//默认打开 应用管理\nconst[productId,setProductId]=useState();//打开应用授权，具体的某一个应用\nconst setShowMessage=useThrottle(value=>{_setShowMessage(value);},200);useEffect(()=>{getProductsList();},[]);useEffect(()=>{if(noticeVisible){setShowMessage(true);}},[noticeVisible]);useEffect(()=>{// 通知自动弹出的条件\n// 1、系统身份信息已填写\n// 2、团队个人信息已填写\n// 3、第一次的新手向导已做\nif((notifyData===null||notifyData===void 0?void 0:notifyData.needPopupFlg)==1&&(notifyData===null||notifyData===void 0?void 0:notifyData.autoPopupFlg)==1/* && !setting125Data?.firstEntryFlg && setting125Data?.bindMobileFlg && setting125Data?.trialReminderFlg */){if(!showMessage){setShowMessage(true);}}},[dataUpdatedAt]);function getProductsList(){httpCommon.team_711_get_team_product_list({teamId:teamId}).then(res=>{if(res.resultCode==200){let allProductsList=(res.productList||[]).filter(product=>product.freeFlg==0&&product.groupId!=eProductGroupId.Pgid_1_OS&&product.statusType!=eProductStatus.Status_3_Unreleased).map(product=>{product.key=product.productId;return product;});productListFormat(allProductsList);}});}function productListFormat(){let productList=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];let packageList=[];productList.forEach(product=>{let item=packageList.find(_product=>product.groupId==_product.groupId);if(!item){packageList.push({groupName:product.groupName,groupId:product.groupId,groupList:[product]});}else{item.groupList.push(product);}});let allProductsList=[];packageList.forEach(group=>{let groupList=group.groupList.map((config,index)=>{var _productListFinal$fin,_productListFinal$fin2;return{...config,key:config.productId,isRowSpan:index==0?true:false,groupListLength:index==0?group.groupList.length:0,authCntDesc:!!config.authCntDesc?config.authCntDesc:((_productListFinal$fin=productListFinal.find(product=>product.productId==config.productId))===null||_productListFinal$fin===void 0?void 0:_productListFinal$fin.authCntDesc)||'',objCntDesc:!!config.objCntDesc?config.objCntDesc:((_productListFinal$fin2=productListFinal.find(product=>product.productId==config.productId))===null||_productListFinal$fin2===void 0?void 0:_productListFinal$fin2.objCntDesc)||''};});allProductsList=allProductsList.concat(groupList);});setProductListFinal([...allProductsList]);}// 删除单条通知\nfunction deleteNotic(item,refetch557){let params={teamId:teamId,opType:\"phy_delete\",notifyIds:[item.id]};httpCommon.team_558_update_notification_status(params).then(res=>{if(res.resultCode===200){globalUtil.success('删除成功');refetch557&&refetch557();}});}//通知设置为已读\nfunction readNotics(id,opType,refetch557){let params={teamId:teamId,opType:\"update\",notifyIds:[id]};if(!!opType){params.notifyType=opType;}httpCommon.team_558_update_notification_status(params).then(res=>{if(res.resultCode===200){globalUtil.success('本消息已读');refetch557&&refetch557();refetchNotify();}});}function isLink(item){var _item$supplierMap,_item$supplierMap2,_item$supplierMap3;if(item.opType==eSysNotifierOpType.op_1_comment||item.opType==eSysNotifierOpType.op_2_at_me||item.opType==eSysNotifierOpType.op_6_like){return true;}else if(item.opType==eSysNotifierOpType.op_11_approve_mbr_join&&item!==null&&item!==void 0&&(_item$supplierMap=item.supplierMap)!==null&&_item$supplierMap!==void 0&&_item$supplierMap.groupId){//申请加入\nreturn true;}else if(item.opType==eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited&&item!==null&&item!==void 0&&(_item$supplierMap2=item.supplierMap)!==null&&_item$supplierMap2!==void 0&&_item$supplierMap2.teamId){//手机/邮箱邀请\nreturn true;}else if(item.opType==eSysNotifierOpType.op_25_product_auth){//应用授权\nif((item===null||item===void 0?void 0:item.teamId)==teamId)return false;else return true;}else if(item.opType==eSysNotifierOpType.op_27_set_kpi_stop_bpmn&&item!==null&&item!==void 0&&(_item$supplierMap3=item.supplierMap)!==null&&_item$supplierMap3!==void 0&&_item$supplierMap3.teamId){// 任务停止流程尚未配置完全\nreturn true;}else{return false;}}function goToNewPage(item,refetch557){var _item$supplierMap4,_item$supplierMap5,_item$supplierMap6;setDropDownOpenId(null);switch(item.opType){case eSysNotifierOpType.op_1_comment://评论\n// let url = getLinkUrl(item);\n// navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\nbtnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_2_at_me://去到被@的节点\n// let url = getLinkUrl(item);\n// navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\nbtnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_6_like://点赞\n// let url = getLinkUrl(item);\n// navigate(url,{state:{refresh: [\"issues\", \"questions\"]}});  /* tmsbug-4739 点击@我的通知，没反应，不能跳转到对应对象 */\nbtnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_11_approve_mbr_join://去审批\n// item?.supplierMap?.spaceId && item?.supplierMap?.groupId && navigate(`/${teamId}/settings/space/${item.supplierMap.spaceId}/member/approval/${item.supplierMap.groupId}/2`);\n!!(item!==null&&item!==void 0&&(_item$supplierMap4=item.supplierMap)!==null&&_item$supplierMap4!==void 0&&_item$supplierMap4.groupId)&&btnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_13_add_mbr_user_quota://团队成员扩容\nsetCreateTeamModalVisible(true);btnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_14_buy_vip_expiration_dt://续费\nsetCreateTeamModalVisible(true);btnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_15_buy_cdisk_traffic://云盘扩容\nglobalUtil.error('待开放');break;case eSysNotifierOpType.op_17_accept_invite://接受邀请\n(item===null||item===void 0?void 0:item.objId)&&(item===null||item===void 0?void 0:(_item$supplierMap5=item.supplierMap)===null||_item$supplierMap5===void 0?void 0:_item$supplierMap5.inviteTeamId)&&acceptInvitation(item,refetch557);break;case eSysNotifierOpType.op_18_buy://购买\nsetCreateTeamModalVisible(true);btnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_22_upgrade_to_vip://升级企业版\nsetCreateTeamModalVisible(true);btnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited://进入团队\n(item===null||item===void 0?void 0:(_item$supplierMap6=item.supplierMap)===null||_item$supplierMap6===void 0?void 0:_item$supplierMap6.teamId)&&btnClick(item,item.opType,refetch557);break;case eSysNotifierOpType.op_25_product_auth://产品授权\nif(teamId==item.teamId){//授权的请求，正好是本团队的请求，直接打开 SettingsDrawer的应用管理\nif(appNoticeIconVisible){setAppNoticeIconVisible(false);}if(noticeVisible){setNoticeVisible(false);//关闭本身(系统通知)Drawer\n}// setSettingsDrawerTab('product')\nsetProductId(item.objId);//objId存储的就是productId,如21为文档/文档库\nsetSettingsDrawerVisible(true);}else{btnClick(item,0,refetch557);//继续走default路径\n}break;default:btnClick(item,0,refetch557);break;}}function btnClick(item,opType,refetch557){// let closeList = [2,11,13,14,15,17,18,22,23]\n// if(closeList.find(objType => objType == item.opType)){\n//   setAppNoticeIconVisible(false)\n// }\nif(notifyData.notificationReminders.find(remind=>remind.id==item.id&&remind.opStatus==1)){readNotics(item.id,opType,refetch557);}}function acceptInvitation(item,refetch557){httpCommon.setting_217_add_user_by_invite_url({teamId:item.supplierMap.inviteTeamId,inviteId:item.objId}).then(res=>{if(res.resultCode==200){//刷新团队列表\nbtnClick(item,eSysNotifierOpType.op_17_accept_invite,refetch557);dispatch(getTeamList());joinTeamSuccess(item.supplierMap.inviteTeamId,item.objTitle);}});}function joinTeamSuccess(_teamId,newTeamName){Modal.confirm({title:'提示',icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'#52c41a'}}),content:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u56E2\\u961F\\u52A0\\u5165\\u6210\\u529F\\uFF0C\\u662F\\u5426\\u5207\\u6362\\u81F3 \",newTeamName.substring(0,newTeamName.indexOf('团队的')),\" \\uFF1F\"]}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u70B9\\u51FB\\\"\\u5426\\\"\\uFF0C\\u505C\\u7559\\u5728\\u5F53\\u524D\\u9875\\u9762\\uFF1B\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u70B9\\u51FB\\\"\\u662F\\\"\\uFF0C\\u5207\\u6362\\u81F3\\u65B0\\u7684\\u56E2\\u961F\\u3002\"})]}),okText:'是，切换团队',cancelText:'否',onOk:()=>{navigate(`/team/${_teamId}`);},onCancel:()=>{}});}const getLinkUrl=item=>{var _item$supplierMap9,_item$supplierMap10,_item$supplierMap11,_item$supplierMap12;if(item.opType==eSysNotifierOpType.op_11_approve_mbr_join){var _item$supplierMap7;// 申请加入 去审批\nif(!!(item!==null&&item!==void 0&&(_item$supplierMap7=item.supplierMap)!==null&&_item$supplierMap7!==void 0&&_item$supplierMap7.spaceId)){return`/${item.teamId}/settings/space/${item.supplierMap.spaceId}/member/approval/${item.supplierMap.groupId}/2`;}return`/${item.teamId}/settings/user/member/approval/${item.supplierMap.groupId}/2`;}if(item.opType==eSysNotifierOpType.op_23_mobielOrEmail_mbr_invited){var _item$supplierMap8;//手机/邮箱邀请 进入团队\nif(!!((_item$supplierMap8=item.supplierMap)!==null&&_item$supplierMap8!==void 0&&_item$supplierMap8.teamId)){return`/team/${item.supplierMap.teamId}`;}return``;}if(item.opType==eSysNotifierOpType.op_25_product_auth){//去授权\n//item.objId 存放 productId\nreturn`/${item.teamId}/settings/product/${item.objId}`;}if(item.opType==eSysNotifierOpType.op_27_set_kpi_stop_bpmn){// 任务停止流程尚未配置完全\nif(!!item.teamId){return`/team/${item.supplierMap.teamId}/stopTasks/${item.supplierMap.nodeId}`;}return``;}let obj={nodeType:(_item$supplierMap9=item.supplierMap)===null||_item$supplierMap9===void 0?void 0:_item$supplierMap9.objType,anchorNodeId:(_item$supplierMap10=item.supplierMap)===null||_item$supplierMap10===void 0?void 0:_item$supplierMap10.anchorNodeId,nodeId:(_item$supplierMap11=item.supplierMap)===null||_item$supplierMap11===void 0?void 0:_item$supplierMap11.nodeId};let no_hash_url=getUrlByModule(item.teamId,{...obj});let hash_url=item.objType==1000&&(_item$supplierMap12=item.supplierMap)!==null&&_item$supplierMap12!==void 0&&_item$supplierMap12.commentId?`#comment-${item.supplierMap.commentId}`:'';return no_hash_url+hash_url;};const getFirstName=teamName=>{try{return teamName.substring(0,1);}catch(e){return\"\";}};const onOpenChange=open=>{if(!open){setDropDownOpenId(null);if(open!=showMessage){setTimeout(()=>{setShowMessage(false);setNoticeVisible(false);},300);//20250801 2000 -> 300, 2秒太久\n}}};return/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsx(Popover,{open:showMessage,placement:\"bottom\",overlayClassName:\"noticPopover \"+(appNoticeIconVisible?\"noticPopover-999\":\"\"),trigger:'click',onOpenChange:onOpenChange,title:/*#__PURE__*/_jsx(NoticeTitle,{teamId:teamId,setAppNoticeIconVisible:setAppNoticeIconVisible,setNoticeVisible:setNoticeVisible,closePopover:()=>{setShowMessage(false);setNoticeVisible(false);setDropDownOpenId(null);},showUnReadView:showUnReadView,setShowUnReadView:setShowUnReadView}),content:showMessage?/*#__PURE__*/_jsx(NoticeContent,{teamId:teamId,goToNewPage:goToNewPage,isLink:isLink,dropDownOpenId:dropDownOpenId,setDropDownOpenId:setDropDownOpenId,getLinkUrl:getLinkUrl,getFirstName:getFirstName,teamList:teamList,showUnReadView:showUnReadView}):/*#__PURE__*/_jsx(_Fragment,{}),children:/*#__PURE__*/_jsx(Badge,{offset:[-5,5],size:\"small\",count:((notifyData===null||notifyData===void 0?void 0:notifyData.notificationReminders)||[]).filter(reminder=>reminder.opStatus==1).length,showZero:false,...props,children:/*#__PURE__*/_jsx(Button,{type:\"link\",shape:\"circle\",icon:/*#__PURE__*/_jsx(\"span\",{className:`iconfont ${(notifyData===null||notifyData===void 0?void 0:notifyData.autoPopupFlg)==1?\"tongzhi1\":\"tongzhibutixin\"} `}),onClick:()=>{setShowMessage(!showMessage);setNoticeVisible(false);},className:\"header-icon\"})})}),/*#__PURE__*/_jsx(DraggableDrawer,{title:/*#__PURE__*/_jsx(\"span\",{style:{color:\"orange\"},className:\"iconfont tongzhi1\",children:/*#__PURE__*/_jsx(\"span\",{style:{color:\"#666\",marginLeft:10},children:\"\\u7CFB\\u7EDF\\u901A\\u77E5 - \\u5168\\u90E8\"})}),className:\"AppNoticeIconAllModl-drawer\",centered:true,open:appNoticeIconVisible,destroyOnClose:true,onClose:()=>{setShowMessage(false);setNoticeVisible(false);setDropDownOpenId(null);setTimeout(()=>{setAppNoticeIconVisible(false);},300);},width:800,footer:false,children:/*#__PURE__*/_jsx(AppNoticeIconAllModal,{teamId:teamId,isLink:isLink,getLinkUrl:getLinkUrl,deleteNotic:deleteNotic,goToNewPage:goToNewPage,getFirstName:getFirstName,refetchNotify:refetchNotify,teamList:teamList})}),/*#__PURE__*/_jsx(CreateTeamModal,{teamId:teamId,type:CREATETYPE_UPGRADE,visible:createTeamModalVisible,onCancel:()=>setCreateTeamModalVisible(false),onOk:()=>setCreateTeamModalVisible(false),productList:productListFinal}),/*#__PURE__*/_jsx(SettingsDrawer,{visible:settingsDrawerVisible,onClose:()=>setSettingsDrawerVisible(false),teamId:teamId,defaultTab:settingsDrawerTab,productId:productId})]});}// the content of notice\nfunction NoticeTitle(_ref){let{teamId,setAppNoticeIconVisible,setNoticeVisible,closePopover,showUnReadView,setShowUnReadView}=_ref;const queryClient=useQueryClient();const{data,refetch:refetchNotify,dataUpdatedAt:team578DataUpdatedAt}=useQuery({...team_578_get_notify_query(teamId),enabled:false,refetchInterval:false});const[ignorePopoverVisible,setIgnorePopoverVisible]=useState(false);const onAutoEjectChange=e=>{let autoPopupFlg=e.target.checked?1:0;httpCommon.team_563_set_notify_auto_popup({teamId,autoPopupFlg}).then(result=>{if(result.resultCode==200){queryClient.setQueryData(team_578_get_notify_query(teamId).queryKey,oldData=>{return{...oldData,autoPopupFlg};});}});};const onShowUnReadChange=e=>{setShowUnReadView(e.target.checked);};// 忽略全部通知\nconst ignoreNotic=()=>{let params={teamId:teamId,opType:\"delete\",notifyIds:[]};httpCommon.team_558_update_notification_status(params).then(result=>{if(result.resultCode==200){refetchNotify();setIgnorePopoverVisible(false);}});};const notifyCount=useMemo(()=>{var _data$notificationRem;return((_data$notificationRem=data===null||data===void 0?void 0:data.notificationReminders)!==null&&_data$notificationRem!==void 0?_data$notificationRem:[]).filter(item=>{if(showUnReadView){return item.opStatus==1;}else{return true;}}).length;},[showUnReadView,team578DataUpdatedAt]);return/*#__PURE__*/_jsxs(\"div\",{className:\"noticPopover-title\",children:[/*#__PURE__*/_jsxs(Space,{size:10,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"noticPopover-title-left\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"iconfont tongzhi1\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"noticPopover-title-text\",children:[\"\\u7CFB\\u7EDF\\u901A\\u77E5\",notifyCount?`(${notifyCount})`:'']})]}),/*#__PURE__*/_jsx(Checkbox,{className:\"noticPopover-title-check\",checked:data===null||data===void 0?void 0:data.autoPopupFlg,onChange:onAutoEjectChange,children:\"\\u81EA\\u52A8\\u5F39\\u51FA\"}),/*#__PURE__*/_jsx(Checkbox,{className:\"noticPopover-title-check\",checked:showUnReadView,onChange:onShowUnReadChange,children:\"\\u53EA\\u770B\\u672A\\u8BFB\"}),/*#__PURE__*/_jsx(Popconfirm,{overlayClassName:\"ignorePopover\",title:`忽略全部(${notifyCount}条)通知?`,placement:'right',trigger:'click',open:ignorePopoverVisible,onConfirm:ignoreNotic,onCancel:()=>{setIgnorePopoverVisible(false);},children:/*#__PURE__*/_jsx(\"a\",{className:\"noticPopover-title-option\",style:!notifyCount?{color:'#999'}:{},onClick:()=>{notifyCount&&setIgnorePopoverVisible(!ignorePopoverVisible);},children:/*#__PURE__*/_jsx(\"span\",{title:\"\\u5FFD\\u7565\\u5168\\u90E8\\u901A\\u77E5\",className:\"iconfont butixing fontsize-14\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"a\",{className:\"fontsize-12\",onClick:()=>{setAppNoticeIconVisible(true);setNoticeVisible(false);//20250801 Jim Song, 系统通知，点击全部后，将自身小弹窗关闭\n},children:[\"\\u5168\\u90E8\",data!==null&&data!==void 0&&data.allCount?'('+(data.allCount>99?'99+':data.allCount)+')':'']}),/*#__PURE__*/_jsx(\"a\",{style:{marginLeft:20,color:'#666'},onClick:()=>closePopover(),children:/*#__PURE__*/_jsx(\"span\",{className:\"iconfont guanbi\",style:{fontSize:14}})})]})]});}function NoticeContent(_ref2){let{teamId,goToNewPage,isLink,dropDownOpenId,setDropDownOpenId,getLinkUrl,getFirstName,teamList,showUnReadView}=_ref2;const{data,refetch:refetchNotify,dataUpdatedAt:team578DataUpdatedAt}=useQuery({...team_578_get_notify_query(teamId),enabled:false,refetchInterval:false});const{data:selectionList}=useQuerySetting407_getCodeValueList(teamId);// 字典数据\nfunction ignoreNotic(item){let params={teamId:teamId,opType:\"delete\",notifyIds:[item.id]};httpCommon.team_558_update_notification_status(params).then(result=>{if(result.resultCode==200){refetchNotify();}});}function remindLater(key,item){let params={teamId:teamId,notifyIds:[item.id],remindType:key};httpCommon.team_559_set_notification_remind_later(params).then(res=>{if(res.resultCode==200){ignoreNotic(item);}});}function menuList(){let list=[{key:'ignore',label:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',fontSize:12,justifyContent:'center'},children:[/*#__PURE__*/_jsx(\"span\",{className:\"iconfont butixing\",style:{fontSize:12,marginRight:5}}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u5FFD\\u7565\"})]})}];let dataList=(selectionList||[]).filter(selection=>selection.selectionId==1947).map(selection=>({key:selection.propType,label:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:12,display:'flex',alignItems:'center',justifyContent:'space-between',width:60},children:[selection.propValue,/*#__PURE__*/_jsx(\"div\",{children:\"\\u540E\"})]})}));return list.concat(dataList);}function menuClick(e,item){setDropDownOpenId(null);if(e.key=='ignore'){ignoreNotic(item);}else{remindLater(e.key,item);}}const memberMenu=item=>/*#__PURE__*/_jsx(\"div\",{className:\"flex-column-parent\",children:/*#__PURE__*/_jsx(Menu,{items:menuList(),className:\"flex-column-child section\",style:{height:150},onClick:e=>menuClick(e,item)})});function getWidth(item,getOptionByType){let width=400;if(!getOptionByType(item.opType)&&!!item.opInfo){width=400;}else{width=350;}if(item.opTypeName.length>2){width=width-20;}return width;}function buttonUi(item,goToNewPage,getOptionByType){if(!getOptionByType(item.opType)){return null;}return/*#__PURE__*/_jsx(Button,{disabled:!!(teamList||[]).find(team=>{var _item$supplierMap13;return team.teamId==(item===null||item===void 0?void 0:(_item$supplierMap13=item.supplierMap)===null||_item$supplierMap13===void 0?void 0:_item$supplierMap13.inviteTeamId);}),className:\"noticPopover-list-btn\",onClick:()=>goToNewPage(item),children:getOptionByType(item.opType)});}function dropDownUi(item,memberMenu){return/*#__PURE__*/_jsx(Dropdown,{trigger:['click'],overlay:memberMenu(item),open:dropDownOpenId==item.id,children:/*#__PURE__*/_jsxs(\"a\",{style:{color:'#999',fontSize:12,display:'flex',alignItems:'center'},onClick:e=>{e.preventDefault();e.stopPropagation();setDropDownOpenId(item.id==dropDownOpenId?null:item.id);},children:[\"\\u7A0D\\u540E\\u63D0\\u9192\",/*#__PURE__*/_jsx(\"span\",{className:\"iconfont xiala1\",style:{fontSize:14,color:'#999'}})]})});}function contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName){return/*#__PURE__*/_jsxs(\"div\",{style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'3px 5px 0px 20px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[!item.logoUrl?/*#__PURE__*/_jsx(\"div\",{className:\"notic-team-logo\",children:getFirstName(item.teamName)}):/*#__PURE__*/_jsx(Avatar,{style:{marginRight:5,width:28,minWidth:28,height:28},src:item.logoUrl}),/*#__PURE__*/_jsx(\"div\",{className:\"fontsize-12 text-overflow\",style:{color:'#333',flex:\"auto\"},children:item.teamName}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:12,color:'#333',marginLeft:10},children:item.opTypeName})]}),/*#__PURE__*/_jsxs(Space,{size:5,children:[!item.opInfo&&buttonUi(item,goToNewPage,getOptionByType),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:12,color:'#999'},children:item.notifyDt?moment(item.notifyDt).format('YY-MM-DD HH:mm'):''})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'0px 5px 0px 20px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center'},children:/*#__PURE__*/_jsxs(\"a\",{onClick:()=>getOptionByType(item.opType)?null:goToNewPage(item),className:\"tms-link-nocolor\",style:{display:'flex',alignItems:'center',marginLeft:!!item.opTypeName?5:0,width:getWidth(item,getOptionByType)},children:[/*#__PURE__*/_jsx(\"div\",{title:item.objTitle,className:\"fontsize-12 text-overflow\",children:item.objTitle}),/*#__PURE__*/_jsx(Badge,{dot:item.opStatus==1,size:\"small\",offset:[-4,-4]})]})}),!!item.opInfo?buttonUi(item,goToNewPage,getOptionByType):dropDownUi(item,memberMenu)]}),!!item.opInfo&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',margin:'0px 5px 0px 20px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:12,color:'#999',width:380,whiteSpace:'nowrap',overflow:'hidden',textOverflow:'ellipsis'},children:item.opInfo}),dropDownUi(item,memberMenu)]})]});}const ListItem=_ref3=>{let{key,item,goToNewPage,goToNewPage1,getOptionByType,memberMenu,getFirstName}=_ref3;return/*#__PURE__*/_jsx(List.Item,{children:isLink(item)?/*#__PURE__*/_jsx(Link,{style:{width:'100%'},to:getLinkUrl(item),target:\"_blank\",onClick:()=>goToNewPage1(item),children:contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName)},key):contentUi(item,goToNewPage,getOptionByType,memberMenu,getFirstName)});};const notificationReminders=useMemo(()=>{var _data$notificationRem2;return((_data$notificationRem2=data===null||data===void 0?void 0:data.notificationReminders)!==null&&_data$notificationRem2!==void 0?_data$notificationRem2:[]).filter(item=>{if(showUnReadView){return item.opStatus==1;}else{return true;}});},[team578DataUpdatedAt,showUnReadView]);return/*#__PURE__*/_jsx(\"div\",{className:\"flex-column-parent\",children:/*#__PURE__*/_jsx(List,{className:\"noticPopover-list\"+(notificationReminders.length>9?' flex-column-child section':''),style:notificationReminders.length>9?{height:'480px'}:{},itemLayout:\"horizontal\",dataSource:notificationReminders,renderItem:(item,index)=>isLink(item)?/*#__PURE__*/_jsx(ListItem,{item:item,goToNewPage:()=>{},goToNewPage1:goToNewPage,getOptionByType:getOptionByType,memberMenu:memberMenu,isLink:isLink,getFirstName:getFirstName},index):/*#__PURE__*/_jsx(ListItem,{item:item,goToNewPage:goToNewPage,getOptionByType:getOptionByType,memberMenu:memberMenu,isLink:isLink,getFirstName:getFirstName},index)})});}export default/*#__PURE__*/React.memo(AppNoticeIcon);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}