{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\GeneralFooter\\\\ReledObjList.jsx\",\n  _s = $RefreshSig$();\n/*\r\n * @Author: Walt <EMAIL>\r\n * @Date: 2023-03-10 10:46:54\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2024-01-31 16:53:46\r\n * @Description: 被对象关联(只读页面，没有操作)\r\n */\n\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\nimport { generateRoutePathByNodeType } from \"@common/router/RouterRegister\";\nimport { useQueryTeam551GetReledObjList } from \"@common/service/commonHooks\";\nimport { isEmpty } from \"@common/utils/ArrayUtils\";\nimport { Table, Space } from \"antd\";\nimport { Link, useParams } from \"react-router-dom\";\nimport \"./ReledObjList.scss\";\n\n//被对象关联\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function ReledObjList({\n  objId,\n  objNodeId,\n  objType,\n  moduleName\n}) {\n  _s();\n  const {\n    teamId\n  } = useParams();\n  const {\n    data: reledObjList\n  } = useQueryTeam551GetReledObjList(teamId, objId, objType, !!objId); // 被对象关联\n\n  // 被对象关联列表\n  const columns = [{\n    title: '名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: \"24%\",\n    render: (value, row, index) => {\n      let {\n        pathname,\n        search\n      } = generateRoutePathByNodeType(row.objType, {\n        teamId,\n        nodeId: row.anchorNodeId,\n        nid: row.nodeId\n      });\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AppNodeResourceIcon, {\n          nodeType: row.objType,\n          className: \"fontsize-14\",\n          style: {\n            color: \"#3279fe\",\n            opacity: 0.4,\n            marginRight: 5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: pathname + search,\n          target: \"_blank\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '类型',\n    dataIndex: 'objTypeName',\n    key: 'objTypeName',\n    width: \"10%\"\n  }, {\n    title: '创建人',\n    dataIndex: 'userName',\n    key: 'userName',\n    width: \"10%\"\n  }, {\n    title: '创建时间',\n    dataIndex: 'createDt',\n    key: 'createDt',\n    width: \"13%\"\n  }, {\n    title: '关联人',\n    dataIndex: 'refName',\n    key: 'refName',\n    width: \"10%\"\n  }, {\n    title: '关联时间',\n    dataIndex: 'refDt',\n    key: 'refDt',\n    width: \"13%\"\n  }, {\n    title: '备注',\n    dataIndex: 'description',\n    key: 'description',\n    width: \"10%\",\n    render: description => /*#__PURE__*/_jsxDEV(\"div\", {\n      title: description,\n      className: \"text-overflow\",\n      style: {\n        width: 130\n      },\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 105\n    }, this)\n  },\n  // 暂无操作\n  {\n    title: '',\n    dataIndex: 'operation',\n    key: 'operation',\n    align: \"center\",\n    width: \"10%\",\n    disabled: true\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: !isEmpty(reledObjList) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"resource-reference\",\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        className: \"resource-reference-title\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u88AB\\u5BF9\\u8C61\\u5173\\u8054\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 12\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        className: \"custome-table padding-0\"\n        // columns={columns.filter(column => (editable || !column.disabled))}\n        ,\n        columns: columns,\n        dataSource: reledObjList,\n        size: \"small\",\n        style: {\n          padding: \"0px 20px\"\n        },\n        pagination: {\n          position: ['bottomCenter'],\n          size: 'small',\n          showQuickJumper: true,\n          showSizeChanger: true,\n          showTitle: true,\n          total: reledObjList === null || reledObjList === void 0 ? void 0 : reledObjList.length,\n          hideOnSinglePage: true,\n          showTotal: total => {\n            return `总共 ${total} 条`;\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n}\n_s(ReledObjList, \"Ui5JJxwetupTJLctc5qjT/IEfpA=\", false, function () {\n  return [useParams, useQueryTeam551GetReledObjList];\n});\n_c = ReledObjList;\nvar _c;\n$RefreshReg$(_c, \"ReledObjList\");", "map": {"version": 3, "names": ["AppNodeResourceIcon", "generateRoutePathByNodeType", "useQueryTeam551GetReledObjList", "isEmpty", "Table", "Space", "Link", "useParams", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReledObjList", "objId", "objNodeId", "objType", "moduleName", "_s", "teamId", "data", "reledObjList", "columns", "title", "dataIndex", "key", "width", "render", "value", "row", "index", "pathname", "search", "nodeId", "anchorNodeId", "nid", "style", "display", "alignItems", "children", "nodeType", "className", "color", "opacity", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "target", "description", "align", "disabled", "dataSource", "size", "padding", "pagination", "position", "showQuickJumper", "showSizeChanger", "showTitle", "total", "length", "hideOnSinglePage", "showTotal", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/GeneralFooter/ReledObjList.jsx"], "sourcesContent": ["/*\r\n * @Author: <PERSON> <EMAIL>\r\n * @Date: 2023-03-10 10:46:54\r\n * @LastEditors: <PERSON> <EMAIL>\r\n * @LastEditTime: 2024-01-31 16:53:46\r\n * @Description: 被对象关联(只读页面，没有操作)\r\n */\r\n\r\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\r\nimport { generateRoutePathByNodeType } from \"@common/router/RouterRegister\";\r\nimport {\r\n  useQueryTeam551GetReledObjList\r\n} from \"@common/service/commonHooks\";\r\nimport { isEmpty } from \"@common/utils/ArrayUtils\";\r\nimport { Table, Space } from \"antd\";\r\nimport { Link, useParams } from \"react-router-dom\";\r\nimport \"./ReledObjList.scss\";\r\n\r\n//被对象关联\r\nexport default function ReledObjList({ objId, objNodeId, objType, moduleName,}) {\r\n  const { teamId } = useParams()\r\n  const { data: reledObjList } = useQueryTeam551GetReledObjList(teamId, objId, objType, !!objId); // 被对象关联\r\n\r\n   // 被对象关联列表\r\n   const columns = [\r\n    {\r\n      title: '名称', dataIndex: 'name', key: 'name', width: \"24%\",\r\n      render: (value, row, index) => {\r\n        let { pathname, search } = generateRoutePathByNodeType(row.objType, {\r\n          teamId,\r\n          nodeId: row.anchorNodeId,\r\n          nid: row.nodeId,\r\n        })\r\n        return <div style={{display:'flex',alignItems:'center'}}>\r\n          <AppNodeResourceIcon nodeType={row.objType} className=\"fontsize-14\" style={{ color: \"#3279fe\", opacity: 0.4,marginRight:5 }}/>\r\n          <Link to={pathname+search} target=\"_blank\">\r\n            {value}\r\n          </Link>\r\n        </div>\r\n      }\r\n    },\r\n    { title: '类型', dataIndex: 'objTypeName', key: 'objTypeName', width: \"10%\", },\r\n    { title: '创建人', dataIndex: 'userName', key: 'userName', width: \"10%\", },\r\n    { title: '创建时间', dataIndex: 'createDt', key: 'createDt', width: \"13%\" },\r\n    { title: '关联人', dataIndex: 'refName', key: 'refName', width: \"10%\", },\r\n    { title: '关联时间', dataIndex: 'refDt', key: 'refDt', width: \"13%\" },\r\n    { title: '备注', dataIndex: 'description', key: 'description', width: \"10%\", render: (description) => <div title={description} className='text-overflow' style={{width: 130}}>{description}</div>},\r\n    // 暂无操作\r\n    {\r\n      title: '', dataIndex: 'operation', key: 'operation', align: \"center\", width: \"10%\", disabled: true,\r\n    }\r\n  ];\r\n  \r\n  return (\r\n    <>\r\n      {!isEmpty(reledObjList) &&\r\n        <div className=\"resource-reference\">\r\n           <Space className=\"resource-reference-title\">\r\n                <span>被对象关联</span>\r\n            </Space>\r\n          <Table className=\"custome-table padding-0\"\r\n            // columns={columns.filter(column => (editable || !column.disabled))}\r\n            columns={columns}\r\n            dataSource={reledObjList}\r\n            size={\"small\"}\r\n            style={{ padding: \"0px 20px\" }} \r\n            pagination={\r\n              {\r\n                position: ['bottomCenter'],\r\n                size: 'small',\r\n                showQuickJumper: true,\r\n                showSizeChanger: true,\r\n                showTitle: true,\r\n                total: reledObjList?.length,\r\n                hideOnSinglePage: true,\r\n                showTotal: (total) =>{\r\n                  return `总共 ${total} 条`\r\n                },\r\n              } \r\n            }  \r\n            />\r\n        </div >\r\n      }\r\n    </>\r\n  )\r\n}\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,mBAAmB,MAAM,iCAAiC;AACjE,SAASC,2BAA2B,QAAQ,+BAA+B;AAC3E,SACEC,8BAA8B,QACzB,6BAA6B;AACpC,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACnC,SAASC,IAAI,EAAEC,SAAS,QAAQ,kBAAkB;AAClD,OAAO,qBAAqB;;AAE5B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,eAAe,SAASC,YAAYA,CAAC;EAAEC,KAAK;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAW,CAAC,EAAE;EAAAC,EAAA;EAC9E,MAAM;IAAEC;EAAO,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC9B,MAAM;IAAEY,IAAI,EAAEC;EAAa,CAAC,GAAGlB,8BAA8B,CAACgB,MAAM,EAAEL,KAAK,EAAEE,OAAO,EAAE,CAAC,CAACF,KAAK,CAAC,CAAC,CAAC;;EAE/F;EACA,MAAMQ,OAAO,GAAG,CACf;IACEC,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,MAAM;IAAEC,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE,KAAK;IACzDC,MAAM,EAAEA,CAACC,KAAK,EAAEC,GAAG,EAAEC,KAAK,KAAK;MAC7B,IAAI;QAAEC,QAAQ;QAAEC;MAAO,CAAC,GAAG9B,2BAA2B,CAAC2B,GAAG,CAACb,OAAO,EAAE;QAClEG,MAAM;QACNc,MAAM,EAAEJ,GAAG,CAACK,YAAY;QACxBC,GAAG,EAAEN,GAAG,CAACI;MACX,CAAC,CAAC;MACF,oBAAOvB,OAAA;QAAK0B,KAAK,EAAE;UAACC,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC;QAAQ,CAAE;QAAAC,QAAA,gBACtD7B,OAAA,CAACT,mBAAmB;UAACuC,QAAQ,EAAEX,GAAG,CAACb,OAAQ;UAACyB,SAAS,EAAC,aAAa;UAACL,KAAK,EAAE;YAAEM,KAAK,EAAE,SAAS;YAAEC,OAAO,EAAE,GAAG;YAACC,WAAW,EAAC;UAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eAC9HtC,OAAA,CAACH,IAAI;UAAC0C,EAAE,EAAElB,QAAQ,GAACC,MAAO;UAACkB,MAAM,EAAC,QAAQ;UAAAX,QAAA,EACvCX;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IACR;EACF,CAAC,EACD;IAAEzB,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,aAAa;IAAEC,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5E;IAAEH,KAAK,EAAE,KAAK;IAAEC,SAAS,EAAE,UAAU;IAAEC,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAO,CAAC,EACvE;IAAEH,KAAK,EAAE,MAAM;IAAEC,SAAS,EAAE,UAAU;IAAEC,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAM,CAAC,EACvE;IAAEH,KAAK,EAAE,KAAK;IAAEC,SAAS,EAAE,SAAS;IAAEC,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAO,CAAC,EACrE;IAAEH,KAAK,EAAE,MAAM;IAAEC,SAAS,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAM,CAAC,EACjE;IAAEH,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,aAAa;IAAEC,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAGwB,WAAW,iBAAKzC,OAAA;MAAKa,KAAK,EAAE4B,WAAY;MAACV,SAAS,EAAC,eAAe;MAACL,KAAK,EAAE;QAACV,KAAK,EAAE;MAAG,CAAE;MAAAa,QAAA,EAAEY;IAAW;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC,CAAC;EAChM;EACA;IACEzB,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE,WAAW;IAAEC,GAAG,EAAE,WAAW;IAAE2B,KAAK,EAAE,QAAQ;IAAE1B,KAAK,EAAE,KAAK;IAAE2B,QAAQ,EAAE;EAChG,CAAC,CACF;EAED,oBACE3C,OAAA,CAAAE,SAAA;IAAA2B,QAAA,EACG,CAACnC,OAAO,CAACiB,YAAY,CAAC,iBACrBX,OAAA;MAAK+B,SAAS,EAAC,oBAAoB;MAAAF,QAAA,gBAChC7B,OAAA,CAACJ,KAAK;QAACmC,SAAS,EAAC,0BAA0B;QAAAF,QAAA,eACtC7B,OAAA;UAAA6B,QAAA,EAAM;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACVtC,OAAA,CAACL,KAAK;QAACoC,SAAS,EAAC;QACf;QAAA;QACAnB,OAAO,EAAEA,OAAQ;QACjBgC,UAAU,EAAEjC,YAAa;QACzBkC,IAAI,EAAE,OAAQ;QACdnB,KAAK,EAAE;UAAEoB,OAAO,EAAE;QAAW,CAAE;QAC/BC,UAAU,EACR;UACEC,QAAQ,EAAE,CAAC,cAAc,CAAC;UAC1BH,IAAI,EAAE,OAAO;UACbI,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE,IAAI;UACfC,KAAK,EAAEzC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0C,MAAM;UAC3BC,gBAAgB,EAAE,IAAI;UACtBC,SAAS,EAAGH,KAAK,IAAI;YACnB,OAAO,MAAMA,KAAK,IAAI;UACxB;QACF;MACD;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC,gBAET,CAAC;AAEP;AAAC9B,EAAA,CAlEuBL,YAAY;EAAA,QACfL,SAAS,EACGL,8BAA8B;AAAA;AAAA+D,EAAA,GAFvCrD,YAAY;AAAA,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}