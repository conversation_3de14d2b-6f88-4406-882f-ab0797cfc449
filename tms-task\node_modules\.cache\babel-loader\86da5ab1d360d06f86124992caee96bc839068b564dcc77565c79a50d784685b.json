{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\quickAcess\\\\views\\\\Search\\\\SearchDetail\\\\FlowSearchEditTable.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\n/*\r\n * @Author: Walt <EMAIL>\r\n * @Date: 2025-01-23 15:48:46\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2025-01-24 15:42:30\r\n * @Description: 电子流先决条件\r\n */\n/* eslint-disable jsx-a11y/anchor-is-valid */\n/* eslint-disable react-hooks/exhaustive-deps */\n\nimport { DeleteOutlined, MinusOutlined, PlusOutlined, EditOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined, ArrowUpOutlined, ArrowDownOutlined, MenuOutlined } from \"@ant-design/icons\";\nimport { CRITERIA_RELOP_TYPE_LIST, eConsoleUiControl, CRITERIA_RELOP_TYPE, eDynamicConditionNodeId, eDynamicCondition, eQueryType, eEnTag, eMoveType } from \"@common/utils/enum\";\nimport * as toolUtil from \"@common/utils/toolUtil\";\nimport { Button, DatePicker, Form, Input, Space, Select, Table } from \"antd\";\nimport moment from \"moment\";\nimport { nanoid } from \"nanoid\";\nimport * as qs from 'qs';\nimport React, { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState } from \"react\";\nimport { isAllEmptyObjValue } from 'src/quickAcess/utils/ArrayUtils';\nimport { getRelOpTypeTitle } from 'src/quickAcess/utils/ViewUtils';\nimport { ATTRVALUE_LIST, ATTR_VAR, ATTR_VALUE, DATE_FORMAT, LEFT_BRACKET, LEFT_BRACKET_STR, LOGICAL_OP_TYPE, RELOP_LIST, REL_OP_TYPE, RIGHT_BRACKET, RIGHT_BRACKET_STR, eDynamicVarType, eAttrSiteType, eRelOpType } from \"src/quickAcess/utils/Config\";\nimport { getPropValueList } from '@common/utils/ViewUtils';\nimport { getOptionsView } from \"@common/utils/ViewUtils\";\nimport SearchCodeEditorInput from \"@components/SearchCodeEditorInput\";\nimport cloneDeep from 'lodash/cloneDeep';\nimport SearchCodeEditorModal from \"./SearchCodeEditorModal\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport \"./SearchEditTable.scss\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { isEmpty, handleMoveAction } from \"@common/utils/ArrayUtils\";\nimport { DndContext } from '@dnd-kit/core';\nimport { restrictToVerticalAxis } from '@dnd-kit/modifiers';\nimport { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { eRegionType } from \"src/inspect/utils/enum\";\nimport { getExprs, getSubAttrNodeList } from \"@common/utils/logicUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EditableContext = /*#__PURE__*/React.createContext(null);\nconst DragCustomerFormRow = ({\n  children,\n  ...props\n}) => {\n  _s();\n  const [form] = Form.useForm();\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n    transition,\n    isDragging\n  } = useSortable({\n    id: props['data-row-key']\n  });\n  const style = {\n    ...props.style,\n    transform: CSS.Transform.toString(transform && {\n      ...transform,\n      scaleY: 1\n    }),\n    transition,\n    ...(isDragging ? {\n      position: 'relative',\n      zIndex: 9999\n    } : {})\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    component: false,\n    children: /*#__PURE__*/_jsxDEV(EditableContext.Provider, {\n      value: form,\n      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n        ...props,\n        ref: setNodeRef,\n        style: style,\n        ...attributes,\n        children: React.Children.map(children, child => {\n          if (child.key === \"operation\" && !child.props.record.isDefault) {\n            // 操作列，且非默认行\n            return {\n              ...child,\n              props: {\n                ...child.props,\n                render: (value, row, index) => /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    ref: setActivatorNodeRef,\n                    icon: /*#__PURE__*/_jsxDEV(MenuOutlined, {\n                      title: \"\\u62D6\\u62FD\\u6392\\u5E8F\",\n                      style: {\n                        color: \"#0077f2\",\n                        touchAction: 'none',\n                        cursor: 'move',\n                        fontSize: 12\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 76,\n                      columnNumber: 74\n                    }, this),\n                    ...listeners\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 76,\n                    columnNumber: 22\n                  }, this), child.props.render && child.props.render(value, row, index)]\n                }, void 0, true)\n              }\n            };\n          }\n          return child;\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(DragCustomerFormRow, \"CRNAa9fwKPsQLsXOYgJzKmidj/U=\", false, function () {\n  return [Form.useForm, useSortable];\n});\n_c = DragCustomerFormRow;\nconst EditableRow = ({\n  index,\n  ...props\n}) => {\n  _s2();\n  const [form] = Form.useForm();\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    component: false,\n    children: /*#__PURE__*/_jsxDEV(EditableContext.Provider, {\n      value: form,\n      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s2(EditableRow, \"rI7DrJIrFu7YmlGWYiMFTzs8jF0=\", false, function () {\n  return [Form.useForm];\n});\n_c2 = EditableRow;\nconst EditableCell = ({\n  title,\n  editable,\n  children,\n  dataIndex,\n  record,\n  handleSave,\n  attrNodeList,\n  subAttrNodeList,\n  _exprs,\n  envList,\n  suggestionList,\n  opreateList,\n  queryType,\n  ...restProps\n}) => {\n  _s3();\n  const [editing, setEditing] = useState(false);\n  const inputRef = useRef(Object.create(null));\n  const codeInputRef = useRef(Object.create(null));\n  const form = useContext(EditableContext);\n  let nodeList = (record === null || record === void 0 ? void 0 : record.index) == 0 ? attrNodeList : subAttrNodeList; // attrNodeList：第一行的数据源  \n\n  useEffect(() => {\n    if (editing) {\n      if (record[ATTR_VAR] == eDynamicConditionNodeId) {\n        // TODO: select和focus\n      } else {\n        // inputRef.current.select();\n        // inputRef.current.foucs();\n      }\n    }\n  }, [editing]);\n\n  // 根据record更新值\n  useEffect(() => {\n    if (dataIndex) {\n      setTableFieldsValue(record, dataIndex);\n    }\n  }, [record]);\n  const toggleEdit = () => {\n    setEditing(!editing);\n    setTableFieldsValue(record, dataIndex);\n  };\n  const toggleSearchCodeInputEdit = e => {\n    e.stopPropagation();\n    globalEventBus.emit(\"openSearchCodeEditorModalEvent\", \"\", {\n      attrNodeList: nodeList,\n      exprs: _exprs,\n      value: record[dataIndex],\n      envList,\n      suggestionList,\n      opreateList,\n      record,\n      queryType,\n      callback: codeInputSave.bind(this)\n    });\n  };\n\n  // 刷新数据\n  const setTableFieldsValue = (data, dataIndex) => {\n    form.setFieldsValue({\n      [dataIndex]: data[dataIndex]\n    });\n  };\n\n  // 保存\n  const save = async () => {\n    try {\n      const values = await form.validateFields();\n      if (values[ATTR_VAR] != record[ATTR_VAR]) {\n        // 字段更新时同时也需要更新字段类型\n        //  t.clone is not a function\n        let properType = nodeList.find(item => item.nodeId === values[ATTR_VAR]);\n        if (!!properType) {\n          // 动态条件不在attrNodeList中，TODO:待优化，实际上需要解决的是动态条件的值添加后更新到form中\n          const {\n            dataType,\n            list,\n            relopList\n          } = properType;\n          values.dataType = dataType; // 字段类型\n          values[ATTRVALUE_LIST] = list; // 值\n          values[ATTR_VALUE] = undefined; // 重置值\n          values[RELOP_LIST] = relopList; // 连接符\n          setTableFieldsValue(values, ATTR_VALUE);\n        }\n      }\n      // if (record.dataType == 'ListBox' && values[REL_OP_TYPE] == '1' && (values[ATTR_VALUE] || []).length > 1) {\n      //   values[REL_OP_TYPE] = '17'\n      // }\n      const newData = {\n        ...record,\n        ...values\n      };\n      handleSave(newData); // 合并原有的row和新的values值\n      setTableFieldsValue(newData, dataIndex);\n    } catch (errInfo) {\n      console.log(\"Save failed:\", errInfo);\n    }\n  };\n\n  // Input的保存，需要切换UI,与Select和Date类型处理方式不同\n  const codeInputSave = async ({\n    originValue\n  }) => {\n    console.log(\"save\", originValue);\n    try {\n      //如果值没有更改的话，则不触发保存方法\n      const newData = {\n        ...record,\n        [dataIndex]: originValue\n      };\n      handleSave(newData); // 合并原有的row和新的values值\n      codeInputRef.current.setValue(originValue);\n    } catch (errInfo) {\n      console.log(\"Save failed:\", errInfo);\n    }\n  };\n\n  // Input的保存，需要切换UI,与Select和Date类型处理方式不同\n  const inputSave = async ({\n    originValue\n  }) => {\n    console.log(\"save\", originValue);\n    try {\n      //如果值没有更改的话，则不触发保存方法\n      handleSave({\n        ...record,\n        [dataIndex]: originValue\n      }); // 合并原有的row和新的values值\n      toggleEdit();\n    } catch (errInfo) {\n      console.log(\"Save failed:\", errInfo);\n    }\n  };\n\n  // 选择任务\n  const fieldObjectType = useMemo(() => {\n    return (nodeList || []).reduce((pre, cur) => {\n      const curObj = {\n        key: cur.nodeId,\n        value: cur.nodeId,\n        label: cur.nodeName,\n        dataType: cur.dataType\n      };\n      return pre.concat(curObj);\n    }, []);\n  }, [record, nodeList]);\n\n  // 值渲染的UI\n  const getValueInputUI = () => {\n    if (record[ATTR_VAR] == eDynamicConditionNodeId) {\n      // 动态条件\n      if (editing) {\n        childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            margin: 0\n          },\n          name: dataIndex,\n          children: /*#__PURE__*/_jsxDEV(SearchCodeEditorInput, {\n            ref: codeInputRef,\n            envList: envList,\n            suggestionList: suggestionList,\n            onPressEnter: inputSave,\n            onBlur: inputSave\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this);\n      } else {\n        childNode = /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-editble\",\n          onClick: toggleEdit,\n          children: [/*#__PURE__*/_jsxDEV(SearchCodeEditorInput, {\n            ref: codeInputRef,\n            envList: envList,\n            suggestionList: suggestionList,\n            readOnly: true,\n            value: record[dataIndex]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"editble-icon\",\n            children: /*#__PURE__*/_jsxDEV(EditOutlined, {\n              onClick: toggleSearchCodeInputEdit\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this);\n      }\n    } else if (record.dataType === eConsoleUiControl.TextBox) {\n      // input类型\n      if (editing) {\n        childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            margin: 0\n          },\n          name: dataIndex,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            ref: inputRef,\n            autoComplete: \"off\",\n            placeholder: placeholder,\n            onPressEnter: e => {\n              inputSave({\n                originValue: e.target.value\n              });\n            },\n            onBlur: e => {\n              inputSave({\n                originValue: e.target.value\n              });\n            },\n            className: \"search-table-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this);\n      } else {\n        //非编辑状态，如果没有值，则显示默认值placeholder，需要创建引用变量data，因为children只读，不能更改\n        let data = [...children];\n        let value = !isEmpty(children[1]) ? children[1] : placeholder;\n        data[1] = value;\n        let className = !isEmpty(children[1]) ? \"editable-cell-value-wrap\" : \"editable-cell-value-wrap-none\";\n        childNode = /*#__PURE__*/_jsxDEV(\"div\", {\n          className: className,\n          onClick: toggleEdit,\n          children: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this);\n      }\n    } else if (record.dataType === eConsoleUiControl.ListBox) {\n      childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          margin: 0\n        },\n        name: dataIndex,\n        children: /*#__PURE__*/_jsxDEV(Select\n        // open={true} //默认展开，单击即可展开\n        // dropdownClassName=\"table-select-option\"\n        , {\n          dropdownMatchSelectWidth: false,\n          bordered: false,\n          mode: record[REL_OP_TYPE] == eRelOpType._in ? \"multiple\" : undefined //值可以多选\n          ,\n          className: \"search-table-select\",\n          popupClassName: \"search-table-select-pop\",\n          optionFilterProp: \"propValue\",\n          onBlur: save //失去焦点回调\n          ,\n          onChange: save //改变后回调\n          ,\n          allowClear: true,\n          showSearch: true,\n          fieldNames: {\n            label: \"propValue\",\n            value: \"propType\"\n          },\n          options: record[ATTRVALUE_LIST]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this);\n    } else if (record.dataType === eConsoleUiControl.Date) {\n      childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          margin: 0\n        },\n        name: dataIndex,\n        children: /*#__PURE__*/_jsxDEV(DatePicker, {\n          format: DATE_FORMAT,\n          onChange: save,\n          className: \"search-table-datepicker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this);\n    }\n    return childNode;\n  };\n  let childNode = children;\n  let placeholder = \"请输入\" + title;\n\n  // 下拉列表的值\n  let objectTypeOptions;\n  switch (dataIndex) {\n    case ATTR_VAR:\n      // 字段\n      // objectTypeSelect = getOptionsView(fieldObjectType);\n      objectTypeOptions = fieldObjectType;\n      break;\n    case LOGICAL_OP_TYPE:\n      // 连接符\n      // objectTypeSelect = getOptionsView(CRITERIA_RELOP_TYPE_LIST);\n      objectTypeOptions = CRITERIA_RELOP_TYPE_LIST;\n      break;\n    case REL_OP_TYPE:\n      // 条件\n      // objectTypeSelect = getPropValueList(record[RELOP_LIST])\n      objectTypeOptions = (record[RELOP_LIST] || []).map(relop => ({\n        key: relop.propType,\n        value: relop.propType,\n        label: relop.propValue\n      }));\n      break;\n    default:\n      break;\n  }\n  if (editable) {\n    if (dataIndex == ATTR_VAR || dataIndex == LOGICAL_OP_TYPE || dataIndex == REL_OP_TYPE) {\n      if (record[ATTR_VAR] == eDynamicConditionNodeId && dataIndex == REL_OP_TYPE) {\n        // 动态字段没有条件\n        childNode = /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"editable-cell-disabled\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this);\n      } else if (record[ATTR_VAR] == eDynamicConditionNodeId && dataIndex == ATTR_VAR) {\n        // 动态字段\n        childNode = /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u52A8\\u6001\\u6761\\u4EF6\\uFF08\", /*#__PURE__*/_jsxDEV(FunctionOutlined, {\n            className: \"color-blue\",\n            style: {\n              margin: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 18\n          }, this), \"\\uFF09\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this);\n      } else {\n        childNode = /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            margin: 0\n          },\n          name: dataIndex,\n          children: /*#__PURE__*/_jsxDEV(Select\n          // open={true} //默认展开，单击即可展开\n          , {\n            dropdownMatchSelectWidth: false,\n            bordered: false,\n            className: \"search-table-select\",\n            popupClassName: \"search-table-select-pop\"\n            // dropdownClassName=\"table-select-option\"\n            ,\n            optionFilterProp: \"label\",\n            onBlur: save //失去焦点回调\n            ,\n            onChange: save //改变后回调\n            ,\n            allowClear: dataIndex == ATTR_VAR,\n            showSearch: dataIndex == ATTR_VAR,\n            options: objectTypeOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this);\n      }\n    } else {\n      // 值\n      childNode = getValueInputUI();\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"td\", {\n    ...restProps,\n    children: childNode\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 411,\n    columnNumber: 10\n  }, this);\n};\n\n/**\r\n * TODO: 编辑器值fontsize-12,color:#333\r\n * 高级搜索-搜索条件表格\r\n *  FIXME: 父组件获取子组件实例关键点:\r\n * 1.useImperativeHandle/forwardRef \r\n * 2.ref不是结构赋值，而是直接取值\r\n * @param {queryType} 是否是Kpi模块 \r\n * @param {*} ref \r\n * @returns \r\n */\n// TODO:此组件为了方便kpi和高级搜索，将数据处理逻辑合并到该组件，需要注意数据处理不能影响原数据，因此全部采用map遍历的形式，可能并不是好的思路，需要注意\n_s3(EditableCell, \"wupz0ybPaPXsmTiYSZvlbxQefsU=\");\n_c3 = EditableCell;\nfunction FlowSearchEditTable({\n  attrNodeList = [],\n  criteriaList = [],\n  selectionList = [],\n  exprs = [],\n  queryType,\n  loading\n}, ref) {\n  _s4();\n  const [initDataSource, setInitDataSource] = useState([]); //初始化数据\n  const [dataSource, setDataSource] = useState([]); //修改后的数据\n  const [_exprs, setExprs] = useState([]); // 变量\n\n  // 可以让你在使用 ref 时自定义暴露给父组件的实例值\n  useImperativeHandle(ref, () => ({\n    // 获取列表内容:过滤掉默认选项\n    getCriteriaListForUI: () => getCriteriaListForUI(dataSource),\n    getInitDataSource: () => getCriteriaListForUI(initDataSource),\n    getCriteriaListForBackend: () => getCriteriaListForBackend(),\n    addSearchCode: () => addSearchCode()\n  }));\n\n  // 创建安全的依赖项，避免循环引用问题\n  const attrNodeListDeps = useMemo(() => {\n    return attrNodeList.map(item => {\n      var _item$list, _item$relopList;\n      return {\n        nodeId: item.nodeId,\n        nodeName: item.nodeName,\n        dataType: item.dataType,\n        queryableFlg: item.queryableFlg,\n        // 只提取 list 中的关键属性，避免 React 元素\n        listLength: ((_item$list = item.list) === null || _item$list === void 0 ? void 0 : _item$list.length) || 0,\n        // 只提取 relopList 中的关键属性\n        relopListLength: ((_item$relopList = item.relopList) === null || _item$relopList === void 0 ? void 0 : _item$relopList.length) || 0\n      };\n    });\n  }, [attrNodeList]);\n  const criteriaListDeps = useMemo(() => {\n    return criteriaList.map(item => ({\n      id: item.id,\n      attrNid: item.attrNid,\n      attrValue: typeof item.attrValue === 'object' ? JSON.stringify(item.attrValue) : item.attrValue,\n      relOpType: item.relOpType\n    }));\n  }, [criteriaList]);\n  useEffect(() => {\n    if (!isEmpty(attrNodeList)) {\n      initData();\n    }\n  }, [JSON.stringify(attrNodeListDeps), JSON.stringify(criteriaListDeps)]);\n  function _setDataSource(dataSource) {\n    setDataSource(cloneDeep(dataSource.map((data, index) => ({\n      ...data,\n      index\n    }))));\n  }\n\n  // 获取表格属性\n  const getCriteriaListForUI = data => {\n    try {\n      // 注意：该方法会走两遍,不能更改原有数据结构，所以需要使用map，不能使用forEach改变原有数据结构，也不能使用json.parse()\n      data = data.filter(item => !item.isDefault);\n      return (data || []).map(item => {\n        let attrValue = item.attrValue;\n        switch (item.dataType) {\n          case eConsoleUiControl.ListBox:\n            // 多选框，需要将多选数组转化为字符串逗号拼接 \n            attrValue = !isEmpty(item.attrValue) && Array.isArray(item.attrValue) ? item.attrValue.join(\",\") : item.attrValue;\n            break;\n          case eConsoleUiControl.Date:\n            // 时间需要转为字符串\n            attrValue = !isEmpty(item.attrValue) ? item.attrValue.format(DATE_FORMAT) : item.attrValue;\n            break;\n          default:\n            break;\n        }\n        return {\n          ...item,\n          ...{\n            attrValue\n          }\n        };\n      });\n    } catch (error) {\n      console.log(\"getCriteriaListForUI\", error);\n      globalUtil.warning(\"保存失败\");\n      return [];\n    }\n  };\n\n  // 获取高级搜索自定义表单接口数据\n  const getCriteriaListForBackend = () => {\n    try {\n      let data = getCriteriaListForUI(dataSource);\n      // 获取表格内容\n      data = data.map(item => {\n        var _item$LEFT_BRACKET, _item$RIGHT_BRACKET;\n        return {\n          \"leftBracketCnt\": (_item$LEFT_BRACKET = item[LEFT_BRACKET]) === null || _item$LEFT_BRACKET === void 0 ? void 0 : _item$LEFT_BRACKET.length,\n          [ATTR_VAR]: item[ATTR_VAR],\n          \"relOpType\": item[REL_OP_TYPE],\n          \"attrValue\": item[ATTR_VALUE],\n          \"logicalOpType\": item[LOGICAL_OP_TYPE] || \"0\",\n          \"rightBracketCnt\": (_item$RIGHT_BRACKET = item[RIGHT_BRACKET]) === null || _item$RIGHT_BRACKET === void 0 ? void 0 : _item$RIGHT_BRACKET.length,\n          \"isDisableDelete\": item.isDisableDelete\n        };\n      });\n      return data || [];\n    } catch (e) {\n      globalUtil.warning(\"保存失败\");\n      console.warn(\"高级搜索自定义表单保存失败\", e);\n      // message.warn(\"高级搜索自定义表单保存失败\");\n      return [];\n    }\n  };\n  const relOpTypeTitle = useMemo(() => getRelOpTypeTitle(selectionList), []);\n  const getColumnsOperate = (text, record, index, bracketCnt, bracketStr) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-table-opreate\",\n      children: [/*#__PURE__*/_jsxDEV(\"a\", {\n        onClick: () => {\n          reduce(record, index, bracketCnt, bracketStr);\n        },\n        className: \"minus-btn \",\n        children: /*#__PURE__*/_jsxDEV(MinusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        onClick: () => {\n          add(record, index, bracketCnt, bracketStr);\n        },\n        className: \"plus-btn\",\n        children: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 12\n    }, this);\n  };\n  const columnsRow = [{\n    title: \"左(\",\n    dataIndex: LEFT_BRACKET,\n    // colunms的render不会随着数据的刷新而刷新,FIXME:改成function函数式组件后，可以刷新了，说明Class的刷新机制和function不太一致\n    render: (text, record, index) => getColumnsOperate(text, record, index, LEFT_BRACKET, LEFT_BRACKET_STR),\n    width: 60\n  }, {\n    title: \"字段\",\n    dataIndex: ATTR_VAR,\n    editable: true,\n    width: 130\n  }, {\n    title: relOpTypeTitle,\n    dataIndex: REL_OP_TYPE,\n    editable: true,\n    width: 80\n  }, {\n    title: \"值\",\n    dataIndex: ATTR_VALUE,\n    editable: true\n  }, {\n    title: \"右)\",\n    dataIndex: RIGHT_BRACKET,\n    render: (text, record, index) => getColumnsOperate(text, record, index, RIGHT_BRACKET, RIGHT_BRACKET_STR),\n    width: 60\n  }, {\n    title: \"连接符\",\n    dataIndex: LOGICAL_OP_TYPE,\n    editable: true,\n    width: 60\n  }, {\n    title: \"操作\",\n    dataIndex: \"operation\",\n    width: 100,\n    render: (_, record, index) => {\n      if (record.isDefault) {\n        return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n      }\n      return /*#__PURE__*/_jsxDEV(Space, {\n        size: 10,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 21\n          }, this),\n          onClick: () => {\n            handleDelete(record.key);\n          },\n          size: \"small\",\n          disabled: record.isDefault || record.isDisableDelete //默认行、不可删除\n          ,\n          title: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n\n  // 获取默认值\n  const getDefaultRow = () => {\n    return {\n      key: nanoid(),\n      leftBracket: \"\",\n      //左括号(\n      [ATTR_VAR]: \"\",\n      //字段\n      relOpType: undefined,\n      //条件\n      attrValue: undefined,\n      //值\n      rightBracket: \"\",\n      //右括号）\n      logicalOpType: \"\",\n      //连接符\n      dataType: eConsoleUiControl.TextBox,\n      //字段类型:默认文本\n      attrValueList: [],\n      //值Select的List\n      isDefault: true\n    };\n  };\n\n  // 初始化数据\n  const initData = () => {\n    let initData1 = assembleInitTableData([...criteriaList]);\n    setInitDataSource([...initData1]); //记录初始化数据\n    let data = addDefaultRow([...initData1]);\n    _setDataSource(data);\n    const _exprs = getExprs(attrNodeList, exprs);\n    setExprs(_exprs);\n  };\n\n  // 初始化数据\n  const assembleInitTableData = criteriaList => {\n    try {\n      if (isEmpty(criteriaList)) return [];\n      // TODO:288个条件直接卡死\n      return criteriaList.map((item, index) => {\n        var _item$REL_OP_TYPE, _item$LOGICAL_OP_TYPE;\n        let newItem = {};\n        let properType = getProperType(item[ATTR_VAR], index, criteriaList);\n        const {\n          dataType,\n          list,\n          relopList\n        } = properType;\n        newItem.key = toolUtil.guid(); //本地数据没有id,需要guid作为key\n        newItem.dataType = dataType;\n        newItem[ATTRVALUE_LIST] = list;\n        newItem[RELOP_LIST] = relopList; //连接符\n        newItem[REL_OP_TYPE] = (_item$REL_OP_TYPE = item[REL_OP_TYPE]) === null || _item$REL_OP_TYPE === void 0 ? void 0 : _item$REL_OP_TYPE.toString(); // 条件,需要转为String\n        newItem[LOGICAL_OP_TYPE] = (_item$LOGICAL_OP_TYPE = item[LOGICAL_OP_TYPE]) === null || _item$LOGICAL_OP_TYPE === void 0 ? void 0 : _item$LOGICAL_OP_TYPE.toString(); // 连接符,需要转为String\n        if (dataType == eConsoleUiControl.ListBox) {\n          newItem.attrValue = item.attrValue.split(\",\"); //List\n        } else if (dataType == eConsoleUiControl.Date) {\n          newItem.attrValue = moment(item.attrValue, DATE_FORMAT); //日期类型格式化\n        }\n        return {\n          ...item,\n          ...newItem\n        };\n      }) || [];\n    } catch (e) {\n      globalUtil.warning('自定义表单数据初始化失败！');\n      console.log('自定义表单数据初始化失败！', e);\n    }\n  };\n\n  // 根据attrNid获取下拉List\n  const getProperType = (attrVar, index, criteriaList) => {\n    var _criteriaList$;\n    let nodeList = index == 0 ? attrNodeList : getSubAttrNodeList((_criteriaList$ = criteriaList[0]) === null || _criteriaList$ === void 0 ? void 0 : _criteriaList$.attrValue, attrNodeList);\n    return [...nodeList, eDynamicCondition].find(item => item.nodeId === attrVar) || {};\n  };\n\n  //添加默认项\n  const addDefaultRow = obj => {\n    let defaultIndex = obj.findIndex(item => item.isDefault);\n    if (defaultIndex === -1) {\n      let defaultRow = getDefaultRow();\n      obj.push(defaultRow);\n    }\n    return obj;\n  };\n\n  // 减少左括号/增加右括号\n  const reduce = (record, index, bracketCnt, bracketStr) => {\n    const newDataSource = [...dataSource];\n    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt].replace(bracketStr, \"\");\n    _setDataSource(newDataSource);\n  };\n  const add = (record, index, bracketCnt, bracketStr) => {\n    const newDataSource = [...dataSource];\n    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt] + bracketStr;\n    _setDataSource(newDataSource);\n  };\n  const handleDelete = key => {\n    const newDataSource = [...dataSource];\n    _setDataSource(newDataSource.filter(item => item.key !== key));\n  };\n\n  // 当输入新的一行内容后，上一行的“连接符”默认给 “且”，因为这个是用户必选的项，又经常容易忘了设置。\n  const handleAddLogicalOpType = dataSource => {\n    dataSource.forEach((field, index) => {\n      if (!field.isDefault && index != dataSource.length - 2 && (field[LOGICAL_OP_TYPE] == CRITERIA_RELOP_TYPE.op_place || isEmpty(field[LOGICAL_OP_TYPE]))) {\n        field[LOGICAL_OP_TYPE] = CRITERIA_RELOP_TYPE.op_and;\n      }\n    });\n    _setDataSource(dataSource);\n  };\n  const handleSave = row => {\n    // 判断row是否有数据\n    let newData = [...dataSource];\n    const index = newData.findIndex(item => row.key === item.key);\n    const item = newData[index];\n    if (index == 0 && row[ATTR_VALUE] != item[ATTR_VALUE]) {\n      // 任务节点更改后需要清空下面子节点的选择\n      newData = [newData[0]];\n    }\n    if (row.isDefault && !isAllEmptyObjValue(row)) row.isDefault = false; //将默认行且非空则更改为非默认行\n    newData.splice(index, 1, {\n      ...item,\n      ...row\n    });\n    let data = addDefaultRow([...newData]);\n    handleAddLogicalOpType(data);\n  };\n\n  // 变量\n  const envList = useMemo(() => {\n    // 搜索模块，需要去除kpiTag为isKpi的变量\n    let _envList = (_exprs || []).filter(item => item.attrType == eDynamicVarType.variable && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType)).map(attrNode => ({\n      varKey: attrNode.attrDisplayValue,\n      varValue: attrNode.attrValue\n    }));\n    return [..._envList];\n  }, [attrNodeList, _exprs, queryType]);\n\n  // 函数\n  const suggestionList = useMemo(() => {\n    const _dynamicCondition = (_exprs || []).filter(item => item.attrType == eDynamicVarType.func && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType));\n    let _suggestionList1 = _dynamicCondition.map(attrNode => ({\n      varKey: attrNode.attrDisplayValue,\n      varValue: attrNode.attrValue,\n      functionName: attrNode.functionName\n    }));\n    return [..._suggestionList1];\n  }, [attrNodeList, _exprs, queryType]);\n\n  // 操作符\n  const opreateList = useMemo(() => {\n    const _dynamicCondition = (_exprs || []).filter(item => item.attrType == eDynamicVarType.operator && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType));\n    let _operatorList = _dynamicCondition.map(attrNode => ({\n      varKey: attrNode.attrDisplayValue,\n      varValue: attrNode.attrValue,\n      functionName: attrNode.functionName\n    }));\n    return [..._operatorList];\n  }, [attrNodeList, _exprs, queryType]);\n\n  // 添加动态条件\n  const addSearchCode = () => {\n    globalEventBus.emit(\"openSearchCodeEditorModalEvent\", \"\", {\n      attrNodeList,\n      exprs: _exprs,\n      value: \"\",\n      envList,\n      suggestionList,\n      opreateList,\n      queryType,\n      callback: addSearchCodeCallback\n    });\n  };\n\n  // 添加动态条件回调\n  const addSearchCodeCallback = ({\n    originValue\n  }) => {\n    const newData = [...dataSource];\n    const newValue = {\n      key: nanoid(),\n      leftBracket: \"\",\n      //左括号(\n      [ATTR_VAR]: eDynamicConditionNodeId,\n      //字段\n      relOpType: undefined,\n      //条件\n      attrValue: originValue,\n      //值\n      rightBracket: \"\",\n      //右括号）\n      logicalOpType: \"\",\n      //连接符\n      dataType: eConsoleUiControl.TextBox,\n      //字段类型:默认文本\n      attrValueList: [],\n      //值Select的List\n      isDefault: false\n    };\n    // 数组倒数第二位插入\n    newData.splice(newData.length - 1, 0, newValue);\n    handleAddLogicalOpType(newData); // 添加动态条件也需要检查连接符\n  };\n\n  // 拖拽排序\n  const onDragEnd = ({\n    active,\n    over\n  }) => {\n    if (active !== null && active !== void 0 && active.id && over !== null && over !== void 0 && over.id && (active === null || active === void 0 ? void 0 : active.id) !== (over === null || over === void 0 ? void 0 : over.id)) {\n      let _dataSource = [...dataSource];\n      const activeIndex = _dataSource.findIndex(i => i.key == (active === null || active === void 0 ? void 0 : active.id));\n      const overIndex = _dataSource.findIndex(i => i.key == (over === null || over === void 0 ? void 0 : over.id));\n      _dataSource = arrayMove(_dataSource, activeIndex, overIndex);\n      _setDataSource(_dataSource);\n    }\n  };\n\n  // 任务下的属性\n  const subAttrNodeList = useMemo(() => {\n    var _dataSource$;\n    return getSubAttrNodeList((_dataSource$ = dataSource[0]) === null || _dataSource$ === void 0 ? void 0 : _dataSource$[ATTR_VALUE], attrNodeList);\n  }, [dataSource, attrNodeList]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(DndContext, {\n      modifiers: [restrictToVerticalAxis],\n      onDragEnd: onDragEnd,\n      children: /*#__PURE__*/_jsxDEV(SortableContext, {\n        items: dataSource.map(i => i.key) // 需要全部数据的key,否则无法拖拽\n        ,\n        strategy: verticalListSortingStrategy,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          components: {\n            body: {\n              row: DragCustomerFormRow,\n              cell: EditableCell\n            }\n          },\n          rowKey: \"key\" // 注意不能是key,也不能不填\n          ,\n          rowClassName: () => \"editable-row\",\n          className: \"search-table custome-table custom-table-border\",\n          bordered: true,\n          loading: loading,\n          dataSource: dataSource,\n          columns: columnsRow.map(col => {\n            if (!col.editable) {\n              return col;\n            }\n            return {\n              ...col,\n              onCell: (record, index) => ({\n                record,\n                editable: col.editable,\n                dataIndex: col.dataIndex,\n                title: col.title,\n                handleSave: handleSave,\n                attrNodeList: attrNodeList,\n                _exprs: _exprs,\n                envList: envList,\n                suggestionList: suggestionList,\n                opreateList: opreateList,\n                queryType: queryType,\n                subAttrNodeList: subAttrNodeList\n              })\n            };\n          }),\n          pagination: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 785,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SearchCodeEditorModal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 830,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 784,\n    columnNumber: 5\n  }, this);\n}\n_s4(FlowSearchEditTable, \"jsuLmcrQ/bVrYndaMjUURjWPGSQ=\");\n_c4 = FlowSearchEditTable;\nexport default _c5 = /*#__PURE__*/forwardRef(FlowSearchEditTable);\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"DragCustomerFormRow\");\n$RefreshReg$(_c2, \"EditableRow\");\n$RefreshReg$(_c3, \"EditableCell\");\n$RefreshReg$(_c4, \"FlowSearchEditTable\");\n$RefreshReg$(_c5, \"%default%\");", "map": {"version": 3, "names": ["DeleteOutlined", "MinusOutlined", "PlusOutlined", "EditOutlined", "FunctionOutlined", "QuestionCircleOutlined", "SearchOutlined", "ArrowUpOutlined", "ArrowDownOutlined", "MenuOutlined", "CRITERIA_RELOP_TYPE_LIST", "eConsoleUiControl", "CRITERIA_RELOP_TYPE", "eDynamicConditionNodeId", "eDynamicCondition", "eQueryType", "eEnTag", "eMoveType", "toolUtil", "<PERSON><PERSON>", "DatePicker", "Form", "Input", "Space", "Select", "Table", "moment", "nanoid", "qs", "React", "forwardRef", "useContext", "useEffect", "useImperativeHandle", "useMemo", "useRef", "useState", "isAllEmptyObjValue", "getRelOpTypeTitle", "ATTRVALUE_LIST", "ATTR_VAR", "ATTR_VALUE", "DATE_FORMAT", "LEFT_BRACKET", "LEFT_BRACKET_STR", "LOGICAL_OP_TYPE", "RELOP_LIST", "REL_OP_TYPE", "RIGHT_BRACKET", "RIGHT_BRACKET_STR", "eDynamicVarType", "eAttrSiteType", "eRelOpType", "getPropValueList", "getOptionsView", "SearchCodeEditorInput", "cloneDeep", "SearchCodeEditorModal", "globalEventBus", "globalUtil", "isEmpty", "handleMoveAction", "DndContext", "restrictToVerticalAxis", "arrayMove", "SortableContext", "useSortable", "verticalListSortingStrategy", "CSS", "eRegionType", "getExprs", "getSubAttrNodeList", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditableContext", "createContext", "DragCustomerFormRow", "children", "props", "_s", "form", "useForm", "attributes", "listeners", "setNodeRef", "setActivatorNodeRef", "transform", "transition", "isDragging", "id", "style", "Transform", "toString", "scaleY", "position", "zIndex", "component", "Provider", "value", "ref", "Children", "map", "child", "key", "record", "isDefault", "render", "row", "index", "type", "icon", "title", "color", "touchAction", "cursor", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "EditableRow", "_s2", "_c2", "EditableCell", "editable", "dataIndex", "handleSave", "attrNodeList", "subAttrNodeList", "_exprs", "envList", "suggestionList", "opreateList", "queryType", "restProps", "_s3", "editing", "setEditing", "inputRef", "Object", "create", "codeInputRef", "nodeList", "setTableFieldsValue", "toggleEdit", "toggleSearchCodeInputEdit", "e", "stopPropagation", "emit", "exprs", "callback", "codeInputSave", "bind", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save", "values", "validateFields", "properType", "find", "item", "nodeId", "dataType", "list", "relopList", "undefined", "newData", "errInfo", "console", "log", "originValue", "current", "setValue", "inputSave", "fieldObjectType", "reduce", "pre", "cur", "curObj", "label", "nodeName", "concat", "getValueInputUI", "childNode", "<PERSON><PERSON>", "margin", "name", "onPressEnter", "onBlur", "className", "onClick", "readOnly", "TextBox", "autoComplete", "placeholder", "target", "ListBox", "dropdownMatchSelectWidth", "bordered", "mode", "_in", "popupClassName", "optionFilterProp", "onChange", "allowClear", "showSearch", "fieldNames", "options", "Date", "format", "objectTypeOptions", "relop", "propType", "propValue", "_c3", "FlowSearchEditTable", "criteriaList", "selectionList", "loading", "_s4", "initDataSource", "setInitDataSource", "dataSource", "setDataSource", "setExprs", "getCriteriaListForUI", "getInitDataSource", "getCriteriaListForBackend", "addSearchCode", "attrNodeListDeps", "_item$list", "_item$relopList", "queryableFlg", "listLength", "length", "relopList<PERSON><PERSON><PERSON>", "criteriaListDeps", "attrNid", "attrValue", "JSON", "stringify", "relOpType", "initData", "_setDataSource", "filter", "Array", "isArray", "join", "error", "warning", "_item$LEFT_BRACKET", "_item$RIGHT_BRACKET", "isDisableDelete", "warn", "relOpTypeTitle", "getColumnsOperate", "text", "bracketCnt", "bracketStr", "add", "columnsRow", "width", "_", "size", "handleDelete", "disabled", "getDefaultRow", "leftBracket", "rightBracket", "logicalOpType", "attrValueList", "initData1", "assembleInitTableData", "addDefaultRow", "_item$REL_OP_TYPE", "_item$LOGICAL_OP_TYPE", "newItem", "getProperType", "guid", "split", "attrVar", "_criteriaList$", "obj", "defaultIndex", "findIndex", "defaultRow", "push", "newDataSource", "replace", "handleAddLogicalOpType", "for<PERSON>ach", "field", "op_place", "op_and", "splice", "_envList", "attrType", "variable", "isNotKpi", "attrNode", "<PERSON><PERSON><PERSON>", "attrDisplayValue", "varValue", "_dynamicCondition", "func", "_suggestionList1", "functionName", "operator", "_operatorList", "addSearchCodeCallback", "newValue", "onDragEnd", "active", "over", "_dataSource", "activeIndex", "i", "overIndex", "_dataSource$", "modifiers", "items", "strategy", "components", "body", "cell", "<PERSON><PERSON><PERSON>", "rowClassName", "columns", "col", "onCell", "pagination", "_c4", "_c5", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/quickAcess/views/Search/SearchDetail/FlowSearchEditTable.jsx"], "sourcesContent": ["/*\r\n * @Author: <PERSON> <EMAIL>\r\n * @Date: 2025-01-23 15:48:46\r\n * @LastEditors: <PERSON> <EMAIL>\r\n * @LastEditTime: 2025-01-24 15:42:30\r\n * @Description: 电子流先决条件\r\n */\r\n/* eslint-disable jsx-a11y/anchor-is-valid */\r\n/* eslint-disable react-hooks/exhaustive-deps */\r\n\r\nimport { DeleteOutlined, MinusOutlined, PlusOutlined, EditOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined, ArrowUpOutlined, ArrowDownOutlined, MenuOutlined } from \"@ant-design/icons\";\r\nimport { CRITERIA_RELOP_TYPE_LIST, eConsoleUiControl, CRITERIA_RELOP_TYPE, eDynamicConditionNodeId, eDynamicCondition, eQueryType, eEnTag,  eMoveType } from \"@common/utils/enum\";\r\nimport * as toolUtil from \"@common/utils/toolUtil\";\r\nimport { But<PERSON>, DatePicker, Form, Input, Space, Select, Table } from \"antd\";\r\nimport moment from \"moment\";\r\nimport { nanoid } from \"nanoid\";\r\nimport * as qs from 'qs';\r\nimport React, { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState, } from \"react\";\r\nimport { isAllEmptyObjValue } from 'src/quickAcess/utils/ArrayUtils';\r\nimport { getRelOpTypeTitle } from 'src/quickAcess/utils/ViewUtils';\r\nimport {\r\n  ATTRVALUE_LIST, ATTR_VAR, ATTR_VALUE, DATE_FORMAT,\r\n  LEFT_BRACKET, LEFT_BRACKET_STR,\r\n  LOGICAL_OP_TYPE, RELOP_LIST, REL_OP_TYPE, RIGHT_BRACKET, RIGHT_BRACKET_STR, eDynamicVarType, eAttrSiteType, eRelOpType,\r\n} from \"src/quickAcess/utils/Config\";\r\nimport { getPropValueList } from '@common/utils/ViewUtils';\r\nimport { getOptionsView } from \"@common/utils/ViewUtils\";\r\nimport SearchCodeEditorInput from \"@components/SearchCodeEditorInput\";\r\nimport cloneDeep from 'lodash/cloneDeep';\r\nimport SearchCodeEditorModal from \"./SearchCodeEditorModal\"\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport \"./SearchEditTable.scss\"\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { isEmpty, handleMoveAction } from \"@common/utils/ArrayUtils\";\r\nimport { DndContext } from '@dnd-kit/core';\r\nimport { restrictToVerticalAxis } from '@dnd-kit/modifiers';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  useSortable,\r\n  verticalListSortingStrategy\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\nimport { eRegionType } from \"src/inspect/utils/enum\";\r\nimport { getExprs, getSubAttrNodeList} from \"@common/utils/logicUtils\"\r\n\r\nconst EditableContext = React.createContext(null);\r\n\r\nconst DragCustomerFormRow = ({ children, ...props }) => {\r\n  const [form] = Form.useForm();\r\n  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({\r\n    id: props['data-row-key'],\r\n  });\r\n  const style = {\r\n    ...props.style,\r\n    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1, }),\r\n    transition,\r\n    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),\r\n  };\r\n  return (\r\n    <Form form={form} component={false}>\r\n      <EditableContext.Provider value={form}>\r\n        <tr {...props} ref={setNodeRef} style={style} {...attributes}>\r\n          {React.Children.map(children, (child) => {\r\n            if (child.key === \"operation\" && !child.props.record.isDefault) { // 操作列，且非默认行\r\n              return {\r\n                ...child,\r\n                props: {\r\n                  ...child.props,\r\n                  render: (value, row, index) => <>\r\n                    {/* {React.cloneElement(<span></span>, {\r\n                      children: (\r\n                        <MenuOutlined title=\"拖拽排序\" ref={setActivatorNodeRef} style={{ color: \"#0077f2\", touchAction: 'none', cursor: 'move' }} {...listeners} />\r\n                      ),\r\n                    })} */}\r\n                     <Button type=\"link\" ref={setActivatorNodeRef} icon={<MenuOutlined title=\"拖拽排序\"  style={{ color: \"#0077f2\", touchAction: 'none', cursor: 'move', fontSize: 12 }}  />} {...listeners}/>\r\n                    {child.props.render && child.props.render(value, row, index)}\r\n                  </>\r\n                }\r\n              }\r\n            }\r\n            return child;\r\n          })}\r\n        </tr>\r\n      </EditableContext.Provider>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst EditableRow = ({ index, ...props }) => {\r\n  const [form] = Form.useForm();\r\n  return (\r\n    <Form form={form} component={false}>\r\n      <EditableContext.Provider value={form}>\r\n        <tr {...props} />\r\n      </EditableContext.Provider>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst EditableCell = ({\r\n  title,\r\n  editable,\r\n  children,\r\n  dataIndex,\r\n  record,\r\n  handleSave,\r\n  attrNodeList,\r\n  subAttrNodeList,\r\n  _exprs,\r\n  envList,\r\n  suggestionList,\r\n  opreateList,\r\n  queryType,\r\n  ...restProps\r\n}) => {\r\n\r\n  const [editing, setEditing] = useState(false);\r\n  const inputRef = useRef(Object.create(null));\r\n  const codeInputRef = useRef(Object.create(null));\r\n  const form = useContext(EditableContext);\r\n\r\n  let nodeList = record?.index == 0 ? attrNodeList : subAttrNodeList; // attrNodeList：第一行的数据源  \r\n\r\n  useEffect(() => {\r\n    if (editing) {\r\n      if (record[ATTR_VAR] == eDynamicConditionNodeId) {\r\n        // TODO: select和focus\r\n      } else {\r\n        // inputRef.current.select();\r\n        // inputRef.current.foucs();\r\n      }\r\n    }\r\n  }, [editing]);\r\n\r\n  // 根据record更新值\r\n  useEffect(() => {\r\n    if (dataIndex) {\r\n      setTableFieldsValue(record, dataIndex);\r\n    }\r\n  }, [record])\r\n\r\n  const toggleEdit = () => {\r\n    setEditing(!editing);\r\n    setTableFieldsValue(record, dataIndex);\r\n  };\r\n\r\n  const toggleSearchCodeInputEdit = (e) => {\r\n    e.stopPropagation();\r\n    globalEventBus.emit(\"openSearchCodeEditorModalEvent\", \"\", { attrNodeList: nodeList, exprs: _exprs, value: record[dataIndex], envList, suggestionList, opreateList, record,  queryType, callback: codeInputSave.bind(this) });\r\n  }\r\n\r\n  // 刷新数据\r\n  const setTableFieldsValue = (data, dataIndex) => {\r\n    form.setFieldsValue({\r\n      [dataIndex]: data[dataIndex],\r\n    });\r\n  }\r\n\r\n  // 保存\r\n  const save = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      if (values[ATTR_VAR] != record[ATTR_VAR]) { // 字段更新时同时也需要更新字段类型\r\n        //  t.clone is not a function\r\n        let properType = nodeList.find(item => item.nodeId === values[ATTR_VAR]);\r\n        if(!!properType){ // 动态条件不在attrNodeList中，TODO:待优化，实际上需要解决的是动态条件的值添加后更新到form中\r\n          const { dataType, list, relopList } = properType\r\n          values.dataType = dataType;     // 字段类型\r\n          values[ATTRVALUE_LIST] = list;  // 值\r\n          values[ATTR_VALUE] = undefined; // 重置值\r\n          values[RELOP_LIST] = relopList; // 连接符\r\n          setTableFieldsValue(values, ATTR_VALUE);\r\n        }\r\n      }\r\n      // if (record.dataType == 'ListBox' && values[REL_OP_TYPE] == '1' && (values[ATTR_VALUE] || []).length > 1) {\r\n      //   values[REL_OP_TYPE] = '17'\r\n      // }\r\n      const newData = { ...record, ...values };\r\n      handleSave(newData); // 合并原有的row和新的values值\r\n      setTableFieldsValue(newData, dataIndex);\r\n    } catch (errInfo) {\r\n      console.log(\"Save failed:\", errInfo);\r\n    }\r\n  };\r\n\r\n  // Input的保存，需要切换UI,与Select和Date类型处理方式不同\r\n  const codeInputSave = async ({ originValue }) => {\r\n    console.log(\"save\", originValue)\r\n    try {\r\n      //如果值没有更改的话，则不触发保存方法\r\n      const newData = { ...record, [dataIndex]: originValue };\r\n      handleSave(newData); // 合并原有的row和新的values值\r\n      codeInputRef.current.setValue(originValue);\r\n    } catch (errInfo) {\r\n      console.log(\"Save failed:\", errInfo);\r\n    }\r\n  };\r\n\r\n  // Input的保存，需要切换UI,与Select和Date类型处理方式不同\r\n  const inputSave = async ({ originValue }) => {\r\n    console.log(\"save\", originValue)\r\n    try {\r\n      //如果值没有更改的话，则不触发保存方法\r\n      handleSave({ ...record, [dataIndex]: originValue }); // 合并原有的row和新的values值\r\n      toggleEdit();\r\n    } catch (errInfo) {\r\n      console.log(\"Save failed:\", errInfo);\r\n    }\r\n  };\r\n\r\n  // 选择任务\r\n  const fieldObjectType = useMemo(() => {\r\n    return (nodeList || []).reduce((pre, cur) => {\r\n      const curObj = {\r\n        key: cur.nodeId,\r\n        value: cur.nodeId,\r\n        label: cur.nodeName,\r\n        dataType: cur.dataType\r\n      };\r\n      return pre.concat(curObj);\r\n    }, []);\r\n  }, [record, nodeList]);\r\n\r\n  // 值渲染的UI\r\n  const getValueInputUI = () => {\r\n    if (record[ATTR_VAR] == eDynamicConditionNodeId) { // 动态条件\r\n      if (editing) {\r\n        childNode = (\r\n          <Form.Item\r\n            style={{\r\n              margin: 0,\r\n            }}\r\n            name={dataIndex}\r\n          >\r\n            <SearchCodeEditorInput\r\n              ref={codeInputRef}\r\n              envList={envList}\r\n              suggestionList={suggestionList}\r\n              onPressEnter={inputSave}\r\n              onBlur={inputSave}\r\n            />\r\n          </Form.Item>\r\n        );\r\n      } else {\r\n        childNode = (\r\n          <div className=\"search-input-editble\" onClick={toggleEdit}>\r\n            <SearchCodeEditorInput\r\n              ref={codeInputRef}\r\n              envList={envList}\r\n              suggestionList={suggestionList}\r\n              readOnly={true}\r\n              value={record[dataIndex]}\r\n            />\r\n            <span className=\"editble-icon\"><EditOutlined onClick={toggleSearchCodeInputEdit} /></span>\r\n          </div>\r\n        );\r\n      }\r\n    } else if (record.dataType === eConsoleUiControl.TextBox) {\r\n      // input类型\r\n      if (editing) {\r\n        childNode = (\r\n          <Form.Item\r\n            style={{\r\n              margin: 0,\r\n            }}\r\n            name={dataIndex}\r\n          >\r\n            <Input\r\n              ref={inputRef}\r\n              autoComplete=\"off\"\r\n              placeholder={placeholder}\r\n              onPressEnter={(e) => { inputSave({ originValue: e.target.value }) }}\r\n              onBlur={(e) => { inputSave({ originValue: e.target.value }) }}\r\n              className=\"search-table-input\"\r\n            />\r\n          </Form.Item>\r\n        );\r\n      } else {\r\n        //非编辑状态，如果没有值，则显示默认值placeholder，需要创建引用变量data，因为children只读，不能更改\r\n        let data = [...children];\r\n        let value = !isEmpty(children[1]) ? children[1] : placeholder;\r\n        data[1] = value;\r\n        let className = !isEmpty(children[1])\r\n          ? \"editable-cell-value-wrap\"\r\n          : \"editable-cell-value-wrap-none\";\r\n        childNode = (\r\n          <div className={className} onClick={toggleEdit}>\r\n            {data}\r\n          </div>\r\n        );\r\n      }\r\n    } else if (record.dataType === eConsoleUiControl.ListBox) {\r\n      childNode = (\r\n        <Form.Item\r\n          style={{\r\n            margin: 0,\r\n          }}\r\n          name={dataIndex}\r\n        >\r\n          <Select\r\n            // open={true} //默认展开，单击即可展开\r\n            // dropdownClassName=\"table-select-option\"\r\n            dropdownMatchSelectWidth={false}\r\n            bordered={false}\r\n            mode={record[REL_OP_TYPE] == eRelOpType._in ? \"multiple\" : undefined} //值可以多选\r\n            className=\"search-table-select\"\r\n            popupClassName=\"search-table-select-pop\"\r\n            optionFilterProp=\"propValue\"\r\n            onBlur={save} //失去焦点回调\r\n            onChange={save} //改变后回调\r\n            allowClear\r\n            showSearch\r\n            fieldNames={{\r\n              label: \"propValue\",\r\n              value: \"propType\"\r\n            }}\r\n            options={record[ATTRVALUE_LIST]}\r\n          >\r\n          </Select>\r\n        </Form.Item>\r\n      );\r\n    } else if (record.dataType === eConsoleUiControl.Date) {\r\n      childNode =\r\n        <Form.Item\r\n          style={{\r\n            margin: 0,\r\n          }}\r\n          name={dataIndex}\r\n        >\r\n          <DatePicker format={DATE_FORMAT} onChange={save} className=\"search-table-datepicker\"/>\r\n        </Form.Item>\r\n    }\r\n    return childNode;\r\n  }\r\n\r\n  let childNode = children;\r\n  let placeholder = \"请输入\" + title;\r\n\r\n  // 下拉列表的值\r\n  let objectTypeOptions;\r\n  switch (dataIndex) {\r\n    case ATTR_VAR:\r\n      // 字段\r\n      // objectTypeSelect = getOptionsView(fieldObjectType);\r\n      objectTypeOptions = fieldObjectType;\r\n      break;\r\n    case LOGICAL_OP_TYPE:\r\n      // 连接符\r\n      // objectTypeSelect = getOptionsView(CRITERIA_RELOP_TYPE_LIST);\r\n      objectTypeOptions = CRITERIA_RELOP_TYPE_LIST;\r\n      break;\r\n    case REL_OP_TYPE:\r\n      // 条件\r\n      // objectTypeSelect = getPropValueList(record[RELOP_LIST])\r\n      objectTypeOptions =  (record[RELOP_LIST] || []).map(relop =>({\r\n        key: relop.propType,\r\n        value: relop.propType,\r\n        label: relop.propValue\r\n      }))\r\n      break;\r\n    default:\r\n      break;\r\n  }\r\n\r\n  if (editable) {\r\n    if (dataIndex == ATTR_VAR || dataIndex == LOGICAL_OP_TYPE || dataIndex == REL_OP_TYPE) {\r\n      if (record[ATTR_VAR] == eDynamicConditionNodeId && dataIndex == REL_OP_TYPE) { // 动态字段没有条件\r\n        childNode = (\r\n          <div className=\"editable-cell-disabled\">\r\n          </div>\r\n        );\r\n      } else if (record[ATTR_VAR] == eDynamicConditionNodeId && dataIndex == ATTR_VAR) { // 动态字段\r\n        childNode = (\r\n          <div>\r\n            动态条件（<FunctionOutlined className=\"color-blue\" style={{margin: 0}}/>）\r\n          </div>\r\n        );\r\n      } else {\r\n        childNode = (\r\n          <Form.Item\r\n            style={{\r\n              margin: 0,\r\n            }}\r\n            name={dataIndex}\r\n          >\r\n            <Select\r\n              // open={true} //默认展开，单击即可展开\r\n              dropdownMatchSelectWidth={false}\r\n              bordered={false}\r\n              className=\"search-table-select\"\r\n              popupClassName=\"search-table-select-pop\"\r\n              // dropdownClassName=\"table-select-option\"\r\n              optionFilterProp=\"label\" \r\n              onBlur={save} //失去焦点回调\r\n              onChange={save} //改变后回调\r\n              allowClear={dataIndex == ATTR_VAR}\r\n              showSearch={dataIndex == ATTR_VAR}\r\n              options={objectTypeOptions}\r\n            >\r\n            </Select>\r\n          </Form.Item>\r\n        );\r\n      }\r\n    } else {\r\n      // 值\r\n      childNode = getValueInputUI();\r\n    }\r\n  }\r\n\r\n  return <td {...restProps}>{childNode}</td>;\r\n};\r\n\r\n/**\r\n * TODO: 编辑器值fontsize-12,color:#333\r\n * 高级搜索-搜索条件表格\r\n *  FIXME: 父组件获取子组件实例关键点:\r\n * 1.useImperativeHandle/forwardRef \r\n * 2.ref不是结构赋值，而是直接取值\r\n * @param {queryType} 是否是Kpi模块 \r\n * @param {*} ref \r\n * @returns \r\n */\r\n// TODO:此组件为了方便kpi和高级搜索，将数据处理逻辑合并到该组件，需要注意数据处理不能影响原数据，因此全部采用map遍历的形式，可能并不是好的思路，需要注意\r\nfunction FlowSearchEditTable({ attrNodeList = [], criteriaList = [], selectionList=[], exprs=[], queryType, loading }, ref,) {\r\n\r\n  const [initDataSource, setInitDataSource] = useState([])//初始化数据\r\n  const [dataSource, setDataSource] = useState([]) //修改后的数据\r\n  const [_exprs, setExprs] = useState([]) // 变量\r\n\r\n  // 可以让你在使用 ref 时自定义暴露给父组件的实例值\r\n  useImperativeHandle(ref, () => ({\r\n    // 获取列表内容:过滤掉默认选项\r\n    getCriteriaListForUI: () => getCriteriaListForUI(dataSource),\r\n    getInitDataSource: () => getCriteriaListForUI(initDataSource),\r\n    getCriteriaListForBackend: () => getCriteriaListForBackend(),\r\n    addSearchCode: () => addSearchCode(),\r\n  }));\r\n\r\n  // 创建安全的依赖项，避免循环引用问题\r\n  const attrNodeListDeps = useMemo(() => {\r\n    return attrNodeList.map(item => ({\r\n      nodeId: item.nodeId,\r\n      nodeName: item.nodeName,\r\n      dataType: item.dataType,\r\n      queryableFlg: item.queryableFlg,\r\n      // 只提取 list 中的关键属性，避免 React 元素\r\n      listLength: item.list?.length || 0,\r\n      // 只提取 relopList 中的关键属性\r\n      relopListLength: item.relopList?.length || 0\r\n    }));\r\n  }, [attrNodeList]);\r\n\r\n  const criteriaListDeps = useMemo(() => {\r\n    return criteriaList.map(item => ({\r\n      id: item.id,\r\n      attrNid: item.attrNid,\r\n      attrValue: typeof item.attrValue === 'object' ? JSON.stringify(item.attrValue) : item.attrValue,\r\n      relOpType: item.relOpType\r\n    }));\r\n  }, [criteriaList]);\r\n\r\n  useEffect(() => {\r\n    if (!isEmpty(attrNodeList)) {\r\n      initData();\r\n    }\r\n  }, [JSON.stringify(attrNodeListDeps), JSON.stringify(criteriaListDeps)]);\r\n\r\n  function _setDataSource (dataSource) {\r\n    setDataSource(cloneDeep(dataSource.map((data, index) => ({...data, index}))));\r\n  }\r\n\r\n  // 获取表格属性\r\n  const getCriteriaListForUI = (data) => {\r\n    try {\r\n      // 注意：该方法会走两遍,不能更改原有数据结构，所以需要使用map，不能使用forEach改变原有数据结构，也不能使用json.parse()\r\n      data = data.filter(item => !item.isDefault);\r\n      return (data || []).map(item => {\r\n        let attrValue = item.attrValue;\r\n        switch (item.dataType) {\r\n          case eConsoleUiControl.ListBox:\r\n            // 多选框，需要将多选数组转化为字符串逗号拼接 \r\n            attrValue = (!isEmpty(item.attrValue) && Array.isArray(item.attrValue)) ? item.attrValue.join(\",\") : item.attrValue;\r\n            break;\r\n          case eConsoleUiControl.Date:\r\n            // 时间需要转为字符串\r\n            attrValue = !isEmpty(item.attrValue) ? item.attrValue.format(DATE_FORMAT) : item.attrValue;\r\n            break;\r\n          default:\r\n            break;\r\n        }\r\n        return { ...item, ...{ attrValue } }\r\n      });\r\n    } catch (error) {\r\n      console.log(\"getCriteriaListForUI\", error);\r\n      globalUtil.warning(\"保存失败\");\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // 获取高级搜索自定义表单接口数据\r\n  const getCriteriaListForBackend = () => {\r\n    try {\r\n      let data = getCriteriaListForUI(dataSource);\r\n      // 获取表格内容\r\n      data = data.map(item => (\r\n        {\r\n          \"leftBracketCnt\": item[LEFT_BRACKET]?.length,\r\n          [ATTR_VAR]: item[ATTR_VAR],\r\n          \"relOpType\": item[REL_OP_TYPE],\r\n          \"attrValue\": item[ATTR_VALUE],\r\n          \"logicalOpType\": item[LOGICAL_OP_TYPE] || \"0\",\r\n          \"rightBracketCnt\": item[RIGHT_BRACKET]?.length,\r\n          \"isDisableDelete\": item.isDisableDelete,\r\n        }\r\n      ))\r\n      return data || [];\r\n    } catch (e) {\r\n      globalUtil.warning(\"保存失败\");\r\n      console.warn(\"高级搜索自定义表单保存失败\", e);\r\n      // message.warn(\"高级搜索自定义表单保存失败\");\r\n      return []\r\n    }\r\n  }\r\n\r\n  const relOpTypeTitle = useMemo(() => (getRelOpTypeTitle(selectionList)), []);\r\n\r\n  const getColumnsOperate = (text, record, index, bracketCnt, bracketStr) => {\r\n    return <div className=\"search-table-opreate\">\r\n      <a\r\n        onClick={() => {\r\n          reduce(record, index, bracketCnt, bracketStr);\r\n        }}\r\n        className=\"minus-btn \"\r\n      ><MinusOutlined /></a>\r\n      <span>{text}</span>\r\n      <a\r\n        onClick={() => {\r\n          add(record, index, bracketCnt, bracketStr);\r\n        }}\r\n        className=\"plus-btn\"\r\n      ><PlusOutlined /></a>\r\n    </div>\r\n  }\r\n\r\n  const columnsRow = [\r\n    {\r\n      title: \"左(\",\r\n      dataIndex: LEFT_BRACKET,\r\n      // colunms的render不会随着数据的刷新而刷新,FIXME:改成function函数式组件后，可以刷新了，说明Class的刷新机制和function不太一致\r\n      render: (text, record, index) => (getColumnsOperate(text, record, index, LEFT_BRACKET, LEFT_BRACKET_STR)),\r\n      width: 60,\r\n    },\r\n    {\r\n      title: \"字段\",\r\n      dataIndex: ATTR_VAR,\r\n      editable: true,\r\n      width: 130,\r\n    },\r\n    {\r\n      title: relOpTypeTitle,\r\n      dataIndex: REL_OP_TYPE,\r\n      editable: true,\r\n      width: 80,\r\n    },\r\n    {\r\n      title: \"值\",\r\n      dataIndex: ATTR_VALUE,\r\n      editable: true,\r\n    },\r\n    {\r\n      title: \"右)\",\r\n      dataIndex: RIGHT_BRACKET,\r\n      render: (text, record, index) => (getColumnsOperate(text, record, index, RIGHT_BRACKET, RIGHT_BRACKET_STR)),\r\n      width: 60,\r\n    },\r\n    {\r\n      title: \"连接符\",\r\n      dataIndex: LOGICAL_OP_TYPE,\r\n      editable: true,\r\n      width: 60,\r\n    },\r\n    {\r\n      title: \"操作\",\r\n      dataIndex: \"operation\",\r\n      width: 100,\r\n      render: (_, record, index) => {\r\n        if (record.isDefault) {\r\n          return <></>\r\n        }\r\n        return (\r\n          <Space size={10}>\r\n            <Button\r\n              type=\"link\"\r\n              icon={<DeleteOutlined />}\r\n              onClick={() => {\r\n                handleDelete(record.key);\r\n              }}\r\n              size=\"small\"\r\n              disabled={record.isDefault || record.isDisableDelete} //默认行、不可删除\r\n              title=\"删除\"\r\n            ></Button>\r\n          </Space>\r\n        )\r\n      }\r\n    },\r\n  ];\r\n\r\n  // 获取默认值\r\n  const getDefaultRow = () => {\r\n    return {\r\n      key: nanoid(),\r\n      leftBracket: \"\",        //左括号(\r\n      [ATTR_VAR]: \"\",            //字段\r\n      relOpType: undefined,   //条件\r\n      attrValue: undefined,   //值\r\n      rightBracket: \"\",       //右括号）\r\n      logicalOpType: \"\",      //连接符\r\n      dataType: eConsoleUiControl.TextBox, //字段类型:默认文本\r\n      attrValueList: [],                    //值Select的List\r\n      isDefault: true,\r\n    };\r\n  };\r\n\r\n  // 初始化数据\r\n  const initData = () => {\r\n    let initData1 = assembleInitTableData([...criteriaList]);\r\n    setInitDataSource([...initData1]); //记录初始化数据\r\n    let data = addDefaultRow([...initData1]);\r\n    _setDataSource(data);\r\n    const _exprs = getExprs(attrNodeList, exprs);\r\n    setExprs(_exprs);\r\n  }\r\n\r\n  // 初始化数据\r\n  const assembleInitTableData = (criteriaList) => {\r\n    try {\r\n      if (isEmpty(criteriaList)) return [];\r\n      // TODO:288个条件直接卡死\r\n      return criteriaList.map((item, index) => {\r\n        let newItem = {}\r\n        let properType = getProperType(item[ATTR_VAR], index, criteriaList);\r\n        const { dataType, list, relopList } = properType;\r\n        newItem.key = toolUtil.guid(); //本地数据没有id,需要guid作为key\r\n        newItem.dataType = dataType;\r\n        newItem[ATTRVALUE_LIST] = list;\r\n        newItem[RELOP_LIST] = relopList; //连接符\r\n        newItem[REL_OP_TYPE] = item[REL_OP_TYPE]?.toString(); // 条件,需要转为String\r\n        newItem[LOGICAL_OP_TYPE] = item[LOGICAL_OP_TYPE]?.toString(); // 连接符,需要转为String\r\n        if (dataType == eConsoleUiControl.ListBox) {\r\n          newItem.attrValue = item.attrValue.split(\",\");  //List\r\n        } else if (dataType == eConsoleUiControl.Date) {\r\n          newItem.attrValue = moment(item.attrValue, DATE_FORMAT) //日期类型格式化\r\n        }\r\n        return { ...item, ...newItem };\r\n      }) || []\r\n    } catch (e) {\r\n      globalUtil.warning('自定义表单数据初始化失败！');\r\n      console.log('自定义表单数据初始化失败！', e);\r\n    }\r\n  }\r\n\r\n  // 根据attrNid获取下拉List\r\n  const getProperType = (attrVar, index, criteriaList) => {\r\n    let nodeList = index == 0 ? attrNodeList :  getSubAttrNodeList(criteriaList[0]?.attrValue, attrNodeList );\r\n    return ([...nodeList, eDynamicCondition]).find(item => item.nodeId === attrVar) || {};\r\n  }\r\n\r\n  //添加默认项\r\n  const addDefaultRow = (obj) => {\r\n    let defaultIndex = obj.findIndex((item) => item.isDefault);\r\n    if (defaultIndex === -1) {\r\n      let defaultRow = getDefaultRow();\r\n      obj.push(defaultRow);\r\n    }\r\n    return obj\r\n  };\r\n\r\n  // 减少左括号/增加右括号\r\n  const reduce = (record, index, bracketCnt, bracketStr) => {\r\n    const newDataSource = [...dataSource];\r\n    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt].replace(bracketStr, \"\");\r\n    _setDataSource(newDataSource);\r\n  };\r\n\r\n  const add = (record, index, bracketCnt, bracketStr) => {\r\n    const newDataSource = [...dataSource];\r\n    newDataSource[index][bracketCnt] = newDataSource[index][bracketCnt] + bracketStr;\r\n    _setDataSource(newDataSource);\r\n  };\r\n\r\n  const handleDelete = (key) => {\r\n    const newDataSource = [...dataSource];\r\n    _setDataSource(newDataSource.filter((item) => item.key !== key));\r\n  };\r\n\r\n  // 当输入新的一行内容后，上一行的“连接符”默认给 “且”，因为这个是用户必选的项，又经常容易忘了设置。\r\n  const handleAddLogicalOpType = (dataSource) => {\r\n    dataSource.forEach((field, index) => {\r\n      if (!field.isDefault && index != dataSource.length - 2 && (field[LOGICAL_OP_TYPE] == CRITERIA_RELOP_TYPE.op_place || isEmpty(field[LOGICAL_OP_TYPE]))) {\r\n        field[LOGICAL_OP_TYPE] = CRITERIA_RELOP_TYPE.op_and\r\n      }\r\n    });\r\n    _setDataSource(dataSource);\r\n  }\r\n\r\n  const handleSave = (row) => {\r\n    // 判断row是否有数据\r\n    let newData = [...dataSource];\r\n    const index = newData.findIndex((item) => row.key === item.key);\r\n    const item = newData[index];\r\n    if(index == 0 && row[ATTR_VALUE] != item[ATTR_VALUE]){ // 任务节点更改后需要清空下面子节点的选择\r\n      newData = [newData[0]];\r\n    }\r\n    if (row.isDefault && !isAllEmptyObjValue(row)) row.isDefault = false; //将默认行且非空则更改为非默认行\r\n    newData.splice(index, 1, { ...item, ...row });\r\n    let data = addDefaultRow([...newData]);\r\n    handleAddLogicalOpType(data);\r\n  };\r\n\r\n  // 变量\r\n  const envList = useMemo(() => {\r\n    // 搜索模块，需要去除kpiTag为isKpi的变量\r\n    let _envList = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.variable && (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) )).map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue,}));\r\n    return [..._envList]\r\n  }, [attrNodeList, _exprs, queryType]);\r\n\r\n  // 函数\r\n  const suggestionList = useMemo(() => {\r\n    const _dynamicCondition = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.func &&  (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) ));\r\n    let _suggestionList1 = _dynamicCondition.map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue, functionName: attrNode.functionName }));\r\n    return [..._suggestionList1,]\r\n  }, [attrNodeList, _exprs, queryType]);\r\n\r\n  // 操作符\r\n  const opreateList = useMemo(() => {\r\n    const _dynamicCondition = (_exprs || []).filter(item => (item.attrType == eDynamicVarType.operator &&  (item.queryType == eQueryType.isNotKpi || item.queryType == queryType) ));\r\n    let _operatorList = _dynamicCondition.map(attrNode => ({ varKey: attrNode.attrDisplayValue, varValue: attrNode.attrValue, functionName: attrNode.functionName }));\r\n    return [..._operatorList,]\r\n  }, [attrNodeList, _exprs, queryType]);\r\n\r\n  // 添加动态条件\r\n  const addSearchCode = () => {\r\n    globalEventBus.emit(\"openSearchCodeEditorModalEvent\", \"\", { attrNodeList, exprs: _exprs, value: \"\", envList, suggestionList, opreateList, queryType, callback: addSearchCodeCallback });\r\n  }\r\n\r\n  // 添加动态条件回调\r\n  const addSearchCodeCallback = ({ originValue }) => {\r\n    const newData = [...dataSource];\r\n    const newValue = {\r\n      key: nanoid(),\r\n      leftBracket: \"\",                  //左括号(\r\n      [ATTR_VAR]: eDynamicConditionNodeId, //字段\r\n      relOpType: undefined,             //条件\r\n      attrValue: originValue,           //值\r\n      rightBracket: \"\",                 //右括号）\r\n      logicalOpType: \"\",                //连接符\r\n      dataType: eConsoleUiControl.TextBox, //字段类型:默认文本\r\n      attrValueList: [],                    //值Select的List\r\n      isDefault: false,\r\n    };\r\n    // 数组倒数第二位插入\r\n    newData.splice(newData.length - 1, 0, newValue);\r\n    handleAddLogicalOpType(newData); // 添加动态条件也需要检查连接符\r\n  }\r\n\r\n  // 拖拽排序\r\n  const onDragEnd = ({ active, over }) => {\r\n    if ((active?.id && over?.id) && (active?.id !== over?.id)) {\r\n      let _dataSource = [...dataSource];\r\n      const activeIndex = _dataSource.findIndex((i) => i.key == active?.id);\r\n      const overIndex = _dataSource.findIndex((i) => i.key == over?.id);\r\n      _dataSource = arrayMove(_dataSource, activeIndex, overIndex);\r\n      _setDataSource(_dataSource);\r\n    }\r\n  };\r\n\r\n  // 任务下的属性\r\n  const subAttrNodeList = useMemo(() => {\r\n    return getSubAttrNodeList(dataSource[0]?.[ATTR_VALUE], attrNodeList)\r\n  }, [dataSource, attrNodeList]);\r\n\r\n  return (\r\n    <div>\r\n      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>\r\n        <SortableContext\r\n          items={dataSource.map((i) => i.key)} // 需要全部数据的key,否则无法拖拽\r\n          strategy={verticalListSortingStrategy}\r\n        >\r\n        <Table\r\n          components={{\r\n            body: {\r\n              row: DragCustomerFormRow,\r\n              cell: EditableCell,\r\n            },\r\n          }}\r\n          rowKey=\"key\" // 注意不能是key,也不能不填\r\n          rowClassName={() => \"editable-row\"}\r\n          className=\"search-table custome-table custom-table-border\"\r\n          bordered\r\n          loading={loading}\r\n          dataSource={dataSource}\r\n          columns={columnsRow.map((col) => {\r\n            if (!col.editable) {\r\n              return col;\r\n            }\r\n\r\n            return {\r\n              ...col,\r\n              onCell: (record, index) => ({\r\n                record,\r\n                editable: col.editable,\r\n                dataIndex: col.dataIndex,\r\n                title: col.title,\r\n                handleSave: handleSave,\r\n                attrNodeList: attrNodeList,\r\n                _exprs: _exprs,\r\n                envList: envList,\r\n                suggestionList: suggestionList,\r\n                opreateList: opreateList,\r\n                queryType: queryType,\r\n                subAttrNodeList: subAttrNodeList,\r\n              }),\r\n            };\r\n          })}\r\n          pagination={false}\r\n        />\r\n        </SortableContext>\r\n      </DndContext>\r\n      <SearchCodeEditorModal />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default forwardRef(FlowSearchEditTable);\r\n"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,cAAc,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,mBAAmB;AACzM,SAASC,wBAAwB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,MAAM,EAAGC,SAAS,QAAQ,oBAAoB;AACjL,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,SAASC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAC5E,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAO,KAAKC,EAAE,MAAM,IAAI;AACxB,OAAOC,KAAK,IAAIC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAS,OAAO;AACjH,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SACEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EACjDC,YAAY,EAAEC,gBAAgB,EAC9BC,eAAe,EAAEC,UAAU,EAAEC,WAAW,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,UAAU,QACjH,6BAA6B;AACpC,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAOC,qBAAqB,MAAM,mCAAmC;AACrE,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAO,wBAAwB;AAC/B,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,OAAO,EAAEC,gBAAgB,QAAQ,0BAA0B;AACpE,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,sBAAsB,QAAQ,oBAAoB;AAC3D,SACEC,SAAS,EACTC,eAAe,EACfC,WAAW,EACXC,2BAA2B,QACtB,mBAAmB;AAC1B,SAASC,GAAG,QAAQ,oBAAoB;AACxC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,QAAQ,EAAEC,kBAAkB,QAAO,0BAA0B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtE,MAAMC,eAAe,gBAAG/C,KAAK,CAACgD,aAAa,CAAC,IAAI,CAAC;AAEjD,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAE,GAAGC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM,CAACC,IAAI,CAAC,GAAG7D,IAAI,CAAC8D,OAAO,CAAC,CAAC;EAC7B,MAAM;IAAEC,UAAU;IAAEC,SAAS;IAAEC,UAAU;IAAEC,mBAAmB;IAAEC,SAAS;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGxB,WAAW,CAAC;IAChHyB,EAAE,EAAEX,KAAK,CAAC,cAAc;EAC1B,CAAC,CAAC;EACF,MAAMY,KAAK,GAAG;IACZ,GAAGZ,KAAK,CAACY,KAAK;IACdJ,SAAS,EAAEpB,GAAG,CAACyB,SAAS,CAACC,QAAQ,CAACN,SAAS,IAAI;MAAE,GAAGA,SAAS;MAAEO,MAAM,EAAE;IAAG,CAAC,CAAC;IAC5EN,UAAU;IACV,IAAIC,UAAU,GAAG;MAAEM,QAAQ,EAAE,UAAU;MAAEC,MAAM,EAAE;IAAK,CAAC,GAAG,CAAC,CAAC;EAC9D,CAAC;EACD,oBACExB,OAAA,CAACpD,IAAI;IAAC6D,IAAI,EAAEA,IAAK;IAACgB,SAAS,EAAE,KAAM;IAAAnB,QAAA,eACjCN,OAAA,CAACG,eAAe,CAACuB,QAAQ;MAACC,KAAK,EAAElB,IAAK;MAAAH,QAAA,eACpCN,OAAA;QAAA,GAAQO,KAAK;QAAEqB,GAAG,EAAEf,UAAW;QAACM,KAAK,EAAEA,KAAM;QAAA,GAAKR,UAAU;QAAAL,QAAA,EACzDlD,KAAK,CAACyE,QAAQ,CAACC,GAAG,CAACxB,QAAQ,EAAGyB,KAAK,IAAK;UACvC,IAAIA,KAAK,CAACC,GAAG,KAAK,WAAW,IAAI,CAACD,KAAK,CAACxB,KAAK,CAAC0B,MAAM,CAACC,SAAS,EAAE;YAAE;YAChE,OAAO;cACL,GAAGH,KAAK;cACRxB,KAAK,EAAE;gBACL,GAAGwB,KAAK,CAACxB,KAAK;gBACd4B,MAAM,EAAEA,CAACR,KAAK,EAAES,GAAG,EAAEC,KAAK,kBAAKrC,OAAA,CAAAE,SAAA;kBAAAI,QAAA,gBAM5BN,OAAA,CAACtD,MAAM;oBAAC4F,IAAI,EAAC,MAAM;oBAACV,GAAG,EAAEd,mBAAoB;oBAACyB,IAAI,eAAEvC,OAAA,CAAChE,YAAY;sBAACwG,KAAK,EAAC,0BAAM;sBAAErB,KAAK,EAAE;wBAAEsB,KAAK,EAAE,SAAS;wBAAEC,WAAW,EAAE,MAAM;wBAAEC,MAAM,EAAE,MAAM;wBAAEC,QAAQ,EAAE;sBAAG;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAE;oBAAA,GAAKpC;kBAAS;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACrLjB,KAAK,CAACxB,KAAK,CAAC4B,MAAM,IAAIJ,KAAK,CAACxB,KAAK,CAAC4B,MAAM,CAACR,KAAK,EAAES,GAAG,EAAEC,KAAK,CAAC;gBAAA,eAC5D;cACJ;YACF,CAAC;UACH;UACA,OAAON,KAAK;QACd,CAAC;MAAC;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACmB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;AAEX,CAAC;AAACxC,EAAA,CAvCIH,mBAAmB;EAAA,QACRzD,IAAI,CAAC8D,OAAO,EAC2EjB,WAAW;AAAA;AAAAwD,EAAA,GAF7G5C,mBAAmB;AAyCzB,MAAM6C,WAAW,GAAGA,CAAC;EAAEb,KAAK;EAAE,GAAG9B;AAAM,CAAC,KAAK;EAAA4C,GAAA;EAC3C,MAAM,CAAC1C,IAAI,CAAC,GAAG7D,IAAI,CAAC8D,OAAO,CAAC,CAAC;EAC7B,oBACEV,OAAA,CAACpD,IAAI;IAAC6D,IAAI,EAAEA,IAAK;IAACgB,SAAS,EAAE,KAAM;IAAAnB,QAAA,eACjCN,OAAA,CAACG,eAAe,CAACuB,QAAQ;MAACC,KAAK,EAAElB,IAAK;MAAAH,QAAA,eACpCN,OAAA;QAAA,GAAQO;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;AAEX,CAAC;AAACG,GAAA,CATID,WAAW;EAAA,QACAtG,IAAI,CAAC8D,OAAO;AAAA;AAAA0C,GAAA,GADvBF,WAAW;AAWjB,MAAMG,YAAY,GAAGA,CAAC;EACpBb,KAAK;EACLc,QAAQ;EACRhD,QAAQ;EACRiD,SAAS;EACTtB,MAAM;EACNuB,UAAU;EACVC,YAAY;EACZC,eAAe;EACfC,MAAM;EACNC,OAAO;EACPC,cAAc;EACdC,WAAW;EACXC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EAAAC,GAAA;EAEJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMyG,QAAQ,GAAG1G,MAAM,CAAC2G,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EAC5C,MAAMC,YAAY,GAAG7G,MAAM,CAAC2G,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EAChD,MAAM7D,IAAI,GAAGnD,UAAU,CAAC6C,eAAe,CAAC;EAExC,IAAIqE,QAAQ,GAAG,CAAAvC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,KAAK,KAAI,CAAC,GAAGoB,YAAY,GAAGC,eAAe,CAAC,CAAC;;EAEpEnG,SAAS,CAAC,MAAM;IACd,IAAI2G,OAAO,EAAE;MACX,IAAIjC,MAAM,CAAClE,QAAQ,CAAC,IAAI3B,uBAAuB,EAAE;QAC/C;MAAA,CACD,MAAM;QACL;QACA;MAAA;IAEJ;EACF,CAAC,EAAE,CAAC8H,OAAO,CAAC,CAAC;;EAEb;EACA3G,SAAS,CAAC,MAAM;IACd,IAAIgG,SAAS,EAAE;MACbkB,mBAAmB,CAACxC,MAAM,EAAEsB,SAAS,CAAC;IACxC;EACF,CAAC,EAAE,CAACtB,MAAM,CAAC,CAAC;EAEZ,MAAMyC,UAAU,GAAGA,CAAA,KAAM;IACvBP,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBO,mBAAmB,CAACxC,MAAM,EAAEsB,SAAS,CAAC;EACxC,CAAC;EAED,MAAMoB,yBAAyB,GAAIC,CAAC,IAAK;IACvCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB5F,cAAc,CAAC6F,IAAI,CAAC,gCAAgC,EAAE,EAAE,EAAE;MAAErB,YAAY,EAAEe,QAAQ;MAAEO,KAAK,EAAEpB,MAAM;MAAEhC,KAAK,EAAEM,MAAM,CAACsB,SAAS,CAAC;MAAEK,OAAO;MAAEC,cAAc;MAAEC,WAAW;MAAE7B,MAAM;MAAG8B,SAAS;MAAEiB,QAAQ,EAAEC,aAAa,CAACC,IAAI,CAAC,IAAI;IAAE,CAAC,CAAC;EAC9N,CAAC;;EAED;EACA,MAAMT,mBAAmB,GAAGA,CAACU,IAAI,EAAE5B,SAAS,KAAK;IAC/C9C,IAAI,CAAC2E,cAAc,CAAC;MAClB,CAAC7B,SAAS,GAAG4B,IAAI,CAAC5B,SAAS;IAC7B,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8B,IAAI,GAAG,MAAAA,CAAA,KAAY;IACvB,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM7E,IAAI,CAAC8E,cAAc,CAAC,CAAC;MAC1C,IAAID,MAAM,CAACvH,QAAQ,CAAC,IAAIkE,MAAM,CAAClE,QAAQ,CAAC,EAAE;QAAE;QAC1C;QACA,IAAIyH,UAAU,GAAGhB,QAAQ,CAACiB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKL,MAAM,CAACvH,QAAQ,CAAC,CAAC;QACxE,IAAG,CAAC,CAACyH,UAAU,EAAC;UAAE;UAChB,MAAM;YAAEI,QAAQ;YAAEC,IAAI;YAAEC;UAAU,CAAC,GAAGN,UAAU;UAChDF,MAAM,CAACM,QAAQ,GAAGA,QAAQ,CAAC,CAAK;UAChCN,MAAM,CAACxH,cAAc,CAAC,GAAG+H,IAAI,CAAC,CAAE;UAChCP,MAAM,CAACtH,UAAU,CAAC,GAAG+H,SAAS,CAAC,CAAC;UAChCT,MAAM,CAACjH,UAAU,CAAC,GAAGyH,SAAS,CAAC,CAAC;UAChCrB,mBAAmB,CAACa,MAAM,EAAEtH,UAAU,CAAC;QACzC;MACF;MACA;MACA;MACA;MACA,MAAMgI,OAAO,GAAG;QAAE,GAAG/D,MAAM;QAAE,GAAGqD;MAAO,CAAC;MACxC9B,UAAU,CAACwC,OAAO,CAAC,CAAC,CAAC;MACrBvB,mBAAmB,CAACuB,OAAO,EAAEzC,SAAS,CAAC;IACzC,CAAC,CAAC,OAAO0C,OAAO,EAAE;MAChBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMhB,aAAa,GAAG,MAAAA,CAAO;IAAEmB;EAAY,CAAC,KAAK;IAC/CF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEC,WAAW,CAAC;IAChC,IAAI;MACF;MACA,MAAMJ,OAAO,GAAG;QAAE,GAAG/D,MAAM;QAAE,CAACsB,SAAS,GAAG6C;MAAY,CAAC;MACvD5C,UAAU,CAACwC,OAAO,CAAC,CAAC,CAAC;MACrBzB,YAAY,CAAC8B,OAAO,CAACC,QAAQ,CAACF,WAAW,CAAC;IAC5C,CAAC,CAAC,OAAOH,OAAO,EAAE;MAChBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMM,SAAS,GAAG,MAAAA,CAAO;IAAEH;EAAY,CAAC,KAAK;IAC3CF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEC,WAAW,CAAC;IAChC,IAAI;MACF;MACA5C,UAAU,CAAC;QAAE,GAAGvB,MAAM;QAAE,CAACsB,SAAS,GAAG6C;MAAY,CAAC,CAAC,CAAC,CAAC;MACrD1B,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOuB,OAAO,EAAE;MAChBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMO,eAAe,GAAG/I,OAAO,CAAC,MAAM;IACpC,OAAO,CAAC+G,QAAQ,IAAI,EAAE,EAAEiC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAC3C,MAAMC,MAAM,GAAG;QACb5E,GAAG,EAAE2E,GAAG,CAAChB,MAAM;QACfhE,KAAK,EAAEgF,GAAG,CAAChB,MAAM;QACjBkB,KAAK,EAAEF,GAAG,CAACG,QAAQ;QACnBlB,QAAQ,EAAEe,GAAG,CAACf;MAChB,CAAC;MACD,OAAOc,GAAG,CAACK,MAAM,CAACH,MAAM,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,EAAE,CAAC3E,MAAM,EAAEuC,QAAQ,CAAC,CAAC;;EAEtB;EACA,MAAMwC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI/E,MAAM,CAAClE,QAAQ,CAAC,IAAI3B,uBAAuB,EAAE;MAAE;MACjD,IAAI8H,OAAO,EAAE;QACX+C,SAAS,gBACPjH,OAAA,CAACpD,IAAI,CAACsK,IAAI;UACR/F,KAAK,EAAE;YACLgG,MAAM,EAAE;UACV,CAAE;UACFC,IAAI,EAAE7D,SAAU;UAAAjD,QAAA,eAEhBN,OAAA,CAAClB,qBAAqB;YACpB8C,GAAG,EAAE2C,YAAa;YAClBX,OAAO,EAAEA,OAAQ;YACjBC,cAAc,EAAEA,cAAe;YAC/BwD,YAAY,EAAEd,SAAU;YACxBe,MAAM,EAAEf;UAAU;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MACH,CAAC,MAAM;QACLiE,SAAS,gBACPjH,OAAA;UAAKuH,SAAS,EAAC,sBAAsB;UAACC,OAAO,EAAE9C,UAAW;UAAApE,QAAA,gBACxDN,OAAA,CAAClB,qBAAqB;YACpB8C,GAAG,EAAE2C,YAAa;YAClBX,OAAO,EAAEA,OAAQ;YACjBC,cAAc,EAAEA,cAAe;YAC/B4D,QAAQ,EAAE,IAAK;YACf9F,KAAK,EAAEM,MAAM,CAACsB,SAAS;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACFhD,OAAA;YAAMuH,SAAS,EAAC,cAAc;YAAAjH,QAAA,eAACN,OAAA,CAACtE,YAAY;cAAC8L,OAAO,EAAE7C;YAA0B;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CACN;MACH;IACF,CAAC,MAAM,IAAIf,MAAM,CAAC2D,QAAQ,KAAK1J,iBAAiB,CAACwL,OAAO,EAAE;MACxD;MACA,IAAIxD,OAAO,EAAE;QACX+C,SAAS,gBACPjH,OAAA,CAACpD,IAAI,CAACsK,IAAI;UACR/F,KAAK,EAAE;YACLgG,MAAM,EAAE;UACV,CAAE;UACFC,IAAI,EAAE7D,SAAU;UAAAjD,QAAA,eAEhBN,OAAA,CAACnD,KAAK;YACJ+E,GAAG,EAAEwC,QAAS;YACduD,YAAY,EAAC,KAAK;YAClBC,WAAW,EAAEA,WAAY;YACzBP,YAAY,EAAGzC,CAAC,IAAK;cAAE2B,SAAS,CAAC;gBAAEH,WAAW,EAAExB,CAAC,CAACiD,MAAM,CAAClG;cAAM,CAAC,CAAC;YAAC,CAAE;YACpE2F,MAAM,EAAG1C,CAAC,IAAK;cAAE2B,SAAS,CAAC;gBAAEH,WAAW,EAAExB,CAAC,CAACiD,MAAM,CAAClG;cAAM,CAAC,CAAC;YAAC,CAAE;YAC9D4F,SAAS,EAAC;UAAoB;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MACH,CAAC,MAAM;QACL;QACA,IAAImC,IAAI,GAAG,CAAC,GAAG7E,QAAQ,CAAC;QACxB,IAAIqB,KAAK,GAAG,CAACxC,OAAO,CAACmB,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGsH,WAAW;QAC7DzC,IAAI,CAAC,CAAC,CAAC,GAAGxD,KAAK;QACf,IAAI4F,SAAS,GAAG,CAACpI,OAAO,CAACmB,QAAQ,CAAC,CAAC,CAAC,CAAC,GACjC,0BAA0B,GAC1B,+BAA+B;QACnC2G,SAAS,gBACPjH,OAAA;UAAKuH,SAAS,EAAEA,SAAU;UAACC,OAAO,EAAE9C,UAAW;UAAApE,QAAA,EAC5C6E;QAAI;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MACH;IACF,CAAC,MAAM,IAAIf,MAAM,CAAC2D,QAAQ,KAAK1J,iBAAiB,CAAC4L,OAAO,EAAE;MACxDb,SAAS,gBACPjH,OAAA,CAACpD,IAAI,CAACsK,IAAI;QACR/F,KAAK,EAAE;UACLgG,MAAM,EAAE;QACV,CAAE;QACFC,IAAI,EAAE7D,SAAU;QAAAjD,QAAA,eAEhBN,OAAA,CAACjD;QACC;QACA;QAAA;UACAgL,wBAAwB,EAAE,KAAM;UAChCC,QAAQ,EAAE,KAAM;UAChBC,IAAI,EAAEhG,MAAM,CAAC3D,WAAW,CAAC,IAAIK,UAAU,CAACuJ,GAAG,GAAG,UAAU,GAAGnC,SAAU,CAAC;UAAA;UACtEwB,SAAS,EAAC,qBAAqB;UAC/BY,cAAc,EAAC,yBAAyB;UACxCC,gBAAgB,EAAC,WAAW;UAC5Bd,MAAM,EAAEjC,IAAK,CAAC;UAAA;UACdgD,QAAQ,EAAEhD,IAAK,CAAC;UAAA;UAChBiD,UAAU;UACVC,UAAU;UACVC,UAAU,EAAE;YACV3B,KAAK,EAAE,WAAW;YAClBlF,KAAK,EAAE;UACT,CAAE;UACF8G,OAAO,EAAExG,MAAM,CAACnE,cAAc;QAAE;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACZ;IACH,CAAC,MAAM,IAAIf,MAAM,CAAC2D,QAAQ,KAAK1J,iBAAiB,CAACwM,IAAI,EAAE;MACrDzB,SAAS,gBACPjH,OAAA,CAACpD,IAAI,CAACsK,IAAI;QACR/F,KAAK,EAAE;UACLgG,MAAM,EAAE;QACV,CAAE;QACFC,IAAI,EAAE7D,SAAU;QAAAjD,QAAA,eAEhBN,OAAA,CAACrD,UAAU;UAACgM,MAAM,EAAE1K,WAAY;UAACoK,QAAQ,EAAEhD,IAAK;UAACkC,SAAS,EAAC;QAAyB;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC;IAChB;IACA,OAAOiE,SAAS;EAClB,CAAC;EAED,IAAIA,SAAS,GAAG3G,QAAQ;EACxB,IAAIsH,WAAW,GAAG,KAAK,GAAGpF,KAAK;;EAE/B;EACA,IAAIoG,iBAAiB;EACrB,QAAQrF,SAAS;IACf,KAAKxF,QAAQ;MACX;MACA;MACA6K,iBAAiB,GAAGpC,eAAe;MACnC;IACF,KAAKpI,eAAe;MAClB;MACA;MACAwK,iBAAiB,GAAG3M,wBAAwB;MAC5C;IACF,KAAKqC,WAAW;MACd;MACA;MACAsK,iBAAiB,GAAI,CAAC3G,MAAM,CAAC5D,UAAU,CAAC,IAAI,EAAE,EAAEyD,GAAG,CAAC+G,KAAK,KAAI;QAC3D7G,GAAG,EAAE6G,KAAK,CAACC,QAAQ;QACnBnH,KAAK,EAAEkH,KAAK,CAACC,QAAQ;QACrBjC,KAAK,EAAEgC,KAAK,CAACE;MACf,CAAC,CAAC,CAAC;MACH;IACF;MACE;EACJ;EAEA,IAAIzF,QAAQ,EAAE;IACZ,IAAIC,SAAS,IAAIxF,QAAQ,IAAIwF,SAAS,IAAInF,eAAe,IAAImF,SAAS,IAAIjF,WAAW,EAAE;MACrF,IAAI2D,MAAM,CAAClE,QAAQ,CAAC,IAAI3B,uBAAuB,IAAImH,SAAS,IAAIjF,WAAW,EAAE;QAAE;QAC7E2I,SAAS,gBACPjH,OAAA;UAAKuH,SAAS,EAAC;QAAwB;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACN;MACH,CAAC,MAAM,IAAIf,MAAM,CAAClE,QAAQ,CAAC,IAAI3B,uBAAuB,IAAImH,SAAS,IAAIxF,QAAQ,EAAE;QAAE;QACjFkJ,SAAS,gBACPjH,OAAA;UAAAM,QAAA,GAAK,gCACE,eAAAN,OAAA,CAACrE,gBAAgB;YAAC4L,SAAS,EAAC,YAAY;YAACpG,KAAK,EAAE;cAACgG,MAAM,EAAE;YAAC;UAAE;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,UACrE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MACH,CAAC,MAAM;QACLiE,SAAS,gBACPjH,OAAA,CAACpD,IAAI,CAACsK,IAAI;UACR/F,KAAK,EAAE;YACLgG,MAAM,EAAE;UACV,CAAE;UACFC,IAAI,EAAE7D,SAAU;UAAAjD,QAAA,eAEhBN,OAAA,CAACjD;UACC;UAAA;YACAgL,wBAAwB,EAAE,KAAM;YAChCC,QAAQ,EAAE,KAAM;YAChBT,SAAS,EAAC,qBAAqB;YAC/BY,cAAc,EAAC;YACf;YAAA;YACAC,gBAAgB,EAAC,OAAO;YACxBd,MAAM,EAAEjC,IAAK,CAAC;YAAA;YACdgD,QAAQ,EAAEhD,IAAK,CAAC;YAAA;YAChBiD,UAAU,EAAE/E,SAAS,IAAIxF,QAAS;YAClCwK,UAAU,EAAEhF,SAAS,IAAIxF,QAAS;YAClC0K,OAAO,EAAEG;UAAkB;YAAA/F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACZ;MACH;IACF,CAAC,MAAM;MACL;MACAiE,SAAS,GAAGD,eAAe,CAAC,CAAC;IAC/B;EACF;EAEA,oBAAOhH,OAAA;IAAA,GAAQgE,SAAS;IAAA1D,QAAA,EAAG2G;EAAS;IAAApE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAAiB,GAAA,CAnUMZ,YAAY;AAAA2F,GAAA,GAAZ3F,YAAY;AAoUlB,SAAS4F,mBAAmBA,CAAC;EAAExF,YAAY,GAAG,EAAE;EAAEyF,YAAY,GAAG,EAAE;EAAEC,aAAa,GAAC,EAAE;EAAEpE,KAAK,GAAC,EAAE;EAAEhB,SAAS;EAAEqF;AAAQ,CAAC,EAAExH,GAAG,EAAG;EAAAyH,GAAA;EAE3H,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG5L,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6L,UAAU,EAAEC,aAAa,CAAC,GAAG9L,QAAQ,CAAC,EAAE,CAAC,EAAC;EACjD,MAAM,CAACgG,MAAM,EAAE+F,QAAQ,CAAC,GAAG/L,QAAQ,CAAC,EAAE,CAAC,EAAC;;EAExC;EACAH,mBAAmB,CAACoE,GAAG,EAAE,OAAO;IAC9B;IACA+H,oBAAoB,EAAEA,CAAA,KAAMA,oBAAoB,CAACH,UAAU,CAAC;IAC5DI,iBAAiB,EAAEA,CAAA,KAAMD,oBAAoB,CAACL,cAAc,CAAC;IAC7DO,yBAAyB,EAAEA,CAAA,KAAMA,yBAAyB,CAAC,CAAC;IAC5DC,aAAa,EAAEA,CAAA,KAAMA,aAAa,CAAC;EACrC,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMC,gBAAgB,GAAGtM,OAAO,CAAC,MAAM;IACrC,OAAOgG,YAAY,CAAC3B,GAAG,CAAC4D,IAAI;MAAA,IAAAsE,UAAA,EAAAC,eAAA;MAAA,OAAK;QAC/BtE,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBmB,QAAQ,EAAEpB,IAAI,CAACoB,QAAQ;QACvBlB,QAAQ,EAAEF,IAAI,CAACE,QAAQ;QACvBsE,YAAY,EAAExE,IAAI,CAACwE,YAAY;QAC/B;QACAC,UAAU,EAAE,EAAAH,UAAA,GAAAtE,IAAI,CAACG,IAAI,cAAAmE,UAAA,uBAATA,UAAA,CAAWI,MAAM,KAAI,CAAC;QAClC;QACAC,eAAe,EAAE,EAAAJ,eAAA,GAAAvE,IAAI,CAACI,SAAS,cAAAmE,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,KAAI;MAC7C,CAAC;IAAA,CAAC,CAAC;EACL,CAAC,EAAE,CAAC3G,YAAY,CAAC,CAAC;EAElB,MAAM6G,gBAAgB,GAAG7M,OAAO,CAAC,MAAM;IACrC,OAAOyL,YAAY,CAACpH,GAAG,CAAC4D,IAAI,KAAK;MAC/BxE,EAAE,EAAEwE,IAAI,CAACxE,EAAE;MACXqJ,OAAO,EAAE7E,IAAI,CAAC6E,OAAO;MACrBC,SAAS,EAAE,OAAO9E,IAAI,CAAC8E,SAAS,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAAChF,IAAI,CAAC8E,SAAS,CAAC,GAAG9E,IAAI,CAAC8E,SAAS;MAC/FG,SAAS,EAAEjF,IAAI,CAACiF;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACzB,YAAY,CAAC,CAAC;EAElB3L,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4B,OAAO,CAACsE,YAAY,CAAC,EAAE;MAC1BmH,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACH,IAAI,CAACC,SAAS,CAACX,gBAAgB,CAAC,EAAEU,IAAI,CAACC,SAAS,CAACJ,gBAAgB,CAAC,CAAC,CAAC;EAExE,SAASO,cAAcA,CAAErB,UAAU,EAAE;IACnCC,aAAa,CAAC1K,SAAS,CAACyK,UAAU,CAAC1H,GAAG,CAAC,CAACqD,IAAI,EAAE9C,KAAK,MAAM;MAAC,GAAG8C,IAAI;MAAE9C;IAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/E;;EAEA;EACA,MAAMsH,oBAAoB,GAAIxE,IAAI,IAAK;IACrC,IAAI;MACF;MACAA,IAAI,GAAGA,IAAI,CAAC2F,MAAM,CAACpF,IAAI,IAAI,CAACA,IAAI,CAACxD,SAAS,CAAC;MAC3C,OAAO,CAACiD,IAAI,IAAI,EAAE,EAAErD,GAAG,CAAC4D,IAAI,IAAI;QAC9B,IAAI8E,SAAS,GAAG9E,IAAI,CAAC8E,SAAS;QAC9B,QAAQ9E,IAAI,CAACE,QAAQ;UACnB,KAAK1J,iBAAiB,CAAC4L,OAAO;YAC5B;YACA0C,SAAS,GAAI,CAACrL,OAAO,CAACuG,IAAI,CAAC8E,SAAS,CAAC,IAAIO,KAAK,CAACC,OAAO,CAACtF,IAAI,CAAC8E,SAAS,CAAC,GAAI9E,IAAI,CAAC8E,SAAS,CAACS,IAAI,CAAC,GAAG,CAAC,GAAGvF,IAAI,CAAC8E,SAAS;YACnH;UACF,KAAKtO,iBAAiB,CAACwM,IAAI;YACzB;YACA8B,SAAS,GAAG,CAACrL,OAAO,CAACuG,IAAI,CAAC8E,SAAS,CAAC,GAAG9E,IAAI,CAAC8E,SAAS,CAAC7B,MAAM,CAAC1K,WAAW,CAAC,GAAGyH,IAAI,CAAC8E,SAAS;YAC1F;UACF;YACE;QACJ;QACA,OAAO;UAAE,GAAG9E,IAAI;UAAE,GAAG;YAAE8E;UAAU;QAAE,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdhF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+E,KAAK,CAAC;MAC1ChM,UAAU,CAACiM,OAAO,CAAC,MAAM,CAAC;MAC1B,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMtB,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI;MACF,IAAI1E,IAAI,GAAGwE,oBAAoB,CAACH,UAAU,CAAC;MAC3C;MACArE,IAAI,GAAGA,IAAI,CAACrD,GAAG,CAAC4D,IAAI;QAAA,IAAA0F,kBAAA,EAAAC,mBAAA;QAAA,OAClB;UACE,gBAAgB,GAAAD,kBAAA,GAAE1F,IAAI,CAACxH,YAAY,CAAC,cAAAkN,kBAAA,uBAAlBA,kBAAA,CAAoBhB,MAAM;UAC5C,CAACrM,QAAQ,GAAG2H,IAAI,CAAC3H,QAAQ,CAAC;UAC1B,WAAW,EAAE2H,IAAI,CAACpH,WAAW,CAAC;UAC9B,WAAW,EAAEoH,IAAI,CAAC1H,UAAU,CAAC;UAC7B,eAAe,EAAE0H,IAAI,CAACtH,eAAe,CAAC,IAAI,GAAG;UAC7C,iBAAiB,GAAAiN,mBAAA,GAAE3F,IAAI,CAACnH,aAAa,CAAC,cAAA8M,mBAAA,uBAAnBA,mBAAA,CAAqBjB,MAAM;UAC9C,iBAAiB,EAAE1E,IAAI,CAAC4F;QAC1B,CAAC;MAAA,CACF,CAAC;MACF,OAAOnG,IAAI,IAAI,EAAE;IACnB,CAAC,CAAC,OAAOP,CAAC,EAAE;MACV1F,UAAU,CAACiM,OAAO,CAAC,MAAM,CAAC;MAC1BjF,OAAO,CAACqF,IAAI,CAAC,eAAe,EAAE3G,CAAC,CAAC;MAChC;MACA,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAM4G,cAAc,GAAG/N,OAAO,CAAC,MAAOI,iBAAiB,CAACsL,aAAa,CAAE,EAAE,EAAE,CAAC;EAE5E,MAAMsC,iBAAiB,GAAGA,CAACC,IAAI,EAAEzJ,MAAM,EAAEI,KAAK,EAAEsJ,UAAU,EAAEC,UAAU,KAAK;IACzE,oBAAO5L,OAAA;MAAKuH,SAAS,EAAC,sBAAsB;MAAAjH,QAAA,gBAC1CN,OAAA;QACEwH,OAAO,EAAEA,CAAA,KAAM;UACbf,MAAM,CAACxE,MAAM,EAAEI,KAAK,EAAEsJ,UAAU,EAAEC,UAAU,CAAC;QAC/C,CAAE;QACFrE,SAAS,EAAC,YAAY;QAAAjH,QAAA,eACvBN,OAAA,CAACxE,aAAa;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACtBhD,OAAA;QAAAM,QAAA,EAAOoL;MAAI;QAAA7I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnBhD,OAAA;QACEwH,OAAO,EAAEA,CAAA,KAAM;UACbqE,GAAG,CAAC5J,MAAM,EAAEI,KAAK,EAAEsJ,UAAU,EAAEC,UAAU,CAAC;QAC5C,CAAE;QACFrE,SAAS,EAAC,UAAU;QAAAjH,QAAA,eACrBN,OAAA,CAACvE,YAAY;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EACR,CAAC;EAED,MAAM8I,UAAU,GAAG,CACjB;IACEtJ,KAAK,EAAE,IAAI;IACXe,SAAS,EAAErF,YAAY;IACvB;IACAiE,MAAM,EAAEA,CAACuJ,IAAI,EAAEzJ,MAAM,EAAEI,KAAK,KAAMoJ,iBAAiB,CAACC,IAAI,EAAEzJ,MAAM,EAAEI,KAAK,EAAEnE,YAAY,EAAEC,gBAAgB,CAAE;IACzG4N,KAAK,EAAE;EACT,CAAC,EACD;IACEvJ,KAAK,EAAE,IAAI;IACXe,SAAS,EAAExF,QAAQ;IACnBuF,QAAQ,EAAE,IAAI;IACdyI,KAAK,EAAE;EACT,CAAC,EACD;IACEvJ,KAAK,EAAEgJ,cAAc;IACrBjI,SAAS,EAAEjF,WAAW;IACtBgF,QAAQ,EAAE,IAAI;IACdyI,KAAK,EAAE;EACT,CAAC,EACD;IACEvJ,KAAK,EAAE,GAAG;IACVe,SAAS,EAAEvF,UAAU;IACrBsF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXe,SAAS,EAAEhF,aAAa;IACxB4D,MAAM,EAAEA,CAACuJ,IAAI,EAAEzJ,MAAM,EAAEI,KAAK,KAAMoJ,iBAAiB,CAACC,IAAI,EAAEzJ,MAAM,EAAEI,KAAK,EAAE9D,aAAa,EAAEC,iBAAiB,CAAE;IAC3GuN,KAAK,EAAE;EACT,CAAC,EACD;IACEvJ,KAAK,EAAE,KAAK;IACZe,SAAS,EAAEnF,eAAe;IAC1BkF,QAAQ,EAAE,IAAI;IACdyI,KAAK,EAAE;EACT,CAAC,EACD;IACEvJ,KAAK,EAAE,IAAI;IACXe,SAAS,EAAE,WAAW;IACtBwI,KAAK,EAAE,GAAG;IACV5J,MAAM,EAAEA,CAAC6J,CAAC,EAAE/J,MAAM,EAAEI,KAAK,KAAK;MAC5B,IAAIJ,MAAM,CAACC,SAAS,EAAE;QACpB,oBAAOlC,OAAA,CAAAE,SAAA,mBAAI,CAAC;MACd;MACA,oBACEF,OAAA,CAAClD,KAAK;QAACmP,IAAI,EAAE,EAAG;QAAA3L,QAAA,eACdN,OAAA,CAACtD,MAAM;UACL4F,IAAI,EAAC,MAAM;UACXC,IAAI,eAAEvC,OAAA,CAACzE,cAAc;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBwE,OAAO,EAAEA,CAAA,KAAM;YACb0E,YAAY,CAACjK,MAAM,CAACD,GAAG,CAAC;UAC1B,CAAE;UACFiK,IAAI,EAAC,OAAO;UACZE,QAAQ,EAAElK,MAAM,CAACC,SAAS,IAAID,MAAM,CAACqJ,eAAgB,CAAC;UAAA;UACtD9I,KAAK,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEZ;EACF,CAAC,CACF;;EAED;EACA,MAAMoJ,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO;MACLpK,GAAG,EAAE9E,MAAM,CAAC,CAAC;MACbmP,WAAW,EAAE,EAAE;MAAS;MACxB,CAACtO,QAAQ,GAAG,EAAE;MAAa;MAC3B4M,SAAS,EAAE5E,SAAS;MAAI;MACxByE,SAAS,EAAEzE,SAAS;MAAI;MACxBuG,YAAY,EAAE,EAAE;MAAQ;MACxBC,aAAa,EAAE,EAAE;MAAO;MACxB3G,QAAQ,EAAE1J,iBAAiB,CAACwL,OAAO;MAAE;MACrC8E,aAAa,EAAE,EAAE;MAAqB;MACtCtK,SAAS,EAAE;IACb,CAAC;EACH,CAAC;;EAED;EACA,MAAM0I,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI6B,SAAS,GAAGC,qBAAqB,CAAC,CAAC,GAAGxD,YAAY,CAAC,CAAC;IACxDK,iBAAiB,CAAC,CAAC,GAAGkD,SAAS,CAAC,CAAC,CAAC,CAAC;IACnC,IAAItH,IAAI,GAAGwH,aAAa,CAAC,CAAC,GAAGF,SAAS,CAAC,CAAC;IACxC5B,cAAc,CAAC1F,IAAI,CAAC;IACpB,MAAMxB,MAAM,GAAG9D,QAAQ,CAAC4D,YAAY,EAAEsB,KAAK,CAAC;IAC5C2E,QAAQ,CAAC/F,MAAM,CAAC;EAClB,CAAC;;EAED;EACA,MAAM+I,qBAAqB,GAAIxD,YAAY,IAAK;IAC9C,IAAI;MACF,IAAI/J,OAAO,CAAC+J,YAAY,CAAC,EAAE,OAAO,EAAE;MACpC;MACA,OAAOA,YAAY,CAACpH,GAAG,CAAC,CAAC4D,IAAI,EAAErD,KAAK,KAAK;QAAA,IAAAuK,iBAAA,EAAAC,qBAAA;QACvC,IAAIC,OAAO,GAAG,CAAC,CAAC;QAChB,IAAItH,UAAU,GAAGuH,aAAa,CAACrH,IAAI,CAAC3H,QAAQ,CAAC,EAAEsE,KAAK,EAAE6G,YAAY,CAAC;QACnE,MAAM;UAAEtD,QAAQ;UAAEC,IAAI;UAAEC;QAAU,CAAC,GAAGN,UAAU;QAChDsH,OAAO,CAAC9K,GAAG,GAAGvF,QAAQ,CAACuQ,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/BF,OAAO,CAAClH,QAAQ,GAAGA,QAAQ;QAC3BkH,OAAO,CAAChP,cAAc,CAAC,GAAG+H,IAAI;QAC9BiH,OAAO,CAACzO,UAAU,CAAC,GAAGyH,SAAS,CAAC,CAAC;QACjCgH,OAAO,CAACxO,WAAW,CAAC,IAAAsO,iBAAA,GAAGlH,IAAI,CAACpH,WAAW,CAAC,cAAAsO,iBAAA,uBAAjBA,iBAAA,CAAmBvL,QAAQ,CAAC,CAAC,CAAC,CAAC;QACtDyL,OAAO,CAAC1O,eAAe,CAAC,IAAAyO,qBAAA,GAAGnH,IAAI,CAACtH,eAAe,CAAC,cAAAyO,qBAAA,uBAArBA,qBAAA,CAAuBxL,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAIuE,QAAQ,IAAI1J,iBAAiB,CAAC4L,OAAO,EAAE;UACzCgF,OAAO,CAACtC,SAAS,GAAG9E,IAAI,CAAC8E,SAAS,CAACyC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAE;QAClD,CAAC,MAAM,IAAIrH,QAAQ,IAAI1J,iBAAiB,CAACwM,IAAI,EAAE;UAC7CoE,OAAO,CAACtC,SAAS,GAAGvN,MAAM,CAACyI,IAAI,CAAC8E,SAAS,EAAEvM,WAAW,CAAC,EAAC;QAC1D;QACA,OAAO;UAAE,GAAGyH,IAAI;UAAE,GAAGoH;QAAQ,CAAC;MAChC,CAAC,CAAC,IAAI,EAAE;IACV,CAAC,CAAC,OAAOlI,CAAC,EAAE;MACV1F,UAAU,CAACiM,OAAO,CAAC,eAAe,CAAC;MACnCjF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEvB,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMmI,aAAa,GAAGA,CAACG,OAAO,EAAE7K,KAAK,EAAE6G,YAAY,KAAK;IAAA,IAAAiE,cAAA;IACtD,IAAI3I,QAAQ,GAAGnC,KAAK,IAAI,CAAC,GAAGoB,YAAY,GAAI3D,kBAAkB,EAAAqN,cAAA,GAACjE,YAAY,CAAC,CAAC,CAAC,cAAAiE,cAAA,uBAAfA,cAAA,CAAiB3C,SAAS,EAAE/G,YAAa,CAAC;IACzG,OAAQ,CAAC,GAAGe,QAAQ,EAAEnI,iBAAiB,CAAC,CAAEoJ,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKuH,OAAO,CAAC,IAAI,CAAC,CAAC;EACvF,CAAC;;EAED;EACA,MAAMP,aAAa,GAAIS,GAAG,IAAK;IAC7B,IAAIC,YAAY,GAAGD,GAAG,CAACE,SAAS,CAAE5H,IAAI,IAAKA,IAAI,CAACxD,SAAS,CAAC;IAC1D,IAAImL,YAAY,KAAK,CAAC,CAAC,EAAE;MACvB,IAAIE,UAAU,GAAGnB,aAAa,CAAC,CAAC;MAChCgB,GAAG,CAACI,IAAI,CAACD,UAAU,CAAC;IACtB;IACA,OAAOH,GAAG;EACZ,CAAC;;EAED;EACA,MAAM3G,MAAM,GAAGA,CAACxE,MAAM,EAAEI,KAAK,EAAEsJ,UAAU,EAAEC,UAAU,KAAK;IACxD,MAAM6B,aAAa,GAAG,CAAC,GAAGjE,UAAU,CAAC;IACrCiE,aAAa,CAACpL,KAAK,CAAC,CAACsJ,UAAU,CAAC,GAAG8B,aAAa,CAACpL,KAAK,CAAC,CAACsJ,UAAU,CAAC,CAAC+B,OAAO,CAAC9B,UAAU,EAAE,EAAE,CAAC;IAC3Ff,cAAc,CAAC4C,aAAa,CAAC;EAC/B,CAAC;EAED,MAAM5B,GAAG,GAAGA,CAAC5J,MAAM,EAAEI,KAAK,EAAEsJ,UAAU,EAAEC,UAAU,KAAK;IACrD,MAAM6B,aAAa,GAAG,CAAC,GAAGjE,UAAU,CAAC;IACrCiE,aAAa,CAACpL,KAAK,CAAC,CAACsJ,UAAU,CAAC,GAAG8B,aAAa,CAACpL,KAAK,CAAC,CAACsJ,UAAU,CAAC,GAAGC,UAAU;IAChFf,cAAc,CAAC4C,aAAa,CAAC;EAC/B,CAAC;EAED,MAAMvB,YAAY,GAAIlK,GAAG,IAAK;IAC5B,MAAMyL,aAAa,GAAG,CAAC,GAAGjE,UAAU,CAAC;IACrCqB,cAAc,CAAC4C,aAAa,CAAC3C,MAAM,CAAEpF,IAAI,IAAKA,IAAI,CAAC1D,GAAG,KAAKA,GAAG,CAAC,CAAC;EAClE,CAAC;;EAED;EACA,MAAM2L,sBAAsB,GAAInE,UAAU,IAAK;IAC7CA,UAAU,CAACoE,OAAO,CAAC,CAACC,KAAK,EAAExL,KAAK,KAAK;MACnC,IAAI,CAACwL,KAAK,CAAC3L,SAAS,IAAIG,KAAK,IAAImH,UAAU,CAACY,MAAM,GAAG,CAAC,KAAKyD,KAAK,CAACzP,eAAe,CAAC,IAAIjC,mBAAmB,CAAC2R,QAAQ,IAAI3O,OAAO,CAAC0O,KAAK,CAACzP,eAAe,CAAC,CAAC,CAAC,EAAE;QACrJyP,KAAK,CAACzP,eAAe,CAAC,GAAGjC,mBAAmB,CAAC4R,MAAM;MACrD;IACF,CAAC,CAAC;IACFlD,cAAc,CAACrB,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMhG,UAAU,GAAIpB,GAAG,IAAK;IAC1B;IACA,IAAI4D,OAAO,GAAG,CAAC,GAAGwD,UAAU,CAAC;IAC7B,MAAMnH,KAAK,GAAG2D,OAAO,CAACsH,SAAS,CAAE5H,IAAI,IAAKtD,GAAG,CAACJ,GAAG,KAAK0D,IAAI,CAAC1D,GAAG,CAAC;IAC/D,MAAM0D,IAAI,GAAGM,OAAO,CAAC3D,KAAK,CAAC;IAC3B,IAAGA,KAAK,IAAI,CAAC,IAAID,GAAG,CAACpE,UAAU,CAAC,IAAI0H,IAAI,CAAC1H,UAAU,CAAC,EAAC;MAAE;MACrDgI,OAAO,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,CAAC;IACxB;IACA,IAAI5D,GAAG,CAACF,SAAS,IAAI,CAACtE,kBAAkB,CAACwE,GAAG,CAAC,EAAEA,GAAG,CAACF,SAAS,GAAG,KAAK,CAAC,CAAC;IACtE8D,OAAO,CAACgI,MAAM,CAAC3L,KAAK,EAAE,CAAC,EAAE;MAAE,GAAGqD,IAAI;MAAE,GAAGtD;IAAI,CAAC,CAAC;IAC7C,IAAI+C,IAAI,GAAGwH,aAAa,CAAC,CAAC,GAAG3G,OAAO,CAAC,CAAC;IACtC2H,sBAAsB,CAACxI,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMvB,OAAO,GAAGnG,OAAO,CAAC,MAAM;IAC5B;IACA,IAAIwQ,QAAQ,GAAG,CAACtK,MAAM,IAAI,EAAE,EAAEmH,MAAM,CAACpF,IAAI,IAAKA,IAAI,CAACwI,QAAQ,IAAIzP,eAAe,CAAC0P,QAAQ,KAAKzI,IAAI,CAAC3B,SAAS,IAAIzH,UAAU,CAAC8R,QAAQ,IAAI1I,IAAI,CAAC3B,SAAS,IAAIA,SAAS,CAAG,CAAC,CAACjC,GAAG,CAACuM,QAAQ,KAAK;MAAEC,MAAM,EAAED,QAAQ,CAACE,gBAAgB;MAAEC,QAAQ,EAAEH,QAAQ,CAAC7D;IAAU,CAAC,CAAC,CAAC;IAC3P,OAAO,CAAC,GAAGyD,QAAQ,CAAC;EACtB,CAAC,EAAE,CAACxK,YAAY,EAAEE,MAAM,EAAEI,SAAS,CAAC,CAAC;;EAErC;EACA,MAAMF,cAAc,GAAGpG,OAAO,CAAC,MAAM;IACnC,MAAMgR,iBAAiB,GAAG,CAAC9K,MAAM,IAAI,EAAE,EAAEmH,MAAM,CAACpF,IAAI,IAAKA,IAAI,CAACwI,QAAQ,IAAIzP,eAAe,CAACiQ,IAAI,KAAMhJ,IAAI,CAAC3B,SAAS,IAAIzH,UAAU,CAAC8R,QAAQ,IAAI1I,IAAI,CAAC3B,SAAS,IAAIA,SAAS,CAAG,CAAC;IAC5K,IAAI4K,gBAAgB,GAAGF,iBAAiB,CAAC3M,GAAG,CAACuM,QAAQ,KAAK;MAAEC,MAAM,EAAED,QAAQ,CAACE,gBAAgB;MAAEC,QAAQ,EAAEH,QAAQ,CAAC7D,SAAS;MAAEoE,YAAY,EAAEP,QAAQ,CAACO;IAAa,CAAC,CAAC,CAAC;IACpK,OAAO,CAAC,GAAGD,gBAAgB,CAAE;EAC/B,CAAC,EAAE,CAAClL,YAAY,EAAEE,MAAM,EAAEI,SAAS,CAAC,CAAC;;EAErC;EACA,MAAMD,WAAW,GAAGrG,OAAO,CAAC,MAAM;IAChC,MAAMgR,iBAAiB,GAAG,CAAC9K,MAAM,IAAI,EAAE,EAAEmH,MAAM,CAACpF,IAAI,IAAKA,IAAI,CAACwI,QAAQ,IAAIzP,eAAe,CAACoQ,QAAQ,KAAMnJ,IAAI,CAAC3B,SAAS,IAAIzH,UAAU,CAAC8R,QAAQ,IAAI1I,IAAI,CAAC3B,SAAS,IAAIA,SAAS,CAAG,CAAC;IAChL,IAAI+K,aAAa,GAAGL,iBAAiB,CAAC3M,GAAG,CAACuM,QAAQ,KAAK;MAAEC,MAAM,EAAED,QAAQ,CAACE,gBAAgB;MAAEC,QAAQ,EAAEH,QAAQ,CAAC7D,SAAS;MAAEoE,YAAY,EAAEP,QAAQ,CAACO;IAAa,CAAC,CAAC,CAAC;IACjK,OAAO,CAAC,GAAGE,aAAa,CAAE;EAC5B,CAAC,EAAE,CAACrL,YAAY,EAAEE,MAAM,EAAEI,SAAS,CAAC,CAAC;;EAErC;EACA,MAAM+F,aAAa,GAAGA,CAAA,KAAM;IAC1B7K,cAAc,CAAC6F,IAAI,CAAC,gCAAgC,EAAE,EAAE,EAAE;MAAErB,YAAY;MAAEsB,KAAK,EAAEpB,MAAM;MAAEhC,KAAK,EAAE,EAAE;MAAEiC,OAAO;MAAEC,cAAc;MAAEC,WAAW;MAAEC,SAAS;MAAEiB,QAAQ,EAAE+J;IAAsB,CAAC,CAAC;EACzL,CAAC;;EAED;EACA,MAAMA,qBAAqB,GAAGA,CAAC;IAAE3I;EAAY,CAAC,KAAK;IACjD,MAAMJ,OAAO,GAAG,CAAC,GAAGwD,UAAU,CAAC;IAC/B,MAAMwF,QAAQ,GAAG;MACfhN,GAAG,EAAE9E,MAAM,CAAC,CAAC;MACbmP,WAAW,EAAE,EAAE;MAAmB;MAClC,CAACtO,QAAQ,GAAG3B,uBAAuB;MAAE;MACrCuO,SAAS,EAAE5E,SAAS;MAAc;MAClCyE,SAAS,EAAEpE,WAAW;MAAY;MAClCkG,YAAY,EAAE,EAAE;MAAkB;MAClCC,aAAa,EAAE,EAAE;MAAiB;MAClC3G,QAAQ,EAAE1J,iBAAiB,CAACwL,OAAO;MAAE;MACrC8E,aAAa,EAAE,EAAE;MAAqB;MACtCtK,SAAS,EAAE;IACb,CAAC;IACD;IACA8D,OAAO,CAACgI,MAAM,CAAChI,OAAO,CAACoE,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE4E,QAAQ,CAAC;IAC/CrB,sBAAsB,CAAC3H,OAAO,CAAC,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMiJ,SAAS,GAAGA,CAAC;IAAEC,MAAM;IAAEC;EAAK,CAAC,KAAK;IACtC,IAAKD,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEhO,EAAE,IAAIiO,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEjO,EAAE,IAAM,CAAAgO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhO,EAAE,OAAKiO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjO,EAAE,CAAC,EAAE;MACzD,IAAIkO,WAAW,GAAG,CAAC,GAAG5F,UAAU,CAAC;MACjC,MAAM6F,WAAW,GAAGD,WAAW,CAAC9B,SAAS,CAAEgC,CAAC,IAAKA,CAAC,CAACtN,GAAG,KAAIkN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhO,EAAE,EAAC;MACrE,MAAMqO,SAAS,GAAGH,WAAW,CAAC9B,SAAS,CAAEgC,CAAC,IAAKA,CAAC,CAACtN,GAAG,KAAImN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjO,EAAE,EAAC;MACjEkO,WAAW,GAAG7P,SAAS,CAAC6P,WAAW,EAAEC,WAAW,EAAEE,SAAS,CAAC;MAC5D1E,cAAc,CAACuE,WAAW,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAM1L,eAAe,GAAGjG,OAAO,CAAC,MAAM;IAAA,IAAA+R,YAAA;IACpC,OAAO1P,kBAAkB,EAAA0P,YAAA,GAAChG,UAAU,CAAC,CAAC,CAAC,cAAAgG,YAAA,uBAAbA,YAAA,CAAgBxR,UAAU,CAAC,EAAEyF,YAAY,CAAC;EACtE,CAAC,EAAE,CAAC+F,UAAU,EAAE/F,YAAY,CAAC,CAAC;EAE9B,oBACEzD,OAAA;IAAAM,QAAA,gBACEN,OAAA,CAACX,UAAU;MAACoQ,SAAS,EAAE,CAACnQ,sBAAsB,CAAE;MAAC2P,SAAS,EAAEA,SAAU;MAAA3O,QAAA,eACpEN,OAAA,CAACR,eAAe;QACdkQ,KAAK,EAAElG,UAAU,CAAC1H,GAAG,CAAEwN,CAAC,IAAKA,CAAC,CAACtN,GAAG,CAAE,CAAC;QAAA;QACrC2N,QAAQ,EAAEjQ,2BAA4B;QAAAY,QAAA,eAExCN,OAAA,CAAChD,KAAK;UACJ4S,UAAU,EAAE;YACVC,IAAI,EAAE;cACJzN,GAAG,EAAE/B,mBAAmB;cACxByP,IAAI,EAAEzM;YACR;UACF,CAAE;UACF0M,MAAM,EAAC,KAAK,CAAC;UAAA;UACbC,YAAY,EAAEA,CAAA,KAAM,cAAe;UACnCzI,SAAS,EAAC,gDAAgD;UAC1DS,QAAQ;UACRoB,OAAO,EAAEA,OAAQ;UACjBI,UAAU,EAAEA,UAAW;UACvByG,OAAO,EAAEnE,UAAU,CAAChK,GAAG,CAAEoO,GAAG,IAAK;YAC/B,IAAI,CAACA,GAAG,CAAC5M,QAAQ,EAAE;cACjB,OAAO4M,GAAG;YACZ;YAEA,OAAO;cACL,GAAGA,GAAG;cACNC,MAAM,EAAEA,CAAClO,MAAM,EAAEI,KAAK,MAAM;gBAC1BJ,MAAM;gBACNqB,QAAQ,EAAE4M,GAAG,CAAC5M,QAAQ;gBACtBC,SAAS,EAAE2M,GAAG,CAAC3M,SAAS;gBACxBf,KAAK,EAAE0N,GAAG,CAAC1N,KAAK;gBAChBgB,UAAU,EAAEA,UAAU;gBACtBC,YAAY,EAAEA,YAAY;gBAC1BE,MAAM,EAAEA,MAAM;gBACdC,OAAO,EAAEA,OAAO;gBAChBC,cAAc,EAAEA,cAAc;gBAC9BC,WAAW,EAAEA,WAAW;gBACxBC,SAAS,EAAEA,SAAS;gBACpBL,eAAe,EAAEA;cACnB,CAAC;YACH,CAAC;UACH,CAAC,CAAE;UACH0M,UAAU,EAAE;QAAM;UAAAvN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACe;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACbhD,OAAA,CAAChB,qBAAqB;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEV;AAACqG,GAAA,CAxZQJ,mBAAmB;AAAAoH,GAAA,GAAnBpH,mBAAmB;AA0Z5B,eAAAqH,GAAA,gBAAejT,UAAU,CAAC4L,mBAAmB,CAAC;AAAC,IAAAhG,EAAA,EAAAG,GAAA,EAAA4F,GAAA,EAAAqH,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAtN,EAAA;AAAAsN,YAAA,CAAAnN,GAAA;AAAAmN,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}