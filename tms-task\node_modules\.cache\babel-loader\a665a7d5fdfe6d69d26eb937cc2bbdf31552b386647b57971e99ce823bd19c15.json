{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { once } from './functional.js';\nimport { Iterable } from './iterator.js';\n/**\n * Enables logging of potentially leaked disposables.\n *\n * A disposable is considered leaked if it is not disposed or not registered as the child of\n * another disposable. This tracking is very simple an only works for classes that either\n * extend Disposable or use a DisposableStore. This means there are a lot of false positives.\n */\nconst TRACK_DISPOSABLES = false;\nlet disposableTracker = null;\nexport function setDisposableTracker(tracker) {\n  disposableTracker = tracker;\n}\nif (TRACK_DISPOSABLES) {\n  const __is_disposable_tracked__ = '__is_disposable_tracked__';\n  setDisposableTracker(new class {\n    trackDisposable(x) {\n      const stack = new Error('Potentially leaked disposable').stack;\n      setTimeout(() => {\n        if (!x[__is_disposable_tracked__]) {\n          console.log(stack);\n        }\n      }, 3000);\n    }\n    setParent(child, parent) {\n      if (child && child !== Disposable.None) {\n        try {\n          child[__is_disposable_tracked__] = true;\n        } catch (_a) {\n          // noop\n        }\n      }\n    }\n    markAsDisposed(disposable) {\n      if (disposable && disposable !== Disposable.None) {\n        try {\n          disposable[__is_disposable_tracked__] = true;\n        } catch (_a) {\n          // noop\n        }\n      }\n    }\n    markAsSingleton(disposable) {}\n  }());\n}\nfunction trackDisposable(x) {\n  disposableTracker === null || disposableTracker === void 0 ? void 0 : disposableTracker.trackDisposable(x);\n  return x;\n}\nfunction markAsDisposed(disposable) {\n  disposableTracker === null || disposableTracker === void 0 ? void 0 : disposableTracker.markAsDisposed(disposable);\n}\nfunction setParentOfDisposable(child, parent) {\n  disposableTracker === null || disposableTracker === void 0 ? void 0 : disposableTracker.setParent(child, parent);\n}\nfunction setParentOfDisposables(children, parent) {\n  if (!disposableTracker) {\n    return;\n  }\n  for (const child of children) {\n    disposableTracker.setParent(child, parent);\n  }\n}\n/**\n * Indicates that the given object is a singleton which does not need to be disposed.\n*/\nexport function markAsSingleton(singleton) {\n  disposableTracker === null || disposableTracker === void 0 ? void 0 : disposableTracker.markAsSingleton(singleton);\n  return singleton;\n}\nexport class MultiDisposeError extends Error {\n  constructor(errors) {\n    super(`Encountered errors while disposing of store. Errors: [${errors.join(', ')}]`);\n    this.errors = errors;\n  }\n}\nexport function isDisposable(thing) {\n  return typeof thing.dispose === 'function' && thing.dispose.length === 0;\n}\nexport function dispose(arg) {\n  if (Iterable.is(arg)) {\n    const errors = [];\n    for (const d of arg) {\n      if (d) {\n        try {\n          d.dispose();\n        } catch (e) {\n          errors.push(e);\n        }\n      }\n    }\n    if (errors.length === 1) {\n      throw errors[0];\n    } else if (errors.length > 1) {\n      throw new MultiDisposeError(errors);\n    }\n    return Array.isArray(arg) ? [] : arg;\n  } else if (arg) {\n    arg.dispose();\n    return arg;\n  }\n}\nexport function combinedDisposable() {\n  for (var _len = arguments.length, disposables = new Array(_len), _key = 0; _key < _len; _key++) {\n    disposables[_key] = arguments[_key];\n  }\n  const parent = toDisposable(() => dispose(disposables));\n  setParentOfDisposables(disposables, parent);\n  return parent;\n}\nexport function toDisposable(fn) {\n  const self = trackDisposable({\n    dispose: once(() => {\n      markAsDisposed(self);\n      fn();\n    })\n  });\n  return self;\n}\nexport class DisposableStore {\n  constructor() {\n    this._toDispose = new Set();\n    this._isDisposed = false;\n    trackDisposable(this);\n  }\n  /**\n   * Dispose of all registered disposables and mark this object as disposed.\n   *\n   * Any future disposables added to this object will be disposed of on `add`.\n   */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    markAsDisposed(this);\n    this._isDisposed = true;\n    this.clear();\n  }\n  /**\n   * Returns `true` if this object has been disposed\n   */\n  get isDisposed() {\n    return this._isDisposed;\n  }\n  /**\n   * Dispose of all registered disposables but do not mark this object as disposed.\n   */\n  clear() {\n    try {\n      dispose(this._toDispose.values());\n    } finally {\n      this._toDispose.clear();\n    }\n  }\n  add(o) {\n    if (!o) {\n      return o;\n    }\n    if (o === this) {\n      throw new Error('Cannot register a disposable on itself!');\n    }\n    setParentOfDisposable(o, this);\n    if (this._isDisposed) {\n      if (!DisposableStore.DISABLE_DISPOSED_WARNING) {\n        console.warn(new Error('Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!').stack);\n      }\n    } else {\n      this._toDispose.add(o);\n    }\n    return o;\n  }\n}\nDisposableStore.DISABLE_DISPOSED_WARNING = false;\nexport class Disposable {\n  constructor() {\n    this._store = new DisposableStore();\n    trackDisposable(this);\n    setParentOfDisposable(this._store, this);\n  }\n  dispose() {\n    markAsDisposed(this);\n    this._store.dispose();\n  }\n  _register(o) {\n    if (o === this) {\n      throw new Error('Cannot register a disposable on itself!');\n    }\n    return this._store.add(o);\n  }\n}\nDisposable.None = Object.freeze({\n  dispose() {}\n});\n/**\n * Manages the lifecycle of a disposable value that may be changed.\n *\n * This ensures that when the disposable value is changed, the previously held disposable is disposed of. You can\n * also register a `MutableDisposable` on a `Disposable` to ensure it is automatically cleaned up.\n */\nexport class MutableDisposable {\n  constructor() {\n    this._isDisposed = false;\n    trackDisposable(this);\n  }\n  get value() {\n    return this._isDisposed ? undefined : this._value;\n  }\n  set value(value) {\n    var _a;\n    if (this._isDisposed || value === this._value) {\n      return;\n    }\n    (_a = this._value) === null || _a === void 0 ? void 0 : _a.dispose();\n    if (value) {\n      setParentOfDisposable(value, this);\n    }\n    this._value = value;\n  }\n  clear() {\n    this.value = undefined;\n  }\n  dispose() {\n    var _a;\n    this._isDisposed = true;\n    markAsDisposed(this);\n    (_a = this._value) === null || _a === void 0 ? void 0 : _a.dispose();\n    this._value = undefined;\n  }\n  /**\n   * Clears the value, but does not dispose it.\n   * The old value is returned.\n  */\n  clearAndLeak() {\n    const oldValue = this._value;\n    this._value = undefined;\n    if (oldValue) {\n      setParentOfDisposable(oldValue, null);\n    }\n    return oldValue;\n  }\n}\nexport class RefCountedDisposable {\n  constructor(_disposable) {\n    this._disposable = _disposable;\n    this._counter = 1;\n  }\n  acquire() {\n    this._counter++;\n    return this;\n  }\n  release() {\n    if (--this._counter === 0) {\n      this._disposable.dispose();\n    }\n    return this;\n  }\n}\n/**\n * A safe disposable can be `unset` so that a leaked reference (listener)\n * can be cut-off.\n */\nexport class SafeDisposable {\n  constructor() {\n    this.dispose = () => {};\n    this.unset = () => {};\n    this.isset = () => false;\n    trackDisposable(this);\n  }\n  set(fn) {\n    let callback = fn;\n    this.unset = () => callback = undefined;\n    this.isset = () => callback !== undefined;\n    this.dispose = () => {\n      if (callback) {\n        callback();\n        callback = undefined;\n        markAsDisposed(this);\n      }\n    };\n    return this;\n  }\n}\nexport class ImmortalReference {\n  constructor(object) {\n    this.object = object;\n  }\n  dispose() {}\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}