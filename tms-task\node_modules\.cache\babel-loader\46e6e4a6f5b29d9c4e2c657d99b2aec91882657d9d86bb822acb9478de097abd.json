{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { globals, isMacintosh, isWindows } from './platform.js';\nlet safeProcess;\n// Native sandbox environment\nif (typeof globals.vscode !== 'undefined' && typeof globals.vscode.process !== 'undefined') {\n  const sandboxProcess = globals.vscode.process;\n  safeProcess = {\n    get platform() {\n      return sandboxProcess.platform;\n    },\n    get arch() {\n      return sandboxProcess.arch;\n    },\n    get env() {\n      return sandboxProcess.env;\n    },\n    cwd() {\n      return sandboxProcess.cwd();\n    }\n  };\n}\n// Native node.js environment\nelse if (typeof process !== 'undefined') {\n  safeProcess = {\n    get platform() {\n      return process.platform;\n    },\n    get arch() {\n      return process.arch;\n    },\n    get env() {\n      return process.env;\n    },\n    cwd() {\n      return process.env['VSCODE_CWD'] || process.cwd();\n    }\n  };\n}\n// Web environment\nelse {\n  safeProcess = {\n    // Supported\n    get platform() {\n      return isWindows ? 'win32' : isMacintosh ? 'darwin' : 'linux';\n    },\n    get arch() {\n      return undefined; /* arch is undefined in web */\n    },\n    // Unsupported\n    get env() {\n      return {};\n    },\n    cwd() {\n      return '/';\n    }\n  };\n}\n/**\n * Provides safe access to the `cwd` property in node.js, sandboxed or web\n * environments.\n *\n * Note: in web, this property is hardcoded to be `/`.\n */\nexport const cwd = safeProcess.cwd;\n/**\n * Provides safe access to the `env` property in node.js, sandboxed or web\n * environments.\n *\n * Note: in web, this property is hardcoded to be `{}`.\n */\nexport const env = safeProcess.env;\n/**\n * Provides safe access to the `platform` property in node.js, sandboxed or web\n * environments.\n */\nexport const platform = safeProcess.platform;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}