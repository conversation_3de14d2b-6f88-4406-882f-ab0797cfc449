{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n// THIS IS A GENERATED FILE. DO NOT EDIT DIRECTLY.\nexport var AccessibilitySupport;\n(function (AccessibilitySupport) {\n  /**\n   * This should be the browser case where it is not known if a screen reader is attached or no.\n   */\n  AccessibilitySupport[AccessibilitySupport[\"Unknown\"] = 0] = \"Unknown\";\n  AccessibilitySupport[AccessibilitySupport[\"Disabled\"] = 1] = \"Disabled\";\n  AccessibilitySupport[AccessibilitySupport[\"Enabled\"] = 2] = \"Enabled\";\n})(AccessibilitySupport || (AccessibilitySupport = {}));\nexport var CodeActionTriggerType;\n(function (CodeActionTriggerType) {\n  CodeActionTriggerType[CodeActionTriggerType[\"Invoke\"] = 1] = \"Invoke\";\n  CodeActionTriggerType[CodeActionTriggerType[\"Auto\"] = 2] = \"Auto\";\n})(CodeActionTriggerType || (CodeActionTriggerType = {}));\nexport var CompletionItemInsertTextRule;\n(function (CompletionItemInsertTextRule) {\n  /**\n   * Adjust whitespace/indentation of multiline insert texts to\n   * match the current line indentation.\n   */\n  CompletionItemInsertTextRule[CompletionItemInsertTextRule[\"KeepWhitespace\"] = 1] = \"KeepWhitespace\";\n  /**\n   * `insertText` is a snippet.\n   */\n  CompletionItemInsertTextRule[CompletionItemInsertTextRule[\"InsertAsSnippet\"] = 4] = \"InsertAsSnippet\";\n})(CompletionItemInsertTextRule || (CompletionItemInsertTextRule = {}));\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n  CompletionItemKind[CompletionItemKind[\"Method\"] = 0] = \"Method\";\n  CompletionItemKind[CompletionItemKind[\"Function\"] = 1] = \"Function\";\n  CompletionItemKind[CompletionItemKind[\"Constructor\"] = 2] = \"Constructor\";\n  CompletionItemKind[CompletionItemKind[\"Field\"] = 3] = \"Field\";\n  CompletionItemKind[CompletionItemKind[\"Variable\"] = 4] = \"Variable\";\n  CompletionItemKind[CompletionItemKind[\"Class\"] = 5] = \"Class\";\n  CompletionItemKind[CompletionItemKind[\"Struct\"] = 6] = \"Struct\";\n  CompletionItemKind[CompletionItemKind[\"Interface\"] = 7] = \"Interface\";\n  CompletionItemKind[CompletionItemKind[\"Module\"] = 8] = \"Module\";\n  CompletionItemKind[CompletionItemKind[\"Property\"] = 9] = \"Property\";\n  CompletionItemKind[CompletionItemKind[\"Event\"] = 10] = \"Event\";\n  CompletionItemKind[CompletionItemKind[\"Operator\"] = 11] = \"Operator\";\n  CompletionItemKind[CompletionItemKind[\"Unit\"] = 12] = \"Unit\";\n  CompletionItemKind[CompletionItemKind[\"Value\"] = 13] = \"Value\";\n  CompletionItemKind[CompletionItemKind[\"Constant\"] = 14] = \"Constant\";\n  CompletionItemKind[CompletionItemKind[\"Enum\"] = 15] = \"Enum\";\n  CompletionItemKind[CompletionItemKind[\"EnumMember\"] = 16] = \"EnumMember\";\n  CompletionItemKind[CompletionItemKind[\"Keyword\"] = 17] = \"Keyword\";\n  CompletionItemKind[CompletionItemKind[\"Text\"] = 18] = \"Text\";\n  CompletionItemKind[CompletionItemKind[\"Color\"] = 19] = \"Color\";\n  CompletionItemKind[CompletionItemKind[\"File\"] = 20] = \"File\";\n  CompletionItemKind[CompletionItemKind[\"Reference\"] = 21] = \"Reference\";\n  CompletionItemKind[CompletionItemKind[\"Customcolor\"] = 22] = \"Customcolor\";\n  CompletionItemKind[CompletionItemKind[\"Folder\"] = 23] = \"Folder\";\n  CompletionItemKind[CompletionItemKind[\"TypeParameter\"] = 24] = \"TypeParameter\";\n  CompletionItemKind[CompletionItemKind[\"User\"] = 25] = \"User\";\n  CompletionItemKind[CompletionItemKind[\"Issue\"] = 26] = \"Issue\";\n  CompletionItemKind[CompletionItemKind[\"Snippet\"] = 27] = \"Snippet\";\n})(CompletionItemKind || (CompletionItemKind = {}));\nexport var CompletionItemTag;\n(function (CompletionItemTag) {\n  CompletionItemTag[CompletionItemTag[\"Deprecated\"] = 1] = \"Deprecated\";\n})(CompletionItemTag || (CompletionItemTag = {}));\n/**\n * How a suggest provider was triggered.\n */\nexport var CompletionTriggerKind;\n(function (CompletionTriggerKind) {\n  CompletionTriggerKind[CompletionTriggerKind[\"Invoke\"] = 0] = \"Invoke\";\n  CompletionTriggerKind[CompletionTriggerKind[\"TriggerCharacter\"] = 1] = \"TriggerCharacter\";\n  CompletionTriggerKind[CompletionTriggerKind[\"TriggerForIncompleteCompletions\"] = 2] = \"TriggerForIncompleteCompletions\";\n})(CompletionTriggerKind || (CompletionTriggerKind = {}));\n/**\n * A positioning preference for rendering content widgets.\n */\nexport var ContentWidgetPositionPreference;\n(function (ContentWidgetPositionPreference) {\n  /**\n   * Place the content widget exactly at a position\n   */\n  ContentWidgetPositionPreference[ContentWidgetPositionPreference[\"EXACT\"] = 0] = \"EXACT\";\n  /**\n   * Place the content widget above a position\n   */\n  ContentWidgetPositionPreference[ContentWidgetPositionPreference[\"ABOVE\"] = 1] = \"ABOVE\";\n  /**\n   * Place the content widget below a position\n   */\n  ContentWidgetPositionPreference[ContentWidgetPositionPreference[\"BELOW\"] = 2] = \"BELOW\";\n})(ContentWidgetPositionPreference || (ContentWidgetPositionPreference = {}));\n/**\n * Describes the reason the cursor has changed its position.\n */\nexport var CursorChangeReason;\n(function (CursorChangeReason) {\n  /**\n   * Unknown or not set.\n   */\n  CursorChangeReason[CursorChangeReason[\"NotSet\"] = 0] = \"NotSet\";\n  /**\n   * A `model.setValue()` was called.\n   */\n  CursorChangeReason[CursorChangeReason[\"ContentFlush\"] = 1] = \"ContentFlush\";\n  /**\n   * The `model` has been changed outside of this cursor and the cursor recovers its position from associated markers.\n   */\n  CursorChangeReason[CursorChangeReason[\"RecoverFromMarkers\"] = 2] = \"RecoverFromMarkers\";\n  /**\n   * There was an explicit user gesture.\n   */\n  CursorChangeReason[CursorChangeReason[\"Explicit\"] = 3] = \"Explicit\";\n  /**\n   * There was a Paste.\n   */\n  CursorChangeReason[CursorChangeReason[\"Paste\"] = 4] = \"Paste\";\n  /**\n   * There was an Undo.\n   */\n  CursorChangeReason[CursorChangeReason[\"Undo\"] = 5] = \"Undo\";\n  /**\n   * There was a Redo.\n   */\n  CursorChangeReason[CursorChangeReason[\"Redo\"] = 6] = \"Redo\";\n})(CursorChangeReason || (CursorChangeReason = {}));\n/**\n * The default end of line to use when instantiating models.\n */\nexport var DefaultEndOfLine;\n(function (DefaultEndOfLine) {\n  /**\n   * Use line feed (\\n) as the end of line character.\n   */\n  DefaultEndOfLine[DefaultEndOfLine[\"LF\"] = 1] = \"LF\";\n  /**\n   * Use carriage return and line feed (\\r\\n) as the end of line character.\n   */\n  DefaultEndOfLine[DefaultEndOfLine[\"CRLF\"] = 2] = \"CRLF\";\n})(DefaultEndOfLine || (DefaultEndOfLine = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n  /**\n   * A textual occurrence.\n   */\n  DocumentHighlightKind[DocumentHighlightKind[\"Text\"] = 0] = \"Text\";\n  /**\n   * Read-access of a symbol, like reading a variable.\n   */\n  DocumentHighlightKind[DocumentHighlightKind[\"Read\"] = 1] = \"Read\";\n  /**\n   * Write-access of a symbol, like writing to a variable.\n   */\n  DocumentHighlightKind[DocumentHighlightKind[\"Write\"] = 2] = \"Write\";\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * Configuration options for auto indentation in the editor\n */\nexport var EditorAutoIndentStrategy;\n(function (EditorAutoIndentStrategy) {\n  EditorAutoIndentStrategy[EditorAutoIndentStrategy[\"None\"] = 0] = \"None\";\n  EditorAutoIndentStrategy[EditorAutoIndentStrategy[\"Keep\"] = 1] = \"Keep\";\n  EditorAutoIndentStrategy[EditorAutoIndentStrategy[\"Brackets\"] = 2] = \"Brackets\";\n  EditorAutoIndentStrategy[EditorAutoIndentStrategy[\"Advanced\"] = 3] = \"Advanced\";\n  EditorAutoIndentStrategy[EditorAutoIndentStrategy[\"Full\"] = 4] = \"Full\";\n})(EditorAutoIndentStrategy || (EditorAutoIndentStrategy = {}));\nexport var EditorOption;\n(function (EditorOption) {\n  EditorOption[EditorOption[\"acceptSuggestionOnCommitCharacter\"] = 0] = \"acceptSuggestionOnCommitCharacter\";\n  EditorOption[EditorOption[\"acceptSuggestionOnEnter\"] = 1] = \"acceptSuggestionOnEnter\";\n  EditorOption[EditorOption[\"accessibilitySupport\"] = 2] = \"accessibilitySupport\";\n  EditorOption[EditorOption[\"accessibilityPageSize\"] = 3] = \"accessibilityPageSize\";\n  EditorOption[EditorOption[\"ariaLabel\"] = 4] = \"ariaLabel\";\n  EditorOption[EditorOption[\"autoClosingBrackets\"] = 5] = \"autoClosingBrackets\";\n  EditorOption[EditorOption[\"autoClosingDelete\"] = 6] = \"autoClosingDelete\";\n  EditorOption[EditorOption[\"autoClosingOvertype\"] = 7] = \"autoClosingOvertype\";\n  EditorOption[EditorOption[\"autoClosingQuotes\"] = 8] = \"autoClosingQuotes\";\n  EditorOption[EditorOption[\"autoIndent\"] = 9] = \"autoIndent\";\n  EditorOption[EditorOption[\"automaticLayout\"] = 10] = \"automaticLayout\";\n  EditorOption[EditorOption[\"autoSurround\"] = 11] = \"autoSurround\";\n  EditorOption[EditorOption[\"bracketPairColorization\"] = 12] = \"bracketPairColorization\";\n  EditorOption[EditorOption[\"guides\"] = 13] = \"guides\";\n  EditorOption[EditorOption[\"codeLens\"] = 14] = \"codeLens\";\n  EditorOption[EditorOption[\"codeLensFontFamily\"] = 15] = \"codeLensFontFamily\";\n  EditorOption[EditorOption[\"codeLensFontSize\"] = 16] = \"codeLensFontSize\";\n  EditorOption[EditorOption[\"colorDecorators\"] = 17] = \"colorDecorators\";\n  EditorOption[EditorOption[\"columnSelection\"] = 18] = \"columnSelection\";\n  EditorOption[EditorOption[\"comments\"] = 19] = \"comments\";\n  EditorOption[EditorOption[\"contextmenu\"] = 20] = \"contextmenu\";\n  EditorOption[EditorOption[\"copyWithSyntaxHighlighting\"] = 21] = \"copyWithSyntaxHighlighting\";\n  EditorOption[EditorOption[\"cursorBlinking\"] = 22] = \"cursorBlinking\";\n  EditorOption[EditorOption[\"cursorSmoothCaretAnimation\"] = 23] = \"cursorSmoothCaretAnimation\";\n  EditorOption[EditorOption[\"cursorStyle\"] = 24] = \"cursorStyle\";\n  EditorOption[EditorOption[\"cursorSurroundingLines\"] = 25] = \"cursorSurroundingLines\";\n  EditorOption[EditorOption[\"cursorSurroundingLinesStyle\"] = 26] = \"cursorSurroundingLinesStyle\";\n  EditorOption[EditorOption[\"cursorWidth\"] = 27] = \"cursorWidth\";\n  EditorOption[EditorOption[\"disableLayerHinting\"] = 28] = \"disableLayerHinting\";\n  EditorOption[EditorOption[\"disableMonospaceOptimizations\"] = 29] = \"disableMonospaceOptimizations\";\n  EditorOption[EditorOption[\"domReadOnly\"] = 30] = \"domReadOnly\";\n  EditorOption[EditorOption[\"dragAndDrop\"] = 31] = \"dragAndDrop\";\n  EditorOption[EditorOption[\"dropIntoEditor\"] = 32] = \"dropIntoEditor\";\n  EditorOption[EditorOption[\"emptySelectionClipboard\"] = 33] = \"emptySelectionClipboard\";\n  EditorOption[EditorOption[\"experimental\"] = 34] = \"experimental\";\n  EditorOption[EditorOption[\"extraEditorClassName\"] = 35] = \"extraEditorClassName\";\n  EditorOption[EditorOption[\"fastScrollSensitivity\"] = 36] = \"fastScrollSensitivity\";\n  EditorOption[EditorOption[\"find\"] = 37] = \"find\";\n  EditorOption[EditorOption[\"fixedOverflowWidgets\"] = 38] = \"fixedOverflowWidgets\";\n  EditorOption[EditorOption[\"folding\"] = 39] = \"folding\";\n  EditorOption[EditorOption[\"foldingStrategy\"] = 40] = \"foldingStrategy\";\n  EditorOption[EditorOption[\"foldingHighlight\"] = 41] = \"foldingHighlight\";\n  EditorOption[EditorOption[\"foldingImportsByDefault\"] = 42] = \"foldingImportsByDefault\";\n  EditorOption[EditorOption[\"foldingMaximumRegions\"] = 43] = \"foldingMaximumRegions\";\n  EditorOption[EditorOption[\"unfoldOnClickAfterEndOfLine\"] = 44] = \"unfoldOnClickAfterEndOfLine\";\n  EditorOption[EditorOption[\"fontFamily\"] = 45] = \"fontFamily\";\n  EditorOption[EditorOption[\"fontInfo\"] = 46] = \"fontInfo\";\n  EditorOption[EditorOption[\"fontLigatures\"] = 47] = \"fontLigatures\";\n  EditorOption[EditorOption[\"fontSize\"] = 48] = \"fontSize\";\n  EditorOption[EditorOption[\"fontWeight\"] = 49] = \"fontWeight\";\n  EditorOption[EditorOption[\"formatOnPaste\"] = 50] = \"formatOnPaste\";\n  EditorOption[EditorOption[\"formatOnType\"] = 51] = \"formatOnType\";\n  EditorOption[EditorOption[\"glyphMargin\"] = 52] = \"glyphMargin\";\n  EditorOption[EditorOption[\"gotoLocation\"] = 53] = \"gotoLocation\";\n  EditorOption[EditorOption[\"hideCursorInOverviewRuler\"] = 54] = \"hideCursorInOverviewRuler\";\n  EditorOption[EditorOption[\"hover\"] = 55] = \"hover\";\n  EditorOption[EditorOption[\"inDiffEditor\"] = 56] = \"inDiffEditor\";\n  EditorOption[EditorOption[\"inlineSuggest\"] = 57] = \"inlineSuggest\";\n  EditorOption[EditorOption[\"letterSpacing\"] = 58] = \"letterSpacing\";\n  EditorOption[EditorOption[\"lightbulb\"] = 59] = \"lightbulb\";\n  EditorOption[EditorOption[\"lineDecorationsWidth\"] = 60] = \"lineDecorationsWidth\";\n  EditorOption[EditorOption[\"lineHeight\"] = 61] = \"lineHeight\";\n  EditorOption[EditorOption[\"lineNumbers\"] = 62] = \"lineNumbers\";\n  EditorOption[EditorOption[\"lineNumbersMinChars\"] = 63] = \"lineNumbersMinChars\";\n  EditorOption[EditorOption[\"linkedEditing\"] = 64] = \"linkedEditing\";\n  EditorOption[EditorOption[\"links\"] = 65] = \"links\";\n  EditorOption[EditorOption[\"matchBrackets\"] = 66] = \"matchBrackets\";\n  EditorOption[EditorOption[\"minimap\"] = 67] = \"minimap\";\n  EditorOption[EditorOption[\"mouseStyle\"] = 68] = \"mouseStyle\";\n  EditorOption[EditorOption[\"mouseWheelScrollSensitivity\"] = 69] = \"mouseWheelScrollSensitivity\";\n  EditorOption[EditorOption[\"mouseWheelZoom\"] = 70] = \"mouseWheelZoom\";\n  EditorOption[EditorOption[\"multiCursorMergeOverlapping\"] = 71] = \"multiCursorMergeOverlapping\";\n  EditorOption[EditorOption[\"multiCursorModifier\"] = 72] = \"multiCursorModifier\";\n  EditorOption[EditorOption[\"multiCursorPaste\"] = 73] = \"multiCursorPaste\";\n  EditorOption[EditorOption[\"occurrencesHighlight\"] = 74] = \"occurrencesHighlight\";\n  EditorOption[EditorOption[\"overviewRulerBorder\"] = 75] = \"overviewRulerBorder\";\n  EditorOption[EditorOption[\"overviewRulerLanes\"] = 76] = \"overviewRulerLanes\";\n  EditorOption[EditorOption[\"padding\"] = 77] = \"padding\";\n  EditorOption[EditorOption[\"parameterHints\"] = 78] = \"parameterHints\";\n  EditorOption[EditorOption[\"peekWidgetDefaultFocus\"] = 79] = \"peekWidgetDefaultFocus\";\n  EditorOption[EditorOption[\"definitionLinkOpensInPeek\"] = 80] = \"definitionLinkOpensInPeek\";\n  EditorOption[EditorOption[\"quickSuggestions\"] = 81] = \"quickSuggestions\";\n  EditorOption[EditorOption[\"quickSuggestionsDelay\"] = 82] = \"quickSuggestionsDelay\";\n  EditorOption[EditorOption[\"readOnly\"] = 83] = \"readOnly\";\n  EditorOption[EditorOption[\"renameOnType\"] = 84] = \"renameOnType\";\n  EditorOption[EditorOption[\"renderControlCharacters\"] = 85] = \"renderControlCharacters\";\n  EditorOption[EditorOption[\"renderFinalNewline\"] = 86] = \"renderFinalNewline\";\n  EditorOption[EditorOption[\"renderLineHighlight\"] = 87] = \"renderLineHighlight\";\n  EditorOption[EditorOption[\"renderLineHighlightOnlyWhenFocus\"] = 88] = \"renderLineHighlightOnlyWhenFocus\";\n  EditorOption[EditorOption[\"renderValidationDecorations\"] = 89] = \"renderValidationDecorations\";\n  EditorOption[EditorOption[\"renderWhitespace\"] = 90] = \"renderWhitespace\";\n  EditorOption[EditorOption[\"revealHorizontalRightPadding\"] = 91] = \"revealHorizontalRightPadding\";\n  EditorOption[EditorOption[\"roundedSelection\"] = 92] = \"roundedSelection\";\n  EditorOption[EditorOption[\"rulers\"] = 93] = \"rulers\";\n  EditorOption[EditorOption[\"scrollbar\"] = 94] = \"scrollbar\";\n  EditorOption[EditorOption[\"scrollBeyondLastColumn\"] = 95] = \"scrollBeyondLastColumn\";\n  EditorOption[EditorOption[\"scrollBeyondLastLine\"] = 96] = \"scrollBeyondLastLine\";\n  EditorOption[EditorOption[\"scrollPredominantAxis\"] = 97] = \"scrollPredominantAxis\";\n  EditorOption[EditorOption[\"selectionClipboard\"] = 98] = \"selectionClipboard\";\n  EditorOption[EditorOption[\"selectionHighlight\"] = 99] = \"selectionHighlight\";\n  EditorOption[EditorOption[\"selectOnLineNumbers\"] = 100] = \"selectOnLineNumbers\";\n  EditorOption[EditorOption[\"showFoldingControls\"] = 101] = \"showFoldingControls\";\n  EditorOption[EditorOption[\"showUnused\"] = 102] = \"showUnused\";\n  EditorOption[EditorOption[\"snippetSuggestions\"] = 103] = \"snippetSuggestions\";\n  EditorOption[EditorOption[\"smartSelect\"] = 104] = \"smartSelect\";\n  EditorOption[EditorOption[\"smoothScrolling\"] = 105] = \"smoothScrolling\";\n  EditorOption[EditorOption[\"stickyTabStops\"] = 106] = \"stickyTabStops\";\n  EditorOption[EditorOption[\"stopRenderingLineAfter\"] = 107] = \"stopRenderingLineAfter\";\n  EditorOption[EditorOption[\"suggest\"] = 108] = \"suggest\";\n  EditorOption[EditorOption[\"suggestFontSize\"] = 109] = \"suggestFontSize\";\n  EditorOption[EditorOption[\"suggestLineHeight\"] = 110] = \"suggestLineHeight\";\n  EditorOption[EditorOption[\"suggestOnTriggerCharacters\"] = 111] = \"suggestOnTriggerCharacters\";\n  EditorOption[EditorOption[\"suggestSelection\"] = 112] = \"suggestSelection\";\n  EditorOption[EditorOption[\"tabCompletion\"] = 113] = \"tabCompletion\";\n  EditorOption[EditorOption[\"tabIndex\"] = 114] = \"tabIndex\";\n  EditorOption[EditorOption[\"unicodeHighlighting\"] = 115] = \"unicodeHighlighting\";\n  EditorOption[EditorOption[\"unusualLineTerminators\"] = 116] = \"unusualLineTerminators\";\n  EditorOption[EditorOption[\"useShadowDOM\"] = 117] = \"useShadowDOM\";\n  EditorOption[EditorOption[\"useTabStops\"] = 118] = \"useTabStops\";\n  EditorOption[EditorOption[\"wordSeparators\"] = 119] = \"wordSeparators\";\n  EditorOption[EditorOption[\"wordWrap\"] = 120] = \"wordWrap\";\n  EditorOption[EditorOption[\"wordWrapBreakAfterCharacters\"] = 121] = \"wordWrapBreakAfterCharacters\";\n  EditorOption[EditorOption[\"wordWrapBreakBeforeCharacters\"] = 122] = \"wordWrapBreakBeforeCharacters\";\n  EditorOption[EditorOption[\"wordWrapColumn\"] = 123] = \"wordWrapColumn\";\n  EditorOption[EditorOption[\"wordWrapOverride1\"] = 124] = \"wordWrapOverride1\";\n  EditorOption[EditorOption[\"wordWrapOverride2\"] = 125] = \"wordWrapOverride2\";\n  EditorOption[EditorOption[\"wrappingIndent\"] = 126] = \"wrappingIndent\";\n  EditorOption[EditorOption[\"wrappingStrategy\"] = 127] = \"wrappingStrategy\";\n  EditorOption[EditorOption[\"showDeprecated\"] = 128] = \"showDeprecated\";\n  EditorOption[EditorOption[\"inlayHints\"] = 129] = \"inlayHints\";\n  EditorOption[EditorOption[\"editorClassName\"] = 130] = \"editorClassName\";\n  EditorOption[EditorOption[\"pixelRatio\"] = 131] = \"pixelRatio\";\n  EditorOption[EditorOption[\"tabFocusMode\"] = 132] = \"tabFocusMode\";\n  EditorOption[EditorOption[\"layoutInfo\"] = 133] = \"layoutInfo\";\n  EditorOption[EditorOption[\"wrappingInfo\"] = 134] = \"wrappingInfo\";\n})(EditorOption || (EditorOption = {}));\n/**\n * End of line character preference.\n */\nexport var EndOfLinePreference;\n(function (EndOfLinePreference) {\n  /**\n   * Use the end of line character identified in the text buffer.\n   */\n  EndOfLinePreference[EndOfLinePreference[\"TextDefined\"] = 0] = \"TextDefined\";\n  /**\n   * Use line feed (\\n) as the end of line character.\n   */\n  EndOfLinePreference[EndOfLinePreference[\"LF\"] = 1] = \"LF\";\n  /**\n   * Use carriage return and line feed (\\r\\n) as the end of line character.\n   */\n  EndOfLinePreference[EndOfLinePreference[\"CRLF\"] = 2] = \"CRLF\";\n})(EndOfLinePreference || (EndOfLinePreference = {}));\n/**\n * End of line character preference.\n */\nexport var EndOfLineSequence;\n(function (EndOfLineSequence) {\n  /**\n   * Use line feed (\\n) as the end of line character.\n   */\n  EndOfLineSequence[EndOfLineSequence[\"LF\"] = 0] = \"LF\";\n  /**\n   * Use carriage return and line feed (\\r\\n) as the end of line character.\n   */\n  EndOfLineSequence[EndOfLineSequence[\"CRLF\"] = 1] = \"CRLF\";\n})(EndOfLineSequence || (EndOfLineSequence = {}));\n/**\n * Describes what to do with the indentation when pressing Enter.\n */\nexport var IndentAction;\n(function (IndentAction) {\n  /**\n   * Insert new line and copy the previous line's indentation.\n   */\n  IndentAction[IndentAction[\"None\"] = 0] = \"None\";\n  /**\n   * Insert new line and indent once (relative to the previous line's indentation).\n   */\n  IndentAction[IndentAction[\"Indent\"] = 1] = \"Indent\";\n  /**\n   * Insert two new lines:\n   *  - the first one indented which will hold the cursor\n   *  - the second one at the same indentation level\n   */\n  IndentAction[IndentAction[\"IndentOutdent\"] = 2] = \"IndentOutdent\";\n  /**\n   * Insert new line and outdent once (relative to the previous line's indentation).\n   */\n  IndentAction[IndentAction[\"Outdent\"] = 3] = \"Outdent\";\n})(IndentAction || (IndentAction = {}));\nexport var InjectedTextCursorStops;\n(function (InjectedTextCursorStops) {\n  InjectedTextCursorStops[InjectedTextCursorStops[\"Both\"] = 0] = \"Both\";\n  InjectedTextCursorStops[InjectedTextCursorStops[\"Right\"] = 1] = \"Right\";\n  InjectedTextCursorStops[InjectedTextCursorStops[\"Left\"] = 2] = \"Left\";\n  InjectedTextCursorStops[InjectedTextCursorStops[\"None\"] = 3] = \"None\";\n})(InjectedTextCursorStops || (InjectedTextCursorStops = {}));\nexport var InlayHintKind;\n(function (InlayHintKind) {\n  InlayHintKind[InlayHintKind[\"Type\"] = 1] = \"Type\";\n  InlayHintKind[InlayHintKind[\"Parameter\"] = 2] = \"Parameter\";\n})(InlayHintKind || (InlayHintKind = {}));\n/**\n * How an {@link InlineCompletionsProvider inline completion provider} was triggered.\n */\nexport var InlineCompletionTriggerKind;\n(function (InlineCompletionTriggerKind) {\n  /**\n   * Completion was triggered automatically while editing.\n   * It is sufficient to return a single completion item in this case.\n   */\n  InlineCompletionTriggerKind[InlineCompletionTriggerKind[\"Automatic\"] = 0] = \"Automatic\";\n  /**\n   * Completion was triggered explicitly by a user gesture.\n   * Return multiple completion items to enable cycling through them.\n   */\n  InlineCompletionTriggerKind[InlineCompletionTriggerKind[\"Explicit\"] = 1] = \"Explicit\";\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\n/**\n * Virtual Key Codes, the value does not hold any inherent meaning.\n * Inspired somewhat from https://msdn.microsoft.com/en-us/library/windows/desktop/dd375731(v=vs.85).aspx\n * But these are \"more general\", as they should work across browsers & OS`s.\n */\nexport var KeyCode;\n(function (KeyCode) {\n  KeyCode[KeyCode[\"DependsOnKbLayout\"] = -1] = \"DependsOnKbLayout\";\n  /**\n   * Placed first to cover the 0 value of the enum.\n   */\n  KeyCode[KeyCode[\"Unknown\"] = 0] = \"Unknown\";\n  KeyCode[KeyCode[\"Backspace\"] = 1] = \"Backspace\";\n  KeyCode[KeyCode[\"Tab\"] = 2] = \"Tab\";\n  KeyCode[KeyCode[\"Enter\"] = 3] = \"Enter\";\n  KeyCode[KeyCode[\"Shift\"] = 4] = \"Shift\";\n  KeyCode[KeyCode[\"Ctrl\"] = 5] = \"Ctrl\";\n  KeyCode[KeyCode[\"Alt\"] = 6] = \"Alt\";\n  KeyCode[KeyCode[\"PauseBreak\"] = 7] = \"PauseBreak\";\n  KeyCode[KeyCode[\"CapsLock\"] = 8] = \"CapsLock\";\n  KeyCode[KeyCode[\"Escape\"] = 9] = \"Escape\";\n  KeyCode[KeyCode[\"Space\"] = 10] = \"Space\";\n  KeyCode[KeyCode[\"PageUp\"] = 11] = \"PageUp\";\n  KeyCode[KeyCode[\"PageDown\"] = 12] = \"PageDown\";\n  KeyCode[KeyCode[\"End\"] = 13] = \"End\";\n  KeyCode[KeyCode[\"Home\"] = 14] = \"Home\";\n  KeyCode[KeyCode[\"LeftArrow\"] = 15] = \"LeftArrow\";\n  KeyCode[KeyCode[\"UpArrow\"] = 16] = \"UpArrow\";\n  KeyCode[KeyCode[\"RightArrow\"] = 17] = \"RightArrow\";\n  KeyCode[KeyCode[\"DownArrow\"] = 18] = \"DownArrow\";\n  KeyCode[KeyCode[\"Insert\"] = 19] = \"Insert\";\n  KeyCode[KeyCode[\"Delete\"] = 20] = \"Delete\";\n  KeyCode[KeyCode[\"Digit0\"] = 21] = \"Digit0\";\n  KeyCode[KeyCode[\"Digit1\"] = 22] = \"Digit1\";\n  KeyCode[KeyCode[\"Digit2\"] = 23] = \"Digit2\";\n  KeyCode[KeyCode[\"Digit3\"] = 24] = \"Digit3\";\n  KeyCode[KeyCode[\"Digit4\"] = 25] = \"Digit4\";\n  KeyCode[KeyCode[\"Digit5\"] = 26] = \"Digit5\";\n  KeyCode[KeyCode[\"Digit6\"] = 27] = \"Digit6\";\n  KeyCode[KeyCode[\"Digit7\"] = 28] = \"Digit7\";\n  KeyCode[KeyCode[\"Digit8\"] = 29] = \"Digit8\";\n  KeyCode[KeyCode[\"Digit9\"] = 30] = \"Digit9\";\n  KeyCode[KeyCode[\"KeyA\"] = 31] = \"KeyA\";\n  KeyCode[KeyCode[\"KeyB\"] = 32] = \"KeyB\";\n  KeyCode[KeyCode[\"KeyC\"] = 33] = \"KeyC\";\n  KeyCode[KeyCode[\"KeyD\"] = 34] = \"KeyD\";\n  KeyCode[KeyCode[\"KeyE\"] = 35] = \"KeyE\";\n  KeyCode[KeyCode[\"KeyF\"] = 36] = \"KeyF\";\n  KeyCode[KeyCode[\"KeyG\"] = 37] = \"KeyG\";\n  KeyCode[KeyCode[\"KeyH\"] = 38] = \"KeyH\";\n  KeyCode[KeyCode[\"KeyI\"] = 39] = \"KeyI\";\n  KeyCode[KeyCode[\"KeyJ\"] = 40] = \"KeyJ\";\n  KeyCode[KeyCode[\"KeyK\"] = 41] = \"KeyK\";\n  KeyCode[KeyCode[\"KeyL\"] = 42] = \"KeyL\";\n  KeyCode[KeyCode[\"KeyM\"] = 43] = \"KeyM\";\n  KeyCode[KeyCode[\"KeyN\"] = 44] = \"KeyN\";\n  KeyCode[KeyCode[\"KeyO\"] = 45] = \"KeyO\";\n  KeyCode[KeyCode[\"KeyP\"] = 46] = \"KeyP\";\n  KeyCode[KeyCode[\"KeyQ\"] = 47] = \"KeyQ\";\n  KeyCode[KeyCode[\"KeyR\"] = 48] = \"KeyR\";\n  KeyCode[KeyCode[\"KeyS\"] = 49] = \"KeyS\";\n  KeyCode[KeyCode[\"KeyT\"] = 50] = \"KeyT\";\n  KeyCode[KeyCode[\"KeyU\"] = 51] = \"KeyU\";\n  KeyCode[KeyCode[\"KeyV\"] = 52] = \"KeyV\";\n  KeyCode[KeyCode[\"KeyW\"] = 53] = \"KeyW\";\n  KeyCode[KeyCode[\"KeyX\"] = 54] = \"KeyX\";\n  KeyCode[KeyCode[\"KeyY\"] = 55] = \"KeyY\";\n  KeyCode[KeyCode[\"KeyZ\"] = 56] = \"KeyZ\";\n  KeyCode[KeyCode[\"Meta\"] = 57] = \"Meta\";\n  KeyCode[KeyCode[\"ContextMenu\"] = 58] = \"ContextMenu\";\n  KeyCode[KeyCode[\"F1\"] = 59] = \"F1\";\n  KeyCode[KeyCode[\"F2\"] = 60] = \"F2\";\n  KeyCode[KeyCode[\"F3\"] = 61] = \"F3\";\n  KeyCode[KeyCode[\"F4\"] = 62] = \"F4\";\n  KeyCode[KeyCode[\"F5\"] = 63] = \"F5\";\n  KeyCode[KeyCode[\"F6\"] = 64] = \"F6\";\n  KeyCode[KeyCode[\"F7\"] = 65] = \"F7\";\n  KeyCode[KeyCode[\"F8\"] = 66] = \"F8\";\n  KeyCode[KeyCode[\"F9\"] = 67] = \"F9\";\n  KeyCode[KeyCode[\"F10\"] = 68] = \"F10\";\n  KeyCode[KeyCode[\"F11\"] = 69] = \"F11\";\n  KeyCode[KeyCode[\"F12\"] = 70] = \"F12\";\n  KeyCode[KeyCode[\"F13\"] = 71] = \"F13\";\n  KeyCode[KeyCode[\"F14\"] = 72] = \"F14\";\n  KeyCode[KeyCode[\"F15\"] = 73] = \"F15\";\n  KeyCode[KeyCode[\"F16\"] = 74] = \"F16\";\n  KeyCode[KeyCode[\"F17\"] = 75] = \"F17\";\n  KeyCode[KeyCode[\"F18\"] = 76] = \"F18\";\n  KeyCode[KeyCode[\"F19\"] = 77] = \"F19\";\n  KeyCode[KeyCode[\"NumLock\"] = 78] = \"NumLock\";\n  KeyCode[KeyCode[\"ScrollLock\"] = 79] = \"ScrollLock\";\n  /**\n   * Used for miscellaneous characters; it can vary by keyboard.\n   * For the US standard keyboard, the ';:' key\n   */\n  KeyCode[KeyCode[\"Semicolon\"] = 80] = \"Semicolon\";\n  /**\n   * For any country/region, the '+' key\n   * For the US standard keyboard, the '=+' key\n   */\n  KeyCode[KeyCode[\"Equal\"] = 81] = \"Equal\";\n  /**\n   * For any country/region, the ',' key\n   * For the US standard keyboard, the ',<' key\n   */\n  KeyCode[KeyCode[\"Comma\"] = 82] = \"Comma\";\n  /**\n   * For any country/region, the '-' key\n   * For the US standard keyboard, the '-_' key\n   */\n  KeyCode[KeyCode[\"Minus\"] = 83] = \"Minus\";\n  /**\n   * For any country/region, the '.' key\n   * For the US standard keyboard, the '.>' key\n   */\n  KeyCode[KeyCode[\"Period\"] = 84] = \"Period\";\n  /**\n   * Used for miscellaneous characters; it can vary by keyboard.\n   * For the US standard keyboard, the '/?' key\n   */\n  KeyCode[KeyCode[\"Slash\"] = 85] = \"Slash\";\n  /**\n   * Used for miscellaneous characters; it can vary by keyboard.\n   * For the US standard keyboard, the '`~' key\n   */\n  KeyCode[KeyCode[\"Backquote\"] = 86] = \"Backquote\";\n  /**\n   * Used for miscellaneous characters; it can vary by keyboard.\n   * For the US standard keyboard, the '[{' key\n   */\n  KeyCode[KeyCode[\"BracketLeft\"] = 87] = \"BracketLeft\";\n  /**\n   * Used for miscellaneous characters; it can vary by keyboard.\n   * For the US standard keyboard, the '\\|' key\n   */\n  KeyCode[KeyCode[\"Backslash\"] = 88] = \"Backslash\";\n  /**\n   * Used for miscellaneous characters; it can vary by keyboard.\n   * For the US standard keyboard, the ']}' key\n   */\n  KeyCode[KeyCode[\"BracketRight\"] = 89] = \"BracketRight\";\n  /**\n   * Used for miscellaneous characters; it can vary by keyboard.\n   * For the US standard keyboard, the ''\"' key\n   */\n  KeyCode[KeyCode[\"Quote\"] = 90] = \"Quote\";\n  /**\n   * Used for miscellaneous characters; it can vary by keyboard.\n   */\n  KeyCode[KeyCode[\"OEM_8\"] = 91] = \"OEM_8\";\n  /**\n   * Either the angle bracket key or the backslash key on the RT 102-key keyboard.\n   */\n  KeyCode[KeyCode[\"IntlBackslash\"] = 92] = \"IntlBackslash\";\n  KeyCode[KeyCode[\"Numpad0\"] = 93] = \"Numpad0\";\n  KeyCode[KeyCode[\"Numpad1\"] = 94] = \"Numpad1\";\n  KeyCode[KeyCode[\"Numpad2\"] = 95] = \"Numpad2\";\n  KeyCode[KeyCode[\"Numpad3\"] = 96] = \"Numpad3\";\n  KeyCode[KeyCode[\"Numpad4\"] = 97] = \"Numpad4\";\n  KeyCode[KeyCode[\"Numpad5\"] = 98] = \"Numpad5\";\n  KeyCode[KeyCode[\"Numpad6\"] = 99] = \"Numpad6\";\n  KeyCode[KeyCode[\"Numpad7\"] = 100] = \"Numpad7\";\n  KeyCode[KeyCode[\"Numpad8\"] = 101] = \"Numpad8\";\n  KeyCode[KeyCode[\"Numpad9\"] = 102] = \"Numpad9\";\n  KeyCode[KeyCode[\"NumpadMultiply\"] = 103] = \"NumpadMultiply\";\n  KeyCode[KeyCode[\"NumpadAdd\"] = 104] = \"NumpadAdd\";\n  KeyCode[KeyCode[\"NUMPAD_SEPARATOR\"] = 105] = \"NUMPAD_SEPARATOR\";\n  KeyCode[KeyCode[\"NumpadSubtract\"] = 106] = \"NumpadSubtract\";\n  KeyCode[KeyCode[\"NumpadDecimal\"] = 107] = \"NumpadDecimal\";\n  KeyCode[KeyCode[\"NumpadDivide\"] = 108] = \"NumpadDivide\";\n  /**\n   * Cover all key codes when IME is processing input.\n   */\n  KeyCode[KeyCode[\"KEY_IN_COMPOSITION\"] = 109] = \"KEY_IN_COMPOSITION\";\n  KeyCode[KeyCode[\"ABNT_C1\"] = 110] = \"ABNT_C1\";\n  KeyCode[KeyCode[\"ABNT_C2\"] = 111] = \"ABNT_C2\";\n  KeyCode[KeyCode[\"AudioVolumeMute\"] = 112] = \"AudioVolumeMute\";\n  KeyCode[KeyCode[\"AudioVolumeUp\"] = 113] = \"AudioVolumeUp\";\n  KeyCode[KeyCode[\"AudioVolumeDown\"] = 114] = \"AudioVolumeDown\";\n  KeyCode[KeyCode[\"BrowserSearch\"] = 115] = \"BrowserSearch\";\n  KeyCode[KeyCode[\"BrowserHome\"] = 116] = \"BrowserHome\";\n  KeyCode[KeyCode[\"BrowserBack\"] = 117] = \"BrowserBack\";\n  KeyCode[KeyCode[\"BrowserForward\"] = 118] = \"BrowserForward\";\n  KeyCode[KeyCode[\"MediaTrackNext\"] = 119] = \"MediaTrackNext\";\n  KeyCode[KeyCode[\"MediaTrackPrevious\"] = 120] = \"MediaTrackPrevious\";\n  KeyCode[KeyCode[\"MediaStop\"] = 121] = \"MediaStop\";\n  KeyCode[KeyCode[\"MediaPlayPause\"] = 122] = \"MediaPlayPause\";\n  KeyCode[KeyCode[\"LaunchMediaPlayer\"] = 123] = \"LaunchMediaPlayer\";\n  KeyCode[KeyCode[\"LaunchMail\"] = 124] = \"LaunchMail\";\n  KeyCode[KeyCode[\"LaunchApp2\"] = 125] = \"LaunchApp2\";\n  /**\n   * VK_CLEAR, 0x0C, CLEAR key\n   */\n  KeyCode[KeyCode[\"Clear\"] = 126] = \"Clear\";\n  /**\n   * Placed last to cover the length of the enum.\n   * Please do not depend on this value!\n   */\n  KeyCode[KeyCode[\"MAX_VALUE\"] = 127] = \"MAX_VALUE\";\n})(KeyCode || (KeyCode = {}));\nexport var MarkerSeverity;\n(function (MarkerSeverity) {\n  MarkerSeverity[MarkerSeverity[\"Hint\"] = 1] = \"Hint\";\n  MarkerSeverity[MarkerSeverity[\"Info\"] = 2] = \"Info\";\n  MarkerSeverity[MarkerSeverity[\"Warning\"] = 4] = \"Warning\";\n  MarkerSeverity[MarkerSeverity[\"Error\"] = 8] = \"Error\";\n})(MarkerSeverity || (MarkerSeverity = {}));\nexport var MarkerTag;\n(function (MarkerTag) {\n  MarkerTag[MarkerTag[\"Unnecessary\"] = 1] = \"Unnecessary\";\n  MarkerTag[MarkerTag[\"Deprecated\"] = 2] = \"Deprecated\";\n})(MarkerTag || (MarkerTag = {}));\n/**\n * Position in the minimap to render the decoration.\n */\nexport var MinimapPosition;\n(function (MinimapPosition) {\n  MinimapPosition[MinimapPosition[\"Inline\"] = 1] = \"Inline\";\n  MinimapPosition[MinimapPosition[\"Gutter\"] = 2] = \"Gutter\";\n})(MinimapPosition || (MinimapPosition = {}));\n/**\n * Type of hit element with the mouse in the editor.\n */\nexport var MouseTargetType;\n(function (MouseTargetType) {\n  /**\n   * Mouse is on top of an unknown element.\n   */\n  MouseTargetType[MouseTargetType[\"UNKNOWN\"] = 0] = \"UNKNOWN\";\n  /**\n   * Mouse is on top of the textarea used for input.\n   */\n  MouseTargetType[MouseTargetType[\"TEXTAREA\"] = 1] = \"TEXTAREA\";\n  /**\n   * Mouse is on top of the glyph margin\n   */\n  MouseTargetType[MouseTargetType[\"GUTTER_GLYPH_MARGIN\"] = 2] = \"GUTTER_GLYPH_MARGIN\";\n  /**\n   * Mouse is on top of the line numbers\n   */\n  MouseTargetType[MouseTargetType[\"GUTTER_LINE_NUMBERS\"] = 3] = \"GUTTER_LINE_NUMBERS\";\n  /**\n   * Mouse is on top of the line decorations\n   */\n  MouseTargetType[MouseTargetType[\"GUTTER_LINE_DECORATIONS\"] = 4] = \"GUTTER_LINE_DECORATIONS\";\n  /**\n   * Mouse is on top of the whitespace left in the gutter by a view zone.\n   */\n  MouseTargetType[MouseTargetType[\"GUTTER_VIEW_ZONE\"] = 5] = \"GUTTER_VIEW_ZONE\";\n  /**\n   * Mouse is on top of text in the content.\n   */\n  MouseTargetType[MouseTargetType[\"CONTENT_TEXT\"] = 6] = \"CONTENT_TEXT\";\n  /**\n   * Mouse is on top of empty space in the content (e.g. after line text or below last line)\n   */\n  MouseTargetType[MouseTargetType[\"CONTENT_EMPTY\"] = 7] = \"CONTENT_EMPTY\";\n  /**\n   * Mouse is on top of a view zone in the content.\n   */\n  MouseTargetType[MouseTargetType[\"CONTENT_VIEW_ZONE\"] = 8] = \"CONTENT_VIEW_ZONE\";\n  /**\n   * Mouse is on top of a content widget.\n   */\n  MouseTargetType[MouseTargetType[\"CONTENT_WIDGET\"] = 9] = \"CONTENT_WIDGET\";\n  /**\n   * Mouse is on top of the decorations overview ruler.\n   */\n  MouseTargetType[MouseTargetType[\"OVERVIEW_RULER\"] = 10] = \"OVERVIEW_RULER\";\n  /**\n   * Mouse is on top of a scrollbar.\n   */\n  MouseTargetType[MouseTargetType[\"SCROLLBAR\"] = 11] = \"SCROLLBAR\";\n  /**\n   * Mouse is on top of an overlay widget.\n   */\n  MouseTargetType[MouseTargetType[\"OVERLAY_WIDGET\"] = 12] = \"OVERLAY_WIDGET\";\n  /**\n   * Mouse is outside of the editor.\n   */\n  MouseTargetType[MouseTargetType[\"OUTSIDE_EDITOR\"] = 13] = \"OUTSIDE_EDITOR\";\n})(MouseTargetType || (MouseTargetType = {}));\n/**\n * A positioning preference for rendering overlay widgets.\n */\nexport var OverlayWidgetPositionPreference;\n(function (OverlayWidgetPositionPreference) {\n  /**\n   * Position the overlay widget in the top right corner\n   */\n  OverlayWidgetPositionPreference[OverlayWidgetPositionPreference[\"TOP_RIGHT_CORNER\"] = 0] = \"TOP_RIGHT_CORNER\";\n  /**\n   * Position the overlay widget in the bottom right corner\n   */\n  OverlayWidgetPositionPreference[OverlayWidgetPositionPreference[\"BOTTOM_RIGHT_CORNER\"] = 1] = \"BOTTOM_RIGHT_CORNER\";\n  /**\n   * Position the overlay widget in the top center\n   */\n  OverlayWidgetPositionPreference[OverlayWidgetPositionPreference[\"TOP_CENTER\"] = 2] = \"TOP_CENTER\";\n})(OverlayWidgetPositionPreference || (OverlayWidgetPositionPreference = {}));\n/**\n * Vertical Lane in the overview ruler of the editor.\n */\nexport var OverviewRulerLane;\n(function (OverviewRulerLane) {\n  OverviewRulerLane[OverviewRulerLane[\"Left\"] = 1] = \"Left\";\n  OverviewRulerLane[OverviewRulerLane[\"Center\"] = 2] = \"Center\";\n  OverviewRulerLane[OverviewRulerLane[\"Right\"] = 4] = \"Right\";\n  OverviewRulerLane[OverviewRulerLane[\"Full\"] = 7] = \"Full\";\n})(OverviewRulerLane || (OverviewRulerLane = {}));\nexport var PositionAffinity;\n(function (PositionAffinity) {\n  /**\n   * Prefers the left most position.\n  */\n  PositionAffinity[PositionAffinity[\"Left\"] = 0] = \"Left\";\n  /**\n   * Prefers the right most position.\n  */\n  PositionAffinity[PositionAffinity[\"Right\"] = 1] = \"Right\";\n  /**\n   * No preference.\n  */\n  PositionAffinity[PositionAffinity[\"None\"] = 2] = \"None\";\n  /**\n   * If the given position is on injected text, prefers the position left of it.\n  */\n  PositionAffinity[PositionAffinity[\"LeftOfInjectedText\"] = 3] = \"LeftOfInjectedText\";\n  /**\n   * If the given position is on injected text, prefers the position right of it.\n  */\n  PositionAffinity[PositionAffinity[\"RightOfInjectedText\"] = 4] = \"RightOfInjectedText\";\n})(PositionAffinity || (PositionAffinity = {}));\nexport var RenderLineNumbersType;\n(function (RenderLineNumbersType) {\n  RenderLineNumbersType[RenderLineNumbersType[\"Off\"] = 0] = \"Off\";\n  RenderLineNumbersType[RenderLineNumbersType[\"On\"] = 1] = \"On\";\n  RenderLineNumbersType[RenderLineNumbersType[\"Relative\"] = 2] = \"Relative\";\n  RenderLineNumbersType[RenderLineNumbersType[\"Interval\"] = 3] = \"Interval\";\n  RenderLineNumbersType[RenderLineNumbersType[\"Custom\"] = 4] = \"Custom\";\n})(RenderLineNumbersType || (RenderLineNumbersType = {}));\nexport var RenderMinimap;\n(function (RenderMinimap) {\n  RenderMinimap[RenderMinimap[\"None\"] = 0] = \"None\";\n  RenderMinimap[RenderMinimap[\"Text\"] = 1] = \"Text\";\n  RenderMinimap[RenderMinimap[\"Blocks\"] = 2] = \"Blocks\";\n})(RenderMinimap || (RenderMinimap = {}));\nexport var ScrollType;\n(function (ScrollType) {\n  ScrollType[ScrollType[\"Smooth\"] = 0] = \"Smooth\";\n  ScrollType[ScrollType[\"Immediate\"] = 1] = \"Immediate\";\n})(ScrollType || (ScrollType = {}));\nexport var ScrollbarVisibility;\n(function (ScrollbarVisibility) {\n  ScrollbarVisibility[ScrollbarVisibility[\"Auto\"] = 1] = \"Auto\";\n  ScrollbarVisibility[ScrollbarVisibility[\"Hidden\"] = 2] = \"Hidden\";\n  ScrollbarVisibility[ScrollbarVisibility[\"Visible\"] = 3] = \"Visible\";\n})(ScrollbarVisibility || (ScrollbarVisibility = {}));\n/**\n * The direction of a selection.\n */\nexport var SelectionDirection;\n(function (SelectionDirection) {\n  /**\n   * The selection starts above where it ends.\n   */\n  SelectionDirection[SelectionDirection[\"LTR\"] = 0] = \"LTR\";\n  /**\n   * The selection starts below where it ends.\n   */\n  SelectionDirection[SelectionDirection[\"RTL\"] = 1] = \"RTL\";\n})(SelectionDirection || (SelectionDirection = {}));\nexport var SignatureHelpTriggerKind;\n(function (SignatureHelpTriggerKind) {\n  SignatureHelpTriggerKind[SignatureHelpTriggerKind[\"Invoke\"] = 1] = \"Invoke\";\n  SignatureHelpTriggerKind[SignatureHelpTriggerKind[\"TriggerCharacter\"] = 2] = \"TriggerCharacter\";\n  SignatureHelpTriggerKind[SignatureHelpTriggerKind[\"ContentChange\"] = 3] = \"ContentChange\";\n})(SignatureHelpTriggerKind || (SignatureHelpTriggerKind = {}));\n/**\n * A symbol kind.\n */\nexport var SymbolKind;\n(function (SymbolKind) {\n  SymbolKind[SymbolKind[\"File\"] = 0] = \"File\";\n  SymbolKind[SymbolKind[\"Module\"] = 1] = \"Module\";\n  SymbolKind[SymbolKind[\"Namespace\"] = 2] = \"Namespace\";\n  SymbolKind[SymbolKind[\"Package\"] = 3] = \"Package\";\n  SymbolKind[SymbolKind[\"Class\"] = 4] = \"Class\";\n  SymbolKind[SymbolKind[\"Method\"] = 5] = \"Method\";\n  SymbolKind[SymbolKind[\"Property\"] = 6] = \"Property\";\n  SymbolKind[SymbolKind[\"Field\"] = 7] = \"Field\";\n  SymbolKind[SymbolKind[\"Constructor\"] = 8] = \"Constructor\";\n  SymbolKind[SymbolKind[\"Enum\"] = 9] = \"Enum\";\n  SymbolKind[SymbolKind[\"Interface\"] = 10] = \"Interface\";\n  SymbolKind[SymbolKind[\"Function\"] = 11] = \"Function\";\n  SymbolKind[SymbolKind[\"Variable\"] = 12] = \"Variable\";\n  SymbolKind[SymbolKind[\"Constant\"] = 13] = \"Constant\";\n  SymbolKind[SymbolKind[\"String\"] = 14] = \"String\";\n  SymbolKind[SymbolKind[\"Number\"] = 15] = \"Number\";\n  SymbolKind[SymbolKind[\"Boolean\"] = 16] = \"Boolean\";\n  SymbolKind[SymbolKind[\"Array\"] = 17] = \"Array\";\n  SymbolKind[SymbolKind[\"Object\"] = 18] = \"Object\";\n  SymbolKind[SymbolKind[\"Key\"] = 19] = \"Key\";\n  SymbolKind[SymbolKind[\"Null\"] = 20] = \"Null\";\n  SymbolKind[SymbolKind[\"EnumMember\"] = 21] = \"EnumMember\";\n  SymbolKind[SymbolKind[\"Struct\"] = 22] = \"Struct\";\n  SymbolKind[SymbolKind[\"Event\"] = 23] = \"Event\";\n  SymbolKind[SymbolKind[\"Operator\"] = 24] = \"Operator\";\n  SymbolKind[SymbolKind[\"TypeParameter\"] = 25] = \"TypeParameter\";\n})(SymbolKind || (SymbolKind = {}));\nexport var SymbolTag;\n(function (SymbolTag) {\n  SymbolTag[SymbolTag[\"Deprecated\"] = 1] = \"Deprecated\";\n})(SymbolTag || (SymbolTag = {}));\n/**\n * The kind of animation in which the editor's cursor should be rendered.\n */\nexport var TextEditorCursorBlinkingStyle;\n(function (TextEditorCursorBlinkingStyle) {\n  /**\n   * Hidden\n   */\n  TextEditorCursorBlinkingStyle[TextEditorCursorBlinkingStyle[\"Hidden\"] = 0] = \"Hidden\";\n  /**\n   * Blinking\n   */\n  TextEditorCursorBlinkingStyle[TextEditorCursorBlinkingStyle[\"Blink\"] = 1] = \"Blink\";\n  /**\n   * Blinking with smooth fading\n   */\n  TextEditorCursorBlinkingStyle[TextEditorCursorBlinkingStyle[\"Smooth\"] = 2] = \"Smooth\";\n  /**\n   * Blinking with prolonged filled state and smooth fading\n   */\n  TextEditorCursorBlinkingStyle[TextEditorCursorBlinkingStyle[\"Phase\"] = 3] = \"Phase\";\n  /**\n   * Expand collapse animation on the y axis\n   */\n  TextEditorCursorBlinkingStyle[TextEditorCursorBlinkingStyle[\"Expand\"] = 4] = \"Expand\";\n  /**\n   * No-Blinking\n   */\n  TextEditorCursorBlinkingStyle[TextEditorCursorBlinkingStyle[\"Solid\"] = 5] = \"Solid\";\n})(TextEditorCursorBlinkingStyle || (TextEditorCursorBlinkingStyle = {}));\n/**\n * The style in which the editor's cursor should be rendered.\n */\nexport var TextEditorCursorStyle;\n(function (TextEditorCursorStyle) {\n  /**\n   * As a vertical line (sitting between two characters).\n   */\n  TextEditorCursorStyle[TextEditorCursorStyle[\"Line\"] = 1] = \"Line\";\n  /**\n   * As a block (sitting on top of a character).\n   */\n  TextEditorCursorStyle[TextEditorCursorStyle[\"Block\"] = 2] = \"Block\";\n  /**\n   * As a horizontal line (sitting under a character).\n   */\n  TextEditorCursorStyle[TextEditorCursorStyle[\"Underline\"] = 3] = \"Underline\";\n  /**\n   * As a thin vertical line (sitting between two characters).\n   */\n  TextEditorCursorStyle[TextEditorCursorStyle[\"LineThin\"] = 4] = \"LineThin\";\n  /**\n   * As an outlined block (sitting on top of a character).\n   */\n  TextEditorCursorStyle[TextEditorCursorStyle[\"BlockOutline\"] = 5] = \"BlockOutline\";\n  /**\n   * As a thin horizontal line (sitting under a character).\n   */\n  TextEditorCursorStyle[TextEditorCursorStyle[\"UnderlineThin\"] = 6] = \"UnderlineThin\";\n})(TextEditorCursorStyle || (TextEditorCursorStyle = {}));\n/**\n * Describes the behavior of decorations when typing/editing near their edges.\n * Note: Please do not edit the values, as they very carefully match `DecorationRangeBehavior`\n */\nexport var TrackedRangeStickiness;\n(function (TrackedRangeStickiness) {\n  TrackedRangeStickiness[TrackedRangeStickiness[\"AlwaysGrowsWhenTypingAtEdges\"] = 0] = \"AlwaysGrowsWhenTypingAtEdges\";\n  TrackedRangeStickiness[TrackedRangeStickiness[\"NeverGrowsWhenTypingAtEdges\"] = 1] = \"NeverGrowsWhenTypingAtEdges\";\n  TrackedRangeStickiness[TrackedRangeStickiness[\"GrowsOnlyWhenTypingBefore\"] = 2] = \"GrowsOnlyWhenTypingBefore\";\n  TrackedRangeStickiness[TrackedRangeStickiness[\"GrowsOnlyWhenTypingAfter\"] = 3] = \"GrowsOnlyWhenTypingAfter\";\n})(TrackedRangeStickiness || (TrackedRangeStickiness = {}));\n/**\n * Describes how to indent wrapped lines.\n */\nexport var WrappingIndent;\n(function (WrappingIndent) {\n  /**\n   * No indentation => wrapped lines begin at column 1.\n   */\n  WrappingIndent[WrappingIndent[\"None\"] = 0] = \"None\";\n  /**\n   * Same => wrapped lines get the same indentation as the parent.\n   */\n  WrappingIndent[WrappingIndent[\"Same\"] = 1] = \"Same\";\n  /**\n   * Indent => wrapped lines get +1 indentation toward the parent.\n   */\n  WrappingIndent[WrappingIndent[\"Indent\"] = 2] = \"Indent\";\n  /**\n   * DeepIndent => wrapped lines get +2 indentation toward the parent.\n   */\n  WrappingIndent[WrappingIndent[\"DeepIndent\"] = 3] = \"DeepIndent\";\n})(WrappingIndent || (WrappingIndent = {}));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}