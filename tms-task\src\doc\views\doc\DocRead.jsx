/* eslint-disable jsx-a11y/anchor-is-valid */
import { ExclamationCircleOutlined, FullscreenExitOutlined } from '@ant-design/icons';
import CommonComment from '@components/CommonComment/CommonComment';
import { TEditorPreview, TMarkdownPreview } from '@components/TEditor/TEditor';
import { useQuerySetting202_getTeamAllUsers } from "@common/service/commonHooks";
import { getUserNameById, isEmpty, twoTimeInterval, checkDate  } from '@common/utils/ArrayUtils';
import { eDocEditOpType } from "@common/utils/enum";
import * as toolUtil from "@common/utils/toolUtil";
import { Button, Drawer, Dropdown, Menu, message, Modal, Popover, Skeleton, Space, Tooltip, Typography } from 'antd';
import { useEffect, useRef, useState } from "react";
import { FullScreen, useFullScreenHandle } from "react-full-screen";
import { Navigate, useLocation, useNavigate, useParams,useAsyncValue } from "react-router-dom";
import GeneralFooter from '@components/GeneralFooter/GeneralFooter';
import SharePopover from '@/share/views/components/SharePopover';
import * as httpDoc from "@common/api/http";
import { useQueryDoc005GetDocDetail, useMutationDOC003GetVersionList } from "@/doc/service/docHooks";
import DocHistory from "./DocHistory";
import { useMutation, useQueries, useQuery, useQueryClient } from "@tanstack/react-query";
import { doc003, doc004, doc005 } from "@common/utils/ApiPath";
import './DocRead.scss';
import { globalUtil } from "@common/utils/globalUtil";
import { setting_320_get_node_priv_query,doc_004_get_tutorial_info_query,doc_005_get_doc_detail_query,team_530_get_obj_node_info_query, setting_get_team_import_setting_query } from "@common/api/query/query"
import { DATE_FORMAT_MICRO } from "@/quickAcess/utils/Config";
import DateUtil from "@common/utils/dateUtil";
import PageTitle from '@components/PageTitle';
import { eNodeTypeId, eNodeType } from "@common/utils/TsbConfig";
import {DocEditDrawer} from "./DocEditDrawer";
import NoviceGuide from '@components/NoviceGuide'; // 文档-新手引导
import { useOutletContext } from "react-router-dom";
import {useThrottle} from "@common/hook";
import * as clipboard from "clipboard-polyfill";
import { getUrl, reftechTeam504GetCommentList, reftechGeneralFooter } from '@common/utils/logicUtils';
import { jsPDF } from "jspdf";
import HTMLtoDOCX from "html-to-docx";
import axios from 'axios';
import jQuery from 'jquery';
import { saveAs } from 'file-saver';
import mammoth from 'mammoth';
import DraggableDrawer from "@components/DraggableDrawer";

const { Title } = Typography;

// 文档查看 
export default function DocRead() {
  const navigate = useNavigate()
  const params = useParams()
  const { teamId, nodeId, nid:objNodeId } = params;
  const location = useLocation();
  //const { team530Result, setting320Result, team504Result, doc005Result } = useAsyncValue();

  const { searchQuery, loading } = useOutletContext() || {};

  // const { nodeType } = team530Result || {}; // 左侧树文档nodeType: 311 和文档库下的文档nodeType 31201, nodeType不一致,在对象关联和被对象关联时需要区分, by bill 2023-03-10
  // console.log("team530Result", team530Result);
  // console.log("nodeType", nodeType);

  const key = objNodeId || nodeId;

  const [visibleVersion, setVisibleVersion] = useState(false);
  const [visibleVersionEdit, setVisibleVersionEdit] = useState(false); //版本历史查看是否显示
  const [visibleBeforeVerison, setVisibleBeforeVerison] = useState(false); //前一个版本按钮是否显示
  const [visibleNextVerison, setVisibleNextVerison] = useState(false); //后一个版本按钮是否显示
  const [currentVersion, setCurrentVersion] = useState(""); //当前版本
  const [lastVersion, setLastVersion] = useState(""); //最新版本
  const [beforeVersion, setBeforeVersion] = useState(""); //前一个版本
  const [nextVersion, setNextVersion] = useState(""); //后一个版本
  const [docVerionList, setDocVerionList] = useState([]); //版本历史内容
  const [versionId, setVersionId] = useState(); // 版本id
  const [docDrawerVisibleFlg,setDocDrawerVisibleFlg] = useState(false);
  const [objNode,setobjNode] = useState({});
  const [tutorialList,setTutorialList] = useState([]);

  // 定义full变量，为的是兼容全屏和非全屏的样式，比如full的时候高度为200，非full高度为100
  const [full, setFull] = useState(false);
  // 创建一个fullScreen的handle
  const handle = useFullScreenHandle();

  const { data: userList, isLoading: isLoadingTeamAllUsers, refetch: refetch202 } = useQuerySetting202_getTeamAllUsers(teamId);
  // const { data : docDetail = {} , isLoading: isLoadingGetDocDetail, refetch: refetchGetDocDetail  } = useQueryDoc005GetDocDetail(teamId, key, versionId, true);

  const {data: setting320Result, isLoading: isLoadingGetNodePriv, refetch: refetchGetNodePriv} = useQuery({
    ...setting_320_get_node_priv_query(teamId,key,!!key)
  })

  const {data: nodeData = {}, isLoading: isLoadingGetObjNodeInfo, refetch: refetchGetObjNodeInfo} = useQuery({
    ...team_530_get_obj_node_info_query(teamId,key)
  })

  const { data : { rootNodeUi, treeKeyList, spaceId } = { rootNodeUi: null, treeKeyList: [] }, dataUpdatedAt: dataUpdatedAtDoc004, refetch: refetchGetTutorialInfo } = useQuery({
    ...doc_004_get_tutorial_info_query(teamId, nodeId, searchQuery, !!objNodeId && !loading)
  })
  const {data: {docDetail}= {}, isLoading: isLoadingGetDocDetail, refetch: refetchGetDocDetail } = useQuery({
    ...doc_005_get_doc_detail_query(teamId, key, versionId)
  })

  const { data: {docImportFlg} = {docImportFlg: 0} } = useQuery({...setting_get_team_import_setting_query(teamId, nodeId)});

  const { onDOC003GetVersionList, isMutating } = useMutationDOC003GetVersionList();
  
  // 文档详情返回的id就是objId, objNodeId就是objNodeId
  const { id: objId, title, content, creatorUid, adminCreatorUid, updateUid, createDt, adminCreateDt, updateDt, docNo } = docDetail || {};

  useEffect(()=>{
    if(dataUpdatedAtDoc004){
      let _docList = docListFormat(rootNodeUi.children || [], []);
      setTutorialList(_docList);
    }
  },[dataUpdatedAtDoc004]);

  function docListFormat(dataList,list){
    dataList.forEach(doc => {
      if(doc.children.length > 0){
        return docListFormat(doc.children,list)
      }else{
        if(doc.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc || doc.nodeType == eNodeTypeId.nt_31202_objtype_docs_excel){
          list.push(doc)
        }
      }
    })
    return list
  }

  // // 滚动至锚点
  // const scrollToAnchor = () => {
  //   setTimeout(() => {
  //     let anchorName = location.hash;
  //     if (anchorName) {
  //       let anchorElement = document.querySelector(anchorName);
  //       if (anchorElement) {
  //         // 添加focus样式
  //         anchorElement.classList.add("focus")
  //         anchorElement.scrollIntoView({
  //           behavior: "smooth",  // 平滑过渡
  //           block: "start"
  //         });
  //       }
  //     }
  //   }, 500);
  // }

  // 编辑
  const onEditBtnClick = () => {
    // const search = !isEmpty(objNodeId) ? `docs=${nodeId}&objNodeId=${objNodeId}&idType=${eDocEditOpType.EDIT_DOC}` : `objNodeId=${nodeId}&idType=${eDocEditOpType.EDIT_DOC}`
    // navigate({
    //   pathname: `/team/${teamId}/docEdit`,
    //   search: search
    // })
    let spaceId = docDetail?.spaceId ? docDetail.spaceId : -999
    setobjNode(!isEmpty(objNodeId) ? {docs: nodeId, objNodeId: objNodeId, idType: eDocEditOpType.EDIT_DOC, spaceId: spaceId} : {objNodeId: nodeId, idType: eDocEditOpType.EDIT_DOC, spaceId: spaceId});
    setDocDrawerVisibleFlg(true);
  }

  // doc-003 get_version_list 获取版本列表
  const getVerionList = () => {
    const request = {
      teamId: teamId,
      objNodeId: key,
    }
    onDOC003GetVersionList(request, {
      onSuccess: (res, vars) => {
          if (res.resultCode == 200) {
            let data = res.versionList;
            changeVersionList(data);
            let lastVersion = !isEmpty(data) ? data[0].versionNo : "0";
            setLastVersion(lastVersion); //最新版本
            setDocVerionList(data);
          }
      },
      onError: (error) => {
          console.log(error)
      }
    });
  }

  // 获取userName
  const changeVersionList = (data) => {
    data.forEach(item => {
      item.key = item.versionId;
      item.userName = getUserNameById(userList, item.creatorUid)
    });
  }

  // 版本历史
  const historyVersionClick = () => {
    getVerionList();
    visibleVersionEditClick(false);
    visibleVersionClick(true);
  }

  // 版本切换
  function versionChange(currentVersion, item) {
    // 点击当前版本，不显示查看历史版本编辑按钮
    setVisibleVersionEdit(true);
    versionViewChange(currentVersion);
    // 关闭历史版本弹窗
    visibleVersionClick(false);
    setVersionId(item.versionId);
  }

  // 前一个，后一个版本信息变化
  function versionViewChange(currentVersion) {
    const dataSource = docVerionList;
    const currentVersionIndex = dataSource.findIndex(
      (item) => item.versionNo === currentVersion
    );
    // 当前version是第一个版本不显示前一个版本
    let visibleBeforeVerison = currentVersionIndex !== dataSource.length - 1;
    // 当前version是最新版本不显示后一个版本
    let visibleNextVerison = currentVersionIndex !== 0;
    let beforeVersion = visibleBeforeVerison ? dataSource[currentVersionIndex + 1].versionNo : "";
    // 后一个版本：如果为当前版本,则显示当前
    let nextVersion = currentVersionIndex - 1 === 0
      ? dataSource[currentVersionIndex - 1].versionNo + " 当前"
      : currentVersionIndex - 1 < 0
        ? ""
        : dataSource[currentVersionIndex - 1].versionNo;
    // 当前版本为最新版本，则隐藏历史版本编辑按钮
    /*  if (currentVersionIndex === 0) {
      visibleVersionEditClick(false);
    } */
    setCurrentVersion(currentVersion)
    setVisibleBeforeVerison(visibleBeforeVerison);
    setVisibleNextVerison(visibleNextVerison);
    setBeforeVersion(beforeVersion);
    setNextVersion(nextVersion);
  }

  // 历史版本编辑按钮
  function visibleVersionEditClick(data) {
    setVisibleVersionEdit(data);
  }

  // 查看历史版本弹窗
  function visibleVersionClick(data) {
    setVisibleVersion(data);
  }

  // 返回最新版本
  function backLastVerison() {
    setCurrentVersion(lastVersion);
    visibleVersionEditClick(false);
    setVersionId(null);
  }

  // 恢复当前版本
  function reverseCurrentVerison() {
    const versionId = findVersionId();
    restoreFromVersionIdConfirm(versionId);
  }

  // 恢复历史版本确认弹窗
  const restoreFromVersionIdConfirm = (versionId) => {
    const text = "是否要恢复该版本？"
    Modal.confirm({
      title: "提醒",
      icon: <ExclamationCircleOutlined />,
      content: text,
      okText: '确认',
      cancelText: '取消',
      onOk: () => { restoreFromVersionId(versionId) }
    });
  }

  // doc-011 restore_doc_from_version 文档版本恢复
  // TODO:恢复历史版本后，左侧树怎么恢复文档名称？
  const restoreFromVersionId = (versionId) => {
    let request = {
      teamId: teamId,
      restoreVersionId: versionId,
      objNodeId: key,
    }
    httpDoc.doc_011_restore_doc_from_version(request).then((res) => {
      if (res.resultCode == 200) {
        // setVersionId();//获取最新文档信息
        // visibleVersionEditClick(false);
        // visibleVersionClick(false);
        globalUtil.success("恢复版本成功"); // message
        navigate({
          pathname: location.pathname,
          search: location.search,
          hash: location.hash,
        },{
          state: {
            refresh: true
          }
        })

      }
    }).catch(e => {
      console.warn(e);
    });
  }

  // 前一个/后一个
  function navigateVersion(isBefore) {
    let currentVersionIndex = docVerionList.findIndex(
      (item) => item.versionNo === currentVersion
    );
    let data = {};
    if (isBefore) {
      data = docVerionList[++currentVersionIndex];
    } else {
      data = docVerionList[--currentVersionIndex];
    }
    const { versionId, versionNo } = data
    setVersionId(versionId)
    versionViewChange(versionNo);
  }

  // 找到versionId
  const findVersionId = () => {
    let currentVersionIndex = docVerionList.findIndex((item) => item.versionNo === currentVersion);
    const { versionId, versionNo } = docVerionList[currentVersionIndex];
    return versionId
  }

  // 全屏
  const fullScreenClick = () => {
    setFull(true);  // 点击设置full为true，接着调用handle的enter方法，进入全屏模式
    handle.enter();
  }

  function describeFormat(creator,createDate,updator,updateDate,realCreateDt){
    if(!!realCreateDt){
      return format(creator,createDate,updator,updateDate,realCreateDt);
    }
    return format(creator,createDate,updator,updateDate);
  }

  function format(creator,createDate,updator,updateDate,realCreateDt){
    if((!!realCreateDt ? realCreateDt : updateDate) != createDate){
      return (
        <>
          <span className="fontcolor-light">由{getUserNameById(userList, creator)}创建</span>
          {/* tmsbug-3404:小于24小时显示时间差，大于24小时显示修改时间 */}
          <span className="fontcolor-light">{getUserNameById(userList, updator)}修改于
            <span>
              { checkDate((!!realCreateDt ? realCreateDt : updateDate), new DateUtil().getNow(DATE_FORMAT_MICRO), 1) ? 
              <span>{twoTimeInterval((!!realCreateDt ? realCreateDt : updateDate), new DateUtil().getNow(DATE_FORMAT_MICRO))}前</span>
              : 
              (!!realCreateDt ? realCreateDt : updateDate)}
            </span>
          </span>
        </>
      );
    }else{
      return (
        <>
          <span className="fontcolor-light">由{getUserNameById(userList, creator)}创建于{realCreateDt ? realCreateDt : createDate}</span>
        </>
      );
    }
  }

  const prevAndNextClick = useThrottle((_nodeId) => {
    navigate(`/team/${teamId}/docs/${nodeId}/doc/${_nodeId}`);
  }, 500, [])

  //复制链接地址
  function copyLink() {
    try {
      const copyUrl = getUrl(teamId, {...nodeData, anchorNodeId: nodeId})
      clipboard.writeText(copyUrl).then(() => {
        globalUtil.success("链接已复制至剪贴板")
      }).catch((err) => {
        globalUtil.error("链接复制失败");
        console.error("Failed to copy text: ", err);
      });
    } catch (e) {
      globalUtil.success("链接复制失败");
    }
  }

  function refetchData () {
    refetchGetDocDetail();
    refetch202();
    reftechTeam504GetCommentList({teamId, nodeId: key});
    reftechGeneralFooter({teamId, nodeId: key, objId: objId, nodeType: nodeData.nodeType },);
  } 

  function escapeHtml(value) {
    return value
        .replace(/&/g, '&amp;')
        .replace(/"/g, '&quot;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
  }

  function readFileInputEventAsArrayBuffer(event, callback) {
    var file = event.target.files[0];

    var reader = new FileReader();

    reader.onload = function(loadEvent) {
        var arrayBuffer = loadEvent.target.result;
        callback(arrayBuffer);
    };

    reader.readAsArrayBuffer(file);
  }

  function displayResult(result) {
    document.querySelector(".docread-body").innerHTML = result.value;

    var messageHtml = result.messages.map(function(message) {
        return '<li class="' + message.type + '">' + escapeHtml(message.message) + "</li>";
    }).join("");

    document.querySelector(".docread-comment").innerHTML = "<ul>" + messageHtml + "</ul>";
  }

  function handleFileSelect(event) {
    readFileInputEventAsArrayBuffer(event, function(arrayBuffer) {
        mammoth.convertToHtml({arrayBuffer: arrayBuffer})
            .then(displayResult, function(error) {
                console.error(error);
            });
    });
  }

  // function initExportHtml() {
    
  // }

  // 是否有编辑权限 当权限privWrite大于0则可以编辑
  const editBtn = (setting320Result?.privWrite||0) > 0?<Button type="primary" className="docread-header-title-right-btn" onClick={onEditBtnClick}>编辑</Button>:<></>

  // 分享
  const shareBtn = (setting320Result?.privWrite||0) > 0?<Popover overlayClassName='sharePopver' destroyTooltipOnHide content={<SharePopover teamId={teamId} nodeId={key} />} title="外链分享" trigger="click">
    <Button title="外链分享" type="text" icon={<span className="iconfont waibufenxiang fontsize-18"></span>} />
  </Popover>:<></>

  const exportBtn = <Dropdown menu={{
      items: [
        { key: 'pdf', label: '导出为PDF' },
        { key: 'word', label: '导出为Word' },
        // { key: 'import', label: 'DOC文件导入' },
      ],
      onClick: async (info) => {
        if(info.key === 'pdf') {
          axios.get('/fonts/simhei.txt')
            .then(result => result.data)
            .then(font => {
              // http://raw.githack.com/MrRio/jsPDF/master/docs/jsPDF.html
              var doc = new jsPDF({
                orientation: "portrait",
                unit: "px",
                format: "a4",
                hotfixes: ["px_scaling"],
              });

              doc.addFileToVFS('Roboto-Regular-normal.ttf', font);
              doc.addFont('Roboto-Regular-normal.ttf', 'Roboto', 'normal');
              doc.setFont('Roboto', 'normal');
              
              let $ = jQuery;
              let $html = $('.docread-body');

              // let cloneHtml = $html.clone();
              $html.find('*').each((index, el) => {
                $(el).css('font-family', '');
              })
              
              $html.width(doc.internal.pageSize.getWidth() - 60);

              doc.html($html[0], {
                margin: [10, 10],
                callback: function (doc) {
                  doc.save(`${docDetail.title}.pdf`);
                  $html.width('100%');
                },
             });

            })
        } else if(info.key === 'word') {
          let $ = jQuery;
          let $html = $('.docread-body').find(".fr-view");

          const htmlString = `<!DOCTYPE html>
            <html lang="en">
              <head>
                <meta charset="UTF-8" />
                <title>Document</title>
              </head>
              <body>
                ${$html.prop('outerHTML')}
              </body>
            </html>`;
          console.log(htmlString);
          
          let fileBuffer = await HTMLtoDOCX(htmlString, null, {
            table: { row: { cantSplit: true } },
            footer: true,
            pageNumber: true,
          })
          saveAs(fileBuffer, `${docDetail.title}.docx`);
        } /* else if(info.key === 'import') {
          let input = document.createElement('input');
          input.type = 'file';
          input.addEventListener('change', handleFileSelect, false)
          input.click();
        } */
      }
    }}>
    <Button type='link'>导出</Button>
  </Dropdown>
  

  return <>
    <div className="docread flex-column-parent" style={{height: "100%"}}>
      <>
          {/* 版本历史查看 */}
          {visibleVersionEdit ? (
            <div className="mock-definition-bar-version">
              <Space size={20} className="ant-page-header-heading">
                <a onClick={() => backLastVerison()}>返回最新版本v{lastVersion}</a>
                {/* 当前版本无需恢复该版本 */}
                {visibleNextVerison && <a onClick={() => reverseCurrentVerison()}>恢复该版本</a>}
                <a
                  onClick={() => {
                    getVerionList();
                    visibleVersionEditClick(false);
                    visibleVersionClick(true);
                  }}
                >
                  查看版本历史
                </a>
              </Space>
              <div className="mock-definition-bar-btns">
                <Space size={25}>
                  {
                    // 没有前一个版本，则不显示
                    visibleBeforeVerison && (
                      <Button
                        className="ant-btn ant-btn-sm ant-btn-primary"
                        onClick={() => navigateVersion(true)}
                      >
                        前一个(v{beforeVersion})
                      </Button>
                    )
                  }
                  <span>v{currentVersion}</span>
                  {
                    visibleNextVerison && (
                      <Button
                        className="ant-btn ant-btn-sm ant-btn-primary"
                        onClick={() => navigateVersion(false)}
                      >
                        下一个(v{nextVersion})
                      </Button>
                    )
                  }

                </Space>
              </div>
            </div>
          ) : null}

          <div className="docread-header">
            <div className="docread-header-title">
              <PageTitle teamId={teamId} nodeId={key} powerLock powerShare={nodeData.nodeType == eNodeTypeId.nt_311_objtype_doc} refetchData={refetchData}/>
              <div className="docread-header-title-right">
                <Space size={5}>
                  {nodeData.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc && (tutorialList?.find(tutorial => tutorial.nodeId == docDetail?.objNodeId)?.prevNodeId ? <a style={{fontSize:12,marginRight:10}} onClick={()=>prevAndNextClick(tutorialList.find(tutorial => tutorial.nodeId == docDetail.objNodeId).prevNodeId)}>{'<上一篇'}</a> : <div style={{fontSize:12,marginRight:10,color:'#CCCCCC'}}>{'<上一篇'}</div>)}
                  {nodeData.nodeType == eNodeTypeId.nt_31201_objtype_docs_doc && (tutorialList?.find(tutorial => tutorial.nodeId == docDetail?.objNodeId)?.nextNodeId ? <a style={{fontSize:12,marginRight:10}} onClick={()=>prevAndNextClick(tutorialList.find(tutorial => tutorial.nodeId == docDetail.objNodeId).nextNodeId)}>{'下一篇>'}</a> : <div style={{fontSize:12,marginRight:10,color:'#CCCCCC'}}>{'下一篇>'}</div>)}
                  {/* 编辑 */}
                  {(docImportFlg != 1 && visibleVersionEdit) ? null : editBtn}
                  {/* 分享 */}
                  {shareBtn}
                  {/* 导出 */}
                  {exportBtn}
                  <Button title="全屏查看" type="text" icon={<span className="iconfont quanping1 fontsize-16"></span>} onClick={() => { fullScreenClick() }} />
                  {/* <Dropdown overlay={moreMenuContent} placement="bottomLeft" arrow> */}
                    <Button title='版本历史' type="text" icon={<span className="iconfont lishiguiji fontsize-16"></span>} onClick={historyVersionClick} />
                  {/* </Dropdown> */}
                </Space>
              </div>
            </div>

            <div className="docread-header-des">
              <Space size={15}>
                <a onClick={() => copyLink()}>{docNo}</a>
                {describeFormat(creatorUid,createDt,updateUid,updateDt,adminCreateDt)}
              </Space>
            </div>
          </div>
          <div className="flex-column-child" id="docRead">
            <FullScreen
              handle={handle}
              onChange={setFull}
              style={{ background: "#ffffff" }}>
              <div className="docread-body" style={{fontFamily: 'Roboto'}}>
                {
                  /* 全屏时显示退出全屏按钮 */
                  full && <Tooltip title="退出全屏" placement="bottom">
                    <FullscreenExitOutlined
                      className="fullscreen-exit"
                      // 退出全屏模式并把full设置为false
                      onClick={() => {
                        setFull(false);
                        handle.exit();
                      }}
                    />
                  </Tooltip>
                }
                {/* refetchGetDocDetail: 文档任务编辑时调用 TMarkdownPreview*/}
                {docDetail?.editorType === 1? <TMarkdownPreview content={docDetail?.content} isedit uploadParams={{teamId}} onRefresh={()=> refetchGetDocDetail()}/>:
                  <TEditorPreview content={docDetail?.content} isedit uploadParams={{teamId}} onRefresh={()=> refetchGetDocDetail()}/>}
              </div>
            </FullScreen>
            {/* objNodeId有值时显示 */}
            {(objId && key) && <GeneralFooter objId={objId} objNodeId={key} objType={nodeData.nodeType} moduleName={eNodeType[nodeData.nodeType]?.nameEn} />}
            {/* id 用于评论锚点，取值objNodeId */}
            <div className="docread-comment" id={`comment-${objNodeId}`}>
              <CommonComment uploadParams={{ teamId, anchorNodeId: nodeId, nodeId: key, moduleName: eNodeType[nodeData.nodeType]?.nameEn, objType: nodeData.nodeType}} showEditorVisible={true}/>
            </div>
          </div>
        </>
      {/* 版本历史 */}
      <DraggableDrawer
        className="tms-drawer "
        width={"65%"}
        onClose={() => visibleVersionClick(false)}
        open={visibleVersion}
        title={title||'' + (!!(title||'') ? ' - 版本历史' : '')}
        closable={true}
        footer={null}>
        <Skeleton loading={isMutating}>
          <DocHistory
            setting320Result={setting320Result}
            currentVersion={currentVersion}
            versionChange={versionChange}
            docVerionList={docVerionList}
            restoreFromVersionIdConfirm={restoreFromVersionIdConfirm}
          />
        </Skeleton>
      </DraggableDrawer>
      {docDrawerVisibleFlg &&
      <DocEditDrawer
          objNode={objNode}
          setDocDrawerVisibleFlg={setDocDrawerVisibleFlg}
          onRefresh={()=> refetchGetDocDetail()} 
          visibleVersionEdit={visibleVersionEdit}
          versionId={versionId}
          _docImportFlg={docImportFlg}
          docDrawerVisibleFlg={docDrawerVisibleFlg}/>
      }
    </div>
    {/* 新手引导 */}
    <NoviceGuide nodeType={nodeData.nodeType} awakeFlg={0}/>
  </>
}