/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2024-02-02 17:53:39
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2025-08-05 17:30:32
 * @Description: 采集口径
 */
/* eslint-disable react-hooks/exhaustive-deps */
import { ExclamationCircleOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';
import * as http from "@common/api/http";
import { useQuerySetting202_getTeamAllUsers, useQuerySetting407_getCodeValueList } from "@common/service/commonHooks";
import { compareArr, isEmpty } from '@common/utils/ArrayUtils';
import { eConsolePropId, eEditingMode, eEnableFlg, eOpType, eSelectionListId, } from "@common/utils/enum";
import { globalEventBus } from "@common/utils/eventBus";
import { globalUtil } from "@common/utils/globalUtil";
import { getQueryableAttrNodeList, transformAttrNodeListForUI, transformCriteriaListForUI, getFlowList } from '@common/utils/logicUtils';
import * as toolUtil from "@common/utils/toolUtil";
import { eNodeTypeId, refreshTeamMenu, refreshSelectTeamMenu, inspCreatePartitionNodeTypeList } from "@common/utils/TsbConfig";
import { useQuery } from "@tanstack/react-query";
import { Button, Drawer, Form, Input, Modal, Space, Checkbox, Select } from "antd";
import { useEffect, useRef, useState, useMemo } from "react";
import { useParams } from "react-router-dom";
import SearchDisplayField from 'src/quickAcess/views/Search/SearchDetail/SearchDisplayField';
import SearchEditTable from "src/quickAcess/views/Search/SearchDetail/SearchEditTable";
import SearchReultPreviewModal from 'src/quickAcess/views/Search/SearchDetail/SearchReultPreviewModal';
import { useQueryTrack019GetPartitionDetail, useQuerySetting409_getTeamAttrgrpProps } from "src/issueTrack/service/issueHooks";
import AddPartitionAttr from "./AddPartitionAttr";
import CustomerFormTable from "./CustomerFormTable";
import { eRegionType } from "src/inspect/utils/enum";
import UploadLoading from "@components/UploadLoading";
import TEmpty from '@components/TEmpty';
import "./CreatePartitionDrawer.scss";
import {insp_091_get_insp_project_detail_query} from "@common/api/query/inspect/query_insp_01_mgmt";
import { useGetInspectRelatedNodes, useQueryCust008GetCustFormList, useQueryCust007GetCustObjList } from "src/inspect/service/inspectHooks";
import DraggableDrawer from "@components/DraggableDrawer";


// 新建&编辑 采集口径
export default function CreatePartitionDrawer() {
  const { teamId } = useParams();
  const [form] = Form.useForm();
  const tRef = useRef(Object.create(null));
  // 弹窗显示配置
  const [isModalVisible, setIsModalVisible] = useState(false); //采集口径弹窗
  const [searchPreFlg, setSearchPreFlg] = useState(false); //是否搜索预览
  const [searchDisplayFieldOpen, setSearchDisplayFieldOpen] = useState(false); //显示字段弹窗
  const [isAddAttrModalVisible, setIsAddAttrModalVisible] = useState(false); //添加字段弹窗
  // 数据配置
  const [nodeItem, setNodeItem] = useState({}); //节点信息
  // const [opType, setOpType] = useState(eEditingMode.Creating_0); //0:新建 1：编辑
  const [selectFields, setSelectFields] = useState([]); //已选择的表单字段
  const [initTableData, setInitTableData] = useState([]); // 初始数据
  const [attrNodeList, setAttrNodeList] = useState([]); // 字段属性列表
  const [checkedValues, setCheckedValues] = useState([]); // 显示字段勾选
  const [modalKey, setModalKey] = useState(toolUtil.guid()); // 显示字段勾选

  const [flowList, setFlowList] = useState([]);                 // 流程图列表
  const [uploadLoading, setUploadLoading] = useState(false); // 提交加载中
  const [showFormFields, setShowFormFields] = useState(true); // 控制表单字段设置的显示

  const opType = useMemo(()=>{
    return inspCreatePartitionNodeTypeList.includes(nodeItem.nodeType) ? eEditingMode.Modifying_1 : eEditingMode.Creating_0  // 如果nodeType为分区节点，则为编辑采集口径
  },[nodeItem])

  const { data: { isCust, isOA }, isLoading: isLoadingInsp072 } = useGetInspectRelatedNodes({teamId, nodeId: nodeItem.nodeId, enabled: !!nodeItem.nodeId,}) 

  // 接口调用
  //（1）获取项目信息和可发起流程
  const { data: projectInfo = {}, isLoading: isLoadingInsp091, dataUpdatedAt: dataUpdatedAtInsp091 } = useQuery(insp_091_get_insp_project_detail_query(teamId, nodeItem?.nodeId, !!nodeItem?.nodeId))
  
  // 获取自定义表单列表
  const { data: customFormData = {}, isLoading: isLoadingCust008 } = useQueryCust008GetCustFormList({teamId, nodeId: nodeItem.nodeId, enabled: !!isCust && !!nodeItem.nodeId});
  
  // 定义模式相关配置
  const modeConfig = {
    custom: {
      fieldName: 'nodeId',
      label: '选择表单',
      getSelectedItem: (id) => customFormData?.formList?.find(form => form.nodeId == id),
      getItemList: () => customFormData?.formList?.map(form => ({
        key: form.nodeId,
        value: form.nodeId,
        label: form.formName,
        // icon: form.subclassIcon
      })) || [],
      getDefaultKey: (list) => list.length > 0 ? list[0].key : undefined,
      getRevertKey: (item) => item?.nodeId,
      objType: eNodeTypeId.nt_871_cust_objlist,
      paramKey: 'processdefNodeId'
    },
    normal: {
      fieldName: 'processdefNodeId',
      label: '选择流程',
      getSelectedItem: (id) => projectInfo?.flowList?.filter?.(flow => flow.enableFlg == eEnableFlg.enable)?.find(flow => flow.processdefNodeId == id),
      getItemList: () => {
        const [flowList] = getFlowList(projectInfo?.flowList || [], selectionList);
        return flowList;
      },
      getDefaultKey: (list, defaultFlow) => defaultFlow?.key,
      getRevertKey: (item) => item?.processdefNodeId,
      objType: isOA ? eNodeTypeId.nt_671_oa_processList : eNodeTypeId.nt_571_insp_processList, 
      paramKey: 'processdefNodeId'
    }
  };
  
  // 当前模式配置
  const currentMode = useMemo(()=>{
    return isCust ? modeConfig.custom : modeConfig.normal
  },[isCust, modeConfig]);

  const processdefNodeId = Form.useWatch(currentMode.fieldName, form);
  const selecedtProcess = useMemo(() => 
    currentMode.getSelectedItem(processdefNodeId)
  ,[projectInfo, customFormData, processdefNodeId, currentMode]) 
  
  //（2）获取自定义表单字段
  const { subclassAttrList = [], isLoading: isLoadingGetSubclassAttrs, dataUpdatedAt: dataUpdatedAtSetting409 } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeItem?.nodeId, selecedtProcess?.subclassId, !!selecedtProcess?.subclassId);

  // (2.1) 编辑时获取采集口径详情 TODO: useQuery cacheTime 需要在相关key变化时才会清除缓存数据，所以需要一个变量key去触发再次打开的更新操作
  const { attrList, criteriaList=[], bizNodeId, objType, createFlg, dataUpdatedAt: dataUpdatedAtTrack019, refetch: refetchGetPartitionDetail } = useQueryTrack019GetPartitionDetail(teamId, nodeItem?.nodeId, modalKey, opType == eEditingMode.Modifying_1 && !!nodeItem?.nodeId);
  //（3）字典数据
  const { data: selectionList } = useQuerySetting407_getCodeValueList(teamId);
  //（4）获取人员列表
  const { data: userList } = useQuerySetting202_getTeamAllUsers(teamId);

  // 选择样式
  const selectStyle = { width: 300, borderRadius: 5 };

  const isLoading  = isLoadingInsp091 || isLoadingGetSubclassAttrs || (!!isCust && isLoadingCust008 ) || uploadLoading

  // 监听打开新建/编辑采集口径弹窗
  useEffect(() => {
    globalEventBus.on("openCreatePartitionDrawerEvent", openCreatePartitionDrawerEvent)
    return () => globalEventBus.off("openCreatePartitionDrawerEvent", openCreatePartitionDrawerEvent)
  }, [])

  // 打开创建项目弹窗事件
  const openCreatePartitionDrawerEvent = (target, args) => {
    setModalKey(toolUtil.guid());
    setNodeItem(args);
    setIsModalVisible(true);
  }

  // 打开弹窗/关闭弹窗
  useEffect(() => {
    if (isModalVisible && opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtSetting409) {
      // 加载数据
      loadIssuePartitionDetail()
    } else {
      // 初始化数据
      initialIssuePartitionData()
    }
  }, [isModalVisible, dataUpdatedAtTrack019, dataUpdatedAtSetting409])

  // 打开搜索预览弹窗
  const openSearchReultPreviewModal = (requst) => {
    globalEventBus.emit("openSearchReultPreviewModalEvent", "",
      { searchPreRequest: requst, onDisplayClick: onDisplayClick.bind(this) });
  }

  // 自定义表单字段
  useEffect(() => {
    if (!isLoadingGetSubclassAttrs) {
      debugger
      changePropertyTypeList(subclassAttrList)
    }
  }, [isLoadingGetSubclassAttrs])

  // 编辑 自定义表单
  useEffect(() => {
    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && !isEmpty(attrNodeList)) {
      changeCriteriaList()
    }
  }, [opType, dataUpdatedAtTrack019, JSON.stringify(attrNodeList) ])

  // 编辑 回显流程 注意有先后依赖关系不可合并
  useEffect(() => {
    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtInsp091) {
      form.setFieldValue(currentMode.fieldName, bizNodeId)
    }
  }, [opType, dataUpdatedAtTrack019, dataUpdatedAtInsp091, JSON.stringify(currentMode)])

  useEffect(() => {
    if (isModalVisible) {
      const itemList = currentMode.getItemList();
      setFlowList(itemList);
      
      if (opType == eEditingMode.Creating_0) {
        const defaultFlow = isCust ? null : getFlowList(projectInfo?.flowList || [], selectionList)[1];
        const defaultKey = currentMode.getDefaultKey(itemList, defaultFlow);
        if (defaultKey) {
          form.setFieldValue(currentMode.fieldName, defaultKey);
        }
      }
    }
  }, [isModalVisible, JSON.stringify(projectInfo), isCust, JSON.stringify(currentMode)]);

  // 自定义表单数据处理
  const changePropertyTypeList = (attrgrpProps) => {
    let attrList = transformAttrNodeListForUI(attrgrpProps, userList, selectionList, true);
    const checkedValues = attrList.filter(item => item.checked).map(item => item.nodeId);
    const properTypeListUI = getQueryableAttrNodeList(attrList); //过滤59类型
    setAttrNodeList(properTypeListUI);
    setCheckedValues(checkedValues);
  }

  // 编辑搜索数据
  const changeCriteriaList = () => {
    let _criteriaList = (criteriaList || []).filter(criteria => attrNodeList.some(subclass => criteria.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17
     _criteriaList = transformCriteriaListForUI(_criteriaList, attrNodeList, selectionList);
    setInitTableData(_criteriaList);
  }

  // 初始化数据
  function initialIssuePartitionData() {
    // console.log("正在清空数据...")
    setInitTableData([]); //清空高级搜索数据
    setSelectFields([]); //清空字段属性数据
    setShowFormFields(true); //重置表单字段设置显示状态
  }

  // 加载采集口径详情
  function loadIssuePartitionDetail() {
    const isCreateEnabled = createFlg == eEnableFlg.enable;
    form.setFieldsValue({
      name: nodeItem.name, // name回显
      createFlg: isCreateEnabled
    });

    // 根据createFlg设置表单字段设置的显示状态
    setShowFormFields(isCreateEnabled);

    const _attrList = attrList.filter(attr => subclassAttrList.some(subclass => attr.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17
    _attrList.forEach((attr) => {
      const propertyList = subclassAttrList.find(subclass => attr.attrNid == subclass.nodeId).propertyList;
      attr.attrModifyableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);
      attr.attrQueryableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);
      attr.uiControl = (propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue;
      attr.selectionId = propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)?.propValue || "";
    });
    setSelectFields(_attrList)
  }

  // 默认值配置
  const getDefAttrPropValuepByType = (propList = [], type) => {
    return (propList.find((item) => item.propType == type) || { propValue: "0" }).propValue;
  }

  // 取消
  const handleCancel = () => {
   return Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleOutlined />,
      content: <p>{ opType == eOpType.add ? '正在新建采集口径，是否放弃编辑?' : '正在编辑采集口径，是否放弃编辑?'}</p>,
      okText: "确定",
      cancelText: "取消",
      onOk: () => {
         setIsModalVisible(false)
      },
      onCancel: () => {
        console.log("Cancel");
      },
    });
  }

  // 选择字段数据处理
  const onSelectFields = (items) => {
    let fieldList = []
    selectFields.filter(el => {
      items.map((_el, index) => {
        if (el.attrNid == _el.nodeId) {
          fieldList.push(el)
          delete items[index]
        }
      })
    })
    items.map((item) => {
      let propertyList = item.propertyList
      const _attrModifyableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);
      const _attrQueryableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);
      let field = {
        attrNid: item.nodeId,
        defaultVal: null,
        attrVisibleFlg: '',
        attrModifyableFlg: _attrModifyableFlg,
        attrModifyableFlgBack: _attrModifyableFlg,
        attrQueryableFlg: _attrQueryableFlg,
        attrQueryableFlgBack: _attrQueryableFlg,
        uiControl: (propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue,
        selectionId: propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)?.propValue || "",
        isRegion: propertyList.find(el => el.propType == eConsolePropId.Prop_28_is_region)?.propValue || "",      // 是否是检查区域字段
      }
      fieldList.push(field)
    })
    setSelectFields(fieldList)
    setIsAddAttrModalVisible(false)
  }

  // + 动态条件
  const handleOnAddSearchCode = () => {
    tRef.current.addSearchCode();
  }

  // 搜索预览 无需校验搜索名称
  const handleSearchPreClick = () => {
    setSearchPreFlg(true);
    form.submit();
  }

  // 显示字段
  const onDisplayClick = (e) => {
    setSearchDisplayFieldOpen(true);
  };

  // 显示字段数据处理
  const assembleQueryAttrList = () => {
    return checkedValues.map(checkedValue => ({ attrNid: checkedValue }));
  }

  // 保存显示字段
  const handleSearchDisplayFieldOnOk = (e, values) => {
    setSearchDisplayFieldOpen(false);
    setCheckedValues(values);
    globalEventBus.emit("changeSearchReultPreviewEvent", "", { values }) // 根据显示字段过滤
  }

  // 取消保存字段
  const handleSearchDisplayFieldOnCancel = (e, values) => {
    if (!compareArr(checkedValues, values)) {
      Modal.confirm({
        title: '提醒',
        icon: <ExclamationCircleOutlined />,
        content: "放弃后将不会保存显示字段，确定要放弃？",
        okText: '确定',
        cancelText: '取消',
        onOk: () => { setSearchDisplayFieldOpen(false); }
      });
    } else {
      setSearchDisplayFieldOpen(false);
    }
  };

  // 点击 确定
  const handleSaveClick = () => {
    setSearchPreFlg(false);
    form.submit();
  }

  // 表单提交 form.submit()
  const onFinish = async (values) => {
    const { name } = values;
    if (!searchPreFlg && !name) {
      // 校验名称
      return globalUtil.warning("请填写采集口径名称!")
    }
    let criteriaList = tRef?.current?.getCriteriaListForBackend?.() || []
    // 搜索预览
    if (searchPreFlg) {
      const queryAttrList = assembleQueryAttrList();
      let requst = {
        "teamId": teamId, "bizNodeId": values[currentMode.fieldName], "name": name, "objType": currentMode.objType,
        "advanceQueryFlg": "1", "criteriaList": criteriaList, "queryAttrList": queryAttrList
      }
      // 搜索预览
      requst = { ...requst, pageNum: 1 } //默认查询第一页
      return openSearchReultPreviewModal(requst);
    }
    setUploadLoading(true);
    // TODO: 检查项后端校验存在问题,先固定传null by walt from jack  2024-02-05
    selectFields.forEach((el, index) => {
      if (el.isRegion == eRegionType.check) {
        el.defaultVal = null;
      }
    });
    //  后端只需要这些参数，去除多余的不接受的参数
    let _selectFields = selectFields.map((el, index) => ({
      attrNid: el.attrNid,
      defaultVal: el.defaultVal,
      attrVisibleFlg: el.attrVisibleFlg,
      attrModifyableFlg: el.attrModifyableFlg,
      attrModifyableFlgBack: el.attrModifyableFlgBack,
      attrQueryableFlg: el.attrQueryableFlg,
      attrQueryableFlgBack: el.attrQueryableFlgBack,
      seqNo: index + 1,
    }))
    
    // 基础参数
    let params = { teamId, name, attrList: _selectFields, criteriaList, createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable };
    
    // 根据操作类型添加不同的参数
    if (opType) {
      // 编辑模式
      params.nodeId = nodeItem.nodeId;
      params[currentMode.paramKey] = values[currentMode.fieldName];
      await editPartition(params);
    } else {
      // 新建模式
      params.objNodeId = nodeItem.nodeId;
      params[currentMode.paramKey] = values[currentMode.fieldName];
      await createPartition(params);
    }
    
    setUploadLoading(false);
  };

  // 新建采集口径
  async function createPartition(params) {
    await http.track_018_create_issue_partition(params).then(result => {
      if (result.resultCode == 200) {
        refreshSelectTeamMenu({ treeNode: result?.nodeTree[0] || {}})
        setIsModalVisible(false);
        nodeItem.callback && nodeItem.callback(result?.nodeTree[0]?.children[0] || {});
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 编辑采集口径
  async function editPartition(params) {
    await http.track_020_modify_issue_partition(params).then(result => {
      if (result.resultCode == 200) {
        refreshTeamMenu();
        setIsModalVisible(false);
        refetchGetPartitionDetail(); // 编辑采集口径后刷新数据
      }
    }).catch(err => {
      console.log(err)
    })
  }

  const handleOnChange = (value) => {
    let criteriaList = tRef?.current?.getCriteriaListForBackend && tRef?.current?.getCriteriaListForBackend?.()
    if (isEmpty(criteriaList) && isEmpty(selectFields)) {
      return
    }
    return Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleOutlined />,
      content: <p>{`重新选择${isCust ? '表单' : '流程'}，下方两个表格将清空，您需要重新设置。是否继续?`}</p>,
      okText: "是",
      cancelText: "否",
      zIndex: 1002, // FIXME:不能超过10000，否则会导致Select下拉框被覆盖
      onOk: () => {
      },
      onCancel: () => {
        console.log("Cancel");
        form.setFieldValue(currentMode.fieldName, currentMode.getRevertKey(selecedtProcess))
      },
    });
  }

  // 处理"可新建"复选框变化
  const handleCreateFlgChange = (e) => {
    const checked = e.target.checked;

    // 如果取消勾选且表单字段设置有值，弹出确认对话框
    if (!checked && !isEmpty(selectFields)) {
      Modal.confirm({
        title: "提示",
        icon: <ExclamationCircleOutlined />,
        content: `取消"可新建"功能，会清除表单字段设置，是否取消？`,
        okText: "取消",
        cancelText: "不取消",
        zIndex: 1002,
        onOk: () => {
          // 用户确认取消，清除表单字段设置并隐藏
          setSelectFields([]);
          setShowFormFields(false);
          form.setFieldValue('createFlg', false);
        },
        onCancel: () => {
          // 用户选择不取消，恢复勾选状态
          form.setFieldValue('createFlg', true);
        },
      });
    } else {
      // 如果重新勾选，显示表单字段设置
      setShowFormFields(checked);
    } 
  }

  return <DraggableDrawer
    className="tms-drawer IssuePartition"
    width={"60%"}
    title={opType ? `采集口径设置` : `新建采集口径`}
    destroyOnClose={true}
    open={isModalVisible}
    onClose={handleCancel}
    footer={<div style={{ textAlign: "right" }} >
      <Space size={20}>
        <Button style={{ borderRadius: 5 }} onClick={handleCancel}>取消</Button>
        <Button type="primary" style={{ borderRadius: 5 }} onClick={handleSaveClick}>提交</Button>
      </Space>
    </div>}
  >
    <UploadLoading spinning={isLoading}>
      <Form
        form={form}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 19 }}
        onFinish={onFinish}
        preserve={false}// Modal关闭后销毁form字段数据
        autoComplete={"off"} // 取消自动补充功能
        initialValues={{
          createFlg: true,
        }}
      >
        <Form.Item label={"采集口径名称"} name="name" required={true}>
          <Input style={selectStyle} autoComplete="off" />
        </Form.Item>
        <Form.Item label={currentMode.label}>
          <Space size={20}>
            <Form.Item name={currentMode.fieldName} required={true} noStyle>
              <Select
                showSearch
                style={{ width: 300, borderRadius: 3 }}
                //select选择框搜索
                filterOption={(input, option) => {
                  return (option.children.props.children[1].props.children || "").toLowerCase().indexOf(input.toLowerCase()) >= 0
                }}
                onChange={handleOnChange}
              >
                {
                  flowList.map(flow => (<Select.Option key={flow.key} value={flow.value} >
                    <div style={{ display: "flex", alignItems: "center" }}>
                      {flow.icon}
                      <span style={{ paddingLeft: 5 }}>{flow.label}</span>
                    </div>
                  </Select.Option>))
                }
              </Select>
            </Form.Item>
            <Form.Item name="createFlg" valuePropName="checked" noStyle>
              <Checkbox onChange={handleCreateFlgChange}>可新建</Checkbox>
            </Form.Item>
          </Space>
        </Form.Item>
        <Form.Item label={"自定义搜索"}>
          {isEmpty(attrNodeList) ? <TEmpty/> :
            <div>
              <SearchEditTable attrNodeList={attrNodeList} exprs={[]} criteriaList={initTableData} selectionList={selectionList} queryType={0} ref={tRef} />
              <div className="search-edit-btns">
                <Button
                  type="link"
                  icon={
                    <QuestionCircleOutlined className="color-yellow" />
                  }
                  onClick={handleOnAddSearchCode}
                >
                  <span>+ 动态条件（<FunctionOutlined style={{ margin: 0 }} />）</span>
                </Button>
                <Button icon={<SearchOutlined />} className="defaultBtn_light" onClick={handleSearchPreClick} >结果预览</Button>
              </div>
              <div className="remarks">备注1：此处的自定义搜索，用于显示当前采集口径对应的{projectInfo.issueAlias ?? ""}列表。</div>
              <div className="remarks">备注2：点击确定按钮仅保存表单(即条件)，预览结果不做保存。</div>
            </div>
          }
        </Form.Item>
        {showFormFields && (
          <Form.Item label={"表单字段设置"}>
            <CustomerFormTable selectFields={selectFields} setSelectFields={setSelectFields} selectionList={selectionList} userList={userList} subclassAttrList={subclassAttrList} selecedtProcess={selecedtProcess} />
            <a className="fontsize-12" onClick={() => setIsAddAttrModalVisible(true)}>+ 添加字段</a>
            <div className="remarks">备注1：在{projectInfo.issueAlias ?? ""}项目"{projectInfo.name}"的自定义字段的基础上，进一步限缩上面表格中字段的是否显示/可修改/可搜索。</div>
            <div className="remarks">备注2：在当前采集口径中提交的新建{projectInfo.issueAlias ?? ""}，其对应上面表格中的字段，如果值为空，则使用"缺省值"列中的值，进行最后的数据存储。</div>
          </Form.Item>
        )}
      </Form>
    </UploadLoading>
    {/* 添加字段 */}
    <AddPartitionAttr selectFields={selectFields} attrList={subclassAttrList} visible={isAddAttrModalVisible} onSelectFields={onSelectFields} onCancel={() => setIsAddAttrModalVisible(false)} />
    {/* 搜索结果预览 */}
    <SearchReultPreviewModal />
    {/* 显示字段 */}
    <SearchDisplayField
      open={searchDisplayFieldOpen}
      onOk={handleSearchDisplayFieldOnOk}
      onCancel={handleSearchDisplayFieldOnCancel}
      nodeName={nodeItem?.nodeName}
      checkedValues={checkedValues}
      attrNodeList={attrNodeList}
    />
  </DraggableDrawer>
}