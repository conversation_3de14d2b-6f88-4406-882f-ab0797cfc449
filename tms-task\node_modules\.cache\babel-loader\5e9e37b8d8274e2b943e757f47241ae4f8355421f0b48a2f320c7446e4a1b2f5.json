{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nlet isPseudo = typeof document !== 'undefined' && document.location && document.location.hash.indexOf('pseudo=true') >= 0;\nconst DEFAULT_TAG = 'i-default';\nfunction _format(message, args) {\n  let result;\n  if (args.length === 0) {\n    result = message;\n  } else {\n    result = message.replace(/\\{(\\d+)\\}/g, (match, rest) => {\n      const index = rest[0];\n      const arg = args[index];\n      let result = match;\n      if (typeof arg === 'string') {\n        result = arg;\n      } else if (typeof arg === 'number' || typeof arg === 'boolean' || arg === void 0 || arg === null) {\n        result = String(arg);\n      }\n      return result;\n    });\n  }\n  if (isPseudo) {\n    // FF3B and FF3D is the Unicode zenkaku representation for [ and ]\n    result = '\\uFF3B' + result.replace(/[aouei]/g, '$&$&') + '\\uFF3D';\n  }\n  return result;\n}\nfunction findLanguageForModule(config, name) {\n  let result = config[name];\n  if (result) {\n    return result;\n  }\n  result = config['*'];\n  if (result) {\n    return result;\n  }\n  return null;\n}\nfunction endWithSlash(path) {\n  if (path.charAt(path.length - 1) === '/') {\n    return path;\n  }\n  return path + '/';\n}\nfunction getMessagesFromTranslationsService(translationServiceUrl, language, name) {\n  return __awaiter(this, void 0, void 0, function* () {\n    const url = endWithSlash(translationServiceUrl) + endWithSlash(language) + 'vscode/' + endWithSlash(name);\n    const res = yield fetch(url);\n    if (res.ok) {\n      const messages = yield res.json();\n      return messages;\n    }\n    throw new Error(`${res.status} - ${res.statusText}`);\n  });\n}\nfunction createScopedLocalize(scope) {\n  return function (idx, defaultValue) {\n    const restArgs = Array.prototype.slice.call(arguments, 2);\n    return _format(scope[idx], restArgs);\n  };\n}\nexport function localize(data, message) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  return _format(message, args);\n}\nexport function getConfiguredDefaultLocale(_) {\n  // This returns undefined because this implementation isn't used and is overwritten by the loader\n  // when loaded.\n  return undefined;\n}\nexport function setPseudoTranslation(value) {\n  isPseudo = value;\n}\n/**\n * Invoked in a built product at run-time\n */\nexport function create(key, data) {\n  var _a;\n  return {\n    localize: createScopedLocalize(data[key]),\n    getConfiguredDefaultLocale: (_a = data.getConfiguredDefaultLocale) !== null && _a !== void 0 ? _a : _ => undefined\n  };\n}\n/**\n * Invoked by the loader at run-time\n */\nexport function load(name, req, load, config) {\n  var _a;\n  const pluginConfig = (_a = config['vs/nls']) !== null && _a !== void 0 ? _a : {};\n  if (!name || name.length === 0) {\n    return load({\n      localize: localize,\n      getConfiguredDefaultLocale: () => {\n        var _a;\n        return (_a = pluginConfig.availableLanguages) === null || _a === void 0 ? void 0 : _a['*'];\n      }\n    });\n  }\n  const language = pluginConfig.availableLanguages ? findLanguageForModule(pluginConfig.availableLanguages, name) : null;\n  const useDefaultLanguage = language === null || language === DEFAULT_TAG;\n  let suffix = '.nls';\n  if (!useDefaultLanguage) {\n    suffix = suffix + '.' + language;\n  }\n  const messagesLoaded = messages => {\n    if (Array.isArray(messages)) {\n      messages.localize = createScopedLocalize(messages);\n    } else {\n      messages.localize = createScopedLocalize(messages[name]);\n    }\n    messages.getConfiguredDefaultLocale = () => {\n      var _a;\n      return (_a = pluginConfig.availableLanguages) === null || _a === void 0 ? void 0 : _a['*'];\n    };\n    load(messages);\n  };\n  if (typeof pluginConfig.loadBundle === 'function') {\n    pluginConfig.loadBundle(name, language, (err, messages) => {\n      // We have an error. Load the English default strings to not fail\n      if (err) {\n        req([name + '.nls'], messagesLoaded);\n      } else {\n        messagesLoaded(messages);\n      }\n    });\n  } else if (pluginConfig.translationServiceUrl && !useDefaultLanguage) {\n    (() => __awaiter(this, void 0, void 0, function* () {\n      var _b;\n      try {\n        const messages = yield getMessagesFromTranslationsService(pluginConfig.translationServiceUrl, language, name);\n        return messagesLoaded(messages);\n      } catch (err) {\n        // Language is already as generic as it gets, so require default messages\n        if (!language.includes('-')) {\n          console.error(err);\n          return req([name + '.nls'], messagesLoaded);\n        }\n        try {\n          // Since there is a dash, the language configured is a specific sub-language of the same generic language.\n          // Since we were unable to load the specific language, try to load the generic language. Ex. we failed to find a\n          // Swiss German (de-CH), so try to load the generic German (de) messages instead.\n          const genericLanguage = language.split('-')[0];\n          const messages = yield getMessagesFromTranslationsService(pluginConfig.translationServiceUrl, genericLanguage, name);\n          // We got some messages, so we configure the configuration to use the generic language for this session.\n          (_b = pluginConfig.availableLanguages) !== null && _b !== void 0 ? _b : pluginConfig.availableLanguages = {};\n          pluginConfig.availableLanguages['*'] = genericLanguage;\n          return messagesLoaded(messages);\n        } catch (err) {\n          console.error(err);\n          return req([name + '.nls'], messagesLoaded);\n        }\n      }\n    }))();\n  } else {\n    req([name + suffix], messagesLoaded, err => {\n      if (suffix === '.nls') {\n        console.error('Failed trying to load default language strings', err);\n        return;\n      }\n      console.error(`Failed to load message bundle for language ${language}. Falling back to the default language:`, err);\n      req([name + '.nls'], messagesLoaded);\n    });\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}