{"ast": null, "code": "import { onUnexpectedError } from './errors.js';\nimport { combinedDisposable, Disposable, DisposableStore, SafeDisposable, toDisposable } from './lifecycle.js';\nimport { LinkedList } from './linkedList.js';\nimport { StopWatch } from './stopwatch.js';\n// -----------------------------------------------------------------------------------------------------------------------\n// Uncomment the next line to print warnings whenever an emitter with listeners is disposed. That is a sign of code smell.\n// -----------------------------------------------------------------------------------------------------------------------\nconst _enableDisposeWithListenerWarning = false;\n// _enableDisposeWithListenerWarning = Boolean(\"TRUE\"); // causes a linter warning so that it cannot be pushed\n// -----------------------------------------------------------------------------------------------------------------------\n// Uncomment the next line to print warnings whenever a snapshotted event is used repeatedly without cleanup.\n// See https://github.com/microsoft/vscode/issues/142851\n// -----------------------------------------------------------------------------------------------------------------------\nconst _enableSnapshotPotentialLeakWarning = false;\nexport var Event;\n(function (Event) {\n  Event.None = () => Disposable.None;\n  function _addLeakageTraceLogic(options) {\n    if (_enableSnapshotPotentialLeakWarning) {\n      const {\n        onListenerDidAdd: origListenerDidAdd\n      } = options;\n      const stack = Stacktrace.create();\n      let count = 0;\n      options.onListenerDidAdd = () => {\n        if (++count === 2) {\n          console.warn('snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here');\n          stack.print();\n        }\n        origListenerDidAdd === null || origListenerDidAdd === void 0 ? void 0 : origListenerDidAdd();\n      };\n    }\n  }\n  /**\n   * Given an event, returns another event which only fires once.\n   */\n  function once(event) {\n    return function (listener) {\n      let thisArgs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      let disposables = arguments.length > 2 ? arguments[2] : undefined;\n      // we need this, in case the event fires during the listener call\n      let didFire = false;\n      let result = undefined;\n      result = event(e => {\n        if (didFire) {\n          return;\n        } else if (result) {\n          result.dispose();\n        } else {\n          didFire = true;\n        }\n        return listener.call(thisArgs, e);\n      }, null, disposables);\n      if (didFire) {\n        result.dispose();\n      }\n      return result;\n    };\n  }\n  Event.once = once;\n  /**\n   * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n   * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n   * returned event causes this utility to leak a listener on the original event.\n   */\n  function map(event, map, disposable) {\n    return snapshot(function (listener) {\n      let thisArgs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      let disposables = arguments.length > 2 ? arguments[2] : undefined;\n      return event(i => listener.call(thisArgs, map(i)), null, disposables);\n    }, disposable);\n  }\n  Event.map = map;\n  /**\n   * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n   * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n   * returned event causes this utility to leak a listener on the original event.\n   */\n  function forEach(event, each, disposable) {\n    return snapshot(function (listener) {\n      let thisArgs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      let disposables = arguments.length > 2 ? arguments[2] : undefined;\n      return event(i => {\n        each(i);\n        listener.call(thisArgs, i);\n      }, null, disposables);\n    }, disposable);\n  }\n  Event.forEach = forEach;\n  function filter(event, filter, disposable) {\n    return snapshot(function (listener) {\n      let thisArgs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      let disposables = arguments.length > 2 ? arguments[2] : undefined;\n      return event(e => filter(e) && listener.call(thisArgs, e), null, disposables);\n    }, disposable);\n  }\n  Event.filter = filter;\n  /**\n   * Given an event, returns the same event but typed as `Event<void>`.\n   */\n  function signal(event) {\n    return event;\n  }\n  Event.signal = signal;\n  function any() {\n    for (var _len = arguments.length, events = new Array(_len), _key = 0; _key < _len; _key++) {\n      events[_key] = arguments[_key];\n    }\n    return function (listener) {\n      let thisArgs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      let disposables = arguments.length > 2 ? arguments[2] : undefined;\n      return combinedDisposable(...events.map(event => event(e => listener.call(thisArgs, e), null, disposables)));\n    };\n  }\n  Event.any = any;\n  /**\n   * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n   * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n   * returned event causes this utility to leak a listener on the original event.\n   */\n  function reduce(event, merge, initial, disposable) {\n    let output = initial;\n    return map(event, e => {\n      output = merge(output, e);\n      return output;\n    }, disposable);\n  }\n  Event.reduce = reduce;\n  function snapshot(event, disposable) {\n    let listener;\n    const options = {\n      onFirstListenerAdd() {\n        listener = event(emitter.fire, emitter);\n      },\n      onLastListenerRemove() {\n        listener === null || listener === void 0 ? void 0 : listener.dispose();\n      }\n    };\n    if (!disposable) {\n      _addLeakageTraceLogic(options);\n    }\n    const emitter = new Emitter(options);\n    disposable === null || disposable === void 0 ? void 0 : disposable.add(emitter);\n    return emitter.event;\n  }\n  function debounce(event, merge) {\n    let delay = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 100;\n    let leading = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n    let leakWarningThreshold = arguments.length > 4 ? arguments[4] : undefined;\n    let disposable = arguments.length > 5 ? arguments[5] : undefined;\n    let subscription;\n    let output = undefined;\n    let handle = undefined;\n    let numDebouncedCalls = 0;\n    const options = {\n      leakWarningThreshold,\n      onFirstListenerAdd() {\n        subscription = event(cur => {\n          numDebouncedCalls++;\n          output = merge(output, cur);\n          if (leading && !handle) {\n            emitter.fire(output);\n            output = undefined;\n          }\n          clearTimeout(handle);\n          handle = setTimeout(() => {\n            const _output = output;\n            output = undefined;\n            handle = undefined;\n            if (!leading || numDebouncedCalls > 1) {\n              emitter.fire(_output);\n            }\n            numDebouncedCalls = 0;\n          }, delay);\n        });\n      },\n      onLastListenerRemove() {\n        subscription.dispose();\n      }\n    };\n    if (!disposable) {\n      _addLeakageTraceLogic(options);\n    }\n    const emitter = new Emitter(options);\n    disposable === null || disposable === void 0 ? void 0 : disposable.add(emitter);\n    return emitter.event;\n  }\n  Event.debounce = debounce;\n  /**\n   * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n   * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n   * returned event causes this utility to leak a listener on the original event.\n   */\n  function latch(event) {\n    let equals = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (a, b) => a === b;\n    let disposable = arguments.length > 2 ? arguments[2] : undefined;\n    let firstCall = true;\n    let cache;\n    return filter(event, value => {\n      const shouldEmit = firstCall || !equals(value, cache);\n      firstCall = false;\n      cache = value;\n      return shouldEmit;\n    }, disposable);\n  }\n  Event.latch = latch;\n  /**\n   * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n   * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n   * returned event causes this utility to leak a listener on the original event.\n   */\n  function split(event, isT, disposable) {\n    return [Event.filter(event, isT, disposable), Event.filter(event, e => !isT(e), disposable)];\n  }\n  Event.split = split;\n  /**\n   * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n   * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n   * returned event causes this utility to leak a listener on the original event.\n   */\n  function buffer(event) {\n    let flushAfterTimeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let _buffer = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    let buffer = _buffer.slice();\n    let listener = event(e => {\n      if (buffer) {\n        buffer.push(e);\n      } else {\n        emitter.fire(e);\n      }\n    });\n    const flush = () => {\n      buffer === null || buffer === void 0 ? void 0 : buffer.forEach(e => emitter.fire(e));\n      buffer = null;\n    };\n    const emitter = new Emitter({\n      onFirstListenerAdd() {\n        if (!listener) {\n          listener = event(e => emitter.fire(e));\n        }\n      },\n      onFirstListenerDidAdd() {\n        if (buffer) {\n          if (flushAfterTimeout) {\n            setTimeout(flush);\n          } else {\n            flush();\n          }\n        }\n      },\n      onLastListenerRemove() {\n        if (listener) {\n          listener.dispose();\n        }\n        listener = null;\n      }\n    });\n    return emitter.event;\n  }\n  Event.buffer = buffer;\n  class ChainableEvent {\n    constructor(event) {\n      this.event = event;\n      this.disposables = new DisposableStore();\n    }\n    map(fn) {\n      return new ChainableEvent(map(this.event, fn, this.disposables));\n    }\n    forEach(fn) {\n      return new ChainableEvent(forEach(this.event, fn, this.disposables));\n    }\n    filter(fn) {\n      return new ChainableEvent(filter(this.event, fn, this.disposables));\n    }\n    reduce(merge, initial) {\n      return new ChainableEvent(reduce(this.event, merge, initial, this.disposables));\n    }\n    latch() {\n      return new ChainableEvent(latch(this.event, undefined, this.disposables));\n    }\n    debounce(merge) {\n      let delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100;\n      let leading = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      let leakWarningThreshold = arguments.length > 3 ? arguments[3] : undefined;\n      return new ChainableEvent(debounce(this.event, merge, delay, leading, leakWarningThreshold, this.disposables));\n    }\n    on(listener, thisArgs, disposables) {\n      return this.event(listener, thisArgs, disposables);\n    }\n    once(listener, thisArgs, disposables) {\n      return once(this.event)(listener, thisArgs, disposables);\n    }\n    dispose() {\n      this.disposables.dispose();\n    }\n  }\n  function chain(event) {\n    return new ChainableEvent(event);\n  }\n  Event.chain = chain;\n  function fromNodeEventEmitter(emitter, eventName) {\n    let map = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : id => id;\n    const fn = function () {\n      return result.fire(map(...arguments));\n    };\n    const onFirstListenerAdd = () => emitter.on(eventName, fn);\n    const onLastListenerRemove = () => emitter.removeListener(eventName, fn);\n    const result = new Emitter({\n      onFirstListenerAdd,\n      onLastListenerRemove\n    });\n    return result.event;\n  }\n  Event.fromNodeEventEmitter = fromNodeEventEmitter;\n  function fromDOMEventEmitter(emitter, eventName) {\n    let map = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : id => id;\n    const fn = function () {\n      return result.fire(map(...arguments));\n    };\n    const onFirstListenerAdd = () => emitter.addEventListener(eventName, fn);\n    const onLastListenerRemove = () => emitter.removeEventListener(eventName, fn);\n    const result = new Emitter({\n      onFirstListenerAdd,\n      onLastListenerRemove\n    });\n    return result.event;\n  }\n  Event.fromDOMEventEmitter = fromDOMEventEmitter;\n  function toPromise(event) {\n    return new Promise(resolve => once(event)(resolve));\n  }\n  Event.toPromise = toPromise;\n  function runAndSubscribe(event, handler) {\n    handler(undefined);\n    return event(e => handler(e));\n  }\n  Event.runAndSubscribe = runAndSubscribe;\n  function runAndSubscribeWithStore(event, handler) {\n    let store = null;\n    function run(e) {\n      store === null || store === void 0 ? void 0 : store.dispose();\n      store = new DisposableStore();\n      handler(e, store);\n    }\n    run(undefined);\n    const disposable = event(e => run(e));\n    return toDisposable(() => {\n      disposable.dispose();\n      store === null || store === void 0 ? void 0 : store.dispose();\n    });\n  }\n  Event.runAndSubscribeWithStore = runAndSubscribeWithStore;\n  class EmitterObserver {\n    constructor(obs, store) {\n      this.obs = obs;\n      this._counter = 0;\n      this._hasChanged = false;\n      const options = {\n        onFirstListenerAdd: () => {\n          obs.addObserver(this);\n        },\n        onLastListenerRemove: () => {\n          obs.removeObserver(this);\n        }\n      };\n      if (!store) {\n        _addLeakageTraceLogic(options);\n      }\n      this.emitter = new Emitter(options);\n      if (store) {\n        store.add(this.emitter);\n      }\n    }\n    beginUpdate(_observable) {\n      // console.assert(_observable === this.obs);\n      this._counter++;\n    }\n    handleChange(_observable, _change) {\n      this._hasChanged = true;\n    }\n    endUpdate(_observable) {\n      if (--this._counter === 0) {\n        if (this._hasChanged) {\n          this._hasChanged = false;\n          this.emitter.fire(this.obs.get());\n        }\n      }\n    }\n  }\n  function fromObservable(obs, store) {\n    const observer = new EmitterObserver(obs, store);\n    return observer.emitter.event;\n  }\n  Event.fromObservable = fromObservable;\n})(Event || (Event = {}));\nclass EventProfiling {\n  constructor(name) {\n    this._listenerCount = 0;\n    this._invocationCount = 0;\n    this._elapsedOverall = 0;\n    this._name = `${name}_${EventProfiling._idPool++}`;\n  }\n  start(listenerCount) {\n    this._stopWatch = new StopWatch(true);\n    this._listenerCount = listenerCount;\n  }\n  stop() {\n    if (this._stopWatch) {\n      const elapsed = this._stopWatch.elapsed();\n      this._elapsedOverall += elapsed;\n      this._invocationCount += 1;\n      console.info(`did FIRE ${this._name}: elapsed_ms: ${elapsed.toFixed(5)}, listener: ${this._listenerCount} (elapsed_overall: ${this._elapsedOverall.toFixed(2)}, invocations: ${this._invocationCount})`);\n      this._stopWatch = undefined;\n    }\n  }\n}\nEventProfiling._idPool = 0;\nlet _globalLeakWarningThreshold = -1;\nclass LeakageMonitor {\n  constructor(customThreshold) {\n    let name = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Math.random().toString(18).slice(2, 5);\n    this.customThreshold = customThreshold;\n    this.name = name;\n    this._warnCountdown = 0;\n  }\n  dispose() {\n    if (this._stacks) {\n      this._stacks.clear();\n    }\n  }\n  check(stack, listenerCount) {\n    let threshold = _globalLeakWarningThreshold;\n    if (typeof this.customThreshold === 'number') {\n      threshold = this.customThreshold;\n    }\n    if (threshold <= 0 || listenerCount < threshold) {\n      return undefined;\n    }\n    if (!this._stacks) {\n      this._stacks = new Map();\n    }\n    const count = this._stacks.get(stack.value) || 0;\n    this._stacks.set(stack.value, count + 1);\n    this._warnCountdown -= 1;\n    if (this._warnCountdown <= 0) {\n      // only warn on first exceed and then every time the limit\n      // is exceeded by 50% again\n      this._warnCountdown = threshold * 0.5;\n      // find most frequent listener and print warning\n      let topStack;\n      let topCount = 0;\n      for (const [stack, count] of this._stacks) {\n        if (!topStack || topCount < count) {\n          topStack = stack;\n          topCount = count;\n        }\n      }\n      console.warn(`[${this.name}] potential listener LEAK detected, having ${listenerCount} listeners already. MOST frequent listener (${topCount}):`);\n      console.warn(topStack);\n    }\n    return () => {\n      const count = this._stacks.get(stack.value) || 0;\n      this._stacks.set(stack.value, count - 1);\n    };\n  }\n}\nclass Stacktrace {\n  constructor(value) {\n    this.value = value;\n  }\n  static create() {\n    var _a;\n    return new Stacktrace((_a = new Error().stack) !== null && _a !== void 0 ? _a : '');\n  }\n  print() {\n    console.warn(this.value.split('\\n').slice(2).join('\\n'));\n  }\n}\nclass Listener {\n  constructor(callback, callbackThis, stack) {\n    this.callback = callback;\n    this.callbackThis = callbackThis;\n    this.stack = stack;\n    this.subscription = new SafeDisposable();\n  }\n  invoke(e) {\n    this.callback.call(this.callbackThis, e);\n  }\n}\n/**\n * The Emitter can be used to expose an Event to the public\n * to fire it from the insides.\n * Sample:\n    class Document {\n\n        private readonly _onDidChange = new Emitter<(value:string)=>any>();\n\n        public onDidChange = this._onDidChange.event;\n\n        // getter-style\n        // get onDidChange(): Event<(value:string)=>any> {\n        // \treturn this._onDidChange.event;\n        // }\n\n        private _doIt() {\n            //...\n            this._onDidChange.fire(value);\n        }\n    }\n */\nexport class Emitter {\n  constructor(options) {\n    var _a, _b;\n    this._disposed = false;\n    this._options = options;\n    this._leakageMon = _globalLeakWarningThreshold > 0 ? new LeakageMonitor(this._options && this._options.leakWarningThreshold) : undefined;\n    this._perfMon = ((_a = this._options) === null || _a === void 0 ? void 0 : _a._profName) ? new EventProfiling(this._options._profName) : undefined;\n    this._deliveryQueue = (_b = this._options) === null || _b === void 0 ? void 0 : _b.deliveryQueue;\n  }\n  dispose() {\n    var _a, _b, _c, _d;\n    if (!this._disposed) {\n      this._disposed = true;\n      // It is bad to have listeners at the time of disposing an emitter, it is worst to have listeners keep the emitter\n      // alive via the reference that's embedded in their disposables. Therefore we loop over all remaining listeners and\n      // unset their subscriptions/disposables. Looping and blaming remaining listeners is done on next tick because the\n      // the following programming pattern is very popular:\n      //\n      // const someModel = this._disposables.add(new ModelObject()); // (1) create and register model\n      // this._disposables.add(someModel.onDidChange(() => { ... }); // (2) subscribe and register model-event listener\n      // ...later...\n      // this._disposables.dispose(); disposes (1) then (2): don't warn after (1) but after the \"overall dispose\" is done\n      if (this._listeners) {\n        if (_enableDisposeWithListenerWarning) {\n          const listeners = Array.from(this._listeners);\n          queueMicrotask(() => {\n            var _a;\n            for (const listener of listeners) {\n              if (listener.subscription.isset()) {\n                listener.subscription.unset();\n                (_a = listener.stack) === null || _a === void 0 ? void 0 : _a.print();\n              }\n            }\n          });\n        }\n        this._listeners.clear();\n      }\n      (_a = this._deliveryQueue) === null || _a === void 0 ? void 0 : _a.clear(this);\n      (_c = (_b = this._options) === null || _b === void 0 ? void 0 : _b.onLastListenerRemove) === null || _c === void 0 ? void 0 : _c.call(_b);\n      (_d = this._leakageMon) === null || _d === void 0 ? void 0 : _d.dispose();\n    }\n  }\n  /**\n   * For the public to allow to subscribe\n   * to events from this Emitter\n   */\n  get event() {\n    if (!this._event) {\n      this._event = (callback, thisArgs, disposables) => {\n        var _a, _b, _c;\n        if (!this._listeners) {\n          this._listeners = new LinkedList();\n        }\n        const firstListener = this._listeners.isEmpty();\n        if (firstListener && ((_a = this._options) === null || _a === void 0 ? void 0 : _a.onFirstListenerAdd)) {\n          this._options.onFirstListenerAdd(this);\n        }\n        let removeMonitor;\n        let stack;\n        if (this._leakageMon && this._listeners.size >= 30) {\n          // check and record this emitter for potential leakage\n          stack = Stacktrace.create();\n          removeMonitor = this._leakageMon.check(stack, this._listeners.size + 1);\n        }\n        if (_enableDisposeWithListenerWarning) {\n          stack = stack !== null && stack !== void 0 ? stack : Stacktrace.create();\n        }\n        const listener = new Listener(callback, thisArgs, stack);\n        const removeListener = this._listeners.push(listener);\n        if (firstListener && ((_b = this._options) === null || _b === void 0 ? void 0 : _b.onFirstListenerDidAdd)) {\n          this._options.onFirstListenerDidAdd(this);\n        }\n        if ((_c = this._options) === null || _c === void 0 ? void 0 : _c.onListenerDidAdd) {\n          this._options.onListenerDidAdd(this, callback, thisArgs);\n        }\n        const result = listener.subscription.set(() => {\n          removeMonitor === null || removeMonitor === void 0 ? void 0 : removeMonitor();\n          if (!this._disposed) {\n            removeListener();\n            if (this._options && this._options.onLastListenerRemove) {\n              const hasListeners = this._listeners && !this._listeners.isEmpty();\n              if (!hasListeners) {\n                this._options.onLastListenerRemove(this);\n              }\n            }\n          }\n        });\n        if (disposables instanceof DisposableStore) {\n          disposables.add(result);\n        } else if (Array.isArray(disposables)) {\n          disposables.push(result);\n        }\n        return result;\n      };\n    }\n    return this._event;\n  }\n  /**\n   * To be kept private to fire an event to\n   * subscribers\n   */\n  fire(event) {\n    var _a, _b;\n    if (this._listeners) {\n      // put all [listener,event]-pairs into delivery queue\n      // then emit all event. an inner/nested event might be\n      // the driver of this\n      if (!this._deliveryQueue) {\n        this._deliveryQueue = new PrivateEventDeliveryQueue();\n      }\n      for (const listener of this._listeners) {\n        this._deliveryQueue.push(this, listener, event);\n      }\n      // start/stop performance insight collection\n      (_a = this._perfMon) === null || _a === void 0 ? void 0 : _a.start(this._deliveryQueue.size);\n      this._deliveryQueue.deliver();\n      (_b = this._perfMon) === null || _b === void 0 ? void 0 : _b.stop();\n    }\n  }\n}\nexport class EventDeliveryQueue {\n  constructor() {\n    this._queue = new LinkedList();\n  }\n  get size() {\n    return this._queue.size;\n  }\n  push(emitter, listener, event) {\n    this._queue.push(new EventDeliveryQueueElement(emitter, listener, event));\n  }\n  clear(emitter) {\n    const newQueue = new LinkedList();\n    for (const element of this._queue) {\n      if (element.emitter !== emitter) {\n        newQueue.push(element);\n      }\n    }\n    this._queue = newQueue;\n  }\n  deliver() {\n    while (this._queue.size > 0) {\n      const element = this._queue.shift();\n      try {\n        element.listener.invoke(element.event);\n      } catch (e) {\n        onUnexpectedError(e);\n      }\n    }\n  }\n}\n/**\n * An `EventDeliveryQueue` that is guaranteed to be used by a single `Emitter`.\n */\nclass PrivateEventDeliveryQueue extends EventDeliveryQueue {\n  clear(emitter) {\n    // Here we can just clear the entire linked list because\n    // all elements are guaranteed to belong to this emitter\n    this._queue.clear();\n  }\n}\nclass EventDeliveryQueueElement {\n  constructor(emitter, listener, event) {\n    this.emitter = emitter;\n    this.listener = listener;\n    this.event = event;\n  }\n}\nexport class PauseableEmitter extends Emitter {\n  constructor(options) {\n    super(options);\n    this._isPaused = 0;\n    this._eventQueue = new LinkedList();\n    this._mergeFn = options === null || options === void 0 ? void 0 : options.merge;\n  }\n  pause() {\n    this._isPaused++;\n  }\n  resume() {\n    if (this._isPaused !== 0 && --this._isPaused === 0) {\n      if (this._mergeFn) {\n        // use the merge function to create a single composite\n        // event. make a copy in case firing pauses this emitter\n        const events = Array.from(this._eventQueue);\n        this._eventQueue.clear();\n        super.fire(this._mergeFn(events));\n      } else {\n        // no merging, fire each event individually and test\n        // that this emitter isn't paused halfway through\n        while (!this._isPaused && this._eventQueue.size !== 0) {\n          super.fire(this._eventQueue.shift());\n        }\n      }\n    }\n  }\n  fire(event) {\n    if (this._listeners) {\n      if (this._isPaused !== 0) {\n        this._eventQueue.push(event);\n      } else {\n        super.fire(event);\n      }\n    }\n  }\n}\nexport class DebounceEmitter extends PauseableEmitter {\n  constructor(options) {\n    var _a;\n    super(options);\n    this._delay = (_a = options.delay) !== null && _a !== void 0 ? _a : 100;\n  }\n  fire(event) {\n    if (!this._handle) {\n      this.pause();\n      this._handle = setTimeout(() => {\n        this._handle = undefined;\n        this.resume();\n      }, this._delay);\n    }\n    super.fire(event);\n  }\n}\n/**\n * The EventBufferer is useful in situations in which you want\n * to delay firing your events during some code.\n * You can wrap that code and be sure that the event will not\n * be fired during that wrap.\n *\n * ```\n * const emitter: Emitter;\n * const delayer = new EventDelayer();\n * const delayedEvent = delayer.wrapEvent(emitter.event);\n *\n * delayedEvent(console.log);\n *\n * delayer.bufferEvents(() => {\n *   emitter.fire(); // event will not be fired yet\n * });\n *\n * // event will only be fired at this point\n * ```\n */\nexport class EventBufferer {\n  constructor() {\n    this.buffers = [];\n  }\n  wrapEvent(event) {\n    return (listener, thisArgs, disposables) => {\n      return event(i => {\n        const buffer = this.buffers[this.buffers.length - 1];\n        if (buffer) {\n          buffer.push(() => listener.call(thisArgs, i));\n        } else {\n          listener.call(thisArgs, i);\n        }\n      }, undefined, disposables);\n    };\n  }\n  bufferEvents(fn) {\n    const buffer = [];\n    this.buffers.push(buffer);\n    const r = fn();\n    this.buffers.pop();\n    buffer.forEach(flush => flush());\n    return r;\n  }\n}\n/**\n * A Relay is an event forwarder which functions as a replugabble event pipe.\n * Once created, you can connect an input event to it and it will simply forward\n * events from that input event through its own `event` property. The `input`\n * can be changed at any point in time.\n */\nexport class Relay {\n  constructor() {\n    this.listening = false;\n    this.inputEvent = Event.None;\n    this.inputEventListener = Disposable.None;\n    this.emitter = new Emitter({\n      onFirstListenerDidAdd: () => {\n        this.listening = true;\n        this.inputEventListener = this.inputEvent(this.emitter.fire, this.emitter);\n      },\n      onLastListenerRemove: () => {\n        this.listening = false;\n        this.inputEventListener.dispose();\n      }\n    });\n    this.event = this.emitter.event;\n  }\n  set input(event) {\n    this.inputEvent = event;\n    if (this.listening) {\n      this.inputEventListener.dispose();\n      this.inputEventListener = event(this.emitter.fire, this.emitter);\n    }\n  }\n  dispose() {\n    this.inputEventListener.dispose();\n    this.emitter.dispose();\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}