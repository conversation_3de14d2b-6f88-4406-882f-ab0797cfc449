{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\issueTrack\\\\views\\\\IssueLongList\\\\IssueLongList.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable jsx-a11y/anchor-has-content */\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable jsx-a11y/anchor-is-valid */\nimport React, { useEffect, useState } from \"react\";\nimport { Button, Checkbox, ConfigProvider, Drawer, Modal, Space } from 'antd';\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport { getPropValueByIdType, getUserNameById, getAvatarById, getIconValueByIdType, formatSvg } from \"src/issueTrack/utils/ArrayUtils\";\nimport * as httpCommon from \"@common/api/http\";\nimport IssueDetail from \"../IssueDetail/IssueDetail\";\nimport DefaultAvatar from \"@components/DefaultAvatar\";\nimport { eConsoleNodeId, eConsoleUiControl, ePagination } from \"@common/utils/enum\";\nimport { eNodeTypeId } from \"@common/utils/TsbConfig\";\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\nimport { useIssueSearchParams } from \"src/issueTrack/service/issueSearchHooks\";\nimport { Outlet, useAsyncValue, useLocation, useNavigate, useOutletContext, useParams, useSearchParams } from \"react-router-dom\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport { isListBoxType } from \"@common/utils/logicUtils\";\nimport { track008, track010 } from '@common/utils/ApiPath';\nimport \"./IssueLongList.scss\";\nimport { setting_234_get_team_mbr_user_info_query } from \"@common/api/query/query\";\nimport { useMutationSetting235SetTeamMbrUser, useMutationTeam545SelectTableFields } from \"@common/service/commonHooks\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport ObjExplorerTitle from \"@components/ObjExplorer/ObjExplorerTitle\";\nimport cloneDeep from \"lodash/cloneDeep\";\nimport { isEmpty } from \"@common/utils/ArrayUtils\";\nimport ResizableTable from \"@components/ResizableTable\";\nimport { ExclamationCircleOutlined } from '@ant-design/icons';\nimport { priPermission, getNodeNameByNodeId } from \"@common/utils/logicUtils\";\nimport DraggableDrawer from '@common/components/DraggableDrawer';\n\n//备注: column == field == attr\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function IssueLongList() {\n  _s();\n  const {\n    teamId,\n    nodeId: issueListNodeId\n  } = useParams();\n  const {\n    totalCnt,\n    issueList,\n    attrList,\n    issue506Result,\n    subclassAttrList,\n    userList,\n    spaceUserList,\n    selectionList,\n    objInfo,\n    pageNo,\n    keywords,\n    previousIssueLinkDisabledFlg,\n    nextIssueLinkDisabledFlg,\n    gotoPreviousIssue,\n    gotoNextIssue,\n    setCurrentIssueIdx,\n    viewMode,\n    refetchIssueList,\n    projectInfo,\n    onShowMoreButtonClick,\n    onMoreBtnClick,\n    searchQuery,\n    setting320Result,\n    finalCreateFlg\n  } = useOutletContext(); //setPageNo, iconSelectionLid,\n  const queryClient = useQueryClient();\n  const navigate = useNavigate();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const createFlg = searchParams.get(\"createFlg\"); //是否创建\n  const open = searchParams.get(\"open\"); //是否打开详情\n  const [issueDetailVisibleFlg, setIssueDetailVisibleFlg] = useState(false);\n  const {\n    issueNodeId,\n    queryId,\n    setIssueSearchParams\n  } = useIssueSearchParams(); // issue路由配置，页面刷新\n  const [issueListLong, setIssueListLong] = useState([]); // 深拷贝issueList\n\n  const [attrColumns, setAttrColumns] = useState([]);\n  //const [refreshingFlg, setRefreshingFlg] = useState(false); //是否正在\"刷新中\"\n  const [selectedAttrNidList, setSelectedAttrList] = useState([]); // 显示字段\n  //const [createIssueModalVisibleFlg, setCreateIssueModalVisibleFlg] = useState(false); //是否显示新建issue对话框\n  const [selectedAttrsModalVisibleFlg, setSelectedAttrsModalVisibleFlg] = useState(false); //是否显示 \"显示字段\" 弹出框\n  const {\n    mutate: mutationSaveFieldList\n  } = useMutationTeam545SelectTableFields(); //保存issue查询显示字段\n  // const { fieldList, configFlg, isLoading } = useQueryTeam544GetTableFieldList(teamId, eNodeTypeId.nt_317_objtype_issue_project, projectId); // 获取显示字段\n\n  const {\n    data: {\n      userId,\n      issueZoomFlg\n    } = {\n      userId: null,\n      issueZoomFlg: 0\n    }\n  } = useQuery({\n    ...setting_234_get_team_mbr_user_info_query(teamId)\n  });\n\n  // 移除 largeView、drawerWidth、handleZoom、setDrawerWidth 相关 state 和逻辑\n\n  useEffect(() => {\n    if (createFlg) {\n      setIssueSearchParams({\n        queryId: -1\n      });\n      setTimeout(() => {\n        // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑\n        // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；\n        openCreateIssueModal();\n      }, 2000);\n    }\n  }, [createFlg]);\n  useEffect(() => {\n    if (open) {\n      setIssueSearchParams({\n        queryId: -1\n      });\n      setIssueDetailVisibleFlg(true);\n    }\n  }, [open]);\n\n  //issuelist变动后重新处理数据\n  useEffect(() => {\n    if (issueList) {\n      _transformIssueTableColumns(issueList);\n      _load_team544_get_table_field_list();\n    }\n  }, [JSON.stringify(issueList)]); // 直接依赖数组会二次调用\n\n  useEffect(() => {\n    refetchIssueList();\n  }, [subclassAttrList]);\n  useEffect(() => {\n    // setLargeView(issueZoomFlg); // 移除此行\n  }, [issueZoomFlg]);\n  function openCreateIssueModal() {\n    globalEventBus.emit(\"openCreateIssueModalEvent\", \"\", {\n      nodeId: issueListNodeId,\n      projectInfo,\n      callback: onPostIssueCreated\n    });\n  }\n  // 数据处理 长列表\n  //https://confluence.ficent.com/pages/viewpage.action?pageId=78362226\n  //构建表格渲染所需要的 attrList(即:其它表格页面所需要的columns数据结构)\n  function _transformIssueTableColumns(issueList = []) {\n    const issueListLong = cloneDeep(issueList); // 需要注意JSON.parse(JSON.stringify())后icon无法识别为jsx对象\n    let attrList1 = attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox);\n    attrList1.forEach(attr => {\n      attr.key = attr.dataIndex = attr.attrNid;\n      attr.title = attr.name;\n      // 为所有列添加 ellipsis: true，防止文本折行\n      attr.ellipsis = true;\n\n      // 通过点击编号跳转详情\n      if (attr.attrNid == eConsoleNodeId.Nid_11101_Issue_IssueNo) {\n        // 不设置固定宽度，让auto layout自动调整\n        attr.render = (text, record) => {\n          return /*#__PURE__*/_jsxDEV(\"a\", {\n            style: {\n              color: \"#3279fe\"\n            },\n            onClick: () => {\n              setIssueDetailVisibleFlg(true);\n              setIssueSearchParams({\n                viewMode,\n                issueNodeId: record.nodeId,\n                queryId\n              });\n            },\n            children: text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 28\n          }, this);\n        };\n      }\n      if (attr.attrNid == eConsoleNodeId.Nid_11102_Issue_Title) {\n        // 不设置固定宽度，让auto layout自动调整\n        attr.render = (text, nodeData) => {\n          return /*#__PURE__*/_jsxDEV(ObjExplorerTitle, {\n            nodeData: nodeData,\n            onShowMoreButtonClick: onShowMoreButtonClick,\n            onMoreBtnClick: onMoreBtnClick,\n            searchQuery: searchQuery,\n            issueFlg: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 26\n          }, this);\n        };\n      }\n      if (isListBoxType(attr.uiControl)) {\n        //下拉框类型的表单字段\n        issueListLong.forEach((issue, _index) => {\n          for (var attrId in issue) {\n            if (attr.attrNid == attrId) {\n              if (attr.selectionLid == \"-701\") {\n                issue[attrId] = {\n                  avatar: getAvatarById(userList, issue[attrId]),\n                  username: getUserNameById(userList, issue[attrId])\n                };\n              } else if (attrId == eConsoleNodeId.Nid_11109_Issue_Type) {\n                issue[attrId] = getPropValueByIdType(selectionList, attr.selectionLid, issue[attrId]); //从下拉code找到其value\n              } else {\n                issue[attrId] = getPropValueByIdType(selectionList, attr.selectionLid, issue[attrId]);\n              }\n            }\n          }\n        });\n      }\n      // 添加icon 问题类别和标题的icon名称重复了，先隐藏 by walt 2023-06-15\n      /* if (attr.attrNid == eConsoleNodeId.Nid_11109_Issue_Type) {\r\n          attr.width = 100\r\n          attr.render = (text, _issue) => {\r\n              return <div style={{ display: \"flex\", alignItems: \"center\" }} className=\"overHidden\">\r\n                  { _issue.icon}\r\n                  <span style={{ paddingLeft: 2 }}>{text}</span>\r\n              </div>\r\n          }\r\n      } */\n      // 添加头像\n      if (attr.selectionLid == \"-701\") {\n        attr.render = (text, record) => {\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overHidden\",\n            children: [text !== null && text !== void 0 && text.username ? /*#__PURE__*/_jsxDEV(DefaultAvatar, {\n              avatarSrc: text === null || text === void 0 ? void 0 : text.avatar,\n              avatarFlg: 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 43\n            }, this) : \"\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: 5\n              },\n              children: text === null || text === void 0 ? void 0 : text.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 28\n          }, this);\n        };\n      }\n      if (attr.uiControl == eConsoleUiControl.Date) {\n        // 不设置固定宽度，让auto layout自动调整\n        /*  attr.render = (text, record) => {\r\n             return <div style={{ color: \"#666\" }}>{text}</div>\r\n         } */\n      }\n    });\n    setIssueListLong(issueListLong);\n    setAttrColumns(cloneDeep(attrList1));\n    // 保存后重新渲染\n  }\n\n  //获取查询显示字段\n  function _load_team544_get_table_field_list() {\n    let params = {\n      teamId: teamId,\n      objType: eNodeTypeId.nt_317_objtype_issue_project,\n      tableType: 1,\n      projectId: issue506Result === null || issue506Result === void 0 ? void 0 : issue506Result.projectId\n    };\n    httpCommon.team_544_get_table_field_list(params).then(res => {\n      if (res.resultCode === 200) {\n        if (res.configFlg == 1) {\n          selectedAttrNidList.length = 0; //清空数组，用[]进行赋值会受到const的语法限制\n          attrList.forEach(attr => {\n            if (attr.uiControl != eConsoleUiControl.RichTextBox) {\n              attr.hiddenFlg = false;\n            }\n            selectedAttrNidList.push(attr.attrNid);\n            attr.hiddenFlg = false;\n          });\n          // attrList.forEach(attr => attr.hiddenFlg = false);\n        } else {\n          selectedAttrNidList.length = 0;\n          selectedAttrNidList.push(...(res.fieldList || []).map(field => Number(field.fieldName))); //fileName为attrNid, 11105, 11104等\n          attrList.forEach(attr => {\n            if (attr.uiControl != eConsoleUiControl.RichTextBox) {\n              attr.hiddenFlg = false;\n            }\n            attr.hiddenFlg = !selectedAttrNidList.includes(attr.attrNid);\n          });\n        }\n      }\n      setAttrColumns(attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox));\n    });\n  }\n  /*   //刷新issue列表\r\n     function refreshIssueList() {\r\n         queryClient.invalidateQueries([track008])\r\n         setTimeout(() => {\r\n             setRefreshingFlg(false);\r\n         }, 500);\r\n     }*/\n  // 加载更多 - 长短列表\n  function gotoPageNo(page, pageSize) {\n    queryClient.setQueryData([track010, teamId, issueListNodeId], pageNo => page);\n    document.querySelector(`.issueLong-List .ant-table-tbody`).scrollTop = 0;\n  }\n  // 显示字段 勾选项\n  function setSelectedAttrList2(checkedValue) {\n    setSelectedAttrList(checkedValue);\n  }\n\n  //显示字段 选择字段, 点击保存按钮\n  function handleSelectedAttrsSaved() {\n    attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox).forEach(attr => attr.hiddenFlg = !selectedAttrNidList.includes(attr.attrNid));\n    let request = {\n      teamId,\n      objType: eNodeTypeId.nt_317_objtype_issue_project,\n      tableType: 1,\n      projectId: issue506Result === null || issue506Result === void 0 ? void 0 : issue506Result.projectId,\n      fields: selectedAttrNidList.map(item => ({\n        fieldName: item\n      }))\n    };\n    mutationSaveFieldList(request, {\n      onSuccess: (data, vars) => {\n        setSelectedAttrsModalVisibleFlg(false);\n        setAttrColumns(origin => {\n          return origin.map(item => {\n            return {\n              ...item,\n              hiddenFlg: !selectedAttrNidList.includes(item.attrNid)\n            };\n          });\n        });\n      }\n    });\n  }\n\n  // issue创建完成后，页面刷新\n  function onPostIssueCreated(issueNodeId) {\n    setIssueSearchParams({\n      viewMode,\n      issueNodeId,\n      queryId,\n      needAdd: false\n    });\n  }\n  const customizeRenderEmpty = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      // textAlign: 'center',\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \" issueLong-blank-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"blank-page-title\",\n        children: \"\\u8FD9\\u91CC\\u662F\\u7A7A\\u7684\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 17\n      }, this), priPermission(setting320Result.privWrite) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"blank-page-des fontsize-12 flexCenter\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            paddingRight: 5\n          },\n          children: \"\\u4F60\\u53EF\\u4EE5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 23\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          onClick: () => openCreateIssueModal(),\n          children: [\"\\u65B0\\u5EFA\", projectInfo.issueAlias]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 23\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 19\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 262,\n    columnNumber: 9\n  }, this);\n  function setTeamMbrUser() {\n    if (!teamId && !userId) {\n      return globalUtil.error('数据获取错误');\n    }\n    let zoomFlg = issueZoomFlg; // 使用 issueZoomFlg 作为 zoom 状态\n    // setLargeView(zoomFlg); // 移除此行\n    let params = {\n      teamId,\n      userId,\n      issueZoomFlg: zoomFlg\n    };\n    // setTeamMbrUserMutation(params); // 删除 setTeamMbrUserMutation 相关调用\n  }\n  async function addClick() {\n    var _await$getNodeNameByN;\n    return Modal.confirm({\n      title: \"提示\",\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 15\n      }, this),\n      centered: true,\n      content: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: `当前列表页为\"查询\"页，将转至“${(_await$getNodeNameByN = await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)) !== null && _await$getNodeNameByN !== void 0 ? _await$getNodeNameByN : \"全部\"}”页创建问题。`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 18\n      }, this),\n      okText: \" 好的，继续新建\",\n      cancelText: \"取消\",\n      onOk: () => {\n        if (!!(projectInfo !== null && projectInfo !== void 0 && projectInfo.allIssueNodeId)) {\n          navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}/list?createFlg=true`);\n        }\n      },\n      onCancel: () => {\n        console.log(\"Cancel\");\n      }\n    });\n  }\n\n  // 移除此函数\n  // const handleZoom = () => {\n  //   if (largeView) {\n  //     setDrawerWidth('50%');\n  //     setLargeView(false);\n  //   } else {\n  //     setDrawerWidth('90%');\n  //     setLargeView(true);\n  //   }\n  // };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"issueLong-List\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flexBetween\",\n      style: {\n        height: 45\n      },\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        size: 10,\n        children: [(searchQuery === null || searchQuery === void 0 ? void 0 : searchQuery.length) > 0 || keywords ? /*#__PURE__*/_jsxDEV(\"a\", {\n          style: {\n            fontSize: 12,\n            color: \"#999\"\n          },\n          title: \"\\u5B9A\\u4F4D\\u81F3#1\\u9875\",\n          onClick: () => pageNo > 1 ? gotoPageNo(1) : null,\n          children: [\"\\u7ED3\\u679C(\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"color333\",\n            children: totalCnt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 69\n          }, this), \"\\u6761)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 21\n        }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n          style: {\n            fontSize: 12,\n            color: \"#999\"\n          },\n          title: \"\\u5B9A\\u4F4D\\u81F3#1\\u9875\",\n          onClick: () => pageNo > 1 ? gotoPageNo(1) : null,\n          children: [\"\\u5168\\u90E8(\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"color333\",\n            children: totalCnt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 71\n          }, this), \"\\u6761)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 23\n        }, this), priPermission(setting320Result.privWrite) && finalCreateFlg && /*#__PURE__*/_jsxDEV(Button, {\n          className: \"defaultBtn\",\n          type: \"primary\",\n          onClick: () => objInfo !== null && objInfo !== void 0 && objInfo.objId ? addClick() : openCreateIssueModal(),\n          children: [\"+ \", projectInfo.issueAlias]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 20\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        onClick: () => setSelectedAttrsModalVisibleFlg(true),\n        className: \"fontsize-12\",\n        style: {\n          float: \"right\",\n          marginRight: 20,\n          color: '#999'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"fontsize-14 iconfont filter2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 17\n        }, this), \"\\u663E\\u793A\\u5B57\\u6BB5\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 9\n    }, this), isEmpty(issueListLong) ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        // textAlign: 'center',\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"issueLong-blank-page\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"blank-page-title\",\n          children: \"\\u8FD9\\u91CC\\u662F\\u7A7A\\u7684\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 17\n        }, this), priPermission(setting320Result.privWrite) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"blank-page-des fontsize-12 flexCenter\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              paddingRight: 5\n            },\n            children: \"\\u4F60\\u53EF\\u4EE5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 23\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            onClick: () => openCreateIssueModal(),\n            children: [\"\\u65B0\\u5EFA\", projectInfo.issueAlias]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 23\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 19\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 12\n    }, this) : /*#__PURE__*/_jsxDEV(ConfigProvider, {\n      renderEmpty: customizeRenderEmpty,\n      children: /*#__PURE__*/_jsxDEV(ResizableTable, {\n        className: \"longlist\",\n        bordered: false,\n        style: {\n          height: \"100%\"\n        },\n        scroll: {\n          scrollToFirstRowOnChange: true,\n          y: 0,\n          x: 0\n        } //y无意义设置，css设为unset\n        ,\n        columns: attrColumns.filter(_attr => !_attr.hiddenFlg),\n        dataSource: issueListLong,\n        size: \"small\",\n        rowClassName: _issue => _issue.nodeId == issueNodeId ? \"currentRow\" : \"\"\n        // 通过table行点击事件，来使用户可以点击一行的任意位置，就可以弹出详情\n        ,\n        onRow: _issue => ({\n          onClick: e => {\n            setIssueSearchParams({\n              viewMode,\n              issueNodeId: _issue.nodeId,\n              queryId\n            }); // fixbug 修复切换issue url未变更的bug\n            // setIssueDetailVisibleFlg(true);\n          }\n        }),\n        pagination: {\n          position: ['bottomCenter'],\n          size: 'small',\n          showQuickJumper: true,\n          showSizeChanger: false,\n          showTitle: true,\n          pageSize: 30,\n          current: pageNo,\n          defaultCurrent: pageNo,\n          total: totalCnt,\n          showTotal: total => `总共 ${total} 条`,\n          onChange: gotoPageNo\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n      destroyOnClose: true,\n      className: \"longlist-detail\",\n      minWidth: \"30%\",\n      maxWidth: \"95%\",\n      draggableFlag: true,\n      fixedMinWidth: \"50%\",\n      fixedMaxWidth: \"90%\",\n      onClose: () => setIssueDetailVisibleFlg(false),\n      open: issueDetailVisibleFlg,\n      closable: true,\n      title: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: `${projectInfo.issueAlias}详情`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 17\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(IssueDetail, {\n        viewMode: viewMode,\n        issueNodeId: issueNodeId,\n        selectionList: selectionList,\n        userList: userList,\n        spaceUserList: spaceUserList,\n        gotoPreviousIssue: gotoPreviousIssue,\n        gotoNextIssue: gotoNextIssue,\n        previousIssueLinkDisabledFlg: previousIssueLinkDisabledFlg,\n        nextIssueLinkDisabledFlg: nextIssueLinkDisabledFlg,\n        projectInfo: projectInfo,\n        onMoreBtnClick: onMoreBtnClick,\n        setCurrentIssueIdx: setCurrentIssueIdx,\n        issueList: issueList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 9\n    }, this), selectedAttrsModalVisibleFlg && /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      className: \"tms-modal\",\n      destroyOnClose: true,\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fontsize-16\",\n        children: \"\\u663E\\u793A\\u5B57\\u6BB5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 24\n      }, this),\n      open: true,\n      onCancel: () => setSelectedAttrsModalVisibleFlg(false),\n      width: 600,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        style: {\n          textAlign: \"center\"\n        },\n        type: \"primary\",\n        onClick: handleSelectedAttrsSaved,\n        children: \"\\u786E\\u5B9A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 21\n      }, this)],\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'flex-start',\n          margin: 30\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginLeft: '30px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n              onChange: e => setSelectedAttrList2(e),\n              defaultValue: selectedAttrNidList,\n              children: attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox).map(_attr => /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: _attr.attrNid,\n                disabled: _attr.name == '问题编号' || _attr.name == '标题' || _attr.name == '负责人',\n                style: {\n                  margin: 10,\n                  width: 130\n                },\n                children: _attr.name\n              }, _attr.attrNid, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 45\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 320,\n    columnNumber: 12\n  }, this);\n}\n_s(IssueLongList, \"E5rT0g4SDVF0mFqTVE+bMJCVDOo=\", false, function () {\n  return [useParams, useOutletContext, useQueryClient, useNavigate, useSearchParams, useIssueSearchParams, useMutationTeam545SelectTableFields, useQuery];\n});\n_c = IssueLongList;\nvar _c;\n$RefreshReg$(_c, \"IssueLongList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Checkbox", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Drawer", "Modal", "Space", "DraggablePopUp", "getPropValueByIdType", "getUserNameById", "getAvatarById", "getIconValueByIdType", "formatSvg", "httpCommon", "IssueDetail", "DefaultAvatar", "eConsoleNodeId", "eConsoleUiControl", "ePagination", "eNodeTypeId", "useQuery", "useQueryClient", "useIssueSearchParams", "Outlet", "useAsyncValue", "useLocation", "useNavigate", "useOutletContext", "useParams", "useSearchParams", "globalEventBus", "isListBoxType", "track008", "track010", "setting_234_get_team_mbr_user_info_query", "useMutationSetting235SetTeamMbrUser", "useMutationTeam545SelectTableFields", "globalUtil", "ObjExplorerTitle", "cloneDeep", "isEmpty", "ResizableTable", "ExclamationCircleOutlined", "priPermission", "getNodeNameByNodeId", "DraggableDrawer", "jsxDEV", "_jsxDEV", "IssueLongList", "_s", "teamId", "nodeId", "issueListNodeId", "totalCnt", "issueList", "attrList", "issue506Result", "subclassAttrList", "userList", "spaceUserList", "selectionList", "objInfo", "pageNo", "keywords", "previousIssueLinkDisabledFlg", "nextIssueLinkDisabledFlg", "gotoPreviousIssue", "gotoNextIssue", "setCurrentIssueIdx", "viewMode", "refetchIssueList", "projectInfo", "onShowMoreButtonClick", "onMoreBtnClick", "searchQuery", "setting320Result", "finalCreateFlg", "queryClient", "navigate", "searchParams", "setSearchParams", "createFlg", "get", "open", "issueDetailVisibleFlg", "setIssueDetailVisibleFlg", "issueNodeId", "queryId", "setIssueSearchParams", "issueListLong", "setIssueListLong", "attrColumns", "setAttrColumns", "selectedAttrNidList", "setSelectedAttrList", "selectedAttrsModalVisibleFlg", "setSelectedAttrsModalVisibleFlg", "mutate", "mutationSaveFieldList", "data", "userId", "issueZoomFlg", "setTimeout", "openCreateIssueModal", "_transformIssueTableColumns", "_load_team544_get_table_field_list", "JSON", "stringify", "emit", "callback", "onPostIssueCreated", "attrList1", "filter", "attr", "uiControl", "RichTextBox", "for<PERSON>ach", "key", "dataIndex", "attrNid", "title", "name", "ellipsis", "Nid_11101_Issue_IssueNo", "render", "text", "record", "style", "color", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Nid_11102_Issue_Title", "nodeData", "issueFlg", "issue", "_index", "attrId", "selectionLid", "avatar", "username", "Nid_11109_Issue_Type", "className", "avatarSrc", "avatarFlg", "marginLeft", "Date", "params", "objType", "nt_317_objtype_issue_project", "tableType", "projectId", "team_544_get_table_field_list", "then", "res", "resultCode", "configFlg", "length", "hiddenFlg", "push", "fieldList", "map", "field", "Number", "fieldName", "includes", "gotoPageNo", "page", "pageSize", "setQueryData", "document", "querySelector", "scrollTop", "setSelectedAttrList2", "checkedValue", "handleSelectedAttrsSaved", "request", "fields", "item", "onSuccess", "vars", "origin", "needAdd", "customizeRenderEmpty", "privWrite", "paddingRight", "issueAlias", "setTeamMbrUser", "error", "zoomFlg", "addClick", "_await$getNodeNameByN", "confirm", "icon", "centered", "content", "allIssueNodeId", "okText", "cancelText", "onOk", "onCancel", "console", "log", "height", "size", "fontSize", "type", "objId", "float", "marginRight", "renderEmpty", "bordered", "scroll", "scrollToFirstRowOnChange", "y", "x", "columns", "_attr", "dataSource", "rowClassName", "_issue", "onRow", "e", "pagination", "position", "showQuickJumper", "showSizeChanger", "showTitle", "current", "defaultCurrent", "total", "showTotal", "onChange", "destroyOnClose", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "draggableFlag", "fixed<PERSON>in<PERSON><PERSON>th", "fixedMaxWidth", "onClose", "closable", "width", "footer", "textAlign", "display", "justifyContent", "margin", "Group", "defaultValue", "value", "disabled", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/issueTrack/views/IssueLongList/IssueLongList.jsx"], "sourcesContent": ["/* eslint-disable jsx-a11y/anchor-has-content */\r\n/* eslint-disable react-hooks/exhaustive-deps */\r\n/* eslint-disable jsx-a11y/anchor-is-valid */\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { But<PERSON>, Checkbox, ConfigProvider, Drawer, Modal, Space } from 'antd';\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";\r\nimport { getPropValueByIdType, getUserNameById, getAvatarById, getIconValueByIdType, formatSvg } from \"src/issueTrack/utils/ArrayUtils\";\r\nimport * as httpCommon from \"@common/api/http\";\r\nimport IssueDetail from \"../IssueDetail/IssueDetail\";\r\nimport DefaultAvatar from \"@components/DefaultAvatar\"\r\nimport { eConsoleNodeId, eConsoleUiControl, ePagination } from \"@common/utils/enum\";\r\nimport { eNodeTypeId } from \"@common/utils/TsbConfig\";\r\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useIssueSearchParams } from \"src/issueTrack/service/issueSearchHooks\";\r\nimport { Outlet, useAsyncValue, useLocation, useNavigate, useOutletContext, useParams, useSearchParams } from \"react-router-dom\";\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport { isListBoxType } from \"@common/utils/logicUtils\";\r\nimport { track008, track010 } from '@common/utils/ApiPath';\r\nimport \"./IssueLongList.scss\";\r\nimport { setting_234_get_team_mbr_user_info_query } from \"@common/api/query/query\";\r\nimport { useMutationSetting235SetTeamMbrUser, useMutationTeam545SelectTableFields } from \"@common/service/commonHooks\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport ObjExplorerTitle from \"@components/ObjExplorer/ObjExplorerTitle\";\r\nimport cloneDeep from \"lodash/cloneDeep\";\r\nimport { isEmpty } from \"@common/utils/ArrayUtils\";\r\nimport ResizableTable from \"@components/ResizableTable\";\r\nimport { ExclamationCircleOutlined } from '@ant-design/icons';\r\nimport { priPermission, getNodeNameByNodeId } from \"@common/utils/logicUtils\";\r\nimport DraggableDrawer from '@common/components/DraggableDrawer';\r\n\r\n//备注: column == field == attr\r\nexport default function IssueLongList() {\r\n    const { teamId, nodeId: issueListNodeId } = useParams();\r\n    const { totalCnt, issueList, attrList, issue506Result, subclassAttrList, userList, spaceUserList, selectionList, objInfo, pageNo,\r\n         keywords,  previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg,\r\n        gotoPreviousIssue, gotoNextIssue, setCurrentIssueIdx, viewMode,refetchIssueList, projectInfo, \r\n        onShowMoreButtonClick, onMoreBtnClick, searchQuery, setting320Result, finalCreateFlg } = useOutletContext(); //setPageNo, iconSelectionLid,\r\n    const queryClient = useQueryClient();\r\n    const navigate = useNavigate();\r\n    const [searchParams, setSearchParams] = useSearchParams();\r\n    const createFlg = searchParams.get(\"createFlg\"); //是否创建\r\n    const open = searchParams.get(\"open\"); //是否打开详情\r\n    const [issueDetailVisibleFlg, setIssueDetailVisibleFlg] = useState(false);\r\n    const {issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams();// issue路由配置，页面刷新\r\n    const [issueListLong, setIssueListLong] = useState([]);// 深拷贝issueList\r\n\r\n    const [attrColumns, setAttrColumns] = useState([]);\r\n    //const [refreshingFlg, setRefreshingFlg] = useState(false); //是否正在\"刷新中\"\r\n    const [selectedAttrNidList, setSelectedAttrList] = useState([]); // 显示字段\r\n    //const [createIssueModalVisibleFlg, setCreateIssueModalVisibleFlg] = useState(false); //是否显示新建issue对话框\r\n    const [selectedAttrsModalVisibleFlg, setSelectedAttrsModalVisibleFlg] = useState(false); //是否显示 \"显示字段\" 弹出框\r\n    const { mutate: mutationSaveFieldList } = useMutationTeam545SelectTableFields();  //保存issue查询显示字段\r\n    // const { fieldList, configFlg, isLoading } = useQueryTeam544GetTableFieldList(teamId, eNodeTypeId.nt_317_objtype_issue_project, projectId); // 获取显示字段\r\n\r\n    const {data: { userId, issueZoomFlg } = { userId: null, issueZoomFlg: 0 } }\r\n      = useQuery({\r\n        ...setting_234_get_team_mbr_user_info_query(teamId)\r\n    });\r\n\r\n    // 移除 largeView、drawerWidth、handleZoom、setDrawerWidth 相关 state 和逻辑\r\n\r\n    useEffect(()=>{\r\n      if(createFlg){\r\n        setIssueSearchParams({queryId: -1});\r\n        setTimeout(()=>{\r\n          // tmsbug-10173:issue查询页，点击“新建”，切换至“全部”issue时，提示是否放弃编辑\r\n          // unstable_usePrompt方法会在新建弹窗弹出后如果再次更改路由则会触发；\r\n         openCreateIssueModal()\r\n        }, 2000)\r\n      } \r\n    },[createFlg])\r\n\r\n    useEffect(()=>{\r\n      if(open){\r\n        setIssueSearchParams({queryId: -1});\r\n        setIssueDetailVisibleFlg(true)\r\n      } \r\n    },[open])\r\n\r\n    //issuelist变动后重新处理数据\r\n    useEffect(() => {\r\n      if(issueList){\r\n        _transformIssueTableColumns(issueList);\r\n        _load_team544_get_table_field_list();\r\n      }\r\n    }, [JSON.stringify(issueList)]); // 直接依赖数组会二次调用\r\n\r\n    useEffect(() => {\r\n        refetchIssueList()\r\n    },[subclassAttrList])\r\n\r\n    useEffect(() => {\r\n        // setLargeView(issueZoomFlg); // 移除此行\r\n    }, [issueZoomFlg]);\r\n\r\n    function openCreateIssueModal () {\r\n        globalEventBus.emit(\"openCreateIssueModalEvent\", \"\", {\r\n          nodeId: issueListNodeId, projectInfo, callback: onPostIssueCreated\r\n        })\r\n      }\r\n    // 数据处理 长列表\r\n    //https://confluence.ficent.com/pages/viewpage.action?pageId=78362226\r\n    //构建表格渲染所需要的 attrList(即:其它表格页面所需要的columns数据结构)\r\n    function _transformIssueTableColumns(issueList = []) {\r\n        const issueListLong = cloneDeep(issueList); // 需要注意JSON.parse(JSON.stringify())后icon无法识别为jsx对象\r\n        let attrList1 = attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox)\r\n        attrList1.forEach((attr) => {\r\n            attr.key = attr.dataIndex = attr.attrNid;\r\n            attr.title = attr.name;\r\n            // 为所有列添加 ellipsis: true，防止文本折行\r\n            attr.ellipsis = true;\r\n            \r\n            // 通过点击编号跳转详情\r\n            if (attr.attrNid == eConsoleNodeId.Nid_11101_Issue_IssueNo) {\r\n                // 不设置固定宽度，让auto layout自动调整\r\n                attr.render = (text, record) => {\r\n                    return <a style={{ color: \"#3279fe\" }}\r\n                        onClick={() => {\r\n                            setIssueDetailVisibleFlg(true);\r\n                            setIssueSearchParams({ viewMode, issueNodeId: record.nodeId, queryId });\r\n                        }}>\r\n                        {text}\r\n                    </a>\r\n                }\r\n            }\r\n            if (attr.attrNid == eConsoleNodeId.Nid_11102_Issue_Title) {\r\n                // 不设置固定宽度，让auto layout自动调整\r\n                attr.render = (text, nodeData) => {\r\n                  return <ObjExplorerTitle nodeData={nodeData}\r\n                                           onShowMoreButtonClick={onShowMoreButtonClick}\r\n                                           onMoreBtnClick={onMoreBtnClick}\r\n                                           searchQuery={searchQuery}\r\n                                           issueFlg={true}/>\r\n                }\r\n            }\r\n            if (isListBoxType(attr.uiControl)) {  //下拉框类型的表单字段\r\n                issueListLong.forEach((issue, _index) => {\r\n                    for (var attrId in issue) {\r\n                        if (attr.attrNid == attrId) {\r\n                            if (attr.selectionLid == \"-701\") {\r\n                                issue[attrId] = {\r\n                                    avatar: getAvatarById(userList, issue[attrId]),\r\n                                    username: getUserNameById(userList, issue[attrId])\r\n                                }\r\n                            } else if (attrId == eConsoleNodeId.Nid_11109_Issue_Type) {\r\n                                issue[attrId] = getPropValueByIdType(selectionList, attr.selectionLid, issue[attrId]) //从下拉code找到其value\r\n                            } else {\r\n                                issue[attrId] = getPropValueByIdType(selectionList, attr.selectionLid, issue[attrId])\r\n                            }\r\n                        }\r\n                    }\r\n                })\r\n            }\r\n            // 添加icon 问题类别和标题的icon名称重复了，先隐藏 by walt 2023-06-15\r\n            /* if (attr.attrNid == eConsoleNodeId.Nid_11109_Issue_Type) {\r\n                attr.width = 100\r\n                attr.render = (text, _issue) => {\r\n                    return <div style={{ display: \"flex\", alignItems: \"center\" }} className=\"overHidden\">\r\n                        { _issue.icon}\r\n                        <span style={{ paddingLeft: 2 }}>{text}</span>\r\n                    </div>\r\n                }\r\n            } */\r\n            // 添加头像\r\n            if (attr.selectionLid == \"-701\") {\r\n                attr.render = (text, record) => {\r\n                    return <div className=\"overHidden\">\r\n                        {text?.username ? <DefaultAvatar avatarSrc={text?.avatar} avatarFlg={1} /> : \"\"}\r\n                        <span style={{ marginLeft: 5 }}>{text?.username}</span>\r\n                    </div>\r\n                }\r\n            }\r\n            if (attr.uiControl == eConsoleUiControl.Date) {\r\n                // 不设置固定宽度，让auto layout自动调整\r\n               /*  attr.render = (text, record) => {\r\n                    return <div style={{ color: \"#666\" }}>{text}</div>\r\n                } */\r\n            }\r\n        })\r\n        setIssueListLong(issueListLong)\r\n        setAttrColumns(cloneDeep(attrList1));\r\n        // 保存后重新渲染\r\n    }\r\n\r\n    //获取查询显示字段\r\n    function _load_team544_get_table_field_list() {\r\n        let params = { teamId: teamId, objType: eNodeTypeId.nt_317_objtype_issue_project, tableType: 1, projectId: issue506Result?.projectId, }\r\n        httpCommon.team_544_get_table_field_list(params).then((res) => {\r\n            if (res.resultCode === 200) {\r\n                if (res.configFlg == 1) {\r\n                    selectedAttrNidList.length = 0; //清空数组，用[]进行赋值会受到const的语法限制\r\n                    attrList.forEach(attr => {\r\n                        if (attr.uiControl != eConsoleUiControl.RichTextBox) {\r\n                            attr.hiddenFlg = false\r\n                        }\r\n                        selectedAttrNidList.push(attr.attrNid)\r\n                        attr.hiddenFlg = false\r\n                    })\r\n                    // attrList.forEach(attr => attr.hiddenFlg = false);\r\n                } else {\r\n                    selectedAttrNidList.length = 0;\r\n                    selectedAttrNidList.push(...(res.fieldList || []).map(field => Number(field.fieldName))); //fileName为attrNid, 11105, 11104等\r\n                    attrList.forEach(attr => {\r\n                        if (attr.uiControl != eConsoleUiControl.RichTextBox) {\r\n                            attr.hiddenFlg = false\r\n                        }\r\n                        attr.hiddenFlg = !selectedAttrNidList.includes(attr.attrNid)\r\n                    });\r\n                }\r\n            }\r\n            setAttrColumns(attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox));\r\n        });\r\n    }\r\n /*   //刷新issue列表\r\n    function refreshIssueList() {\r\n        queryClient.invalidateQueries([track008])\r\n        setTimeout(() => {\r\n            setRefreshingFlg(false);\r\n        }, 500);\r\n    }*/\r\n    // 加载更多 - 长短列表\r\n    function gotoPageNo(page, pageSize) {\r\n        queryClient.setQueryData([track010, teamId, issueListNodeId], (pageNo)=>(page));\r\n        document.querySelector(`.issueLong-List .ant-table-tbody`).scrollTop = 0\r\n    }\r\n    // 显示字段 勾选项\r\n    function setSelectedAttrList2(checkedValue) {\r\n        setSelectedAttrList(checkedValue);\r\n    }\r\n\r\n    //显示字段 选择字段, 点击保存按钮\r\n    function handleSelectedAttrsSaved() {\r\n        attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox).forEach(attr => attr.hiddenFlg = !selectedAttrNidList.includes(attr.attrNid));\r\n        let request = {\r\n            teamId,\r\n            objType: eNodeTypeId.nt_317_objtype_issue_project,\r\n            tableType: 1,\r\n            projectId: issue506Result?.projectId,\r\n            fields: selectedAttrNidList.map(item => ({fieldName: item}))\r\n        }\r\n        mutationSaveFieldList(request, {\r\n            onSuccess: (data, vars) => {\r\n                setSelectedAttrsModalVisibleFlg(false);\r\n                setAttrColumns(origin => {\r\n                    return origin.map(item => {\r\n                        return {\r\n                            ...item,\r\n                            hiddenFlg: !selectedAttrNidList.includes(item.attrNid)\r\n                        }\r\n                    });\r\n                })\r\n            }\r\n        })\r\n    }\r\n\r\n    // issue创建完成后，页面刷新\r\n    function onPostIssueCreated(issueNodeId) {\r\n        setIssueSearchParams({viewMode, issueNodeId, queryId, needAdd: false})\r\n    }\r\n\r\n    const customizeRenderEmpty = () => (\r\n        <div\r\n            style={{\r\n                // textAlign: 'center',\r\n            }}\r\n        >\r\n            <div className=\" issueLong-blank-page\">\r\n                <div className=\"blank-page-title\">这里是空的</div>\r\n                {\r\n                  priPermission(setting320Result.privWrite) &&  \r\n                  <div className=\"blank-page-des fontsize-12 flexCenter\">\r\n                      <span style={{ paddingRight: 5 }}>你可以</span>\r\n                      <a onClick={() =>openCreateIssueModal()}>新建{projectInfo.issueAlias}</a>\r\n                  </div>\r\n                }\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    function setTeamMbrUser(){\r\n        if(!teamId && !userId){\r\n          return globalUtil.error('数据获取错误');\r\n        }\r\n        let zoomFlg = issueZoomFlg // 使用 issueZoomFlg 作为 zoom 状态\r\n        // setLargeView(zoomFlg); // 移除此行\r\n        let params = { teamId, userId, issueZoomFlg: zoomFlg }\r\n        // setTeamMbrUserMutation(params); // 删除 setTeamMbrUserMutation 相关调用\r\n      }\r\n\r\n  async  function addClick(){\r\n    return Modal.confirm({\r\n        title: \"提示\",\r\n        icon: <ExclamationCircleOutlined />,\r\n        centered: true,\r\n        content: <p>{`当前列表页为\"查询\"页，将转至“${await getNodeNameByNodeId(teamId, projectInfo.allIssueNodeId)??\"全部\"}”页创建问题。`}</p>,\r\n        okText: \" 好的，继续新建\",\r\n        cancelText: \"取消\",\r\n        onOk: () => {\r\n            if(!!projectInfo?.allIssueNodeId){\r\n                navigate(`/team/${teamId}/issues/${projectInfo.allIssueNodeId}/list?createFlg=true`)\r\n            }\r\n        },\r\n        onCancel: () => {\r\n            console.log(\"Cancel\");\r\n        },\r\n        });\r\n    }\r\n\r\n    // 移除此函数\r\n    // const handleZoom = () => {\r\n    //   if (largeView) {\r\n    //     setDrawerWidth('50%');\r\n    //     setLargeView(false);\r\n    //   } else {\r\n    //     setDrawerWidth('90%');\r\n    //     setLargeView(true);\r\n    //   }\r\n    // };\r\n\r\n    return <div className=\"issueLong-List\">\r\n        <div className=\"flexBetween\" style={{ height: 45 }}>\r\n            <Space size={10} >\r\n                {searchQuery?.length > 0 || keywords ?\r\n                    <a style={{ fontSize: 12, color: \"#999\" }} title='定位至#1页'\r\n                       onClick={()=>pageNo>1?gotoPageNo(1):null}>结果(<span className=\"color333\">{totalCnt}</span>条)</a>\r\n                    : <a style={{ fontSize: 12, color: \"#999\" }} title='定位至#1页'\r\n                         onClick={()=>pageNo>1?gotoPageNo(1):null}>全部(<span className=\"color333\">{totalCnt}</span>条)</a>\r\n                }\r\n                {/* <Button\r\n                    className={refreshingFlg && 'refresh-icon'}\r\n                    style={{ position: 'relative', color: '#999' }}\r\n                    type=\"link\"\r\n                    icon={<span className=\"refresh-position fontsize-14 iconfont shuaxin1\" />}\r\n                    onClick={() => {\r\n                        setRefreshingFlg(true);\r\n                        refreshIssueList()\r\n                    }}\r\n                /> */}\r\n                {\r\n                  ( priPermission(setting320Result.privWrite) && finalCreateFlg) &&\r\n                  (<Button className=\"defaultBtn\" type=\"primary\"\r\n                           onClick={() => objInfo?.objId? addClick() :openCreateIssueModal()}>\r\n                      + {projectInfo.issueAlias}\r\n                  </Button>)\r\n                }\r\n            </Space>\r\n            <a onClick={() => setSelectedAttrsModalVisibleFlg(true)}\r\n                className=\"fontsize-12\"\r\n                style={{ float: \"right\", marginRight: 20, color:'#999' }}>\r\n                <span className=\"fontsize-14 iconfont filter2\" />\r\n                显示字段\r\n            </a>\r\n        </div>\r\n        {\r\n          isEmpty(issueListLong) ?\r\n           <div\r\n              style={{\r\n                  // textAlign: 'center',\r\n              }}\r\n            >\r\n            <div className=\"issueLong-blank-page\">\r\n                <div className=\"blank-page-title\">这里是空的</div>\r\n                {\r\n                  priPermission(setting320Result.privWrite) &&  \r\n                  <div className=\"blank-page-des fontsize-12 flexCenter\">\r\n                      <span style={{ paddingRight: 5 }}>你可以</span>\r\n                      <a onClick={() =>openCreateIssueModal()}>新建{projectInfo.issueAlias}</a>\r\n                  </div>\r\n                }\r\n            </div>\r\n          </div> :\r\n          <ConfigProvider renderEmpty={customizeRenderEmpty}>\r\n            <ResizableTable \r\n                className=\"longlist\"\r\n                bordered={false}\r\n                style={{ height: \"100%\" }}\r\n                scroll={{ scrollToFirstRowOnChange: true, y: 0, x: 0 }}//y无意义设置，css设为unset\r\n                columns={attrColumns.filter(_attr => !_attr.hiddenFlg)}\r\n                dataSource={issueListLong}\r\n                size={\"small\"}\r\n                rowClassName={(_issue) => _issue.nodeId == issueNodeId ? \"currentRow\" : \"\"}\r\n                // 通过table行点击事件，来使用户可以点击一行的任意位置，就可以弹出详情\r\n                onRow={(_issue) => ({\r\n                    onClick: (e) => {\r\n                        setIssueSearchParams({viewMode, issueNodeId: _issue.nodeId, queryId}) // fixbug 修复切换issue url未变更的bug\r\n                        // setIssueDetailVisibleFlg(true);\r\n                    }\r\n                })}\r\n                pagination={\r\n                    {\r\n                        position: ['bottomCenter'],\r\n                        size: 'small',\r\n                        showQuickJumper: true,\r\n                        showSizeChanger: false,\r\n                        showTitle: true,\r\n                        pageSize: 30,\r\n                        current: pageNo,\r\n                        defaultCurrent: pageNo,\r\n                        total: totalCnt,\r\n                        showTotal: (total) => `总共 ${total} 条`,\r\n                        onChange: gotoPageNo,\r\n                    }\r\n                }\r\n            />\r\n        </ConfigProvider> }\r\n\r\n        <DraggableDrawer\r\n            destroyOnClose\r\n            className=\"longlist-detail\"\r\n            minWidth=\"30%\"\r\n            maxWidth=\"95%\"\r\n            draggableFlag={true}\r\n            fixedMinWidth=\"50%\"\r\n            fixedMaxWidth=\"90%\"\r\n            onClose={() => setIssueDetailVisibleFlg(false)}\r\n            open={issueDetailVisibleFlg}\r\n            closable={true}\r\n            title={\r\n                <span>{`${projectInfo.issueAlias}详情`}</span>\r\n            }\r\n        >\r\n            <IssueDetail\r\n                viewMode={viewMode}\r\n                issueNodeId={issueNodeId}\r\n                selectionList={selectionList}\r\n                userList={userList}\r\n                spaceUserList={spaceUserList}\r\n                gotoPreviousIssue={gotoPreviousIssue}\r\n                gotoNextIssue={gotoNextIssue}\r\n                previousIssueLinkDisabledFlg={previousIssueLinkDisabledFlg}\r\n                nextIssueLinkDisabledFlg={nextIssueLinkDisabledFlg}\r\n                projectInfo={projectInfo}\r\n                onMoreBtnClick={onMoreBtnClick}\r\n                setCurrentIssueIdx={setCurrentIssueIdx}\r\n                issueList={issueList}\r\n            />\r\n        </DraggableDrawer>\r\n        {/*显示字段*/}\r\n        {selectedAttrsModalVisibleFlg &&\r\n            <DraggablePopUp className=\"tms-modal\" destroyOnClose\r\n                title={<div className=\"fontsize-16\">显示字段</div>}\r\n                open={true}\r\n                onCancel={() => setSelectedAttrsModalVisibleFlg(false)}\r\n                width={600}\r\n                footer={[\r\n                    <Button style={{ textAlign: \"center\" }} type=\"primary\" onClick={handleSelectedAttrsSaved}>确定</Button>\r\n                ]}\r\n            >\r\n                <div style={{ display: 'flex', justifyContent: 'flex-start', margin: 30 }}>\r\n                    <div style={{ marginLeft: '30px' }}>\r\n                        <div>\r\n                            <Checkbox.Group onChange={(e) => setSelectedAttrList2(e)} defaultValue={selectedAttrNidList}>\r\n                                {attrList.filter(attr => attr.uiControl != eConsoleUiControl.RichTextBox)\r\n                                          .map(_attr => (\r\n                                            <Checkbox key={_attr.attrNid}\r\n                                                      value={_attr.attrNid}\r\n                                                      disabled={_attr.name == '问题编号' || _attr.name == '标题' || _attr.name == '负责人'}\r\n                                                      style={{ margin: 10, width: 130 }} >\r\n                                                {_attr.name}\r\n                                            </Checkbox>\r\n                                        ))\r\n                                }\r\n                            </Checkbox.Group>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </DraggablePopUp>\r\n        }\r\n    </div>\r\n}\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC7E,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,iCAAiC;AACvI,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,oBAAoB;AACnF,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,QAAQ,EAAEC,cAAc,QAAQ,uBAAuB;AAChE,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,QAAQ,kBAAkB;AAChI,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,uBAAuB;AAC1D,OAAO,sBAAsB;AAC7B,SAASC,wCAAwC,QAAQ,yBAAyB;AAClF,SAASC,mCAAmC,EAAEC,mCAAmC,QAAQ,6BAA6B;AACtH,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,yBAAyB,QAAQ,mBAAmB;AAC7D,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,0BAA0B;AAC7E,OAAOC,eAAe,MAAM,oCAAoC;;AAEhE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAM;IAAEC,MAAM;IAAEC,MAAM,EAAEC;EAAgB,CAAC,GAAGxB,SAAS,CAAC,CAAC;EACvD,MAAM;IAAEyB,QAAQ;IAAEC,SAAS;IAAEC,QAAQ;IAAEC,cAAc;IAAEC,gBAAgB;IAAEC,QAAQ;IAAEC,aAAa;IAAEC,aAAa;IAAEC,OAAO;IAAEC,MAAM;IAC3HC,QAAQ;IAAGC,4BAA4B;IAAEC,wBAAwB;IAClEC,iBAAiB;IAAEC,aAAa;IAAEC,kBAAkB;IAAEC,QAAQ;IAACC,gBAAgB;IAAEC,WAAW;IAC5FC,qBAAqB;IAAEC,cAAc;IAAEC,WAAW;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGjD,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACjH,MAAMkD,WAAW,GAAGxD,cAAc,CAAC,CAAC;EACpC,MAAMyD,QAAQ,GAAGpD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,eAAe,CAAC,CAAC;EACzD,MAAMoD,SAAS,GAAGF,YAAY,CAACG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;EACjD,MAAMC,IAAI,GAAGJ,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;EACvC,MAAM,CAACE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM;IAACsF,WAAW;IAAEC,OAAO;IAAEC;EAAqB,CAAC,GAAGlE,oBAAoB,CAAC,CAAC,CAAC;EAC7E,MAAM,CAACmE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC,CAAC;;EAEvD,MAAM,CAAC2F,WAAW,EAAEC,cAAc,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAAC6F,mBAAmB,EAAEC,mBAAmB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACjE;EACA,MAAM,CAAC+F,4BAA4B,EAAEC,+BAA+B,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzF,MAAM;IAAEiG,MAAM,EAAEC;EAAsB,CAAC,GAAG9D,mCAAmC,CAAC,CAAC,CAAC,CAAE;EAClF;;EAEA,MAAM;IAAC+D,IAAI,EAAE;MAAEC,MAAM;MAAEC;IAAa,CAAC,GAAG;MAAED,MAAM,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAE;EAAE,CAAC,GACvEjF,QAAQ,CAAC;IACT,GAAGc,wCAAwC,CAACgB,MAAM;EACtD,CAAC,CAAC;;EAEF;;EAEAnD,SAAS,CAAC,MAAI;IACZ,IAAGkF,SAAS,EAAC;MACXO,oBAAoB,CAAC;QAACD,OAAO,EAAE,CAAC;MAAC,CAAC,CAAC;MACnCe,UAAU,CAAC,MAAI;QACb;QACA;QACDC,oBAAoB,CAAC,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAC,CAACtB,SAAS,CAAC,CAAC;EAEdlF,SAAS,CAAC,MAAI;IACZ,IAAGoF,IAAI,EAAC;MACNK,oBAAoB,CAAC;QAACD,OAAO,EAAE,CAAC;MAAC,CAAC,CAAC;MACnCF,wBAAwB,CAAC,IAAI,CAAC;IAChC;EACF,CAAC,EAAC,CAACF,IAAI,CAAC,CAAC;;EAET;EACApF,SAAS,CAAC,MAAM;IACd,IAAGuD,SAAS,EAAC;MACXkD,2BAA2B,CAAClD,SAAS,CAAC;MACtCmD,kCAAkC,CAAC,CAAC;IACtC;EACF,CAAC,EAAE,CAACC,IAAI,CAACC,SAAS,CAACrD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjCvD,SAAS,CAAC,MAAM;IACZuE,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAC,CAACb,gBAAgB,CAAC,CAAC;EAErB1D,SAAS,CAAC,MAAM;IACZ;EAAA,CACH,EAAE,CAACsG,YAAY,CAAC,CAAC;EAElB,SAASE,oBAAoBA,CAAA,EAAI;IAC7BzE,cAAc,CAAC8E,IAAI,CAAC,2BAA2B,EAAE,EAAE,EAAE;MACnDzD,MAAM,EAAEC,eAAe;MAAEmB,WAAW;MAAEsC,QAAQ,EAAEC;IAClD,CAAC,CAAC;EACJ;EACF;EACA;EACA;EACA,SAASN,2BAA2BA,CAAClD,SAAS,GAAG,EAAE,EAAE;IACjD,MAAMmC,aAAa,GAAGlD,SAAS,CAACe,SAAS,CAAC,CAAC,CAAC;IAC5C,IAAIyD,SAAS,GAAGxD,QAAQ,CAACyD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAIjG,iBAAiB,CAACkG,WAAW,CAAC;IACxFJ,SAAS,CAACK,OAAO,CAAEH,IAAI,IAAK;MACxBA,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACK,SAAS,GAAGL,IAAI,CAACM,OAAO;MACxCN,IAAI,CAACO,KAAK,GAAGP,IAAI,CAACQ,IAAI;MACtB;MACAR,IAAI,CAACS,QAAQ,GAAG,IAAI;;MAEpB;MACA,IAAIT,IAAI,CAACM,OAAO,IAAIvG,cAAc,CAAC2G,uBAAuB,EAAE;QACxD;QACAV,IAAI,CAACW,MAAM,GAAG,CAACC,IAAI,EAAEC,MAAM,KAAK;UAC5B,oBAAO/E,OAAA;YAAGgF,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YAClCC,OAAO,EAAEA,CAAA,KAAM;cACX5C,wBAAwB,CAAC,IAAI,CAAC;cAC9BG,oBAAoB,CAAC;gBAAEnB,QAAQ;gBAAEiB,WAAW,EAAEwC,MAAM,CAAC3E,MAAM;gBAAEoC;cAAQ,CAAC,CAAC;YAC3E,CAAE;YAAA2C,QAAA,EACDL;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QACR,CAAC;MACL;MACA,IAAIrB,IAAI,CAACM,OAAO,IAAIvG,cAAc,CAACuH,qBAAqB,EAAE;QACtD;QACAtB,IAAI,CAACW,MAAM,GAAG,CAACC,IAAI,EAAEW,QAAQ,KAAK;UAChC,oBAAOzF,OAAA,CAACT,gBAAgB;YAACkG,QAAQ,EAAEA,QAAS;YACnBhE,qBAAqB,EAAEA,qBAAsB;YAC7CC,cAAc,EAAEA,cAAe;YAC/BC,WAAW,EAAEA,WAAY;YACzB+D,QAAQ,EAAE;UAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAC5C,CAAC;MACL;MACA,IAAIvG,aAAa,CAACkF,IAAI,CAACC,SAAS,CAAC,EAAE;QAAG;QAClCzB,aAAa,CAAC2B,OAAO,CAAC,CAACsB,KAAK,EAAEC,MAAM,KAAK;UACrC,KAAK,IAAIC,MAAM,IAAIF,KAAK,EAAE;YACtB,IAAIzB,IAAI,CAACM,OAAO,IAAIqB,MAAM,EAAE;cACxB,IAAI3B,IAAI,CAAC4B,YAAY,IAAI,MAAM,EAAE;gBAC7BH,KAAK,CAACE,MAAM,CAAC,GAAG;kBACZE,MAAM,EAAEpI,aAAa,CAACgD,QAAQ,EAAEgF,KAAK,CAACE,MAAM,CAAC,CAAC;kBAC9CG,QAAQ,EAAEtI,eAAe,CAACiD,QAAQ,EAAEgF,KAAK,CAACE,MAAM,CAAC;gBACrD,CAAC;cACL,CAAC,MAAM,IAAIA,MAAM,IAAI5H,cAAc,CAACgI,oBAAoB,EAAE;gBACtDN,KAAK,CAACE,MAAM,CAAC,GAAGpI,oBAAoB,CAACoD,aAAa,EAAEqD,IAAI,CAAC4B,YAAY,EAAEH,KAAK,CAACE,MAAM,CAAC,CAAC,EAAC;cAC1F,CAAC,MAAM;gBACHF,KAAK,CAACE,MAAM,CAAC,GAAGpI,oBAAoB,CAACoD,aAAa,EAAEqD,IAAI,CAAC4B,YAAY,EAAEH,KAAK,CAACE,MAAM,CAAC,CAAC;cACzF;YACJ;UACJ;QACJ,CAAC,CAAC;MACN;MACA;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY;MACA,IAAI3B,IAAI,CAAC4B,YAAY,IAAI,MAAM,EAAE;QAC7B5B,IAAI,CAACW,MAAM,GAAG,CAACC,IAAI,EAAEC,MAAM,KAAK;UAC5B,oBAAO/E,OAAA;YAAKkG,SAAS,EAAC,YAAY;YAAAf,QAAA,GAC7BL,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,QAAQ,gBAAGhG,OAAA,CAAChC,aAAa;cAACmI,SAAS,EAAErB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,MAAO;cAACK,SAAS,EAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG,EAAE,eAC/EvF,OAAA;cAAMgF,KAAK,EAAE;gBAAEqB,UAAU,EAAE;cAAE,CAAE;cAAAlB,QAAA,EAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB;YAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QACV,CAAC;MACL;MACA,IAAIrB,IAAI,CAACC,SAAS,IAAIjG,iBAAiB,CAACoI,IAAI,EAAE;QAC1C;QACD;AACf;AACA;MAFe;IAIP,CAAC,CAAC;IACF3D,gBAAgB,CAACD,aAAa,CAAC;IAC/BG,cAAc,CAACrD,SAAS,CAACwE,SAAS,CAAC,CAAC;IACpC;EACJ;;EAEA;EACA,SAASN,kCAAkCA,CAAA,EAAG;IAC1C,IAAI6C,MAAM,GAAG;MAAEpG,MAAM,EAAEA,MAAM;MAAEqG,OAAO,EAAEpI,WAAW,CAACqI,4BAA4B;MAAEC,SAAS,EAAE,CAAC;MAAEC,SAAS,EAAElG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEkG;IAAW,CAAC;IACvI7I,UAAU,CAAC8I,6BAA6B,CAACL,MAAM,CAAC,CAACM,IAAI,CAAEC,GAAG,IAAK;MAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;QACxB,IAAID,GAAG,CAACE,SAAS,IAAI,CAAC,EAAE;UACpBlE,mBAAmB,CAACmE,MAAM,GAAG,CAAC,CAAC,CAAC;UAChCzG,QAAQ,CAAC6D,OAAO,CAACH,IAAI,IAAI;YACrB,IAAIA,IAAI,CAACC,SAAS,IAAIjG,iBAAiB,CAACkG,WAAW,EAAE;cACjDF,IAAI,CAACgD,SAAS,GAAG,KAAK;YAC1B;YACApE,mBAAmB,CAACqE,IAAI,CAACjD,IAAI,CAACM,OAAO,CAAC;YACtCN,IAAI,CAACgD,SAAS,GAAG,KAAK;UAC1B,CAAC,CAAC;UACF;QACJ,CAAC,MAAM;UACHpE,mBAAmB,CAACmE,MAAM,GAAG,CAAC;UAC9BnE,mBAAmB,CAACqE,IAAI,CAAC,GAAG,CAACL,GAAG,CAACM,SAAS,IAAI,EAAE,EAAEC,GAAG,CAACC,KAAK,IAAIC,MAAM,CAACD,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1FhH,QAAQ,CAAC6D,OAAO,CAACH,IAAI,IAAI;YACrB,IAAIA,IAAI,CAACC,SAAS,IAAIjG,iBAAiB,CAACkG,WAAW,EAAE;cACjDF,IAAI,CAACgD,SAAS,GAAG,KAAK;YAC1B;YACAhD,IAAI,CAACgD,SAAS,GAAG,CAACpE,mBAAmB,CAAC2E,QAAQ,CAACvD,IAAI,CAACM,OAAO,CAAC;UAChE,CAAC,CAAC;QACN;MACJ;MACA3B,cAAc,CAACrC,QAAQ,CAACyD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAIjG,iBAAiB,CAACkG,WAAW,CAAC,CAAC;IAC5F,CAAC,CAAC;EACN;EACH;AACD;AACA;AACA;AACA;AACA;AACA;EACI;EACA,SAASsD,UAAUA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAChC9F,WAAW,CAAC+F,YAAY,CAAC,CAAC3I,QAAQ,EAAEiB,MAAM,EAAEE,eAAe,CAAC,EAAGU,MAAM,IAAI4G,IAAK,CAAC;IAC/EG,QAAQ,CAACC,aAAa,CAAC,kCAAkC,CAAC,CAACC,SAAS,GAAG,CAAC;EAC5E;EACA;EACA,SAASC,oBAAoBA,CAACC,YAAY,EAAE;IACxCnF,mBAAmB,CAACmF,YAAY,CAAC;EACrC;;EAEA;EACA,SAASC,wBAAwBA,CAAA,EAAG;IAChC3H,QAAQ,CAACyD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAIjG,iBAAiB,CAACkG,WAAW,CAAC,CAACC,OAAO,CAACH,IAAI,IAAIA,IAAI,CAACgD,SAAS,GAAG,CAACpE,mBAAmB,CAAC2E,QAAQ,CAACvD,IAAI,CAACM,OAAO,CAAC,CAAC;IACtJ,IAAI4D,OAAO,GAAG;MACVjI,MAAM;MACNqG,OAAO,EAAEpI,WAAW,CAACqI,4BAA4B;MACjDC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAElG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEkG,SAAS;MACpC0B,MAAM,EAAEvF,mBAAmB,CAACuE,GAAG,CAACiB,IAAI,KAAK;QAACd,SAAS,EAAEc;MAAI,CAAC,CAAC;IAC/D,CAAC;IACDnF,qBAAqB,CAACiF,OAAO,EAAE;MAC3BG,SAAS,EAAEA,CAACnF,IAAI,EAAEoF,IAAI,KAAK;QACvBvF,+BAA+B,CAAC,KAAK,CAAC;QACtCJ,cAAc,CAAC4F,MAAM,IAAI;UACrB,OAAOA,MAAM,CAACpB,GAAG,CAACiB,IAAI,IAAI;YACtB,OAAO;cACH,GAAGA,IAAI;cACPpB,SAAS,EAAE,CAACpE,mBAAmB,CAAC2E,QAAQ,CAACa,IAAI,CAAC9D,OAAO;YACzD,CAAC;UACL,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;;EAEA;EACA,SAAST,kBAAkBA,CAACxB,WAAW,EAAE;IACrCE,oBAAoB,CAAC;MAACnB,QAAQ;MAAEiB,WAAW;MAAEC,OAAO;MAAEkG,OAAO,EAAE;IAAK,CAAC,CAAC;EAC1E;EAEA,MAAMC,oBAAoB,GAAGA,CAAA,kBACzB3I,OAAA;IACIgF,KAAK,EAAE;MACH;IAAA,CACF;IAAAG,QAAA,eAEFnF,OAAA;MAAKkG,SAAS,EAAC,uBAAuB;MAAAf,QAAA,gBAClCnF,OAAA;QAAKkG,SAAS,EAAC,kBAAkB;QAAAf,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAE3C3F,aAAa,CAACgC,gBAAgB,CAACgH,SAAS,CAAC,iBACzC5I,OAAA;QAAKkG,SAAS,EAAC,uCAAuC;QAAAf,QAAA,gBAClDnF,OAAA;UAAMgF,KAAK,EAAE;YAAE6D,YAAY,EAAE;UAAE,CAAE;UAAA1D,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5CvF,OAAA;UAAGkF,OAAO,EAAEA,CAAA,KAAK1B,oBAAoB,CAAC,CAAE;UAAA2B,QAAA,GAAC,cAAE,EAAC3D,WAAW,CAACsH,UAAU;QAAA;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,SAASwD,cAAcA,CAAA,EAAE;IACrB,IAAG,CAAC5I,MAAM,IAAI,CAACkD,MAAM,EAAC;MACpB,OAAO/D,UAAU,CAAC0J,KAAK,CAAC,QAAQ,CAAC;IACnC;IACA,IAAIC,OAAO,GAAG3F,YAAY,EAAC;IAC3B;IACA,IAAIiD,MAAM,GAAG;MAAEpG,MAAM;MAAEkD,MAAM;MAAEC,YAAY,EAAE2F;IAAQ,CAAC;IACtD;EACF;EAEJ,eAAgBC,QAAQA,CAAA,EAAE;IAAA,IAAAC,qBAAA;IACxB,OAAO7L,KAAK,CAAC8L,OAAO,CAAC;MACjB3E,KAAK,EAAE,IAAI;MACX4E,IAAI,eAAErJ,OAAA,CAACL,yBAAyB;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnC+D,QAAQ,EAAE,IAAI;MACdC,OAAO,eAAEvJ,OAAA;QAAAmF,QAAA,EAAI,oBAAAgE,qBAAA,GAAmB,MAAMtJ,mBAAmB,CAACM,MAAM,EAAEqB,WAAW,CAACgI,cAAc,CAAC,cAAAL,qBAAA,cAAAA,qBAAA,GAAE,IAAI;MAAS;QAAA/D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;MACjHkE,MAAM,EAAE,UAAU;MAClBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEA,CAAA,KAAM;QACR,IAAG,CAAC,EAACnI,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEgI,cAAc,GAAC;UAC7BzH,QAAQ,CAAC,SAAS5B,MAAM,WAAWqB,WAAW,CAACgI,cAAc,sBAAsB,CAAC;QACxF;MACJ,CAAC;MACDI,QAAQ,EAAEA,CAAA,KAAM;QACZC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACzB;IACA,CAAC,CAAC;EACN;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,oBAAO9J,OAAA;IAAKkG,SAAS,EAAC,gBAAgB;IAAAf,QAAA,gBAClCnF,OAAA;MAAKkG,SAAS,EAAC,aAAa;MAAClB,KAAK,EAAE;QAAE+E,MAAM,EAAE;MAAG,CAAE;MAAA5E,QAAA,gBAC/CnF,OAAA,CAACzC,KAAK;QAACyM,IAAI,EAAE,EAAG;QAAA7E,QAAA,GACX,CAAAxD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsF,MAAM,IAAG,CAAC,IAAIjG,QAAQ,gBAChChB,OAAA;UAAGgF,KAAK,EAAE;YAAEiF,QAAQ,EAAE,EAAE;YAAEhF,KAAK,EAAE;UAAO,CAAE;UAACR,KAAK,EAAC,4BAAQ;UACtDS,OAAO,EAAEA,CAAA,KAAInE,MAAM,GAAC,CAAC,GAAC2G,UAAU,CAAC,CAAC,CAAC,GAAC,IAAK;UAAAvC,QAAA,GAAC,eAAG,eAAAnF,OAAA;YAAMkG,SAAS,EAAC,UAAU;YAAAf,QAAA,EAAE7E;UAAQ;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,WAAE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAChGvF,OAAA;UAAGgF,KAAK,EAAE;YAAEiF,QAAQ,EAAE,EAAE;YAAEhF,KAAK,EAAE;UAAO,CAAE;UAACR,KAAK,EAAC,4BAAQ;UACtDS,OAAO,EAAEA,CAAA,KAAInE,MAAM,GAAC,CAAC,GAAC2G,UAAU,CAAC,CAAC,CAAC,GAAC,IAAK;UAAAvC,QAAA,GAAC,eAAG,eAAAnF,OAAA;YAAMkG,SAAS,EAAC,UAAU;YAAAf,QAAA,EAAE7E;UAAQ;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,WAAE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAapG3F,aAAa,CAACgC,gBAAgB,CAACgH,SAAS,CAAC,IAAI/G,cAAc,iBAC5D7B,OAAA,CAAC9C,MAAM;UAACgJ,SAAS,EAAC,YAAY;UAACgE,IAAI,EAAC,SAAS;UACrChF,OAAO,EAAEA,CAAA,KAAMpE,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqJ,KAAK,GAAEjB,QAAQ,CAAC,CAAC,GAAE1F,oBAAoB,CAAC,CAAE;UAAA2B,QAAA,GAAC,IACtE,EAAC3D,WAAW,CAACsH,UAAU;QAAA;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAET,CAAC,eACRvF,OAAA;QAAGkF,OAAO,EAAEA,CAAA,KAAMjC,+BAA+B,CAAC,IAAI,CAAE;QACpDiD,SAAS,EAAC,aAAa;QACvBlB,KAAK,EAAE;UAAEoF,KAAK,EAAE,OAAO;UAAEC,WAAW,EAAE,EAAE;UAAEpF,KAAK,EAAC;QAAO,CAAE;QAAAE,QAAA,gBACzDnF,OAAA;UAAMkG,SAAS,EAAC;QAA8B;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAErD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEJ9F,OAAO,CAACiD,aAAa,CAAC,gBACrB1C,OAAA;MACGgF,KAAK,EAAE;QACH;MAAA,CACF;MAAAG,QAAA,eAEJnF,OAAA;QAAKkG,SAAS,EAAC,sBAAsB;QAAAf,QAAA,gBACjCnF,OAAA;UAAKkG,SAAS,EAAC,kBAAkB;UAAAf,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAE3C3F,aAAa,CAACgC,gBAAgB,CAACgH,SAAS,CAAC,iBACzC5I,OAAA;UAAKkG,SAAS,EAAC,uCAAuC;UAAAf,QAAA,gBAClDnF,OAAA;YAAMgF,KAAK,EAAE;cAAE6D,YAAY,EAAE;YAAE,CAAE;YAAA1D,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5CvF,OAAA;YAAGkF,OAAO,EAAEA,CAAA,KAAK1B,oBAAoB,CAAC,CAAE;YAAA2B,QAAA,GAAC,cAAE,EAAC3D,WAAW,CAACsH,UAAU;UAAA;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBACNvF,OAAA,CAAC5C,cAAc;MAACkN,WAAW,EAAE3B,oBAAqB;MAAAxD,QAAA,eAChDnF,OAAA,CAACN,cAAc;QACXwG,SAAS,EAAC,UAAU;QACpBqE,QAAQ,EAAE,KAAM;QAChBvF,KAAK,EAAE;UAAE+E,MAAM,EAAE;QAAO,CAAE;QAC1BS,MAAM,EAAE;UAAEC,wBAAwB,EAAE,IAAI;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAA;QACvDC,OAAO,EAAEhI,WAAW,CAACqB,MAAM,CAAC4G,KAAK,IAAI,CAACA,KAAK,CAAC3D,SAAS,CAAE;QACvD4D,UAAU,EAAEpI,aAAc;QAC1BsH,IAAI,EAAE,OAAQ;QACde,YAAY,EAAGC,MAAM,IAAKA,MAAM,CAAC5K,MAAM,IAAImC,WAAW,GAAG,YAAY,GAAG;QACxE;QAAA;QACA0I,KAAK,EAAGD,MAAM,KAAM;UAChB9F,OAAO,EAAGgG,CAAC,IAAK;YACZzI,oBAAoB,CAAC;cAACnB,QAAQ;cAAEiB,WAAW,EAAEyI,MAAM,CAAC5K,MAAM;cAAEoC;YAAO,CAAC,CAAC,EAAC;YACtE;UACJ;QACJ,CAAC,CAAE;QACH2I,UAAU,EACN;UACIC,QAAQ,EAAE,CAAC,cAAc,CAAC;UAC1BpB,IAAI,EAAE,OAAO;UACbqB,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,KAAK;UACtBC,SAAS,EAAE,IAAI;UACf3D,QAAQ,EAAE,EAAE;UACZ4D,OAAO,EAAEzK,MAAM;UACf0K,cAAc,EAAE1K,MAAM;UACtB2K,KAAK,EAAEpL,QAAQ;UACfqL,SAAS,EAAGD,KAAK,IAAK,MAAMA,KAAK,IAAI;UACrCE,QAAQ,EAAElE;QACd;MACH;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,eAEjBvF,OAAA,CAACF,eAAe;MACZ+L,cAAc;MACd3F,SAAS,EAAC,iBAAiB;MAC3B4F,QAAQ,EAAC,KAAK;MACdC,QAAQ,EAAC,KAAK;MACdC,aAAa,EAAE,IAAK;MACpBC,aAAa,EAAC,KAAK;MACnBC,aAAa,EAAC,KAAK;MACnBC,OAAO,EAAEA,CAAA,KAAM7J,wBAAwB,CAAC,KAAK,CAAE;MAC/CF,IAAI,EAAEC,qBAAsB;MAC5B+J,QAAQ,EAAE,IAAK;MACf3H,KAAK,eACDzE,OAAA;QAAAmF,QAAA,EAAO,GAAG3D,WAAW,CAACsH,UAAU;MAAI;QAAA1D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAC9C;MAAAJ,QAAA,eAEDnF,OAAA,CAACjC,WAAW;QACRuD,QAAQ,EAAEA,QAAS;QACnBiB,WAAW,EAAEA,WAAY;QACzB1B,aAAa,EAAEA,aAAc;QAC7BF,QAAQ,EAAEA,QAAS;QACnBC,aAAa,EAAEA,aAAc;QAC7BO,iBAAiB,EAAEA,iBAAkB;QACrCC,aAAa,EAAEA,aAAc;QAC7BH,4BAA4B,EAAEA,4BAA6B;QAC3DC,wBAAwB,EAAEA,wBAAyB;QACnDM,WAAW,EAAEA,WAAY;QACzBE,cAAc,EAAEA,cAAe;QAC/BL,kBAAkB,EAAEA,kBAAmB;QACvCd,SAAS,EAAEA;MAAU;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,EAEjBvC,4BAA4B,iBACzBhD,OAAA,CAACxC,cAAc;MAAC0I,SAAS,EAAC,WAAW;MAAC2F,cAAc;MAChDpH,KAAK,eAAEzE,OAAA;QAAKkG,SAAS,EAAC,aAAa;QAAAf,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAC/CnD,IAAI,EAAE,IAAK;MACXwH,QAAQ,EAAEA,CAAA,KAAM3G,+BAA+B,CAAC,KAAK,CAAE;MACvDoJ,KAAK,EAAE,GAAI;MACXC,MAAM,EAAE,cACJtM,OAAA,CAAC9C,MAAM;QAAC8H,KAAK,EAAE;UAAEuH,SAAS,EAAE;QAAS,CAAE;QAACrC,IAAI,EAAC,SAAS;QAAChF,OAAO,EAAEiD,wBAAyB;QAAAhD,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,CACvG;MAAAJ,QAAA,eAEFnF,OAAA;QAAKgF,KAAK,EAAE;UAAEwH,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,YAAY;UAAEC,MAAM,EAAE;QAAG,CAAE;QAAAvH,QAAA,eACtEnF,OAAA;UAAKgF,KAAK,EAAE;YAAEqB,UAAU,EAAE;UAAO,CAAE;UAAAlB,QAAA,eAC/BnF,OAAA;YAAAmF,QAAA,eACInF,OAAA,CAAC7C,QAAQ,CAACwP,KAAK;cAACf,QAAQ,EAAGV,CAAC,IAAKjD,oBAAoB,CAACiD,CAAC,CAAE;cAAC0B,YAAY,EAAE9J,mBAAoB;cAAAqC,QAAA,EACvF3E,QAAQ,CAACyD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAIjG,iBAAiB,CAACkG,WAAW,CAAC,CAC9DiD,GAAG,CAACwD,KAAK,iBACR7K,OAAA,CAAC7C,QAAQ;gBACC0P,KAAK,EAAEhC,KAAK,CAACrG,OAAQ;gBACrBsI,QAAQ,EAAEjC,KAAK,CAACnG,IAAI,IAAI,MAAM,IAAImG,KAAK,CAACnG,IAAI,IAAI,IAAI,IAAImG,KAAK,CAACnG,IAAI,IAAI,KAAM;gBAC5EM,KAAK,EAAE;kBAAE0H,MAAM,EAAE,EAAE;kBAAEL,KAAK,EAAE;gBAAI,CAAE;gBAAAlH,QAAA,EACvC0F,KAAK,CAACnG;cAAI,GAJAmG,KAAK,CAACrG,OAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKlB,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEpB,CAAC;AACV;AAACrF,EAAA,CAtbuBD,aAAa;EAAA,QACWpB,SAAS,EAIwCD,gBAAgB,EACzFN,cAAc,EACjBK,WAAW,EACYG,eAAe,EAIDP,oBAAoB,EAQhCc,mCAAmC,EAIzEhB,QAAQ;AAAA;AAAA0O,EAAA,GAxBQ9M,aAAa;AAAA,IAAA8M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}