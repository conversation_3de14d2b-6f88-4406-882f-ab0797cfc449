import { Button, Image, Modal, Space } from "antd";
import { useParams } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import * as https from "../api/http"
import { globalUtil } from "@common/utils/globalUtil";
import { setting_320_get_node_priv_query } from "@common/api/query/query";
import { useEffect, useState } from "react";
import { globalEventBus } from "@common/utils/eventBus";

/**
 * 
 * @param {*} code 401：应用已过期-非管理员 402：应用已过期-管理员 403：用户未被授权应用 404：管理员未被授权应用
 * @returns 
 */
export default function ProductError({code}){
  const queryClient = useQueryClient();
  const {teamId, nodeId, nid} = useParams();
  const [productId, setProductId] = useState(0);

  const setting334Mutation = useMutation({
    mutationFn: https.setting_334_apply_authorization
  })

  useEffect(() => {
    let setting320Query = setting_320_get_node_priv_query(teamId, nid||nodeId);
    let setting320Result = queryClient.getQueryData(setting320Query.queryKey);
    setProductId(setting320Result?.noAuth?.productId);
  }, []);

  const sendApply = () => {
    if(!!productId) {
      setting334Mutation.mutate({ teamId, productId }, {
        onSuccess: (result) => {
          if(result.resultCode === 200) {
            //globalUtil.success("提交申请成功！")
            Modal.info({
              title: "提示",
              content: "提交成功，管理员会接收到申请信息，请您耐心等候",
              maskClosable: true,
              //centered: true, // 居中
              okText: "我知道了",
              width: 500,
            });
          }
        }
      })
    }
  }

  const openSettingsDrawer = (tab = 'product') => {
    globalEventBus.emit("openSettingsDrawerEvent", null, { teamId, defaultTab:tab, productId });
  }

  const code401 = <>
    <div style={{fontSize:"14px",color:"#333"}}>应用已过期</div>
    <div style={{fontSize:"14px",color:"#333"}}>请联系团队管理员续费</div>
  </>

  const code402 = <>
    <div style={{fontSize:"14px",color:"#333"}}>应用已过期，请购买或续费</div>
    <Button type="primary" onClick={() => openSettingsDrawer('product')}>去续费</Button>
  </>

  const code403 = <>
    <div style={{fontSize:"14px",color:"#333"}}>您暂无权限访问当前对象</div>
    <div style={{fontSize:"14px",color:"#333"}}>向管理员<a onClick={sendApply}>申请授权</a></div>
  </>

  const code404 = <>
    <div style={{fontSize:"14px",color:"#333"}}>您未在应用授权列表中</div>
    <Button type="primary" onClick={() => openSettingsDrawer('product')}>去授权</Button>
  </>

  return <div style={{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",background:"#fff"}}>
    <Space size={10} direction="vertical" align="center">
      <Image src={require("@assets/images/product_error.png")} preview={false} style={{width:"250px",marginBottom:"20px"}}/>
      {code == 401 && code401}
      {code == 402 && code402}
      {code == 403 && code403}
      {code == 404 && code404}
    </Space>
  </div>
}