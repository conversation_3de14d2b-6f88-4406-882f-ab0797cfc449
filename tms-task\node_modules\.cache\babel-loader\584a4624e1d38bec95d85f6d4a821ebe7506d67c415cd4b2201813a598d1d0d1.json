{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\CheckAppUpdate.jsx\",\n  _s = $RefreshSig$();\nimport { local_check_version_update } from \"@common/api/http\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport React, { useEffect, useState } from \"react\";\nimport { Modal, Divider, Typography } from \"antd\";\nimport * as storageConstant from \"@common/utils/storageConstant\";\nimport { eEnableFlg } from \"@common/utils/enum\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nexport default function CheckAppUpdate() {\n  _s();\n  const [systemUpdateTips, setSystemUpdateTips] = useState(true);\n\n  // 版本更新\n  const versionQuery = useQuery({\n    queryFn: async () => {\n      return local_check_version_update({\n        timestamp: Date.now()\n      }).then(result => {\n        return result.version || process.env.REACT_APP_VERSION;\n      });\n    },\n    enabled: process.env.NODE_ENV === \"production\" && localStorage.getItem(storageConstant.no_polling) != eEnableFlg.enable,\n    cacheTime: Infinity,\n    // 每15秒会刷新一次接口\n    refetchInterval: 15000,\n    refetchOnWindowFocus: true\n  });\n  useEffect(() => {\n    if (versionQuery.dataUpdatedAt && systemUpdateTips && Number(versionQuery.data) !== Number(process.env.REACT_APP_VERSION)) {\n      setSystemUpdateTips(false);\n      Modal.confirm({\n        title: \"新版本更新\",\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u5F53\\u524D\\u7248\\u672C \", process.env.REACT_APP_VERSION]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u68C0\\u6D4B\\u5230\\u6700\\u65B0\\u7248\\u672C \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#fa86d9'\n              },\n              children: versionQuery.data\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u70B9\\u51FB\\u201C\\u66F4\\u65B0\\u201D\\u6309\\u94AE\\u6216\\u8005\\u5237\\u65B0\\u9875\\u9762\\uFF0C\\u8BBF\\u95EE\\u6700\\u65B0\\u7248\\u672C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 18\n        }, this),\n        okText: '更新',\n        cancelText: '暂不更新',\n        onOk: () => {\n          window.location.reload(true);\n        }\n      });\n    }\n  }, [versionQuery.dataUpdatedAt]);\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 10\n  }, this);\n}\n_s(CheckAppUpdate, \"hLIbqKfh0DdA5BjQkbCCXGtZ314=\", false, function () {\n  return [useQuery];\n});\n_c = CheckAppUpdate;\nvar _c;\n$RefreshReg$(_c, \"CheckAppUpdate\");", "map": {"version": 3, "names": ["local_check_version_update", "useQuery", "React", "useEffect", "useState", "Modal", "Divider", "Typography", "storageConstant", "eEnableFlg", "jsxDEV", "_jsxDEV", "Title", "Text", "CheckAppUpdate", "_s", "systemUpdateTips", "setSystemUpdateTips", "versionQuery", "queryFn", "timestamp", "Date", "now", "then", "result", "version", "process", "env", "REACT_APP_VERSION", "enabled", "NODE_ENV", "localStorage", "getItem", "no_polling", "enable", "cacheTime", "Infinity", "refetchInterval", "refetchOnWindowFocus", "dataUpdatedAt", "Number", "data", "confirm", "title", "content", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "okText", "cancelText", "onOk", "window", "location", "reload", "Fragment", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/CheckAppUpdate.jsx"], "sourcesContent": ["import { local_check_version_update } from \"@common/api/http\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Modal, Divider, Typography } from \"antd\";\r\nimport * as storageConstant from \"@common/utils/storageConstant\";\r\nimport { eEnableFlg } from \"@common/utils/enum\";\r\n\r\nconst { Title, Text } = Typography;\r\n\r\nexport default function CheckAppUpdate() {\r\n  const [systemUpdateTips,setSystemUpdateTips] = useState(true);\r\n\r\n  // 版本更新\r\n  const versionQuery = useQuery({\r\n    queryFn: async() => {\r\n      return local_check_version_update({timestamp: Date.now()}).then((result) => {\r\n        return result.version || process.env.REACT_APP_VERSION\r\n      })\r\n    },\r\n    enabled: process.env.NODE_ENV === \"production\" && (localStorage.getItem(storageConstant.no_polling ) != eEnableFlg.enable),\r\n    cacheTime: Infinity,\r\n    // 每15秒会刷新一次接口\r\n    refetchInterval: 15000,\r\n    refetchOnWindowFocus: true\r\n  })\r\n\r\n  useEffect(() => {\r\n    if(versionQuery.dataUpdatedAt && systemUpdateTips && Number(versionQuery.data) !== Number(process.env.REACT_APP_VERSION)) {\r\n      setSystemUpdateTips(false);\r\n      Modal.confirm({\r\n        title: \"新版本更新\",\r\n        content: <div>\r\n          <div>当前版本 {process.env.REACT_APP_VERSION}</div>\r\n          <div>检测到最新版本 <span style={{color: '#fa86d9'}}>{versionQuery.data}</span></div>\r\n          <div>点击“更新”按钮或者刷新页面，访问最新版本</div>\r\n        </div>,\r\n        okText: '更新',\r\n        cancelText: '暂不更新',\r\n        onOk: () => {\r\n          window.location.reload(true);\r\n        }\r\n      })\r\n    }\r\n  },[versionQuery.dataUpdatedAt])\r\n\r\n  return <React.Fragment></React.Fragment>\r\n}"], "mappings": ";;AAAA,SAASA,0BAA0B,QAAQ,kBAAkB;AAC7D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AACjD,OAAO,KAAKC,eAAe,MAAM,+BAA+B;AAChE,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGN,UAAU;AAElC,eAAe,SAASO,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM,CAACC,gBAAgB,EAACC,mBAAmB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;;EAE7D;EACA,MAAMc,YAAY,GAAGjB,QAAQ,CAAC;IAC5BkB,OAAO,EAAE,MAAAA,CAAA,KAAW;MAClB,OAAOnB,0BAA0B,CAAC;QAACoB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MAAC,CAAC,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAK;QAC1E,OAAOA,MAAM,CAACC,OAAO,IAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB;MACxD,CAAC,CAAC;IACJ,CAAC;IACDC,OAAO,EAAEH,OAAO,CAACC,GAAG,CAACG,QAAQ,KAAK,YAAY,IAAKC,YAAY,CAACC,OAAO,CAACxB,eAAe,CAACyB,UAAW,CAAC,IAAIxB,UAAU,CAACyB,MAAO;IAC1HC,SAAS,EAAEC,QAAQ;IACnB;IACAC,eAAe,EAAE,KAAK;IACtBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;EAEFnC,SAAS,CAAC,MAAM;IACd,IAAGe,YAAY,CAACqB,aAAa,IAAIvB,gBAAgB,IAAIwB,MAAM,CAACtB,YAAY,CAACuB,IAAI,CAAC,KAAKD,MAAM,CAACd,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC,EAAE;MACxHX,mBAAmB,CAAC,KAAK,CAAC;MAC1BZ,KAAK,CAACqC,OAAO,CAAC;QACZC,KAAK,EAAE,OAAO;QACdC,OAAO,eAAEjC,OAAA;UAAAkC,QAAA,gBACPlC,OAAA;YAAAkC,QAAA,GAAK,2BAAK,EAACnB,OAAO,CAACC,GAAG,CAACC,iBAAiB;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CtC,OAAA;YAAAkC,QAAA,GAAK,6CAAQ,eAAAlC,OAAA;cAAMuC,KAAK,EAAE;gBAACC,KAAK,EAAE;cAAS,CAAE;cAAAN,QAAA,EAAE3B,YAAY,CAACuB;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9EtC,OAAA;YAAAkC,QAAA,EAAK;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;QACNG,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,MAAM;QAClBC,IAAI,EAAEA,CAAA,KAAM;UACVC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAC,CAACvC,YAAY,CAACqB,aAAa,CAAC,CAAC;EAE/B,oBAAO5B,OAAA,CAACT,KAAK,CAACwD,QAAQ;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAiB,CAAC;AAC1C;AAAClC,EAAA,CArCuBD,cAAc;EAAA,QAIfb,QAAQ;AAAA;AAAA0D,EAAA,GAJP7C,cAAc;AAAA,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}