{"ast": null, "code": "var _a;\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as nls from '../../nls.js';\nconst LANGUAGE_DEFAULT = 'en';\nlet _isWindows = false;\nlet _isMacintosh = false;\nlet _isLinux = false;\nlet _isLinuxSnap = false;\nlet _isNative = false;\nlet _isWeb = false;\nlet _isElectron = false;\nlet _isIOS = false;\nlet _isCI = false;\nlet _locale = undefined;\nlet _language = LANGUAGE_DEFAULT;\nlet _translationsConfigFile = undefined;\nlet _userAgent = undefined;\nexport const globals = typeof self === 'object' ? self : typeof global === 'object' ? global : {};\nlet nodeProcess = undefined;\nif (typeof globals.vscode !== 'undefined' && typeof globals.vscode.process !== 'undefined') {\n  // Native environment (sandboxed)\n  nodeProcess = globals.vscode.process;\n} else if (typeof process !== 'undefined') {\n  // Native environment (non-sandboxed)\n  nodeProcess = process;\n}\nconst isElectronProcess = typeof ((_a = nodeProcess === null || nodeProcess === void 0 ? void 0 : nodeProcess.versions) === null || _a === void 0 ? void 0 : _a.electron) === 'string';\nconst isElectronRenderer = isElectronProcess && (nodeProcess === null || nodeProcess === void 0 ? void 0 : nodeProcess.type) === 'renderer';\n// Web environment\nif (typeof navigator === 'object' && !isElectronRenderer) {\n  _userAgent = navigator.userAgent;\n  _isWindows = _userAgent.indexOf('Windows') >= 0;\n  _isMacintosh = _userAgent.indexOf('Macintosh') >= 0;\n  _isIOS = (_userAgent.indexOf('Macintosh') >= 0 || _userAgent.indexOf('iPad') >= 0 || _userAgent.indexOf('iPhone') >= 0) && !!navigator.maxTouchPoints && navigator.maxTouchPoints > 0;\n  _isLinux = _userAgent.indexOf('Linux') >= 0;\n  _isWeb = true;\n  const configuredLocale = nls.getConfiguredDefaultLocale(\n  // This call _must_ be done in the file that calls `nls.getConfiguredDefaultLocale`\n  // to ensure that the NLS AMD Loader plugin has been loaded and configured.\n  // This is because the loader plugin decides what the default locale is based on\n  // how it's able to resolve the strings.\n  nls.localize({\n    key: 'ensureLoaderPluginIsLoaded',\n    comment: ['{Locked}']\n  }, '_'));\n  _locale = configuredLocale || LANGUAGE_DEFAULT;\n  _language = _locale;\n}\n// Native environment\nelse if (typeof nodeProcess === 'object') {\n  _isWindows = nodeProcess.platform === 'win32';\n  _isMacintosh = nodeProcess.platform === 'darwin';\n  _isLinux = nodeProcess.platform === 'linux';\n  _isLinuxSnap = _isLinux && !!nodeProcess.env['SNAP'] && !!nodeProcess.env['SNAP_REVISION'];\n  _isElectron = isElectronProcess;\n  _isCI = !!nodeProcess.env['CI'] || !!nodeProcess.env['BUILD_ARTIFACTSTAGINGDIRECTORY'];\n  _locale = LANGUAGE_DEFAULT;\n  _language = LANGUAGE_DEFAULT;\n  const rawNlsConfig = nodeProcess.env['VSCODE_NLS_CONFIG'];\n  if (rawNlsConfig) {\n    try {\n      const nlsConfig = JSON.parse(rawNlsConfig);\n      const resolved = nlsConfig.availableLanguages['*'];\n      _locale = nlsConfig.locale;\n      // VSCode's default language is 'en'\n      _language = resolved ? resolved : LANGUAGE_DEFAULT;\n      _translationsConfigFile = nlsConfig._translationsConfigFile;\n    } catch (e) {}\n  }\n  _isNative = true;\n}\n// Unknown environment\nelse {\n  console.error('Unable to resolve platform.');\n}\nlet _platform = 0 /* Platform.Web */;\nif (_isMacintosh) {\n  _platform = 1 /* Platform.Mac */;\n} else if (_isWindows) {\n  _platform = 3 /* Platform.Windows */;\n} else if (_isLinux) {\n  _platform = 2 /* Platform.Linux */;\n}\nexport const isWindows = _isWindows;\nexport const isMacintosh = _isMacintosh;\nexport const isLinux = _isLinux;\nexport const isNative = _isNative;\nexport const isWeb = _isWeb;\nexport const isWebWorker = _isWeb && typeof globals.importScripts === 'function';\nexport const isIOS = _isIOS;\nexport const userAgent = _userAgent;\n/**\n * The language used for the user interface. The format of\n * the string is all lower case (e.g. zh-tw for Traditional\n * Chinese)\n */\nexport const language = _language;\nexport const setTimeout0IsFaster = typeof globals.postMessage === 'function' && !globals.importScripts;\n/**\n * See https://html.spec.whatwg.org/multipage/timers-and-user-prompts.html#:~:text=than%204%2C%20then-,set%20timeout%20to%204,-.\n *\n * Works similarly to `setTimeout(0)` but doesn't suffer from the 4ms artificial delay\n * that browsers set when the nesting level is > 5.\n */\nexport const setTimeout0 = (() => {\n  if (setTimeout0IsFaster) {\n    const pending = [];\n    globals.addEventListener('message', e => {\n      if (e.data && e.data.vscodeScheduleAsyncWork) {\n        for (let i = 0, len = pending.length; i < len; i++) {\n          const candidate = pending[i];\n          if (candidate.id === e.data.vscodeScheduleAsyncWork) {\n            pending.splice(i, 1);\n            candidate.callback();\n            return;\n          }\n        }\n      }\n    });\n    let lastId = 0;\n    return callback => {\n      const myId = ++lastId;\n      pending.push({\n        id: myId,\n        callback: callback\n      });\n      globals.postMessage({\n        vscodeScheduleAsyncWork: myId\n      }, '*');\n    };\n  }\n  return callback => setTimeout(callback);\n})();\nexport const OS = _isMacintosh || _isIOS ? 2 /* OperatingSystem.Macintosh */ : _isWindows ? 1 /* OperatingSystem.Windows */ : 3 /* OperatingSystem.Linux */;\nlet _isLittleEndian = true;\nlet _isLittleEndianComputed = false;\nexport function isLittleEndian() {\n  if (!_isLittleEndianComputed) {\n    _isLittleEndianComputed = true;\n    const test = new Uint8Array(2);\n    test[0] = 1;\n    test[1] = 2;\n    const view = new Uint16Array(test.buffer);\n    _isLittleEndian = view[0] === (2 << 8) + 1;\n  }\n  return _isLittleEndian;\n}\nexport const isChrome = !!(userAgent && userAgent.indexOf('Chrome') >= 0);\nexport const isFirefox = !!(userAgent && userAgent.indexOf('Firefox') >= 0);\nexport const isSafari = !!(!isChrome && userAgent && userAgent.indexOf('Safari') >= 0);\nexport const isEdge = !!(userAgent && userAgent.indexOf('Edg/') >= 0);\nexport const isAndroid = !!(userAgent && userAgent.indexOf('Android') >= 0);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}