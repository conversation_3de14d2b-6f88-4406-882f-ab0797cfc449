{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\inspect\\\\views\\\\InspectProject\\\\CreatePartitionDrawer\\\\CreatePartitionDrawer.jsx\",\n  _s = $RefreshSig$();\n/*\r\n * @Author: Walt <EMAIL>\r\n * @Date: 2024-02-02 17:53:39\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2025-08-05 17:28:10\r\n * @Description: 采集口径\r\n */\n/* eslint-disable react-hooks/exhaustive-deps */\nimport { ExclamationCircleOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';\nimport * as http from \"@common/api/http\";\nimport { useQuerySetting202_getTeamAllUsers, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\nimport { compareArr, isEmpty } from '@common/utils/ArrayUtils';\nimport { eConsolePropId, eEditingMode, eEnableFlg, eOpType, eSelectionListId } from \"@common/utils/enum\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { getQueryableAttrNodeList, transformAttrNodeListForUI, transformCriteriaListForUI, getFlowList } from '@common/utils/logicUtils';\nimport * as toolUtil from \"@common/utils/toolUtil\";\nimport { eNodeTypeId, refreshTeamMenu, refreshSelectTeamMenu, inspCreatePartitionNodeTypeList } from \"@common/utils/TsbConfig\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { Button, Drawer, Form, Input, Modal, Space, Checkbox, Select } from \"antd\";\nimport { useEffect, useRef, useState, useMemo } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport SearchDisplayField from 'src/quickAcess/views/Search/SearchDetail/SearchDisplayField';\nimport SearchEditTable from \"src/quickAcess/views/Search/SearchDetail/SearchEditTable\";\nimport SearchReultPreviewModal from 'src/quickAcess/views/Search/SearchDetail/SearchReultPreviewModal';\nimport { useQueryTrack019GetPartitionDetail, useQuerySetting409_getTeamAttrgrpProps } from \"src/issueTrack/service/issueHooks\";\nimport AddPartitionAttr from \"./AddPartitionAttr\";\nimport CustomerFormTable from \"./CustomerFormTable\";\nimport { eRegionType } from \"src/inspect/utils/enum\";\nimport UploadLoading from \"@components/UploadLoading\";\nimport TEmpty from '@components/TEmpty';\nimport \"./CreatePartitionDrawer.scss\";\nimport { insp_091_get_insp_project_detail_query } from \"@common/api/query/inspect/query_insp_01_mgmt\";\nimport { useGetInspectRelatedNodes, useQueryCust008GetCustFormList, useQueryCust007GetCustObjList } from \"src/inspect/service/inspectHooks\";\nimport DraggableDrawer from \"@components/DraggableDrawer\";\n\n// 新建&编辑 采集口径\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CreatePartitionDrawer() {\n  _s();\n  var _projectInfo$issueAli, _projectInfo$issueAli2, _projectInfo$issueAli3;\n  const {\n    teamId\n  } = useParams();\n  const [form] = Form.useForm();\n  const tRef = useRef(Object.create(null));\n  // 弹窗显示配置\n  const [isModalVisible, setIsModalVisible] = useState(false); //采集口径弹窗\n  const [searchPreFlg, setSearchPreFlg] = useState(false); //是否搜索预览\n  const [searchDisplayFieldOpen, setSearchDisplayFieldOpen] = useState(false); //显示字段弹窗\n  const [isAddAttrModalVisible, setIsAddAttrModalVisible] = useState(false); //添加字段弹窗\n  // 数据配置\n  const [nodeItem, setNodeItem] = useState({}); //节点信息\n  // const [opType, setOpType] = useState(eEditingMode.Creating_0); //0:新建 1：编辑\n  const [selectFields, setSelectFields] = useState([]); //已选择的表单字段\n  const [initTableData, setInitTableData] = useState([]); // 初始数据\n  const [attrNodeList, setAttrNodeList] = useState([]); // 字段属性列表\n  const [checkedValues, setCheckedValues] = useState([]); // 显示字段勾选\n  const [modalKey, setModalKey] = useState(toolUtil.guid()); // 显示字段勾选\n\n  const [flowList, setFlowList] = useState([]); // 流程图列表\n  const [uploadLoading, setUploadLoading] = useState(false); // 提交加载中\n  const [showFormFields, setShowFormFields] = useState(true); // 控制表单字段设置的显示\n\n  const opType = useMemo(() => {\n    return inspCreatePartitionNodeTypeList.includes(nodeItem.nodeType) ? eEditingMode.Modifying_1 : eEditingMode.Creating_0; // 如果nodeType为分区节点，则为编辑采集口径\n  }, [nodeItem]);\n  const {\n    data: {\n      isCust,\n      isOA\n    },\n    isLoading: isLoadingInsp072\n  } = useGetInspectRelatedNodes({\n    teamId,\n    nodeId: nodeItem.nodeId,\n    enabled: !!nodeItem.nodeId\n  });\n\n  // 接口调用\n  //（1）获取项目信息和可发起流程\n  const {\n    data: projectInfo = {},\n    isLoading: isLoadingInsp091,\n    dataUpdatedAt: dataUpdatedAtInsp091\n  } = useQuery(insp_091_get_insp_project_detail_query(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, !!(nodeItem !== null && nodeItem !== void 0 && nodeItem.nodeId)));\n\n  // 获取自定义表单列表\n  const {\n    data: customFormData = {},\n    isLoading: isLoadingCust008\n  } = useQueryCust008GetCustFormList({\n    teamId,\n    nodeId: nodeItem.nodeId,\n    enabled: !!isCust && !!nodeItem.nodeId\n  });\n\n  // 定义模式相关配置\n  const modeConfig = {\n    custom: {\n      fieldName: 'nodeId',\n      label: '选择表单',\n      getSelectedItem: id => {\n        var _customFormData$formL;\n        return customFormData === null || customFormData === void 0 ? void 0 : (_customFormData$formL = customFormData.formList) === null || _customFormData$formL === void 0 ? void 0 : _customFormData$formL.find(form => form.nodeId == id);\n      },\n      getItemList: () => {\n        var _customFormData$formL2;\n        return (customFormData === null || customFormData === void 0 ? void 0 : (_customFormData$formL2 = customFormData.formList) === null || _customFormData$formL2 === void 0 ? void 0 : _customFormData$formL2.map(form => ({\n          key: form.nodeId,\n          value: form.nodeId,\n          label: form.formName\n          // icon: form.subclassIcon\n        }))) || [];\n      },\n      getDefaultKey: list => list.length > 0 ? list[0].key : undefined,\n      getRevertKey: item => item === null || item === void 0 ? void 0 : item.nodeId,\n      objType: eNodeTypeId.nt_871_cust_objlist,\n      paramKey: 'processdefNodeId'\n    },\n    normal: {\n      fieldName: 'processdefNodeId',\n      label: '选择流程',\n      getSelectedItem: id => {\n        var _projectInfo$flowList, _projectInfo$flowList2, _projectInfo$flowList3;\n        return projectInfo === null || projectInfo === void 0 ? void 0 : (_projectInfo$flowList = projectInfo.flowList) === null || _projectInfo$flowList === void 0 ? void 0 : (_projectInfo$flowList2 = _projectInfo$flowList.filter) === null || _projectInfo$flowList2 === void 0 ? void 0 : (_projectInfo$flowList3 = _projectInfo$flowList2.call(_projectInfo$flowList, flow => flow.enableFlg == eEnableFlg.enable)) === null || _projectInfo$flowList3 === void 0 ? void 0 : _projectInfo$flowList3.find(flow => flow.processdefNodeId == id);\n      },\n      getItemList: () => {\n        const [flowList] = getFlowList((projectInfo === null || projectInfo === void 0 ? void 0 : projectInfo.flowList) || [], selectionList);\n        return flowList;\n      },\n      getDefaultKey: (list, defaultFlow) => defaultFlow === null || defaultFlow === void 0 ? void 0 : defaultFlow.key,\n      getRevertKey: item => item === null || item === void 0 ? void 0 : item.processdefNodeId,\n      objType: isOA ? eNodeTypeId.nt_671_oa_processList : eNodeTypeId.nt_571_insp_processList,\n      paramKey: 'processdefNodeId'\n    }\n  };\n\n  // 当前模式配置\n  const currentMode = useMemo(() => {\n    return isCust ? modeConfig.custom : modeConfig.normal;\n  }, [isCust, modeConfig]);\n  const processdefNodeId = Form.useWatch(currentMode.fieldName, form);\n  const selecedtProcess = useMemo(() => currentMode.getSelectedItem(processdefNodeId), [projectInfo, customFormData, processdefNodeId, currentMode]);\n\n  //（2）获取自定义表单字段\n  const {\n    subclassAttrList = [],\n    isLoading: isLoadingGetSubclassAttrs,\n    dataUpdatedAt: dataUpdatedAtSetting409\n  } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, selecedtProcess === null || selecedtProcess === void 0 ? void 0 : selecedtProcess.subclassId, !!(selecedtProcess !== null && selecedtProcess !== void 0 && selecedtProcess.subclassId));\n\n  // (2.1) 编辑时获取采集口径详情 TODO: useQuery cacheTime 需要在相关key变化时才会清除缓存数据，所以需要一个变量key去触发再次打开的更新操作\n  const {\n    attrList,\n    criteriaList = [],\n    bizNodeId,\n    objType,\n    createFlg,\n    dataUpdatedAt: dataUpdatedAtTrack019,\n    refetch: refetchGetPartitionDetail\n  } = useQueryTrack019GetPartitionDetail(teamId, nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeId, modalKey, opType == eEditingMode.Modifying_1 && !!(nodeItem !== null && nodeItem !== void 0 && nodeItem.nodeId));\n  //（3）字典数据\n  const {\n    data: selectionList\n  } = useQuerySetting407_getCodeValueList(teamId);\n  //（4）获取人员列表\n  const {\n    data: userList\n  } = useQuerySetting202_getTeamAllUsers(teamId);\n\n  // 选择样式\n  const selectStyle = {\n    width: 300,\n    borderRadius: 5\n  };\n  const isLoading = isLoadingInsp091 || isLoadingGetSubclassAttrs || !!isCust && isLoadingCust008 || uploadLoading;\n\n  // 监听打开新建/编辑采集口径弹窗\n  useEffect(() => {\n    globalEventBus.on(\"openCreatePartitionDrawerEvent\", openCreatePartitionDrawerEvent);\n    return () => globalEventBus.off(\"openCreatePartitionDrawerEvent\", openCreatePartitionDrawerEvent);\n  }, []);\n\n  // 打开创建项目弹窗事件\n  const openCreatePartitionDrawerEvent = (target, args) => {\n    setModalKey(toolUtil.guid());\n    setNodeItem(args);\n    setIsModalVisible(true);\n  };\n\n  // 打开弹窗/关闭弹窗\n  useEffect(() => {\n    if (isModalVisible && opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtSetting409) {\n      // 加载数据\n      loadIssuePartitionDetail();\n    } else {\n      // 初始化数据\n      initialIssuePartitionData();\n    }\n  }, [isModalVisible, dataUpdatedAtTrack019, dataUpdatedAtSetting409]);\n\n  // 打开搜索预览弹窗\n  const openSearchReultPreviewModal = requst => {\n    globalEventBus.emit(\"openSearchReultPreviewModalEvent\", \"\", {\n      searchPreRequest: requst,\n      onDisplayClick: onDisplayClick.bind(this)\n    });\n  };\n\n  // 自定义表单字段\n  useEffect(() => {\n    if (!isLoadingGetSubclassAttrs) {\n      debugger;\n      changePropertyTypeList(subclassAttrList);\n    }\n  }, [isLoadingGetSubclassAttrs]);\n\n  // 编辑 自定义表单\n  useEffect(() => {\n    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && !isEmpty(attrNodeList)) {\n      changeCriteriaList();\n    }\n  }, [opType, dataUpdatedAtTrack019, JSON.stringify(attrNodeList)]);\n\n  // 编辑 回显流程 注意有先后依赖关系不可合并\n  useEffect(() => {\n    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtInsp091) {\n      form.setFieldValue(currentMode.fieldName, bizNodeId);\n    }\n  }, [opType, dataUpdatedAtTrack019, dataUpdatedAtInsp091, JSON.stringify(currentMode)]);\n  useEffect(() => {\n    if (isModalVisible) {\n      const itemList = currentMode.getItemList();\n      setFlowList(itemList);\n      if (opType == eEditingMode.Creating_0) {\n        const defaultFlow = isCust ? null : getFlowList((projectInfo === null || projectInfo === void 0 ? void 0 : projectInfo.flowList) || [], selectionList)[1];\n        const defaultKey = currentMode.getDefaultKey(itemList, defaultFlow);\n        if (defaultKey) {\n          form.setFieldValue(currentMode.fieldName, defaultKey);\n        }\n      }\n    }\n  }, [isModalVisible, JSON.stringify(projectInfo), isCust, JSON.stringify(currentMode)]);\n\n  // 自定义表单数据处理\n  const changePropertyTypeList = attrgrpProps => {\n    let attrList = transformAttrNodeListForUI(attrgrpProps, userList, selectionList, true);\n    const checkedValues = attrList.filter(item => item.checked).map(item => item.nodeId);\n    const properTypeListUI = getQueryableAttrNodeList(attrList); //过滤59类型\n    setAttrNodeList(properTypeListUI);\n    setCheckedValues(checkedValues);\n  };\n\n  // 编辑搜索数据\n  const changeCriteriaList = () => {\n    let _criteriaList = (criteriaList || []).filter(criteria => attrNodeList.some(subclass => criteria.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17\n    _criteriaList = transformCriteriaListForUI(_criteriaList, attrNodeList, selectionList);\n    setInitTableData(_criteriaList);\n  };\n\n  // 初始化数据\n  function initialIssuePartitionData() {\n    // console.log(\"正在清空数据...\")\n    setInitTableData([]); //清空高级搜索数据\n    setSelectFields([]); //清空字段属性数据\n    setShowFormFields(true); //重置表单字段设置显示状态\n  }\n\n  // 加载采集口径详情\n  function loadIssuePartitionDetail() {\n    const isCreateEnabled = createFlg == eEnableFlg.enable;\n    form.setFieldsValue({\n      name: nodeItem.name,\n      // name回显\n      createFlg: isCreateEnabled\n    });\n\n    // 根据createFlg设置表单字段设置的显示状态\n    setShowFormFields(isCreateEnabled);\n    const _attrList = attrList.filter(attr => subclassAttrList.some(subclass => attr.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17\n    _attrList.forEach(attr => {\n      var _propertyList$find, _propertyList$find2;\n      const propertyList = subclassAttrList.find(subclass => attr.attrNid == subclass.nodeId).propertyList;\n      attr.attrModifyableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\n      attr.attrQueryableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\n      attr.uiControl = (_propertyList$find = propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control)) === null || _propertyList$find === void 0 ? void 0 : _propertyList$find.propValue;\n      attr.selectionId = ((_propertyList$find2 = propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)) === null || _propertyList$find2 === void 0 ? void 0 : _propertyList$find2.propValue) || \"\";\n    });\n    setSelectFields(_attrList);\n  }\n\n  // 默认值配置\n  const getDefAttrPropValuepByType = (propList = [], type) => {\n    return (propList.find(item => item.propType == type) || {\n      propValue: \"0\"\n    }).propValue;\n  };\n\n  // 取消\n  const handleCancel = () => {\n    return Modal.confirm({\n      title: \"提示\",\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: opType == eOpType.add ? '正在新建采集口径，是否放弃编辑?' : '正在编辑采集口径，是否放弃编辑?'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 16\n      }, this),\n      okText: \"确定\",\n      cancelText: \"取消\",\n      onOk: () => {\n        setIsModalVisible(false);\n      },\n      onCancel: () => {\n        console.log(\"Cancel\");\n      }\n    });\n  };\n\n  // 选择字段数据处理\n  const onSelectFields = items => {\n    let fieldList = [];\n    selectFields.filter(el => {\n      items.map((_el, index) => {\n        if (el.attrNid == _el.nodeId) {\n          fieldList.push(el);\n          delete items[index];\n        }\n      });\n    });\n    items.map(item => {\n      var _propertyList$find3, _propertyList$find4, _propertyList$find5;\n      let propertyList = item.propertyList;\n      const _attrModifyableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\n      const _attrQueryableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\n      let field = {\n        attrNid: item.nodeId,\n        defaultVal: null,\n        attrVisibleFlg: '',\n        attrModifyableFlg: _attrModifyableFlg,\n        attrModifyableFlgBack: _attrModifyableFlg,\n        attrQueryableFlg: _attrQueryableFlg,\n        attrQueryableFlgBack: _attrQueryableFlg,\n        uiControl: (_propertyList$find3 = propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control)) === null || _propertyList$find3 === void 0 ? void 0 : _propertyList$find3.propValue,\n        selectionId: ((_propertyList$find4 = propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)) === null || _propertyList$find4 === void 0 ? void 0 : _propertyList$find4.propValue) || \"\",\n        isRegion: ((_propertyList$find5 = propertyList.find(el => el.propType == eConsolePropId.Prop_28_is_region)) === null || _propertyList$find5 === void 0 ? void 0 : _propertyList$find5.propValue) || \"\" // 是否是检查区域字段\n      };\n      fieldList.push(field);\n    });\n    setSelectFields(fieldList);\n    setIsAddAttrModalVisible(false);\n  };\n\n  // + 动态条件\n  const handleOnAddSearchCode = () => {\n    tRef.current.addSearchCode();\n  };\n\n  // 搜索预览 无需校验搜索名称\n  const handleSearchPreClick = () => {\n    setSearchPreFlg(true);\n    form.submit();\n  };\n\n  // 显示字段\n  const onDisplayClick = e => {\n    setSearchDisplayFieldOpen(true);\n  };\n\n  // 显示字段数据处理\n  const assembleQueryAttrList = () => {\n    return checkedValues.map(checkedValue => ({\n      attrNid: checkedValue\n    }));\n  };\n\n  // 保存显示字段\n  const handleSearchDisplayFieldOnOk = (e, values) => {\n    setSearchDisplayFieldOpen(false);\n    setCheckedValues(values);\n    globalEventBus.emit(\"changeSearchReultPreviewEvent\", \"\", {\n      values\n    }); // 根据显示字段过滤\n  };\n\n  // 取消保存字段\n  const handleSearchDisplayFieldOnCancel = (e, values) => {\n    if (!compareArr(checkedValues, values)) {\n      Modal.confirm({\n        title: '提醒',\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 15\n        }, this),\n        content: \"放弃后将不会保存显示字段，确定要放弃？\",\n        okText: '确定',\n        cancelText: '取消',\n        onOk: () => {\n          setSearchDisplayFieldOpen(false);\n        }\n      });\n    } else {\n      setSearchDisplayFieldOpen(false);\n    }\n  };\n\n  // 点击 确定\n  const handleSaveClick = () => {\n    setSearchPreFlg(false);\n    form.submit();\n  };\n\n  // 表单提交 form.submit()\n  const onFinish = async values => {\n    var _tRef$current, _tRef$current$getCrit;\n    const {\n      name\n    } = values;\n    if (!searchPreFlg && !name) {\n      // 校验名称\n      return globalUtil.warning(\"请填写采集口径名称!\");\n    }\n    let criteriaList = (tRef === null || tRef === void 0 ? void 0 : (_tRef$current = tRef.current) === null || _tRef$current === void 0 ? void 0 : (_tRef$current$getCrit = _tRef$current.getCriteriaListForBackend) === null || _tRef$current$getCrit === void 0 ? void 0 : _tRef$current$getCrit.call(_tRef$current)) || [];\n    // 搜索预览\n    if (searchPreFlg) {\n      const queryAttrList = assembleQueryAttrList();\n      let requst = {\n        \"teamId\": teamId,\n        \"bizNodeId\": values[currentMode.fieldName],\n        \"name\": name,\n        \"objType\": currentMode.objType,\n        \"advanceQueryFlg\": \"1\",\n        \"criteriaList\": criteriaList,\n        \"queryAttrList\": queryAttrList\n      };\n      // 搜索预览\n      requst = {\n        ...requst,\n        pageNum: 1\n      }; //默认查询第一页\n      return openSearchReultPreviewModal(requst);\n    }\n    setUploadLoading(true);\n    // TODO: 检查项后端校验存在问题,先固定传null by walt from jack  2024-02-05\n    selectFields.forEach((el, index) => {\n      if (el.isRegion == eRegionType.check) {\n        el.defaultVal = null;\n      }\n    });\n    //  后端只需要这些参数，去除多余的不接受的参数\n    let _selectFields = selectFields.map((el, index) => ({\n      attrNid: el.attrNid,\n      defaultVal: el.defaultVal,\n      attrVisibleFlg: el.attrVisibleFlg,\n      attrModifyableFlg: el.attrModifyableFlg,\n      attrModifyableFlgBack: el.attrModifyableFlgBack,\n      attrQueryableFlg: el.attrQueryableFlg,\n      attrQueryableFlgBack: el.attrQueryableFlgBack,\n      seqNo: index + 1\n    }));\n\n    // 基础参数\n    let params = {\n      teamId,\n      name,\n      attrList: _selectFields,\n      criteriaList,\n      createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable\n    };\n\n    // 根据操作类型添加不同的参数\n    if (opType) {\n      // 编辑模式\n      params.nodeId = nodeItem.nodeId;\n      params[currentMode.paramKey] = values[currentMode.fieldName];\n      await editPartition(params);\n    } else {\n      // 新建模式\n      params.objNodeId = nodeItem.nodeId;\n      params[currentMode.paramKey] = values[currentMode.fieldName];\n      await createPartition(params);\n    }\n    setUploadLoading(false);\n  };\n\n  // 新建采集口径\n  async function createPartition(params) {\n    await http.track_018_create_issue_partition(params).then(result => {\n      if (result.resultCode == 200) {\n        var _result$nodeTree$;\n        refreshSelectTeamMenu({\n          treeNode: (result === null || result === void 0 ? void 0 : result.nodeTree[0]) || {}\n        });\n        setIsModalVisible(false);\n        nodeItem.callback && nodeItem.callback((result === null || result === void 0 ? void 0 : (_result$nodeTree$ = result.nodeTree[0]) === null || _result$nodeTree$ === void 0 ? void 0 : _result$nodeTree$.children[0]) || {});\n      }\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n\n  // 编辑采集口径\n  async function editPartition(params) {\n    await http.track_020_modify_issue_partition(params).then(result => {\n      if (result.resultCode == 200) {\n        refreshTeamMenu();\n        setIsModalVisible(false);\n        refetchGetPartitionDetail(); // 编辑采集口径后刷新数据\n      }\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n  const handleOnChange = value => {\n    var _tRef$current2, _tRef$current3, _tRef$current3$getCri;\n    let criteriaList = (tRef === null || tRef === void 0 ? void 0 : (_tRef$current2 = tRef.current) === null || _tRef$current2 === void 0 ? void 0 : _tRef$current2.getCriteriaListForBackend) && (tRef === null || tRef === void 0 ? void 0 : (_tRef$current3 = tRef.current) === null || _tRef$current3 === void 0 ? void 0 : (_tRef$current3$getCri = _tRef$current3.getCriteriaListForBackend) === null || _tRef$current3$getCri === void 0 ? void 0 : _tRef$current3$getCri.call(_tRef$current3));\n    if (isEmpty(criteriaList) && isEmpty(selectFields)) {\n      return;\n    }\n    return Modal.confirm({\n      title: \"提示\",\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: `重新选择${isCust ? '表单' : '流程'}，下方两个表格将清空，您需要重新设置。是否继续?`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 16\n      }, this),\n      okText: \"是\",\n      cancelText: \"否\",\n      zIndex: 1002,\n      // FIXME:不能超过10000，否则会导致Select下拉框被覆盖\n      onOk: () => {},\n      onCancel: () => {\n        console.log(\"Cancel\");\n        form.setFieldValue(currentMode.fieldName, currentMode.getRevertKey(selecedtProcess));\n      }\n    });\n  };\n\n  // 处理\"可新建\"复选框变化\n  const handleCreateFlgChange = e => {\n    const checked = e.target.checked;\n\n    // 如果取消勾选且表单字段设置有值，弹出确认对话框\n    if (!checked && !isEmpty(selectFields)) {\n      Modal.confirm({\n        title: \"提示\",\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 15\n        }, this),\n        content: \"取消可新建功能，则清除表单字段设置，是否取消？\",\n        okText: \"取消\",\n        cancelText: \"不取消\",\n        zIndex: 1002,\n        onOk: () => {\n          // 用户确认取消，清除表单字段设置并隐藏\n          setSelectFields([]);\n          setShowFormFields(false);\n          form.setFieldValue('createFlg', false);\n        },\n        onCancel: () => {\n          // 用户选择不取消，恢复勾选状态\n          form.setFieldValue('createFlg', true);\n        }\n      });\n    } else if (checked) {\n      // 如果重新勾选，显示表单字段设置\n      setShowFormFields(true);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n    className: \"tms-drawer IssuePartition\",\n    width: \"60%\",\n    title: opType ? `采集口径设置` : `新建采集口径`,\n    destroyOnClose: true,\n    open: isModalVisible,\n    onClose: handleCancel,\n    footer: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"right\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: 20,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            borderRadius: 5\n          },\n          onClick: handleCancel,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          style: {\n            borderRadius: 5\n          },\n          onClick: handleSaveClick,\n          children: \"\\u63D0\\u4EA4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 13\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(UploadLoading, {\n      spinning: isLoading,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        labelCol: {\n          span: 4\n        },\n        wrapperCol: {\n          span: 19\n        },\n        onFinish: onFinish,\n        preserve: false // Modal关闭后销毁form字段数据\n        ,\n        autoComplete: \"off\" // 取消自动补充功能\n        ,\n        initialValues: {\n          createFlg: true\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"采集口径名称\",\n          name: \"name\",\n          required: true,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            style: selectStyle,\n            autoComplete: \"off\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: currentMode.label,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            size: 20,\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: currentMode.fieldName,\n              required: true,\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                showSearch: true,\n                style: {\n                  width: 300,\n                  borderRadius: 3\n                }\n                //select选择框搜索\n                ,\n                filterOption: (input, option) => {\n                  return (option.children.props.children[1].props.children || \"\").toLowerCase().indexOf(input.toLowerCase()) >= 0;\n                },\n                onChange: handleOnChange,\n                children: flowList.map(flow => /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: flow.value,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    },\n                    children: [flow.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        paddingLeft: 5\n                      },\n                      children: flow.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 21\n                  }, this)\n                }, flow.key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"createFlg\",\n              valuePropName: \"checked\",\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                onChange: handleCreateFlgChange,\n                children: \"\\u53EF\\u65B0\\u5EFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"自定义搜索\",\n          children: isEmpty(attrNodeList) ? /*#__PURE__*/_jsxDEV(TEmpty, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 36\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(SearchEditTable, {\n              attrNodeList: attrNodeList,\n              exprs: [],\n              criteriaList: initTableData,\n              selectionList: selectionList,\n              queryType: 0,\n              ref: tRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-edit-btns\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {\n                  className: \"color-yellow\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this),\n                onClick: handleOnAddSearchCode,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"+ \\u52A8\\u6001\\u6761\\u4EF6\\uFF08\", /*#__PURE__*/_jsxDEV(FunctionOutlined, {\n                    style: {\n                      margin: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 32\n                  }, this), \"\\uFF09\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 31\n                }, this),\n                className: \"defaultBtn_light\",\n                onClick: handleSearchPreClick,\n                children: \"\\u7ED3\\u679C\\u9884\\u89C8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remarks\",\n              children: [\"\\u5907\\u6CE81\\uFF1A\\u6B64\\u5904\\u7684\\u81EA\\u5B9A\\u4E49\\u641C\\u7D22\\uFF0C\\u7528\\u4E8E\\u663E\\u793A\\u5F53\\u524D\\u91C7\\u96C6\\u53E3\\u5F84\\u5BF9\\u5E94\\u7684\", (_projectInfo$issueAli = projectInfo.issueAlias) !== null && _projectInfo$issueAli !== void 0 ? _projectInfo$issueAli : \"\", \"\\u5217\\u8868\\u3002\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remarks\",\n              children: \"\\u5907\\u6CE82\\uFF1A\\u70B9\\u51FB\\u786E\\u5B9A\\u6309\\u94AE\\u4EC5\\u4FDD\\u5B58\\u8868\\u5355(\\u5373\\u6761\\u4EF6)\\uFF0C\\u9884\\u89C8\\u7ED3\\u679C\\u4E0D\\u505A\\u4FDD\\u5B58\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 9\n        }, this), showFormFields && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"表单字段设置\",\n          children: [/*#__PURE__*/_jsxDEV(CustomerFormTable, {\n            selectFields: selectFields,\n            setSelectFields: setSelectFields,\n            selectionList: selectionList,\n            userList: userList,\n            subclassAttrList: subclassAttrList,\n            selecedtProcess: selecedtProcess\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            className: \"fontsize-12\",\n            onClick: () => setIsAddAttrModalVisible(true),\n            children: \"+ \\u6DFB\\u52A0\\u5B57\\u6BB5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"remarks\",\n            children: [\"\\u5907\\u6CE81\\uFF1A\\u5728\", (_projectInfo$issueAli2 = projectInfo.issueAlias) !== null && _projectInfo$issueAli2 !== void 0 ? _projectInfo$issueAli2 : \"\", \"\\u9879\\u76EE\\\"\", projectInfo.name, \"\\\"\\u7684\\u81EA\\u5B9A\\u4E49\\u5B57\\u6BB5\\u7684\\u57FA\\u7840\\u4E0A\\uFF0C\\u8FDB\\u4E00\\u6B65\\u9650\\u7F29\\u4E0A\\u9762\\u8868\\u683C\\u4E2D\\u5B57\\u6BB5\\u7684\\u662F\\u5426\\u663E\\u793A/\\u53EF\\u4FEE\\u6539/\\u53EF\\u641C\\u7D22\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"remarks\",\n            children: [\"\\u5907\\u6CE82\\uFF1A\\u5728\\u5F53\\u524D\\u91C7\\u96C6\\u53E3\\u5F84\\u4E2D\\u63D0\\u4EA4\\u7684\\u65B0\\u5EFA\", (_projectInfo$issueAli3 = projectInfo.issueAlias) !== null && _projectInfo$issueAli3 !== void 0 ? _projectInfo$issueAli3 : \"\", \"\\uFF0C\\u5176\\u5BF9\\u5E94\\u4E0A\\u9762\\u8868\\u683C\\u4E2D\\u7684\\u5B57\\u6BB5\\uFF0C\\u5982\\u679C\\u503C\\u4E3A\\u7A7A\\uFF0C\\u5219\\u4F7F\\u7528\\\"\\u7F3A\\u7701\\u503C\\\"\\u5217\\u4E2D\\u7684\\u503C\\uFF0C\\u8FDB\\u884C\\u6700\\u540E\\u7684\\u6570\\u636E\\u5B58\\u50A8\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(AddPartitionAttr, {\n      selectFields: selectFields,\n      attrList: subclassAttrList,\n      visible: isAddAttrModalVisible,\n      onSelectFields: onSelectFields,\n      onCancel: () => setIsAddAttrModalVisible(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(SearchReultPreviewModal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(SearchDisplayField, {\n      open: searchDisplayFieldOpen,\n      onOk: handleSearchDisplayFieldOnOk,\n      onCancel: handleSearchDisplayFieldOnCancel,\n      nodeName: nodeItem === null || nodeItem === void 0 ? void 0 : nodeItem.nodeName,\n      checkedValues: checkedValues,\n      attrNodeList: attrNodeList\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 483,\n    columnNumber: 10\n  }, this);\n}\n_s(CreatePartitionDrawer, \"7t5yRMNGI8jdU/VZnNOzrBSYwl0=\", false, function () {\n  return [useParams, Form.useForm, useGetInspectRelatedNodes, useQuery, useQueryCust008GetCustFormList, Form.useWatch, useQuerySetting409_getTeamAttrgrpProps, useQueryTrack019GetPartitionDetail, useQuerySetting407_getCodeValueList, useQuerySetting202_getTeamAllUsers];\n});\n_c = CreatePartitionDrawer;\nvar _c;\n$RefreshReg$(_c, \"CreatePartitionDrawer\");", "map": {"version": 3, "names": ["ExclamationCircleOutlined", "FunctionOutlined", "QuestionCircleOutlined", "SearchOutlined", "http", "useQuerySetting202_getTeamAllUsers", "useQuerySetting407_getCodeValueList", "compareArr", "isEmpty", "eConsolePropId", "eEditingMode", "eEnableFlg", "eOpType", "eSelectionListId", "globalEventBus", "globalUtil", "getQueryableAttrNodeList", "transformAttrNodeListForUI", "transformCriteriaListForUI", "getFlowList", "toolUtil", "eNodeTypeId", "refreshTeamMenu", "refreshSelectTeamMenu", "inspCreatePartitionNodeTypeList", "useQuery", "<PERSON><PERSON>", "Drawer", "Form", "Input", "Modal", "Space", "Checkbox", "Select", "useEffect", "useRef", "useState", "useMemo", "useParams", "SearchDisplayField", "SearchEditTable", "SearchReultPreviewModal", "useQueryTrack019GetPartitionDetail", "useQuerySetting409_getTeamAttrgrpProps", "AddPartitionAttr", "CustomerFormTable", "eRegionType", "UploadLoading", "TEmpty", "insp_091_get_insp_project_detail_query", "useGetInspectRelatedNodes", "useQueryCust008GetCustFormList", "useQueryCust007GetCustObjList", "DraggableDrawer", "jsxDEV", "_jsxDEV", "CreatePartitionDrawer", "_s", "_projectInfo$issueAli", "_projectInfo$issueAli2", "_projectInfo$issueAli3", "teamId", "form", "useForm", "tRef", "Object", "create", "isModalVisible", "setIsModalVisible", "searchPreFlg", "setSearchPreFlg", "searchDisplayFieldOpen", "setSearchDisplayFieldOpen", "isAddAttrModalVisible", "setIsAddAttrModalVisible", "nodeItem", "setNodeItem", "selectFields", "setSelectFields", "initTableData", "setInitTableData", "attrNodeList", "setAttrNodeList", "checkedValues", "setCheckedValues", "modalKey", "setModalKey", "guid", "flowList", "setFlowList", "uploadLoading", "setUploadLoading", "showFormFields", "<PERSON>S<PERSON><PERSON><PERSON><PERSON><PERSON>s", "opType", "includes", "nodeType", "Modifying_1", "Creating_0", "data", "isCust", "isOA", "isLoading", "isLoadingInsp072", "nodeId", "enabled", "projectInfo", "isLoadingInsp091", "dataUpdatedAt", "dataUpdatedAtInsp091", "customFormData", "isLoadingCust008", "modeConfig", "custom", "fieldName", "label", "getSelectedItem", "id", "_customFormData$formL", "formList", "find", "getItemList", "_customFormData$formL2", "map", "key", "value", "formName", "getDefaultKey", "list", "length", "undefined", "getRevertKey", "item", "objType", "nt_871_cust_objlist", "<PERSON><PERSON><PERSON><PERSON>", "normal", "_projectInfo$flowList", "_projectInfo$flowList2", "_projectInfo$flowList3", "filter", "call", "flow", "enableFlg", "enable", "processdefNodeId", "selectionList", "defaultFlow", "nt_671_oa_processList", "nt_571_insp_processList", "currentMode", "useWatch", "selecedtProcess", "subclassAttrList", "isLoadingGetSubclassAttrs", "dataUpdatedAtSetting409", "subclassId", "attrList", "criteriaList", "bizNodeId", "createFlg", "dataUpdatedAtTrack019", "refetch", "refetchGetPartitionDetail", "userList", "selectStyle", "width", "borderRadius", "on", "openCreatePartitionDrawerEvent", "off", "target", "args", "loadIssuePartitionDetail", "initialIssuePartitionData", "openSearchReultPreviewModal", "requst", "emit", "searchPreRequest", "onDisplayClick", "bind", "changePropertyTypeList", "changeCriteriaList", "JSON", "stringify", "setFieldValue", "itemList", "defaultKey", "attrgrpProps", "checked", "properTypeListUI", "_criteriaList", "criteria", "some", "subclass", "attrNid", "isCreateEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "_attrList", "attr", "for<PERSON>ach", "_propertyList$find", "_propertyList$find2", "propertyList", "attrModifyableFlgBack", "getDefAttrPropValuepByType", "Prop_60_modifiable", "attrQueryableFlgBack", "Prop_59_queryable", "uiControl", "el", "propType", "Prop_14_ui_control", "propValue", "selectionId", "_el", "Prop_12_selection", "propList", "type", "handleCancel", "confirm", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "children", "add", "okText", "cancelText", "onOk", "onCancel", "console", "log", "onSelectFields", "items", "fieldList", "index", "push", "_propertyList$find3", "_propertyList$find4", "_propertyList$find5", "_attrModifyableFlg", "_attrQueryableFlg", "field", "defaultVal", "attrVisibleFlg", "attrModifyableFlg", "attrQueryableFlg", "isRegion", "Prop_28_is_region", "handleOnAddSearchCode", "current", "addSearchCode", "handleSearchPreClick", "submit", "e", "assembleQueryAttrList", "checkedValue", "handleSearchDisplayFieldOnOk", "values", "handleSearchDisplayFieldOnCancel", "handleSaveClick", "onFinish", "_tRef$current", "_tRef$current$getCrit", "warning", "getCriteriaListForBackend", "queryAttrList", "pageNum", "check", "_selectFields", "seqNo", "params", "disable", "editPartition", "objNodeId", "createPartition", "track_018_create_issue_partition", "then", "result", "resultCode", "_result$nodeTree$", "treeNode", "nodeTree", "callback", "catch", "err", "track_020_modify_issue_partition", "handleOnChange", "_tRef$current2", "_tRef$current3", "_tRef$current3$getCri", "zIndex", "handleCreateFlgChange", "className", "destroyOnClose", "open", "onClose", "footer", "style", "textAlign", "size", "onClick", "spinning", "labelCol", "span", "wrapperCol", "preserve", "autoComplete", "initialValues", "<PERSON><PERSON>", "required", "noStyle", "showSearch", "filterOption", "input", "option", "props", "toLowerCase", "indexOf", "onChange", "Option", "display", "alignItems", "paddingLeft", "valuePropName", "exprs", "queryType", "ref", "margin", "issueAlias", "visible", "nodeName", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/inspect/views/InspectProject/CreatePartitionDrawer/CreatePartitionDrawer.jsx"], "sourcesContent": ["/*\r\n * @Author: <PERSON> <EMAIL>\r\n * @Date: 2024-02-02 17:53:39\r\n * @LastEditors: Walt <EMAIL>\r\n * @LastEditTime: 2025-08-05 17:28:10\r\n * @Description: 采集口径\r\n */\r\n/* eslint-disable react-hooks/exhaustive-deps */\r\nimport { ExclamationCircleOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';\r\nimport * as http from \"@common/api/http\";\r\nimport { useQuerySetting202_getTeamAllUsers, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\r\nimport { compareArr, isEmpty } from '@common/utils/ArrayUtils';\r\nimport { eConsolePropId, eEditingMode, eEnableFlg, eOpType, eSelectionListId, } from \"@common/utils/enum\";\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { getQueryableAttrNodeList, transformAttrNodeListForUI, transformCriteriaListForUI, getFlowList } from '@common/utils/logicUtils';\r\nimport * as toolUtil from \"@common/utils/toolUtil\";\r\nimport { eNodeTypeId, refreshTeamMenu, refreshSelectTeamMenu, inspCreatePartitionNodeTypeList } from \"@common/utils/TsbConfig\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { Button, Drawer, Form, Input, Modal, Space, Checkbox, Select } from \"antd\";\r\nimport { useEffect, useRef, useState, useMemo } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport SearchDisplayField from 'src/quickAcess/views/Search/SearchDetail/SearchDisplayField';\r\nimport SearchEditTable from \"src/quickAcess/views/Search/SearchDetail/SearchEditTable\";\r\nimport SearchReultPreviewModal from 'src/quickAcess/views/Search/SearchDetail/SearchReultPreviewModal';\r\nimport { useQueryTrack019GetPartitionDetail, useQuerySetting409_getTeamAttrgrpProps } from \"src/issueTrack/service/issueHooks\";\r\nimport AddPartitionAttr from \"./AddPartitionAttr\";\r\nimport CustomerFormTable from \"./CustomerFormTable\";\r\nimport { eRegionType } from \"src/inspect/utils/enum\";\r\nimport UploadLoading from \"@components/UploadLoading\";\r\nimport TEmpty from '@components/TEmpty';\r\nimport \"./CreatePartitionDrawer.scss\";\r\nimport {insp_091_get_insp_project_detail_query} from \"@common/api/query/inspect/query_insp_01_mgmt\";\r\nimport { useGetInspectRelatedNodes, useQueryCust008GetCustFormList, useQueryCust007GetCustObjList } from \"src/inspect/service/inspectHooks\";\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\n\r\n\r\n// 新建&编辑 采集口径\r\nexport default function CreatePartitionDrawer() {\r\n  const { teamId } = useParams();\r\n  const [form] = Form.useForm();\r\n  const tRef = useRef(Object.create(null));\r\n  // 弹窗显示配置\r\n  const [isModalVisible, setIsModalVisible] = useState(false); //采集口径弹窗\r\n  const [searchPreFlg, setSearchPreFlg] = useState(false); //是否搜索预览\r\n  const [searchDisplayFieldOpen, setSearchDisplayFieldOpen] = useState(false); //显示字段弹窗\r\n  const [isAddAttrModalVisible, setIsAddAttrModalVisible] = useState(false); //添加字段弹窗\r\n  // 数据配置\r\n  const [nodeItem, setNodeItem] = useState({}); //节点信息\r\n  // const [opType, setOpType] = useState(eEditingMode.Creating_0); //0:新建 1：编辑\r\n  const [selectFields, setSelectFields] = useState([]); //已选择的表单字段\r\n  const [initTableData, setInitTableData] = useState([]); // 初始数据\r\n  const [attrNodeList, setAttrNodeList] = useState([]); // 字段属性列表\r\n  const [checkedValues, setCheckedValues] = useState([]); // 显示字段勾选\r\n  const [modalKey, setModalKey] = useState(toolUtil.guid()); // 显示字段勾选\r\n\r\n  const [flowList, setFlowList] = useState([]);                 // 流程图列表\r\n  const [uploadLoading, setUploadLoading] = useState(false); // 提交加载中\r\n  const [showFormFields, setShowFormFields] = useState(true); // 控制表单字段设置的显示\r\n\r\n  const opType = useMemo(()=>{\r\n    return inspCreatePartitionNodeTypeList.includes(nodeItem.nodeType) ? eEditingMode.Modifying_1 : eEditingMode.Creating_0  // 如果nodeType为分区节点，则为编辑采集口径\r\n  },[nodeItem])\r\n\r\n  const { data: { isCust, isOA }, isLoading: isLoadingInsp072 } = useGetInspectRelatedNodes({teamId, nodeId: nodeItem.nodeId, enabled: !!nodeItem.nodeId,}) \r\n\r\n  // 接口调用\r\n  //（1）获取项目信息和可发起流程\r\n  const { data: projectInfo = {}, isLoading: isLoadingInsp091, dataUpdatedAt: dataUpdatedAtInsp091 } = useQuery(insp_091_get_insp_project_detail_query(teamId, nodeItem?.nodeId, !!nodeItem?.nodeId))\r\n  \r\n  // 获取自定义表单列表\r\n  const { data: customFormData = {}, isLoading: isLoadingCust008 } = useQueryCust008GetCustFormList({teamId, nodeId: nodeItem.nodeId, enabled: !!isCust && !!nodeItem.nodeId});\r\n  \r\n  // 定义模式相关配置\r\n  const modeConfig = {\r\n    custom: {\r\n      fieldName: 'nodeId',\r\n      label: '选择表单',\r\n      getSelectedItem: (id) => customFormData?.formList?.find(form => form.nodeId == id),\r\n      getItemList: () => customFormData?.formList?.map(form => ({\r\n        key: form.nodeId,\r\n        value: form.nodeId,\r\n        label: form.formName,\r\n        // icon: form.subclassIcon\r\n      })) || [],\r\n      getDefaultKey: (list) => list.length > 0 ? list[0].key : undefined,\r\n      getRevertKey: (item) => item?.nodeId,\r\n      objType: eNodeTypeId.nt_871_cust_objlist,\r\n      paramKey: 'processdefNodeId'\r\n    },\r\n    normal: {\r\n      fieldName: 'processdefNodeId',\r\n      label: '选择流程',\r\n      getSelectedItem: (id) => projectInfo?.flowList?.filter?.(flow => flow.enableFlg == eEnableFlg.enable)?.find(flow => flow.processdefNodeId == id),\r\n      getItemList: () => {\r\n        const [flowList] = getFlowList(projectInfo?.flowList || [], selectionList);\r\n        return flowList;\r\n      },\r\n      getDefaultKey: (list, defaultFlow) => defaultFlow?.key,\r\n      getRevertKey: (item) => item?.processdefNodeId,\r\n      objType: isOA ? eNodeTypeId.nt_671_oa_processList : eNodeTypeId.nt_571_insp_processList, \r\n      paramKey: 'processdefNodeId'\r\n    }\r\n  };\r\n  \r\n  // 当前模式配置\r\n  const currentMode = useMemo(()=>{\r\n    return isCust ? modeConfig.custom : modeConfig.normal\r\n  },[isCust, modeConfig]);\r\n\r\n  const processdefNodeId = Form.useWatch(currentMode.fieldName, form);\r\n  const selecedtProcess = useMemo(() => \r\n    currentMode.getSelectedItem(processdefNodeId)\r\n  ,[projectInfo, customFormData, processdefNodeId, currentMode]) \r\n  \r\n  //（2）获取自定义表单字段\r\n  const { subclassAttrList = [], isLoading: isLoadingGetSubclassAttrs, dataUpdatedAt: dataUpdatedAtSetting409 } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeItem?.nodeId, selecedtProcess?.subclassId, !!selecedtProcess?.subclassId);\r\n\r\n  // (2.1) 编辑时获取采集口径详情 TODO: useQuery cacheTime 需要在相关key变化时才会清除缓存数据，所以需要一个变量key去触发再次打开的更新操作\r\n  const { attrList, criteriaList=[], bizNodeId, objType, createFlg, dataUpdatedAt: dataUpdatedAtTrack019, refetch: refetchGetPartitionDetail } = useQueryTrack019GetPartitionDetail(teamId, nodeItem?.nodeId, modalKey, opType == eEditingMode.Modifying_1 && !!nodeItem?.nodeId);\r\n  //（3）字典数据\r\n  const { data: selectionList } = useQuerySetting407_getCodeValueList(teamId);\r\n  //（4）获取人员列表\r\n  const { data: userList } = useQuerySetting202_getTeamAllUsers(teamId);\r\n\r\n  // 选择样式\r\n  const selectStyle = { width: 300, borderRadius: 5 };\r\n\r\n  const isLoading  = isLoadingInsp091 || isLoadingGetSubclassAttrs || (!!isCust && isLoadingCust008 ) || uploadLoading\r\n\r\n  // 监听打开新建/编辑采集口径弹窗\r\n  useEffect(() => {\r\n    globalEventBus.on(\"openCreatePartitionDrawerEvent\", openCreatePartitionDrawerEvent)\r\n    return () => globalEventBus.off(\"openCreatePartitionDrawerEvent\", openCreatePartitionDrawerEvent)\r\n  }, [])\r\n\r\n  // 打开创建项目弹窗事件\r\n  const openCreatePartitionDrawerEvent = (target, args) => {\r\n    setModalKey(toolUtil.guid());\r\n    setNodeItem(args);\r\n    setIsModalVisible(true);\r\n  }\r\n\r\n  // 打开弹窗/关闭弹窗\r\n  useEffect(() => {\r\n    if (isModalVisible && opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtSetting409) {\r\n      // 加载数据\r\n      loadIssuePartitionDetail()\r\n    } else {\r\n      // 初始化数据\r\n      initialIssuePartitionData()\r\n    }\r\n  }, [isModalVisible, dataUpdatedAtTrack019, dataUpdatedAtSetting409])\r\n\r\n  // 打开搜索预览弹窗\r\n  const openSearchReultPreviewModal = (requst) => {\r\n    globalEventBus.emit(\"openSearchReultPreviewModalEvent\", \"\",\r\n      { searchPreRequest: requst, onDisplayClick: onDisplayClick.bind(this) });\r\n  }\r\n\r\n  // 自定义表单字段\r\n  useEffect(() => {\r\n    if (!isLoadingGetSubclassAttrs) {\r\n      debugger\r\n      changePropertyTypeList(subclassAttrList)\r\n    }\r\n  }, [isLoadingGetSubclassAttrs])\r\n\r\n  // 编辑 自定义表单\r\n  useEffect(() => {\r\n    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && !isEmpty(attrNodeList)) {\r\n      changeCriteriaList()\r\n    }\r\n  }, [opType, dataUpdatedAtTrack019, JSON.stringify(attrNodeList) ])\r\n\r\n  // 编辑 回显流程 注意有先后依赖关系不可合并\r\n  useEffect(() => {\r\n    if (opType == eEditingMode.Modifying_1 && dataUpdatedAtTrack019 && dataUpdatedAtInsp091) {\r\n      form.setFieldValue(currentMode.fieldName, bizNodeId)\r\n    }\r\n  }, [opType, dataUpdatedAtTrack019, dataUpdatedAtInsp091, JSON.stringify(currentMode)])\r\n\r\n  useEffect(() => {\r\n    if (isModalVisible) {\r\n      const itemList = currentMode.getItemList();\r\n      setFlowList(itemList);\r\n      \r\n      if (opType == eEditingMode.Creating_0) {\r\n        const defaultFlow = isCust ? null : getFlowList(projectInfo?.flowList || [], selectionList)[1];\r\n        const defaultKey = currentMode.getDefaultKey(itemList, defaultFlow);\r\n        if (defaultKey) {\r\n          form.setFieldValue(currentMode.fieldName, defaultKey);\r\n        }\r\n      }\r\n    }\r\n  }, [isModalVisible, JSON.stringify(projectInfo), isCust, JSON.stringify(currentMode)]);\r\n\r\n  // 自定义表单数据处理\r\n  const changePropertyTypeList = (attrgrpProps) => {\r\n    let attrList = transformAttrNodeListForUI(attrgrpProps, userList, selectionList, true);\r\n    const checkedValues = attrList.filter(item => item.checked).map(item => item.nodeId);\r\n    const properTypeListUI = getQueryableAttrNodeList(attrList); //过滤59类型\r\n    setAttrNodeList(properTypeListUI);\r\n    setCheckedValues(checkedValues);\r\n  }\r\n\r\n  // 编辑搜索数据\r\n  const changeCriteriaList = () => {\r\n    let _criteriaList = (criteriaList || []).filter(criteria => attrNodeList.some(subclass => criteria.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17\r\n     _criteriaList = transformCriteriaListForUI(_criteriaList, attrNodeList, selectionList);\r\n    setInitTableData(_criteriaList);\r\n  }\r\n\r\n  // 初始化数据\r\n  function initialIssuePartitionData() {\r\n    // console.log(\"正在清空数据...\")\r\n    setInitTableData([]); //清空高级搜索数据\r\n    setSelectFields([]); //清空字段属性数据\r\n    setShowFormFields(true); //重置表单字段设置显示状态\r\n  }\r\n\r\n  // 加载采集口径详情\r\n  function loadIssuePartitionDetail() {\r\n    const isCreateEnabled = createFlg == eEnableFlg.enable;\r\n    form.setFieldsValue({\r\n      name: nodeItem.name, // name回显\r\n      createFlg: isCreateEnabled\r\n    });\r\n\r\n    // 根据createFlg设置表单字段设置的显示状态\r\n    setShowFormFields(isCreateEnabled);\r\n\r\n    const _attrList = attrList.filter(attr => subclassAttrList.some(subclass => attr.attrNid == subclass.nodeId)); // 已删除的需要排除 2024-05-17\r\n    _attrList.forEach((attr) => {\r\n      const propertyList = subclassAttrList.find(subclass => attr.attrNid == subclass.nodeId).propertyList;\r\n      attr.attrModifyableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\r\n      attr.attrQueryableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\r\n      attr.uiControl = (propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue;\r\n      attr.selectionId = propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)?.propValue || \"\";\r\n    });\r\n    setSelectFields(_attrList)\r\n  }\r\n\r\n  // 默认值配置\r\n  const getDefAttrPropValuepByType = (propList = [], type) => {\r\n    return (propList.find((item) => item.propType == type) || { propValue: \"0\" }).propValue;\r\n  }\r\n\r\n  // 取消\r\n  const handleCancel = () => {\r\n   return Modal.confirm({\r\n      title: \"提示\",\r\n      icon: <ExclamationCircleOutlined />,\r\n      content: <p>{ opType == eOpType.add ? '正在新建采集口径，是否放弃编辑?' : '正在编辑采集口径，是否放弃编辑?'}</p>,\r\n      okText: \"确定\",\r\n      cancelText: \"取消\",\r\n      onOk: () => {\r\n         setIsModalVisible(false)\r\n      },\r\n      onCancel: () => {\r\n        console.log(\"Cancel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  // 选择字段数据处理\r\n  const onSelectFields = (items) => {\r\n    let fieldList = []\r\n    selectFields.filter(el => {\r\n      items.map((_el, index) => {\r\n        if (el.attrNid == _el.nodeId) {\r\n          fieldList.push(el)\r\n          delete items[index]\r\n        }\r\n      })\r\n    })\r\n    items.map((item) => {\r\n      let propertyList = item.propertyList\r\n      const _attrModifyableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);\r\n      const _attrQueryableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);\r\n      let field = {\r\n        attrNid: item.nodeId,\r\n        defaultVal: null,\r\n        attrVisibleFlg: '',\r\n        attrModifyableFlg: _attrModifyableFlg,\r\n        attrModifyableFlgBack: _attrModifyableFlg,\r\n        attrQueryableFlg: _attrQueryableFlg,\r\n        attrQueryableFlgBack: _attrQueryableFlg,\r\n        uiControl: (propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue,\r\n        selectionId: propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)?.propValue || \"\",\r\n        isRegion: propertyList.find(el => el.propType == eConsolePropId.Prop_28_is_region)?.propValue || \"\",      // 是否是检查区域字段\r\n      }\r\n      fieldList.push(field)\r\n    })\r\n    setSelectFields(fieldList)\r\n    setIsAddAttrModalVisible(false)\r\n  }\r\n\r\n  // + 动态条件\r\n  const handleOnAddSearchCode = () => {\r\n    tRef.current.addSearchCode();\r\n  }\r\n\r\n  // 搜索预览 无需校验搜索名称\r\n  const handleSearchPreClick = () => {\r\n    setSearchPreFlg(true);\r\n    form.submit();\r\n  }\r\n\r\n  // 显示字段\r\n  const onDisplayClick = (e) => {\r\n    setSearchDisplayFieldOpen(true);\r\n  };\r\n\r\n  // 显示字段数据处理\r\n  const assembleQueryAttrList = () => {\r\n    return checkedValues.map(checkedValue => ({ attrNid: checkedValue }));\r\n  }\r\n\r\n  // 保存显示字段\r\n  const handleSearchDisplayFieldOnOk = (e, values) => {\r\n    setSearchDisplayFieldOpen(false);\r\n    setCheckedValues(values);\r\n    globalEventBus.emit(\"changeSearchReultPreviewEvent\", \"\", { values }) // 根据显示字段过滤\r\n  }\r\n\r\n  // 取消保存字段\r\n  const handleSearchDisplayFieldOnCancel = (e, values) => {\r\n    if (!compareArr(checkedValues, values)) {\r\n      Modal.confirm({\r\n        title: '提醒',\r\n        icon: <ExclamationCircleOutlined />,\r\n        content: \"放弃后将不会保存显示字段，确定要放弃？\",\r\n        okText: '确定',\r\n        cancelText: '取消',\r\n        onOk: () => { setSearchDisplayFieldOpen(false); }\r\n      });\r\n    } else {\r\n      setSearchDisplayFieldOpen(false);\r\n    }\r\n  };\r\n\r\n  // 点击 确定\r\n  const handleSaveClick = () => {\r\n    setSearchPreFlg(false);\r\n    form.submit();\r\n  }\r\n\r\n  // 表单提交 form.submit()\r\n  const onFinish = async (values) => {\r\n    const { name } = values;\r\n    if (!searchPreFlg && !name) {\r\n      // 校验名称\r\n      return globalUtil.warning(\"请填写采集口径名称!\")\r\n    }\r\n    let criteriaList = tRef?.current?.getCriteriaListForBackend?.() || []\r\n    // 搜索预览\r\n    if (searchPreFlg) {\r\n      const queryAttrList = assembleQueryAttrList();\r\n      let requst = {\r\n        \"teamId\": teamId, \"bizNodeId\": values[currentMode.fieldName], \"name\": name, \"objType\": currentMode.objType,\r\n        \"advanceQueryFlg\": \"1\", \"criteriaList\": criteriaList, \"queryAttrList\": queryAttrList\r\n      }\r\n      // 搜索预览\r\n      requst = { ...requst, pageNum: 1 } //默认查询第一页\r\n      return openSearchReultPreviewModal(requst);\r\n    }\r\n    setUploadLoading(true);\r\n    // TODO: 检查项后端校验存在问题,先固定传null by walt from jack  2024-02-05\r\n    selectFields.forEach((el, index) => {\r\n      if (el.isRegion == eRegionType.check) {\r\n        el.defaultVal = null;\r\n      }\r\n    });\r\n    //  后端只需要这些参数，去除多余的不接受的参数\r\n    let _selectFields = selectFields.map((el, index) => ({\r\n      attrNid: el.attrNid,\r\n      defaultVal: el.defaultVal,\r\n      attrVisibleFlg: el.attrVisibleFlg,\r\n      attrModifyableFlg: el.attrModifyableFlg,\r\n      attrModifyableFlgBack: el.attrModifyableFlgBack,\r\n      attrQueryableFlg: el.attrQueryableFlg,\r\n      attrQueryableFlgBack: el.attrQueryableFlgBack,\r\n      seqNo: index + 1,\r\n    }))\r\n    \r\n    // 基础参数\r\n    let params = { teamId, name, attrList: _selectFields, criteriaList, createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable };\r\n    \r\n    // 根据操作类型添加不同的参数\r\n    if (opType) {\r\n      // 编辑模式\r\n      params.nodeId = nodeItem.nodeId;\r\n      params[currentMode.paramKey] = values[currentMode.fieldName];\r\n      await editPartition(params);\r\n    } else {\r\n      // 新建模式\r\n      params.objNodeId = nodeItem.nodeId;\r\n      params[currentMode.paramKey] = values[currentMode.fieldName];\r\n      await createPartition(params);\r\n    }\r\n    \r\n    setUploadLoading(false);\r\n  };\r\n\r\n  // 新建采集口径\r\n  async function createPartition(params) {\r\n    await http.track_018_create_issue_partition(params).then(result => {\r\n      if (result.resultCode == 200) {\r\n        refreshSelectTeamMenu({ treeNode: result?.nodeTree[0] || {}})\r\n        setIsModalVisible(false);\r\n        nodeItem.callback && nodeItem.callback(result?.nodeTree[0]?.children[0] || {});\r\n      }\r\n    }).catch(err => {\r\n      console.log(err)\r\n    })\r\n  }\r\n\r\n  // 编辑采集口径\r\n  async function editPartition(params) {\r\n    await http.track_020_modify_issue_partition(params).then(result => {\r\n      if (result.resultCode == 200) {\r\n        refreshTeamMenu();\r\n        setIsModalVisible(false);\r\n        refetchGetPartitionDetail(); // 编辑采集口径后刷新数据\r\n      }\r\n    }).catch(err => {\r\n      console.log(err)\r\n    })\r\n  }\r\n\r\n  const handleOnChange = (value) => {\r\n    let criteriaList = tRef?.current?.getCriteriaListForBackend && tRef?.current?.getCriteriaListForBackend?.()\r\n    if (isEmpty(criteriaList) && isEmpty(selectFields)) {\r\n      return\r\n    }\r\n    return Modal.confirm({\r\n      title: \"提示\",\r\n      icon: <ExclamationCircleOutlined />,\r\n      content: <p>{`重新选择${isCust ? '表单' : '流程'}，下方两个表格将清空，您需要重新设置。是否继续?`}</p>,\r\n      okText: \"是\",\r\n      cancelText: \"否\",\r\n      zIndex: 1002, // FIXME:不能超过10000，否则会导致Select下拉框被覆盖\r\n      onOk: () => {\r\n      },\r\n      onCancel: () => {\r\n        console.log(\"Cancel\");\r\n        form.setFieldValue(currentMode.fieldName, currentMode.getRevertKey(selecedtProcess))\r\n      },\r\n    });\r\n  }\r\n\r\n  // 处理\"可新建\"复选框变化\r\n  const handleCreateFlgChange = (e) => {\r\n    const checked = e.target.checked;\r\n\r\n    // 如果取消勾选且表单字段设置有值，弹出确认对话框\r\n    if (!checked && !isEmpty(selectFields)) {\r\n      Modal.confirm({\r\n        title: \"提示\",\r\n        icon: <ExclamationCircleOutlined />,\r\n        content: \"取消可新建功能，则清除表单字段设置，是否取消？\",\r\n        okText: \"取消\",\r\n        cancelText: \"不取消\",\r\n        zIndex: 1002,\r\n        onOk: () => {\r\n          // 用户确认取消，清除表单字段设置并隐藏\r\n          setSelectFields([]);\r\n          setShowFormFields(false);\r\n          form.setFieldValue('createFlg', false);\r\n        },\r\n        onCancel: () => {\r\n          // 用户选择不取消，恢复勾选状态\r\n          form.setFieldValue('createFlg', true);\r\n        },\r\n      });\r\n    } else if (checked) {\r\n      // 如果重新勾选，显示表单字段设置\r\n      setShowFormFields(true);\r\n    }\r\n  }\r\n\r\n  return <DraggableDrawer\r\n    className=\"tms-drawer IssuePartition\"\r\n    width={\"60%\"}\r\n    title={opType ? `采集口径设置` : `新建采集口径`}\r\n    destroyOnClose={true}\r\n    open={isModalVisible}\r\n    onClose={handleCancel}\r\n    footer={<div style={{ textAlign: \"right\" }} >\r\n      <Space size={20}>\r\n        <Button style={{ borderRadius: 5 }} onClick={handleCancel}>取消</Button>\r\n        <Button type=\"primary\" style={{ borderRadius: 5 }} onClick={handleSaveClick}>提交</Button>\r\n      </Space>\r\n    </div>}\r\n  >\r\n    <UploadLoading spinning={isLoading}>\r\n      <Form\r\n        form={form}\r\n        labelCol={{ span: 4 }}\r\n        wrapperCol={{ span: 19 }}\r\n        onFinish={onFinish}\r\n        preserve={false}// Modal关闭后销毁form字段数据\r\n        autoComplete={\"off\"} // 取消自动补充功能\r\n        initialValues={{\r\n          createFlg: true,\r\n        }}\r\n      >\r\n        <Form.Item label={\"采集口径名称\"} name=\"name\" required={true}>\r\n          <Input style={selectStyle} autoComplete=\"off\" />\r\n        </Form.Item>\r\n        <Form.Item label={currentMode.label}>\r\n          <Space size={20}>\r\n            <Form.Item name={currentMode.fieldName} required={true} noStyle>\r\n              <Select\r\n                showSearch\r\n                style={{ width: 300, borderRadius: 3 }}\r\n                //select选择框搜索\r\n                filterOption={(input, option) => {\r\n                  return (option.children.props.children[1].props.children || \"\").toLowerCase().indexOf(input.toLowerCase()) >= 0\r\n                }}\r\n                onChange={handleOnChange}\r\n              >\r\n                {\r\n                  flowList.map(flow => (<Select.Option key={flow.key} value={flow.value} >\r\n                    <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                      {flow.icon}\r\n                      <span style={{ paddingLeft: 5 }}>{flow.label}</span>\r\n                    </div>\r\n                  </Select.Option>))\r\n                }\r\n              </Select>\r\n            </Form.Item>\r\n            <Form.Item name=\"createFlg\" valuePropName=\"checked\" noStyle>\r\n              <Checkbox onChange={handleCreateFlgChange}>可新建</Checkbox>\r\n            </Form.Item>\r\n          </Space>\r\n        </Form.Item>\r\n        <Form.Item label={\"自定义搜索\"}>\r\n          {isEmpty(attrNodeList) ? <TEmpty/> :\r\n            <div>\r\n              <SearchEditTable attrNodeList={attrNodeList} exprs={[]} criteriaList={initTableData} selectionList={selectionList} queryType={0} ref={tRef} />\r\n              <div className=\"search-edit-btns\">\r\n                <Button\r\n                  type=\"link\"\r\n                  icon={\r\n                    <QuestionCircleOutlined className=\"color-yellow\" />\r\n                  }\r\n                  onClick={handleOnAddSearchCode}\r\n                >\r\n                  <span>+ 动态条件（<FunctionOutlined style={{ margin: 0 }} />）</span>\r\n                </Button>\r\n                <Button icon={<SearchOutlined />} className=\"defaultBtn_light\" onClick={handleSearchPreClick} >结果预览</Button>\r\n              </div>\r\n              <div className=\"remarks\">备注1：此处的自定义搜索，用于显示当前采集口径对应的{projectInfo.issueAlias ?? \"\"}列表。</div>\r\n              <div className=\"remarks\">备注2：点击确定按钮仅保存表单(即条件)，预览结果不做保存。</div>\r\n            </div>\r\n          }\r\n        </Form.Item>\r\n        {showFormFields && (\r\n          <Form.Item label={\"表单字段设置\"}>\r\n            <CustomerFormTable selectFields={selectFields} setSelectFields={setSelectFields} selectionList={selectionList} userList={userList} subclassAttrList={subclassAttrList} selecedtProcess={selecedtProcess} />\r\n            <a className=\"fontsize-12\" onClick={() => setIsAddAttrModalVisible(true)}>+ 添加字段</a>\r\n            <div className=\"remarks\">备注1：在{projectInfo.issueAlias ?? \"\"}项目\"{projectInfo.name}\"的自定义字段的基础上，进一步限缩上面表格中字段的是否显示/可修改/可搜索。</div>\r\n            <div className=\"remarks\">备注2：在当前采集口径中提交的新建{projectInfo.issueAlias ?? \"\"}，其对应上面表格中的字段，如果值为空，则使用\"缺省值\"列中的值，进行最后的数据存储。</div>\r\n          </Form.Item>\r\n        )}\r\n      </Form>\r\n    </UploadLoading>\r\n    {/* 添加字段 */}\r\n    <AddPartitionAttr selectFields={selectFields} attrList={subclassAttrList} visible={isAddAttrModalVisible} onSelectFields={onSelectFields} onCancel={() => setIsAddAttrModalVisible(false)} />\r\n    {/* 搜索结果预览 */}\r\n    <SearchReultPreviewModal />\r\n    {/* 显示字段 */}\r\n    <SearchDisplayField\r\n      open={searchDisplayFieldOpen}\r\n      onOk={handleSearchDisplayFieldOnOk}\r\n      onCancel={handleSearchDisplayFieldOnCancel}\r\n      nodeName={nodeItem?.nodeName}\r\n      checkedValues={checkedValues}\r\n      attrNodeList={attrNodeList}\r\n    />\r\n  </DraggableDrawer>\r\n}"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,cAAc,QAAQ,mBAAmB;AACvH,OAAO,KAAKC,IAAI,MAAM,kBAAkB;AACxC,SAASC,kCAAkC,EAAEC,mCAAmC,QAAQ,6BAA6B;AACrH,SAASC,UAAU,EAAEC,OAAO,QAAQ,0BAA0B;AAC9D,SAASC,cAAc,EAAEC,YAAY,EAAEC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,QAAS,oBAAoB;AACzG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,wBAAwB,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,WAAW,QAAQ,0BAA0B;AACxI,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,SAASC,WAAW,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,+BAA+B,QAAQ,yBAAyB;AAC9H,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,MAAM;AAClF,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAC5D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,kBAAkB,MAAM,6DAA6D;AAC5F,OAAOC,eAAe,MAAM,0DAA0D;AACtF,OAAOC,uBAAuB,MAAM,kEAAkE;AACtG,SAASC,kCAAkC,EAAEC,sCAAsC,QAAQ,mCAAmC;AAC9H,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAO,8BAA8B;AACrC,SAAQC,sCAAsC,QAAO,8CAA8C;AACnG,SAASC,yBAAyB,EAAEC,8BAA8B,EAAEC,6BAA6B,QAAQ,kCAAkC;AAC3I,OAAOC,eAAe,MAAM,6BAA6B;;AAGzD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,eAAe,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC9C,MAAM;IAAEC;EAAO,CAAC,GAAGvB,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACwB,IAAI,CAAC,GAAGlC,IAAI,CAACmC,OAAO,CAAC,CAAC;EAC7B,MAAMC,IAAI,GAAG7B,MAAM,CAAC8B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACxC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzD,MAAM,CAACmC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7E,MAAM,CAACqC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3E;EACA,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAChB,QAAQ,CAACmE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAiB;EAC9D,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE5D,MAAM0D,MAAM,GAAGzD,OAAO,CAAC,MAAI;IACzB,OAAOb,+BAA+B,CAACuE,QAAQ,CAACpB,QAAQ,CAACqB,QAAQ,CAAC,GAAGtF,YAAY,CAACuF,WAAW,GAAGvF,YAAY,CAACwF,UAAU,EAAE;EAC3H,CAAC,EAAC,CAACvB,QAAQ,CAAC,CAAC;EAEb,MAAM;IAAEwB,IAAI,EAAE;MAAEC,MAAM;MAAEC;IAAK,CAAC;IAAEC,SAAS,EAAEC;EAAiB,CAAC,GAAGrD,yBAAyB,CAAC;IAACW,MAAM;IAAE2C,MAAM,EAAE7B,QAAQ,CAAC6B,MAAM;IAAEC,OAAO,EAAE,CAAC,CAAC9B,QAAQ,CAAC6B;EAAO,CAAC,CAAC;;EAEzJ;EACA;EACA,MAAM;IAAEL,IAAI,EAAEO,WAAW,GAAG,CAAC,CAAC;IAAEJ,SAAS,EAAEK,gBAAgB;IAAEC,aAAa,EAAEC;EAAqB,CAAC,GAAGpF,QAAQ,CAACwB,sCAAsC,CAACY,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6B,MAAM,EAAE,CAAC,EAAC7B,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE6B,MAAM,EAAC,CAAC;;EAEnM;EACA,MAAM;IAAEL,IAAI,EAAEW,cAAc,GAAG,CAAC,CAAC;IAAER,SAAS,EAAES;EAAiB,CAAC,GAAG5D,8BAA8B,CAAC;IAACU,MAAM;IAAE2C,MAAM,EAAE7B,QAAQ,CAAC6B,MAAM;IAAEC,OAAO,EAAE,CAAC,CAACL,MAAM,IAAI,CAAC,CAACzB,QAAQ,CAAC6B;EAAM,CAAC,CAAC;;EAE5K;EACA,MAAMQ,UAAU,GAAG;IACjBC,MAAM,EAAE;MACNC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbC,eAAe,EAAGC,EAAE;QAAA,IAAAC,qBAAA;QAAA,OAAKR,cAAc,aAAdA,cAAc,wBAAAQ,qBAAA,GAAdR,cAAc,CAAES,QAAQ,cAAAD,qBAAA,uBAAxBA,qBAAA,CAA0BE,IAAI,CAAC1D,IAAI,IAAIA,IAAI,CAAC0C,MAAM,IAAIa,EAAE,CAAC;MAAA;MAClFI,WAAW,EAAEA,CAAA;QAAA,IAAAC,sBAAA;QAAA,OAAM,CAAAZ,cAAc,aAAdA,cAAc,wBAAAY,sBAAA,GAAdZ,cAAc,CAAES,QAAQ,cAAAG,sBAAA,uBAAxBA,sBAAA,CAA0BC,GAAG,CAAC7D,IAAI,KAAK;UACxD8D,GAAG,EAAE9D,IAAI,CAAC0C,MAAM;UAChBqB,KAAK,EAAE/D,IAAI,CAAC0C,MAAM;UAClBW,KAAK,EAAErD,IAAI,CAACgE;UACZ;QACF,CAAC,CAAC,CAAC,KAAI,EAAE;MAAA;MACTC,aAAa,EAAGC,IAAI,IAAKA,IAAI,CAACC,MAAM,GAAG,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,GAAGM,SAAS;MAClEC,YAAY,EAAGC,IAAI,IAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE5B,MAAM;MACpC6B,OAAO,EAAEhH,WAAW,CAACiH,mBAAmB;MACxCC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;MACNtB,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAE,MAAM;MACbC,eAAe,EAAGC,EAAE;QAAA,IAAAoB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAAA,OAAKjC,WAAW,aAAXA,WAAW,wBAAA+B,qBAAA,GAAX/B,WAAW,CAAElB,QAAQ,cAAAiD,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBG,MAAM,cAAAF,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAAAG,IAAA,CAAAJ,qBAAA,EAAgCK,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAIpI,UAAU,CAACqI,MAAM,CAAC,cAAAL,sBAAA,uBAA5EA,sBAAA,CAA8EnB,IAAI,CAACsB,IAAI,IAAIA,IAAI,CAACG,gBAAgB,IAAI5B,EAAE,CAAC;MAAA;MAChJI,WAAW,EAAEA,CAAA,KAAM;QACjB,MAAM,CAACjC,QAAQ,CAAC,GAAGrE,WAAW,CAAC,CAAAuF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAElB,QAAQ,KAAI,EAAE,EAAE0D,aAAa,CAAC;QAC1E,OAAO1D,QAAQ;MACjB,CAAC;MACDuC,aAAa,EAAEA,CAACC,IAAI,EAAEmB,WAAW,KAAKA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEvB,GAAG;MACtDO,YAAY,EAAGC,IAAI,IAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,gBAAgB;MAC9CZ,OAAO,EAAEhC,IAAI,GAAGhF,WAAW,CAAC+H,qBAAqB,GAAG/H,WAAW,CAACgI,uBAAuB;MACvFd,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMe,WAAW,GAAGjH,OAAO,CAAC,MAAI;IAC9B,OAAO+D,MAAM,GAAGY,UAAU,CAACC,MAAM,GAAGD,UAAU,CAACwB,MAAM;EACvD,CAAC,EAAC,CAACpC,MAAM,EAAEY,UAAU,CAAC,CAAC;EAEvB,MAAMiC,gBAAgB,GAAGrH,IAAI,CAAC2H,QAAQ,CAACD,WAAW,CAACpC,SAAS,EAAEpD,IAAI,CAAC;EACnE,MAAM0F,eAAe,GAAGnH,OAAO,CAAC,MAC9BiH,WAAW,CAAClC,eAAe,CAAC6B,gBAAgB,CAAC,EAC9C,CAACvC,WAAW,EAAEI,cAAc,EAAEmC,gBAAgB,EAAEK,WAAW,CAAC,CAAC;;EAE9D;EACA,MAAM;IAAEG,gBAAgB,GAAG,EAAE;IAAEnD,SAAS,EAAEoD,yBAAyB;IAAE9C,aAAa,EAAE+C;EAAwB,CAAC,GAAGhH,sCAAsC,CAACkB,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6B,MAAM,EAAEgD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,UAAU,EAAE,CAAC,EAACJ,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEI,UAAU,EAAC;;EAE5O;EACA,MAAM;IAAEC,QAAQ;IAAEC,YAAY,GAAC,EAAE;IAAEC,SAAS;IAAE1B,OAAO;IAAE2B,SAAS;IAAEpD,aAAa,EAAEqD,qBAAqB;IAAEC,OAAO,EAAEC;EAA0B,CAAC,GAAGzH,kCAAkC,CAACmB,MAAM,EAAEc,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6B,MAAM,EAAEnB,QAAQ,EAAES,MAAM,IAAIpF,YAAY,CAACuF,WAAW,IAAI,CAAC,EAACtB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE6B,MAAM,EAAC;EAC/Q;EACA,MAAM;IAAEL,IAAI,EAAE+C;EAAc,CAAC,GAAG5I,mCAAmC,CAACuD,MAAM,CAAC;EAC3E;EACA,MAAM;IAAEsC,IAAI,EAAEiE;EAAS,CAAC,GAAG/J,kCAAkC,CAACwD,MAAM,CAAC;;EAErE;EACA,MAAMwG,WAAW,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAE,CAAC;EAEnD,MAAMjE,SAAS,GAAIK,gBAAgB,IAAI+C,yBAAyB,IAAK,CAAC,CAACtD,MAAM,IAAIW,gBAAkB,IAAIrB,aAAa;;EAEpH;EACAxD,SAAS,CAAC,MAAM;IACdpB,cAAc,CAAC0J,EAAE,CAAC,gCAAgC,EAAEC,8BAA8B,CAAC;IACnF,OAAO,MAAM3J,cAAc,CAAC4J,GAAG,CAAC,gCAAgC,EAAED,8BAA8B,CAAC;EACnG,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,8BAA8B,GAAGA,CAACE,MAAM,EAAEC,IAAI,KAAK;IACvDtF,WAAW,CAAClE,QAAQ,CAACmE,IAAI,CAAC,CAAC,CAAC;IAC5BX,WAAW,CAACgG,IAAI,CAAC;IACjBxG,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,IAAIiC,cAAc,IAAI2B,MAAM,IAAIpF,YAAY,CAACuF,WAAW,IAAIgE,qBAAqB,IAAIN,uBAAuB,EAAE;MAC5G;MACAkB,wBAAwB,CAAC,CAAC;IAC5B,CAAC,MAAM;MACL;MACAC,yBAAyB,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC3G,cAAc,EAAE8F,qBAAqB,EAAEN,uBAAuB,CAAC,CAAC;;EAEpE;EACA,MAAMoB,2BAA2B,GAAIC,MAAM,IAAK;IAC9ClK,cAAc,CAACmK,IAAI,CAAC,kCAAkC,EAAE,EAAE,EACxD;MAAEC,gBAAgB,EAAEF,MAAM;MAAEG,cAAc,EAAEA,cAAc,CAACC,IAAI,CAAC,IAAI;IAAE,CAAC,CAAC;EAC5E,CAAC;;EAED;EACAlJ,SAAS,CAAC,MAAM;IACd,IAAI,CAACwH,yBAAyB,EAAE;MAC9B;MACA2B,sBAAsB,CAAC5B,gBAAgB,CAAC;IAC1C;EACF,CAAC,EAAE,CAACC,yBAAyB,CAAC,CAAC;;EAE/B;EACAxH,SAAS,CAAC,MAAM;IACd,IAAI4D,MAAM,IAAIpF,YAAY,CAACuF,WAAW,IAAIgE,qBAAqB,IAAI,CAACzJ,OAAO,CAACyE,YAAY,CAAC,EAAE;MACzFqG,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACxF,MAAM,EAAEmE,qBAAqB,EAAEsB,IAAI,CAACC,SAAS,CAACvG,YAAY,CAAC,CAAE,CAAC;;EAElE;EACA/C,SAAS,CAAC,MAAM;IACd,IAAI4D,MAAM,IAAIpF,YAAY,CAACuF,WAAW,IAAIgE,qBAAqB,IAAIpD,oBAAoB,EAAE;MACvF/C,IAAI,CAAC2H,aAAa,CAACnC,WAAW,CAACpC,SAAS,EAAE6C,SAAS,CAAC;IACtD;EACF,CAAC,EAAE,CAACjE,MAAM,EAAEmE,qBAAqB,EAAEpD,oBAAoB,EAAE0E,IAAI,CAACC,SAAS,CAAClC,WAAW,CAAC,CAAC,CAAC;EAEtFpH,SAAS,CAAC,MAAM;IACd,IAAIiC,cAAc,EAAE;MAClB,MAAMuH,QAAQ,GAAGpC,WAAW,CAAC7B,WAAW,CAAC,CAAC;MAC1ChC,WAAW,CAACiG,QAAQ,CAAC;MAErB,IAAI5F,MAAM,IAAIpF,YAAY,CAACwF,UAAU,EAAE;QACrC,MAAMiD,WAAW,GAAG/C,MAAM,GAAG,IAAI,GAAGjF,WAAW,CAAC,CAAAuF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAElB,QAAQ,KAAI,EAAE,EAAE0D,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9F,MAAMyC,UAAU,GAAGrC,WAAW,CAACvB,aAAa,CAAC2D,QAAQ,EAAEvC,WAAW,CAAC;QACnE,IAAIwC,UAAU,EAAE;UACd7H,IAAI,CAAC2H,aAAa,CAACnC,WAAW,CAACpC,SAAS,EAAEyE,UAAU,CAAC;QACvD;MACF;IACF;EACF,CAAC,EAAE,CAACxH,cAAc,EAAEoH,IAAI,CAACC,SAAS,CAAC9E,WAAW,CAAC,EAAEN,MAAM,EAAEmF,IAAI,CAACC,SAAS,CAAClC,WAAW,CAAC,CAAC,CAAC;;EAEtF;EACA,MAAM+B,sBAAsB,GAAIO,YAAY,IAAK;IAC/C,IAAI/B,QAAQ,GAAG5I,0BAA0B,CAAC2K,YAAY,EAAExB,QAAQ,EAAElB,aAAa,EAAE,IAAI,CAAC;IACtF,MAAM/D,aAAa,GAAG0E,QAAQ,CAACjB,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACyD,OAAO,CAAC,CAAClE,GAAG,CAACS,IAAI,IAAIA,IAAI,CAAC5B,MAAM,CAAC;IACpF,MAAMsF,gBAAgB,GAAG9K,wBAAwB,CAAC6I,QAAQ,CAAC,CAAC,CAAC;IAC7D3E,eAAe,CAAC4G,gBAAgB,CAAC;IACjC1G,gBAAgB,CAACD,aAAa,CAAC;EACjC,CAAC;;EAED;EACA,MAAMmG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIS,aAAa,GAAG,CAACjC,YAAY,IAAI,EAAE,EAAElB,MAAM,CAACoD,QAAQ,IAAI/G,YAAY,CAACgH,IAAI,CAACC,QAAQ,IAAIF,QAAQ,CAACG,OAAO,IAAID,QAAQ,CAAC1F,MAAM,CAAC,CAAC,CAAC,CAAC;IAChIuF,aAAa,GAAG7K,0BAA0B,CAAC6K,aAAa,EAAE9G,YAAY,EAAEiE,aAAa,CAAC;IACvFlE,gBAAgB,CAAC+G,aAAa,CAAC;EACjC,CAAC;;EAED;EACA,SAASjB,yBAAyBA,CAAA,EAAG;IACnC;IACA9F,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBF,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IACrBe,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC3B;;EAEA;EACA,SAASgF,wBAAwBA,CAAA,EAAG;IAClC,MAAMuB,eAAe,GAAGpC,SAAS,IAAIrJ,UAAU,CAACqI,MAAM;IACtDlF,IAAI,CAACuI,cAAc,CAAC;MAClBC,IAAI,EAAE3H,QAAQ,CAAC2H,IAAI;MAAE;MACrBtC,SAAS,EAAEoC;IACb,CAAC,CAAC;;IAEF;IACAvG,iBAAiB,CAACuG,eAAe,CAAC;IAElC,MAAMG,SAAS,GAAG1C,QAAQ,CAACjB,MAAM,CAAC4D,IAAI,IAAI/C,gBAAgB,CAACwC,IAAI,CAACC,QAAQ,IAAIM,IAAI,CAACL,OAAO,IAAID,QAAQ,CAAC1F,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/G+F,SAAS,CAACE,OAAO,CAAED,IAAI,IAAK;MAAA,IAAAE,kBAAA,EAAAC,mBAAA;MAC1B,MAAMC,YAAY,GAAGnD,gBAAgB,CAACjC,IAAI,CAAC0E,QAAQ,IAAIM,IAAI,CAACL,OAAO,IAAID,QAAQ,CAAC1F,MAAM,CAAC,CAACoG,YAAY;MACpGJ,IAAI,CAACK,qBAAqB,GAAGC,0BAA0B,CAACF,YAAY,EAAEnM,cAAc,CAACsM,kBAAkB,CAAC;MACxGP,IAAI,CAACQ,oBAAoB,GAAGF,0BAA0B,CAACF,YAAY,EAAEnM,cAAc,CAACwM,iBAAiB,CAAC;MACtGT,IAAI,CAACU,SAAS,IAAAR,kBAAA,GAAIE,YAAY,CAACpF,IAAI,CAAC2F,EAAE,IAAIA,EAAE,CAACC,QAAQ,IAAI3M,cAAc,CAAC4M,kBAAkB,CAAC,cAAAX,kBAAA,uBAA1EA,kBAAA,CAA6EY,SAAS;MACvGd,IAAI,CAACe,WAAW,GAAG,EAAAZ,mBAAA,GAAAC,YAAY,CAACpF,IAAI,CAACgG,GAAG,IAAIA,GAAG,CAACJ,QAAQ,IAAI3M,cAAc,CAACgN,iBAAiB,CAAC,cAAAd,mBAAA,uBAA1EA,mBAAA,CAA4EW,SAAS,KAAI,EAAE;IAChH,CAAC,CAAC;IACFxI,eAAe,CAACyH,SAAS,CAAC;EAC5B;;EAEA;EACA,MAAMO,0BAA0B,GAAGA,CAACY,QAAQ,GAAG,EAAE,EAAEC,IAAI,KAAK;IAC1D,OAAO,CAACD,QAAQ,CAAClG,IAAI,CAAEY,IAAI,IAAKA,IAAI,CAACgF,QAAQ,IAAIO,IAAI,CAAC,IAAI;MAAEL,SAAS,EAAE;IAAI,CAAC,EAAEA,SAAS;EACzF,CAAC;;EAED;EACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;IAC1B,OAAO9L,KAAK,CAAC+L,OAAO,CAAC;MAClBC,KAAK,EAAE,IAAI;MACXC,IAAI,eAAExK,OAAA,CAACvD,yBAAyB;QAAAgO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,eAAE7K,OAAA;QAAA8K,QAAA,EAAKvI,MAAM,IAAIlF,OAAO,CAAC0N,GAAG,GAAG,kBAAkB,GAAG;MAAkB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;MAClFI,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEA,CAAA,KAAM;QACTrK,iBAAiB,CAAC,KAAK,CAAC;MAC3B,CAAC;MACDsK,QAAQ,EAAEA,CAAA,KAAM;QACdC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIC,SAAS,GAAG,EAAE;IAClBlK,YAAY,CAAC+D,MAAM,CAACuE,EAAE,IAAI;MACxB2B,KAAK,CAACnH,GAAG,CAAC,CAAC6F,GAAG,EAAEwB,KAAK,KAAK;QACxB,IAAI7B,EAAE,CAAChB,OAAO,IAAIqB,GAAG,CAAChH,MAAM,EAAE;UAC5BuI,SAAS,CAACE,IAAI,CAAC9B,EAAE,CAAC;UAClB,OAAO2B,KAAK,CAACE,KAAK,CAAC;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFF,KAAK,CAACnH,GAAG,CAAES,IAAI,IAAK;MAAA,IAAA8G,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;MAClB,IAAIxC,YAAY,GAAGxE,IAAI,CAACwE,YAAY;MACpC,MAAMyC,kBAAkB,GAAGvC,0BAA0B,CAACF,YAAY,EAAEnM,cAAc,CAACsM,kBAAkB,CAAC;MACtG,MAAMuC,iBAAiB,GAAGxC,0BAA0B,CAACF,YAAY,EAAEnM,cAAc,CAACwM,iBAAiB,CAAC;MACpG,IAAIsC,KAAK,GAAG;QACVpD,OAAO,EAAE/D,IAAI,CAAC5B,MAAM;QACpBgJ,UAAU,EAAE,IAAI;QAChBC,cAAc,EAAE,EAAE;QAClBC,iBAAiB,EAAEL,kBAAkB;QACrCxC,qBAAqB,EAAEwC,kBAAkB;QACzCM,gBAAgB,EAAEL,iBAAiB;QACnCtC,oBAAoB,EAAEsC,iBAAiB;QACvCpC,SAAS,GAAAgC,mBAAA,GAAGtC,YAAY,CAACpF,IAAI,CAAC2F,EAAE,IAAIA,EAAE,CAACC,QAAQ,IAAI3M,cAAc,CAAC4M,kBAAkB,CAAC,cAAA6B,mBAAA,uBAA1EA,mBAAA,CAA6E5B,SAAS;QACjGC,WAAW,EAAE,EAAA4B,mBAAA,GAAAvC,YAAY,CAACpF,IAAI,CAACgG,GAAG,IAAIA,GAAG,CAACJ,QAAQ,IAAI3M,cAAc,CAACgN,iBAAiB,CAAC,cAAA0B,mBAAA,uBAA1EA,mBAAA,CAA4E7B,SAAS,KAAI,EAAE;QACxGsC,QAAQ,EAAE,EAAAR,mBAAA,GAAAxC,YAAY,CAACpF,IAAI,CAAC2F,EAAE,IAAIA,EAAE,CAACC,QAAQ,IAAI3M,cAAc,CAACoP,iBAAiB,CAAC,cAAAT,mBAAA,uBAAxEA,mBAAA,CAA0E9B,SAAS,KAAI,EAAE,CAAO;MAC5G,CAAC;MACDyB,SAAS,CAACE,IAAI,CAACM,KAAK,CAAC;IACvB,CAAC,CAAC;IACFzK,eAAe,CAACiK,SAAS,CAAC;IAC1BrK,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAMoL,qBAAqB,GAAGA,CAAA,KAAM;IAClC9L,IAAI,CAAC+L,OAAO,CAACC,aAAa,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC3L,eAAe,CAAC,IAAI,CAAC;IACrBR,IAAI,CAACoM,MAAM,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAM/E,cAAc,GAAIgF,CAAC,IAAK;IAC5B3L,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAM4L,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAOjL,aAAa,CAACwC,GAAG,CAAC0I,YAAY,KAAK;MAAElE,OAAO,EAAEkE;IAAa,CAAC,CAAC,CAAC;EACvE,CAAC;;EAED;EACA,MAAMC,4BAA4B,GAAGA,CAACH,CAAC,EAAEI,MAAM,KAAK;IAClD/L,yBAAyB,CAAC,KAAK,CAAC;IAChCY,gBAAgB,CAACmL,MAAM,CAAC;IACxBzP,cAAc,CAACmK,IAAI,CAAC,+BAA+B,EAAE,EAAE,EAAE;MAAEsF;IAAO,CAAC,CAAC,EAAC;EACvE,CAAC;;EAED;EACA,MAAMC,gCAAgC,GAAGA,CAACL,CAAC,EAAEI,MAAM,KAAK;IACtD,IAAI,CAAChQ,UAAU,CAAC4E,aAAa,EAAEoL,MAAM,CAAC,EAAE;MACtCzO,KAAK,CAAC+L,OAAO,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,IAAI,eAAExK,OAAA,CAACvD,yBAAyB;UAAAgO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACnCC,OAAO,EAAE,qBAAqB;QAC9BG,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAI;QAChBC,IAAI,EAAEA,CAAA,KAAM;UAAEjK,yBAAyB,CAAC,KAAK,CAAC;QAAE;MAClD,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,yBAAyB,CAAC,KAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAMiM,eAAe,GAAGA,CAAA,KAAM;IAC5BnM,eAAe,CAAC,KAAK,CAAC;IACtBR,IAAI,CAACoM,MAAM,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMQ,QAAQ,GAAG,MAAOH,MAAM,IAAK;IAAA,IAAAI,aAAA,EAAAC,qBAAA;IACjC,MAAM;MAAEtE;IAAK,CAAC,GAAGiE,MAAM;IACvB,IAAI,CAAClM,YAAY,IAAI,CAACiI,IAAI,EAAE;MAC1B;MACA,OAAOvL,UAAU,CAAC8P,OAAO,CAAC,YAAY,CAAC;IACzC;IACA,IAAI/G,YAAY,GAAG,CAAA9F,IAAI,aAAJA,IAAI,wBAAA2M,aAAA,GAAJ3M,IAAI,CAAE+L,OAAO,cAAAY,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAeG,yBAAyB,cAAAF,qBAAA,uBAAxCA,qBAAA,CAAA/H,IAAA,CAAA8H,aAA2C,CAAC,KAAI,EAAE;IACrE;IACA,IAAItM,YAAY,EAAE;MAChB,MAAM0M,aAAa,GAAGX,qBAAqB,CAAC,CAAC;MAC7C,IAAIpF,MAAM,GAAG;QACX,QAAQ,EAAEnH,MAAM;QAAE,WAAW,EAAE0M,MAAM,CAACjH,WAAW,CAACpC,SAAS,CAAC;QAAE,MAAM,EAAEoF,IAAI;QAAE,SAAS,EAAEhD,WAAW,CAACjB,OAAO;QAC1G,iBAAiB,EAAE,GAAG;QAAE,cAAc,EAAEyB,YAAY;QAAE,eAAe,EAAEiH;MACzE,CAAC;MACD;MACA/F,MAAM,GAAG;QAAE,GAAGA,MAAM;QAAEgG,OAAO,EAAE;MAAE,CAAC,EAAC;MACnC,OAAOjG,2BAA2B,CAACC,MAAM,CAAC;IAC5C;IACArF,gBAAgB,CAAC,IAAI,CAAC;IACtB;IACAd,YAAY,CAAC4H,OAAO,CAAC,CAACU,EAAE,EAAE6B,KAAK,KAAK;MAClC,IAAI7B,EAAE,CAACyC,QAAQ,IAAI9M,WAAW,CAACmO,KAAK,EAAE;QACpC9D,EAAE,CAACqC,UAAU,GAAG,IAAI;MACtB;IACF,CAAC,CAAC;IACF;IACA,IAAI0B,aAAa,GAAGrM,YAAY,CAAC8C,GAAG,CAAC,CAACwF,EAAE,EAAE6B,KAAK,MAAM;MACnD7C,OAAO,EAAEgB,EAAE,CAAChB,OAAO;MACnBqD,UAAU,EAAErC,EAAE,CAACqC,UAAU;MACzBC,cAAc,EAAEtC,EAAE,CAACsC,cAAc;MACjCC,iBAAiB,EAAEvC,EAAE,CAACuC,iBAAiB;MACvC7C,qBAAqB,EAAEM,EAAE,CAACN,qBAAqB;MAC/C8C,gBAAgB,EAAExC,EAAE,CAACwC,gBAAgB;MACrC3C,oBAAoB,EAAEG,EAAE,CAACH,oBAAoB;MAC7CmE,KAAK,EAAEnC,KAAK,GAAG;IACjB,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIoC,MAAM,GAAG;MAAEvN,MAAM;MAAEyI,IAAI;MAAEzC,QAAQ,EAAEqH,aAAa;MAAEpH,YAAY;MAAEE,SAAS,EAAEuG,MAAM,CAACvG,SAAS,GAAGrJ,UAAU,CAACqI,MAAM,GAAGrI,UAAU,CAAC0Q;IAAQ,CAAC;;IAE1I;IACA,IAAIvL,MAAM,EAAE;MACV;MACAsL,MAAM,CAAC5K,MAAM,GAAG7B,QAAQ,CAAC6B,MAAM;MAC/B4K,MAAM,CAAC9H,WAAW,CAACf,QAAQ,CAAC,GAAGgI,MAAM,CAACjH,WAAW,CAACpC,SAAS,CAAC;MAC5D,MAAMoK,aAAa,CAACF,MAAM,CAAC;IAC7B,CAAC,MAAM;MACL;MACAA,MAAM,CAACG,SAAS,GAAG5M,QAAQ,CAAC6B,MAAM;MAClC4K,MAAM,CAAC9H,WAAW,CAACf,QAAQ,CAAC,GAAGgI,MAAM,CAACjH,WAAW,CAACpC,SAAS,CAAC;MAC5D,MAAMsK,eAAe,CAACJ,MAAM,CAAC;IAC/B;IAEAzL,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;;EAED;EACA,eAAe6L,eAAeA,CAACJ,MAAM,EAAE;IACrC,MAAMhR,IAAI,CAACqR,gCAAgC,CAACL,MAAM,CAAC,CAACM,IAAI,CAACC,MAAM,IAAI;MACjE,IAAIA,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;QAAA,IAAAC,iBAAA;QAC5BtQ,qBAAqB,CAAC;UAAEuQ,QAAQ,EAAE,CAAAH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC;QAAC,CAAC,CAAC;QAC7D3N,iBAAiB,CAAC,KAAK,CAAC;QACxBO,QAAQ,CAACqN,QAAQ,IAAIrN,QAAQ,CAACqN,QAAQ,CAAC,CAAAL,MAAM,aAANA,MAAM,wBAAAE,iBAAA,GAANF,MAAM,CAAEI,QAAQ,CAAC,CAAC,CAAC,cAAAF,iBAAA,uBAAnBA,iBAAA,CAAqBxD,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC,CAAC;MAChF;IACF,CAAC,CAAC,CAAC4D,KAAK,CAACC,GAAG,IAAI;MACdvD,OAAO,CAACC,GAAG,CAACsD,GAAG,CAAC;IAClB,CAAC,CAAC;EACJ;;EAEA;EACA,eAAeZ,aAAaA,CAACF,MAAM,EAAE;IACnC,MAAMhR,IAAI,CAAC+R,gCAAgC,CAACf,MAAM,CAAC,CAACM,IAAI,CAACC,MAAM,IAAI;MACjE,IAAIA,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;QAC5BtQ,eAAe,CAAC,CAAC;QACjB8C,iBAAiB,CAAC,KAAK,CAAC;QACxB+F,yBAAyB,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC,CAAC8H,KAAK,CAACC,GAAG,IAAI;MACdvD,OAAO,CAACC,GAAG,CAACsD,GAAG,CAAC;IAClB,CAAC,CAAC;EACJ;EAEA,MAAME,cAAc,GAAIvK,KAAK,IAAK;IAAA,IAAAwK,cAAA,EAAAC,cAAA,EAAAC,qBAAA;IAChC,IAAIzI,YAAY,GAAG,CAAA9F,IAAI,aAAJA,IAAI,wBAAAqO,cAAA,GAAJrO,IAAI,CAAE+L,OAAO,cAAAsC,cAAA,uBAAbA,cAAA,CAAevB,yBAAyB,MAAI9M,IAAI,aAAJA,IAAI,wBAAAsO,cAAA,GAAJtO,IAAI,CAAE+L,OAAO,cAAAuC,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAexB,yBAAyB,cAAAyB,qBAAA,uBAAxCA,qBAAA,CAAA1J,IAAA,CAAAyJ,cAA2C,CAAC;IAC3G,IAAI9R,OAAO,CAACsJ,YAAY,CAAC,IAAItJ,OAAO,CAACqE,YAAY,CAAC,EAAE;MAClD;IACF;IACA,OAAO/C,KAAK,CAAC+L,OAAO,CAAC;MACnBC,KAAK,EAAE,IAAI;MACXC,IAAI,eAAExK,OAAA,CAACvD,yBAAyB;QAAAgO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,eAAE7K,OAAA;QAAA8K,QAAA,EAAI,OAAOjI,MAAM,GAAG,IAAI,GAAG,IAAI;MAA0B;QAAA4H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;MACvEI,MAAM,EAAE,GAAG;MACXC,UAAU,EAAE,GAAG;MACfgE,MAAM,EAAE,IAAI;MAAE;MACd/D,IAAI,EAAEA,CAAA,KAAM,CACZ,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAM;QACdC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QACrB9K,IAAI,CAAC2H,aAAa,CAACnC,WAAW,CAACpC,SAAS,EAAEoC,WAAW,CAACnB,YAAY,CAACqB,eAAe,CAAC,CAAC;MACtF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMiJ,qBAAqB,GAAItC,CAAC,IAAK;IACnC,MAAMtE,OAAO,GAAGsE,CAAC,CAACxF,MAAM,CAACkB,OAAO;;IAEhC;IACA,IAAI,CAACA,OAAO,IAAI,CAACrL,OAAO,CAACqE,YAAY,CAAC,EAAE;MACtC/C,KAAK,CAAC+L,OAAO,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,IAAI,eAAExK,OAAA,CAACvD,yBAAyB;UAAAgO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACnCC,OAAO,EAAE,yBAAyB;QAClCG,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,KAAK;QACjBgE,MAAM,EAAE,IAAI;QACZ/D,IAAI,EAAEA,CAAA,KAAM;UACV;UACA3J,eAAe,CAAC,EAAE,CAAC;UACnBe,iBAAiB,CAAC,KAAK,CAAC;UACxB/B,IAAI,CAAC2H,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC;QACxC,CAAC;QACDiD,QAAQ,EAAEA,CAAA,KAAM;UACd;UACA5K,IAAI,CAAC2H,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC;QACvC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAII,OAAO,EAAE;MAClB;MACAhG,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,oBAAOtC,OAAA,CAACF,eAAe;IACrBqP,SAAS,EAAC,2BAA2B;IACrCpI,KAAK,EAAE,KAAM;IACbwD,KAAK,EAAEhI,MAAM,GAAG,QAAQ,GAAG,QAAS;IACpC6M,cAAc,EAAE,IAAK;IACrBC,IAAI,EAAEzO,cAAe;IACrB0O,OAAO,EAAEjF,YAAa;IACtBkF,MAAM,eAAEvP,OAAA;MAAKwP,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAA3E,QAAA,eACzC9K,OAAA,CAACxB,KAAK;QAACkR,IAAI,EAAE,EAAG;QAAA5E,QAAA,gBACd9K,OAAA,CAAC7B,MAAM;UAACqR,KAAK,EAAE;YAAExI,YAAY,EAAE;UAAE,CAAE;UAAC2I,OAAO,EAAEtF,YAAa;UAAAS,QAAA,EAAC;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtE5K,OAAA,CAAC7B,MAAM;UAACiM,IAAI,EAAC,SAAS;UAACoF,KAAK,EAAE;YAAExI,YAAY,EAAE;UAAE,CAAE;UAAC2I,OAAO,EAAEzC,eAAgB;UAAApC,QAAA,EAAC;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAE;IAAAE,QAAA,gBAEP9K,OAAA,CAACR,aAAa;MAACoQ,QAAQ,EAAE7M,SAAU;MAAA+H,QAAA,eACjC9K,OAAA,CAAC3B,IAAI;QACHkC,IAAI,EAAEA,IAAK;QACXsP,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QACtBC,UAAU,EAAE;UAAED,IAAI,EAAE;QAAG,CAAE;QACzB3C,QAAQ,EAAEA,QAAS;QACnB6C,QAAQ,EAAE,KAAM;QAAA;QAChBC,YAAY,EAAE,KAAM,CAAC;QAAA;QACrBC,aAAa,EAAE;UACbzJ,SAAS,EAAE;QACb,CAAE;QAAAqE,QAAA,gBAEF9K,OAAA,CAAC3B,IAAI,CAAC8R,IAAI;UAACvM,KAAK,EAAE,QAAS;UAACmF,IAAI,EAAC,MAAM;UAACqH,QAAQ,EAAE,IAAK;UAAAtF,QAAA,eACrD9K,OAAA,CAAC1B,KAAK;YAACkR,KAAK,EAAE1I,WAAY;YAACmJ,YAAY,EAAC;UAAK;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACZ5K,OAAA,CAAC3B,IAAI,CAAC8R,IAAI;UAACvM,KAAK,EAAEmC,WAAW,CAACnC,KAAM;UAAAkH,QAAA,eAClC9K,OAAA,CAACxB,KAAK;YAACkR,IAAI,EAAE,EAAG;YAAA5E,QAAA,gBACd9K,OAAA,CAAC3B,IAAI,CAAC8R,IAAI;cAACpH,IAAI,EAAEhD,WAAW,CAACpC,SAAU;cAACyM,QAAQ,EAAE,IAAK;cAACC,OAAO;cAAAvF,QAAA,eAC7D9K,OAAA,CAACtB,MAAM;gBACL4R,UAAU;gBACVd,KAAK,EAAE;kBAAEzI,KAAK,EAAE,GAAG;kBAAEC,YAAY,EAAE;gBAAE;gBACrC;gBAAA;gBACAuJ,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;kBAC/B,OAAO,CAACA,MAAM,CAAC3F,QAAQ,CAAC4F,KAAK,CAAC5F,QAAQ,CAAC,CAAC,CAAC,CAAC4F,KAAK,CAAC5F,QAAQ,IAAI,EAAE,EAAE6F,WAAW,CAAC,CAAC,CAACC,OAAO,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;gBACjH,CAAE;gBACFE,QAAQ,EAAEhC,cAAe;gBAAA/D,QAAA,EAGvB7I,QAAQ,CAACmC,GAAG,CAACmB,IAAI,iBAAKvF,OAAA,CAACtB,MAAM,CAACoS,MAAM;kBAAgBxM,KAAK,EAAEiB,IAAI,CAACjB,KAAM;kBAAAwG,QAAA,eACpE9K,OAAA;oBAAKwP,KAAK,EAAE;sBAAEuB,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAAlG,QAAA,GACnDvF,IAAI,CAACiF,IAAI,eACVxK,OAAA;sBAAMwP,KAAK,EAAE;wBAAEyB,WAAW,EAAE;sBAAE,CAAE;sBAAAnG,QAAA,EAAEvF,IAAI,CAAC3B;oBAAK;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC,GAJkCrF,IAAI,CAAClB,GAAG;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKnC,CAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACZ5K,OAAA,CAAC3B,IAAI,CAAC8R,IAAI;cAACpH,IAAI,EAAC,WAAW;cAACmI,aAAa,EAAC,SAAS;cAACb,OAAO;cAAAvF,QAAA,eACzD9K,OAAA,CAACvB,QAAQ;gBAACoS,QAAQ,EAAE3B,qBAAsB;gBAAApE,QAAA,EAAC;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACZ5K,OAAA,CAAC3B,IAAI,CAAC8R,IAAI;UAACvM,KAAK,EAAE,OAAQ;UAAAkH,QAAA,EACvB7N,OAAO,CAACyE,YAAY,CAAC,gBAAG1B,OAAA,CAACP,MAAM;YAAAgL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,gBAChC5K,OAAA;YAAA8K,QAAA,gBACE9K,OAAA,CAACf,eAAe;cAACyC,YAAY,EAAEA,YAAa;cAACyP,KAAK,EAAE,EAAG;cAAC5K,YAAY,EAAE/E,aAAc;cAACmE,aAAa,EAAEA,aAAc;cAACyL,SAAS,EAAE,CAAE;cAACC,GAAG,EAAE5Q;YAAK;cAAAgK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9I5K,OAAA;cAAKmP,SAAS,EAAC,kBAAkB;cAAArE,QAAA,gBAC/B9K,OAAA,CAAC7B,MAAM;gBACLiM,IAAI,EAAC,MAAM;gBACXI,IAAI,eACFxK,OAAA,CAACrD,sBAAsB;kBAACwS,SAAS,EAAC;gBAAc;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACnD;gBACD+E,OAAO,EAAEpD,qBAAsB;gBAAAzB,QAAA,eAE/B9K,OAAA;kBAAA8K,QAAA,GAAM,kCAAO,eAAA9K,OAAA,CAACtD,gBAAgB;oBAAC8S,KAAK,EAAE;sBAAE8B,MAAM,EAAE;oBAAE;kBAAE;oBAAA7G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACT5K,OAAA,CAAC7B,MAAM;gBAACqM,IAAI,eAAExK,OAAA,CAACpD,cAAc;kBAAA6N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACuE,SAAS,EAAC,kBAAkB;gBAACQ,OAAO,EAAEjD,oBAAqB;gBAAA5B,QAAA,EAAE;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC,eACN5K,OAAA;cAAKmP,SAAS,EAAC,SAAS;cAAArE,QAAA,GAAC,yJAA0B,GAAA3K,qBAAA,GAACgD,WAAW,CAACoO,UAAU,cAAApR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAAC,oBAAG;YAAA;cAAAsK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1F5K,OAAA;cAAKmP,SAAS,EAAC,SAAS;cAAArE,QAAA,EAAC;YAA8B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEC,CAAC,EACXvI,cAAc,iBACbrC,OAAA,CAAC3B,IAAI,CAAC8R,IAAI;UAACvM,KAAK,EAAE,QAAS;UAAAkH,QAAA,gBACzB9K,OAAA,CAACV,iBAAiB;YAACgC,YAAY,EAAEA,YAAa;YAACC,eAAe,EAAEA,eAAgB;YAACoE,aAAa,EAAEA,aAAc;YAACkB,QAAQ,EAAEA,QAAS;YAACX,gBAAgB,EAAEA,gBAAiB;YAACD,eAAe,EAAEA;UAAgB;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3M5K,OAAA;YAAGmP,SAAS,EAAC,aAAa;YAACQ,OAAO,EAAEA,CAAA,KAAMxO,wBAAwB,CAAC,IAAI,CAAE;YAAA2J,QAAA,EAAC;UAAM;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpF5K,OAAA;YAAKmP,SAAS,EAAC,SAAS;YAAArE,QAAA,GAAC,2BAAK,GAAA1K,sBAAA,GAAC+C,WAAW,CAACoO,UAAU,cAAAnR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,EAAC,gBAAG,EAAC+C,WAAW,CAAC4F,IAAI,EAAC,wNAAsC;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7H5K,OAAA;YAAKmP,SAAS,EAAC,SAAS;YAAArE,QAAA,GAAC,mGAAiB,GAAAzK,sBAAA,GAAC8C,WAAW,CAACoO,UAAU,cAAAlR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,EAAC,sPAA0C;UAAA;YAAAoK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEhB5K,OAAA,CAACX,gBAAgB;MAACiC,YAAY,EAAEA,YAAa;MAACgF,QAAQ,EAAEJ,gBAAiB;MAACsL,OAAO,EAAEtQ,qBAAsB;MAACoK,cAAc,EAAEA,cAAe;MAACH,QAAQ,EAAEA,CAAA,KAAMhK,wBAAwB,CAAC,KAAK;IAAE;MAAAsJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE7L5K,OAAA,CAACd,uBAAuB;MAAAuL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3B5K,OAAA,CAAChB,kBAAkB;MACjBqQ,IAAI,EAAErO,sBAAuB;MAC7BkK,IAAI,EAAE6B,4BAA6B;MACnC5B,QAAQ,EAAE8B,gCAAiC;MAC3CwE,QAAQ,EAAErQ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqQ,QAAS;MAC7B7P,aAAa,EAAEA,aAAc;MAC7BF,YAAY,EAAEA;IAAa;MAAA+I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AACpB;AAAC1K,EAAA,CAjiBuBD,qBAAqB;EAAA,QACxBlB,SAAS,EACbV,IAAI,CAACmC,OAAO,EAwBqCb,yBAAyB,EAIYzB,QAAQ,EAG1C0B,8BAA8B,EAuCxEvB,IAAI,CAAC2H,QAAQ,EAM0E5G,sCAAsC,EAGPD,kCAAkC,EAEjJpC,mCAAmC,EAExCD,kCAAkC;AAAA;AAAA4U,EAAA,GArFvCzR,qBAAqB;AAAA,IAAAyR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}