import { QuestionsUploadIcon } from "@components/IconUtil";
import {Ava<PERSON>,Button,Cascader,DatePicker,Input,message,Form,Radio,Select,Tag,Upload, Modal} from "antd";
import DraggablePopUp from "@components/DraggablePopUp";
import { EditOutlined, CameraOutlined, CloseCircleFilled } from "@ant-design/icons";
import ImgCrop from "antd-img-crop";
import moment from "moment";
import React, { useEffect, useState, useRef } from "react";
import { team_619_user_info } from "@common/api/http";
import * as http from "@common/api/http";
import { useDispatch, shallowEqual, useSelector } from "react-redux";
import {province, personalList } from "../../utils/Config";
import { setLoginInfo } from "@/login/store/actionCreators";
import "./personalData.scss";
import { globalUtil } from "@common/utils/globalUtil";
import { NoAvatarIcon } from '@common/components/IconUtil';
import SettingsDrawer from 'src/settings/views/SettingsDrawer';
import { useParams } from "react-router-dom";

const { Dragger } = Upload;
const { Option } = Select;

// 个人资料
export default function PersonalData(params) {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const {teamId} = useParams();
    const state = useSelector((state) => ({
      userInfo: state.getIn(["login", "loginInfo", "userInfo"]),
    }), shallowEqual);
    const settingsDrawerTab = 'personal';
    const [settingsDrawerVisible, setSettingsDrawerVisible] = useState(false);

    useEffect(() => {
      if(!!state.userInfo?.userId) {
        console.log('用户Id', state.userInfo?.userId);
        getPersonInfo(state.userInfo)
      }
    },[state.userInfo?.userId])
    
    // 获取个人资料
    const getPersonInfo = (userInfo) => {
      http.uc_001_get_person_info({userId: userInfo.userId}).then((res) => {
        if(res.resultCode === 200) {
          // setStatisticalStudy({...res.personalInfo.statisticalStudy})
          // setInterestList([...res.personalInfo.interestList])
          
          form.setFieldValue("avatar", res.personalInfo.baseInfo["avatar"])
          personalList.forEach(item => {
            form.setFieldValue(item.name, res.personalInfo.baseInfo[item.name])
          })
        } else {
          // setStatisticalStudy({})
          // setInterestList([])

          form.setFieldValue("avatar", "")
          personalList.forEach(item => {
            form.setFieldValue(item.name, "")
          })
        }
      })
    }
    
    // 保存个人资料
    const savePersonInfo = () => {
      let formData = form.getFieldsValue(true);
      let personalInfo = {
        ...formData
        // avatar: personalObj.avatar,
        // userName: formData.userName.formItemValue,
        // gender: formData.gender.formItemValue,
        // birthday: formData.birthday.formItemValue,
        // job: formData.job.formItemValue,
        // workDate: formData.workDate.formItemValue,
        // city: formData.city.formItemValue,
        // college: formData.college.formItemValue
      }
      http.uc_002_save_person_info(personalInfo).then((res) => {
        if(res.resultCode === 200) {
          getPersonInfo(state.userInfo)
          // fixBUG tmsbug-3347
          updateUserInfo()
        }
      })
    }

    const updateUserInfo = () => {
      team_619_user_info({}).then(res => {
        if(res.resultCode == 200){
          dispatch(setLoginInfo({
            isAuthenticated: true,
            userInfo: res.userInfo
          }));
        }
      })
    }

  /*  // 保存用户感兴趣的标签
    const saveUserInterest = (tagList) => {
      http.uc_004_save_user_interest({interestIds: tagList}).then((res) => {
        if(res.resultCode === 200) {
          setAddInterestVisible(false);
          getPersonInfo(state.userInfo.userId)
        }
      })
    }*/

    return (<>
      <div className="PersonalData">
          <div className="PersonalData-left">
            <span style={{ marginLeft: 14 }}>基本信息</span>
            <Form 
              form={form} 
              colon={false} 
              className="PersonalData-form"
              onFieldsChange={savePersonInfo}>
                <Form.Item label="头像" name="avatar">
                  <PersonLogoFormItem/>
                </Form.Item>
                {personalList.map((item, index) => (
                  <Form.Item key={index} label={item.label} name={item.name}>
                    <CustomFormItem type={item.type} label={item.label} name={item.name} visible={setSettingsDrawerVisible} teamId={teamId}/>
                  </Form.Item>
                ))}
            </Form>
        </div>
          {/* <div className="PersonalData-right">
                <span>学习统计</span>
                <div></div>
            </div> */}
      </div>
          {/* <div className="PersonalData-interest">
              <span>选择感兴趣的内容</span>
              <div className="PersonalData-Tag">
                  {interestList.filter(el => el.checkFlg == 1).map((tag, index) => (
                  <Tag className="interestTagCheck">{tag.name}</Tag>
                  ))}
                  <Tag className="interestTagUnchecked" onClick={() => setAddInterestVisible(true)}>{interestList.filter(el => el.checkFlg == 1).length>0?'+更多':'+添加'}</Tag>
              </div>
              <AddInterestTagModal interestList={interestList} onOk={saveUserInterest} onCancel={() => setAddInterestVisible(false)} visible={addInterestVisible}/>
          </div> */}
          {/* 团队设置/成员管理/应用管理 Drawer */}
      {!!teamId && <SettingsDrawer 
        visible={settingsDrawerVisible} 
        onClose={()=>setSettingsDrawerVisible(false)} 
        teamId={teamId}
        defaultTab={settingsDrawerTab}
      />}
    </>)  
}

// 自定义FormItem
function CustomFormItem({ value='', onChange, type, label, name, visible, teamId}) {
    const [modifyFlag, setModifyFlag] = useState(false);
    const modifyRef = useRef();
    // 日期格式
    const dateFormat = "YYYY/MM/DD";

    const triggerChange = (changedValue) => {
      setModifyFlag(false);
      onChange?.(changedValue);
    };

    useEffect(() => {
      if (modifyFlag && type !== '') {
        modifyRef.current.focus({ cursor: "end" });
      }
    }, [modifyFlag]);

    const modifyClick = () => {
      setModifyFlag(true);
    };

    function content(){
      if(!teamId){
        return <div>个人中心的用户名是全局的，如您需要修改您在当前团队的成员名(供其他成员识别), 可返回团队并点击 个人设置(团队内) 进行调整。</div>
      }
      return <div>个人中心的用户名是全局的，如您需要修改您在当前团队的成员名(供其他成员识别), 可点击 <a onClick={()=>{visible(true)}}>个人设置(团队内)</a>  进行调整。</div>
    }

    const modifyBlur = (e) => {
      Modal.info({
        title: "友情提示",
        content: content(),
        maskClosable: true,
        //centered: true, // 居中
        okText: "我知道了",
        width: 500,
        onOk: ()=>{
          triggerChange(e.target.value);
        }
      });
    };

    const genderChange = (e) => {
      triggerChange(e);
    }

    // 选择日期
    const datePickerChange = (date, dateString) => {
        triggerChange(dateString);
    }

    // 选择城市
    const cascaderChange = (value, selectedOptions) => {
        triggerChange(value.join('/'));
    }

    // 获取form item编辑样式
    const getEditFormItemValue = (type,label) => {
        switch (type) {
            case 'input':
                return <Input
                        className="MemberFromItem-inpvalue"
                        autoComplete="off"
                        style={{ width: 240 }}
                        defaultValue={value}
                        ref={modifyRef}
                        onBlur={modifyBlur}
                        onPressEnter={modifyBlur}
                        placeholder={`请输入${label}`}/>
            case 'select':
                return <Select 
                        className="MemberFromItem-sex" 
                        style={{ width: 240 }} 
                        ref={modifyRef}
                        defaultValue={value} 
                        onChange={genderChange}
                        onBlur={() => setModifyFlag(false)}
                        placeholder={`请选择${label}`}>
                            <Option value={0}>未知</Option>
                            <Option value={1}>男</Option>
                            <Option value={2}>女</Option>
                        </Select>
            case 'date':
                return <DatePicker
                        style={{ width: 240 }}
                        ref={modifyRef}
                        defaultValue={value!=null&&value!=''?moment(value, dateFormat):null}
                        format={dateFormat}
                        onBlur={() => setModifyFlag(false)}
                        placeholder={`请选择${label}`}
                        onChange={datePickerChange}/>
            case 'cascader':
                return <Cascader 
                        style={{ width: 240 }}
                        ref={modifyRef}
                        defaultValue={value != null && value.split('/')}
                        options={province} 
                        onBlur={() => setModifyFlag(false)}
                        onChange={cascaderChange} 
                        placeholder={`请选择${label}`}/>
            default:
                return <></>
        }
    }

    // 格式化form item只读状态样式
    const getReadFormItemValue = (name) => {
        switch (name) {
            case 'gender':
                return (value == 1 ? '男' : (value == 2 ? '女' : '未知'))        
            default:
                return value
        }
    }

    return (
      <div className="MemberFromItem">
        <span>{modifyFlag?getEditFormItemValue(type,label):getReadFormItemValue(name)}</span>
        {!modifyFlag ? (
          <Button
            className="CustomFromItem-editIcon"
            size="small"
            type="link"
            icon={<EditOutlined />}
            onClick={modifyClick}/>
        ):<></>}
      </div>
    );
}

// 上传头像
function PersonLogoFormItem({value,onChange}){
  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑状态

  // 上传头像
  const avatarUpload = (link) => {
    onChange?.(link)
  }

  const deleteLogo = () => {
    onChange?.('') //20240313 Jim 从 null 改为 ''
  }

  return <React.Fragment>
    <div className="PersonalData-form-avatar">
      <Avatar
        className="PersonalData-avatar"
        src={value}
        icon={<NoAvatarIcon/>}/>
        <div className="PersonalData-form-upload">
          <CameraOutlined
            className="PersonalData-form-uploadIcon"
            onClick={(event) => setIsModalVisible(true)}/>
        </div>
        {!!value && <CloseCircleFilled title="删除头像" className="delete-PersonalData-avatar" onClick={deleteLogo}/>}
    </div>
    <DraggablePopUp
      title="上传logo"
      className="avatarUpload-modal"
      open={isModalVisible}
      onCancel={() => setIsModalVisible(false)}
      footer={null}>
      <ImgUpload avatarUpload={avatarUpload} onCancel={() => setIsModalVisible(false)}/>
    </DraggablePopUp>
  </React.Fragment>
}

// 图片上传
function ImgUpload(props) {
  const {avatarUpload,onCancel} = props
  const dataSource = {
    maxCount: 1,
    name: "file",
    multiple: false,
    showUploadList: false,
    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/uc_upload_file`,
    beforeUpload: (file) => {
      const isPNG = file.type === "image/png" || file.type === 'image/jpeg';
      if (!isPNG) {
        globalUtil.error(`${file.name}不是图片格式`);
      }
      return isPNG || Upload.LIST_IGNORE;
    },
    onChange(info) {
      onCancel()
      const { status, response } = info.file;
      if (status == "uploading") {
        console.log(info.file, info.fileList);
      }
      if (status === "done") {
        if(response.resultCode == 200) {
          avatarUpload(response.link)
          globalUtil.success('上传成功');
        } else {
          globalUtil.error("上传失败")
        }
      } else if (status === "error") {
        globalUtil.error(`${info.file.name} file upload failed.`);
      }
    },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };
  
  // 预览/裁剪图片
  const onPreview = async (file) => {
    let src = file.url;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj);
        reader.onload = () => resolve(reader.result);
      });
    }
    const image = new Image();
    image.src = src;
    const imgWindow = window.open(src);
    imgWindow.document.write(image.outerHTML);
  };
  
  return (
    <ImgCrop modalClassName="clippingImgCrop" rotate modalTitle={"编辑图片"} modalOk="确认" modalCancel="取消">
      <Dragger {...dataSource} onPreview={onPreview}>
        <p className="ant-upload-drag-icon">
          <QuestionsUploadIcon />
        </p>
        <p className="ant-upload-text">点击或拖动图片至此处</p>
        <p style={{color:'#999',marginTop:2}}>图片格式：jpg、png</p>
      </Dragger>
    </ImgCrop>
  );
}