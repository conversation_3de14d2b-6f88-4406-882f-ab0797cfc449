{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../core/range.js';\nimport { Searcher } from '../model/textModelSearch.js';\nimport * as strings from '../../../base/common/strings.js';\nimport { assertNever } from '../../../base/common/types.js';\nimport { DEFAULT_WORD_REGEXP, getWordAtText } from '../core/wordHelper.js';\nexport class UnicodeTextModelHighlighter {\n  static computeUnicodeHighlights(model, options, range) {\n    const startLine = range ? range.startLineNumber : 1;\n    const endLine = range ? range.endLineNumber : model.getLineCount();\n    const codePointHighlighter = new CodePointHighlighter(options);\n    const candidates = codePointHighlighter.getCandidateCodePoints();\n    let regex;\n    if (candidates === 'allNonBasicAscii') {\n      regex = new RegExp('[^\\\\t\\\\n\\\\r\\\\x20-\\\\x7E]', 'g');\n    } else {\n      regex = new RegExp(`${buildRegExpCharClassExpr(Array.from(candidates))}`, 'g');\n    }\n    const searcher = new Searcher(null, regex);\n    const ranges = [];\n    let hasMore = false;\n    let m;\n    let ambiguousCharacterCount = 0;\n    let invisibleCharacterCount = 0;\n    let nonBasicAsciiCharacterCount = 0;\n    forLoop: for (let lineNumber = startLine, lineCount = endLine; lineNumber <= lineCount; lineNumber++) {\n      const lineContent = model.getLineContent(lineNumber);\n      const lineLength = lineContent.length;\n      // Reset regex to search from the beginning\n      searcher.reset(0);\n      do {\n        m = searcher.next(lineContent);\n        if (m) {\n          let startIndex = m.index;\n          let endIndex = m.index + m[0].length;\n          // Extend range to entire code point\n          if (startIndex > 0) {\n            const charCodeBefore = lineContent.charCodeAt(startIndex - 1);\n            if (strings.isHighSurrogate(charCodeBefore)) {\n              startIndex--;\n            }\n          }\n          if (endIndex + 1 < lineLength) {\n            const charCodeBefore = lineContent.charCodeAt(endIndex - 1);\n            if (strings.isHighSurrogate(charCodeBefore)) {\n              endIndex++;\n            }\n          }\n          const str = lineContent.substring(startIndex, endIndex);\n          const word = getWordAtText(startIndex + 1, DEFAULT_WORD_REGEXP, lineContent, 0);\n          const highlightReason = codePointHighlighter.shouldHighlightNonBasicASCII(str, word ? word.word : null);\n          if (highlightReason !== 0 /* SimpleHighlightReason.None */) {\n            if (highlightReason === 3 /* SimpleHighlightReason.Ambiguous */) {\n              ambiguousCharacterCount++;\n            } else if (highlightReason === 2 /* SimpleHighlightReason.Invisible */) {\n              invisibleCharacterCount++;\n            } else if (highlightReason === 1 /* SimpleHighlightReason.NonBasicASCII */) {\n              nonBasicAsciiCharacterCount++;\n            } else {\n              assertNever(highlightReason);\n            }\n            const MAX_RESULT_LENGTH = 1000;\n            if (ranges.length >= MAX_RESULT_LENGTH) {\n              hasMore = true;\n              break forLoop;\n            }\n            ranges.push(new Range(lineNumber, startIndex + 1, lineNumber, endIndex + 1));\n          }\n        }\n      } while (m);\n    }\n    return {\n      ranges,\n      hasMore,\n      ambiguousCharacterCount,\n      invisibleCharacterCount,\n      nonBasicAsciiCharacterCount\n    };\n  }\n  static computeUnicodeHighlightReason(char, options) {\n    const codePointHighlighter = new CodePointHighlighter(options);\n    const reason = codePointHighlighter.shouldHighlightNonBasicASCII(char, null);\n    switch (reason) {\n      case 0 /* SimpleHighlightReason.None */:\n        return null;\n      case 2 /* SimpleHighlightReason.Invisible */:\n        return {\n          kind: 1 /* UnicodeHighlighterReasonKind.Invisible */\n        };\n      case 3 /* SimpleHighlightReason.Ambiguous */:\n        {\n          const codePoint = char.codePointAt(0);\n          const primaryConfusable = codePointHighlighter.ambiguousCharacters.getPrimaryConfusable(codePoint);\n          const notAmbiguousInLocales = strings.AmbiguousCharacters.getLocales().filter(l => !strings.AmbiguousCharacters.getInstance(new Set([...options.allowedLocales, l])).isAmbiguous(codePoint));\n          return {\n            kind: 0 /* UnicodeHighlighterReasonKind.Ambiguous */,\n            confusableWith: String.fromCodePoint(primaryConfusable),\n            notAmbiguousInLocales\n          };\n        }\n      case 1 /* SimpleHighlightReason.NonBasicASCII */:\n        return {\n          kind: 2 /* UnicodeHighlighterReasonKind.NonBasicAscii */\n        };\n    }\n  }\n}\nfunction buildRegExpCharClassExpr(codePoints, flags) {\n  const src = `[${strings.escapeRegExpCharacters(codePoints.map(i => String.fromCodePoint(i)).join(''))}]`;\n  return src;\n}\nclass CodePointHighlighter {\n  constructor(options) {\n    this.options = options;\n    this.allowedCodePoints = new Set(options.allowedCodePoints);\n    this.ambiguousCharacters = strings.AmbiguousCharacters.getInstance(new Set(options.allowedLocales));\n  }\n  getCandidateCodePoints() {\n    if (this.options.nonBasicASCII) {\n      return 'allNonBasicAscii';\n    }\n    const set = new Set();\n    if (this.options.invisibleCharacters) {\n      for (const cp of strings.InvisibleCharacters.codePoints) {\n        if (!isAllowedInvisibleCharacter(String.fromCodePoint(cp))) {\n          set.add(cp);\n        }\n      }\n    }\n    if (this.options.ambiguousCharacters) {\n      for (const cp of this.ambiguousCharacters.getConfusableCodePoints()) {\n        set.add(cp);\n      }\n    }\n    for (const cp of this.allowedCodePoints) {\n      set.delete(cp);\n    }\n    return set;\n  }\n  shouldHighlightNonBasicASCII(character, wordContext) {\n    const codePoint = character.codePointAt(0);\n    if (this.allowedCodePoints.has(codePoint)) {\n      return 0 /* SimpleHighlightReason.None */;\n    }\n    if (this.options.nonBasicASCII) {\n      return 1 /* SimpleHighlightReason.NonBasicASCII */;\n    }\n    let hasBasicASCIICharacters = false;\n    let hasNonConfusableNonBasicAsciiCharacter = false;\n    if (wordContext) {\n      for (const char of wordContext) {\n        const codePoint = char.codePointAt(0);\n        const isBasicASCII = strings.isBasicASCII(char);\n        hasBasicASCIICharacters = hasBasicASCIICharacters || isBasicASCII;\n        if (!isBasicASCII && !this.ambiguousCharacters.isAmbiguous(codePoint) && !strings.InvisibleCharacters.isInvisibleCharacter(codePoint)) {\n          hasNonConfusableNonBasicAsciiCharacter = true;\n        }\n      }\n    }\n    if (/* Don't allow mixing weird looking characters with ASCII */!hasBasicASCIICharacters && /* Is there an obviously weird looking character? */hasNonConfusableNonBasicAsciiCharacter) {\n      return 0 /* SimpleHighlightReason.None */;\n    }\n    if (this.options.invisibleCharacters) {\n      // TODO check for emojis\n      if (!isAllowedInvisibleCharacter(character) && strings.InvisibleCharacters.isInvisibleCharacter(codePoint)) {\n        return 2 /* SimpleHighlightReason.Invisible */;\n      }\n    }\n    if (this.options.ambiguousCharacters) {\n      if (this.ambiguousCharacters.isAmbiguous(codePoint)) {\n        return 3 /* SimpleHighlightReason.Ambiguous */;\n      }\n    }\n    return 0 /* SimpleHighlightReason.None */;\n  }\n}\nfunction isAllowedInvisibleCharacter(character) {\n  return character === ' ' || character === '\\n' || character === '\\t';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}