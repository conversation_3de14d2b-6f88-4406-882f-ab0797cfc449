/* eslint-disable react-hooks/exhaustive-deps */
import TEmpty from '@components/TEmpty';
import { ExclamationCircleOutlined, FunctionOutlined, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';
import * as http from "@common/api/http";
import { track_002_get_issuegrp_info_query } from "@common/api/query/query";
import { useQuerySetting202_getTeamAllUsers, useQuerySetting407_getCodeValueList } from "@common/service/commonHooks";
import { compareArr, isEmpty } from '@common/utils/ArrayUtils';
import { eConsolePropId, eEditingMode, eEnableFlg } from "@common/utils/enum";
import { globalEventBus } from "@common/utils/eventBus";
import { globalUtil } from "@common/utils/globalUtil";
import { getQueryableAttrNodeList, transformAttrNodeListForUI, transformCriteriaListForUI, updateObjNodeInfoQuery } from '@common/utils/logicUtils';
import * as toolUtil from "@common/utils/toolUtil";
import { eNodeTypeId, refreshTeamMenu } from "@common/utils/TsbConfig";
import { useQuery } from "@tanstack/react-query";
import { Button, Drawer, Form, Input, Modal, Space, Spin, Checkbox } from "antd";
import { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import SearchDisplayField from 'src/quickAcess/views/Search/SearchDetail/SearchDisplayField';
import SearchEditTable from "src/quickAcess/views/Search/SearchDetail/SearchEditTable";
import SearchReultPreviewModal from 'src/quickAcess/views/Search/SearchDetail/SearchReultPreviewModal';
import { useQueryTrack005_getSubclass, useQueryTrack019GetPartitionDetail, useQuerySetting409_getTeamAttrgrpProps } from "../../service/issueHooks";
import AddPartitionAttr from "./AddPartitionAttr";
import CustomerFormTable from "./CustomerFormTable";
import "./IssuePartition.scss";
import DraggableDrawer from "@components/DraggableDrawer";

// 新建&编辑 采集口径
export default function CreateIssuePartition() {
    const { teamId } = useParams();
    const [form] = Form.useForm();
    const tRef = useRef(Object.create(null));
    // 弹窗显示配置
    const [isModalVisible, setIsModalVisible] = useState(false); //采集口径弹窗
    const [searchPreFlg, setSearchPreFlg] = useState(false); //是否搜索预览
    const [searchDisplayFieldOpen, setSearchDisplayFieldOpen] = useState(false); //显示字段弹窗
    const [isAddAttrModalVisible, setIsAddAttrModalVisible] = useState(false); //添加字段弹窗
    // 数据配置
    const [nodeItem, setNodeItem] = useState({}); //节点信息
    const [opType, setOpType] = useState(eEditingMode.Creating_0 ); //0:新建 1：编辑
    const [selectFields, setSelectFields] = useState([]); //已选择的表单字段
    const [initTableData, setInitTableData] = useState([]); // 初始数据
    const [attrNodeList, setAttrNodeList] = useState([]); // 字段属性列表
    const [checkedValues, setCheckedValues] = useState([]); // 显示字段勾选
    const [modalKey, setModalKey] = useState(toolUtil.guid()); // 显示字段勾选
    const [showFormFields, setShowFormFields] = useState(true); // 控制表单字段设置的显示
    // 接口调用
    //（1）获取subclassid
    const { subclassNid, projectId, isLoading: isLoadingGetSubclass, /*refetch: refetchGetSubclass*/ } = useQueryTrack005_getSubclass(teamId, nodeItem?.nodeId, !!nodeItem?.nodeId);
    //（2）获取自定义表单字段
    const { subclassAttrList=[], exprsList=[], isLoading: isLoadingGetSubclassAttrs, /*refetch: refetchGetAttrs*/ } = useQuerySetting409_getTeamAttrgrpProps(teamId, nodeItem?.nodeId, subclassNid, !!subclassNid);
    // (2.1) 编辑时获取采集口径详情 TODO: useQuery cacheTime 需要在相关key变化时才会清除缓存数据，所以需要一个变量key去触发再次打开的更新操作
    const { attrList=[], criteriaList=[], bizNodeId, objType, createFlg, isLoading: isLoadingGetGetPartitionDetail, /*refetch: refetchGetPartitionDetail*/ } = useQueryTrack019GetPartitionDetail(teamId, nodeItem?.nodeId, modalKey, opType == eEditingMode.Modifying_1 && !!nodeItem?.nodeId && !isLoadingGetSubclassAttrs );
    //（3）字典数据
    const { data: selectionList, /*isLoading: isLoadingCodeValueList, refetch: refetchGetValueList*/ } = useQuerySetting407_getCodeValueList(teamId);
    //（4）获取人员列表
    const { data: userList, /*isLoading, refetch: refetchGetUserList*/ } = useQuerySetting202_getTeamAllUsers(teamId);
    // (5) 项目信息
    const {data: projectInfo = {}} = useQuery(track_002_get_issuegrp_info_query(teamId, nodeItem?.nodeId, projectId, !!projectId))
    
    // 新增：动态设置浏览器标题
    const prevTitle = useRef(document.title);
    useEffect(() => {
      if (isModalVisible) {
        prevTitle.current = document.title;
        if (opType) {
          document.title = `${projectInfo.issueAlias ?? ""}采集口径设置`;
        } else {
          document.title = `新建${projectInfo.issueAlias ?? ""}采集口径`;
        }
      } else {
        document.title = prevTitle.current;
      }
      return () => {
        document.title = prevTitle.current;
      };
    }, [isModalVisible, opType, projectInfo.issueAlias]);
    
    // 选择样式
    const selectStyle = { width: 300, borderRadius: 5 };

    // 监听打开新建/编辑采集口径弹窗
    useEffect(() => {
        globalEventBus.on("openCreateIssuePartition", openCreateIssueProjectEvent)
        return () => globalEventBus.off("openCreateIssuePartition", openCreateIssueProjectEvent)
    }, [])

    // 打开创建项目弹窗事件
    const openCreateIssueProjectEvent = (target, args) => {
        setModalKey(toolUtil.guid());
        console.log("target,args", target, args)
        // 如果nodeType为31703，则为编辑采集口径
        if (args.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp || args.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp) {
            setOpType(eEditingMode.Modifying_1 )
        } else {
            setOpType(eEditingMode.Creating_0 )
        }
        setNodeItem(args);
        setIsModalVisible(true);
    }

    // 打开弹窗/关闭弹窗
    useEffect(() => {
      if (isModalVisible && opType && !isLoadingGetGetPartitionDetail) {
          // 加载数据
          loadIssuePartitionDetail()
      } else {
          // 初始化数据
          initialIssuePartitionData()
      }
    }, [isModalVisible, isLoadingGetGetPartitionDetail])

    // 打开搜索预览弹窗
    const openSearchReultPreviewModal = (requst) => {
        globalEventBus.emit("openSearchReultPreviewModalEvent", "", { searchPreRequest: requst, onDisplayClick: onDisplayClick.bind(this) });
    }

    // 自定义表单字段
    useEffect(() => {
        if (!isLoadingGetSubclassAttrs) {
          changePropertyTypeList(subclassAttrList)
        }
    }, [isLoadingGetSubclassAttrs])

    // 编辑搜索数据
    useEffect(() => {
      if (opType == eEditingMode.Modifying_1 && !isLoadingGetGetPartitionDetail && !isEmpty(attrNodeList)) {
        changeCriteriaList()
      }
  }, [isLoadingGetGetPartitionDetail, attrNodeList])

    // 自定义表单数据处理
    const changePropertyTypeList = (attrgrpProps) => {
        let attrList = transformAttrNodeListForUI(attrgrpProps, userList, selectionList, true);
        const checkedValues = attrList.filter(item => item.checked).map(item => item.nodeId);
        const properTypeListUI = getQueryableAttrNodeList(attrList); //过滤59类型
        setAttrNodeList(properTypeListUI);
        setCheckedValues(checkedValues);
    }

    // 编辑搜索数据
    const changeCriteriaList = () =>{
      let _criteriaList = transformCriteriaListForUI(criteriaList, attrNodeList, selectionList);
      setInitTableData(_criteriaList);
    }

    // 初始化数据
    function initialIssuePartitionData() {
        // console.log("正在清空数据...")
        setInitTableData([]); //清空高级搜索数据
        setSelectFields([]); //清空字段属性数据
        setShowFormFields(true); //重置表单字段设置显示状态
    }

    // 加载采集口径详情
    function loadIssuePartitionDetail() {
      const isCreateEnabled = createFlg == eEnableFlg.enable; 
        form.setFieldsValue({
            name: nodeItem.name,// name回显
            createFlg: isCreateEnabled
        });
         // 根据createFlg设置表单字段设置的显示状态
          setShowFormFields(isCreateEnabled);

        const _attrList = [...attrList];
        _attrList.forEach((attr)=>{
          const propertyList = subclassAttrList.find(subclass =>  attr.attrNid == subclass.nodeId).propertyList;
          attr.attrModifyableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);
          attr.attrQueryableFlgBack = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);
        })
        setSelectFields(_attrList)
    }

    // 默认值配置
    const getDefAttrPropValuepByType = (propList = [], type) => {
        return (propList.find((item) => item.propType == type) || { propValue: "0" }).propValue;
    }

    // 取消
    const handleCancel = () => {
        setIsModalVisible(false)
    }

    // 选择字段数据处理
    const onSelectFields = (items) => {
        let fieldList = []
        selectFields.filter(el => {
            items.map((_el, index) => {
                if (el.attrNid == _el.nodeId) {
                    fieldList.push(el)
                    delete items[index]
                }
            })
        })
        items.map((item) => {
            let propertyList = item.propertyList
            const _attrModifyableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_60_modifiable);
            const _attrQueryableFlg = getDefAttrPropValuepByType(propertyList, eConsolePropId.Prop_59_queryable);
            let field = {
                attrNid: item.nodeId,
                defaultVal: null,
                attrVisibleFlg: '',
                attrModifyableFlg: _attrModifyableFlg,
                attrModifyableFlgBack: _attrModifyableFlg,
                attrQueryableFlg: _attrQueryableFlg,
                attrQueryableFlgBack: _attrQueryableFlg,
            }
            fieldList.push(field)
        })
        setSelectFields(fieldList)
        setIsAddAttrModalVisible(false)
    }

    // + 动态条件
    const handleOnAddSearchCode = () => {
        tRef.current.addSearchCode();
    }

    // 搜索预览 无需校验搜索名称
    const handleSearchPreClick = () => {
        setSearchPreFlg(true);
        form.submit();
    }

    // 显示字段
    const onDisplayClick = (e) => {
        setSearchDisplayFieldOpen(true);
    };

    // 显示字段数据处理
    const assembleQueryAttrList = () => {
        return checkedValues.map(checkedValue => ({ attrNid: checkedValue }));
    }

    // 保存显示字段
    const handleSearchDisplayFieldOnOk = (e, values) => {
        setSearchDisplayFieldOpen(false);
        setCheckedValues(values);
        globalEventBus.emit("changeSearchReultPreviewEvent", "", { values }) // 根据显示字段过滤
    }

    // 取消保存字段
    const handleSearchDisplayFieldOnCancel = (e, values) => {
        if (!compareArr(checkedValues, values)) {
            Modal.confirm({
                title: '提醒',
                icon: <ExclamationCircleOutlined />,
                content: "放弃后将不会保存显示字段，确定要放弃？",
                okText: '确定',
                cancelText: '取消',
                onOk: () => { setSearchDisplayFieldOpen(false); }
            });
        } else {
            setSearchDisplayFieldOpen(false);
        }
    };

    // 点击 确定
    const handleSaveClick = () => {
        setSearchPreFlg(false);
        form.submit();
    }

    // 表单提交 form.submit()
    const onFinish = (values) => {
        const { name } = values;
        if (!searchPreFlg && !name) {
            // 校验名称
            return globalUtil.warning("请填写采集口径名称!")
        }
        let criteriaList = tRef.current.getCriteriaListForBackend()
        // 搜索预览
        if (searchPreFlg) {
            const queryAttrList = assembleQueryAttrList();
            let requst = {
                teamId: teamId,
                bizNodeId: opType == eEditingMode.Modifying_1 ? bizNodeId : nodeItem?.nodeId,
                name: name,
                objType: opType == eEditingMode.Modifying_1 ? objType : eNodeTypeId.nt_317_objtype_issue_project,
                advanceQueryFlg: "1",
                criteriaList: criteriaList,
                queryAttrList: queryAttrList
            }
            // 搜索预览
            requst = { ...requst, pageNum: 1 } //默认查询第一页
            return openSearchReultPreviewModal(requst);
        }
        selectFields.forEach((el, index) => {
            el.seqNo = index + 1
        })
        if (opType) {
            // 编辑
            let params = { teamId, nodeId: nodeItem.nodeId, name, createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable, attrList: selectFields, criteriaList }
            editPartition(params);
        } else {
            // 新建
            let params = { teamId, objNodeId: nodeItem.nodeId, name, createFlg: values.createFlg ? eEnableFlg.enable : eEnableFlg.disable, attrList: selectFields, criteriaList }
            createPartition(params);
        }
    };

    // 新建采集口径
    function createPartition(params) {
        http.track_018_create_issue_partition(params).then(result => {
            if (result.resultCode == 200) {
                refreshTeamMenu();
                setIsModalVisible(false);
                nodeItem.callback && nodeItem.callback(result?.nodeTree[0]?.children[0] || {});
            }
        }).catch(err => {
            console.log(err)
        })
    }

    // 编辑采集口径
    function editPartition(params) {
        http.track_020_modify_issue_partition(params).then(result => {
            if (result.resultCode == 200) {
                refreshTeamMenu();
                setIsModalVisible(false);
                updateObjNodeInfoQuery(teamId, nodeItem?.nodeId, {
                    label: params.name,   // 更新名称
                    nodeName: params.name, // 更新名称
                  });// 更新节点名称
            }
        }).catch(err => {
            console.log(err)
        })
    }

    // 处理"可新建"复选框变化
  const handleCreateFlgChange = (e) => {
    const checked = e.target.checked;

    // 如果取消勾选且表单字段设置有值，弹出确认对话框
    if (!checked && !isEmpty(selectFields)) {
      Modal.confirm({
        title: "提示",
        icon: <ExclamationCircleOutlined />,
        content: `取消"可新建"功能，会清除表单字段设置，是否取消？`,
        okText: "取消",
        cancelText: "不取消",
        zIndex: 1002,
        onOk: () => {
          // 用户确认取消，清除表单字段设置并隐藏
          setSelectFields([]);
          setShowFormFields(false);
          form.setFieldValue('createFlg', false);
        },
        onCancel: () => {
          // 用户选择不取消，恢复勾选状态
          form.setFieldValue('createFlg', true);
        },
      });
    } else {
      // 如果重新勾选，显示表单字段设置
      setShowFormFields(checked);
    } 
  }

    return <DraggableDrawer
        className="tms-drawer IssuePartition"
        width={"60%"}
        title={opType ? `${projectInfo.issueAlias??""}采集口径设置` : `新建 ${projectInfo.issueAlias??""}采集口径`}
        destroyOnClose={true}
        open={isModalVisible}
        onClose={handleCancel}
        footer={<div style={{ textAlign: "right" }} >
            <Space size={20}>
                <Button style={{ borderRadius: 5 }} onClick={handleCancel}>取消</Button>
                <Button type="primary" style={{ borderRadius: 5 }} onClick={handleSaveClick}>提交</Button>
            </Space>
        </div>}
    >
        <Spin spinning={isLoadingGetSubclass || isLoadingGetSubclassAttrs}>
            <Form
                form={form}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 19 }}
                onFinish={onFinish}
                preserve={false}// Modal关闭后销毁form字段数据
                autoComplete={"off"} // 取消自动补充功能
                initialValues={{
                  createFlg: true,
                }}
            >
                <Form.Item label={"采集口径名称"}>
                  <Space size={20}>
                     <Form.Item name="name" required={true} noStyle>
                      <Input style={selectStyle} autoComplete="off"/>
                     </ Form.Item>
                     <Form.Item name="createFlg" valuePropName="checked" noStyle>
                      <Checkbox onChange={handleCreateFlgChange}>可新建</Checkbox>
                     </Form.Item>        
                  </Space>
                </Form.Item>
                <Form.Item label={"采集口径条件"}>
                    {isEmpty(attrNodeList) ? <TEmpty/> :
                        <div>
                            <SearchEditTable attrNodeList={attrNodeList} exprs={exprsList} criteriaList={initTableData} selectionList={selectionList} queryType={0} ref={tRef} />
                            <div className="search-edit-btns">
                                <Button 
                                    type="link" 
                                    icon={
                                        <QuestionCircleOutlined className="color-yellow" />
                                    } 
                                    onClick={handleOnAddSearchCode}
                                >
                                    <span>+ 动态条件（<FunctionOutlined style={{ margin: 0 }} />）</span>
                                </Button>
                                <Button icon={<SearchOutlined />} className="defaultBtn_light" onClick={handleSearchPreClick} >结果预览</Button>
                            </div>
                            <div className="remarks">备注1：此处的自定义搜索，用于显示当前采集口径对应的{projectInfo.issueAlias??""}列表。</div>
                            <div className="remarks">备注2：点击确定按钮仅保存表单(即条件)，预览结果不做保存。</div>
                        </div>
                    }
                </Form.Item>
                {
                  showFormFields && <Form.Item label={"表单字段设置"}>
                    <CustomerFormTable selectFields={selectFields} setSelectFields={setSelectFields} selectionList={selectionList} userList={userList} subclassAttrList={subclassAttrList} />
                    <a className="fontsize-12" onClick={() => setIsAddAttrModalVisible(true)}>+ 添加字段</a>
                    <div className="remarks">备注1：在项目“{projectInfo.name}”的自定义字段的基础上，进一步限缩上面表格中字段的 “是否显示”，“可修改”，“可搜索”。</div>
                    <div className="remarks">备注2：在当前采集口径中提交的新建{projectInfo.issueAlias??""}，其对应上面表格中的字段，如果值为空，则使用”缺省值”列中的值，进行最后的数据保存。</div>
                </Form.Item>
                }
            </Form>
        </Spin>
        {/* 添加字段 */}
        <AddPartitionAttr selectFields={selectFields}
                          attrList={subclassAttrList}
                          visible={isAddAttrModalVisible}
                          onSelectFields={onSelectFields}
                          onCancel={() => setIsAddAttrModalVisible(false)} />
        {/* 搜索结果预览 */}
        <SearchReultPreviewModal />
        {/* 显示字段 */}
        <SearchDisplayField
            open={searchDisplayFieldOpen}
            onOk={handleSearchDisplayFieldOnOk}
            onCancel={handleSearchDisplayFieldOnCancel}
            nodeName={nodeItem?.nodeName}
            checkedValues={checkedValues}
            attrNodeList={attrNodeList}
        />
    </DraggableDrawer>
}