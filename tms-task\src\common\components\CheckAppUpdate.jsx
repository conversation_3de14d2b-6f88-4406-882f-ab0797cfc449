import { local_check_version_update } from "@common/api/http";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import { Modal, Divider, Typography } from "antd";
import * as storageConstant from "@common/utils/storageConstant";
import { eEnableFlg } from "@common/utils/enum";

const { Text } = Typography;

export default function CheckAppUpdate() {
  const [systemUpdateTips,setSystemUpdateTips] = useState(true);

  // 版本更新
  const versionQuery = useQuery({
    queryFn: async() => {
      return local_check_version_update({timestamp: Date.now()}).then((result) => {
        return {
          version: result.version || process.env.REACT_APP_VERSION,
          changelog: result.changelog || null
        }
      })
    },
    enabled: process.env.NODE_ENV === "production" && 
    (localStorage.getItem(storageConstant.no_polling ) != eEnableFlg.enable),
    cacheTime: Infinity,
    // 每15秒会刷新一次接口
    refetchInterval: 15000,
    refetchOnWindowFocus: true
  })

  // 渲染更新日志内容
  const renderChangelogContent = (changelog, newVersion) => {
    if (!changelog || !changelog.versionInfo ) {
      return null;
    }

    const { versionInfo } = changelog;

    return (
      <div style={{ marginTop: 16 }}>
        <Divider orientation="left" style={{ margin: '12px 0 8px 0' }}>
          <Text strong style={{ fontSize: 14 }}>更新内容</Text>
        </Divider>

        {versionInfo.features && versionInfo.features.length > 0 && (
          <div style={{ marginBottom: 12 }}>
            <Text strong style={{ color: '#52c41a', fontSize: 13 }}>✨ 新功能</Text>
            <ul style={{ margin: '4px 0 0 0', paddingLeft: 20 }}>
              {versionInfo.features.map((feature, index) => (
                <li key={index} style={{ fontSize: 12, lineHeight: '18px', marginBottom: 2 }}>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        )}

        {versionInfo.improvements && versionInfo.improvements.length > 0 && (
          <div style={{ marginBottom: 12 }}>
            <Text strong style={{ color: '#1890ff', fontSize: 13 }}>🚀 优化改进</Text>
            <ul style={{ margin: '4px 0 0 0', paddingLeft: 20 }}>
              {versionInfo.improvements.map((improvement, index) => (
                <li key={index} style={{ fontSize: 12, lineHeight: '18px', marginBottom: 2 }}>
                  {improvement}
                </li>
              ))}
            </ul>
          </div>
        )}

        {versionInfo.bugfixes && versionInfo.bugfixes.length > 0 && (
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ color: '#ff4d4f', fontSize: 13 }}>🐛 问题修复</Text>
            <ul style={{ margin: '4px 0 0 0', paddingLeft: 20 }}>
              {versionInfo.bugfixes.map((bugfix, index) => (
                <li key={index} style={{ fontSize: 12, lineHeight: '18px', marginBottom: 2 }}>
                  {bugfix}
                </li>
              ))}
            </ul>
          </div>
        )}

        {versionInfo.releaseDate && (
          <div style={{ marginTop: 12, textAlign: 'right' }}>
            <Text type="secondary" style={{ fontSize: 11 }}>
              发布时间: {versionInfo.releaseDate}
            </Text>
          </div>
        )}
      </div>
    );
  };

  useEffect(() => {
    if(versionQuery.dataUpdatedAt && systemUpdateTips && versionQuery.data &&
       Number(versionQuery.data.version) !== Number(process.env.REACT_APP_VERSION)) {
      setSystemUpdateTips(false);

      const newVersion = versionQuery.data.version;
      const changelog = versionQuery.data.changelog;

      Modal.confirm({
        title: "新版本更新",
        width: 600,
        content: <div>
          <div style={{ marginBottom: 12 }}>
            <div>当前版本: <Text code>{process.env.REACT_APP_VERSION}</Text></div>
            <div>最新版本: <Text code style={{color: '#fa86d9'}}>{newVersion}</Text></div>
          </div>
          <div style={{ fontSize: 13, color: '#666', marginBottom: 8 }}>
            点击"立即更新"按钮或刷新页面，访问最新版本
          </div>
          {renderChangelogContent(changelog, newVersion)}
        </div>,
        okText: '立即更新',
        cancelText: '暂不更新',
        onOk: () => {
          window.location.reload(true);
        }
      })
    }
  },[versionQuery.dataUpdatedAt])

  return <React.Fragment></React.Fragment>
}