/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-use-before-define */
import { track_008_get_issue_list_query, track_002_get_issuegrp_info_query, setting_105_get_team_detail_query } from "@common/api/query/query";
import { useQuerySetting407_getCodeValueList } from "@common/service/commonHooks";
import { useQuerySetting320GetNodePrivQuery } from "@common/service/commonHooks";
import { eConsoleNodeId, eIssueViewMode, ePagination } from "@common/utils/enum";
import { globalUtil } from "@common/utils/globalUtil";
import * as toolUtil from "@common/utils/toolUtil";
import { eNodeTypeId } from "@common/utils/TsbConfig";
import { priSettingPermission } from "@common/utils/logicUtils";
import { globalEventBus } from "@common/utils/eventBus";
import { eCtxTypeId, eTreeOpType } from "@common/utils/TsbConfig";
import PageTitle from "@components/PageTitle";
import { Row, Space, Button } from "antd";
import { UnorderedListOutlined, MenuOutlined, AppstoreAddOutlined } from '@ant-design/icons';
import NoviceGuide from "@components/NoviceGuide"; // issue-新手引导
import { useQuery } from "@tanstack/react-query";
import { Layout, Spin } from "antd";
import { useEffect, useState, useRef } from "react";
import { Outlet, useAsyncValue, useLocation, useNavigate, useParams } from "react-router-dom";
import { useSearchQuery, useIssueSearchParams } from "src/issueTrack/service/issueSearchHooks";
import { useQueryIssue511_getPageById, useQuerySetting409_getTeamAttrgrpProps, useQueryTrack019GetPartitionDetail   } from "../../service/issueHooks";
import "./IssueHome.scss";
import IssueTopSearchBar from "./IssueTopSearchBar";
import { track010 } from '@common/utils/ApiPath';
import { useImmer } from "use-immer";
import { objNodeListMoreOps } from "@common/service/objNodeListMoreOps";
import ContextBoard from "@common/components/ContextBoard";
import { formatSvg, getIconValueByIdType, getViewModePath, getPropValueByIdType } from "src/issueTrack/utils/ArrayUtils";
import { isContain, scrollToAnchor } from "@common/utils/ViewUtils";
import { useQuerySetting234_getTeamMbrUserInfo } from "@common/service/commonHooks";
import { isEmpty } from '@common/utils/ArrayUtils';
import ScrollableHeader from "@components/ScrollableHeader";

export default function IssueHome() {
  const location = useLocation();
  //const navigate = useNavigate();
  const contextboardRef = useRef(null)
  const { teamId, nodeId: issueListNodeId } = useParams();
  const { setting320Result, issue506Result, userList = [], spaceUserList = [], allSpaceUserList = [], queryKeywords, objInfo, criteriaList: _searchQuery }
    = useAsyncValue(); //预加载获取的数据
  const { data: selectionList, isLoading: isLoadingCodeValueList, dataUpdatedAt:dataUpdatedAtSetting407, refetch: refechCodeValueList }
    = useQuerySetting407_getCodeValueList(teamId); //字典数据

  //自定义表单属性列表
  const { subclassAttrList, isLoading: isLoadingGetSubclassAttrs }
    = useQuerySetting409_getTeamAttrgrpProps(teamId, issueListNodeId, issue506Result?.subclassNid, !!issue506Result?.subclassNid);
  //获取项目详情
  const {data: projectInfo = {}, isLoading }
    = useQuery(track_002_get_issuegrp_info_query(teamId, issueListNodeId, issue506Result?.projectId, !!issue506Result?.projectId))

  const { data: { issueViewType } = {}, isFetching: isLoadingGetTeamMbrUserInfo }
    = useQuerySetting234_getTeamMbrUserInfo(teamId); // 团队个人基本信息

  const { attrList=[], criteriaList=[], bizNodeId, objType, createFlg, isLoading: isLoadingGetGetPartitionDetail,  } = useQueryTrack019GetPartitionDetail(teamId, issueListNodeId, undefined, objInfo.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp );

  // 分区受createFlg控制
  const finalCreateFlg = objInfo.nodeType != eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp || !!createFlg;

  const viewMode = getViewModePath(location.pathname); //视图回显
  
  // 权限相关
  const { data: { privSetting } } = useQuerySetting320GetNodePrivQuery({ teamId, nodeId: issueListNodeId });

  const _issueViewType = (viewMode == eIssueViewMode.ViewMode_1_ListDetail && !isEmpty(issueViewType)) ? issueViewType : viewMode;

  // console.log("viewMode", viewMode);
  // console.log("issueViewType", issueViewType);
  // console.log("_issueViewType", _issueViewType);

  // console.log("isLoadingCodeValueList", isLoadingCodeValueList);
 
  const [_issueList, setIssueList] = useImmer(null); //issue长短列表数据
  const [_totalCnt, setTotalCnt] = useState(0); //查询结果集总的记录数，分页加载数据
  const [iconSelectionLid, setIconSelectionLid] = useState(null); //icon列表id

  // 搜索条件相关
  const { issueNodeId, queryId, setIssueSearchParams } = useIssueSearchParams(); //issue路由配置，页面刷新
  let pageSize = ePagination.PageSize_30; //默认pageSize为30
  const {searchQuery, updateSearchQuery, loading } = useSearchQuery({ _searchQuery }); //查询条件列表
  // const [pageNo, setPageNo] = useState(1); //页码
  const [keywords, setKeywords] = useState(""); //关键字
  const [orderBy, setOrderBy] = useState(eConsoleNodeId.Nid_11118_Issue_CreateDate); //排序，创建时间
  const [ascendingFlg, setAscendingFlg] = useState(false); //false:倒序,true:正序 

  const [contextKey, setContextKey] = useState([]); // 右键菜单选中的key

  // TODO:当前issue过滤后会跳转至第一页，不能保留当前页码
  // 定位信息，查询条件及页码：不监听issueNodeId，只在初始化和路由跳转时调用（需要先找到当前路由的页数）
  const {data: pageNo = 1, isLoading: isLoadingGetPageById }
    = useQueryIssue511_getPageById(teamId, issueListNodeId, issueNodeId, searchQuery, pageSize, keywords, orderBy, ascendingFlg, !loading ); // 只在初始化时获取页码
  const [currentIssueIdx, setCurrentIssueIdx] = useState(0); // 用于切换页码时，加载数据前，先设置选中的issue索引,上一条为当前页的最后一条，下一条为当前页的第一条
  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);
  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState([]); // 选中节点

  // 无需依赖的，useEffect已做好依赖处理, 
  // 监听isFetchingIssueList，需要注意enabled不能依赖
  const { 
    data: {issueList, tableColumn, totalCnt} = {}, 
    isLoading: isLoadingIssueList, 
    dataUpdatedAt: dataUpdatedAtTrack008, 
    refetch:refetchIssueList 
  } = useQuery(track_008_get_issue_list_query(teamId, issueListNodeId, pageNo, pageSize, searchQuery, keywords, orderBy, ascendingFlg, !isLoadingGetPageById && !loading))

  // 监听issueNodeId
  useEffect(() => {
    if(!!issueNodeId){
      setSelectedKeys([issueNodeId?.toString()]);
    } else {
      setSelectedKeys([]);
    }
  }, [issueNodeId])

  // 监听queryKeywords
  useEffect(() => {
    setKeywords(queryKeywords)
  }, [queryKeywords])
  
  // 找到当前issue详情
  function findByIssueId(data, nodeId) {
    let nodeItem = data.find((el) => el.nodeId == nodeId)
    return nodeItem;
  }

  useEffect(() => {
    // undefined时，不执行, []执行
    if (dataUpdatedAtSetting407 && dataUpdatedAtTrack008 && issueList) {
       // 保存类别iconSelectionLid
       let iconSelectionLid;
       tableColumn.forEach(el => {
        if (el.attrNid == eConsoleNodeId.Nid_11109_Issue_Type) {
          iconSelectionLid = el.selectionLid
        }
      })
      let statusSelectionLid = tableColumn.find(column => column.attrNid == eConsoleNodeId.Nid_11110_Issue_Status)?.selectionLid;
      const processedIssueList = issueList.map(_issue => ({
        ..._issue,
        key: _issue.nodeId, //增加key属性，IssueLongList表格需要用key属性 
        anchorNodeId: issueListNodeId, // 项目nodeId
        realNodeId: _issue.nodeId,     // 对象nodeId，兼容快捷方式
        label: _issue.title,
        icon: formatSvg(getIconValueByIdType(selectionList, iconSelectionLid, _issue[eConsoleNodeId.Nid_11109_Issue_Type])), // 图标
        showMoreIcon: true, // 显示更多...
        statusCode: _issue[eConsoleNodeId.Nid_11110_Issue_Status], // 状态名称
        statusValue: getPropValueByIdType(selectionList, statusSelectionLid, _issue[eConsoleNodeId.Nid_11110_Issue_Status]) // 状态名称
      }));
      setIconSelectionLid(iconSelectionLid)
      setIssueList(processedIssueList)
      setTotalCnt(totalCnt)
    }
  }, [dataUpdatedAtSetting407, dataUpdatedAtTrack008, issueList]);

  //issue编号发生变化, 判定是否可以上一条和下一条
  //不能使用currentIssueIdx来判定，因为鼠标点击某一个issue时，currentIssueId发生变化，需要重新计算其对应的index
  useEffect(() => {
    if (_issueList  && !isLoadingGetTeamMbrUserInfo ) { // issueNodeId有值时需要等isLoadingGetPageById加载结束
      if(viewMode != eIssueViewMode.ViewMode_3_Kanban){
      // _issueList 无数据，则进行提示
      if (totalCnt == 0) {
        //globalUtil.info("暂无数据!"); tmsbug-8034 无需提示
        setIssueSearchParams({viewMode:_issueViewType, issueNodeId: null, queryId})
      } else {
        // issueList 有数据，则进行数据处理
        // 根据issueId 路由跳转issue详情
        if (issueNodeId) {
          // 查找列表中是否有issueId
          let foundNode = findByIssueId(_issueList, (issueNodeId))
          if (foundNode) {
            scrollToSelectedKey(issueNodeId);
            // tmsbug-7811:评论对应的“链接”点击或嵌入到富文本里，原本定位到评论的功能缺失 解决：加载记忆视图只需要在初始化时进行，路由中存在issueId,默认为非初始化，无需加载记忆视图
            // setIssueSearchParams({viewMode:_issueViewType, issueNodeId: issueNodeId, queryId})
            return;
          } 
        } 
        let _issueNodeId = _issueList[currentIssueIdx]?.nodeId;
        if(!_issueNodeId){
          if(currentIssueIdx-1 >= 0) { // 找不到则取上一个；
            _issueNodeId = _issueList[currentIssueIdx-1]?.nodeId
          } else {
            _issueNodeId = _issueList[0]?.nodeId || null; // 找不到上一个则默认选中第一个，全部没有则清空
          }
        }
        setIssueSearchParams({viewMode:_issueViewType, issueNodeId: _issueNodeId, queryId})
        scrollToSelectedKey(_issueNodeId);
      }
    }
  }
  }, [_issueList, isLoadingGetTeamMbrUserInfo])

  useEffect(() => {
    if( _issueList && issueNodeId){
      if(viewMode != eIssueViewMode.ViewMode_3_Kanban){
        let _idx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);
        setPreviousIssueLinkDisabledFlg(pageNo == 1 && _idx == 0);
        setNextIssueLinkDisabledFlg(pageNo == Math.ceil(totalCnt / pageSize) && _idx == _issueList?.length - 1);
      }
    } 
  }, [_issueList, issueNodeId])
  
  //  滚动至选中的节点
  const scrollToSelectedKey = (objNodeId) => {
    if (!!objNodeId) {
      setTimeout(() => {
        let target = document.querySelector(`div[data-content-key="${objNodeId}"]`); // 节点 兼容长短列表, 看板视图不支持
        if (!isContain(target)) {
          scrollToAnchor(target);
        }
      },100); //20250522 500ms -> 100ms
    }
  }

  // 处理定位图标点击事件
  const onPositioningClick = (nodeId) => {
    if (issueNodeId) {
      scrollToSelectedKey(issueNodeId);
    }
  }

  // 上一条
  function gotoPreviousIssue() {
    const currentIssueIdx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);
    if (currentIssueIdx == 0) {  //当前页的最后一行记录，再往前即需要向前翻页
      if (pageNo > 1) {
        // setPageNo(pageNo - 1);
        globalUtil.getQueryClient().setQueryData([track010, teamId, issueListNodeId], (pageNo)=>(pageNo - 1)); //触发 useQueryIssue017_getIssueList 再次加载
        setCurrentIssueIdx(ePagination.PageSize_30 - 1);
      }
    } else {
      setIssueSearchParams({viewMode:_issueViewType, issueNodeId: _issueList[currentIssueIdx - 1].nodeId});
    }
  }

  // 下一条
  function gotoNextIssue() {
    const currentIssueIdx = _issueList.findIndex(_issue => _issue.nodeId == issueNodeId);
    if (currentIssueIdx == pageSize - 1) { //当前页的最后一行记录，再往后即需要向后翻页
      if (pageNo < Math.ceil(_totalCnt / pageSize)) {
        // setPageNo(pageNo + 1);
        const queryClient = globalUtil.getQueryClient();
        queryClient.setQueryData([track010, teamId, issueListNodeId], (pageNo)=>(pageNo + 1)); //触发 useQueryIssue017_getIssueList 再次加载
        setCurrentIssueIdx(0);
      }
    } else {
      setIssueSearchParams({viewMode:_issueViewType, issueNodeId: _issueList[currentIssueIdx + 1].nodeId});
    }
  }

  // 响应右击菜单中的 二级菜单项，比如 偏好设置或创建快捷方式至
  const onMoreBtnClick = ({ nodeItem, ctxType, ...args }) => {
    //设置 图标颜色/文案颜色/快捷至桌面等 二级菜单项 点击响应
    objNodeListMoreOps({
      teamId,
      nodeType: eNodeTypeId.nt_31704_objtype_issue_item,
      objNode: {...nodeItem, key: nodeItem.nodeId },
      ctxType,
      setRootNode: setIssueList,
      setTotal: setTotalCnt,
      setCurrentIssueIdx: setCurrentIssueIdx,
      args,
    });
  }

  //参考 IssueItem.jsx 右击，回调到此函数
  const onShowMoreButtonClick = (info) => {
    console.log("onShowMoreButtonClick info", info)
    contextboardRef.current.showContextBoard(info.event.nativeEvent || info.event, {...info.node, nodeType: eNodeTypeId.nt_31704_objtype_issue_item}, info.node.nodeId, )
    setSelectedKeys([...selectedKeys, info.node.nodeId?.toString()]);// 右击菜单显示时，选中当前节点
    setContextKey(info.node.nodeId);
  }

   // react-contexify 隐藏事件，v6.0版本:废弃onHidden,改用onVisibilityChange属性，参考：https://github.com/fkhadra/react-contexify/pull/220
   function handleOnVisibilityChange(isVisible) {
    
    if(!isVisible && contextKey != issueNodeId){  // 右击菜单隐藏时，取消选中当前节点
      let _selectedKeys = selectedKeys.filter(selectedKey => selectedKey != contextKey);
      setSelectedKeys([..._selectedKeys]);
    }
  }

  // 新建采集口径设置
  const openCreateIssuePartitionEvent = (objInfo) => {
    globalEventBus.emit("openCreateIssuePartition", "", { ...objInfo });
  }

  // 显示视图模式
  const showViewMode = (viewMode) => {
    setIssueSearchParams({ viewMode, issueNodeId: null, queryId });
  }

  // Excel导出模态框状态
  const [excelExportModalVisibleFlg, setExcelExportModalVisibleFlg] = useState(false);

  if (isLoadingGetSubclassAttrs || isLoadingCodeValueList) {
    return
  }

  return <>

    {/* 头部搜索栏 */}
    <ScrollableHeader title={
      <Row justify="space-between" align="middle">
        <PageTitle teamId={teamId} nodeId={issueListNodeId} powerLock refetchData={refetchIssueList} onPositioningClick={onPositioningClick}/>
        <Space size={10}>
          {true ?
            <>
              {/* 如果有privWrite修改权限，显示设置  -- 23/1/5 xuying */}
              {objInfo.nodeType == eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp && priSettingPermission(privSetting) ?
                <a className='fontsize-12 fontcolor-normal' onClick={() => openCreateIssuePartitionEvent(objInfo)} >
                  <Space size={2}>
                    <span className="iconfont shezhixitongshezhigongnengshezhishuxing fontsize-12"></span>
                    <span>采集口径设置</span>
                  </Space>
                </a>
                : <></>
              }
              <a className='fontsize-12 fontcolor-normal' onClick={(e) => setExcelExportModalVisibleFlg(true)} >
                <Space size={2}>
                  <span className="iconfont Excelup fontsize-12"></span>
                  <span>Excel导出</span>
                </Space>
              </a>
              <Space className="issuehome-header-btns" size={2}>
                <Button size="middle" type={(viewMode != eIssueViewMode.ViewMode_2_ListOnly && viewMode != eIssueViewMode.ViewMode_3_Kanban) ? "primary" : ""}
                  title="短列表"
                  icon={<UnorderedListOutlined />}
                  onClick={() => showViewMode(eIssueViewMode.ViewMode_1_ListDetail)}
                />
                <Button size="middle" type={viewMode == eIssueViewMode.ViewMode_2_ListOnly ? "primary" : ""}
                  title="长列表"
                  icon={< MenuOutlined />}
                  onClick={() => { showViewMode(eIssueViewMode.ViewMode_2_ListOnly) }}
                />
                <Button size="middle" type={viewMode == eIssueViewMode.ViewMode_3_Kanban ? "primary" : ""}
                  title="看板"
                  icon={<AppstoreAddOutlined />}
                  onClick={() => {
                    showViewMode(eIssueViewMode.ViewMode_3_Kanban)
                  }}
                />
              </Space>
            </>
            : <></>
          }
        </Space>
      </Row>
    }>
      <IssueTopSearchBar
        subclassAttrList={subclassAttrList}
        subclassNid={issue506Result?.subclassNid}
        // userList={userList}
        allSpaceUserList={allSpaceUserList}
        spaceUserList={spaceUserList}
        selectionList={selectionList}
        objInfo={objInfo}
        projectId={issue506Result?.projectId}
        projectNodeId={issue506Result?.projectNodeId}
        showAllSet={true} //区分阶段和issue
        setting320Result={setting320Result}
        keywords={keywords}
        setKeywords={setKeywords}
        // setPageNo={setPageNo}
        // _criteriaList={_criteriaList}
        refechCodeValueList={refechCodeValueList}
        criteriaList={searchQuery}
        updateSearchQuery={updateSearchQuery}
        projectInfo={projectInfo}
        refetchIssueList={refetchIssueList}
      />
    </ScrollableHeader>
    {/* 短列表/长列表/看板 视图 */}
    <Layout style={{ flex: "auto", backgroundColor: "#fff", height: 0}}>
      <Spin spinning={isLoadingIssueList} wrapperClassName="issueSpin">
        <Outlet context={{
          issue506Result, subclassAttrList, userList, spaceUserList, selectionList, objInfo, issueList: _issueList, attrList: tableColumn, totalCnt: _totalCnt, pageNo, /* setPageNo,  */
          searchQuery,
          iconSelectionLid, setCurrentIssueIdx, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, keywords, gotoPreviousIssue, gotoNextIssue,
          ascendingFlg, setAscendingFlg, orderBy, setOrderBy, viewMode: _issueViewType, refetchIssueList, projectInfo, setting320Result,
          selectedKeys, onMoreBtnClick, onShowMoreButtonClick, onPositioningClick, finalCreateFlg
        }} />
      </Spin>
    </Layout>
     {/* 右击菜单 */}
     <ContextBoard
        ref={contextboardRef}
        teamId={teamId}
        onMoreBtnClick={onMoreBtnClick}
        // onCreateBtnClick={onCreateBtnClick} 
        id={"issue-context-menu"}
        handleOnVisibilityChange={handleOnVisibilityChange}
      />
    {/* 新手引导 */}
    <NoviceGuide nodeType={eNodeTypeId.nt_31705_objtype_issue_list} awakeFlg={0}/>
  
  </>;
}