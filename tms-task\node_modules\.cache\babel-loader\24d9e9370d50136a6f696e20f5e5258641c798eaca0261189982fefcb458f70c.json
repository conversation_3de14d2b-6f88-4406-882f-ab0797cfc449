{"ast": null, "code": "// 根据传入的property字段排序，默认升序\nexport const sortByProperty=function(a,b,property){let sortAsc=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;let value1=a[property];let value2=b[property];if(Object.prototype.toString.call(value1)===\"[object String]\"){// 字符串\nif(sortAsc){return value1.localeCompare(value2);}else{return value2.localeCompare(value1);}}else if(Object.prototype.toString.call(value1)===\"[object Number]\"){// 数值\nif(sortAsc){return value1-value2;}else{return value2-value1;}}else if(Object.prototype.toString.call(value1)===\"[object Array]\"){// 数组\nif(sortAsc){return(value1||[]).length-(value2||[]).length;}else{return(value2||[]).length-(value1||[]).length;}}};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}