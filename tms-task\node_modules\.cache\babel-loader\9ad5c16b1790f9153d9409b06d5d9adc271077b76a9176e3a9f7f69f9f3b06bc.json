{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { toUint8 } from '../../../base/common/uint.js';\n/**\n * A fast character classifier that uses a compact array for ASCII values.\n */\nexport class CharacterClassifier {\n  constructor(_defaultValue) {\n    const defaultValue = toUint8(_defaultValue);\n    this._defaultValue = defaultValue;\n    this._asciiMap = CharacterClassifier._createAsciiMap(defaultValue);\n    this._map = new Map();\n  }\n  static _createAsciiMap(defaultValue) {\n    const asciiMap = new Uint8Array(256);\n    for (let i = 0; i < 256; i++) {\n      asciiMap[i] = defaultValue;\n    }\n    return asciiMap;\n  }\n  set(charCode, _value) {\n    const value = toUint8(_value);\n    if (charCode >= 0 && charCode < 256) {\n      this._asciiMap[charCode] = value;\n    } else {\n      this._map.set(charCode, value);\n    }\n  }\n  get(charCode) {\n    if (charCode >= 0 && charCode < 256) {\n      return this._asciiMap[charCode];\n    } else {\n      return this._map.get(charCode) || this._defaultValue;\n    }\n  }\n}\nexport class CharacterSet {\n  constructor() {\n    this._actual = new CharacterClassifier(0 /* Boolean.False */);\n  }\n  add(charCode) {\n    this._actual.set(charCode, 1 /* Boolean.True */);\n  }\n  has(charCode) {\n    return this._actual.get(charCode) === 1 /* Boolean.True */;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}