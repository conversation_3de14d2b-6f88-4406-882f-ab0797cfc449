/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2025-01-21 11:34:18
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-08-05 14:22:17
 * @Description: 新建"事件推送"
 */
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { Button, Input, Drawer, Form, Checkbox, Avatar, Select, Space, Dropdown, Cascader, Modal } from "antd";
import DraggablePopUp from "@components/DraggablePopUp";
import React, { useEffect, useRef, useState, useMemo } from "react";
import { useParams } from "react-router-dom";
import '../DataPush.scss';
import * as httpQuickAccess from "@common/api/http";
import { globalUtil } from "@common/utils/globalUtil";
import { eNodeTypeId, eNodeType } from "@common/utils/TsbConfig";
import { NoAvatarIcon } from '@common/components/IconUtil';
import TextModelModal from './TextModel/TextModelModal';
import PlanModal from './Plan/PlanModal';
import SubscribeContent from './SubscribeContent/SubscribeContent';
import { ePriorityTypeObj, ePriorityType, eMsgChannel, eMsgChannelObj, eTriggerTime, eTriggerTimeObj, eExecTimeType, eEventParentType } from "src/quickAcess/utils/Config";
import { useQueryTeam599GetDataDictionary } from "src/quickAcess/service/quickHooks";
import { eSelectionListId } from "@common/utils/enum"
import {getEventParentType} from "@common/utils/logicUtils"
import Conditions from "./Conditions";
import TemplateDrawer from "./TemplateDrawer/TemplateDrawer"
import { globalEventBus } from "@common/utils/eventBus";
import FormEditor from "@components/FormEditor/FormEditor"
import DraggableDrawer from "@components/DraggableDrawer";

// 新建/编辑 事件推送
export default function OpDataPush({ allMember,opType,closeDrawer, setModalVisible,modalVisible,editData,isChange,setIsChange,selectedKey,typeList,fromType, ganttName, nodeType, }) {
  const { teamId,nodeId } = useParams();
  const [form] = Form.useForm();
  // Form.Item布局样式
  const formItemLayout = { labelCol: { span: 2 } };

  // Form.Item的样式
  const style = { style: {  height: "50%", marginBottom: 20 } };

  const [pushwhenEmpty,setPushwhenEmpty] = useState(false);
  const [subscribeData, setSubscribeData] = useState(null); // 先决条件
  const [description, setDescription] = useState(""); // 备注
  const [msgTplDetail, setMsgTplDetail] = useState(); // 模版

  const { data: { dataDictionaries=[], templateVariables=[] } = { dataDictionaries: [], templateVariables: [], workflowVariables: [] }, isLoading: isLoadingTeamXxx,refetch: refetchTeamXxx} = useQueryTeam599GetDataDictionary({teamId, selectionId: eSelectionListId.Selection_1970_datapush});

  useEffect(()=>{
    if(modalVisible){
      if(!!editData){
        form.setFieldsValue({
          subscrTitle: editData.subscrTitle||'',
          // toUids: (editData.toUids||'').split(',').filter(uid => !!uid),
          toUids: editData.toUids,
          ccUids: (editData.ccUids||'').split(',').filter(uid => !!uid),
          msgChannel: editData.msgChannel,
          priorityNo: editData.priorityNo,
          tplTitle: editData.tplTitle,
          // msgTpl: editData.msgTpl,
          msgTpl: editData.tplBody,
          attachmentFlg: !!editData.attachmentFlg,
          objRefFlg: !!editData.objRefFlg,
        });
        setDescription(editData.description||'');
        let obj = !!editData.objId ? {objId: editData.objId, objType: editData.objType, objName: editData.objName, objNodeId: editData.objNodeId } : null
        setSubscribeData(obj);
        setPushwhenEmpty(editData.pushWhenEmpty == 1);
        setMsgTplDetail({
          msgTplId: editData.msgTplId,
        });
      } else {
        form.resetFields();
      }
    }
  },[modalVisible]);

  useEffect(()=>{
    if(!isLoadingTeamXxx && !!editData){
      let { eventType } = editData
      if(!eventType){
        return ;
      }
      const eventTypeParentNode = dataDictionaries.find(data => data.children.some(item => item.code == eventType));
      const eventTypeNode = (eventTypeParentNode?.children || []).find(item => item.code == eventType);
      const eventTypeList = [eventTypeParentNode.code, eventTypeNode.code]
      form.setFieldValue("eventType", eventTypeList);
    }
  },[isLoadingTeamXxx, editData])

  function avatarFormat(src, name) {
    return (<Avatar style={{ marginRight: 5 }} src={src} icon={<NoAvatarIcon />} size={24} />);
  }

  function onFinish(opType){
    debugger
    let values = form.getFieldsValue(true);
    console.log("values", values);
    const { msgTpl, toUids, ccUids, priorityNo } = values;
    // if(opType == 0){
    //   if(!!subscribeData?.objId){
    //     if((msgTpl||'').indexOf('%订阅数据%') == -1){
    //      return Modal.confirm({
    //         title: "提示",
    //         icon: <ExclamationCircleOutlined />,
    //         content: <div style={{margin:'10px 0px',fontSize:12}}>
    //         <div>内容框中，未检测到有 %订阅数据% 占位符，若不含此占位符，订阅邮件中将不会有 搜索或报表结果。</div>
    //         <div>点击"继续保存"，表示您不需要包含订阅数据，仅以"内容"框中文案作为邮件主体内容；</div>
    //         <div>点击"重新修改"，停在当前界面，您可在"内容"框中某个位置，点击蓝色%订阅数据%插入占位符。</div>
    //       </div>,
    //         okText: "继续保存", 
    //         cancelText: "重新修改",
    //         onOk: () => {
    //           onFinish(1)
    //         },
    //         onCancel: () => {
    //           console.log("Cancel");
    //         },
    //       });
    //     }
    //   }
    // }
    let params = {
      teamId: teamId,
      subscrTitle: values.subscrTitle,
      msgChannel: values.msgChannel,
      objId: subscribeData?.objId,  // 先决条件保存的 搜索queryId 
      objType: subscribeData?.objType, // objType固定传 9
      objName: subscribeData?.objName,
      // msgTpl: values.msgTpl,
      msgTplId: msgTplDetail.tplId,
      // toUids: (toUids||[]).join(','),
      toUids:toUids,
      ccUids: (ccUids||[]).join(','),
      execTimeType: eExecTimeType.dataPush, 
      eventType: eventType,
      priorityNo: priorityNo,
      description: description,
      pushWhenEmpty: pushwhenEmpty ? 1 : 0,
      projectNodeId: nodeId,
      nodeType: parseInt(nodeType),
    }
    if(!!editData){
      params.nodeId = selectedKey
    }
    httpQuickAccess.team_523_save_sched_task_src(params).then(res => {
      if(res.resultCode == 200){
        closeDrawer(1);
      }
    });
  }

  function _onClose(){
    let values = form.getFieldsValue(true);
    closeDrawer(0,{
      values: values, 
      objId: subscribeData?.objId, 
      msgTpl: values.msgTpl||'', 
      // toUids: values.toUids||[],
      toUids: values.toUids,
      ccUids: values.ccUids||[],
      pushwhenEmpty: pushwhenEmpty ? 1 : 0,
    })
  }

  // 模版
  function onClickTemplateVariables (formKey, info) {
    let formValue = form.getFieldValue(formKey);
    if(formKey == "tplTitle"){
      form.setFieldValue(formKey, `${formValue??""}${info.key}`);
    } else if (formKey ==  "msgTpl"){
      form.setFieldValue(formKey, `${formValue??""}${info.key}`);
    } else {
      form.setFieldValue(formKey, [...(formValue??[]), info.key??""]);
    }
  }

  function onChangeRemark (e) {
   setDescription(e.target.value);
  }

  function treeFormat(list = []){
    return list.map(tree => {
      let _tree = {label: tree.value, value: tree.code}
      if((tree.children||[]).length > 0){
        _tree.children = treeFormat(tree.children)
      }
      return _tree
    })
  }

  const eventTypeList = Form.useWatch("eventType",form);
  const msgChannel = Form.useWatch("msgChannel",form);
  const eventType = eventTypeList ? eventTypeList[eventTypeList.length -1] : undefined;

  const eventParentType = useMemo(()=>{
     return getEventParentType(dataDictionaries, eventType);
  },[dataDictionaries, eventType])
  
  const dataDictionariesOptions = useMemo(() => {
    return treeFormat(dataDictionaries);
  }, [dataDictionaries]);

  const selectDataSource = () => {
    globalEventBus.emit("openTemplateDrawerEvent", "", { msgChannel, callback: selectDataSourceCallback});
  }

  function selectDataSourceCallback (msgChannel, checkMsgTpl) {
    console.log("checkMsgTpl", checkMsgTpl);
    if(!!checkMsgTpl){
      setMsgTplDetail(checkMsgTpl);
      form.setFieldsValue({
        msgChannel, // 系统推送通道
       "tplTitle": checkMsgTpl.tplTitle,
       "msgTpl": checkMsgTpl.tplBody,
       "attachmentFlg": !!checkMsgTpl.attachmentFlg, // 附件
       "objRefFlg": !!checkMsgTpl.objRefFlg,  // 对象关联
     });
    }
  }

  function onClickSubmit () {
    if(!msgTplDetail){
      return globalUtil.warning("请选择模版");
    }
    form.submit();
  }

  const selectTemplateView = (formKey) => {
    return <Dropdown
    menu={{
      items: templateVariables.map(obj => ({
        key: obj.value,
        value: obj.value,
        label: obj.value,
      })),
      onClick: (info)=>onClickTemplateVariables(formKey, info)
    }}
    placement="bottom"
    arrow={{
      pointAtCenter: true,
    }}
  >
  <Button type='link'>选择模版变量</Button>
  </Dropdown>
  }

  return (
    <DraggableDrawer
      className="tms-drawer"
      title={`${opType == 0 ? "新建" : '编辑'}${fromType == 'gantt' ? '进度推送订阅' : '事件推送'}`}
      width={'70%'}
      onClose={() => _onClose()}
      open={modalVisible}
      destroyOnClose={true} //关闭时销毁子元素,避免重新打开数据不会刷新
      preserve={false}
      closable
      centered
      footer={
        <div style={{display:'flex',alignItems:'center',justifyContent:'end'}}>
          <Space size={20}>
          {!!subscribeData &&
          <Checkbox
            className="subscribe-way-mark-check"
            onChange={(e)=> setPushwhenEmpty(e.target.checked)} 
            checked={pushwhenEmpty}
          >
            订阅数据为空时，依然推送。
          </Checkbox>
          }
          <Input value={description} placeholder="请输入备注" style={{ borderRadius: 5, width:300 }} autoComplete="off" onChange={onChangeRemark}></Input>
          <Button style={{borderRadius:5}} type={'primary'} onClick={()=>{onClickSubmit()}}>提交</Button>
          </Space>
        </div>
      }
    >
      <Form
        className="subscribe-way"
        form={form}
        {...formItemLayout}
        labelAlign="right"
        onFinish={()=>onFinish(0)}
        initialValues={{
          msgChannel: eMsgChannel.mail, // 默认邮件
          triggerTime: eTriggerTime.immediately, // 默认即时触发
        }}
      >
        <div className="label-header">推送规则</div>
        {/* 订阅名称 */}
        <Form.Item label="名称" rules={[{required: true, message: '名称不能为空'}]} {...style} name='subscrTitle'>
          <Input style={{ width: 300,  borderRadius: 5 }} autoComplete="off"/>
        </Form.Item>
        <Form.Item label="选择事件" rules={[{required: true, message: '事件不能为空'}]}  {...style} name="eventType">
          <Cascader
            style={{ width: 300, borderRadius: 3 }}
            // changeOnSelect
            allowClear
            popupClassName="tms-cascader-popup"
            expandTrigger="hover"
            options={dataDictionariesOptions}
            placeholder={`请选择事件`}
            displayRender={(label, selectedOptions) => label.join("-") }
            showSearch={{
              filter: (inputValue, path) =>path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1),
            }}
          />
        </Form.Item>
        {
          (eventParentType == eEventParentType.businessOp || eventParentType == eEventParentType.electronStream) && 
          <Form.Item label="先决条件" {...style} >
            <Conditions subscribeData={subscribeData} setSubscribeData={setSubscribeData} eventParentType={eventParentType}/>
          </Form.Item>
        }
        <Form.Item label="触发时机" {...style} name="triggerTime">
          <Select
            showSearch
            allowClear
            style={{ width: 300 }}
            placeholder='请选择触发时机'
            disabled={true}
            options={[
              eTriggerTimeObj[eTriggerTime.immediately],
            ]}
          />
        </Form.Item>
        <Form.Item label="优先级" {...style} name="priorityNo">
          <Select
            showSearch
            allowClear
            style={{ width: 300 }}
            placeholder='请选择优先级'
            options={[
              ePriorityTypeObj[ePriorityType.high],
              ePriorityTypeObj[ePriorityType.middle],
              ePriorityTypeObj[ePriorityType.low],
            ]}
          />
        </Form.Item>
         <Form.Item label='系统推送通道' {...style} name="msgChannel">
           <Select
            // disabled={true}
            showSearch
            allowClear
            style={{ width: 300 }}
            placeholder='请选择'
            options={[
              eMsgChannelObj[eMsgChannel.mail],
              eMsgChannelObj[eMsgChannel.msg],
              eMsgChannelObj[eMsgChannel.voice],
              eMsgChannelObj[eMsgChannel.wechat],
              eMsgChannelObj[eMsgChannel.inSiteMsg],
            ]}
          />
        </Form.Item>
        <div className="label-header">推送内容</div>
        {/* 收件人 */}
        <Form.Item label="收件人" required {...style}>
           <Form.Item name="toUids" rules={[{required: true, message: '收件人不能为空'}]} noStyle>
             <Select
                showSearch
                allowClear
                // mode='multiple' fix tmsbug-11663:系统订阅，收件人，期望从多选改为单选
                placeholder="请选择收件人"
                dropdownMatchSelectWidth={false}
                style={{ borderRadius: 5, width:300 }}
                filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                options={allMember.map(user => ({
                  key: user.key.toString(),
                  value: user.key.toString(),
                  icon: avatarFormat(user.avatar, user.label),
                  label: <div style={{ display: 'flex', alignItems: 'center' }}>{avatarFormat(user.avatar, user.label)}{user.label}</div>,
                  name: user.label,
                })).concat(templateVariables.map(obj => ({
                    key: obj.value,
                    value: obj.value,
                    label: obj.value,
                    name: obj.value,
                  })))}
              />
           </Form.Item>
          {selectTemplateView("toUids")}
        </Form.Item>
        {/* 抄送人 */}
        <Form.Item label="抄送人" {...style}>
          <Form.Item name="ccUids" noStyle>
            <Select
              showSearch
              mode="multiple"
              allowClear
              placeholder="请选择抄送人"
              style={{ borderRadius: 5, width:300 }}
              filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
              options={allMember.map(user => ({
                key: user.key.toString(),
                value: user.key.toString(),
                label: <div style={{ display: 'flex', alignItems: 'center' }}>{avatarFormat(user.avatar, user.label)}{user.label}</div>,
                name: user.label,
              })).concat(templateVariables.map(obj => ({
                key: obj.value,
                value: obj.value,
                label: obj.value,
                name: obj.value,
              })))}
            />
           </Form.Item>
           {selectTemplateView("ccUids")}
        </Form.Item>
        <Form.Item label="主题"  {...style}>
            <Form.Item name="tplTitle" noStyle>
              <Input style={{ borderRadius: 5, width:300 }} autoComplete="off" disabled={true}/>
            </Form.Item>
            <Button type="link" onClick={selectDataSource}>选择模版</Button>
        </Form.Item>
        {/* 订阅数据源 */}
        {/* <Form.Item label="订阅数据源" {...style}>
          <div style={{display:'flex',alignItems:'center'}}>
            <Select
              style={{ width: 300 }}
              open={false}
              placeholder='请选择'
              value={subscribeData?.objName}
              suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
              onChange={(value)=>_onChange(value,0)}
              allowClear
              onClick={() => setSubscribeDataVisible(true)} />
            <div style={{color:'#999',marginLeft:10}}>备注：订阅邮件中将呈现的搜索结果或报表</div>
          </div>
        </Form.Item> */}
        <Form.Item label="正文" {...style}>
          <div style={{marginTop:5}}>
            <Form.Item name="msgTpl" noStyle>
              <FormEditor
                disabled={true}
                placeholder={""}
                editorProps = {
                  {
                    uploadParams : {
                      teamId: teamId,
                      nodeId: nodeId,
                      moduleName: eNodeType[nodeType]?.nameEn,
                      objType: nodeType
                    },
                    heightMin:'200px',
                    autofocus:false,
                  }
                }/>
             </Form.Item>
            {/* <div style={{color:'#999'}}>提示：占位符 <a onClick={()=>editRef.current.insert('%订阅数据%')}>%订阅数据%</a>，代表需要订阅的搜索结果或报表数据，请点击占位符，将其包含进正文。</div> */}
          </div>
        </Form.Item>
        {
          (eventParentType == eEventParentType.businessOp || eventParentType == eEventParentType.electronStream) && <>
            <Form.Item label="附件" {...style} name="attachmentFlg" valuePropName="checked">
               <Checkbox  disabled={true}/>
            </Form.Item>
            <Form.Item label="对象关联"  {...style} name="objRefFlg" valuePropName="checked" >
               <Checkbox  disabled={true}/>
            </Form.Item>
          </>
        }
        {/* 备注 */}
        {/* <Form.Item label="备注" {...style} name='description'>
          <TextArea style={{ minHeight: 200, borderRadius: 5 }} autoComplete="off"/>
        </Form.Item> */}
      </Form>
      {/* 模版列表 */}
      <TemplateDrawer />
      <DraggablePopUp
        className="tms-modal"
        title='提示'
        centered
        width={300}
        open={isChange}
        onCancel={()=>setIsChange(false)}
        onOk={()=>{
          setIsChange(false)
          setModalVisible(false)
        }}
      >
        <div style={{textAlign:'center',margin:'10px 0px'}}>
          您正在编辑"事件推送"，确定放弃编辑？
        </div>
      </DraggablePopUp>
    </DraggableDrawer>
  );
}
