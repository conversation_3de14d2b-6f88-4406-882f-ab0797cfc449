{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter, Event } from './event.js';\nconst shortcutEvent = Object.freeze(function (callback, context) {\n  const handle = setTimeout(callback.bind(context), 0);\n  return {\n    dispose() {\n      clearTimeout(handle);\n    }\n  };\n});\nexport var CancellationToken;\n(function (CancellationToken) {\n  function isCancellationToken(thing) {\n    if (thing === CancellationToken.None || thing === CancellationToken.Cancelled) {\n      return true;\n    }\n    if (thing instanceof MutableToken) {\n      return true;\n    }\n    if (!thing || typeof thing !== 'object') {\n      return false;\n    }\n    return typeof thing.isCancellationRequested === 'boolean' && typeof thing.onCancellationRequested === 'function';\n  }\n  CancellationToken.isCancellationToken = isCancellationToken;\n  CancellationToken.None = Object.freeze({\n    isCancellationRequested: false,\n    onCancellationRequested: Event.None\n  });\n  CancellationToken.Cancelled = Object.freeze({\n    isCancellationRequested: true,\n    onCancellationRequested: shortcutEvent\n  });\n})(CancellationToken || (CancellationToken = {}));\nclass MutableToken {\n  constructor() {\n    this._isCancelled = false;\n    this._emitter = null;\n  }\n  cancel() {\n    if (!this._isCancelled) {\n      this._isCancelled = true;\n      if (this._emitter) {\n        this._emitter.fire(undefined);\n        this.dispose();\n      }\n    }\n  }\n  get isCancellationRequested() {\n    return this._isCancelled;\n  }\n  get onCancellationRequested() {\n    if (this._isCancelled) {\n      return shortcutEvent;\n    }\n    if (!this._emitter) {\n      this._emitter = new Emitter();\n    }\n    return this._emitter.event;\n  }\n  dispose() {\n    if (this._emitter) {\n      this._emitter.dispose();\n      this._emitter = null;\n    }\n  }\n}\nexport class CancellationTokenSource {\n  constructor(parent) {\n    this._token = undefined;\n    this._parentListener = undefined;\n    this._parentListener = parent && parent.onCancellationRequested(this.cancel, this);\n  }\n  get token() {\n    if (!this._token) {\n      // be lazy and create the token only when\n      // actually needed\n      this._token = new MutableToken();\n    }\n    return this._token;\n  }\n  cancel() {\n    if (!this._token) {\n      // save an object by returning the default\n      // cancelled token when cancellation happens\n      // before someone asks for the token\n      this._token = CancellationToken.Cancelled;\n    } else if (this._token instanceof MutableToken) {\n      // actually cancel\n      this._token.cancel();\n    }\n  }\n  dispose() {\n    let cancel = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (cancel) {\n      this.cancel();\n    }\n    if (this._parentListener) {\n      this._parentListener.dispose();\n    }\n    if (!this._token) {\n      // ensure to initialize with an empty token if we had none\n      this._token = CancellationToken.None;\n    } else if (this._token instanceof MutableToken) {\n      // actually dispose\n      this._token.dispose();\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}