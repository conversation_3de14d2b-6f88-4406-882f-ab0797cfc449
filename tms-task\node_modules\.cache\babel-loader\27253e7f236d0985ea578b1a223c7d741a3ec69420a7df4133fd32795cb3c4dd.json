{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Position } from './position.js';\n/**\n * A range in the editor. (startLineNumber,startColumn) is <= (endLineNumber,endColumn)\n */\nexport class Range {\n  constructor(startLineNumber, startColumn, endLineNumber, endColumn) {\n    if (startLineNumber > endLineNumber || startLineNumber === endLineNumber && startColumn > endColumn) {\n      this.startLineNumber = endLineNumber;\n      this.startColumn = endColumn;\n      this.endLineNumber = startLineNumber;\n      this.endColumn = startColumn;\n    } else {\n      this.startLineNumber = startLineNumber;\n      this.startColumn = startColumn;\n      this.endLineNumber = endLineNumber;\n      this.endColumn = endColumn;\n    }\n  }\n  /**\n   * Test if this range is empty.\n   */\n  isEmpty() {\n    return Range.isEmpty(this);\n  }\n  /**\n   * Test if `range` is empty.\n   */\n  static isEmpty(range) {\n    return range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn;\n  }\n  /**\n   * Test if position is in this range. If the position is at the edges, will return true.\n   */\n  containsPosition(position) {\n    return Range.containsPosition(this, position);\n  }\n  /**\n   * Test if `position` is in `range`. If the position is at the edges, will return true.\n   */\n  static containsPosition(range, position) {\n    if (position.lineNumber < range.startLineNumber || position.lineNumber > range.endLineNumber) {\n      return false;\n    }\n    if (position.lineNumber === range.startLineNumber && position.column < range.startColumn) {\n      return false;\n    }\n    if (position.lineNumber === range.endLineNumber && position.column > range.endColumn) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Test if `position` is in `range`. If the position is at the edges, will return false.\n   * @internal\n   */\n  static strictContainsPosition(range, position) {\n    if (position.lineNumber < range.startLineNumber || position.lineNumber > range.endLineNumber) {\n      return false;\n    }\n    if (position.lineNumber === range.startLineNumber && position.column <= range.startColumn) {\n      return false;\n    }\n    if (position.lineNumber === range.endLineNumber && position.column >= range.endColumn) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Test if range is in this range. If the range is equal to this range, will return true.\n   */\n  containsRange(range) {\n    return Range.containsRange(this, range);\n  }\n  /**\n   * Test if `otherRange` is in `range`. If the ranges are equal, will return true.\n   */\n  static containsRange(range, otherRange) {\n    if (otherRange.startLineNumber < range.startLineNumber || otherRange.endLineNumber < range.startLineNumber) {\n      return false;\n    }\n    if (otherRange.startLineNumber > range.endLineNumber || otherRange.endLineNumber > range.endLineNumber) {\n      return false;\n    }\n    if (otherRange.startLineNumber === range.startLineNumber && otherRange.startColumn < range.startColumn) {\n      return false;\n    }\n    if (otherRange.endLineNumber === range.endLineNumber && otherRange.endColumn > range.endColumn) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Test if `range` is strictly in this range. `range` must start after and end before this range for the result to be true.\n   */\n  strictContainsRange(range) {\n    return Range.strictContainsRange(this, range);\n  }\n  /**\n   * Test if `otherRange` is strictly in `range` (must start after, and end before). If the ranges are equal, will return false.\n   */\n  static strictContainsRange(range, otherRange) {\n    if (otherRange.startLineNumber < range.startLineNumber || otherRange.endLineNumber < range.startLineNumber) {\n      return false;\n    }\n    if (otherRange.startLineNumber > range.endLineNumber || otherRange.endLineNumber > range.endLineNumber) {\n      return false;\n    }\n    if (otherRange.startLineNumber === range.startLineNumber && otherRange.startColumn <= range.startColumn) {\n      return false;\n    }\n    if (otherRange.endLineNumber === range.endLineNumber && otherRange.endColumn >= range.endColumn) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * A reunion of the two ranges.\n   * The smallest position will be used as the start point, and the largest one as the end point.\n   */\n  plusRange(range) {\n    return Range.plusRange(this, range);\n  }\n  /**\n   * A reunion of the two ranges.\n   * The smallest position will be used as the start point, and the largest one as the end point.\n   */\n  static plusRange(a, b) {\n    let startLineNumber;\n    let startColumn;\n    let endLineNumber;\n    let endColumn;\n    if (b.startLineNumber < a.startLineNumber) {\n      startLineNumber = b.startLineNumber;\n      startColumn = b.startColumn;\n    } else if (b.startLineNumber === a.startLineNumber) {\n      startLineNumber = b.startLineNumber;\n      startColumn = Math.min(b.startColumn, a.startColumn);\n    } else {\n      startLineNumber = a.startLineNumber;\n      startColumn = a.startColumn;\n    }\n    if (b.endLineNumber > a.endLineNumber) {\n      endLineNumber = b.endLineNumber;\n      endColumn = b.endColumn;\n    } else if (b.endLineNumber === a.endLineNumber) {\n      endLineNumber = b.endLineNumber;\n      endColumn = Math.max(b.endColumn, a.endColumn);\n    } else {\n      endLineNumber = a.endLineNumber;\n      endColumn = a.endColumn;\n    }\n    return new Range(startLineNumber, startColumn, endLineNumber, endColumn);\n  }\n  /**\n   * A intersection of the two ranges.\n   */\n  intersectRanges(range) {\n    return Range.intersectRanges(this, range);\n  }\n  /**\n   * A intersection of the two ranges.\n   */\n  static intersectRanges(a, b) {\n    let resultStartLineNumber = a.startLineNumber;\n    let resultStartColumn = a.startColumn;\n    let resultEndLineNumber = a.endLineNumber;\n    let resultEndColumn = a.endColumn;\n    const otherStartLineNumber = b.startLineNumber;\n    const otherStartColumn = b.startColumn;\n    const otherEndLineNumber = b.endLineNumber;\n    const otherEndColumn = b.endColumn;\n    if (resultStartLineNumber < otherStartLineNumber) {\n      resultStartLineNumber = otherStartLineNumber;\n      resultStartColumn = otherStartColumn;\n    } else if (resultStartLineNumber === otherStartLineNumber) {\n      resultStartColumn = Math.max(resultStartColumn, otherStartColumn);\n    }\n    if (resultEndLineNumber > otherEndLineNumber) {\n      resultEndLineNumber = otherEndLineNumber;\n      resultEndColumn = otherEndColumn;\n    } else if (resultEndLineNumber === otherEndLineNumber) {\n      resultEndColumn = Math.min(resultEndColumn, otherEndColumn);\n    }\n    // Check if selection is now empty\n    if (resultStartLineNumber > resultEndLineNumber) {\n      return null;\n    }\n    if (resultStartLineNumber === resultEndLineNumber && resultStartColumn > resultEndColumn) {\n      return null;\n    }\n    return new Range(resultStartLineNumber, resultStartColumn, resultEndLineNumber, resultEndColumn);\n  }\n  /**\n   * Test if this range equals other.\n   */\n  equalsRange(other) {\n    return Range.equalsRange(this, other);\n  }\n  /**\n   * Test if range `a` equals `b`.\n   */\n  static equalsRange(a, b) {\n    return !!a && !!b && a.startLineNumber === b.startLineNumber && a.startColumn === b.startColumn && a.endLineNumber === b.endLineNumber && a.endColumn === b.endColumn;\n  }\n  /**\n   * Return the end position (which will be after or equal to the start position)\n   */\n  getEndPosition() {\n    return Range.getEndPosition(this);\n  }\n  /**\n   * Return the end position (which will be after or equal to the start position)\n   */\n  static getEndPosition(range) {\n    return new Position(range.endLineNumber, range.endColumn);\n  }\n  /**\n   * Return the start position (which will be before or equal to the end position)\n   */\n  getStartPosition() {\n    return Range.getStartPosition(this);\n  }\n  /**\n   * Return the start position (which will be before or equal to the end position)\n   */\n  static getStartPosition(range) {\n    return new Position(range.startLineNumber, range.startColumn);\n  }\n  /**\n   * Transform to a user presentable string representation.\n   */\n  toString() {\n    return '[' + this.startLineNumber + ',' + this.startColumn + ' -> ' + this.endLineNumber + ',' + this.endColumn + ']';\n  }\n  /**\n   * Create a new range using this range's start position, and using endLineNumber and endColumn as the end position.\n   */\n  setEndPosition(endLineNumber, endColumn) {\n    return new Range(this.startLineNumber, this.startColumn, endLineNumber, endColumn);\n  }\n  /**\n   * Create a new range using this range's end position, and using startLineNumber and startColumn as the start position.\n   */\n  setStartPosition(startLineNumber, startColumn) {\n    return new Range(startLineNumber, startColumn, this.endLineNumber, this.endColumn);\n  }\n  /**\n   * Create a new empty range using this range's start position.\n   */\n  collapseToStart() {\n    return Range.collapseToStart(this);\n  }\n  /**\n   * Create a new empty range using this range's start position.\n   */\n  static collapseToStart(range) {\n    return new Range(range.startLineNumber, range.startColumn, range.startLineNumber, range.startColumn);\n  }\n  // ---\n  static fromPositions(start) {\n    let end = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : start;\n    return new Range(start.lineNumber, start.column, end.lineNumber, end.column);\n  }\n  static lift(range) {\n    if (!range) {\n      return null;\n    }\n    return new Range(range.startLineNumber, range.startColumn, range.endLineNumber, range.endColumn);\n  }\n  /**\n   * Test if `obj` is an `IRange`.\n   */\n  static isIRange(obj) {\n    return obj && typeof obj.startLineNumber === 'number' && typeof obj.startColumn === 'number' && typeof obj.endLineNumber === 'number' && typeof obj.endColumn === 'number';\n  }\n  /**\n   * Test if the two ranges are touching in any way.\n   */\n  static areIntersectingOrTouching(a, b) {\n    // Check if `a` is before `b`\n    if (a.endLineNumber < b.startLineNumber || a.endLineNumber === b.startLineNumber && a.endColumn < b.startColumn) {\n      return false;\n    }\n    // Check if `b` is before `a`\n    if (b.endLineNumber < a.startLineNumber || b.endLineNumber === a.startLineNumber && b.endColumn < a.startColumn) {\n      return false;\n    }\n    // These ranges must intersect\n    return true;\n  }\n  /**\n   * Test if the two ranges are intersecting. If the ranges are touching it returns true.\n   */\n  static areIntersecting(a, b) {\n    // Check if `a` is before `b`\n    if (a.endLineNumber < b.startLineNumber || a.endLineNumber === b.startLineNumber && a.endColumn <= b.startColumn) {\n      return false;\n    }\n    // Check if `b` is before `a`\n    if (b.endLineNumber < a.startLineNumber || b.endLineNumber === a.startLineNumber && b.endColumn <= a.startColumn) {\n      return false;\n    }\n    // These ranges must intersect\n    return true;\n  }\n  /**\n   * A function that compares ranges, useful for sorting ranges\n   * It will first compare ranges on the startPosition and then on the endPosition\n   */\n  static compareRangesUsingStarts(a, b) {\n    if (a && b) {\n      const aStartLineNumber = a.startLineNumber | 0;\n      const bStartLineNumber = b.startLineNumber | 0;\n      if (aStartLineNumber === bStartLineNumber) {\n        const aStartColumn = a.startColumn | 0;\n        const bStartColumn = b.startColumn | 0;\n        if (aStartColumn === bStartColumn) {\n          const aEndLineNumber = a.endLineNumber | 0;\n          const bEndLineNumber = b.endLineNumber | 0;\n          if (aEndLineNumber === bEndLineNumber) {\n            const aEndColumn = a.endColumn | 0;\n            const bEndColumn = b.endColumn | 0;\n            return aEndColumn - bEndColumn;\n          }\n          return aEndLineNumber - bEndLineNumber;\n        }\n        return aStartColumn - bStartColumn;\n      }\n      return aStartLineNumber - bStartLineNumber;\n    }\n    const aExists = a ? 1 : 0;\n    const bExists = b ? 1 : 0;\n    return aExists - bExists;\n  }\n  /**\n   * A function that compares ranges, useful for sorting ranges\n   * It will first compare ranges on the endPosition and then on the startPosition\n   */\n  static compareRangesUsingEnds(a, b) {\n    if (a.endLineNumber === b.endLineNumber) {\n      if (a.endColumn === b.endColumn) {\n        if (a.startLineNumber === b.startLineNumber) {\n          return a.startColumn - b.startColumn;\n        }\n        return a.startLineNumber - b.startLineNumber;\n      }\n      return a.endColumn - b.endColumn;\n    }\n    return a.endLineNumber - b.endLineNumber;\n  }\n  /**\n   * Test if the range spans multiple lines.\n   */\n  static spansMultipleLines(range) {\n    return range.endLineNumber > range.startLineNumber;\n  }\n  toJSON() {\n    return this;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}