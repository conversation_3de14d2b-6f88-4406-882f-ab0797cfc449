{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * A position in the editor.\n */\nexport class Position {\n  constructor(lineNumber, column) {\n    this.lineNumber = lineNumber;\n    this.column = column;\n  }\n  /**\n   * Create a new position from this position.\n   *\n   * @param newLineNumber new line number\n   * @param newColumn new column\n   */\n  with() {\n    let newLineNumber = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.lineNumber;\n    let newColumn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.column;\n    if (newLineNumber === this.lineNumber && newColumn === this.column) {\n      return this;\n    } else {\n      return new Position(newLineNumber, newColumn);\n    }\n  }\n  /**\n   * Derive a new position from this position.\n   *\n   * @param deltaLineNumber line number delta\n   * @param deltaColumn column delta\n   */\n  delta() {\n    let deltaLineNumber = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    let deltaColumn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    return this.with(this.lineNumber + deltaLineNumber, this.column + deltaColumn);\n  }\n  /**\n   * Test if this position equals other position\n   */\n  equals(other) {\n    return Position.equals(this, other);\n  }\n  /**\n   * Test if position `a` equals position `b`\n   */\n  static equals(a, b) {\n    if (!a && !b) {\n      return true;\n    }\n    return !!a && !!b && a.lineNumber === b.lineNumber && a.column === b.column;\n  }\n  /**\n   * Test if this position is before other position.\n   * If the two positions are equal, the result will be false.\n   */\n  isBefore(other) {\n    return Position.isBefore(this, other);\n  }\n  /**\n   * Test if position `a` is before position `b`.\n   * If the two positions are equal, the result will be false.\n   */\n  static isBefore(a, b) {\n    if (a.lineNumber < b.lineNumber) {\n      return true;\n    }\n    if (b.lineNumber < a.lineNumber) {\n      return false;\n    }\n    return a.column < b.column;\n  }\n  /**\n   * Test if this position is before other position.\n   * If the two positions are equal, the result will be true.\n   */\n  isBeforeOrEqual(other) {\n    return Position.isBeforeOrEqual(this, other);\n  }\n  /**\n   * Test if position `a` is before position `b`.\n   * If the two positions are equal, the result will be true.\n   */\n  static isBeforeOrEqual(a, b) {\n    if (a.lineNumber < b.lineNumber) {\n      return true;\n    }\n    if (b.lineNumber < a.lineNumber) {\n      return false;\n    }\n    return a.column <= b.column;\n  }\n  /**\n   * A function that compares positions, useful for sorting\n   */\n  static compare(a, b) {\n    const aLineNumber = a.lineNumber | 0;\n    const bLineNumber = b.lineNumber | 0;\n    if (aLineNumber === bLineNumber) {\n      const aColumn = a.column | 0;\n      const bColumn = b.column | 0;\n      return aColumn - bColumn;\n    }\n    return aLineNumber - bLineNumber;\n  }\n  /**\n   * Clone this position.\n   */\n  clone() {\n    return new Position(this.lineNumber, this.column);\n  }\n  /**\n   * Convert to a human-readable representation.\n   */\n  toString() {\n    return '(' + this.lineNumber + ',' + this.column + ')';\n  }\n  // ---\n  /**\n   * Create a `Position` from an `IPosition`.\n   */\n  static lift(pos) {\n    return new Position(pos.lineNumber, pos.column);\n  }\n  /**\n   * Test if `obj` is an `IPosition`.\n   */\n  static isIPosition(obj) {\n    return obj && typeof obj.lineNumber === 'number' && typeof obj.column === 'number';\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}