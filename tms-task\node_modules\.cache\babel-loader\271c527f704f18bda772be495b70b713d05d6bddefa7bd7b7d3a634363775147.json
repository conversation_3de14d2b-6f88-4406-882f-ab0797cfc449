{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class Lazy {\n  constructor(executor) {\n    this.executor = executor;\n    this._didRun = false;\n  }\n  /**\n   * True if the lazy value has been resolved.\n   */\n  hasValue() {\n    return this._didRun;\n  }\n  /**\n   * Get the wrapped value.\n   *\n   * This will force evaluation of the lazy value if it has not been resolved yet. Lazy values are only\n   * resolved once. `getValue` will re-throw exceptions that are hit while resolving the value\n   */\n  getValue() {\n    if (!this._didRun) {\n      try {\n        this._value = this.executor();\n      } catch (err) {\n        this._error = err;\n      } finally {\n        this._didRun = true;\n      }\n    }\n    if (this._error) {\n      throw this._error;\n    }\n    return this._value;\n  }\n  /**\n   * Get the wrapped value without forcing evaluation.\n   */\n  get rawValue() {\n    return this._value;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}