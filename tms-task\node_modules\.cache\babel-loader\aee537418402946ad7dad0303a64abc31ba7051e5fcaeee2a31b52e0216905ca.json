{"ast": null, "code": "import{Button,Image,Modal,Space}from\"antd\";import{useParams}from\"react-router-dom\";import{useMutation,useQueryClient}from\"@tanstack/react-query\";import*as https from\"../api/http\";import{globalUtil}from\"@common/utils/globalUtil\";import{setting_320_get_node_priv_query}from\"@common/api/query/query\";import{useEffect,useState}from\"react\";import{globalEventBus}from\"@common/utils/eventBus\";/**\r\n * \r\n * @param {*} code 401：应用已过期-非管理员 402：应用已过期-管理员 403：用户未被授权应用 404：管理员未被授权应用\r\n * @returns \r\n */import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";export default function ProductError(_ref){let{code}=_ref;const queryClient=useQueryClient();const{teamId,nodeId,nid}=useParams();const[productId,setProductId]=useState(0);const setting334Mutation=useMutation({mutationFn:https.setting_334_apply_authorization});useEffect(()=>{var _setting320Result$noA;let setting320Query=setting_320_get_node_priv_query(teamId,nid||nodeId);let setting320Result=queryClient.getQueryData(setting320Query.queryKey);setProductId(setting320Result===null||setting320Result===void 0?void 0:(_setting320Result$noA=setting320Result.noAuth)===null||_setting320Result$noA===void 0?void 0:_setting320Result$noA.productId);},[]);const sendApply=()=>{if(!!productId){setting334Mutation.mutate({teamId,productId},{onSuccess:result=>{if(result.resultCode===200){//globalUtil.success(\"提交申请成功！\")\nModal.info({title:\"提示\",content:\"提交成功，管理员会接收到申请信息，请您耐心等候\",maskClosable:true,//centered: true, // 居中\nokText:\"我知道了\",width:500});}}});}};const openSettingsDrawer=function(){let tab=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'product';globalEventBus.emit(\"openSettingsDrawerEvent\",null,{teamId,defaultTab:tab,productId});};const code401=/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"14px\",color:\"#333\"},children:\"\\u5E94\\u7528\\u5DF2\\u8FC7\\u671F\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"14px\",color:\"#333\"},children:\"\\u8BF7\\u8054\\u7CFB\\u56E2\\u961F\\u7BA1\\u7406\\u5458\\u7EED\\u8D39\"})]});const code402=/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"14px\",color:\"#333\"},children:\"\\u5E94\\u7528\\u5DF2\\u8FC7\\u671F\\uFF0C\\u8BF7\\u8D2D\\u4E70\\u6216\\u7EED\\u8D39\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>openSettingsDrawer('product'),children:\"\\u53BB\\u7EED\\u8D39\"})]});const code403=/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"14px\",color:\"#333\"},children:\"\\u60A8\\u6682\\u65E0\\u6743\\u9650\\u8BBF\\u95EE\\u5F53\\u524D\\u5BF9\\u8C61\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:\"14px\",color:\"#333\"},children:[\"\\u5411\\u7BA1\\u7406\\u5458\",/*#__PURE__*/_jsx(\"a\",{onClick:sendApply,children:\"\\u7533\\u8BF7\\u6388\\u6743\"})]})]});const code404=/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"14px\",color:\"#333\"},children:\"\\u60A8\\u672A\\u5728\\u5E94\\u7528\\u6388\\u6743\\u5217\\u8868\\u4E2D\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>openSettingsDrawer('product'),children:\"\\u53BB\\u6388\\u6743\"})]});return/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\",height:\"100%\",display:\"flex\",justifyContent:\"center\",alignItems:\"center\",background:\"#fff\"},children:/*#__PURE__*/_jsxs(Space,{size:10,direction:\"vertical\",align:\"center\",children:[/*#__PURE__*/_jsx(Image,{src:require(\"@assets/images/product_error.png\"),preview:false,style:{width:\"250px\",marginBottom:\"20px\"}}),code==401&&code401,code==402&&code402,code==403&&code403,code==404&&code404]})});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}