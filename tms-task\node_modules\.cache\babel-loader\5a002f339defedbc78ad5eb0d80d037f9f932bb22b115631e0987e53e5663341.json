{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\ProductError.jsx\",\n  _s = $RefreshSig$();\nimport { Button, Image, Modal, Space } from \"antd\";\nimport { useParams } from \"react-router-dom\";\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport * as https from \"../api/http\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { setting_320_get_node_priv_query } from \"@common/api/query/query\";\nimport { useEffect, useState } from \"react\";\nimport { globalEventBus } from \"@common/utils/eventBus\";\n\n/**\r\n * \r\n * @param {*} code 401：应用已过期-非管理员 402：应用已过期-管理员 403：用户未被授权应用 404：管理员未被授权应用\r\n * @returns \r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function ProductError({\n  code\n}) {\n  _s();\n  const queryClient = useQueryClient();\n  const {\n    teamId,\n    nodeId,\n    nid\n  } = useParams();\n  const [productId, setProductId] = useState(0);\n  const setting334Mutation = useMutation({\n    mutationFn: https.setting_334_apply_authorization\n  });\n  useEffect(() => {\n    var _setting320Result$noA;\n    let setting320Query = setting_320_get_node_priv_query(teamId, nid || nodeId);\n    let setting320Result = queryClient.getQueryData(setting320Query.queryKey);\n    setProductId(setting320Result === null || setting320Result === void 0 ? void 0 : (_setting320Result$noA = setting320Result.noAuth) === null || _setting320Result$noA === void 0 ? void 0 : _setting320Result$noA.productId);\n  }, []);\n  const sendApply = () => {\n    if (!!productId) {\n      setting334Mutation.mutate({\n        teamId,\n        productId\n      }, {\n        onSuccess: result => {\n          if (result.resultCode === 200) {\n            //globalUtil.success(\"提交申请成功！\")\n            Modal.info({\n              title: \"提示\",\n              content: \"提交成功，管理员会接收到申请信息，请您耐心等候\",\n              maskClosable: true,\n              //centered: true, // 居中\n              okText: \"我知道了\",\n              width: 500\n            });\n          }\n        }\n      });\n    }\n  };\n  const openSettingsDrawer = (tab = 'product') => {\n    globalEventBus.emit(\"openSettingsDrawerEvent\", null, {\n      teamId,\n      defaultTab: tab,\n      productId\n    });\n  };\n  const code401 = /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: \"14px\",\n        color: \"#333\"\n      },\n      children: \"\\u5E94\\u7528\\u5DF2\\u8FC7\\u671F\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: \"14px\",\n        color: \"#333\"\n      },\n      children: \"\\u8BF7\\u8054\\u7CFB\\u56E2\\u961F\\u7BA1\\u7406\\u5458\\u7EED\\u8D39\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n  const code402 = /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: \"14px\",\n        color: \"#333\"\n      },\n      children: \"\\u5E94\\u7528\\u5DF2\\u8FC7\\u671F\\uFF0C\\u8BF7\\u8D2D\\u4E70\\u6216\\u7EED\\u8D39\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      onClick: () => openSettingsDrawer('product'),\n      children: \"\\u53BB\\u7EED\\u8D39\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n  const code403 = /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: \"14px\",\n        color: \"#333\"\n      },\n      children: \"\\u60A8\\u6682\\u65E0\\u6743\\u9650\\u8BBF\\u95EE\\u5F53\\u524D\\u5BF9\\u8C61\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: \"14px\",\n        color: \"#333\"\n      },\n      children: [\"\\u5411\\u7BA1\\u7406\\u5458\", /*#__PURE__*/_jsxDEV(\"a\", {\n        onClick: sendApply,\n        children: \"\\u7533\\u8BF7\\u6388\\u6743\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 53\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n  const code404 = /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: \"14px\",\n        color: \"#333\"\n      },\n      children: \"\\u60A8\\u672A\\u5728\\u5E94\\u7528\\u6388\\u6743\\u5217\\u8868\\u4E2D\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      onClick: () => openSettingsDrawer('product'),\n      children: \"\\u53BB\\u6388\\u6743\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: \"100%\",\n      height: \"100%\",\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      background: \"#fff\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Space, {\n      size: 10,\n      direction: \"vertical\",\n      align: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Image, {\n        src: require(\"@assets/images/product_error.png\"),\n        preview: false,\n        style: {\n          width: \"250px\",\n          marginBottom: \"20px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 7\n      }, this), code == 401 && code401, code == 402 && code402, code == 403 && code403, code == 404 && code404]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 10\n  }, this);\n}\n_s(ProductError, \"yRYt2I6de9n7tnuFy/T1r4WPBcY=\", false, function () {\n  return [useQueryClient, useParams, useMutation];\n});\n_c = ProductError;\nvar _c;\n$RefreshReg$(_c, \"ProductError\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Image", "Modal", "Space", "useParams", "useMutation", "useQueryClient", "https", "globalUtil", "setting_320_get_node_priv_query", "useEffect", "useState", "globalEventBus", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductError", "code", "_s", "queryClient", "teamId", "nodeId", "nid", "productId", "setProductId", "setting334Mutation", "mutationFn", "setting_334_apply_authorization", "_setting320Result$noA", "setting320Query", "setting320Result", "getQueryData", "query<PERSON><PERSON>", "noAuth", "sendApply", "mutate", "onSuccess", "result", "resultCode", "info", "title", "content", "maskClosable", "okText", "width", "openSettingsDrawer", "tab", "emit", "defaultTab", "code401", "children", "style", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "code402", "type", "onClick", "code403", "code404", "height", "display", "justifyContent", "alignItems", "background", "size", "direction", "align", "src", "require", "preview", "marginBottom", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/ProductError.jsx"], "sourcesContent": ["import { Button, Image, Modal, Space } from \"antd\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport * as https from \"../api/http\"\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { setting_320_get_node_priv_query } from \"@common/api/query/query\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { globalEventBus } from \"@common/utils/eventBus\";\r\n\r\n/**\r\n * \r\n * @param {*} code 401：应用已过期-非管理员 402：应用已过期-管理员 403：用户未被授权应用 404：管理员未被授权应用\r\n * @returns \r\n */\r\nexport default function ProductError({code}){\r\n  const queryClient = useQueryClient();\r\n  const {teamId, nodeId, nid} = useParams();\r\n  const [productId, setProductId] = useState(0);\r\n\r\n  const setting334Mutation = useMutation({\r\n    mutationFn: https.setting_334_apply_authorization\r\n  })\r\n\r\n  useEffect(() => {\r\n    let setting320Query = setting_320_get_node_priv_query(teamId, nid||nodeId);\r\n    let setting320Result = queryClient.getQueryData(setting320Query.queryKey);\r\n    setProductId(setting320Result?.noAuth?.productId);\r\n  }, []);\r\n\r\n  const sendApply = () => {\r\n    if(!!productId) {\r\n      setting334Mutation.mutate({ teamId, productId }, {\r\n        onSuccess: (result) => {\r\n          if(result.resultCode === 200) {\r\n            //globalUtil.success(\"提交申请成功！\")\r\n            Modal.info({\r\n              title: \"提示\",\r\n              content: \"提交成功，管理员会接收到申请信息，请您耐心等候\",\r\n              maskClosable: true,\r\n              //centered: true, // 居中\r\n              okText: \"我知道了\",\r\n              width: 500,\r\n            });\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  const openSettingsDrawer = (tab = 'product') => {\r\n    globalEventBus.emit(\"openSettingsDrawerEvent\", null, { teamId, defaultTab:tab, productId });\r\n  }\r\n\r\n  const code401 = <>\r\n    <div style={{fontSize:\"14px\",color:\"#333\"}}>应用已过期</div>\r\n    <div style={{fontSize:\"14px\",color:\"#333\"}}>请联系团队管理员续费</div>\r\n  </>\r\n\r\n  const code402 = <>\r\n    <div style={{fontSize:\"14px\",color:\"#333\"}}>应用已过期，请购买或续费</div>\r\n    <Button type=\"primary\" onClick={() => openSettingsDrawer('product')}>去续费</Button>\r\n  </>\r\n\r\n  const code403 = <>\r\n    <div style={{fontSize:\"14px\",color:\"#333\"}}>您暂无权限访问当前对象</div>\r\n    <div style={{fontSize:\"14px\",color:\"#333\"}}>向管理员<a onClick={sendApply}>申请授权</a></div>\r\n  </>\r\n\r\n  const code404 = <>\r\n    <div style={{fontSize:\"14px\",color:\"#333\"}}>您未在应用授权列表中</div>\r\n    <Button type=\"primary\" onClick={() => openSettingsDrawer('product')}>去授权</Button>\r\n  </>\r\n\r\n  return <div style={{width:\"100%\",height:\"100%\",display:\"flex\",justifyContent:\"center\",alignItems:\"center\",background:\"#fff\"}}>\r\n    <Space size={10} direction=\"vertical\" align=\"center\">\r\n      <Image src={require(\"@assets/images/product_error.png\")} preview={false} style={{width:\"250px\",marginBottom:\"20px\"}}/>\r\n      {code == 401 && code401}\r\n      {code == 402 && code402}\r\n      {code == 403 && code403}\r\n      {code == 404 && code404}\r\n    </Space>\r\n  </div>\r\n}"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,EAAEC,cAAc,QAAQ,uBAAuB;AACnE,OAAO,KAAKC,KAAK,MAAM,aAAa;AACpC,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,+BAA+B,QAAQ,yBAAyB;AACzE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,cAAc,QAAQ,wBAAwB;;AAEvD;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKA,eAAe,SAASC,YAAYA,CAAC;EAACC;AAAI,CAAC,EAAC;EAAAC,EAAA;EAC1C,MAAMC,WAAW,GAAGd,cAAc,CAAC,CAAC;EACpC,MAAM;IAACe,MAAM;IAAEC,MAAM;IAAEC;EAAG,CAAC,GAAGnB,SAAS,CAAC,CAAC;EACzC,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EAE7C,MAAMe,kBAAkB,GAAGrB,WAAW,CAAC;IACrCsB,UAAU,EAAEpB,KAAK,CAACqB;EACpB,CAAC,CAAC;EAEFlB,SAAS,CAAC,MAAM;IAAA,IAAAmB,qBAAA;IACd,IAAIC,eAAe,GAAGrB,+BAA+B,CAACY,MAAM,EAAEE,GAAG,IAAED,MAAM,CAAC;IAC1E,IAAIS,gBAAgB,GAAGX,WAAW,CAACY,YAAY,CAACF,eAAe,CAACG,QAAQ,CAAC;IACzER,YAAY,CAACM,gBAAgB,aAAhBA,gBAAgB,wBAAAF,qBAAA,GAAhBE,gBAAgB,CAAEG,MAAM,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BL,SAAS,CAAC;EACnD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAG,CAAC,CAACX,SAAS,EAAE;MACdE,kBAAkB,CAACU,MAAM,CAAC;QAAEf,MAAM;QAAEG;MAAU,CAAC,EAAE;QAC/Ca,SAAS,EAAGC,MAAM,IAAK;UACrB,IAAGA,MAAM,CAACC,UAAU,KAAK,GAAG,EAAE;YAC5B;YACArC,KAAK,CAACsC,IAAI,CAAC;cACTC,KAAK,EAAE,IAAI;cACXC,OAAO,EAAE,yBAAyB;cAClCC,YAAY,EAAE,IAAI;cAClB;cACAC,MAAM,EAAE,MAAM;cACdC,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACC,GAAG,GAAG,SAAS,KAAK;IAC9CnC,cAAc,CAACoC,IAAI,CAAC,yBAAyB,EAAE,IAAI,EAAE;MAAE3B,MAAM;MAAE4B,UAAU,EAACF,GAAG;MAAEvB;IAAU,CAAC,CAAC;EAC7F,CAAC;EAED,MAAM0B,OAAO,gBAAGpC,OAAA,CAAAE,SAAA;IAAAmC,QAAA,gBACdrC,OAAA;MAAKsC,KAAK,EAAE;QAACC,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC;MAAM,CAAE;MAAAH,QAAA,EAAC;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACvD5C,OAAA;MAAKsC,KAAK,EAAE;QAACC,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC;MAAM,CAAE;MAAAH,QAAA,EAAC;IAAU;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA,eAC5D,CAAC;EAEH,MAAMC,OAAO,gBAAG7C,OAAA,CAAAE,SAAA;IAAAmC,QAAA,gBACdrC,OAAA;MAAKsC,KAAK,EAAE;QAACC,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC;MAAM,CAAE;MAAAH,QAAA,EAAC;IAAY;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC9D5C,OAAA,CAACd,MAAM;MAAC4D,IAAI,EAAC,SAAS;MAACC,OAAO,EAAEA,CAAA,KAAMf,kBAAkB,CAAC,SAAS,CAAE;MAAAK,QAAA,EAAC;IAAG;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA,eACjF,CAAC;EAEH,MAAMI,OAAO,gBAAGhD,OAAA,CAAAE,SAAA;IAAAmC,QAAA,gBACdrC,OAAA;MAAKsC,KAAK,EAAE;QAACC,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC;MAAM,CAAE;MAAAH,QAAA,EAAC;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC7D5C,OAAA;MAAKsC,KAAK,EAAE;QAACC,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC;MAAM,CAAE;MAAAH,QAAA,GAAC,0BAAI,eAAArC,OAAA;QAAG+C,OAAO,EAAE1B,SAAU;QAAAgB,QAAA,EAAC;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA,eACrF,CAAC;EAEH,MAAMK,OAAO,gBAAGjD,OAAA,CAAAE,SAAA;IAAAmC,QAAA,gBACdrC,OAAA;MAAKsC,KAAK,EAAE;QAACC,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC;MAAM,CAAE;MAAAH,QAAA,EAAC;IAAU;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC5D5C,OAAA,CAACd,MAAM;MAAC4D,IAAI,EAAC,SAAS;MAACC,OAAO,EAAEA,CAAA,KAAMf,kBAAkB,CAAC,SAAS,CAAE;MAAAK,QAAA,EAAC;IAAG;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA,eACjF,CAAC;EAEH,oBAAO5C,OAAA;IAAKsC,KAAK,EAAE;MAACP,KAAK,EAAC,MAAM;MAACmB,MAAM,EAAC,MAAM;MAACC,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,UAAU,EAAC;IAAM,CAAE;IAAAjB,QAAA,eAC3HrC,OAAA,CAACX,KAAK;MAACkE,IAAI,EAAE,EAAG;MAACC,SAAS,EAAC,UAAU;MAACC,KAAK,EAAC,QAAQ;MAAApB,QAAA,gBAClDrC,OAAA,CAACb,KAAK;QAACuE,GAAG,EAAEC,OAAO,CAAC,kCAAkC,CAAE;QAACC,OAAO,EAAE,KAAM;QAACtB,KAAK,EAAE;UAACP,KAAK,EAAC,OAAO;UAAC8B,YAAY,EAAC;QAAM;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,EACrHxC,IAAI,IAAI,GAAG,IAAIgC,OAAO,EACtBhC,IAAI,IAAI,GAAG,IAAIyC,OAAO,EACtBzC,IAAI,IAAI,GAAG,IAAI4C,OAAO,EACtB5C,IAAI,IAAI,GAAG,IAAI6C,OAAO;IAAA;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AACR;AAACvC,EAAA,CApEuBF,YAAY;EAAA,QACdX,cAAc,EACJF,SAAS,EAGZC,WAAW;AAAA;AAAAuE,EAAA,GALhB3D,YAAY;AAAA,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}