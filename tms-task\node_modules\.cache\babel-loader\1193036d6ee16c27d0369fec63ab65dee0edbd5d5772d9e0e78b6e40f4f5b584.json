{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function toUint8(v) {\n  if (v < 0) {\n    return 0;\n  }\n  if (v > 255 /* Constants.MAX_UINT_8 */) {\n    return 255 /* Constants.MAX_UINT_8 */;\n  }\n  return v | 0;\n}\nexport function toUint32(v) {\n  if (v < 0) {\n    return 0;\n  }\n  if (v > ********** /* Constants.MAX_UINT_32 */) {\n    return ********** /* Constants.MAX_UINT_32 */;\n  }\n  return v | 0;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}