import AppNodeResourceIcon from "@components/AppNodeResourceIcon";
import { generateRoutePathByNodeType } from "@common/router/RouterRegister";
import { eEnableFlg } from "@common/utils/enum";
import { getColorByType } from "@common/utils/TsbConfig";
import { Link, useNavigate, useParams } from "react-router-dom";
import "./ThumbView.scss";
import ObjExplorerTitle from "@components/ObjExplorer/ObjExplorerTitle";
import { team_036_get_node_ctx_options } from "@common/api/http";

// 快捷方式
export default function ThumbView({ objNode: nodeData, onShowMoreButtonClick, onAddClick, iconClassName, shortcutClassName, isDraggable, onMoreBtnClick }) {

  const { teamId } = useParams();
  const navigate = useNavigate();

  async function onClick(e){
    e.preventDefault();
    e.stopPropagation();
    if(nodeData?.nodeId == -9999){
      return
    }
    let result = await team_036_get_node_ctx_options({teamId, nodeId: nodeData.nodeId});
    if(result?.resultCode == 10021 || result?.resultCode == 10022){
      return
    }
    let url = generateRoutePathByNodeType(nodeData.nodeType, {
                teamId,
                nodeId: nodeData.anchorNodeId || nodeData.realNodeId || nodeData.nodeId, // 左树没有anchorNodeId，中树有anchorNodeId
                nid: nodeData.realNodeId || nodeData.nodeId, // 对象真实的nodeId，中树对象直接跳转至真实对象
              })
    navigate(url);
  }

  return <div className={`bookmark-tag-col ${isDraggable ? "shortcut-draggable" : ""}`}>
    {/* <Link
      to={
        generateRoutePathByNodeType(nodeData.nodeType, {
          teamId,
          nodeId: nodeData.anchorNodeId || nodeData.realNodeId || nodeData.nodeId, // 左树没有anchorNodeId，中树有anchorNodeId
          nid: nodeData.realNodeId || nodeData.nodeId, // 对象真实的nodeId，中树对象直接跳转至真实对象
        })}
        title={nodeData.nodeName}
        // target="_blank" rel="noopener noreferrer"
    > */}
    <div style={{cursor:'pointer'}} title={nodeData.nodeName} onClick={onClick}>
      <div className="bookmark-tag" onClick={nodeData.isAdd ? onAddClick : () => { }} >
        <span className="bookmark-tag-icon-border">
          <AppNodeResourceIcon nodeType={nodeData.nodeType} isShortcut={nodeData.isShortcut} className={`bookmark-tag-icon ${iconClassName}`} style={{ color: getColorByType(nodeData.rightFlgIconType) }} shortcutClassName={shortcutClassName}>
            {
              !nodeData.isAdd && <span className="iconfont a-gengduoshu fontcolor-light bookmark-extra-btn bookmark-extra-btn-big" onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onShowMoreButtonClick && onShowMoreButtonClick({
                  event: e,
                  node: nodeData,
                })
              }}></span>
            }
          </AppNodeResourceIcon>
        </span>
        {/* <div className={`fontcolor-light text-overflow bookmark-tag-name ${nodeData.boldTypeFlg == eEnableFlg.enable ? "tree-dir-title-bold" : ""} 
          ${nodeData.italicTypeFlg == eEnableFlg.enable ? "tree-dir-title-italic" : ""} 
          ${nodeData.underlineTypeFlg == eEnableFlg.enable && nodeData.strikeTypeFlg == eEnableFlg.enable ? "tree-dir-title-text-decoration" :
            nodeData.underlineTypeFlg == eEnableFlg.enable ? "tree-dir-title-underline" :
              nodeData.strikeTypeFlg == eEnableFlg.enable ? "tree-dir-title-delete" :
                ""
          }`} style={{ color: getColorByType(nodeData.nameTextColorType) }}>{nodeData.label}
        </div> */}
        <ObjExplorerTitle nodeData={{...nodeData, showMoreIcon: false}} onShowMoreButtonClick={onShowMoreButtonClick} onMoreBtnClick={onMoreBtnClick} showNodeTypeIcon={false} align={"center"}/>
      </div>
    {/* </Link > */}
    </div>
  </div>
}