{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{Tree,message,Table,Button,DatePicker,Input,Select,Space}from'antd';import DraggablePopUp from\"@components/DraggablePopUp\";import{ExclamationCircleOutlined}from'@ant-design/icons';import{objNodeTransform}from\"@common/service/objNodeTransform\";import{useParams}from\"react-router-dom\";import*as httpSettings from'@common/api/http';import*as TsbConfig from\"@common/utils/TsbConfig\";import{globalUtil}from\"@common/utils/globalUtil\";import moment from\"moment\";import{sortByProperty}from\"@common/utils/Comparators\";import{isSelectRestore}from\"../../utils/enum\";import{refreshTeamMenu}from\"@common/utils/TsbConfig\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Option}=Select;const{RangePicker}=DatePicker;// 回收站\nexport default function SpaceTrashTab(_ref){let{teamId:propsTeamId,spaceId:propsSpaceId,spaceName:propsSpaceName}=_ref;const paramsTeamId=useParams().teamId;const paramsSpaceId=useParams().spaceId;const paramsSpaceName=useParams().spaceName;const teamId=propsTeamId||paramsTeamId;const spaceId=propsSpaceId||paramsSpaceId;const spaceName=propsSpaceName||paramsSpaceName||'协作群';const[trashNodeList,setTrashNodeList]=useState([]);const[resourceType,setResourceType]=useState([]);const[nodeType,setNodeType]=useState(null);const[nodeId,setNodeId]=useState(null);const[beginDate,setBeginDate]=useState(null);const[endDate,setEndDate]=useState(null);const[keyWords,setKeyWords]=useState(null);const[iskPromptVisible,setIskPromptVisible]=useState(false);const[restoreVisible,setRestoreVisible]=useState(false);const[objNodeTree,setObjNodeTree]=useState([]);const[restoreKeyList,setRestoreKeyList]=useState([]);const[selectedNodeId,setSelectedNodeId]=useState(\"\");const[selectedParentNodeId,setSelectedParentNodeId]=useState(\"\");const[restoreIssueVisible,setRestoreIssueVisible]=useState(false);useEffect(()=>{getTrashNodeList();sortTest();},[]);const sortTest=()=>{let arr_=[12,4,6,23,7,2,4];console.log('当前arr_',arr_);for(let i=0;i<arr_.length;i++){let index=i;for(let j=i+1;j<arr_.length;j++){if(arr_[j]<arr_[index]){index=j;}}if(index!=i){let min_=arr_[index];arr_[index]=arr_[i];arr_[i]=min_;}}console.log(\"arr_\",arr_);};const columns=[{title:'名称',dataIndex:'name',sorter:(a,b)=>sortByProperty(a,b,'name')},{title:'类型',dataIndex:'nodeType',sorter:(a,b)=>sortByProperty(a,b,'nodeType'),render:nodeType=>/*#__PURE__*/_jsx(\"span\",{children:TsbConfig.getNodeConfigByNodeType(nodeType).name})},{title:'删除时间',dataIndex:'deleteDt',sorter:(a,b)=>sortByProperty(a,b,'deleteDt')},{title:'操作人',dataIndex:'opName',sorter:(a,b)=>sortByProperty(a,b,'opName')},{title:'操作',dataIndex:'',render:e=>/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(\"a\",{onClick:()=>riskPromptBox(e),children:\"\\u5F7B\\u5E95\\u5220\\u9664\"}),/*#__PURE__*/_jsx(\"a\",{style:{marginLeft:20},onClick:()=>restoreTrashPopUp(e),children:\"\\u8FD8\\u539F\"})]})}];function order(a,b,type){if(a&&b){if(type=='deleteDt'){return parseInt(moment(a).format('YYYYMMDDHHmmss'))-parseInt(moment(b).format('YYYYMMDDHHmmss'));}return 1;}else if(a&&!b){return-1;}else{return 0;}}const formatTreeData=function(){let cur=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];let arr=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[];// 生成根目录\nif(cur.length==0){// 格式化数据格式\narr=arr.map(item=>{let nodeConfig=TsbConfig.getNodeConfigByNodeType(item.nodeType);return{key:item.nodeId,title:item.nodeName,icon:/*#__PURE__*/_jsx(\"span\",{className:\"fontsize-12 fontcolor-normal iconfont \"+nodeConfig.icon,style:{color:nodeConfig.iconColor}}),...item};});cur=arr.filter(item=>arr.every(itemx=>itemx.nodeId!=item.nodeParentId));}cur.forEach(item=>{let childs=arr.filter(itemx=>itemx.nodeParentId==item.nodeId);if(childs.length){let turnChilds=formatTreeData(childs,arr);item.children=turnChilds;}});return cur;};// 获取回收站对象列表\nconst getTrashNodeList=function(){let type=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'';let params={spaceId:spaceId,teamId:teamId,nodeType:type=='delete'?null:nodeType,beginDate:beginDate,endDate:endDate,keyWords:keyWords};httpSettings.team_514_get_trash_node_list(params).then(res=>{if(res.resultCode===200){setTrashNodeList(((res===null||res===void 0?void 0:res.trashNodeList)||[]).sort((a,b)=>order(b.deleteDt,a.deleteDt,'deleteDt')));if(res.trashNodeList.length>0){if(nodeType==null){let resourceType_=res.trashNodeList.map((item,index)=>{return{key:index+1,name:item.nodeType};});let temp={};let resourceTypeList=[];resourceType_.map((item,index)=>{if(!temp[item.name]){resourceTypeList.push(item);temp[item.name]=true;}});setResourceType(resourceTypeList);}}}});};// 选择对象类型\nconst resourceTypeSelect=key=>{setNodeType(key);};// 全选\nconst resourceTypeClear=()=>{setNodeType(null);};// 获取输入的关键字\nconst keywordChange=e=>{setKeyWords(e.target.value);};const dateChange=(value,mode)=>{if(mode[0]==''&&mode[1]==''){setBeginDate(null);setEndDate(null);}else{setBeginDate(mode[0]);setEndDate(mode[1]);}};// 删除风险提示\nconst riskPromptBox=e=>{setIskPromptVisible(true);setNodeId(e.nodeId);setNodeType(e.nodeType);};// 获取当前子工作区的对象树\nconst getSubspaceTree=spaceId=>{let params={teamId:teamId,spaceId:spaceId};httpSettings.team_019_get_space_tree(params).then(res=>{if(res.resultCode===200){setObjNodeTree(res.objNodeTree.filter(e=>e.nodeType!='301'));console.log(formatTreeData([],res.objNodeTree.filter(e=>e.nodeId!=spaceId&&e.nodeId!='65651694')));}});};// 彻底删除回收站对象\nconst delTrashNode=()=>{let params={nodeId:nodeId,teamId:teamId,nodeType:nodeType};httpSettings.team_502_del_trash_node(params).then(res=>{if(res.resultCode===200){setIskPromptVisible(false);getTrashNodeList('delete');}});};// 打开恢复对象弹窗\nconst restoreTrashPopUp=e=>{if(isSelectRestore(e.nodeType)){setRestoreVisible(true);}else{setRestoreIssueVisible(true);}setSelectedNodeId(e.nodeId);getSubspaceTree(spaceId);};// 恢复回收站对象\nconst restoreTrashNode=()=>{let params={teamId:teamId,nodeId:selectedNodeId,restoreParentNodeId:selectedParentNodeId};httpSettings.team_503_restore_trash_node(params).then(res=>{if(res.resultCode===200){globalUtil.success(\"还原成功\");setRestoreIssueVisible(false);setRestoreVisible(false);getTrashNodeList();refreshTeamMenu();}});};// Tree节点防止取消\nconst restoreSelected=(selectedKeys,info)=>{// 当前选中节点\nsetRestoreKeyList([info.node.key]);setSelectedParentNodeId(info.node.nodeId);};return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"RecycleBin\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"RecycleBin-space\",children:/*#__PURE__*/_jsxs(Space,{size:40,children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u5BF9\\u8C61\\u7C7B\\u578B\",/*#__PURE__*/_jsx(Select,{allowClear:true,placeholder:\"\\u5168\\u90E8\",style:{width:100,marginLeft:20},onSelect:resourceTypeSelect,onClear:resourceTypeClear,children:resourceType.map((item,index)=>/*#__PURE__*/_jsx(Option,{value:item.name,children:TsbConfig.getNodeConfigByNodeType(item.name).name}))})]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u9009\\u62E9\\u65E5\\u671F\",/*#__PURE__*/_jsx(RangePicker,{style:{marginLeft:10,borderRadius:6},showTime:true,placeholder:['开始日期','结束日期'],onChange:dateChange})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"RecycleBin-space-search\",children:[/*#__PURE__*/_jsx(Input,{style:{width:180,borderRadius:6},autoComplete:\"off\",placeholder:\"\\u5173\\u952E\\u5B57\\u641C\\u7D22\",onChange:keywordChange,onPressEnter:getTrashNodeList}),/*#__PURE__*/_jsx(Button,{type:\"primary\",style:{marginLeft:10,borderRadius:6},onClick:getTrashNodeList,children:\"\\u641C\\u7D22\"})]})]})}),/*#__PURE__*/_jsx(Table,{className:\"RecycleBin-table\",size:\"small\",columns:columns,dataSource:trashNodeList,showSorterTooltip:false,pagination:{size:\"small\",position:[\"bottomCenter\"],showSizeChanger:true,showQuickJumper:true,total:trashNodeList.length,pageSize:20,showTotal:total=>{return`共${total}条`;}}}),/*#__PURE__*/_jsx(DraggablePopUp,{className:\"RecycleBin-modal\",centered:true,title:\"\\u63D0\\u793A\",width:240,closable:false,okText:\"\\u786E\\u8BA4\",cancelText:\"\\u53D6\\u6D88\",open:iskPromptVisible,maskClosable:false//tmsbug-8018 点击蒙层不隐藏对话框\n,onOk:()=>delTrashNode(),onCancel:()=>setIskPromptVisible(false),children:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(ExclamationCircleOutlined,{style:{marginRight:6,color:'#ffc53d'}}),\"\\u5F7B\\u5E95\\u5220\\u9664\\u5C06\\u65E0\\u6CD5\\u8FD8\\u539F\"]})}),/*#__PURE__*/_jsxs(DraggablePopUp,{className:\"RecycleBin-modalTree\",centered:true,closable:false,title:\"\\u9009\\u62E9\\u8FD8\\u539F\\u7684\\u4F4D\\u7F6E\",okText:\"\\u786E\\u8BA4\",cancelText:\"\\u53D6\\u6D88\",open:restoreVisible,maskClosable:false,onOk:()=>restoreTrashNode(),onCancel:()=>setRestoreVisible(false),children:[/*#__PURE__*/_jsx(\"span\",{children:spaceName}),/*#__PURE__*/_jsx(Tree,{className:\"RecycleBin-modalTree-tree\",blockNode:true,showIcon:true,selectedKeys:restoreKeyList,onSelect:restoreSelected,treeData:formatTreeData([],objNodeTree)})]}),/*#__PURE__*/_jsx(DraggablePopUp,{className:\"restoreIssueDrag\",icon:\"warning\",title:\"\\u8FD8\\u539F\",width:300,content:\"\\u8FD8\\u539F\\u81F3\\u4E4B\\u524D\\u4F4D\\u7F6E\",open:restoreIssueVisible,onOk:restoreTrashNode,onCancel:()=>setRestoreIssueVisible(false)})]})});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}