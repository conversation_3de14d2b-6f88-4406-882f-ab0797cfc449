{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Iterable } from '../../../base/common/iterator.js';\nimport { LinkedList } from '../../../base/common/linkedList.js';\nexport const USUAL_WORD_SEPARATORS = '`~!@#$%^&*()-=+[{]}\\\\|;:\\'\",.<>/?';\n/**\n * Create a word definition regular expression based on default word separators.\n * Optionally provide allowed separators that should be included in words.\n *\n * The default would look like this:\n * /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\$\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g\n */\nfunction createWordRegExp() {\n  let allowInWords = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  let source = '(-?\\\\d*\\\\.\\\\d\\\\w*)|([^';\n  for (const sep of USUAL_WORD_SEPARATORS) {\n    if (allowInWords.indexOf(sep) >= 0) {\n      continue;\n    }\n    source += '\\\\' + sep;\n  }\n  source += '\\\\s]+)';\n  return new RegExp(source, 'g');\n}\n// catches numbers (including floating numbers) in the first group, and alphanum in the second\nexport const DEFAULT_WORD_REGEXP = createWordRegExp();\nexport function ensureValidWordDefinition(wordDefinition) {\n  let result = DEFAULT_WORD_REGEXP;\n  if (wordDefinition && wordDefinition instanceof RegExp) {\n    if (!wordDefinition.global) {\n      let flags = 'g';\n      if (wordDefinition.ignoreCase) {\n        flags += 'i';\n      }\n      if (wordDefinition.multiline) {\n        flags += 'm';\n      }\n      if (wordDefinition.unicode) {\n        flags += 'u';\n      }\n      result = new RegExp(wordDefinition.source, flags);\n    } else {\n      result = wordDefinition;\n    }\n  }\n  result.lastIndex = 0;\n  return result;\n}\nconst _defaultConfig = new LinkedList();\n_defaultConfig.unshift({\n  maxLen: 1000,\n  windowSize: 15,\n  timeBudget: 150\n});\nexport function getWordAtText(column, wordDefinition, text, textOffset, config) {\n  if (!config) {\n    config = Iterable.first(_defaultConfig);\n  }\n  if (text.length > config.maxLen) {\n    // don't throw strings that long at the regexp\n    // but use a sub-string in which a word must occur\n    let start = column - config.maxLen / 2;\n    if (start < 0) {\n      start = 0;\n    } else {\n      textOffset += start;\n    }\n    text = text.substring(start, column + config.maxLen / 2);\n    return getWordAtText(column, wordDefinition, text, textOffset, config);\n  }\n  const t1 = Date.now();\n  const pos = column - 1 - textOffset;\n  let prevRegexIndex = -1;\n  let match = null;\n  for (let i = 1;; i++) {\n    // check time budget\n    if (Date.now() - t1 >= config.timeBudget) {\n      break;\n    }\n    // reset the index at which the regexp should start matching, also know where it\n    // should stop so that subsequent search don't repeat previous searches\n    const regexIndex = pos - config.windowSize * i;\n    wordDefinition.lastIndex = Math.max(0, regexIndex);\n    const thisMatch = _findRegexMatchEnclosingPosition(wordDefinition, text, pos, prevRegexIndex);\n    if (!thisMatch && match) {\n      // stop: we have something\n      break;\n    }\n    match = thisMatch;\n    // stop: searched at start\n    if (regexIndex <= 0) {\n      break;\n    }\n    prevRegexIndex = regexIndex;\n  }\n  if (match) {\n    const result = {\n      word: match[0],\n      startColumn: textOffset + 1 + match.index,\n      endColumn: textOffset + 1 + match.index + match[0].length\n    };\n    wordDefinition.lastIndex = 0;\n    return result;\n  }\n  return null;\n}\nfunction _findRegexMatchEnclosingPosition(wordDefinition, text, pos, stopPos) {\n  let match;\n  while (match = wordDefinition.exec(text)) {\n    const matchIndex = match.index || 0;\n    if (matchIndex <= pos && wordDefinition.lastIndex >= pos) {\n      return match;\n    } else if (stopPos > 0 && matchIndex > stopPos) {\n      return null;\n    }\n  }\n  return null;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}