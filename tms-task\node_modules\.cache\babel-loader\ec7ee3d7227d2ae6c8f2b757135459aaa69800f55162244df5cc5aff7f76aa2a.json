{"ast": null, "code": "import{local_check_version_update}from\"@common/api/http\";import{useQuery}from\"@tanstack/react-query\";import React,{useEffect,useState}from\"react\";import{Modal,Divider,Typography}from\"antd\";import*as storageConstant from\"@common/utils/storageConstant\";import{eEnableFlg}from\"@common/utils/enum\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Text}=Typography;export default function CheckAppUpdate(){const[systemUpdateTips,setSystemUpdateTips]=useState(true);// 版本更新\nconst versionQuery=useQuery({queryFn:async()=>{return local_check_version_update({timestamp:Date.now()}).then(result=>{return{version:result.version||process.env.REACT_APP_VERSION,changelog:result.changelog||null};});},enabled:process.env.NODE_ENV===\"production\"&&localStorage.getItem(storageConstant.no_polling)!=eEnableFlg.enable,cacheTime:Infinity,// 每15秒会刷新一次接口\nrefetchInterval:15000,refetchOnWindowFocus:true});// 渲染更新日志内容\nconst renderChangelogContent=(changelog,newVersion)=>{if(!changelog||!changelog.versionInfo){return null;}const{versionInfo}=changelog;return/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:16},children:[/*#__PURE__*/_jsx(Divider,{orientation:\"left\",style:{margin:'12px 0 8px 0'},children:/*#__PURE__*/_jsx(Text,{strong:true,style:{fontSize:14},children:\"\\u66F4\\u65B0\\u5185\\u5BB9\"})}),versionInfo.features&&versionInfo.features.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:12},children:[/*#__PURE__*/_jsx(Text,{strong:true,style:{color:'#52c41a',fontSize:13},children:\"\\u2728 \\u65B0\\u529F\\u80FD\"}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:'4px 0 0 0',paddingLeft:20},children:versionInfo.features.map((feature,index)=>/*#__PURE__*/_jsx(\"li\",{style:{fontSize:12,lineHeight:'18px',marginBottom:2},children:feature},index))})]}),versionInfo.improvements&&versionInfo.improvements.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:12},children:[/*#__PURE__*/_jsx(Text,{strong:true,style:{color:'#1890ff',fontSize:13},children:\"\\uD83D\\uDE80 \\u4F18\\u5316\\u6539\\u8FDB\"}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:'4px 0 0 0',paddingLeft:20},children:versionInfo.improvements.map((improvement,index)=>/*#__PURE__*/_jsx(\"li\",{style:{fontSize:12,lineHeight:'18px',marginBottom:2},children:improvement},index))})]}),versionInfo.bugfixes&&versionInfo.bugfixes.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,style:{color:'#ff4d4f',fontSize:13},children:\"\\uD83D\\uDC1B \\u95EE\\u9898\\u4FEE\\u590D\"}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:'4px 0 0 0',paddingLeft:20},children:versionInfo.bugfixes.map((bugfix,index)=>/*#__PURE__*/_jsx(\"li\",{style:{fontSize:12,lineHeight:'18px',marginBottom:2},children:bugfix},index))})]}),versionInfo.releaseDate&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:12,textAlign:'right'},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:11},children:[\"\\u53D1\\u5E03\\u65F6\\u95F4: \",versionInfo.releaseDate]})})]});};useEffect(()=>{if(versionQuery.dataUpdatedAt&&systemUpdateTips&&versionQuery.data&&Number(versionQuery.data.version)!==Number(process.env.REACT_APP_VERSION)){setSystemUpdateTips(false);const newVersion=versionQuery.data.version;const changelog=versionQuery.data.changelog;Modal.confirm({title:\"新版本更新\",width:520,content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:12},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u5F53\\u524D\\u7248\\u672C: \",/*#__PURE__*/_jsx(Text,{code:true,children:process.env.REACT_APP_VERSION})]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u6700\\u65B0\\u7248\\u672C: \",/*#__PURE__*/_jsx(Text,{code:true,style:{color:'#fa86d9'},children:newVersion})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:13,color:'#666',marginBottom:8},children:\"\\u70B9\\u51FB\\\"\\u7ACB\\u5373\\u66F4\\u65B0\\\"\\u6309\\u94AE\\u6216\\u5237\\u65B0\\u9875\\u9762\\uFF0C\\u8BBF\\u95EE\\u6700\\u65B0\\u7248\\u672C\"}),renderChangelogContent(changelog,newVersion)]}),okText:'立即更新',cancelText:'暂不更新',onOk:()=>{window.location.reload(true);}});}},[versionQuery.dataUpdatedAt]);return/*#__PURE__*/_jsx(React.Fragment,{});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}