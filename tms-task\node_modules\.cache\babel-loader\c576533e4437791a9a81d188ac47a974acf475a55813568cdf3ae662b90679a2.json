{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\quickAcess\\\\views\\\\DataPush\\\\EditDrawer\\\\SubscribeEdit.jsx\",\n  _s = $RefreshSig$();\nimport { CaretDownOutlined } from \"@ant-design/icons\";\nimport { Button, Input, Drawer, Form, Checkbox, Avatar, Select, Space, Dropdown } from \"antd\";\nimport DraggablePopUp from \"@components/DraggablePopUp\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport '../DataPush.scss';\nimport * as httpQuickAccess from \"@common/api/http\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\nimport { eNodeTypeId, eNodeType } from \"@common/utils/TsbConfig\";\nimport { NoAvatarIcon } from '@common/components/IconUtil';\nimport TEditor from \"@common/components/TEditor/TEditor\";\nimport TextModelModal from './TextModel/TextModelModal';\nimport PlanModal from './Plan/PlanModal';\nimport SubscribeContent from './SubscribeContent/SubscribeContent';\nimport { ePriorityTypeObj, ePriorityType, eMsgChannel, eMsgChannelObj, eExecTimeType } from \"src/quickAcess/utils/Config\";\nimport { useQueryTeam599GetDataDictionary } from \"src/quickAcess/service/quickHooks\";\nimport { eSelectionListId } from \"@common/utils/enum\";\nimport { isEmpty } from \"@common/utils/ArrayUtils\";\nimport DraggableDrawer from \"@components/DraggableDrawer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TextArea\n} = Input;\n\n// 新建/编辑系统订阅\nexport default function SubscribeEdit({\n  allMember,\n  opType,\n  closeDrawer,\n  modalVisible,\n  editData,\n  isChange,\n  setIsChange,\n  selectedKey,\n  typeList,\n  fromType,\n  ganttName,\n  nodeType\n}) {\n  _s();\n  var _allMember$find, _eNodeType$nodeType;\n  const {\n    teamId,\n    nodeId\n  } = useParams();\n  const [form] = Form.useForm();\n  const codeInputRef = useRef(Object.create(null));\n  // Form.Item布局样式\n  const formItemLayout = {\n    labelCol: {\n      span: 2\n    }\n  };\n\n  // Form.Item的样式\n  const style = {\n    style: {\n      height: \"50%\",\n      marginBottom: 20\n    }\n  };\n  const [subscribeDataVisible, setSubscribeDataVisible] = useState(false);\n  const [subscribeTextVisible, setSubscribeTextVisible] = useState(false);\n  const [subscribePlanVisible, setSubscribePlanVisible] = useState(false);\n  const [touids, setTouids] = useState([]); // 收件人\n  const [ccuids, setCcuids] = useState([]); // 抄送人\n  const [pushwhenEmpty, setPushwhenEmpty] = useState(false);\n  const [subscribeData, setSubscribeData] = useState(null);\n  const [timedPlan, setTimedPlan] = useState(null);\n  const [description, setDescription] = useState(\"\"); // 备注\n\n  const [showTip, setShowTip] = useState(false);\n  const {\n    data: {\n      dataDictionaries = [],\n      templateVariables = [],\n      workflowVariables = []\n    } = {\n      dataDictionaries: [],\n      templateVariables: [],\n      workflowVariables: []\n    },\n    isLoading: isLoadingTeamXxx,\n    refetch: refetchTeamXxx\n  } = useQueryTeam599GetDataDictionary({\n    teamId,\n    selectionId: eSelectionListId.Selection_1970_datapush\n  });\n  useEffect(() => {\n    if (modalVisible) {\n      if (!!editData) {\n        form.setFieldValue('subscrTitle', editData.subscrTitle || '');\n        // form.setFieldValue('description',editData.description||'');\n        setDescription(editData.description || '');\n        let toUids = (editData.toUids || '').split(',').filter(uid => !!uid);\n        setTouids([...toUids]);\n        let ccUids = (editData.ccUids || '').split(',').filter(uid => !!uid);\n        setCcuids([...ccUids]);\n        let obj = !!editData.objId ? {\n          objId: editData.objId,\n          objType: editData.objType,\n          objName: editData.objName\n        } : null;\n        setSubscribeData(obj);\n        setPushwhenEmpty(editData.pushWhenEmpty == 1);\n        setTimedPlan({\n          cronId: editData.cronId,\n          cronName: editData.cronDesc\n        });\n        setTimeout(() => {\n          var _codeInputRef$current;\n          !!(codeInputRef !== null && codeInputRef !== void 0 && (_codeInputRef$current = codeInputRef.current) !== null && _codeInputRef$current !== void 0 && _codeInputRef$current.setContent) && codeInputRef.current.setContent(editData.msgTpl);\n        }, 500);\n      } else {\n        form.setFieldValue('subscrTitle', '');\n        // form.setFieldValue('description','');\n        setDescription(\"\");\n        setTouids([]);\n        setCcuids([]);\n        setSubscribeData(null);\n        setPushwhenEmpty(false);\n        setTimedPlan(null);\n        setTimeout(() => {\n          var _codeInputRef$current2;\n          //todo 推送文案默认值\n          !!(codeInputRef !== null && codeInputRef !== void 0 && (_codeInputRef$current2 = codeInputRef.current) !== null && _codeInputRef$current2 !== void 0 && _codeInputRef$current2.setContent) && codeInputRef.current.setContent(\"<p>你好，</p><p>%订阅数据%，</p><p>谢谢</p>\");\n        }, 500);\n      }\n    }\n  }, [modalVisible]);\n  function avatarFormat(src, name) {\n    return /*#__PURE__*/_jsxDEV(Avatar, {\n      style: {\n        marginRight: 5\n      },\n      src: src,\n      icon: /*#__PURE__*/_jsxDEV(NoAvatarIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 64\n      }, this),\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this);\n  }\n  function onOk(opType) {\n    var _codeInputRef$current3;\n    let values = form.getFieldsValue(true);\n    setShowTip(false);\n    if ((touids || []).length == 0) {\n      globalUtil.warning('请选择收件人');\n      return;\n    }\n    if (!timedPlan) {\n      globalUtil.warning('请选择定时计划');\n      return;\n    }\n    let msgtpl = (codeInputRef === null || codeInputRef === void 0 ? void 0 : (_codeInputRef$current3 = codeInputRef.current) === null || _codeInputRef$current3 === void 0 ? void 0 : _codeInputRef$current3.getContent()) || '';\n    if (opType == 0) {\n      if (!!(subscribeData !== null && subscribeData !== void 0 && subscribeData.objId)) {\n        if ((msgtpl || '').indexOf('%订阅数据%') == -1) {\n          setShowTip(true);\n          return;\n        }\n      }\n    }\n    let params = {\n      teamId: teamId,\n      subscrTitle: values.subscrTitle,\n      msgChannel: values.msgChannel,\n      objId: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objId,\n      objType: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objType,\n      objName: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objName,\n      msgTpl: msgtpl,\n      toUids: (touids || []).join(','),\n      ccUids: (ccuids || []).join(','),\n      execTimeType: eExecTimeType.reportSubscribe,\n      // 报表订阅 \n      cronId: timedPlan.cronId,\n      description: description,\n      pushWhenEmpty: pushwhenEmpty ? 1 : 0,\n      projectNodeId: nodeId,\n      nodeType: nodeType\n    };\n    if (!!editData) {\n      params.nodeId = selectedKey;\n    }\n    httpQuickAccess.team_523_save_sched_task_src(params).then(res => {\n      if (res.resultCode == 200) {\n        closeDrawer(1);\n      }\n    });\n  }\n  function _onClose() {\n    var _codeInputRef$current4;\n    closeDrawer(0, {\n      values: form.getFieldsValue(true),\n      objId: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objId,\n      msgTpl: (codeInputRef === null || codeInputRef === void 0 ? void 0 : (_codeInputRef$current4 = codeInputRef.current) === null || _codeInputRef$current4 === void 0 ? void 0 : _codeInputRef$current4.getContent()) || '',\n      cronId: timedPlan === null || timedPlan === void 0 ? void 0 : timedPlan.cronId,\n      toUids: touids || [],\n      ccUids: ccuids || [],\n      pushwhenEmpty: pushwhenEmpty ? 1 : 0\n    });\n  }\n  function _onChange(value, opType) {\n    if (!value) {\n      if (opType == 1) {\n        // 编辑\n        setTimedPlan(null);\n      }\n      if (opType == 0) {\n        // 新建\n        setSubscribeData(null);\n      }\n    }\n  }\n\n  /*  // 模版\r\n    function onClickTemplateVariables (info) {\r\n      let theme = form.getFieldValue(\"theme\")??\"\";\r\n      console.log(\"theme\", theme, info.key);\r\n      form.setFieldValue(\"theme\", `${theme}${info.key}`);\r\n    }*/\n\n  function onChangeRemark(e) {\n    setDescription(e.target.value);\n  }\n  return /*#__PURE__*/_jsxDEV(DraggableDrawer, {\n    className: \"tms-drawer\",\n    title: `${opType == 0 ? \"新建\" : '编辑'}${fromType == 'gantt' ? '进度推送' : '报表'}订阅`,\n    width: '70%',\n    onClose: () => _onClose(),\n    open: modalVisible,\n    destroyOnClose: true //关闭时销毁子元素,避免重新打开数据不会刷新\n    ,\n    closable: true,\n    centered: true,\n    footer: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'end'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: 20,\n        children: [!!subscribeData && /*#__PURE__*/_jsxDEV(Checkbox, {\n          className: \"subscribe-way-mark-check\",\n          onChange: e => setPushwhenEmpty(e.target.checked),\n          checked: pushwhenEmpty,\n          children: \"\\u8BA2\\u9605\\u6570\\u636E\\u4E3A\\u7A7A\\u65F6\\uFF0C\\u4F9D\\u7136\\u63A8\\u9001\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          value: description,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\",\n          style: {\n            borderRadius: 5,\n            width: 300\n          },\n          autoComplete: \"off\",\n          onChange: onChangeRemark\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            borderRadius: 5\n          },\n          type: 'primary',\n          onClick: () => {\n            form.submit();\n          },\n          children: \"\\u63D0\\u4EA4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(Form, {\n      className: \"subscribe-way\",\n      form: form,\n      ...formItemLayout,\n      labelAlign: \"right\",\n      onFinish: () => onOk(0),\n      initialValues: {\n        msgChannel: eMsgChannel.mail // 默认邮件\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"label-header\",\n        children: \"\\u63A8\\u9001\\u89C4\\u5219\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u540D\\u79F0\",\n        rules: [{\n          required: true,\n          message: '名称不能为空'\n        }],\n        ...style,\n        name: \"subscrTitle\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          style: {\n            width: 300,\n            borderRadius: 5\n          },\n          autoComplete: \"off\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u5B9A\\u65F6\\u8BA1\\u5212\",\n        required: true,\n        ...style,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: 300\n            },\n            placeholder: \"\\u8BF7\\u9009\\u62E9\",\n            value: timedPlan === null || timedPlan === void 0 ? void 0 : timedPlan.cronName,\n            suffixIcon: /*#__PURE__*/_jsxDEV(CaretDownOutlined, {\n              style: {\n                pointerEvents: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 27\n            }, this),\n            onChange: value => _onChange(value, 1),\n            allowClear: true,\n            onClick: () => setSubscribePlanVisible(true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#999',\n              marginLeft: 10\n            },\n            children: \"\\u5907\\u6CE8\\uFF1A\\u8BA2\\u9605\\u90AE\\u4EF6\\u53D1\\u9001\\u65F6\\u95F4\\u4F1A\\u67091-5\\u5206\\u949F\\u7684\\u5EF6\\u8FDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u4F18\\u5148\\u7EA7\",\n        ...style,\n        name: \"priority\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          showSearch: true,\n          allowClear: true,\n          style: {\n            width: 300\n          },\n          placeholder: \"\\u8BF7\\u9009\\u62E9\",\n          options: [ePriorityTypeObj[ePriorityType.high], ePriorityTypeObj[ePriorityType.middle], ePriorityTypeObj[ePriorityType.low]]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u7CFB\\u7EDF\\u63A8\\u9001\\u901A\\u9053\",\n        ...style,\n        name: \"msgChannel\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          disabled: true,\n          showSearch: true,\n          allowClear: true,\n          style: {\n            width: 300\n          },\n          placeholder: \"\\u8BF7\\u9009\\u62E9\",\n          options: [eMsgChannelObj[eMsgChannel.mail], eMsgChannelObj[eMsgChannel.msg], eMsgChannelObj[eMsgChannel.voice], eMsgChannelObj[eMsgChannel.wechat], eMsgChannelObj[eMsgChannel.inSiteMsg]]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 12\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 10\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"label-header\",\n        children: \"\\u63A8\\u9001\\u5185\\u5BB9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6536\\u4EF6\\u4EBA\",\n        required: true,\n        ...style,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          showSearch: true,\n          allowClear: true\n          // mode='multiple' fix tmsbug-11663:系统订阅，收件人，期望从多选改为单选\n          ,\n          placeholder: \"\\u8BF7\\u9009\\u62E9\\u6536\\u4EF6\\u4EBA\",\n          value: touids === null || touids === void 0 ? void 0 : touids[0],\n          onChange: value => setTouids(!!value ? [value] : []),\n          optionFilterProp: \"children\",\n          dropdownMatchSelectWidth: false,\n          filterOption: (input, option) => {\n            var _option$key;\n            return ((_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : '').toLowerCase().includes(input.toLowerCase());\n          },\n          options: allMember.map(user => ({\n            value: user.key.toString(),\n            label: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [avatarFormat(user.avatar, user.label), user.label]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 22\n            }, this),\n            key: user.label\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6284\\u9001\\u4EBA\",\n        ...style,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          showSearch: true,\n          mode: \"multiple\",\n          allowClear: true,\n          placeholder: \"\\u8BF7\\u9009\\u62E9\\u6284\\u9001\\u4EBA\",\n          value: ccuids,\n          onChange: value => setCcuids(value),\n          optionFilterProp: \"children\",\n          filterOption: (input, option) => {\n            var _option$key2;\n            return ((_option$key2 = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key2 !== void 0 ? _option$key2 : '').toLowerCase().includes(input.toLowerCase());\n          },\n          options: allMember.map(user => ({\n            value: user.key.toString(),\n            label: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [avatarFormat(user.avatar, user.label), user.label]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 22\n            }, this),\n            key: user.label\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u8BA2\\u9605\\u6570\\u636E\\u6E90\",\n        ...style,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: 300\n            },\n            open: false,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\",\n            value: subscribeData === null || subscribeData === void 0 ? void 0 : subscribeData.objName,\n            suffixIcon: /*#__PURE__*/_jsxDEV(CaretDownOutlined, {\n              style: {\n                pointerEvents: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 27\n            }, this),\n            onChange: value => _onChange(value, 0),\n            allowClear: true,\n            onClick: () => setSubscribeDataVisible(true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#999',\n              marginLeft: 10\n            },\n            children: [\"\\u5907\\u6CE8\\uFF1A\\u7CFB\\u7EDF\\u53D1\\u4EF6\\u65F6\\uFF0C\\u4F1A\\u4EE5\\u6536\\u4EF6\\u4EBA\", `${!isEmpty(touids) ? `(${(_allMember$find = allMember.find(member => member.key == touids[0])) === null || _allMember$find === void 0 ? void 0 : _allMember$find.label})` : \"\"}`, \"\\u8EAB\\u4EFD\\u5BF9\\u8BA2\\u9605\\u6570\\u636E\\u8FDB\\u884C\\u8FC7\\u6EE4\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6B63\\u6587\",\n        ...style,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            onClick: () => setSubscribeTextVisible(true),\n            children: \"\\u9009\\u62E9\\u6A21\\u7248\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TEditor, {\n            ref: codeInputRef,\n            placeholderText: \"\",\n            heightMin: '200px',\n            uploadParams: {\n              teamId: teamId,\n              nodeId: nodeId,\n              moduleName: (_eNodeType$nodeType = eNodeType[nodeType]) === null || _eNodeType$nodeType === void 0 ? void 0 : _eNodeType$nodeType.nameEn,\n              objType: nodeType\n            },\n            autofocus: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#999'\n            },\n            children: [\"\\u63D0\\u793A\\uFF1A\\u5360\\u4F4D\\u7B26 \", /*#__PURE__*/_jsxDEV(\"a\", {\n              onClick: () => codeInputRef.current.insert('%订阅数据%'),\n              children: \"%\\u8BA2\\u9605\\u6570\\u636E%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 48\n            }, this), \"\\uFF0C\\u4EE3\\u8868\\u9700\\u8981\\u8BA2\\u9605\\u7684\\u641C\\u7D22\\u7ED3\\u679C\\u6216\\u62A5\\u8868\\u6570\\u636E\\uFF0C\\u8BF7\\u70B9\\u51FB\\u5360\\u4F4D\\u7B26\\uFF0C\\u5C06\\u5176\\u5305\\u542B\\u8FDB\\u6B63\\u6587\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SubscribeContent, {\n      allMember: allMember,\n      subscribeDataVisible: subscribeDataVisible,\n      setSubscribeDataVisible: setSubscribeDataVisible,\n      subscribeData: subscribeData,\n      setSubscribeData: setSubscribeData,\n      typeList: typeList,\n      fromType: fromType,\n      ganttName: ganttName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TextModelModal, {\n      subscribeTextVisible: subscribeTextVisible,\n      setSubscribeTextVisible: setSubscribeTextVisible,\n      setPushText: codeInputRef\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PlanModal, {\n      subscribePlanVisible: subscribePlanVisible,\n      setSubscribePlanVisible: setSubscribePlanVisible,\n      timedPlan: timedPlan,\n      setTimedPlan: setTimedPlan\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      className: \"tms-modal\",\n      title: \"\\u63D0\\u793A\",\n      centered: true,\n      width: 300,\n      open: isChange,\n      onCancel: () => setIsChange(false),\n      onOk: () => closeDrawer(0),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          margin: '10px 0px'\n        },\n        children: \"\\u60A8\\u6B63\\u5728\\u7F16\\u8F91\\u8BA2\\u9605\\uFF0C\\u786E\\u5B9A\\u653E\\u5F03\\u7F16\\u8F91\\uFF1F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      className: \"tms-modal\",\n      title: \"\\u63D0\\u793A\",\n      centered: true,\n      width: 600,\n      open: showTip,\n      cancelText: \"\\u7EE7\\u7EED\\u4FDD\\u5B58\",\n      onCancel: () => onOk(1),\n      okText: \"\\u91CD\\u65B0\\u4FEE\\u6539\",\n      onOk: () => setShowTip(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          margin: '10px 0px',\n          fontSize: 12\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5185\\u5BB9\\u6846\\u4E2D\\uFF0C\\u672A\\u68C0\\u6D4B\\u5230\\u6709 %\\u8BA2\\u9605\\u6570\\u636E% \\u5360\\u4F4D\\u7B26\\uFF0C\\u82E5\\u4E0D\\u542B\\u6B64\\u5360\\u4F4D\\u7B26\\uFF0C\\u8BA2\\u9605\\u90AE\\u4EF6\\u4E2D\\u5C06\\u4E0D\\u4F1A\\u6709 \\u641C\\u7D22\\u6216\\u62A5\\u8868\\u7ED3\\u679C\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u70B9\\u51FB\\\"\\u7EE7\\u7EED\\u4FDD\\u5B58\\\"\\uFF0C\\u8868\\u793A\\u60A8\\u4E0D\\u9700\\u8981\\u5305\\u542B\\u8BA2\\u9605\\u6570\\u636E\\uFF0C\\u4EC5\\u4EE5\\\"\\u5185\\u5BB9\\\"\\u6846\\u4E2D\\u6587\\u6848\\u4F5C\\u4E3A\\u90AE\\u4EF6\\u4E3B\\u4F53\\u5185\\u5BB9\\uFF1B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u70B9\\u51FB\\\"\\u91CD\\u65B0\\u4FEE\\u6539\\\"\\uFF0C\\u505C\\u5728\\u5F53\\u524D\\u754C\\u9762\\uFF0C\\u60A8\\u53EF\\u5728\\\"\\u5185\\u5BB9\\\"\\u6846\\u4E2D\\u67D0\\u4E2A\\u4F4D\\u7F6E\\uFF0C\\u70B9\\u51FB\\u84DD\\u8272%\\u8BA2\\u9605\\u6570\\u636E%\\u63D2\\u5165\\u5360\\u4F4D\\u7B26\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n}\n_s(SubscribeEdit, \"O+g2RE1NFLRBerYBMqKocXIxTa4=\", false, function () {\n  return [useParams, Form.useForm, useQueryTeam599GetDataDictionary];\n});\n_c = SubscribeEdit;\nvar _c;\n$RefreshReg$(_c, \"SubscribeEdit\");", "map": {"version": 3, "names": ["CaretDownOutlined", "<PERSON><PERSON>", "Input", "Drawer", "Form", "Checkbox", "Avatar", "Select", "Space", "Dropdown", "DraggablePopUp", "React", "useEffect", "useRef", "useState", "useParams", "httpQuickAccess", "globalUtil", "eNodeTypeId", "eNodeType", "NoAvatarIcon", "TEditor", "TextModelModal", "PlanModal", "SubscribeContent", "ePriorityTypeObj", "ePriorityType", "eMsgChannel", "eMsgChannelObj", "eExecTimeType", "useQueryTeam599GetDataDictionary", "eSelectionListId", "isEmpty", "DraggableDrawer", "jsxDEV", "_jsxDEV", "TextArea", "SubscribeEdit", "allMember", "opType", "closeDrawer", "modalVisible", "editData", "isChange", "setIsChange", "<PERSON><PERSON><PERSON>", "typeList", "fromType", "ganttName", "nodeType", "_s", "_allMember$find", "_eNodeType$nodeType", "teamId", "nodeId", "form", "useForm", "codeInputRef", "Object", "create", "formItemLayout", "labelCol", "span", "style", "height", "marginBottom", "subscribeDataVisible", "setSubscribeDataVisible", "subscribeTextVisible", "setSubscribeTextVisible", "subscribePlanVisible", "setSubscribePlanVisible", "to<PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "ccuids", "setCcuids", "pushwhenEmpty", "setPushwhenEmpty", "subscribeData", "setSubscribeData", "timedPlan", "setTimedPlan", "description", "setDescription", "showTip", "setShowTip", "data", "dataDictionaries", "templateVariables", "workflowVariables", "isLoading", "isLoadingTeamXxx", "refetch", "refetchTeamXxx", "selectionId", "Selection_1970_datapush", "setFieldValue", "subscrTitle", "toUids", "split", "filter", "uid", "ccUids", "obj", "objId", "objType", "objName", "pushWhenEmpty", "cronId", "cronName", "cronDesc", "setTimeout", "_codeInputRef$current", "current", "<PERSON><PERSON><PERSON><PERSON>", "msgTpl", "_codeInputRef$current2", "avatarFormat", "src", "name", "marginRight", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onOk", "_codeInputRef$current3", "values", "getFieldsValue", "length", "warning", "msgtpl", "get<PERSON>ontent", "indexOf", "params", "msgChannel", "join", "execTimeType", "reportSubscribe", "projectNodeId", "team_523_save_sched_task_src", "then", "res", "resultCode", "_onClose", "_codeInputRef$current4", "_onChange", "value", "onChangeRemark", "e", "target", "className", "title", "width", "onClose", "open", "destroyOnClose", "closable", "centered", "footer", "display", "alignItems", "justifyContent", "children", "onChange", "checked", "placeholder", "borderRadius", "autoComplete", "type", "onClick", "submit", "labelAlign", "onFinish", "initialValues", "mail", "<PERSON><PERSON>", "label", "rules", "required", "message", "suffixIcon", "pointerEvents", "allowClear", "color", "marginLeft", "showSearch", "options", "high", "middle", "low", "disabled", "msg", "voice", "wechat", "inSiteMsg", "optionFilterProp", "dropdownMatchSelectWidth", "filterOption", "input", "option", "_option$key", "key", "toLowerCase", "includes", "map", "user", "toString", "avatar", "mode", "_option$key2", "find", "member", "marginTop", "ref", "placeholderText", "heightMin", "uploadParams", "moduleName", "nameEn", "autofocus", "insert", "setPushText", "onCancel", "textAlign", "margin", "cancelText", "okText", "fontSize", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/quickAcess/views/DataPush/EditDrawer/SubscribeEdit.jsx"], "sourcesContent": ["import { CaretDownOutlined } from \"@ant-design/icons\";\r\nimport { Button, Input, Drawer, Form, Checkbox, Avatar, Select, Space, Dropdown } from \"antd\";\r\nimport DraggablePopUp from \"@components/DraggablePopUp\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport '../DataPush.scss';\r\nimport * as httpQuickAccess from \"@common/api/http\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\nimport { eNodeTypeId, eNodeType } from \"@common/utils/TsbConfig\";\r\nimport { NoAvatarIcon } from '@common/components/IconUtil';\r\nimport TEditor from \"@common/components/TEditor/TEditor\";\r\nimport TextModelModal from './TextModel/TextModelModal';\r\nimport PlanModal from './Plan/PlanModal';\r\nimport SubscribeContent from './SubscribeContent/SubscribeContent';\r\nimport { ePriorityTypeObj, ePriorityType, eMsgChannel, eMsgChannelObj, eExecTimeType } from \"src/quickAcess/utils/Config\";\r\nimport { useQueryTeam599GetDataDictionary } from \"src/quickAcess/service/quickHooks\";\r\nimport { eSelectionListId } from \"@common/utils/enum\"\r\nimport { isEmpty } from \"@common/utils/ArrayUtils\"\r\nimport DraggableDrawer from \"@components/DraggableDrawer\";\r\n\r\nconst { TextArea } = Input\r\n\r\n// 新建/编辑系统订阅\r\nexport default function SubscribeEdit({ allMember,opType,closeDrawer,modalVisible,editData,isChange,setIsChange,selectedKey,typeList,fromType,ganttName, nodeType }) {\r\n  const { teamId,nodeId } = useParams();\r\n  const [form] = Form.useForm();\r\n  const codeInputRef = useRef(Object.create(null));\r\n  // Form.Item布局样式\r\n  const formItemLayout = { labelCol: { span: 2 } };\r\n\r\n  // Form.Item的样式\r\n  const style = { style: {  height: \"50%\", marginBottom: 20 } };\r\n\r\n  const [subscribeDataVisible, setSubscribeDataVisible] = useState(false);\r\n  const [subscribeTextVisible, setSubscribeTextVisible] = useState(false);\r\n  const [subscribePlanVisible, setSubscribePlanVisible] = useState(false);\r\n\r\n  const [touids,setTouids] = useState([]); // 收件人\r\n  const [ccuids,setCcuids] = useState([]); // 抄送人\r\n  const [pushwhenEmpty,setPushwhenEmpty] = useState(false);\r\n  const [subscribeData, setSubscribeData] = useState(null);\r\n  const [timedPlan, setTimedPlan] = useState(null);\r\n  const [description, setDescription] = useState(\"\"); // 备注\r\n\r\n  const [showTip,setShowTip] = useState(false);\r\n\r\n  const { data: { dataDictionaries=[], templateVariables=[], workflowVariables=[] } = { dataDictionaries: [], templateVariables: [], workflowVariables: [] }, isLoading: isLoadingTeamXxx,refetch: refetchTeamXxx} = useQueryTeam599GetDataDictionary({teamId, selectionId: eSelectionListId.Selection_1970_datapush});\r\n\r\n  useEffect(()=>{\r\n    if(modalVisible){\r\n      if(!!editData){\r\n        form.setFieldValue('subscrTitle',editData.subscrTitle||'');\r\n        // form.setFieldValue('description',editData.description||'');\r\n        setDescription(editData.description||'');\r\n        let toUids = (editData.toUids||'').split(',').filter(uid => !!uid);\r\n        setTouids([...toUids]);\r\n        let ccUids = (editData.ccUids||'').split(',').filter(uid => !!uid);\r\n        setCcuids([...ccUids]);\r\n        let obj = !!editData.objId ? {objId: editData.objId, objType: editData.objType, objName: editData.objName} : null\r\n        setSubscribeData(obj);\r\n        setPushwhenEmpty(editData.pushWhenEmpty == 1);\r\n        setTimedPlan({cronId: editData.cronId, cronName: editData.cronDesc});\r\n        setTimeout(()=>{\r\n          !!codeInputRef?.current?.setContent && codeInputRef.current.setContent(editData.msgTpl);\r\n        },500);\r\n      }else{\r\n        form.setFieldValue('subscrTitle','');\r\n        // form.setFieldValue('description','');\r\n        setDescription(\"\");\r\n        setTouids([]);\r\n        setCcuids([]);\r\n        setSubscribeData(null);\r\n        setPushwhenEmpty(false);\r\n        setTimedPlan(null);\r\n        setTimeout(()=>{\r\n          //todo 推送文案默认值\r\n          !!codeInputRef?.current?.setContent && codeInputRef.current.setContent(\"<p>你好，</p><p>%订阅数据%，</p><p>谢谢</p>\");\r\n        },500);\r\n      }\r\n    }\r\n  },[modalVisible]);\r\n\r\n  function avatarFormat(src, name) {\r\n    return (<Avatar style={{ marginRight: 5 }} src={src} icon={<NoAvatarIcon />} size={24} />);\r\n  }\r\n\r\n  function onOk(opType){\r\n    let values = form.getFieldsValue(true);\r\n    setShowTip(false);\r\n    if((touids||[]).length == 0){\r\n      globalUtil.warning('请选择收件人');\r\n      return\r\n    }\r\n    if(!timedPlan){\r\n      globalUtil.warning('请选择定时计划');\r\n      return\r\n    }\r\n    let msgtpl = codeInputRef?.current?.getContent()||'';\r\n    if(opType == 0){\r\n      if(!!subscribeData?.objId){\r\n        if((msgtpl||'').indexOf('%订阅数据%') == -1){\r\n          setShowTip(true);\r\n          return\r\n        }\r\n      }\r\n    }\r\n    let params = {\r\n      teamId: teamId,\r\n      subscrTitle: values.subscrTitle,\r\n      msgChannel: values.msgChannel,\r\n      objId: subscribeData?.objId,\r\n      objType: subscribeData?.objType,\r\n      objName: subscribeData?.objName,\r\n      msgTpl: msgtpl,\r\n      toUids: (touids||[]).join(','),\r\n      ccUids: (ccuids||[]).join(','),\r\n      execTimeType: eExecTimeType.reportSubscribe, // 报表订阅 \r\n      cronId: timedPlan.cronId,\r\n      description: description,\r\n      pushWhenEmpty: pushwhenEmpty ? 1 : 0,\r\n      projectNodeId: nodeId,\r\n      nodeType: nodeType,\r\n    }\r\n    if(!!editData){\r\n      params.nodeId = selectedKey\r\n    }\r\n    httpQuickAccess.team_523_save_sched_task_src(params).then(res => {\r\n      if(res.resultCode == 200){\r\n        closeDrawer(1);\r\n      }\r\n    });\r\n  }\r\n\r\n  function _onClose(){\r\n    closeDrawer(0,{\r\n      values: form.getFieldsValue(true), \r\n      objId: subscribeData?.objId, \r\n      msgTpl: codeInputRef?.current?.getContent()||'', \r\n      cronId: timedPlan?.cronId,\r\n      toUids: touids||[],\r\n      ccUids: ccuids||[],\r\n      pushwhenEmpty: pushwhenEmpty ? 1 : 0,\r\n    })\r\n  }\r\n\r\n  function _onChange(value,opType){\r\n    if(!value){\r\n      if(opType == 1){ // 编辑\r\n        setTimedPlan(null);\r\n      }\r\n      if(opType == 0){ // 新建\r\n        setSubscribeData(null);\r\n      }\r\n    }\r\n  }\r\n\r\n/*  // 模版\r\n  function onClickTemplateVariables (info) {\r\n    let theme = form.getFieldValue(\"theme\")??\"\";\r\n    console.log(\"theme\", theme, info.key);\r\n    form.setFieldValue(\"theme\", `${theme}${info.key}`);\r\n  }*/\r\n\r\n  function onChangeRemark (e) {\r\n   setDescription(e.target.value);\r\n  }\r\n\r\n  return (\r\n    <DraggableDrawer\r\n      className=\"tms-drawer\"\r\n      title={`${opType == 0 ? \"新建\" : '编辑'}${fromType == 'gantt' ? '进度推送' : '报表'}订阅`}\r\n      width={'70%'}\r\n      onClose={() => _onClose()}\r\n      open={modalVisible}\r\n      destroyOnClose //关闭时销毁子元素,避免重新打开数据不会刷新\r\n      closable\r\n      centered\r\n      footer={\r\n        <div style={{display:'flex',alignItems:'center',justifyContent:'end'}}>\r\n          <Space size={20}>\r\n          {!!subscribeData &&\r\n          <Checkbox\r\n            className=\"subscribe-way-mark-check\"\r\n            onChange={(e)=> setPushwhenEmpty(e.target.checked)} \r\n            checked={pushwhenEmpty}\r\n          >\r\n            订阅数据为空时，依然推送。\r\n          </Checkbox>\r\n          }\r\n          <Input value={description} placeholder=\"请输入备注\" style={{ borderRadius: 5, width:300 }} autoComplete=\"off\" onChange={onChangeRemark}></Input>\r\n          <Button style={{borderRadius:5}} type={'primary'} onClick={()=>{form.submit()}}>提交</Button>\r\n          </Space>\r\n        </div>\r\n      }\r\n    >\r\n      <Form\r\n        className=\"subscribe-way\"\r\n        form={form}\r\n        {...formItemLayout}\r\n        labelAlign=\"right\"\r\n        onFinish={()=>onOk(0)}\r\n        initialValues={{\r\n          msgChannel: eMsgChannel.mail, // 默认邮件\r\n        }}\r\n      >\r\n        <div className=\"label-header\">推送规则</div>\r\n        {/* 订阅名称 */}\r\n        <Form.Item label=\"名称\" rules={[{required: true, message: '名称不能为空'}]} {...style} name='subscrTitle'>\r\n          <Input style={{ width: 300,  borderRadius: 5 }} autoComplete=\"off\"/>\r\n        </Form.Item>\r\n        {/* 定时计划 */}\r\n        <Form.Item label=\"定时计划\" required {...style}>\r\n          <div style={{display:'flex',alignItems:'center'}}>\r\n            <Select\r\n              style={{ width: 300,  }}\r\n              placeholder='请选择'\r\n              value={timedPlan?.cronName}\r\n              suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}\r\n              onChange={(value)=>_onChange(value,1)}\r\n              allowClear\r\n              onClick={() => setSubscribePlanVisible(true)} />\r\n            <div style={{color:'#999',marginLeft:10}}>备注：订阅邮件发送时间会有1-5分钟的延迟</div>\r\n          </div>\r\n        </Form.Item>\r\n        <Form.Item label=\"优先级\" {...style} name=\"priority\">\r\n          <Select\r\n            showSearch\r\n            allowClear\r\n            style={{ width: 300 }}\r\n            placeholder='请选择'\r\n            options={[\r\n              ePriorityTypeObj[ePriorityType.high],\r\n              ePriorityTypeObj[ePriorityType.middle],\r\n              ePriorityTypeObj[ePriorityType.low],\r\n            ]}\r\n          />\r\n        </Form.Item>\r\n         <Form.Item label='系统推送通道' {...style} name=\"msgChannel\">\r\n           <Select\r\n            disabled={true}\r\n            showSearch\r\n            allowClear\r\n            style={{ width: 300 }}\r\n            placeholder='请选择'\r\n            options={[\r\n              eMsgChannelObj[eMsgChannel.mail],\r\n              eMsgChannelObj[eMsgChannel.msg],\r\n              eMsgChannelObj[eMsgChannel.voice],\r\n              eMsgChannelObj[eMsgChannel.wechat],\r\n              eMsgChannelObj[eMsgChannel.inSiteMsg],\r\n            ]}\r\n          />\r\n        </Form.Item>\r\n        <div className=\"label-header\">推送内容</div>\r\n        {/* <Form.Item label=\"主题\"  {...style}>\r\n          <Space size={10}>\r\n             <Form.Item name=\"theme\" noStyle>\r\n                <Input style={{ borderRadius: 5, width:300 }} autoComplete=\"off\"/>\r\n             </Form.Item>\r\n             <Dropdown\r\n              menu={{\r\n                items: templateVariables.map(obj => ({\r\n                  key: obj.value,\r\n                  value: obj.value,\r\n                  label: obj.value,\r\n                })),\r\n                onClick: onClickTemplateVariables\r\n              }}\r\n              placement=\"bottom\"\r\n              arrow={{\r\n                pointAtCenter: true,\r\n              }}\r\n            >\r\n            <Button type='link'>选择模版变量</Button>\r\n            </Dropdown>\r\n          </Space>\r\n        </Form.Item> */}\r\n        {/* 收件人 */}\r\n        <Form.Item label=\"收件人\" required {...style}>\r\n          <Select\r\n            showSearch\r\n            allowClear\r\n            // mode='multiple' fix tmsbug-11663:系统订阅，收件人，期望从多选改为单选\r\n            placeholder=\"请选择收件人\"\r\n            value={touids?.[0]}\r\n            onChange={(value) => setTouids( !!value ? [value] : [])}\r\n            optionFilterProp=\"children\"\r\n            dropdownMatchSelectWidth={false}\r\n            filterOption={(input, option) => (option?.key ?? '').toLowerCase().includes(input.toLowerCase())}\r\n            options={allMember.map(user => ({\r\n              value: user.key.toString(),\r\n              label: <div style={{ display: 'flex', alignItems: 'center' }}>{avatarFormat(user.avatar, user.label)}{user.label}</div>,\r\n              key: user.label,\r\n            }))}\r\n          />\r\n        </Form.Item>\r\n        {/* 抄送人 */}\r\n        <Form.Item label=\"抄送人\" {...style}>\r\n          <Select\r\n            showSearch\r\n            mode=\"multiple\"\r\n            allowClear\r\n            placeholder=\"请选择抄送人\"\r\n            value={ccuids}\r\n            onChange={(value) => setCcuids(value)}\r\n            optionFilterProp=\"children\"\r\n            filterOption={(input, option) => (option?.key ?? '').toLowerCase().includes(input.toLowerCase())}\r\n            options={allMember.map(user => ({\r\n              value: user.key.toString(),\r\n              label: <div style={{ display: 'flex', alignItems: 'center' }}>{avatarFormat(user.avatar, user.label)}{user.label}</div>,\r\n              key: user.label,\r\n            }))}\r\n          />\r\n        </Form.Item>\r\n        {/* 订阅数据源 */}\r\n        <Form.Item label=\"订阅数据源\" {...style}>\r\n          <div style={{display:'flex',alignItems:'center'}}>\r\n            <Select\r\n              style={{ width: 300 }}\r\n              open={false}\r\n              placeholder='请选择'\r\n              value={subscribeData?.objName}\r\n              suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}\r\n              onChange={(value)=>_onChange(value,0)}\r\n              allowClear\r\n              onClick={() => setSubscribeDataVisible(true)} />\r\n            <div style={{color:'#999',marginLeft:10}}>备注：系统发件时，会以收件人{`${!isEmpty(touids) ? `(${allMember.find(member => member.key == touids[0])?.label})` : \"\"}`}身份对订阅数据进行过滤。</div>\r\n          </div>\r\n        </Form.Item>\r\n        <Form.Item label=\"正文\" {...style}>\r\n          <div style={{marginTop:5}}>\r\n            <a onClick={() => setSubscribeTextVisible(true)}>选择模版</a>\r\n            <TEditor\r\n                ref={codeInputRef}\r\n                placeholderText={\"\"}\r\n                heightMin={'200px'}\r\n                uploadParams={{\r\n                  teamId: teamId,\r\n                  nodeId: nodeId,\r\n                  moduleName: eNodeType[nodeType]?.nameEn,\r\n                  objType: nodeType\r\n                }}\r\n                autofocus={false}\r\n            />\r\n            <div style={{color:'#999'}}>提示：占位符 <a onClick={()=>codeInputRef.current.insert('%订阅数据%')}>%订阅数据%</a>，代表需要订阅的搜索结果或报表数据，请点击占位符，将其包含进正文。</div>\r\n          </div>\r\n        </Form.Item>\r\n        {/* 备注 */}\r\n        {/* <Form.Item label=\"备注\" {...style} name='description'>\r\n          <TextArea style={{ minHeight: 200, borderRadius: 5 }} autoComplete=\"off\"/>\r\n        </Form.Item> */}\r\n      </Form>\r\n      <SubscribeContent\r\n          allMember={allMember}\r\n          subscribeDataVisible={subscribeDataVisible}\r\n          setSubscribeDataVisible={setSubscribeDataVisible}\r\n          subscribeData={subscribeData}\r\n          setSubscribeData={setSubscribeData}\r\n          typeList={typeList}\r\n          fromType={fromType}\r\n          ganttName={ganttName}\r\n      />\r\n      <TextModelModal\r\n        subscribeTextVisible={subscribeTextVisible}\r\n        setSubscribeTextVisible={setSubscribeTextVisible}\r\n        setPushText={codeInputRef}\r\n      />\r\n      <PlanModal\r\n        subscribePlanVisible={subscribePlanVisible}\r\n        setSubscribePlanVisible={setSubscribePlanVisible}\r\n        timedPlan={timedPlan}\r\n        setTimedPlan={setTimedPlan}\r\n      />\r\n      <DraggablePopUp\r\n        className=\"tms-modal\"\r\n        title='提示'\r\n        centered\r\n        width={300}\r\n        open={isChange}\r\n        onCancel={()=>setIsChange(false)}\r\n        onOk={()=>closeDrawer(0)}\r\n      >\r\n        <div style={{textAlign:'center',margin:'10px 0px'}}>\r\n          您正在编辑订阅，确定放弃编辑？\r\n        </div>\r\n      </DraggablePopUp>\r\n      <DraggablePopUp\r\n        className=\"tms-modal\"\r\n        title='提示'\r\n        centered\r\n        width={600}\r\n        open={showTip}\r\n        cancelText='继续保存'\r\n        onCancel={()=>onOk(1)}\r\n        okText='重新修改'\r\n        onOk={()=>setShowTip(false)}\r\n      >\r\n        <div style={{margin:'10px 0px',fontSize:12}}>\r\n          <div>内容框中，未检测到有 %订阅数据% 占位符，若不含此占位符，订阅邮件中将不会有 搜索或报表结果。</div>\r\n          <div>点击\"继续保存\"，表示您不需要包含订阅数据，仅以\"内容\"框中文案作为邮件主体内容；</div>\r\n          <div>点击\"重新修改\"，停在当前界面，您可在\"内容\"框中某个位置，点击蓝色%订阅数据%插入占位符。</div>\r\n        </div>\r\n      </DraggablePopUp>\r\n    </DraggableDrawer>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,MAAM;AAC7F,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAO,kBAAkB;AACzB,OAAO,KAAKC,eAAe,MAAM,kBAAkB;AACnD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,WAAW,EAAEC,SAAS,QAAQ,yBAAyB;AAChE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,OAAOC,OAAO,MAAM,oCAAoC;AACxD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,cAAc,EAAEC,aAAa,QAAQ,6BAA6B;AACzH,SAASC,gCAAgC,QAAQ,mCAAmC;AACpF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAOC,eAAe,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAM;EAAEC;AAAS,CAAC,GAAGlC,KAAK;;AAE1B;AACA,eAAe,SAASmC,aAAaA,CAAC;EAAEC,SAAS;EAACC,MAAM;EAACC,WAAW;EAACC,YAAY;EAACC,QAAQ;EAACC,QAAQ;EAACC,WAAW;EAACC,WAAW;EAACC,QAAQ;EAACC,QAAQ;EAACC,SAAS;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,mBAAA;EACnK,MAAM;IAAEC,MAAM;IAACC;EAAO,CAAC,GAAGvC,SAAS,CAAC,CAAC;EACrC,MAAM,CAACwC,IAAI,CAAC,GAAGnD,IAAI,CAACoD,OAAO,CAAC,CAAC;EAC7B,MAAMC,YAAY,GAAG5C,MAAM,CAAC6C,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EAChD;EACA,MAAMC,cAAc,GAAG;IAAEC,QAAQ,EAAE;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC;;EAEhD;EACA,MAAMC,KAAK,GAAG;IAAEA,KAAK,EAAE;MAAGC,MAAM,EAAE,KAAK;MAAEC,YAAY,EAAE;IAAG;EAAE,CAAC;EAE7D,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACsD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACwD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAEvE,MAAM,CAAC0D,MAAM,EAACC,SAAS,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACzC,MAAM,CAAC4D,MAAM,EAACC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACzC,MAAM,CAAC8D,aAAa,EAACC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACxD,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpD,MAAM,CAACsE,OAAO,EAACC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAE5C,MAAM;IAAEwE,IAAI,EAAE;MAAEC,gBAAgB,GAAC,EAAE;MAAEC,iBAAiB,GAAC,EAAE;MAAEC,iBAAiB,GAAC;IAAG,CAAC,GAAG;MAAEF,gBAAgB,EAAE,EAAE;MAAEC,iBAAiB,EAAE,EAAE;MAAEC,iBAAiB,EAAE;IAAG,CAAC;IAAEC,SAAS,EAAEC,gBAAgB;IAACC,OAAO,EAAEC;EAAc,CAAC,GAAG/D,gCAAgC,CAAC;IAACuB,MAAM;IAAEyC,WAAW,EAAE/D,gBAAgB,CAACgE;EAAuB,CAAC,CAAC;EAEpTnF,SAAS,CAAC,MAAI;IACZ,IAAG6B,YAAY,EAAC;MACd,IAAG,CAAC,CAACC,QAAQ,EAAC;QACZa,IAAI,CAACyC,aAAa,CAAC,aAAa,EAACtD,QAAQ,CAACuD,WAAW,IAAE,EAAE,CAAC;QAC1D;QACAd,cAAc,CAACzC,QAAQ,CAACwC,WAAW,IAAE,EAAE,CAAC;QACxC,IAAIgB,MAAM,GAAG,CAACxD,QAAQ,CAACwD,MAAM,IAAE,EAAE,EAAEC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC;QAClE5B,SAAS,CAAC,CAAC,GAAGyB,MAAM,CAAC,CAAC;QACtB,IAAII,MAAM,GAAG,CAAC5D,QAAQ,CAAC4D,MAAM,IAAE,EAAE,EAAEH,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC;QAClE1B,SAAS,CAAC,CAAC,GAAG2B,MAAM,CAAC,CAAC;QACtB,IAAIC,GAAG,GAAG,CAAC,CAAC7D,QAAQ,CAAC8D,KAAK,GAAG;UAACA,KAAK,EAAE9D,QAAQ,CAAC8D,KAAK;UAAEC,OAAO,EAAE/D,QAAQ,CAAC+D,OAAO;UAAEC,OAAO,EAAEhE,QAAQ,CAACgE;QAAO,CAAC,GAAG,IAAI;QACjH3B,gBAAgB,CAACwB,GAAG,CAAC;QACrB1B,gBAAgB,CAACnC,QAAQ,CAACiE,aAAa,IAAI,CAAC,CAAC;QAC7C1B,YAAY,CAAC;UAAC2B,MAAM,EAAElE,QAAQ,CAACkE,MAAM;UAAEC,QAAQ,EAAEnE,QAAQ,CAACoE;QAAQ,CAAC,CAAC;QACpEC,UAAU,CAAC,MAAI;UAAA,IAAAC,qBAAA;UACb,CAAC,EAACvD,YAAY,aAAZA,YAAY,gBAAAuD,qBAAA,GAAZvD,YAAY,CAAEwD,OAAO,cAAAD,qBAAA,eAArBA,qBAAA,CAAuBE,UAAU,KAAIzD,YAAY,CAACwD,OAAO,CAACC,UAAU,CAACxE,QAAQ,CAACyE,MAAM,CAAC;QACzF,CAAC,EAAC,GAAG,CAAC;MACR,CAAC,MAAI;QACH5D,IAAI,CAACyC,aAAa,CAAC,aAAa,EAAC,EAAE,CAAC;QACpC;QACAb,cAAc,CAAC,EAAE,CAAC;QAClBV,SAAS,CAAC,EAAE,CAAC;QACbE,SAAS,CAAC,EAAE,CAAC;QACbI,gBAAgB,CAAC,IAAI,CAAC;QACtBF,gBAAgB,CAAC,KAAK,CAAC;QACvBI,YAAY,CAAC,IAAI,CAAC;QAClB8B,UAAU,CAAC,MAAI;UAAA,IAAAK,sBAAA;UACb;UACA,CAAC,EAAC3D,YAAY,aAAZA,YAAY,gBAAA2D,sBAAA,GAAZ3D,YAAY,CAAEwD,OAAO,cAAAG,sBAAA,eAArBA,sBAAA,CAAuBF,UAAU,KAAIzD,YAAY,CAACwD,OAAO,CAACC,UAAU,CAAC,mCAAmC,CAAC;QAC7G,CAAC,EAAC,GAAG,CAAC;MACR;IACF;EACF,CAAC,EAAC,CAACzE,YAAY,CAAC,CAAC;EAEjB,SAAS4E,YAAYA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAC/B,oBAAQpF,OAAA,CAAC7B,MAAM;MAACyD,KAAK,EAAE;QAAEyD,WAAW,EAAE;MAAE,CAAE;MAACF,GAAG,EAAEA,GAAI;MAACG,IAAI,eAAEtF,OAAA,CAACf,YAAY;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAACC,IAAI,EAAE;IAAG;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3F;EAEA,SAASE,IAAIA,CAACxF,MAAM,EAAC;IAAA,IAAAyF,sBAAA;IACnB,IAAIC,MAAM,GAAG1E,IAAI,CAAC2E,cAAc,CAAC,IAAI,CAAC;IACtC7C,UAAU,CAAC,KAAK,CAAC;IACjB,IAAG,CAACb,MAAM,IAAE,EAAE,EAAE2D,MAAM,IAAI,CAAC,EAAC;MAC1BlH,UAAU,CAACmH,OAAO,CAAC,QAAQ,CAAC;MAC5B;IACF;IACA,IAAG,CAACpD,SAAS,EAAC;MACZ/D,UAAU,CAACmH,OAAO,CAAC,SAAS,CAAC;MAC7B;IACF;IACA,IAAIC,MAAM,GAAG,CAAA5E,YAAY,aAAZA,YAAY,wBAAAuE,sBAAA,GAAZvE,YAAY,CAAEwD,OAAO,cAAAe,sBAAA,uBAArBA,sBAAA,CAAuBM,UAAU,CAAC,CAAC,KAAE,EAAE;IACpD,IAAG/F,MAAM,IAAI,CAAC,EAAC;MACb,IAAG,CAAC,EAACuC,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAE0B,KAAK,GAAC;QACxB,IAAG,CAAC6B,MAAM,IAAE,EAAE,EAAEE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAC;UACtClD,UAAU,CAAC,IAAI,CAAC;UAChB;QACF;MACF;IACF;IACA,IAAImD,MAAM,GAAG;MACXnF,MAAM,EAAEA,MAAM;MACd4C,WAAW,EAAEgC,MAAM,CAAChC,WAAW;MAC/BwC,UAAU,EAAER,MAAM,CAACQ,UAAU;MAC7BjC,KAAK,EAAE1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0B,KAAK;MAC3BC,OAAO,EAAE3B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2B,OAAO;MAC/BC,OAAO,EAAE5B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4B,OAAO;MAC/BS,MAAM,EAAEkB,MAAM;MACdnC,MAAM,EAAE,CAAC1B,MAAM,IAAE,EAAE,EAAEkE,IAAI,CAAC,GAAG,CAAC;MAC9BpC,MAAM,EAAE,CAAC5B,MAAM,IAAE,EAAE,EAAEgE,IAAI,CAAC,GAAG,CAAC;MAC9BC,YAAY,EAAE9G,aAAa,CAAC+G,eAAe;MAAE;MAC7ChC,MAAM,EAAE5B,SAAS,CAAC4B,MAAM;MACxB1B,WAAW,EAAEA,WAAW;MACxByB,aAAa,EAAE/B,aAAa,GAAG,CAAC,GAAG,CAAC;MACpCiE,aAAa,EAAEvF,MAAM;MACrBL,QAAQ,EAAEA;IACZ,CAAC;IACD,IAAG,CAAC,CAACP,QAAQ,EAAC;MACZ8F,MAAM,CAAClF,MAAM,GAAGT,WAAW;IAC7B;IACA7B,eAAe,CAAC8H,4BAA4B,CAACN,MAAM,CAAC,CAACO,IAAI,CAACC,GAAG,IAAI;MAC/D,IAAGA,GAAG,CAACC,UAAU,IAAI,GAAG,EAAC;QACvBzG,WAAW,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC;EACJ;EAEA,SAAS0G,QAAQA,CAAA,EAAE;IAAA,IAAAC,sBAAA;IACjB3G,WAAW,CAAC,CAAC,EAAC;MACZyF,MAAM,EAAE1E,IAAI,CAAC2E,cAAc,CAAC,IAAI,CAAC;MACjC1B,KAAK,EAAE1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0B,KAAK;MAC3BW,MAAM,EAAE,CAAA1D,YAAY,aAAZA,YAAY,wBAAA0F,sBAAA,GAAZ1F,YAAY,CAAEwD,OAAO,cAAAkC,sBAAA,uBAArBA,sBAAA,CAAuBb,UAAU,CAAC,CAAC,KAAE,EAAE;MAC/C1B,MAAM,EAAE5B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE4B,MAAM;MACzBV,MAAM,EAAE1B,MAAM,IAAE,EAAE;MAClB8B,MAAM,EAAE5B,MAAM,IAAE,EAAE;MAClBE,aAAa,EAAEA,aAAa,GAAG,CAAC,GAAG;IACrC,CAAC,CAAC;EACJ;EAEA,SAASwE,SAASA,CAACC,KAAK,EAAC9G,MAAM,EAAC;IAC9B,IAAG,CAAC8G,KAAK,EAAC;MACR,IAAG9G,MAAM,IAAI,CAAC,EAAC;QAAE;QACf0C,YAAY,CAAC,IAAI,CAAC;MACpB;MACA,IAAG1C,MAAM,IAAI,CAAC,EAAC;QAAE;QACfwC,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF;EACF;;EAEF;AACA;AACA;AACA;AACA;AACA;;EAEE,SAASuE,cAAcA,CAAEC,CAAC,EAAE;IAC3BpE,cAAc,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;EAC/B;EAEA,oBACElH,OAAA,CAACF,eAAe;IACdwH,SAAS,EAAC,YAAY;IACtBC,KAAK,EAAE,GAAGnH,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,GAAGQ,QAAQ,IAAI,OAAO,GAAG,MAAM,GAAG,IAAI,IAAK;IAC9E4G,KAAK,EAAE,KAAM;IACbC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,CAAE;IAC1BW,IAAI,EAAEpH,YAAa;IACnBqH,cAAc,OAAC;IAAA;IACfC,QAAQ;IACRC,QAAQ;IACRC,MAAM,eACJ9H,OAAA;MAAK4B,KAAK,EAAE;QAACmG,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,cAAc,EAAC;MAAK,CAAE;MAAAC,QAAA,eACpElI,OAAA,CAAC3B,KAAK;QAACsH,IAAI,EAAE,EAAG;QAAAuC,QAAA,GACf,CAAC,CAACvF,aAAa,iBAChB3C,OAAA,CAAC9B,QAAQ;UACPoJ,SAAS,EAAC,0BAA0B;UACpCa,QAAQ,EAAGf,CAAC,IAAI1E,gBAAgB,CAAC0E,CAAC,CAACC,MAAM,CAACe,OAAO,CAAE;UACnDA,OAAO,EAAE3F,aAAc;UAAAyF,QAAA,EACxB;QAED;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAEX1F,OAAA,CAACjC,KAAK;UAACmJ,KAAK,EAAEnE,WAAY;UAACsF,WAAW,EAAC,gCAAO;UAACzG,KAAK,EAAE;YAAE0G,YAAY,EAAE,CAAC;YAAEd,KAAK,EAAC;UAAI,CAAE;UAACe,YAAY,EAAC,KAAK;UAACJ,QAAQ,EAAEhB;QAAe;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC3I1F,OAAA,CAAClC,MAAM;UAAC8D,KAAK,EAAE;YAAC0G,YAAY,EAAC;UAAC,CAAE;UAACE,IAAI,EAAE,SAAU;UAACC,OAAO,EAAEA,CAAA,KAAI;YAACrH,IAAI,CAACsH,MAAM,CAAC,CAAC;UAAA,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;IAAAwC,QAAA,gBAEDlI,OAAA,CAAC/B,IAAI;MACHqJ,SAAS,EAAC,eAAe;MACzBlG,IAAI,EAAEA,IAAK;MAAA,GACPK,cAAc;MAClBkH,UAAU,EAAC,OAAO;MAClBC,QAAQ,EAAEA,CAAA,KAAIhD,IAAI,CAAC,CAAC,CAAE;MACtBiD,aAAa,EAAE;QACbvC,UAAU,EAAE9G,WAAW,CAACsJ,IAAI,CAAE;MAChC,CAAE;MAAAZ,QAAA,gBAEFlI,OAAA;QAAKsH,SAAS,EAAC,cAAc;QAAAY,QAAA,EAAC;MAAI;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAExC1F,OAAA,CAAC/B,IAAI,CAAC8K,IAAI;QAACC,KAAK,EAAC,cAAI;QAACC,KAAK,EAAE,CAAC;UAACC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAQ,CAAC,CAAE;QAAA,GAAKvH,KAAK;QAAEwD,IAAI,EAAC,aAAa;QAAA8C,QAAA,eAC/FlI,OAAA,CAACjC,KAAK;UAAC6D,KAAK,EAAE;YAAE4F,KAAK,EAAE,GAAG;YAAGc,YAAY,EAAE;UAAE,CAAE;UAACC,YAAY,EAAC;QAAK;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAEZ1F,OAAA,CAAC/B,IAAI,CAAC8K,IAAI;QAACC,KAAK,EAAC,0BAAM;QAACE,QAAQ;QAAA,GAAKtH,KAAK;QAAAsG,QAAA,eACxClI,OAAA;UAAK4B,KAAK,EAAE;YAACmG,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC;UAAQ,CAAE;UAAAE,QAAA,gBAC/ClI,OAAA,CAAC5B,MAAM;YACLwD,KAAK,EAAE;cAAE4F,KAAK,EAAE;YAAM,CAAE;YACxBa,WAAW,EAAC,oBAAK;YACjBnB,KAAK,EAAErE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE6B,QAAS;YAC3B0E,UAAU,eAAEpJ,OAAA,CAACnC,iBAAiB;cAAC+D,KAAK,EAAE;gBAAEyH,aAAa,EAAE;cAAO;YAAE;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACpEyC,QAAQ,EAAGjB,KAAK,IAAGD,SAAS,CAACC,KAAK,EAAC,CAAC,CAAE;YACtCoC,UAAU;YACVb,OAAO,EAAEA,CAAA,KAAMrG,uBAAuB,CAAC,IAAI;UAAE;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClD1F,OAAA;YAAK4B,KAAK,EAAE;cAAC2H,KAAK,EAAC,MAAM;cAACC,UAAU,EAAC;YAAE,CAAE;YAAAtB,QAAA,EAAC;UAAqB;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZ1F,OAAA,CAAC/B,IAAI,CAAC8K,IAAI;QAACC,KAAK,EAAC,oBAAK;QAAA,GAAKpH,KAAK;QAAEwD,IAAI,EAAC,UAAU;QAAA8C,QAAA,eAC/ClI,OAAA,CAAC5B,MAAM;UACLqL,UAAU;UACVH,UAAU;UACV1H,KAAK,EAAE;YAAE4F,KAAK,EAAE;UAAI,CAAE;UACtBa,WAAW,EAAC,oBAAK;UACjBqB,OAAO,EAAE,CACPpK,gBAAgB,CAACC,aAAa,CAACoK,IAAI,CAAC,EACpCrK,gBAAgB,CAACC,aAAa,CAACqK,MAAM,CAAC,EACtCtK,gBAAgB,CAACC,aAAa,CAACsK,GAAG,CAAC;QACnC;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eACX1F,OAAA,CAAC/B,IAAI,CAAC8K,IAAI;QAACC,KAAK,EAAC,sCAAQ;QAAA,GAAKpH,KAAK;QAAEwD,IAAI,EAAC,YAAY;QAAA8C,QAAA,eACpDlI,OAAA,CAAC5B,MAAM;UACN0L,QAAQ,EAAE,IAAK;UACfL,UAAU;UACVH,UAAU;UACV1H,KAAK,EAAE;YAAE4F,KAAK,EAAE;UAAI,CAAE;UACtBa,WAAW,EAAC,oBAAK;UACjBqB,OAAO,EAAE,CACPjK,cAAc,CAACD,WAAW,CAACsJ,IAAI,CAAC,EAChCrJ,cAAc,CAACD,WAAW,CAACuK,GAAG,CAAC,EAC/BtK,cAAc,CAACD,WAAW,CAACwK,KAAK,CAAC,EACjCvK,cAAc,CAACD,WAAW,CAACyK,MAAM,CAAC,EAClCxK,cAAc,CAACD,WAAW,CAAC0K,SAAS,CAAC;QACrC;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eACZ1F,OAAA;QAAKsH,SAAS,EAAC,cAAc;QAAAY,QAAA,EAAC;MAAI;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAyBxC1F,OAAA,CAAC/B,IAAI,CAAC8K,IAAI;QAACC,KAAK,EAAC,oBAAK;QAACE,QAAQ;QAAA,GAAKtH,KAAK;QAAAsG,QAAA,eACvClI,OAAA,CAAC5B,MAAM;UACLqL,UAAU;UACVH,UAAU;UACV;UAAA;UACAjB,WAAW,EAAC,sCAAQ;UACpBnB,KAAK,EAAE7E,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,CAAC,CAAE;UACnB8F,QAAQ,EAAGjB,KAAK,IAAK5E,SAAS,CAAE,CAAC,CAAC4E,KAAK,GAAG,CAACA,KAAK,CAAC,GAAG,EAAE,CAAE;UACxDiD,gBAAgB,EAAC,UAAU;UAC3BC,wBAAwB,EAAE,KAAM;UAChCC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM;YAAA,IAAAC,WAAA;YAAA,OAAK,EAAAA,WAAA,GAACD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,GAAG,cAAAD,WAAA,cAAAA,WAAA,GAAI,EAAE,EAAEE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,KAAK,CAACI,WAAW,CAAC,CAAC,CAAC;UAAA,CAAC;UACjGhB,OAAO,EAAEvJ,SAAS,CAACyK,GAAG,CAACC,IAAI,KAAK;YAC9B3D,KAAK,EAAE2D,IAAI,CAACJ,GAAG,CAACK,QAAQ,CAAC,CAAC;YAC1B9B,KAAK,eAAEhJ,OAAA;cAAK4B,KAAK,EAAE;gBAAEmG,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAE,QAAA,GAAEhD,YAAY,CAAC2F,IAAI,CAACE,MAAM,EAAEF,IAAI,CAAC7B,KAAK,CAAC,EAAE6B,IAAI,CAAC7B,KAAK;YAAA;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;YACvH+E,GAAG,EAAEI,IAAI,CAAC7B;UACZ,CAAC,CAAC;QAAE;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZ1F,OAAA,CAAC/B,IAAI,CAAC8K,IAAI;QAACC,KAAK,EAAC,oBAAK;QAAA,GAAKpH,KAAK;QAAAsG,QAAA,eAC9BlI,OAAA,CAAC5B,MAAM;UACLqL,UAAU;UACVuB,IAAI,EAAC,UAAU;UACf1B,UAAU;UACVjB,WAAW,EAAC,sCAAQ;UACpBnB,KAAK,EAAE3E,MAAO;UACd4F,QAAQ,EAAGjB,KAAK,IAAK1E,SAAS,CAAC0E,KAAK,CAAE;UACtCiD,gBAAgB,EAAC,UAAU;UAC3BE,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM;YAAA,IAAAU,YAAA;YAAA,OAAK,EAAAA,YAAA,GAACV,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,GAAG,cAAAQ,YAAA,cAAAA,YAAA,GAAI,EAAE,EAAEP,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,KAAK,CAACI,WAAW,CAAC,CAAC,CAAC;UAAA,CAAC;UACjGhB,OAAO,EAAEvJ,SAAS,CAACyK,GAAG,CAACC,IAAI,KAAK;YAC9B3D,KAAK,EAAE2D,IAAI,CAACJ,GAAG,CAACK,QAAQ,CAAC,CAAC;YAC1B9B,KAAK,eAAEhJ,OAAA;cAAK4B,KAAK,EAAE;gBAAEmG,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAE,QAAA,GAAEhD,YAAY,CAAC2F,IAAI,CAACE,MAAM,EAAEF,IAAI,CAAC7B,KAAK,CAAC,EAAE6B,IAAI,CAAC7B,KAAK;YAAA;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;YACvH+E,GAAG,EAAEI,IAAI,CAAC7B;UACZ,CAAC,CAAC;QAAE;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZ1F,OAAA,CAAC/B,IAAI,CAAC8K,IAAI;QAACC,KAAK,EAAC,gCAAO;QAAA,GAAKpH,KAAK;QAAAsG,QAAA,eAChClI,OAAA;UAAK4B,KAAK,EAAE;YAACmG,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC;UAAQ,CAAE;UAAAE,QAAA,gBAC/ClI,OAAA,CAAC5B,MAAM;YACLwD,KAAK,EAAE;cAAE4F,KAAK,EAAE;YAAI,CAAE;YACtBE,IAAI,EAAE,KAAM;YACZW,WAAW,EAAC,oBAAK;YACjBnB,KAAK,EAAEvE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4B,OAAQ;YAC9B6E,UAAU,eAAEpJ,OAAA,CAACnC,iBAAiB;cAAC+D,KAAK,EAAE;gBAAEyH,aAAa,EAAE;cAAO;YAAE;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACpEyC,QAAQ,EAAGjB,KAAK,IAAGD,SAAS,CAACC,KAAK,EAAC,CAAC,CAAE;YACtCoC,UAAU;YACVb,OAAO,EAAEA,CAAA,KAAMzG,uBAAuB,CAAC,IAAI;UAAE;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClD1F,OAAA;YAAK4B,KAAK,EAAE;cAAC2H,KAAK,EAAC,MAAM;cAACC,UAAU,EAAC;YAAE,CAAE;YAAAtB,QAAA,GAAC,sFAAc,EAAC,GAAG,CAACrI,OAAO,CAACwC,MAAM,CAAC,GAAG,KAAArB,eAAA,GAAIb,SAAS,CAAC+K,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACV,GAAG,IAAIpI,MAAM,CAAC,CAAC,CAAC,CAAC,cAAArB,eAAA,uBAAjDA,eAAA,CAAmDgI,KAAK,GAAG,GAAG,EAAE,EAAE,EAAC,0EAAY;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZ1F,OAAA,CAAC/B,IAAI,CAAC8K,IAAI;QAACC,KAAK,EAAC,cAAI;QAAA,GAAKpH,KAAK;QAAAsG,QAAA,eAC7BlI,OAAA;UAAK4B,KAAK,EAAE;YAACwJ,SAAS,EAAC;UAAC,CAAE;UAAAlD,QAAA,gBACxBlI,OAAA;YAAGyI,OAAO,EAAEA,CAAA,KAAMvG,uBAAuB,CAAC,IAAI,CAAE;YAAAgG,QAAA,EAAC;UAAI;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzD1F,OAAA,CAACd,OAAO;YACJmM,GAAG,EAAE/J,YAAa;YAClBgK,eAAe,EAAE,EAAG;YACpBC,SAAS,EAAE,OAAQ;YACnBC,YAAY,EAAE;cACZtK,MAAM,EAAEA,MAAM;cACdC,MAAM,EAAEA,MAAM;cACdsK,UAAU,GAAAxK,mBAAA,GAAEjC,SAAS,CAAC8B,QAAQ,CAAC,cAAAG,mBAAA,uBAAnBA,mBAAA,CAAqByK,MAAM;cACvCpH,OAAO,EAAExD;YACX,CAAE;YACF6K,SAAS,EAAE;UAAM;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACF1F,OAAA;YAAK4B,KAAK,EAAE;cAAC2H,KAAK,EAAC;YAAM,CAAE;YAAArB,QAAA,GAAC,uCAAO,eAAAlI,OAAA;cAAGyI,OAAO,EAAEA,CAAA,KAAInH,YAAY,CAACwD,OAAO,CAAC8G,MAAM,CAAC,QAAQ,CAAE;cAAA1D,QAAA,EAAC;YAAM;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,0MAAiC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKR,CAAC,eACP1F,OAAA,CAACX,gBAAgB;MACbc,SAAS,EAAEA,SAAU;MACrB4B,oBAAoB,EAAEA,oBAAqB;MAC3CC,uBAAuB,EAAEA,uBAAwB;MACjDW,aAAa,EAAEA,aAAc;MAC7BC,gBAAgB,EAAEA,gBAAiB;MACnCjC,QAAQ,EAAEA,QAAS;MACnBC,QAAQ,EAAEA,QAAS;MACnBC,SAAS,EAAEA;IAAU;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eACF1F,OAAA,CAACb,cAAc;MACb8C,oBAAoB,EAAEA,oBAAqB;MAC3CC,uBAAuB,EAAEA,uBAAwB;MACjD2J,WAAW,EAAEvK;IAAa;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eACF1F,OAAA,CAACZ,SAAS;MACR+C,oBAAoB,EAAEA,oBAAqB;MAC3CC,uBAAuB,EAAEA,uBAAwB;MACjDS,SAAS,EAAEA,SAAU;MACrBC,YAAY,EAAEA;IAAa;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eACF1F,OAAA,CAACzB,cAAc;MACb+I,SAAS,EAAC,WAAW;MACrBC,KAAK,EAAC,cAAI;MACVM,QAAQ;MACRL,KAAK,EAAE,GAAI;MACXE,IAAI,EAAElH,QAAS;MACfsL,QAAQ,EAAEA,CAAA,KAAIrL,WAAW,CAAC,KAAK,CAAE;MACjCmF,IAAI,EAAEA,CAAA,KAAIvF,WAAW,CAAC,CAAC,CAAE;MAAA6H,QAAA,eAEzBlI,OAAA;QAAK4B,KAAK,EAAE;UAACmK,SAAS,EAAC,QAAQ;UAACC,MAAM,EAAC;QAAU,CAAE;QAAA9D,QAAA,EAAC;MAEpD;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eACjB1F,OAAA,CAACzB,cAAc;MACb+I,SAAS,EAAC,WAAW;MACrBC,KAAK,EAAC,cAAI;MACVM,QAAQ;MACRL,KAAK,EAAE,GAAI;MACXE,IAAI,EAAEzE,OAAQ;MACdgJ,UAAU,EAAC,0BAAM;MACjBH,QAAQ,EAAEA,CAAA,KAAIlG,IAAI,CAAC,CAAC,CAAE;MACtBsG,MAAM,EAAC,0BAAM;MACbtG,IAAI,EAAEA,CAAA,KAAI1C,UAAU,CAAC,KAAK,CAAE;MAAAgF,QAAA,eAE5BlI,OAAA;QAAK4B,KAAK,EAAE;UAACoK,MAAM,EAAC,UAAU;UAACG,QAAQ,EAAC;QAAE,CAAE;QAAAjE,QAAA,gBAC1ClI,OAAA;UAAAkI,QAAA,EAAK;QAAgD;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3D1F,OAAA;UAAAkI,QAAA,EAAK;QAAyC;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD1F,OAAA;UAAAkI,QAAA,EAAK;QAA8C;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEtB;AAAC3E,EAAA,CA9XuBb,aAAa;EAAA,QACTtB,SAAS,EACpBX,IAAI,CAACoD,OAAO,EAqBwL1B,gCAAgC;AAAA;AAAAyM,EAAA,GAvB7NlM,aAAa;AAAA,IAAAkM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}