{"ast": null, "code": "import{useEffect,useState,useRef}from\"react\";import{Modal,Form,Input,Radio,Badge,InputNumber,Row,Image,Button,Table,Checkbox,Dropdown,Menu,Card,Tooltip,Popconfirm}from\"antd\";import DraggablePopUp from\"@components/DraggablePopUp\";import{useNavigate,useParams}from\"react-router-dom\";import{useDispatch}from\"react-redux\";import{getTeamList}from\"@/team/store/actionCreators\";import{CheckCircleOutlined,CloseOutlined,ExclamationCircleOutlined,QuestionCircleOutlined,DownOutlined}from'@ant-design/icons';import{team_703_calc_price,team_704_submit_order,team_705_get_order_status,team_711_get_team_product_list,team_735_bind_user_coupon,team_736_unbind_user_coupon}from\"@common/api/http\";import{setting_105_get_team_detail_query,team_701_get_product_list_query,team_706_get_free_team_count_by_user_query,useQueryGetUserValidCoupon}from\"@common/api/query/query\";import{useDebounce}from\"@common/hook\";import QRCode from\"qrcode\";import\"./CreateTeam.scss\";import TLoading from\"./TLoading\";import{useQueries}from\"@tanstack/react-query\";import moment from\"moment\";import{globalUtil}from\"@common/utils/globalUtil\";import{eCouponType,eOrderStatus,eProductGroupId,eProductId,eProductStatus}from\"@common/utils/enum\";import DraggableDrawer from\"@components/DraggableDrawer\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Search}=Input;const{Column,ColumnGroup}=Table;export const CREATETYPE_CREATE=0;// 创建团队\nexport const CREATETYPE_UPGRADE=1;// 升级至企业版\n// export const CREATETYPE_EXPAND = 2; // 成员扩容\n// export const CREATETYPE_RENEWAL = 3; // \"应用\"续费\n// export const CREATETYPE_BUY = 4; // \"应用\"新购\nconst formItemLayout={labelCol:{span:0}};/**\r\n * @description 创建工作区\r\n * @param {*} param0 \r\n * @returns \r\n */export function CreateTeam(_ref){var _team706Result$data,_team706Result$data2,_myCouponList$find,_myCouponList$find2,_myCouponList$find3,_myCouponList$find4;let{onCancel,onOk,teamId,type,priceData,productList,setIsChange}=_ref;const{teamId:localTeamId}=useParams();const queryResult=useQueries({queries:[team_701_get_product_list_query(),team_706_get_free_team_count_by_user_query(),{...setting_105_get_team_detail_query(teamId),enabled:!!teamId}// team_702_get_team_package_duration_rebate_query(), //20231025 Jim Song, 月度折扣接口已废弃，伴随\"应用\"列表接口一起范围\n]});const[team701Result,team706Result,setting105Result]=queryResult;const{data:{couponList}={couponList:[]},refetch:refetchGetUserValidCoupon}=useQueryGetUserValidCoupon(teamId);const navigate=useNavigate();const dispatch=useDispatch();const[form]=Form.useForm();const[discountForm]=Form.useForm();const[cdiskCheck,setCdiskCheck]=useState(true);const[priceInfo,setPriceInfo]=useState({originalPrice:0,regularAmt:0,promoAmt:0,discountReduction:0,payPrice:0,couponReduction:0,resultCode:null,resultMessage:null});const freeAuthUserQuota1000=1000;//1000个免费用户数\n// 是否确认购买\nconst[isPaying,setIsPaying]=useState(false);const[payInfo,setPayInfo]=useState();const[showPriceDetail,setShowPriceDetail]=useState(false);const vipFlg=Form.useWatch('isEnterpriseEdition',form);const[discountCodeVisible,setDiscountCodeVisible]=useState(false);const[payCode,setPayCode]=useState({orderId:null});// const [payCode1,setPayCode1] = useState({\n//   orderInfo:{\n//     orderId: null,\n//   },\n//   payUrl: null\n// });\nconst[url,setUrl]=useState();const[url1,setUrl1]=useState();const[spining,setSpining]=useState(false);const discountCodeRef=useRef({});const[selectedProductList,setSelectedProductList]=useState([]);const[tipShow,setTipShow]=useState(false);const[couponSelect,setCouponSelect]=useState(null);const[refreshingFlg,setRefreshingFlg]=useState(false);const[myCouponList,setMyCouponList]=useState([]);// 获取协作群基本信息\nuseEffect(()=>{if(type!==CREATETYPE_CREATE){let res=setting105Result.data;if(res&&res.resultCode===200){form.setFieldValue(\"teamName\",res.name);}}},[setting105Result.dataUpdatedAt]);useEffect(()=>{if(!!(priceData!==null&&priceData!==void 0&&priceData.code)&&priceData!==null&&priceData!==void 0&&priceData.isEnterpriseEdition){setCouponSelect({couponCode:priceData.code});}},[priceData===null||priceData===void 0?void 0:priceData.code]);useEffect(()=>{if((couponList||[]).length>0){setMyCouponList([...couponList]);}},[JSON.stringify(couponList)]);useEffect(()=>{var _team701Result$data;if((((_team701Result$data=team701Result.data)===null||_team701Result$data===void 0?void 0:_team701Result$data.productList)||[]).length>0){let formatList=team701Result.data.productList.filter(group=>vipFlg==1?group.groupId!=eProductGroupId.Pgid_1_OS:true);let _prodGrpList=[];formatList.forEach(product=>{let item=_prodGrpList.find(_product=>product.groupId==_product.groupId);if(!item){_prodGrpList.push({groupName:product.groupName,groupId:product.groupId,products:[product]});}else{item.products.push(product);}});let allProductsList=[];productList=productList||[];//尝试修复 tmsbug-8699\n_prodGrpList.forEach(_prodGrp=>{let _productList=_prodGrp.products.map((config,index)=>{var _productList$find,_productList$find2,_productList$find3,_productList$find4,_productList$find5,_productList$find6,_find,_productList$find7,_productList$find8,_productList$find9;// !!data.lastExpirationDt && leftDaysFormat(data.lastExpirationDt) < 0\nlet lastMemberNo=type===CREATETYPE_UPGRADE&&((_productList$find=productList.find(product=>product.productId==config.productId))===null||_productList$find===void 0?void 0:_productList$find.freeFlg)!=1?(((_productList$find2=productList.find(product=>product.productId==config.productId))===null||_productList$find2===void 0?void 0:_productList$find2.authCnt)||0)>0?(_productList$find3=productList.find(product=>product.productId==config.productId))===null||_productList$find3===void 0?void 0:_productList$find3.authCnt:0:0;let lastExpirationDt=type===CREATETYPE_UPGRADE&&((_productList$find4=productList.find(product=>product.productId==config.productId))===null||_productList$find4===void 0?void 0:_productList$find4.freeFlg)!=1?!!((_productList$find5=productList.find(product=>product.productId==config.productId))!==null&&_productList$find5!==void 0&&_productList$find5.expirationDt)?(_productList$find6=productList.find(product=>product.productId==config.productId))===null||_productList$find6===void 0?void 0:_productList$find6.expirationDt:'':'';let checked=(_find=(selectedProductList||[]).find(record=>record.productId==config.productId))===null||_find===void 0?void 0:_find.checked;let item={...config,key:config.productId,isRowSpan:index==0?true:false,prodsLength:index==0?_prodGrp.products.length:0,memberNo:lastMemberNo>0&&!!lastExpirationDt&&leftDaysFormat(lastExpirationDt)<0?lastMemberNo:0,memberNoShow:lastMemberNo>0&&!!lastExpirationDt&&leftDaysFormat(lastExpirationDt)<0?lastMemberNo:0,lastMemberNo:lastMemberNo,nextMemberNo:type===CREATETYPE_UPGRADE&&((_productList$find7=productList.find(product=>product.productId==config.productId))===null||_productList$find7===void 0?void 0:_productList$find7.freeFlg)!=1?(((_productList$find8=productList.find(product=>product.productId==config.productId))===null||_productList$find8===void 0?void 0:_productList$find8.authCnt)||0)>0?(_productList$find9=productList.find(product=>product.productId==config.productId))===null||_productList$find9===void 0?void 0:_productList$find9.authCnt:0:0,durationMonth:null,name:'',rebate:'',effectBeginDt:'',expirationDt:'',lastExpirationDt:lastExpirationDt,originalPrice:'',discountPrice:'',checked:config.productId==eProductId.Pid_13_Cdisk?cdiskCheck:checked==true||checked==false?checked:config.statusType==eProductStatus.Status_2_QA||config.statusType==eProductStatus.Status_3_Unreleased?false:true};return item;});allProductsList=allProductsList.concat(_productList);});if(!!(priceData!==null&&priceData!==void 0&&priceData.teamPackage)){let list1=JSON.parse(priceData.teamPackage);allProductsList=allProductsList.map(config=>{var _list1$find,_list1$find2,_list1$find3,_list1$find4;config.memberNo=(_list1$find=list1.find(a=>a.productId==config.productId))===null||_list1$find===void 0?void 0:_list1$find.memberNo;config.durationMonth=(_list1$find2=list1.find(a=>a.productId==config.productId))===null||_list1$find2===void 0?void 0:_list1$find2.durationMonth;config.name=(_list1$find3=list1.find(a=>a.productId==config.productId))===null||_list1$find3===void 0?void 0:_list1$find3.name;config.rebate=(_list1$find4=list1.find(a=>a.productId==config.productId))===null||_list1$find4===void 0?void 0:_list1$find4.rebate;return config;});}setSelectedProductList([...allProductsList]);if(!!(priceData!==null&&priceData!==void 0&&priceData.teamPackage)){setTimeout(()=>{load_team_703_calc_price(priceData===null||priceData===void 0?void 0:priceData.code,allProductsList);},3000);}}},[JSON.stringify(team701Result),vipFlg]);function checkBoxChange(e,key){let checked=e.target.checked;let item=selectedProductList.find(data=>data.key==key);if(!!item){if(key==eProductId.Pid_13_Cdisk){setCdiskCheck(checked);}item.checked=checked;let list=[];if(checked){list=(item.dependsOnIds||'').split(',').filter(_productId=>!!_productId);}else{list=(item.dependedByIds||'').split(',').filter(_productId=>!!_productId);}list.forEach(_productId=>{let _item=selectedProductList.find(data=>data.key==_productId);_item.checked=checked;});setSelectedProductList([...selectedProductList]);}}function memberNoChange(item,clickType){let data=selectedProductList.find(data=>data.key==item.key);if(clickType==0){if(type==CREATETYPE_CREATE&&data.memberNo<=0){return;}if(type==CREATETYPE_UPGRADE&&data.memberNo<=-data.lastMemberNo){return;}}if(clickType==1){if(data.memberNo>=1000){return;}}data.memberNo=clickType==0?data.memberNo-1:clickType==1?data.memberNo+1:data.memberNoShow||0;data.memberNoShow=clickType==0?data.memberNoShow-1:clickType==1?data.memberNoShow+1:data.memberNoShow||0;if(newBuyFormat(data)==1){if(!data.durationMonth){var _find2,_team701Result$data2,_find3,_team701Result$data3;data.durationMonth=12;data.name=((_find2=(((_team701Result$data2=team701Result.data)===null||_team701Result$data2===void 0?void 0:_team701Result$data2.monthPromoList)||[]).find(item1=>item1.monthCnt==12))===null||_find2===void 0?void 0:_find2.monthCntDesc)||'';data.rebate=((_find3=(((_team701Result$data3=team701Result.data)===null||_team701Result$data3===void 0?void 0:_team701Result$data3.monthPromoList)||[]).find(item1=>item1.monthCnt==12))===null||_find3===void 0?void 0:_find3.promoRateDesc)||'';}if(data.memberNo<=0){data.durationMonth=null;data.name='';data.rebate='';}}let teamName=form.getFieldValue('teamName');isChange(teamName,selectedProductList);load_team_703_calc_price(couponSelect===null||couponSelect===void 0?void 0:couponSelect.couponCode);setSelectedProductList([...selectedProductList]);}function memberNoChangeW(item,value){let data=selectedProductList.find(data=>data.key==item.key);data.memberNo=value;data.memberNoShow=value;if(newBuyFormat(data)==1){if(!data.durationMonth){var _find4,_team701Result$data4,_find5,_team701Result$data5;data.durationMonth=12;data.name=((_find4=(((_team701Result$data4=team701Result.data)===null||_team701Result$data4===void 0?void 0:_team701Result$data4.monthPromoList)||[]).find(item1=>item1.monthCnt==12))===null||_find4===void 0?void 0:_find4.monthCntDesc)||'';data.rebate=((_find5=(((_team701Result$data5=team701Result.data)===null||_team701Result$data5===void 0?void 0:_team701Result$data5.monthPromoList)||[]).find(item1=>item1.monthCnt==12))===null||_find5===void 0?void 0:_find5.promoRateDesc)||'';}if((value||0)<=0){data.durationMonth=null;data.name='';data.rebate='';}}setSelectedProductList([...selectedProductList]);}function durationMonthChange(item,item1){let data=selectedProductList.find(data=>data.key==item.key);if(!!item1){data.durationMonth=item1.monthCnt;data.name=item1.monthCntDesc;data.rebate=item1.promoRateDesc;if(newBuyFormat(data)==1){if(data.memberNo==0){data.memberNo=1;data.memberNoShow=1;}}}else{data.durationMonth=null;data.name='';data.rebate='';if(newBuyFormat(data)==1){data.memberNo=0;data.memberNoShow=0;}}let teamName=form.getFieldValue('teamName');isChange(teamName,selectedProductList);load_team_703_calc_price(couponSelect===null||couponSelect===void 0?void 0:couponSelect.couponCode);setSelectedProductList([...selectedProductList]);}function teamNameChange(e){isChange(e.target.value,selectedProductList);}function isChange(teamName,currentList){if(type==CREATETYPE_CREATE&&!!teamName){setIsChange(true);}else{if(currentList.filter(product=>product.memberNo!=0||!!product.durationMonth).length>0){setIsChange(true);}else{setIsChange(false);}}}const _onOk=useDebounce(async()=>{let values=form.getFieldsValue(true);if(!values.teamName){globalUtil.warning('请输入团队名称');return;}if(vipFlg==0&&selectedProductList.filter(product=>product.checked&&product.productId!=eProductId.Pid_11_Explorer&&product.productId!=eProductId.Pid_12_Space).length<=0){globalUtil.warning('至少启用一个应用');return;}let validFlg=false;if(priceInfo.payPrice<=0){if(type==CREATETYPE_CREATE){if(vipFlg==1&&(priceInfo.couponReduction||0)<=0){globalUtil.warning('请选择套餐');return;}validFlg=true;}else{if(priceInfo.payPrice<0){setTipShow(true);return;}if(selectedProductList.filter(data=>data.memberNo<0).length==0&&(priceInfo.couponReduction||0)<=0){globalUtil.warning('请选择套餐');return;}validFlg=true;}}let _prodsInCart=selectedProductList//购物车(有数量的产品）\n.filter(data=>data.statusType!=eProductStatus.Status_3_Unreleased&&(data.memberNo!=0||!!data.durationMonth)).map(data=>({productId:data.productId,adjustMonthCnt:!!data.durationMonth?data.durationMonth:0,adjustAuthCnt:data.memberNo}));setSpining(true);let payInfo1={teamName:values.teamName,orderType:type==CREATETYPE_CREATE?1:type==CREATETYPE_UPGRADE?2:''};let couponCode=null;if(vipFlg==0){payInfo1.productList=[];payInfo1.productIdList=selectedProductList.filter(product=>product.checked).map(product=>{return product.productId;});//20250731 后端需要这个数组字段来判断\n//payInfo1.disabledProductList = selectedProductList.filter(product => !product.checked).map(product => {return product.productId}) //后端不需要，禁用掉\n}else{payInfo1.productList=_prodsInCart;if(!!couponSelect&&priceInfo.couponReduction>0){couponCode=couponSelect.couponCode;}}if(validFlg){await upload_team_704_team_package_pay_order(payInfo1,couponCode,1);}else{await upload_team_704_team_package_pay_order(payInfo1,couponCode,2);}setPayInfo(payInfo1);setSpining(false);// } else {\n//   setSpining(true);\n//   let _packageList = dataSource.map(data => ({\n//     objType: data.objType,\n//     enableFlg: data.checked ? 1: 0,\n//     typeDesc: data.typeDesc,\n//     userCnt: 0\n//   }))\n//   // 创建免费团队\n//   let request = {\n//     teamName: values.teamName,\n//     isEnterpriseEdition: 0,\n//     pkgOpCode: type === CREATETYPE_CREATE?\"1000\":\n//                type === CREATETYPE_UPGRADE?\"1001\":\"\",\n//     packageList: _packageList\n//   }\n//   //备注: 前端不再需要调用 create_team接口，这里暂且注释掉 20231025 @garry，这里也是走 创建订单的流程\n//  /* await team_002_create_team(request).then(result => {\n//     if(result.resultCode == 200){\n//       sucessPay(result.teamId)\n//     }\n//   });*/\n// }\n// setSpining(false);\n},500);const cancelPay=()=>{setIsPaying(false);};const sucessPay=_teamId=>{dispatch(getTeamList(localTeamId));// 刷新团队列表\ncancelPay();onCancel();onOk===null||onOk===void 0?void 0:onOk();if(!teamId){// 弹出跳转弹窗\nModal.confirm({title:'提示',icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'#52c41a'}}),content:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u56E2\\u961F\\u521B\\u5EFA\\u6210\\u529F\\uFF0C\\u662F\\u5426\\u5207\\u6362\\u81F3 \",form.getFieldValue('teamName'),\" \\uFF1F\"]}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u70B9\\u51FB\\\"\\u5426\\\"\\uFF0C\\u505C\\u7559\\u5728\\u5F53\\u524D\\u9875\\u9762\\uFF1B\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u70B9\\u51FB\\\"\\u662F\\\"\\uFF0C\\u5207\\u6362\\u81F3\\u65B0\\u7684\\u56E2\\u961F\\u3002\"})]}),okText:'是，切换团队',cancelText:'否',onOk:()=>{navigate(`/team/${_teamId}`);}});}};// 打开优惠码校验框\nconst openDiscountCode=()=>{if(discountCodeVisible)return;setDiscountCodeVisible(!discountCodeVisible);};// 关闭优惠码校验框\nconst closeDiscountCode=()=>{setDiscountCodeVisible(!discountCodeVisible);discountForm.resetFields();discountCodeRef.current={};//clearDiscountCodeVerification()\n};// 校验优惠码\nconst verificationDiscountCode=(value,event)=>{if(event.nativeEvent.type==='click'&&!value){//if(event.nativeEvent.type === 'click') clearDiscountCodeVerification()\nif(!value)//globalUtil.warning(\"请填写优惠码\");\nreturn;}team_735_bind_user_coupon({couponCode:value}).then(res=>{if(res.resultCode==200){discountForm.resetFields();discountCodeRef.current={};if((priceInfo.payPrice||0)>0){load_team_703_calc_price(!!(couponSelect!==null&&couponSelect!==void 0&&couponSelect.couponCode)?couponSelect.couponCode:value,[],!(couponSelect!==null&&couponSelect!==void 0&&couponSelect.couponCode));}else{refetchGetUserValidCoupon();}}if(res.resultCode==500){globalUtil.error(res.resultMessage);}});};function warn(){let discountCode=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"\";let dataList=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[];let _packageList=(dataList.length>0?dataList:selectedProductList).filter(data=>{if(type==CREATETYPE_CREATE){return data.memberNo>0&&!!data.durationMonth;}else{if(!!newBuyFormat(data)){return data.memberNo>0&&!!data.durationMonth;}return data.memberNo!=0||!!data.durationMonth;}}).map(data=>({productId:data.productId,adjustMonthCnt:!!data.durationMonth?data.durationMonth:0,adjustAuthCnt:data.memberNo}));if(_packageList.length==0){if(!!discountCode){globalUtil.warning('请选择套餐');}return[];}return _packageList;}// 获取支付价格\nconst load_team_703_calc_price=useDebounce(function(){let discountCode=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"\";let dataList=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[];let couponUnBindFlg=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;let _packageList=warn(discountCode,dataList);if(_packageList.length==0){setPriceInfo({originalPrice:0,regularAmt:0,promoAmt:0,discountReduction:0,payPrice:0,couponReduction:0,resultCode:null,resultMessage:null});setSelectedProductList([...(dataList.length>0?dataList:selectedProductList).map(data=>{data.effectBeginDt='';data.expirationDt='';data.originalPrice='';data.discountPrice='';data.nextMemberNo=data.lastMemberNo>0&&!!data.lastExpirationDt?data.lastMemberNo:0;return data;})]);return;}let request={teamId,productList:_packageList,orderType:type==CREATETYPE_UPGRADE?2:1,couponCode:discountCode};team_703_calc_price(request).then(result=>{if(result.resultCode==200){const{listingAmt,promoAmt,regularAmt,volumePromoAmt,netAmt,couponAmt,orderProductList,couponList}=result;setPriceInfo({originalPrice:listingAmt,regularAmt:regularAmt,promoAmt:promoAmt,discountReduction:volumePromoAmt,payPrice:netAmt,couponReduction:couponAmt,resultCode:!!discountCode?result.resultCode:null,resultMessage:!!discountCode?\"有效优惠码\":\"\"});if(couponUnBindFlg){let coupon=(couponList||[]).find(_coupon=>_coupon.couponCode==discountCode);if(!!coupon&&!couponCanUse(coupon)){setCouponSelect(coupon);}}setMyCouponList([...(couponList||[])]);if((orderProductList||[]).length>0){(dataList.length>0?dataList:selectedProductList).forEach(data=>{let pkgItem=orderProductList.find(pkg=>pkg.productId==data.productId);if(!!pkgItem){data.effectBeginDt=pkgItem.effectiveDt;data.expirationDt=pkgItem.expirationDt;data.originalPrice=pkgItem.listingAmt;data.discountPrice=pkgItem.amtSubstractPromo;data.nextMemberNo=pkgItem.authCnt;}else{data.effectBeginDt='';data.expirationDt='';data.originalPrice='';data.discountPrice='';data.nextMemberNo=data.lastMemberNo>0&&!!data.lastExpirationDt?data.lastMemberNo:0;}});setSelectedProductList([...(dataList.length>0?dataList:selectedProductList)]);}}else if(result.resultCode==500){const{listingAmt=0,promoAmt=0,regularAmt=0,volumePromoAmt=0,netAmt=0,couponAmt=0,orderProductList=[],couponList}=result;setPriceInfo({originalPrice:listingAmt,regularAmt:regularAmt,promoAmt:promoAmt,discountReduction:volumePromoAmt,payPrice:netAmt,couponReduction:couponAmt,resultCode:!!discountCode?result.resultCode:null,resultMessage:!!discountCode?result.resultMessage:null});//setMyCouponList([...(couponList||[])]);\nif((orderProductList||[]).length>0){(dataList.length>0?dataList:selectedProductList).forEach(data=>{let pkgItem=orderProductList.find(pkg=>pkg.productId==data.productId);if(!!pkgItem){data.effectBeginDt=pkgItem.effectiveDt;data.expirationDt=pkgItem.expirationDt;data.originalPrice=pkgItem.listingAmt;data.discountPrice=pkgItem.amtSubstractPromo;data.nextMemberNo=pkgItem.authCnt;}else{data.effectBeginDt='';data.expirationDt='';data.originalPrice='';data.discountPrice='';data.nextMemberNo=data.lastMemberNo>0&&!!data.lastExpirationDt?data.lastMemberNo:0;}});setSelectedProductList([...(dataList.length>0?dataList:selectedProductList)]);}}});},500);const onFieldsChange=args=>{// console.log(\"我正在瞬息万变！！！！\", args);\n};if(team701Result.isLoading||team706Result.isLoading||!!teamId&&setting105Result.isLoading)return/*#__PURE__*/_jsx(TLoading,{});const userCntHelp=vipFlg===0?`限免基础版(免费)团队${(_team706Result$data=team706Result.data)===null||_team706Result$data===void 0?void 0:_team706Result$data.teamCountFree}个,已使用${(_team706Result$data2=team706Result.data)===null||_team706Result$data2===void 0?void 0:_team706Result$data2.enabledTeamCountFree}个。`:\"\";// 上传信息获取支付Url\nconst upload_team_704_team_package_pay_order=async(payInfo,discountCode,flag)=>{let request={...payInfo,teamId,couponCode:discountCode||null};await team_704_submit_order(request).then(result=>{if(result.resultCode==200){if(flag==1){sucessPay(result===null||result===void 0?void 0:result.teamId);return;}const{orderId,alipayUrl,wxpayUrl}=result;load_pay_code_url(alipayUrl,'zhifubao');load_pay_code_url(wxpayUrl,'weixin');setPayCode({orderId:orderId});if(flag==2){if(isPaying)return;setIsPaying(true);}}else{}});};// 生成支付二维码\nconst load_pay_code_url=async(url,payMethod)=>{try{if(!url){console.error('Invalid URL for QR code generation');if(payMethod=='zhifubao'){setUrl1(null);}else{setUrl(null);}return;}const qrOptions={errorCorrectionLevel:'H',margin:2,width:120,color:{dark:'#000000',light:'#ffffff'}};const image_url=await QRCode.toDataURL(url,qrOptions);if(payMethod=='zhifubao'){setUrl1(image_url);}else{setUrl(image_url);}}catch(error){console.error('Error generating QR code:',error);if(payMethod=='zhifubao'){setUrl1(null);}else{setUrl(null);}}};const navigateTo=type=>{let url='';if(type==1){url='personal/personaldata';}else{url='personal/invoice';}window.open(`${window.location.origin}/#/${url}`);};function couponSelectChange(coupon){if(!couponCanUse(coupon)){if((couponSelect===null||couponSelect===void 0?void 0:couponSelect.couponCode)==coupon.couponCode){setCouponSelect(null);load_team_703_calc_price();}else{setCouponSelect(coupon);load_team_703_calc_price(coupon.couponCode);}}}function couponCanUse(coupon){if(!!coupon.expirationDt){if(moment(coupon.expirationDt).isBefore(moment())){return'优惠券已过期';}}if(coupon.orderAmtFlg==0){//金额是否适用，0---不适用   1---适用\nreturn'订单金额不在适用范围';}if(!!teamId){//购买应用\nif(!!coupon.teamId&&coupon.teamId!=teamId){//如果指定团队和该团队不匹配，无法使用\nreturn`指定团队可用(${coupon.teamId})`;}if(coupon.usedByTeamFlg==1){//券有没有被该团队使用过，0---未使用过  1---使用过\nreturn'本团队已使用过该券';}//20250401 Jim Song, tmsbug-11993 全已经被使用过，也需要显示为灰色\nif(coupon.usedByUserFlg==1){//有没有被该用户使用过， 0---未使用过  1---使用过\nreturn'您已使用过该券';}}else{//创建团队\nif(!!coupon.teamId){//是否指定团队使用，不为空即表示指定团队使用，为空表示任意团队都可使用\nreturn`指定团队可用(${coupon.teamId})`;}//因为是新建团队，所以不必关注是否被团队使用过\nif(coupon.usedByUserFlg==1){//有没有被该用户使用过， 0---未使用过  1---使用过\nreturn'您已使用过该券';}}return'';}function deleteCoupon(coupon){team_736_unbind_user_coupon({couponId:coupon.couponId}).then(res=>{if(res.resultCode==200){if((priceInfo.payPrice||0)>0){if((couponSelect===null||couponSelect===void 0?void 0:couponSelect.couponCode)==coupon.couponCode){setCouponSelect(null);load_team_703_calc_price();}else{load_team_703_calc_price(couponSelect===null||couponSelect===void 0?void 0:couponSelect.couponCode);}}else{refetchGetUserValidCoupon();}}});//setMyCouponList([...myCouponList.filter(myCoupon => myCoupon.couponId != coupon.couponId)]);\n}function leftDaysFormat(date){let days=moment(moment(date).format('YYYYMMDD')).diff(moment(moment().format('YYYYMMDD')),'days')-1;if(days>=0){return days;}return-1;}function newBuyFormat(data){if(data.lastMemberNo<=0||!data.lastExpirationDt){return 1;//新购\n}if(!!data.lastExpirationDt&&leftDaysFormat(data.lastExpirationDt)<0){return 2;//过期\n}return 0;//在有效期内\n}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Form,{form:form,name:\"basic\",onFinish:_onOk,initialValues:{teamName:\"\",isEnterpriseEdition:(priceData===null||priceData===void 0?void 0:priceData.isEnterpriseEdition)==1?1:type===CREATETYPE_CREATE?0:1},...formItemLayout,style:{paddingRight:24,paddingLeft:24},autoComplete:\"off\",onFieldsChange:onFieldsChange,children:[/*#__PURE__*/_jsx(Form.Item,{className:\"team-name-label\",label:\"\\u56E2\\u961F\\u540D\\u79F0\",name:\"teamName\",children:/*#__PURE__*/_jsx(Input,{className:\"team-name-input\",autoComplete:\"off\",maxLength:100,disabled:type!==CREATETYPE_CREATE,onChange:teamNameChange})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u7248\\u672C\\u9009\\u62E9\",name:\"isEnterpriseEdition\",style:type==CREATETYPE_CREATE?{marginBottom:10}:{display:\"none\"},children:/*#__PURE__*/_jsxs(Radio.Group,{buttonStyle:\"solid\",children:[/*#__PURE__*/_jsx(Radio.Button,{value:0,style:{borderTopLeftRadius:5,borderBottomLeftRadius:5,width:120,textAlign:'center'},children:\"\\u57FA\\u7840\\u7248(\\u514D\\u8D39)\"}),/*#__PURE__*/_jsx(Radio.Button,{value:1,style:{borderTopRightRadius:5,borderBottomRightRadius:5,width:120,textAlign:'center'},children:\"VIP\\u7248\"})]})})]}),/*#__PURE__*/_jsx(Form,{style:{paddingLeft:24},name:\"productCheck\",initialValues:{packageList:[]},...formItemLayout,autoComplete:\"off\",children:/*#__PURE__*/_jsx(Form.Item,{className:\"product-selection-formItem\",label:\"\\u9009\\u62E9\\u5E94\\u7528\",name:\"packageList\",style:{marginBottom:10},children:/*#__PURE__*/_jsxs(Table,{size:\"small\",className:\"before-header\",dataSource:selectedProductList,pagination:false,bordered:true,scroll:{y:`calc(100vh - ${vipFlg==1?400:300}px)`},children:[/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center'},children:\"\\u7C7B\\u522B\"}),dataIndex:'groupName',width:vipFlg==1?type==CREATETYPE_CREATE?200:135:'19%',render:groupName=>/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center'},children:groupName}),onCell:item=>{if(item.isRowSpan){return{rowSpan:item.prodsLength};}else{return{rowSpan:0};}}},'groupName'),/*#__PURE__*/_jsx(Column,{title:'应用',dataIndex:'productName',width:vipFlg==1?type==CREATETYPE_CREATE?220:155:'19%',render:(productName,item)=>/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[productName,(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_2_QA&&/*#__PURE__*/_jsx(\"span\",{style:{color:'#70B603',fontSize:12,marginLeft:10},children:\"\\u5185\\u6D4B\\u4E2D\"}),(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased&&/*#__PURE__*/_jsx(\"span\",{style:{color:'#F59A23',fontSize:12,marginLeft:10},children:\"\\u5373\\u5C06\\u63A8\\u51FA\"})]}),onCell:()=>{return{rowSpan:1};}},'productName'),type==CREATETYPE_CREATE?/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:vipFlg==1?'授权人数':'成员数'}),dataIndex:'memberNo',width:vipFlg==1?150:'19%',render:(memberNo,item)=>{if(vipFlg==1){if(item.groupId==eProductGroupId.Pgid_1_OS){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"-\"});}return/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(\"div\",{className:`member-edit-primary`,children:/*#__PURE__*/_jsx(InputNumber,{controls:false,style:{width:110},size:\"small\",precision:0,min:0,max:1000,disabled:(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased,addonBefore:/*#__PURE__*/_jsx(Button,{type:'primary',disabled:(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased||(memberNo||0)==0,style:{borderTopLeftRadius:5,borderBottomLeftRadius:5,borderRight:'0px',borderColor:((item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased||(memberNo||0)==0)&&'#d9d9d9'},onClick:()=>memberNoChange(item,0),children:\"-\"}),addonAfter:/*#__PURE__*/_jsx(Button,{type:'primary',disabled:(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased||(memberNo||0)=={freeAuthUserQuota1000},style:{borderTopRightRadius:5,borderBottomRightRadius:5,borderLeft:'0px',borderColor:((item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased||(memberNo||0)==1000)&&'#d9d9d9'},onClick:()=>memberNoChange(item,1),children:\"+\"}),value:memberNo,onChange:value=>memberNoChangeW(item,value),onPressEnter:()=>memberNoChange(item,2),onBlur:()=>memberNoChange(item,2)})})});}else{return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:freeAuthUserQuota1000});}},onCell:()=>{return{rowSpan:1};}},'memberNo'):/*#__PURE__*/_jsxs(ColumnGroup,{title:'购买前',className:\"top-header-a\",children:[/*#__PURE__*/_jsx(Column,{title:'授权数',className:\"top-header-a\",dataIndex:'lastMemberNo',width:90,render:lastMemberNo=>/*#__PURE__*/_jsxs(\"div\",{children:[lastMemberNo||0,\"\\u4EBA\"]}),onCell:()=>{return{rowSpan:1};}},'lastMemberNo'),/*#__PURE__*/_jsx(Column,{title:'对象新建数',className:\"top-header-a\",dataIndex:'lastResourceNo',width:150,render:(lastResourceNo,item)=>{if(!!item.lastExpirationDt&&(item.lastMemberNo||0)>0){return/*#__PURE__*/_jsx(\"div\",{style:newBuyFormat(item)==2?{color:'#999'}:{},children:\"\\u221E\"});}return/*#__PURE__*/_jsx(\"div\",{children:(item===null||item===void 0?void 0:item.freeQuotaDesc)||'-'});},onCell:()=>{return{rowSpan:1};}},'lastResourceNo'),/*#__PURE__*/_jsx(Column,{title:'有效期至',className:\"top-header-a\",dataIndex:'lastExpirationDt',width:100,render:(lastExpirationDt,item)=>{if(!!lastExpirationDt&&(item.lastMemberNo||0)>0){return/*#__PURE__*/_jsx(\"div\",{style:newBuyFormat(item)==2?{color:'#999'}:{},children:moment(lastExpirationDt).format('YYYY-MM-DD')});}return/*#__PURE__*/_jsx(\"div\",{children:\"\\u221E\"});},onCell:()=>{return{rowSpan:1};}},'lastExpirationDt')]}),type==CREATETYPE_CREATE&&/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u5BF9\\u8C61\\u65B0\\u5EFA\\u6570\"}),dataIndex:'lastResourceNo',width:vipFlg==1?200:'19%',render:(lastResourceNo,item)=>{if(vipFlg==1&&(item.memberNo||0)>0){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u221E\"});}return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:(item===null||item===void 0?void 0:item.freeQuotaDesc)||'-'});},onCell:()=>{return{rowSpan:1};}},'lastResourceNo'),vipFlg==1?type==CREATETYPE_CREATE?/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u8D2D\\u4E70\\u65F6\\u957F\"}),dataIndex:'durationMonth',width:160,render:(durationMonth,item,index1)=>{var _team701Result$data6;if(item.groupId==eProductGroupId.Pgid_1_OS){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"-\"});}return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[\"  \",/*#__PURE__*/_jsx(Dropdown,{trigger:['click'],disabled:(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased,overlay:/*#__PURE__*/_jsx(Menu,{children:/*#__PURE__*/_jsx(Menu.Item,{children:/*#__PURE__*/_jsx(Radio.Group,{size:\"small\",buttonStyle:\"solid\",className:\"create-team-date-item\",value:item.durationMonth,children:(((_team701Result$data6=team701Result.data)===null||_team701Result$data6===void 0?void 0:_team701Result$data6.monthPromoList)||[]).map((item1,index)=>/*#__PURE__*/_jsx(Badge,{size:\"small\",title:\"\",className:\"pay-badge\",count:item1.promoRateDesc||\"\",offset:[-8,-5],children:/*#__PURE__*/_jsx(Radio.Button,{onClick:()=>durationMonthChange(item,item1),value:item1.monthCnt,children:item1.monthCntDesc})},index))})})}),children:/*#__PURE__*/_jsxs(\"a\",{style:{color:'inherit',display:'flex',justifyContent:'space-between',alignItems:'center',width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',flex:'0 0 auto',paddingRight:'16px'},children:[newBuyFormat(item)!=2&&/*#__PURE__*/_jsx(\"span\",{style:{color:'#999',fontStyle:'italic'},children:!!newBuyFormat(item)?'':`剩${leftDaysFormat(item.lastExpirationDt)}天`}),newBuyFormat(item)==2&&/*#__PURE__*/_jsx(\"span\",{style:{color:'red',fontStyle:'italic'},children:\"\\u5DF2\\u8FC7\\u671F\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',flex:'0 0 auto',paddingLeft:'16px'},children:!!durationMonth?/*#__PURE__*/_jsx(Badge,{size:\"small\",title:\"\",className:\"pay-badge\",count:item.rebate||\"\",offset:[-8,-5],children:/*#__PURE__*/_jsxs(\"div\",{style:{height:24,width:60,display:'flex',alignItems:'center',fontSize:12,color:'#fff',backgroundColor:'#0077F2',borderRadius:5},children:[/*#__PURE__*/_jsx(\"span\",{style:{width:40,display:'flex',justifyContent:'center'},children:item.name}),/*#__PURE__*/_jsx(\"span\",{style:{width:20,display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(DownOutlined,{})})]})},index1):/*#__PURE__*/_jsx(\"span\",{style:{color:newBuyFormat(item)==1?'#B3D6FB':'#F59A23'},children:newBuyFormat(item)==1?'选时长':'续时长'})})]})}),!!durationMonth&&/*#__PURE__*/_jsx(Button,{style:{borderRadius:'50%',transition:'all 0s 0s'},size:\"small\",type:\"text\",icon:/*#__PURE__*/_jsx(CloseOutlined,{className:\"fontsize-12\"}),onClick:()=>durationMonthChange(item)})]});},onCell:()=>{return{rowSpan:1};}},'durationMonth'):/*#__PURE__*/_jsxs(ColumnGroup,{title:'规格选择',className:\"top-header-b\",children:[/*#__PURE__*/_jsx(Column,{title:'增/减员数',className:\"top-header-b\",dataIndex:'memberNo',width:160,render:(memberNo,item)=>{return/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(\"div\",{className:`member-edit-primary`,children:/*#__PURE__*/_jsx(InputNumber,{controls:false,style:{width:110},size:\"small\",precision:0,min:!!newBuyFormat(item)?0:-item.lastMemberNo,max:1000,disabled:(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased,addonBefore:/*#__PURE__*/_jsx(Button,{type:'primary',disabled:(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased||(!!newBuyFormat(item)?(memberNo||0)==0:(memberNo||0)==-item.lastMemberNo),style:{borderTopLeftRadius:5,borderBottomLeftRadius:5,borderRight:'0px',borderColor:((item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased||(!!newBuyFormat(item)?(memberNo||0)==0:(memberNo||0)==-item.lastMemberNo))&&'#d9d9d9'},onClick:()=>memberNoChange(item,0),children:\"-\"}),addonAfter:/*#__PURE__*/_jsx(Button,{type:'primary',disabled:(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased||(memberNo||0)=={freeAuthUserQuota1000},style:{borderTopRightRadius:5,borderBottomRightRadius:5,borderLeft:'0px',borderColor:((item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased||(memberNo||0)=={freeAuthUserQuota1000})&&'#d9d9d9'},onClick:()=>memberNoChange(item,1),children:\"+\"}),value:memberNo,onChange:value=>memberNoChangeW(item,value),onPressEnter:()=>memberNoChange(item,2),onBlur:()=>memberNoChange(item,2)})})});},onCell:()=>{return{rowSpan:1};}},'memberNo'),/*#__PURE__*/_jsx(Column,{title:'购买时长',className:\"top-header-b\",dataIndex:'durationMonth',width:160,render:(durationMonth,item,index1)=>{var _team701Result$data7;return/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Dropdown,{trigger:['click'],disabled:(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased,overlay:/*#__PURE__*/_jsx(Menu,{children:/*#__PURE__*/_jsx(Menu.Item,{children:/*#__PURE__*/_jsx(Radio.Group,{size:\"small\",buttonStyle:\"solid\",className:\"create-team-date-item\",value:item.durationMonth,children:(((_team701Result$data7=team701Result.data)===null||_team701Result$data7===void 0?void 0:_team701Result$data7.monthPromoList)||[]).map((item1,index)=>/*#__PURE__*/_jsx(Badge,{size:\"small\",title:\"\",className:\"pay-badge\",count:item1.promoRateDesc||\"\",offset:[-8,-5],children:/*#__PURE__*/_jsx(Radio.Button,{onClick:()=>durationMonthChange(item,item1),value:item1.monthCnt,children:item1.monthCntDesc})},index))})})}),children:/*#__PURE__*/_jsxs(\"a\",{style:{color:'inherit',display:'flex',justifyContent:'space-between',alignItems:'center',width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',flex:'0 0 auto',paddingRight:'16px'},children:[newBuyFormat(item)!=2&&/*#__PURE__*/_jsx(\"span\",{style:{color:'#999',fontStyle:'italic'},children:!!newBuyFormat(item)?'':`剩${leftDaysFormat(item.lastExpirationDt)}天`}),newBuyFormat(item)==2&&/*#__PURE__*/_jsx(\"span\",{style:{color:'red',fontStyle:'italic'},children:\"\\u5DF2\\u8FC7\\u671F\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',flex:'0 0 auto',paddingLeft:'16px'},children:!!durationMonth?/*#__PURE__*/_jsx(Badge,{size:\"small\",title:\"\",className:\"pay-badge\",count:item.rebate||\"\",offset:[-8,-5],children:/*#__PURE__*/_jsxs(\"div\",{style:{height:24,width:60,display:'flex',alignItems:'center',fontSize:12,color:'#fff',backgroundColor:'#0077F2',borderRadius:5},children:[/*#__PURE__*/_jsx(\"span\",{style:{width:40,display:'flex',justifyContent:'center'},children:item.name}),/*#__PURE__*/_jsx(\"span\",{style:{width:20,display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(DownOutlined,{})})]})},index1):/*#__PURE__*/_jsx(\"span\",{style:{color:newBuyFormat(item)==1?'#B3D6FB':'#F59A23'},children:newBuyFormat(item)==1?'选时长':'续时长'})})]})}),!!durationMonth&&/*#__PURE__*/_jsx(Button,{style:{borderRadius:'50%',transition:'all 0s 0s'},size:\"small\",type:\"text\",icon:/*#__PURE__*/_jsx(CloseOutlined,{className:\"fontsize-12\"}),onClick:()=>durationMonthChange(item)})]})});},onCell:()=>{return{rowSpan:1};}},'durationMonth')]}):null,type==CREATETYPE_CREATE?vipFlg==1?/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u751F\\u6548\\u5F00\\u59CB\"}),dataIndex:'effectBeginDt',width:120,render:(effectBeginDt,item)=>{if(item.groupId==eProductGroupId.Pgid_1_OS){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"-\"});}return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:!!effectBeginDt?moment(effectBeginDt).format('YYYY-MM-DD'):''});},onCell:()=>{return{rowSpan:1};}},'effectBeginDt'):/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u6709\\u6548\\u671F\\u81F3\"}),dataIndex:'expirationDt',width:'19%',render:()=>/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u221E\"}),onCell:()=>{return{rowSpan:1};}},'expirationDt'):/*#__PURE__*/_jsxs(ColumnGroup,{title:'预览',className:\"top-header-c\",children:[/*#__PURE__*/_jsx(Column,{title:'授权数',className:\"top-header-c\",dataIndex:'nextMemberNo',width:90,render:(nextMemberNo,item)=>{if(!!newBuyFormat(item)&&!item.durationMonth&&item.memberNo<=0){return/*#__PURE__*/_jsxs(\"div\",{children:[item.memberNo,\"\\u4EBA\"]});}return/*#__PURE__*/_jsxs(\"div\",{children:[nextMemberNo||0,\"\\u4EBA\"]});},onCell:()=>{return{rowSpan:1};}},'nextMemberNo'),/*#__PURE__*/_jsx(Column,{title:'对象新建数',className:\"top-header-c\",dataIndex:'nextResourceNo',width:150,render:(nextResourceNo,item)=>{if(!newBuyFormat(item)){if(item.memberNo<0&&item.memberNo+item.lastMemberNo==0){return/*#__PURE__*/_jsx(\"div\",{children:(item===null||item===void 0?void 0:item.freeQuotaDesc)||'-'});}return/*#__PURE__*/_jsx(\"div\",{children:\"\\u221E\"});}if((item.memberNo||0)>0&&!!item.durationMonth){return/*#__PURE__*/_jsx(\"div\",{children:\"\\u221E\"});}return/*#__PURE__*/_jsx(\"div\",{children:(item===null||item===void 0?void 0:item.freeQuotaDesc)||'-'});},onCell:()=>{return{rowSpan:1};}},'nextResourceNo'),/*#__PURE__*/_jsx(Column,{title:'有效期至',className:\"top-header-c\",dataIndex:'expirationDt',width:90,render:(expirationDt,item)=>{if(!newBuyFormat(item)){if(!!expirationDt){return/*#__PURE__*/_jsx(\"div\",{children:moment(expirationDt).format('YYYY-MM-DD')});}return/*#__PURE__*/_jsx(\"div\",{children:moment(item.lastExpirationDt).format('YYYY-MM-DD')});}if((item.memberNo||0)>0&&!!item.durationMonth){if(!!expirationDt){return/*#__PURE__*/_jsx(\"div\",{children:moment(expirationDt).format('YYYY-MM-DD')});}return/*#__PURE__*/_jsx(_Fragment,{});}return/*#__PURE__*/_jsx(\"div\",{children:\"\\u221E\"});},onCell:()=>{return{rowSpan:1};}},'expirationDt')]}),vipFlg==1&&type==CREATETYPE_CREATE?/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u751F\\u6548\\u7ED3\\u675F\"}),dataIndex:'expirationDt',width:120,render:expirationDt=>{if(!!expirationDt){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:!!expirationDt?moment(expirationDt).format('YYYY-MM-DD'):''});}else{return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u221E\"});}},onCell:()=>{return{rowSpan:1};}},'expirationDt'):null,vipFlg==1&&/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u5355\\u4EF7\"}),dataIndex:'discountPrice',width:120,render:(discountPrice,item)=>{if(item.groupId==eProductGroupId.Pgid_1_OS){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"0\"});}return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"span\",{style:{color:'#999',width:40,textDecoration:'line-through'},children:[\"\\xA5\",(item===null||item===void 0?void 0:item.listingUnitAmt)||'0']}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\xA5\",(item===null||item===void 0?void 0:item.regularUnitAmt)||'0',\"/\\u4EBA/\\u6708\"]})]});},onCell:()=>{return{rowSpan:1};}},'discountPrice'),vipFlg==1&&/*#__PURE__*/_jsx(Column,{title:'小计',dataIndex:'subtotal',render:(subtotal,item)=>{if(item.groupId==eProductGroupId.Pgid_1_OS){return/*#__PURE__*/_jsx(\"div\",{});}return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"span\",{style:{color:'#999',marginRight:10,textDecoration:'line-through',flex:1},children:[!!item.originalPrice?'¥':'',item.originalPrice]}),/*#__PURE__*/_jsxs(\"span\",{style:{flex:1},children:[!!item.discountPrice?'¥':'',item.discountPrice]})]});},onCell:()=>{return{rowSpan:1};}},'subtotal'),vipFlg==1?null:type==CREATETYPE_CREATE?/*#__PURE__*/_jsx(Column,{title:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:\"\\u52FE\\u9009\\u4F7F\\u7528\"}),dataIndex:'productId',width:'5%',render:(productId,item)=>{return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:/*#__PURE__*/_jsx(Checkbox,{disabled:productId==eProductId.Pid_11_Explorer||productId==eProductId.Pid_12_Space||(item===null||item===void 0?void 0:item.statusType)==eProductStatus.Status_3_Unreleased,checked:item.checked,onChange:e=>checkBoxChange(e,productId)})});},onCell:()=>{return{rowSpan:1};}},'productId'):null]})})}),/*#__PURE__*/_jsxs(\"div\",{style:{height:220},children:[vipFlg==1?/*#__PURE__*/_jsxs(\"div\",{className:\"price-bottom\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"price-bottom-left\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"span\",{style:{marginRight:50},children:[\"\\u9009\\u62E9\\u4F18\\u60E0\\u5238(\",myCouponList.length,\")\",/*#__PURE__*/_jsx(Tooltip,{title:\"\\u9AD8\\u4EAE\\u663E\\u793A\\u7684\\u4F18\\u60E0\\u5238\\u53EF\\u7528\\uFF0C\\u7070\\u663E\\u7684\\u4F18\\u60E0\\u5238\\u4E0D\\u53EF\\u7528\",placement:\"right\",children:/*#__PURE__*/_jsx(QuestionCircleOutlined,{style:{color:'#f59a23',marginLeft:5}})})]}),/*#__PURE__*/_jsx(Button,{className:refreshingFlg&&'refresh-icon',style:{position:'relative',color:'#666'},type:\"link\",icon:/*#__PURE__*/_jsx(\"span\",{className:\"refresh-position fontsize-14 iconfont shuaxin1\"}),onClick:()=>{setRefreshingFlg(true);setTimeout(()=>{if((priceInfo.payPrice||0)>0){load_team_703_calc_price(couponSelect===null||couponSelect===void 0?void 0:couponSelect.couponCode);}else{refetchGetUserValidCoupon();}setRefreshingFlg(false);},500);}}),/*#__PURE__*/_jsx(\"span\",{style:{marginRight:50},children:\"\\u5237\\u65B0\"}),/*#__PURE__*/_jsx(Form,{colon:false,form:discountForm,children:/*#__PURE__*/_jsxs(Form.Item,{style:{margin:0},label:/*#__PURE__*/_jsx(\"span\",{style:{color:'#0077f2',cursor:'pointer'},onClick:openDiscountCode,children:\"\\u6211\\u6709\\u4F18\\u60E0\\u7801\"})// validateStatus={priceInfo.resultCode === 200 ? \"success\" : (priceInfo.resultCode === 500 ? \"error\" : null)}\n,children:[discountCodeVisible&&/*#__PURE__*/_jsx(Form.Item,{name:\"code\",noStyle:true,children:/*#__PURE__*/_jsx(Search,{className:\"discount-code discount-code-visible\",size:\"small\",maxLength:12,allowClear:true,autoComplete:\"off\",placeholder:\"\\u8BF7\\u586B\\u5199\\u4F18\\u60E0\\u7801\",enterButton:\"\\u6821\\u9A8C\",onChange:e=>{discountCodeRef.current.code=e.target.value;},onSearch:verificationDiscountCode})}),discountCodeVisible&&/*#__PURE__*/_jsx(Form.Item,{noStyle:true,children:/*#__PURE__*/_jsx(Button,{style:{borderRadius:'50%',transition:'all 0s 0s'},size:\"small\",type:\"text\",icon:/*#__PURE__*/_jsx(CloseOutlined,{className:\"fontsize-12\"}),onClick:closeDiscountCode})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-column-parent\",style:{height:110,width:'90%',border:'1px solid #f0f0f0',borderRadius:5},children:/*#__PURE__*/_jsx(\"div\",{style:{height:'100%',padding:'5px 0px 5px 5px'},className:\"flex-column-child section\",children:/*#__PURE__*/_jsx(Row,{children:myCouponList.map(coupon=>{return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',position:'relative',height:105},children:[/*#__PURE__*/_jsx(Button,{type:\"text\",title:!couponCanUse(coupon)?`优惠码：${coupon.couponCode}`:`优惠码：${coupon.couponCode}\\n不可用原因：${couponCanUse(coupon)}`,onClick:()=>couponSelectChange(coupon),style:{padding:0},disabled:!!couponCanUse(coupon),children:/*#__PURE__*/_jsxs(Card,{style:{backgroundColor:!couponCanUse(coupon)?'#E6F2FE':'#f0f0f0'},className:((couponSelect===null||couponSelect===void 0?void 0:couponSelect.couponCode)==coupon.couponCode?\"CouponCard-select\":'')+\" CouponCard\",hoverable:false,children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:70,backgroundColor:!couponCanUse(coupon)?'#0077F2':'#999',borderTopLeftRadius:5,borderTopRightRadius:5},children:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',height:'100%',marginLeft:10},children:/*#__PURE__*/_jsxs(\"div\",{style:{color:'#fff'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:18,marginRight:20},children:coupon.couponType==eCouponType.Type_2_AmountOff?'减￥'+(coupon.couponDiscount||''):(coupon.couponDiscount||'')+'折'}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u6EE1  \\uFFE5\",coupon.minOrderAmt]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u6709\\u6548\\u671F\\u81F3\",moment(coupon.expirationDt).format('YYYY-MM-DD')]})]})})}),/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:30},children:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',height:30,marginLeft:10,color:!couponCanUse(coupon)?'#0077F2':'#999'},children:/*#__PURE__*/_jsx(\"span\",{children:coupon.couponName})})})]})}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u5220\\u9664\\u8BE5\\u4F18\\u60E0\\u5238?\",onConfirm:()=>deleteCoupon(coupon),children:/*#__PURE__*/_jsx(Button,{style:{borderRadius:'50%',transition:'all 0s 0s',width:14,height:14,position:'absolute',right:0},size:\"small\",icon:/*#__PURE__*/_jsx(CloseOutlined,{style:{fontSize:10}})})})]});})})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"price-bottom-right\",children:/*#__PURE__*/_jsx(\"div\",{className:\"price-bottom-detailed\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"price-bottom-detailed-descriptions\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"price-bottom-detailed-descriptions-li\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"li-label\",children:\"\\u539F\\u4EF7:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"li-value\",children:[/*#__PURE__*/_jsxs(\"span\",{style:{textDecoration:'line-through',color:'#999',marginRight:10},children:[\"\\uFFE5\",priceInfo.originalPrice||0]}),(priceInfo.promoAmt||0)>0?`(立减-￥${priceInfo.promoAmt})`:'']})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"price-bottom-detailed-descriptions-li\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"li-label\",children:\"\\u73B0\\u4EF7:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"li-value\",children:[\"\\uFFE5\",priceInfo.regularAmt||0]})]}),(priceInfo.discountReduction||0)>0?/*#__PURE__*/_jsxs(\"div\",{className:\"price-bottom-detailed-descriptions-li\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"li-label\",children:\"\\u6298\\u6263\\u51CF:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"li-value\",children:[\"-\\uFFE5\",priceInfo.discountReduction]})]}):/*#__PURE__*/_jsx(\"div\",{style:{display:'none'}}),!!couponSelect?/*#__PURE__*/_jsxs(\"div\",{className:\"price-bottom-detailed-descriptions-li\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"li-label\",style:{color:priceInfo.couponReduction==0?'#999':'#333'},children:\"\\u4F18\\u60E0\\u5238:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"li-value\",style:{color:priceInfo.couponReduction==0?'#999':'#333'},children:[\"-\\uFFE5\",priceInfo.couponReduction,/*#__PURE__*/_jsxs(\"span\",{className:\"coupon\",style:{backgroundColor:priceInfo.couponReduction==0?'#999':'#0077F2'},children:[\"\\u6EE1\\uFFE5\",(_myCouponList$find=myCouponList.find(coupon=>coupon.couponCode==couponSelect.couponCode))===null||_myCouponList$find===void 0?void 0:_myCouponList$find.minOrderAmt,((_myCouponList$find2=myCouponList.find(coupon=>coupon.couponCode==couponSelect.couponCode))===null||_myCouponList$find2===void 0?void 0:_myCouponList$find2.couponType)==eCouponType.Type_2_AmountOff?'减￥':'享',(_myCouponList$find3=myCouponList.find(coupon=>coupon.couponCode==couponSelect.couponCode))===null||_myCouponList$find3===void 0?void 0:_myCouponList$find3.couponDiscount,((_myCouponList$find4=myCouponList.find(coupon=>coupon.couponCode==couponSelect.couponCode))===null||_myCouponList$find4===void 0?void 0:_myCouponList$find4.couponType)==eCouponType.Type_2_AmountOff?'':'折',/*#__PURE__*/_jsx(\"a\",{className:\"delete\",style:{color:priceInfo.couponReduction==0?'#999':'#0077F2'},onClick:()=>{setCouponSelect(null);load_team_703_calc_price();},children:/*#__PURE__*/_jsx(CloseOutlined,{style:{fontSize:10}})})]})]})]}):/*#__PURE__*/_jsx(\"div\",{style:{display:'none'}}),/*#__PURE__*/_jsxs(\"div\",{className:\"price-bottom-detailed-descriptions-li\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"li-label\",children:\"\\u5408\\u8BA1:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"li-value\",children:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:12,display:'flex',alignItems:'baseline',justifyContent:'end'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#f59a23'},children:\"\\uFFE5\"}),/*#__PURE__*/_jsx(\"span\",{className:\"shifu-rate\",style:{color:'#f59a23'},children:priceInfo.payPrice||0})]})})]})]})})})]}):/*#__PURE__*/_jsx(\"div\",{style:{height:40}}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:\"flex-end\",justifyContent:'space-between',paddingLeft:14},children:[/*#__PURE__*/_jsxs(\"div\",{children:[vipFlg==0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:10,height:20,fontSize:12,color:'#999'},children:[\"\\u5907\\u6CE81\\uFF1A\",userCntHelp]}),vipFlg==1&&/*#__PURE__*/_jsx(\"div\",{style:{marginLeft:10,height:20,fontSize:12,color:'#999'},children:\"\\u5907\\u6CE81\\uFF1Avip\\u4EBA\\u6570\\u4E3A0\\u7684\\u5E94\\u7528\\uFF0C\\u60A8\\u5C06\\u7EE7\\u7EED\\u4EAB\\u6709\\u57FA\\u7840\\u7248\\u7684\\u529F\\u80FD\\u514D\\u8D39\\u957F\\u671F\\u4F7F\\u7528\\u3002\"}),type==CREATETYPE_CREATE?/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:10,height:20,fontSize:12,color:'#999'},children:[\"\\u5907\\u6CE82\\uFF1A\\u60A8\\u4E0D\\u9700\\u8981\\u7684\\u529F\\u80FD\\uFF0C\\u5728\\u56E2\\u961F\\u521B\\u5EFA\\u6210\\u529F\\u540E\\uFF0C\\u53EF\\u5728 \\u8BBE\\u7F6E \",'->',\" \\u5E94\\u7528\\u7BA1\\u7406 \\u9875\\u9762\\u4E2D\\u7981\\u7528(\\u6216\\u518D\\u6B21\\u5F00\\u542F)\"]}):null,type==CREATETYPE_CREATE&&/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:10,height:20,fontSize:12,color:'#999'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#fff'},children:\"\\u5907\\u6CE82\\uFF1A\"}),\"\\u70B9\\u51FB \\u5E2E\\u52A9\\u4E2D\\u5FC3 \",'->',\" \\u5E94\\u7528\\u7BA1\\u7406 \\u94FE\\u63A5\\uFF0C\\u53EF\\u5B9A\\u4F4D\\u5230 \\u5B98\\u7F51\\u5E2E\\u52A9\\u9875\\u7684 \\u5173\\u4E8E\\u5E94\\u7528\\u7BA1\\u7406\\u7684\\u5E2E\\u52A9\\u6587\\u6863\\u3002\"]}),type==CREATETYPE_UPGRADE&&/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:10,height:20,fontSize:12,color:'#999'},children:[\"\\u5907\\u6CE82\\uFF1A\\u53EF\\u5728 \\u8BBE\\u7F6E \",'->',\" \\u5E94\\u7528\\u7BA1\\u7406 \\u4E2D\\u8FDB\\u884C\\u5E94\\u7528\\u542F\\u7528/\\u7981\\u7528\\u3002\"]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{paddingRight:24},children:[/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'right'},children:/*#__PURE__*/_jsx(Button,{className:vipFlg==1?\"purchase-btn\":\"found-btn\",type:\"primary\",loading:spining,style:{minWidth:140,height:34,borderRadius:5},onClick:_onOk,children:vipFlg==1?\"立即购买\":`创建(已选${selectedProductList.filter(product=>product.checked&&product.productId!=eProductId.Pid_11_Explorer&&product.productId!=eProductId.Pid_12_Space).length}个应用)`})}),vipFlg==1&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'right',fontSize:12,color:'#999',marginTop:5},children:[\"\\u652F\\u4ED8\\u6210\\u529F\\u540E\\uFF0C\\u53EF\\u524D\\u5F80 \",/*#__PURE__*/_jsx(\"a\",{style:{color:'#0077F2'},onClick:()=>navigateTo(1),children:\"\\u4E2A\\u4EBA\\u4E2D\\u5FC3\"}),\" \",'->',\" \",/*#__PURE__*/_jsx(\"a\",{style:{color:'#0077F2'},onClick:()=>navigateTo(2),children:\"\\u53D1\\u7968\\u7BA1\\u7406\"}),\" \\u8FDB\\u884C\\u5F00\\u7968\"]})]})]})]}),/*#__PURE__*/_jsx(PayCreateTeamModal,{teamId:teamId,visible:isPaying,onCancel:cancelPay,priceInfo:priceInfo,onOk:sucessPay,payCode:payCode,url:url,url1:url1}),/*#__PURE__*/_jsx(PriceDetailModal,{visible:showPriceDetail,onCancel:()=>setShowPriceDetail(false)}),/*#__PURE__*/_jsxs(DraggablePopUp,{title:\"\\u63D0\\u793A\",className:\"tms-modal\",width:420,open:tipShow,onCancel:()=>setTipShow(false),centered:true,maskClosable:false,footer:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(Button,{type:'primary',style:{borderRadius:5},onClick:()=>setTipShow(false),children:\"\\u6211\\u77E5\\u9053\\u4E86\"})}),children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',marginTop:20},children:\"\\u56E0\\u51CF\\u5458\\u7B49\\u56E0\\u7D20\\uFF0C\\u8BA2\\u5355\\u91D1\\u989D\\uFF1C0\\uFF0C\\u60A8\\u53EF\\u7EE7\\u7EED\\u589E\\u8D2D\\u5E94\\u7528\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',marginBottom:20},children:\"\\u6216\\u5EF6\\u7EED\\u65F6\\u957F\\uFF0C\\u786E\\u4FDD\\u8BA2\\u5355\\u91D1\\u989D\\u22650\\u3002\"})]})]});}function PayCreateTeam(_ref2){let{onOk,teamId,priceInfo,payCode,url,url1}=_ref2;const[form]=Form.useForm();const payMethod=Form.useWatch('payMethod',form);//const [refreshingFlg,setRefreshingFlg] = useState(false);\nuseEffect(()=>{const timer=!(payCode&&payCode!==null&&payCode!==void 0&&payCode.orderId)||setInterval(()=>{let params={orderId:payCode.orderId};if(!!teamId){params.teamId=teamId;}team_705_get_order_status(params).then(result=>{if(result.resultCode==200){if(result.statusType==eOrderStatus.Status_1_Paid){clearInterval(timer);onOk&&onOk(result.teamId);globalUtil.success('购买成功');}}});},1000);return()=>{clearInterval(timer);};},[payCode]);useEffect(()=>{if(payMethod){}},[payMethod]);return/*#__PURE__*/_jsx(Form,{className:\"PayCreateTeam-form\",form:form,name:\"basic\"// {...formItemLayout}\n,autoComplete:\"off\",children:/*#__PURE__*/_jsxs(Form.Item,{noStyle:true,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"pay-images\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"image\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"image-header\",children:[/*#__PURE__*/_jsx(\"img\",{width:24,height:24,src:require(\"@assets/images/createTeam/wxpay.png\")}),\"\\u5FAE\\u4FE1\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"image-content\",style:{backgroundColor:'#04C161'},children:url?/*#__PURE__*/_jsx(Image,{src:url,width:120,height:120,preview:false,placeholde:/*#__PURE__*/_jsx(TLoading,{})}):/*#__PURE__*/_jsx(\"div\",{className:\"invalid_QR_code\",children:\"\\u4E8C\\u7EF4\\u7801\\u65E0\\u6548\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"image\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"image-header\",children:[/*#__PURE__*/_jsx(\"img\",{width:24,height:24,src:require(\"@assets/images/createTeam/alipay.png\")}),\"\\u652F\\u4ED8\\u5B9D\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"image-content\",style:{backgroundColor:'#1273FF'},children:url1?/*#__PURE__*/_jsx(Image,{src:url1,width:120,height:120,preview:false,placeholde:/*#__PURE__*/_jsx(TLoading,{})}):/*#__PURE__*/_jsx(\"div\",{className:\"invalid_QR_code\",children:\"\\u4E8C\\u7EF4\\u7801\\u65E0\\u6548\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"pay-tips\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"create-team-price-color\",style:{marginLeft:10,marginRight:10},children:\"\\uFFE5\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ant-form-text create-team-discountPrice create-team-price-color\",children:priceInfo.payPrice})]})]})});}function PayCreateTeamModal(_ref3){let{teamId,visible=false,onCancel,priceInfo,onOk,payCode,url,url1}=_ref3;return/*#__PURE__*/_jsx(DraggablePopUp,{className:\"createTeamDraModal\",title:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center'},children:[\"\\u626B\\u7801\\u652F\\u4ED8\",/*#__PURE__*/_jsxs(\"span\",{className:\"fontsize-12\",style:{marginLeft:10,color:'#999'},children:[\"\\u6D41\\u6C34\\u7F16\\u53F7\\uFF1A\",(payCode===null||payCode===void 0?void 0:payCode.orderId)||'']})]}),width:520,open:visible,onCancel:onCancel,centered:true,footer:null,maskClosable:false,destroyOnClose:true,children:/*#__PURE__*/_jsx(PayCreateTeam,{priceInfo:priceInfo,teamId:teamId,onOk:onOk,payCode:payCode,url:url,url1:url1})});}function PriceDetailModal(_ref4){let{visible=false,onCancel}=_ref4;return/*#__PURE__*/_jsx(DraggablePopUp,{className:\"createTeamDraModal\",title:\"\\u4EF7\\u683C\\u8BE6\\u60C5\",width:800,open:visible,onCancel:onCancel,centered:true,footer:null,maskClosable:false,destroyOnClose:true,children:/*#__PURE__*/_jsx(PriceDetailTable,{})});}function PriceDetailTable(_ref5){let{}=_ref5;const[dataSource,setDataSource]=useState([]);const columns=[{title:'应用',dataIndex:'productName',key:'productName'},{title:'原价',dataIndex:'oldPrice',key:'oldPrice'},{title:'现价',dataIndex:'nowPrice',key:'nowPrice'},{title:'购买日期',dataIndex:'buyDate',key:'buyDate'},{title:'有效期至',dataIndex:'expiration',key:'expiration'},{title:'剩余时长(天)',dataIndex:'remainingDuration',key:'remainingDuration'},{title:'原价：扩容单人',dataIndex:'oldPersonalPrice',key:'oldPersonalPrice'},{title:'现价：扩容单人',dataIndex:'nowPersonalPrice',key:'nowPersonalPrice'},{title:'折扣减',dataIndex:'rate',key:'rate'}];return/*#__PURE__*/_jsx(Table,{size:\"small\",columns:columns,dataSource:dataSource,pagination:{position:['bottomCenter'],size:'small',showSizeChanger:true,showQuickJumper:true,total:dataSource.length,showTotal:total=>{return`共${total}条`;}}});}export default function CreateTeamModal(_ref6){let{visible=false,onCancel,teamId,onOk,type=CREATETYPE_CREATE,priceData,productList}=_ref6;const[isChange,setIsChange]=useState(false);// 新增：动态设置浏览器标题\nconst prevTitle=useRef(document.title);useEffect(()=>{if(visible){prevTitle.current=document.title;document.title=type===CREATETYPE_UPGRADE?\"应用管理-购买\":\"新建团队\";}else{document.title=prevTitle.current;}return()=>{document.title=prevTitle.current;};},[visible,type]);function checkClose(){if(isChange){Modal.confirm({title:'提示',icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{style:{color:'orange'}}),centered:true,content:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column'},children:/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u6B63\\u5728\",`${type==CREATETYPE_CREATE?'创建团队':'购买应用'}`,\"\\uFF0C\\u662F\\u5426\\u653E\\u5F03\",`${type==CREATETYPE_CREATE?'创建':'购买'}`,\" \\uFF1F\"]})}),okText:'是',cancelText:'否',onOk:()=>{onCancel();setIsChange(false);}});}else{onCancel();}}return/*#__PURE__*/_jsx(DraggableDrawer,{className:\"tms-drawer createTeamDrawer\",centered:true,title:type===CREATETYPE_UPGRADE?\"应用管理-购买\":\"新建团队\",width:'60%',minWidth:'50%',maxWidth:'95%',open:visible,onClose:checkClose,footer:null,destroyOnClose:true,children:/*#__PURE__*/_jsx(CreateTeam,{teamId:teamId,onCancel:onCancel,onOk:onOk,type:type,priceData:priceData,productList:productList,setIsChange:setIsChange})});}/*unused\r\n//应用续费、应用扩容、应用新购\r\nexport function BuyTeamModal({visible=false,onCancel,teamId,onOk,type=CREATETYPE_EXPAND,defaultPackageList=[]}) {\r\n  const [createType,setCreateType] = useState(CREATETYPE_EXPAND)\r\n  const [loading,setLoading] = useState(true)\r\n  const [_defaultPackageList,setDefaultPackageList] = useState([])\r\n  const [hideBuy,setHideBuy] = useState(false);\r\n\r\n  useEffect(()=>{\r\n    if(visible){\r\n      setLoading(true)\r\n      getProductsList();\r\n      setDefaultPackageList(defaultPackageList)\r\n      setCreateType(type)\r\n    }\r\n  },[visible])\r\n\r\n  useEffect(()=>{\r\n    if(visible){\r\n      setLoading(true)\r\n      if(type === createType){\r\n        console.log(\"defaultPackageList\",defaultPackageList)\r\n        setDefaultPackageList(defaultPackageList)\r\n      }else{\r\n        setDefaultPackageList([])\r\n      }\r\n      setTimeout(()=>{\r\n        setLoading(false)\r\n      },300)\r\n    }\r\n  },[createType])\r\n\r\n  async function getProductsList() {\r\n    await team_711_get_team_product_list({teamId}).then(res => {\r\n        if(res.resultCode == 200){\r\n            let allProductsList = (res.productList||[])\r\n            setHideBuy(allProductsList.filter(product => !product.expirationDt).length == 0)\r\n        }\r\n    });\r\n    setTimeout(()=>{\r\n      setLoading(false)\r\n    },300)\r\n}\r\n\r\n  return <DraggablePopUp\r\n    className=\"createTeamDraModal\"\r\n    title={\r\n      <Radio.Group className=\"createTeamTypeRadio\" value={createType} onChange={(e)=>{setCreateType(Number(e.target.value))}}>\r\n        <Radio.Button value={CREATETYPE_EXPAND}>成员扩容</Radio.Button>\r\n        <Radio.Button value={CREATETYPE_RENEWAL}>应用续费</Radio.Button>\r\n        {!hideBuy && <Radio.Button value={CREATETYPE_BUY}>应用新购</Radio.Button>}\r\n      </Radio.Group>\r\n    }\r\n    width={1000}\r\n    open={visible}\r\n    onCancel={onCancel}\r\n    maskClosable={false}\r\n    footer={null}\r\n    centered\r\n    destroyOnClose>\r\n      {loading?\r\n        <TLoading/>\r\n        :\r\n        <CreateTeam teamId={teamId}\r\n                    onCancel={onCancel}\r\n                    onOk={onOk}\r\n                    type={createType}\r\n                    defaultPackageList={_defaultPackageList}/>}\r\n    </DraggablePopUp>\r\n}\r\n*/", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}