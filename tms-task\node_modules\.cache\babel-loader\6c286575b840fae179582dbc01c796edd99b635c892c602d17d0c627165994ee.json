{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { equals } from '../../base/common/objects.js';\n/**\n * Vertical Lane in the overview ruler of the editor.\n */\nexport var OverviewRulerLane;\n(function (OverviewRulerLane) {\n  OverviewRulerLane[OverviewRulerLane[\"Left\"] = 1] = \"Left\";\n  OverviewRulerLane[OverviewRulerLane[\"Center\"] = 2] = \"Center\";\n  OverviewRulerLane[OverviewRulerLane[\"Right\"] = 4] = \"Right\";\n  OverviewRulerLane[OverviewRulerLane[\"Full\"] = 7] = \"Full\";\n})(OverviewRulerLane || (OverviewRulerLane = {}));\n/**\n * Position in the minimap to render the decoration.\n */\nexport var MinimapPosition;\n(function (MinimapPosition) {\n  MinimapPosition[MinimapPosition[\"Inline\"] = 1] = \"Inline\";\n  MinimapPosition[MinimapPosition[\"Gutter\"] = 2] = \"Gutter\";\n})(MinimapPosition || (MinimapPosition = {}));\nexport var InjectedTextCursorStops;\n(function (InjectedTextCursorStops) {\n  InjectedTextCursorStops[InjectedTextCursorStops[\"Both\"] = 0] = \"Both\";\n  InjectedTextCursorStops[InjectedTextCursorStops[\"Right\"] = 1] = \"Right\";\n  InjectedTextCursorStops[InjectedTextCursorStops[\"Left\"] = 2] = \"Left\";\n  InjectedTextCursorStops[InjectedTextCursorStops[\"None\"] = 3] = \"None\";\n})(InjectedTextCursorStops || (InjectedTextCursorStops = {}));\nexport class TextModelResolvedOptions {\n  /**\n   * @internal\n   */\n  constructor(src) {\n    this._textModelResolvedOptionsBrand = undefined;\n    this.tabSize = Math.max(1, src.tabSize | 0);\n    this.indentSize = src.tabSize | 0;\n    this.insertSpaces = Boolean(src.insertSpaces);\n    this.defaultEOL = src.defaultEOL | 0;\n    this.trimAutoWhitespace = Boolean(src.trimAutoWhitespace);\n    this.bracketPairColorizationOptions = src.bracketPairColorizationOptions;\n  }\n  /**\n   * @internal\n   */\n  equals(other) {\n    return this.tabSize === other.tabSize && this.indentSize === other.indentSize && this.insertSpaces === other.insertSpaces && this.defaultEOL === other.defaultEOL && this.trimAutoWhitespace === other.trimAutoWhitespace && equals(this.bracketPairColorizationOptions, other.bracketPairColorizationOptions);\n  }\n  /**\n   * @internal\n   */\n  createChangeEvent(newOpts) {\n    return {\n      tabSize: this.tabSize !== newOpts.tabSize,\n      indentSize: this.indentSize !== newOpts.indentSize,\n      insertSpaces: this.insertSpaces !== newOpts.insertSpaces,\n      trimAutoWhitespace: this.trimAutoWhitespace !== newOpts.trimAutoWhitespace\n    };\n  }\n}\nexport class FindMatch {\n  /**\n   * @internal\n   */\n  constructor(range, matches) {\n    this._findMatchBrand = undefined;\n    this.range = range;\n    this.matches = matches;\n  }\n}\n/**\n * @internal\n */\nexport function isITextSnapshot(obj) {\n  return obj && typeof obj.read === 'function';\n}\n/**\n * @internal\n */\nexport class ValidAnnotatedEditOperation {\n  constructor(identifier, range, text, forceMoveMarkers, isAutoWhitespaceEdit, _isTracked) {\n    this.identifier = identifier;\n    this.range = range;\n    this.text = text;\n    this.forceMoveMarkers = forceMoveMarkers;\n    this.isAutoWhitespaceEdit = isAutoWhitespaceEdit;\n    this._isTracked = _isTracked;\n  }\n}\n/**\n * @internal\n */\nexport class SearchData {\n  constructor(regex, wordSeparators, simpleSearch) {\n    this.regex = regex;\n    this.wordSeparators = wordSeparators;\n    this.simpleSearch = simpleSearch;\n  }\n}\n/**\n * @internal\n */\nexport class ApplyEditsResult {\n  constructor(reverseEdits, changes, trimAutoWhitespaceLineNumbers) {\n    this.reverseEdits = reverseEdits;\n    this.changes = changes;\n    this.trimAutoWhitespaceLineNumbers = trimAutoWhitespaceLineNumbers;\n  }\n}\n/**\n * @internal\n */\nexport function shouldSynchronizeModel(model) {\n  return !model.isTooLargeForSyncing() && !model.isForSimpleWidget;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}