import { setting_234_get_team_mbr_user_info_query, setting_get_team_import_setting_query } from "@common/api/query/query";
import {  useQuerySetting202_getTeamAllUsers } from "@common/service/commonHooks";
import { globalEventBus } from "@common/utils/eventBus";
import { globalUtil } from "@common/utils/globalUtil";
import DefaultAvatar from "@components/DefaultAvatar";
import { useQuery } from "@tanstack/react-query";
import { Button, Checkbox, DatePicker, /* Drawer, */ Select } from "antd";
import DraggableDrawer from "@common/components/DraggableDrawer";
import moment from "moment";
import { Suspense, lazy, useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import "./../IssueDetail/IssueDetail.scss";

const CreateIssueContent = lazy(() => import("./CreateIssueContent"))

// 新建Issue弹出层
// 新建issue选择人员更改为当前协作群有效人员 Walt 2023-03-27
export default function CreateIssueDrawer() {

  const ref = useRef(Object.create(null));
  const { teamId } = useParams();
  const [createAnotherIssueFlg, setCreateAnotherIssueFlg] = useState(false); //是否继续新建另一个issue
  const [uploadLoading, setUploadLoading] = useState(false); // 提交加载中  
  const [nodeItem, setNodeItem] = useState({});
  const [open, setOpen] = useState(false);
  const [drawerWidth, setDrawerWidth] = useState(800); // 添加drawer宽度状态，设置初始值为800px

  // projectInfo：项目信息，用于显示别名
  const { nodeId:issueListNodeId, projectInfo, callback } = nodeItem;

  // 新增：动态设置浏览器标题
  const prevTitle = useRef(document.title);
  useEffect(() => {
    if (open) {
      prevTitle.current = document.title;
      document.title = `新建${projectInfo?.issueAlias ?? "问题"}`;
    } else {
      document.title = prevTitle.current;
    }
    return () => {
      document.title = prevTitle.current;
    };
  }, [open, projectInfo?.issueAlias]);

  const {data: { userId, issueZoomFlg } = { userId: null, issueZoomFlg: 0 }, /*isLoading: isLoading234, refetch: refetchUserInfo*/ }
    = useQuery({
    ...setting_234_get_team_mbr_user_info_query(teamId)
  });

  const { data: {issueImportFlg} = {issueImportFlg: 0} } = useQuery({...setting_get_team_import_setting_query(teamId, issueListNodeId, !!issueListNodeId)});
  const { data: userList = [] } =  useQuerySetting202_getTeamAllUsers(teamId, issueImportFlg == 1);
  const [creator,setCreator] = useState(null);
  const [createDt,setCreateDt] = useState('');

  useEffect(() => {
    globalEventBus.on("openCreateIssueModalEvent", openCreateIssueModalEvent);
    return () => {
      globalEventBus.off("openCreateIssueModalEvent", openCreateIssueModalEvent)
    }
  }, []);

  function openCreateIssueModalEvent(target, args) {
    setNodeItem(args);
    setOpen(true);
  }

  function setTeamMbrUser(){
    if(!teamId && !userId){
      return globalUtil.error('数据获取错误');
    }
    // DraggableDrawer 内部已管理缩放，无需本地 largeView 状态
    // 这里只负责通知后端
    // 这里可以根据需要传递 zoomFlg，假设 DraggableDrawer 提供了当前缩放状态
    // 例如: let zoomFlg = ...
    // let params = { teamId, userId, issueZoomFlg: zoomFlg }
    // setTeamMbrUserMutation(params);
  }


  // TODO: 更改创建人创建时间未对接接口
  function creatorChange(e){
    setCreator(!!e ? e : null);
  }

  function createDtChange(e){
    let dateTime = !!e ? moment(e).format('YYYY-MM-DD HH:mm') : ''
    setCreateDt(dateTime);
  }


  return <>
    <DraggableDrawer
      className="tms-drawer add-issue"
      minWidth="30%"
      maxWidth="95%"
      draggableFlag={true}
      fixedMinWidth="60%"
      fixedMaxWidth="90%"
      onClose={()=> ref.current?.onCancel?.()}
      onWidthChange={(width) => setDrawerWidth(width)} // 监听宽度变化
      open={open}
      closable={true}
      destroyOnClose={true}
      title={
        <div style={{display:'flex',alignItems:'center'}}>
          <span>{`新建${projectInfo?.issueAlias??"问题"}`}</span>
          <a 
            onClick={setTeamMbrUser} 
            style={{ paddingLeft: 20,color:'#000', cursor: 'pointer' }} 
            title={'缩放'}
            className={'iconfont fangda-suoxiao'} // 你可以根据 DraggableDrawer 的缩放状态动态切换图标
          />
        </div>
      }
      footer={<div style={{ display:'flex',alignItems:'center',justifyContent:'end' }} >
        <div style={{ display:'flex',alignItems:'center',marginRight:10 }} >
          {issueImportFlg == 1 ? 
            <Select 
                showSearch
                style={{marginRight:10,width:150}}
                placeholder='创建人'
                optionFilterProp="children"
                filterOption={(input, option) => (option?.key ?? '').toLowerCase().includes(input.toLowerCase())}
                dropdownMatchSelectWidth={false}
                allowClear
                //value={creator}
                onChange={creatorChange}
                options={(userList||[]).map(user => ({
                  value: user.userId,
                  label: <div style={{display:'flex',alignItems:'center'}}><DefaultAvatar avatarSrc={user.avatar} avatarFlg={user.userName}/><span className={`member-select-${user.deleteFlg > 0 ? 'delete' : user.enableFlg == 0 ? 'enable' : 'normal'}`}>{user.userName}</span></div>,
                  key: user.userName,
                }))}
            />
          :
            null
          }
          {issueImportFlg == 1 ?
            <DatePicker 
              style={{borderRadius:5}} 
              placeholder="创建时间"
              showTime={{ format: 'HH:mm' }} 
              format="YYYY-MM-DD HH:mm"
              allowClear
              //value={createDt}
              onChange={createDtChange}
            />
          :
            null
          }
        </div>
        <Checkbox style={{ paddingRight: 20 }} onChange={(e) => setCreateAnotherIssueFlg(e.target.checked)}>继续新建</Checkbox>
        <Button loading={uploadLoading} type="primary" style={{ borderRadius: 5 }} onClick={()=>ref.current?.onOk?.()}>提交</Button>
      </div>}
    >
      <Suspense>
        <CreateIssueContent
          ref={ref}
          nodeItem={nodeItem}
          createAnotherIssueFlg={createAnotherIssueFlg}
          setUploadLoading={setUploadLoading}
          uploadLoading={uploadLoading}
          setOpen={setOpen}
          drawerWidth={drawerWidth} // 传递drawer宽度
        />
      </Suspense>
    </DraggableDrawer>
  </>;
}