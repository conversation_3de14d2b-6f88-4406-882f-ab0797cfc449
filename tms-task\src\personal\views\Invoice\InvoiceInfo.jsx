import { Select, Form, Space, List, Button, Drawer, Input, Image, Tag, Pagination, Radio, Checkbox } from "antd";
import React, { useEffect, useState } from "react";
import "./InvoiceInfo.scss";
import * as http from "@common/api/http";
import { isEmpty } from '@common/utils/ArrayUtils'
import { globalUtil } from "@common/utils/globalUtil";

// 发票信息
export default function InvoiceInfo({orderTitalInfo,getInvoiceHistory,form,onCloseInvoiceDrawer}) {
  
  useEffect(() => {
    form.setFieldsValue({
      invoiceType: 1,
      invoiceHead: 1,
      invoiceContent: 1,
      invoiceHeadName: '',
      companyNum: '',
      address: '',
      taxPayerTel: '',
      bank: '',
      openingAccount: '',
      mailBox: '',
      contactTel: '',
      invoicePrice: orderTitalInfo.amount.toFixed(2)
    })
  },[])

  // 发票类型切换
  const invoiceTypeChange = (e) => {
    form.setFieldValue("invoiceHead", 1)
  }

  const validateEmail = (rule, value) => {
    if(isEmpty(value)){
      return Promise.reject(new Error("请输入您的邮箱"));
    }
    var email = value.replace(/\s/g, ""); //去除空格
    let regs = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
    if (email.length != 0) {
      if (!regs.test(email)) {
        return Promise.reject(new Error("邮箱输入不合法"));
      } else {
        return Promise.resolve();
      }
    }
    return Promise.reject(new Error("请输入您的邮箱"));
  };

  // 正则校验修改手机号,使用Promise方式代替callback
  const validateMobileNo = (rule, value) => {
    if(isEmpty(value)){
      return Promise.resolve();
    }
    var phone = value.replace(/\s/g, ""); //去除空格
    //校验手机号，号段主要有(不包括上网卡)：130~139、150~153，155~159，180~189、170~171、176~178。14号段为上网卡专属号段
    let regs = /^((13[0-9])|(17[0-1,6-8])|(15[^4,\\D])|(18[0-9])|(19[0-9]))\d{8}$/;
    if (phone.length != 0) {
      if (!regs.test(phone)) {
        return Promise.reject(new Error("手机号输入不合法"));
      } else {
        return Promise.resolve();
      }
    }
    return Promise.resolve();
  };

  const handleOk = (value) => {
    const {invoiceType,invoiceHead,invoiceContent,invoicePrice,mailBox,invoiceHeadName,companyNum,taxPayerTel,contactTel,address,bank,openingAccount} = value
    let params = {
      invoiceType: invoiceType,
      taxPayerType: invoiceHead,
      contentType: invoiceContent,
      amount: invoicePrice,
      contactEmail: mailBox,
      orderIdList: orderTitalInfo.orderIdList
    }
    if(parseFloat(invoicePrice) == 0){
      globalUtil.info('开票金额为￥0，无需开票');
      return
    }else if(parseFloat(invoicePrice) > 0 && parseFloat(invoicePrice) < 100){
      globalUtil.info(`开票金额￥${invoicePrice}，低于最低开票金额￥100元，可选多张订单凑满金额后再开票。`);
      return
    }
    if(invoiceHeadName){
      params.taxPayerName = invoiceHeadName
    }
    if(companyNum){
      params.taxPayerNo = companyNum
    }

    if(address){
      params.taxPayerAddress = address
    }
    if(taxPayerTel){
      params.taxPayerTel = taxPayerTel  //联系电话
    }
    if(bank){
      params.taxPayerBankName = bank
    }
    if(openingAccount){
      params.taxPayerBankAcctNo = openingAccount
    }

    if(contactTel){
      params.contactTel = contactTel //手机号码
    }
    
    http.team_577_submit_invoice_request(params).then(res => {
      if(res.resultCode == 200){
        getInvoiceHistory();
        onCloseInvoiceDrawer();
        globalUtil.success('提交成功');
      }
    });
  }

  const style = {borderRadius:5}
  
  return (
    <Form
    className="Invoice-form"
    name="Invoice"
    form={form}
    colon={false}
    labelCol={{ span: 6 }}
    wrapperCol={{ offset: 1, span: 12 }}
    onFinish={handleOk}
    >
      <Form.Item
        name="invoiceType"
        label="发票类型"
        rules={[
          {
            required: true,
            message: "请选择发票类型！",
          },
        ]}
      >
        <Radio.Group onChange={invoiceTypeChange}>
          <Radio value={1}>增值税普通发票</Radio>
          <Radio value={2}>增值税专用发票</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item
      noStyle
      shouldUpdate={(pre, cur) => pre.invoiceType !== cur.invoiceType}>
        {({getFieldValue}) => (
          <Form.Item
            name="invoiceHead"
            label="发票抬头"
            rules={[
              {
                required: true,
                message: "请选择发票抬头！",
              },
            ]}
          >
            <Radio.Group>
              <Radio value={1}>公司</Radio>
              <Radio value={2} disabled={getFieldValue("invoiceType") == 2}>个人</Radio>
            </Radio.Group>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item
      noStyle
      shouldUpdate={(pre, cur) => pre.invoiceHead !== cur.invoiceHead}>
        {({getFieldValue}) => (
          getFieldValue("invoiceHead") === 1
           ? 
            <>
              <Form.Item
              name="invoiceHeadName"
              rules={[
                {
                  required: true,
                  message: "请输入发票抬头",
                },
              ]}
              wrapperCol={{ offset: 7, span: 12 }}
              >
                <Input placeholder="请填写发票抬头" autocomplete="noff" style={style}/>
              </Form.Item>

              <Form.Item
              name="companyNum"
              rules={[
                {
                  required: true,
                  message: "请输入公司税号",
                },
              ]}
              wrapperCol={{ offset: 7, span: 12 }}
              >
                <Input placeholder="请填写公司税号" autocomplete="off" style={style}/>
              </Form.Item>
            </>
           : ''
        )}
      </Form.Item>

      <Form.Item
        name="invoiceContent"
        label="发票内容"
        rules={[
          {
            required: true,
            message: "请选择发票内容！",
          },
        ]}
      >
        <Radio.Group>
          <Radio value={1}>商品大类</Radio>
          <Radio value={2}>商品明细</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item
        name="invoicePrice"
        label="开票金额"
      >
        <InvoicePrice/>
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(pre, cur) => pre.invoiceType !== cur.invoiceType}>
        {({getFieldValue}) => (
          getFieldValue('invoiceType') == 2 &&
          <Form.Item
            name="address"
            label="联系地址"
            rules={[{required: true, message: '请输入联系地址'}]}
          >
            <Input placeholder="请填写联系地址" autocomplete="off" style={style}/>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(pre, cur) => pre.invoiceType !== cur.invoiceType}>
        {({getFieldValue}) => (
          getFieldValue('invoiceType') == 2 &&    
          <Form.Item
            name="taxPayerTel"
            label="联系电话"
            rules={[{required: true, message: '请输入联系电话'}]}
          >
            <Input placeholder="请填写联系电话" autocomplete="off" style={style}/>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(pre, cur) => pre.invoiceType !== cur.invoiceType}>
        {({getFieldValue}) => (
          getFieldValue('invoiceType') == 2 &&      
          <Form.Item
            name="bank"
            label="开户银行"
            rules={[{required: true, message: '请输入开户银行'}]}
          >
            <Input placeholder="请填写开户银行" autocomplete="off" style={style}/>
          </Form.Item>     
        )}
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(pre, cur) => pre.invoiceType !== cur.invoiceType}>
        {({getFieldValue}) => (
          getFieldValue('invoiceType') == 2 &&      
          <Form.Item
            name="openingAccount"
            label="开户账号"
            rules={[{required: true, message: '请输入开户账号'}]}
          >
            <Input placeholder="请填写开户账号" autocomplete="off" style={style}/>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item
        name="mailBox"
        label="电子邮箱"
        rules={[
          {
            required: true,
            validator: validateEmail
          },
        ]}
      >
        <Input placeholder="请填写有效的电子邮箱" autocomplete="off" style={style}/>
      </Form.Item>

      <Form.Item
        label="手机号码"
      >
        <div>
            <Form.Item
                name="contactTel"
                style={{ margin: 0 }}
                rules={[{validator: validateMobileNo}]}
            >
                <Input placeholder="请填写手机号码" autocomplete="off" style={style}/>
            </Form.Item>
            <div style={{marginTop:5,color:'#CCCCCC'}}>若发票信息有疑问时，方便iTeam与您电话联络。</div>
        </div> 
      </Form.Item>
    </Form>
  )
}

// 开票金额
function InvoicePrice({value, onChange}) {
  useEffect(() => {
    if(!!value) {
      onChange?.(value)
    }
  },[value])
  return (
    <>
      <span>￥</span>
      <span style={{ color: 'red' }}>{value}</span>
    </>
  )
}