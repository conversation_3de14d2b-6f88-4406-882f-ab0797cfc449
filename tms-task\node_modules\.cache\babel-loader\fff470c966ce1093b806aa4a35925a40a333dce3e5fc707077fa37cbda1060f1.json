{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\settings\\\\views\\\\SettingsDrawer.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { Layout, Drawer, Button, Space, Tabs } from \"antd\";\nimport { useParams, Link, useLocation, useNavigate } from \"react-router-dom\";\nimport { shallowEqual, useDispatch, useSelector } from \"react-redux\";\nimport { eMembergrpType } from \"@common/utils/enum\";\nimport CreateInvitedMember from \"src/settings/utils/CreateInvitedMember/CreateInvitedMember\";\nimport SettingsSideBar from \"./SettingsSideBar\";\n\n// 导入设置页面组件\nimport BasicInfoTab from \"./basicInfo/BasicInfoTab\";\nimport TeamDomainSmtpTab from \"./basicInfo/SmtpTab\";\nimport Personal_Tab_Subscribe from \"./personal/Manager_Tab_Subscribe\";\nimport Personal_Tab_Data_Import from \"./personal/Personal_Tab_Data_Import\";\nimport Settings_Product from \"./product/Settings_Product\";\nimport MemberTab from \"./user/MemberTab\";\nimport AdminTab from \"./user/AdminTab\";\nimport InviteHistory from \"./user/InviteHistory\";\nimport Personal from \"./personal/Personal\";\nimport SpaceRoleTab from \"./space/SpaceRoleTab\";\nimport SpaceTrashTab from \"./space/SpaceTrashTab\";\nimport SpaceShareLinkHistoryTab from \"./space/SpaceShareLinkHistoryTab\";\nimport MemberTab_Approval from \"./user/MemberTab_Approval\";\nimport DraggablePopUp from '@components/DraggablePopUp';\nimport DraggableDrawer from '@common/components/DraggableDrawer';\nimport { useQuerySetting327_getTeamSpaceAadmin } from \"@common/service/commonHooks\";\nimport \"./SettingsDrawer.scss\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Sider,\n  Content\n} = Layout;\nconst {\n  TabPane\n} = Tabs;\nexport default function SettingsDrawer({\n  visible,\n  onClose,\n  teamId,\n  defaultTab,\n  productId\n}) {\n  _s();\n  //productId: 是打开\"应用管理\"某一个产品的的授权对话框\n  const [approvalNum, setApprovalNum] = useState(0);\n  const [createInvitedMemberVisible, setCreateInvitedMemberVisible] = useState(false);\n  const [currentContent, setCurrentContent] = useState(defaultTab || 'basic');\n  const [currentSpaceId, setCurrentSpaceId] = useState(null);\n  const [activeTab, setActiveTab] = useState('base-info');\n  const [closeConfirmVisible, setCloseConfirmVisible] = useState(false);\n  // 新增：用于高亮“申请加入”分组\n  const [approvalGroupId, setApprovalGroupId] = useState(null);\n  const [approvalGroupType, setApprovalGroupType] = useState(null);\n\n  // 新增：动态设置浏览器标题\n  const prevTitle = useRef(document.title);\n  useEffect(() => {\n    if (visible) {\n      prevTitle.current = document.title;\n      document.title = \"团队设置\";\n    } else {\n      document.title = prevTitle.current;\n    }\n    return () => {\n      document.title = prevTitle.current;\n    };\n  }, [visible]);\n\n  // 监听defaultTab变化，动态切换tab\n  useEffect(() => {\n    if (visible && defaultTab) {\n      setCurrentContent(defaultTab);\n      // 根据tab类型设置activeTab\n      if (defaultTab === 'basic') setActiveTab('base-info');\n      if (defaultTab === 'user') setActiveTab('member');\n      if (defaultTab === 'product') setActiveTab('product');\n    }\n  }, [visible, defaultTab]);\n  const state = useSelector(state => ({\n    userGrpList: state.getIn([\"workSetUp\", \"userGrpList\"])\n  }), shallowEqual);\n  const userGrpList = state.userGrpList || [];\n  //const { spaceId } = useParams();\n  // 提前解构，避免未初始化时被访问\n  const {\n    data: {\n      teamAdminFlag: isManager,\n      spaceAdminFlag: isSpaceManager,\n      teamSpaceAdminFlag\n    } //teamSpaceAdminFlag表示，只要是团队中的“某一个群”的管理员，它就为true\n    = {\n      teamAdminFlag: undefined,\n      spaceAdminFlag: undefined,\n      teamSpaceAdminFlag: undefined\n    }\n  } = useQuerySetting327_getTeamSpaceAadmin({\n    teamId,\n    spaceId: currentSpaceId,\n    enabled: true\n  });\n\n  // 自动切换Tab副作用，避免在renderContent中setState\n  useEffect(() => {\n    if (currentContent === 'space' && !(isManager || isSpaceManager || teamSpaceAdminFlag) && activeTab !== 'member') {\n      setActiveTab('member');\n    }\n  }, [currentContent, isManager, isSpaceManager, teamSpaceAdminFlag, activeTab]);\n\n  // 设置申请加入数量\n  useEffect(() => {\n    console.log('SettingsDrawer: userGrpList changed:', userGrpList);\n    if ((userGrpList || []).length > 0) {\n      var _userGrpList$find;\n      const approvalCount = ((_userGrpList$find = userGrpList.find(e => e.membergrpType === eMembergrpType.grp_2_apply_join)) === null || _userGrpList$find === void 0 ? void 0 : _userGrpList$find.membergrpNum) || 0;\n      console.log('SettingsDrawer: setting approvalNum to:', approvalCount);\n      setApprovalNum(approvalCount);\n    }\n  }, [userGrpList]);\n\n  // 处理申请加入点击 - 显示申请列表并高亮分组\n  const handleApprovalClick = () => {\n    setCurrentContent('user');\n    // 找到“申请加入”分组\n    const approvalGroup = userGrpList.find(item => item.membergrpType === 2); // eMembergrpType.grp_2_apply_join = 2\n    if (approvalGroup) {\n      setApprovalGroupId(approvalGroup.groupId);\n      setApprovalGroupType(approvalGroup.membergrpType);\n    }\n  };\n\n  // 处理SettingsSideBar的内容变化\n  const handleContentChange = (content, spaceId = null, tab = null) => {\n    console.log('SettingsDrawer: content changed to', content, spaceId, tab);\n    setCurrentContent(content);\n    setCurrentSpaceId(spaceId);\n    if (tab) {\n      setActiveTab(tab);\n    } else if (content === 'basic') {\n      // 当选择\"基础设置\"时，默认选中\"基本设置\"Tab\n      setActiveTab('base-info');\n    } else if (content === 'user') {\n      // 当选择\"成员及管理员\"时，默认选中\"成员\"Tab\n      setActiveTab('member');\n    } else if (content === 'space') {\n      // 当选择协作群时，默认选中\"角色权限\"Tab\n      setActiveTab('role');\n    }\n  };\n\n  // 渲染右侧内容\n  const renderContent = () => {\n    var _userGrpList$find2, _userGrpList$find3, _userGrpList$find4, _userGrpList$find5;\n    switch (currentContent) {\n      case 'basic':\n        return /*#__PURE__*/_jsxDEV(Tabs, {\n          activeKey: activeTab,\n          onChange: setActiveTab,\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u57FA\\u672C\\u8BBE\\u7F6E\",\n            children: /*#__PURE__*/_jsxDEV(BasicInfoTab, {\n              teamId: teamId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)\n          }, \"base-info\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u57DF\\u540D&SMTP\\u670D\\u52A1\\u5668\",\n            children: /*#__PURE__*/_jsxDEV(TeamDomainSmtpTab, {\n              teamId: teamId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)\n          }, \"smtp\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u7CFB\\u7EDF\\u63A8\\u9001\\u8BBE\\u7F6E\",\n            children: /*#__PURE__*/_jsxDEV(Personal_Tab_Subscribe, {\n              teamId: teamId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, \"subscribe\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u6570\\u636E\\u5BFC\\u5165\",\n            children: /*#__PURE__*/_jsxDEV(Personal_Tab_Data_Import, {\n              teamId: teamId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, \"import\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this);\n      case 'product':\n        return /*#__PURE__*/_jsxDEV(Settings_Product, {\n          teamId: teamId,\n          productId: productId,\n          visible: visible\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 16\n        }, this);\n      case 'user':\n        return /*#__PURE__*/_jsxDEV(Tabs, {\n          activeKey: activeTab,\n          onChange: setActiveTab,\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u6210\\u5458\",\n            children: /*#__PURE__*/_jsxDEV(MemberTab, {\n              teamId: teamId,\n              spaceId: currentSpaceId,\n              isInDrawer: true,\n              initialGroupId: approvalGroupId || ((_userGrpList$find2 = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)) === null || _userGrpList$find2 === void 0 ? void 0 : _userGrpList$find2.groupId),\n              initialMembergrpType: approvalGroupType || ((_userGrpList$find3 = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)) === null || _userGrpList$find3 === void 0 ? void 0 : _userGrpList$find3.membergrpType)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)\n          }, \"member\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u7BA1\\u7406\\u5458\",\n            children: /*#__PURE__*/_jsxDEV(AdminTab, {\n              teamId: teamId,\n              spaceId: currentSpaceId,\n              isInDrawer: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)\n          }, \"admin\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u9080\\u8BF7\\u5386\\u53F2\",\n            children: /*#__PURE__*/_jsxDEV(InviteHistory, {\n              teamId: teamId,\n              spaceId: currentSpaceId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, \"invite\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this);\n      case 'personal':\n        return /*#__PURE__*/_jsxDEV(Personal, {\n          teamId: teamId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 16\n        }, this);\n      case 'space':\n        // 非管理员只显示成员Tab\n        if (!(isManager || isSpaceManager || teamSpaceAdminFlag)) {\n          const allMemberGroup = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members);\n          return /*#__PURE__*/_jsxDEV(Tabs, {\n            activeKey: 'member',\n            onChange: setActiveTab,\n            children: /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: \"\\u6210\\u5458\",\n              children: /*#__PURE__*/_jsxDEV(MemberTab, {\n                teamId: teamId,\n                spaceId: currentSpaceId,\n                isInDrawer: true,\n                initialGroupId: allMemberGroup === null || allMemberGroup === void 0 ? void 0 : allMemberGroup.groupId,\n                initialMembergrpType: allMemberGroup === null || allMemberGroup === void 0 ? void 0 : allMemberGroup.membergrpType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, \"member\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this);\n        }\n        // 管理员显示全部Tab\n        return /*#__PURE__*/_jsxDEV(Tabs, {\n          activeKey: activeTab,\n          onChange: setActiveTab,\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u89D2\\u8272\\u6743\\u9650\",\n            children: /*#__PURE__*/_jsxDEV(SpaceRoleTab, {\n              teamId: teamId,\n              spaceId: currentSpaceId,\n              isInDrawer: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, \"role\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u6210\\u5458\",\n            children: /*#__PURE__*/_jsxDEV(MemberTab, {\n              teamId: teamId,\n              spaceId: currentSpaceId,\n              isInDrawer: true,\n              initialGroupId: (_userGrpList$find4 = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)) === null || _userGrpList$find4 === void 0 ? void 0 : _userGrpList$find4.groupId,\n              initialMembergrpType: (_userGrpList$find5 = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)) === null || _userGrpList$find5 === void 0 ? void 0 : _userGrpList$find5.membergrpType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, \"member\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u56DE\\u6536\\u7AD9\",\n            children: /*#__PURE__*/_jsxDEV(SpaceTrashTab, {\n              teamId: teamId,\n              spaceId: currentSpaceId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)\n          }, \"trash\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5916\\u94FE\\u5206\\u4EAB\",\n            children: /*#__PURE__*/_jsxDEV(SpaceShareLinkHistoryTab, {\n              teamId: teamId,\n              spaceId: currentSpaceId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)\n          }, \"shareLinkHistory\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u9080\\u8BF7\\u5386\\u53F2\",\n            children: /*#__PURE__*/_jsxDEV(InviteHistory, {\n              teamId: teamId,\n              spaceId: currentSpaceId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, \"invite\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this);\n      case 'approval':\n        const approvalGroup = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_2_apply_join);\n        if (approvalGroup) {\n          return /*#__PURE__*/_jsxDEV(MemberTab_Approval, {\n            teamId: teamId,\n            spaceId: null,\n            selectedGroupId: approvalGroup.groupId,\n            selectedMembergrpType: approvalGroup.membergrpType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 18\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u672A\\u627E\\u5230\\u7533\\u8BF7\\u52A0\\u5165\\u5206\\u7EC4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 18\n          }, this);\n        }\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u7F6E\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 新的关闭逻辑\n  const handleDrawerClose = () => {\n    setCloseConfirmVisible(true);\n  };\n  const handleCloseConfirm = () => {\n    setCloseConfirmVisible(false);\n    onClose && onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(DraggableDrawer, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u56E2\\u961F\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: 20,\n          children: [!!approvalNum && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            onClick: handleApprovalClick,\n            style: {\n              padding: 0\n            },\n            children: [\"\\u7533\\u8BF7\\u52A0\\u5165(\", approvalNum, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 17\n          }, this), (isManager || isSpaceManager || teamSpaceAdminFlag) &&\n          /*#__PURE__*/\n          // 团队管理员 && 当前群管理员 &&   团队中某一个群管理员即为true\n          _jsxDEV(Button, {\n            type: \"primary\",\n            className: \"defaultBtn\",\n            onClick: () => setCreateInvitedMemberVisible(true),\n            children: \"\\u9080\\u8BF7\\u6210\\u5458\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this),\n      placement: \"right\",\n      width: \"85%\",\n      minWidth: \"40%\",\n      maxWidth: \"95%\",\n      draggableFlag: true,\n      open: visible,\n      onClose: handleDrawerClose,\n      className: \"settings-drawer\",\n      children: [/*#__PURE__*/_jsxDEV(Layout, {\n        className: \"WorkSetUp-layout\",\n        children: [/*#__PURE__*/_jsxDEV(Sider, {\n          width: 260,\n          className: \"WorkSetUp-layout-sider\",\n          children: /*#__PURE__*/_jsxDEV(SettingsSideBar, {\n            teamId: teamId,\n            isInDrawer: true,\n            onContentChange: handleContentChange,\n            activeKey: currentContent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Content, {\n          className: \"WorkSetUp-layout-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"WorkSetUp-layout-content-inner\",\n            style: {\n              padding: '20px 20px'\n            },\n            children: renderContent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CreateInvitedMember, {\n        addPopVisible: createInvitedMemberVisible,\n        onClose: () => setCreateInvitedMemberVisible(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DraggablePopUp, {\n      className: \"settings-drawer-close-confirm\",\n      title: \"\\u63D0\\u793A\",\n      icon: \"warning\",\n      width: 300,\n      destroyOnClose: true,\n      open: closeConfirmVisible,\n      onOk: handleCloseConfirm,\n      onCancel: () => setCloseConfirmVisible(false),\n      content: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u662F\\u5426\\u786E\\u5B9A\\u5173\\u95ED\\u56E2\\u961F\\u8BBE\\u7F6E\\u5BF9\\u8BDD\\u6846?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 18\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(SettingsDrawer, \"UiPaB6QuPOz9Ks8BOpMeV9BVDUU=\", false, function () {\n  return [useSelector, useQuerySetting327_getTeamSpaceAadmin];\n});\n_c = SettingsDrawer;\nvar _c;\n$RefreshReg$(_c, \"SettingsDrawer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Layout", "Drawer", "<PERSON><PERSON>", "Space", "Tabs", "useParams", "Link", "useLocation", "useNavigate", "shallowEqual", "useDispatch", "useSelector", "eMembergrpType", "CreateInvitedMember", "SettingsSideBar", "BasicInfoTab", "TeamDomainSmtpTab", "Personal_Tab_Subscribe", "Personal_Tab_Data_Import", "Settings_Product", "MemberTab", "AdminTab", "InviteHistory", "Personal", "SpaceRoleTab", "SpaceTrashTab", "SpaceShareLinkHistoryTab", "MemberTab_Approval", "DraggablePopUp", "DraggableDrawer", "useQuerySetting327_getTeamSpaceAadmin", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Content", "TabPane", "SettingsDrawer", "visible", "onClose", "teamId", "defaultTab", "productId", "_s", "approvalNum", "setApprovalNum", "createInvitedMemberVisible", "setCreateInvitedMemberVisible", "currentC<PERSON>nt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentSpaceId", "setCurrentSpaceId", "activeTab", "setActiveTab", "closeConfirmVisible", "setCloseConfirmVisible", "approvalGroupId", "setApprovalGroupId", "approvalGroupType", "setApprovalGroupType", "prevTitle", "document", "title", "current", "state", "userGrpList", "getIn", "data", "teamAdminFlag", "is<PERSON>anager", "spaceAdminFlag", "isSpaceManager", "teamSpaceAdminFlag", "undefined", "spaceId", "enabled", "console", "log", "length", "_userGrpList$find", "approvalCount", "find", "e", "membergrpType", "grp_2_apply_join", "membergrpNum", "handleApprovalClick", "approvalGroup", "item", "groupId", "handleContentChange", "content", "tab", "renderContent", "_userGrpList$find2", "_userGrpList$find3", "_userGrpList$find4", "_userGrpList$find5", "active<PERSON><PERSON>", "onChange", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isInDrawer", "initialGroupId", "grp_1_all_members", "initialMembergrpType", "allMemberGroup", "selectedGroupId", "selectedMembergrpType", "handleDrawerClose", "handleCloseConfirm", "style", "display", "justifyContent", "alignItems", "size", "type", "onClick", "padding", "className", "placement", "width", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "draggableFlag", "open", "onContentChange", "addPopVisible", "icon", "destroyOnClose", "onOk", "onCancel", "_c", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/settings/views/SettingsDrawer.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { Layout, Drawer, Button, Space, Tabs } from \"antd\";\r\nimport { useParams, Link, useLocation, useNavigate } from \"react-router-dom\";\r\nimport { shallowEqual, useDispatch, useSelector } from \"react-redux\";\r\nimport { eMembergrpType } from \"@common/utils/enum\";\r\nimport CreateInvitedMember from \"src/settings/utils/CreateInvitedMember/CreateInvitedMember\";\r\nimport SettingsSideBar from \"./SettingsSideBar\";\r\n\r\n// 导入设置页面组件\r\nimport BasicInfoTab from \"./basicInfo/BasicInfoTab\";\r\nimport TeamDomainSmtpTab from \"./basicInfo/SmtpTab\";\r\nimport Personal_Tab_Subscribe from \"./personal/Manager_Tab_Subscribe\";\r\nimport Personal_Tab_Data_Import from \"./personal/Personal_Tab_Data_Import\";\r\nimport Settings_Product from \"./product/Settings_Product\";\r\nimport MemberTab from \"./user/MemberTab\";\r\nimport AdminTab from \"./user/AdminTab\";\r\nimport InviteHistory from \"./user/InviteHistory\";\r\nimport Personal from \"./personal/Personal\";\r\nimport SpaceRoleTab from \"./space/SpaceRoleTab\";\r\nimport SpaceTrashTab from \"./space/SpaceTrashTab\";\r\nimport SpaceShareLinkHistoryTab from \"./space/SpaceShareLinkHistoryTab\";\r\nimport MemberTab_Approval from \"./user/MemberTab_Approval\";\r\nimport DraggablePopUp from '@components/DraggablePopUp';\r\nimport DraggableDrawer from '@common/components/DraggableDrawer';\r\nimport { useQuerySetting327_getTeamSpaceAadmin } from \"@common/service/commonHooks\";\r\n\r\nimport \"./SettingsDrawer.scss\";\r\n\r\nconst { Sider, Content } = Layout;\r\nconst { TabPane } = Tabs;\r\n\r\nexport default function SettingsDrawer({ visible, onClose, teamId, defaultTab,productId }) { //productId: 是打开\"应用管理\"某一个产品的的授权对话框\r\n  const [approvalNum, setApprovalNum] = useState(0);\r\n  const [createInvitedMemberVisible, setCreateInvitedMemberVisible] = useState(false);\r\n  const [currentContent, setCurrentContent] = useState(defaultTab || 'basic');\r\n  const [currentSpaceId, setCurrentSpaceId] = useState(null);\r\n  const [activeTab, setActiveTab] = useState('base-info');\r\n  const [closeConfirmVisible, setCloseConfirmVisible] = useState(false);\r\n  // 新增：用于高亮“申请加入”分组\r\n  const [approvalGroupId, setApprovalGroupId] = useState(null);\r\n  const [approvalGroupType, setApprovalGroupType] = useState(null);\r\n\r\n  // 新增：动态设置浏览器标题\r\n  const prevTitle = useRef(document.title);\r\n  useEffect(() => {\r\n    if (visible) {\r\n      prevTitle.current = document.title;\r\n      document.title = \"团队设置\";\r\n    } else {\r\n      document.title = prevTitle.current;\r\n    }\r\n    return () => {\r\n      document.title = prevTitle.current;\r\n    };\r\n  }, [visible]);\r\n\r\n  // 监听defaultTab变化，动态切换tab\r\n  useEffect(() => {\r\n    if (visible && defaultTab) {\r\n      setCurrentContent(defaultTab);\r\n      // 根据tab类型设置activeTab\r\n      if (defaultTab === 'basic') setActiveTab('base-info');\r\n      if (defaultTab === 'user') setActiveTab('member');\r\n      if (defaultTab === 'product') setActiveTab('product');\r\n    }\r\n  }, [visible, defaultTab]);\r\n\r\n  const state = useSelector((state) => ({\r\n    userGrpList: state.getIn([\"workSetUp\", \"userGrpList\"]),\r\n  }), shallowEqual);\r\n  const userGrpList = state.userGrpList || [];\r\n  //const { spaceId } = useParams();\r\n  // 提前解构，避免未初始化时被访问\r\n  const { data: { teamAdminFlag: isManager, spaceAdminFlag: isSpaceManager, teamSpaceAdminFlag } //teamSpaceAdminFlag表示，只要是团队中的“某一个群”的管理员，它就为true\r\n    = { teamAdminFlag: undefined, spaceAdminFlag: undefined, teamSpaceAdminFlag: undefined },\r\n  } = useQuerySetting327_getTeamSpaceAadmin({ teamId, spaceId: currentSpaceId, enabled: true });\r\n\r\n  // 自动切换Tab副作用，避免在renderContent中setState\r\n  useEffect(() => {\r\n    if (\r\n      currentContent === 'space' &&\r\n      !(isManager || isSpaceManager || teamSpaceAdminFlag) &&\r\n      activeTab !== 'member'\r\n    ) {\r\n      setActiveTab('member');\r\n    }\r\n  }, [currentContent, isManager, isSpaceManager, teamSpaceAdminFlag, activeTab]);\r\n\r\n  // 设置申请加入数量\r\n  useEffect(() => {\r\n    console.log('SettingsDrawer: userGrpList changed:', userGrpList);\r\n    if ((userGrpList || []).length > 0) {\r\n      const approvalCount = userGrpList.find(e => e.membergrpType === eMembergrpType.grp_2_apply_join)?.membergrpNum || 0;\r\n      console.log('SettingsDrawer: setting approvalNum to:', approvalCount);\r\n      setApprovalNum(approvalCount);\r\n    }\r\n  }, [userGrpList]);\r\n\r\n  // 处理申请加入点击 - 显示申请列表并高亮分组\r\n  const handleApprovalClick = () => {\r\n    setCurrentContent('user');\r\n    // 找到“申请加入”分组\r\n    const approvalGroup = userGrpList.find(item => item.membergrpType === 2); // eMembergrpType.grp_2_apply_join = 2\r\n    if (approvalGroup) {\r\n      setApprovalGroupId(approvalGroup.groupId);\r\n      setApprovalGroupType(approvalGroup.membergrpType);\r\n    }\r\n  };\r\n\r\n  // 处理SettingsSideBar的内容变化\r\n  const handleContentChange = (content, spaceId = null, tab = null) => {\r\n    console.log('SettingsDrawer: content changed to', content, spaceId, tab);\r\n    setCurrentContent(content);\r\n    setCurrentSpaceId(spaceId);\r\n    if (tab) {\r\n      setActiveTab(tab);\r\n    } else if (content === 'basic') {\r\n      // 当选择\"基础设置\"时，默认选中\"基本设置\"Tab\r\n      setActiveTab('base-info');\r\n    } else if (content === 'user') {\r\n      // 当选择\"成员及管理员\"时，默认选中\"成员\"Tab\r\n      setActiveTab('member');\r\n    } else if (content === 'space') {\r\n      // 当选择协作群时，默认选中\"角色权限\"Tab\r\n      setActiveTab('role');\r\n    }\r\n  };\r\n\r\n  // 渲染右侧内容\r\n  const renderContent = () => {\r\n    switch (currentContent) {\r\n      case 'basic':\r\n        return (\r\n          <Tabs activeKey={activeTab} onChange={setActiveTab}>\r\n            <TabPane tab=\"基本设置\" key=\"base-info\">\r\n              <BasicInfoTab teamId={teamId} />\r\n            </TabPane>\r\n            <TabPane tab=\"域名&SMTP服务器\" key=\"smtp\">\r\n              <TeamDomainSmtpTab teamId={teamId} />\r\n            </TabPane>\r\n            <TabPane tab=\"系统推送设置\" key=\"subscribe\">\r\n              <Personal_Tab_Subscribe teamId={teamId} />\r\n            </TabPane>\r\n            <TabPane tab=\"数据导入\" key=\"import\">\r\n              <Personal_Tab_Data_Import teamId={teamId} />\r\n            </TabPane>\r\n          </Tabs>\r\n        );\r\n      case 'product':\r\n        return <Settings_Product teamId={teamId} productId={productId} visible={visible}/>;\r\n      case 'user':\r\n        return (\r\n          <Tabs activeKey={activeTab} onChange={setActiveTab}>\r\n            <TabPane tab=\"成员\" key=\"member\">\r\n              <MemberTab\r\n                teamId={teamId}\r\n                spaceId={currentSpaceId}\r\n                isInDrawer={true}\r\n                initialGroupId={approvalGroupId || (userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)?.groupId)}\r\n                initialMembergrpType={approvalGroupType || (userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)?.membergrpType)}\r\n              />\r\n            </TabPane>\r\n            <TabPane tab=\"管理员\" key=\"admin\">\r\n              <AdminTab teamId={teamId} spaceId={currentSpaceId} isInDrawer={true} />\r\n            </TabPane>\r\n            <TabPane tab=\"邀请历史\" key=\"invite\">\r\n              <InviteHistory teamId={teamId} spaceId={currentSpaceId} />\r\n            </TabPane>\r\n          </Tabs>\r\n        );\r\n      case 'personal':\r\n        return <Personal teamId={teamId} />;\r\n      case 'space':\r\n        // 非管理员只显示成员Tab\r\n        if (!(isManager || isSpaceManager || teamSpaceAdminFlag)) {\r\n          const allMemberGroup = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members);\r\n          return (\r\n            <Tabs activeKey={'member'} onChange={setActiveTab}>\r\n              <TabPane tab=\"成员\" key=\"member\">\r\n                <MemberTab\r\n                  teamId={teamId}\r\n                  spaceId={currentSpaceId}\r\n                  isInDrawer={true}\r\n                  initialGroupId={allMemberGroup?.groupId}\r\n                  initialMembergrpType={allMemberGroup?.membergrpType}\r\n                />\r\n              </TabPane>\r\n            </Tabs>\r\n          );\r\n        }\r\n        // 管理员显示全部Tab\r\n        return (\r\n          <Tabs activeKey={activeTab} onChange={setActiveTab}>\r\n            <TabPane tab=\"角色权限\" key=\"role\">\r\n              <SpaceRoleTab teamId={teamId} spaceId={currentSpaceId} isInDrawer={true} />\r\n            </TabPane>\r\n            <TabPane tab=\"成员\" key=\"member\">\r\n              <MemberTab \r\n                teamId={teamId} \r\n                spaceId={currentSpaceId} \r\n                isInDrawer={true}\r\n                initialGroupId={userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)?.groupId}\r\n                initialMembergrpType={userGrpList.find(item => item.membergrpType === eMembergrpType.grp_1_all_members)?.membergrpType}\r\n              />\r\n            </TabPane>\r\n            <TabPane tab=\"回收站\" key=\"trash\">\r\n              <SpaceTrashTab teamId={teamId} spaceId={currentSpaceId} />\r\n            </TabPane>\r\n            <TabPane tab=\"外链分享\" key=\"shareLinkHistory\">\r\n              <SpaceShareLinkHistoryTab teamId={teamId} spaceId={currentSpaceId} />\r\n            </TabPane>\r\n            <TabPane tab=\"邀请历史\" key=\"invite\">\r\n              <InviteHistory teamId={teamId} spaceId={currentSpaceId} />\r\n            </TabPane>\r\n          </Tabs>\r\n        );\r\n      case 'approval':\r\n        const approvalGroup = userGrpList.find(item => item.membergrpType === eMembergrpType.grp_2_apply_join);\r\n        if (approvalGroup) {\r\n          return <MemberTab_Approval \r\n            teamId={teamId} \r\n            spaceId={null} \r\n            selectedGroupId={approvalGroup.groupId}\r\n            selectedMembergrpType={approvalGroup.membergrpType}\r\n          />;\r\n        } else {\r\n          return <div>未找到申请加入分组</div>;\r\n        }\r\n      default:\r\n        return <div>请选择设置项</div>;\r\n    }\r\n  };\r\n\r\n  // 新的关闭逻辑\r\n  const handleDrawerClose = () => {\r\n    setCloseConfirmVisible(true);\r\n  };\r\n  const handleCloseConfirm = () => {\r\n    setCloseConfirmVisible(false);\r\n    onClose && onClose();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <DraggableDrawer\r\n        title={\r\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n            <span>团队设置</span>\r\n              <Space size={20}>\r\n              {!!approvalNum && (\r\n                <Button \r\n                  type=\"link\" \r\n                  onClick={handleApprovalClick}\r\n                  style={{ padding: 0 }}\r\n                >\r\n                  申请加入({approvalNum})\r\n                </Button>\r\n              )}\r\n                {/*tmsbug-12931 非管理员可以看到 邀请成员按钮 https://os.iteam.com/#/team/9176631781/issues/3914480416/issue/103097772323736*/}\r\n              {(isManager || isSpaceManager || teamSpaceAdminFlag) && ( // 团队管理员 && 当前群管理员 &&   团队中某一个群管理员即为true\r\n                <Button type=\"primary\" className=\"defaultBtn\" onClick={()=>setCreateInvitedMemberVisible(true)}>邀请成员</Button>\r\n              )}\r\n              </Space>\r\n          </div>\r\n        }\r\n        placement=\"right\"\r\n        width=\"85%\"\r\n        minWidth=\"40%\"\r\n        maxWidth=\"95%\"\r\n        draggableFlag={true}\r\n        open={visible}\r\n        onClose={handleDrawerClose}\r\n        className=\"settings-drawer\"\r\n      >\r\n        <Layout className=\"WorkSetUp-layout\">\r\n          <Sider width={260} className=\"WorkSetUp-layout-sider\">\r\n            <SettingsSideBar \r\n              teamId={teamId} \r\n              isInDrawer={true}\r\n              onContentChange={handleContentChange}\r\n              activeKey={currentContent}\r\n            />\r\n          </Sider>\r\n          <Content className=\"WorkSetUp-layout-content\">\r\n            <div className=\"WorkSetUp-layout-content-inner\" style={{ padding: '20px 20px' }}>\r\n              {renderContent()}\r\n            </div>\r\n          </Content>\r\n        </Layout>\r\n        <CreateInvitedMember addPopVisible={createInvitedMemberVisible} onClose={()=>setCreateInvitedMemberVisible(false)}/>\r\n      </DraggableDrawer>\r\n      <DraggablePopUp\r\n        className=\"settings-drawer-close-confirm\"\r\n        title=\"提示\"\r\n        icon=\"warning\"\r\n        width={300}\r\n        destroyOnClose={true}\r\n        open={closeConfirmVisible}\r\n        onOk={handleCloseConfirm}\r\n        onCancel={() => setCloseConfirmVisible(false)}\r\n        content={<span>是否确定关闭团队设置对话框?</span>}\r\n      />\r\n    </>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AAC1D,SAASC,SAAS,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC5E,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,mBAAmB,MAAM,4DAA4D;AAC5F,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AACA,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,OAAOC,wBAAwB,MAAM,qCAAqC;AAC1E,OAAOC,gBAAgB,MAAM,4BAA4B;AACzD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,wBAAwB,MAAM,kCAAkC;AACvE,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,SAASC,qCAAqC,QAAQ,6BAA6B;AAEnF,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGpC,MAAM;AACjC,MAAM;EAAEqC;AAAQ,CAAC,GAAGjC,IAAI;AAExB,eAAe,SAASkC,cAAcA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC,UAAU;EAACC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAAE;EAC3F,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC6C,UAAU,IAAI,OAAO,CAAC;EAC3E,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC0D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACrE;EACA,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAMgE,SAAS,GAAG9D,MAAM,CAAC+D,QAAQ,CAACC,KAAK,CAAC;EACxCjE,SAAS,CAAC,MAAM;IACd,IAAIyC,OAAO,EAAE;MACXsB,SAAS,CAACG,OAAO,GAAGF,QAAQ,CAACC,KAAK;MAClCD,QAAQ,CAACC,KAAK,GAAG,MAAM;IACzB,CAAC,MAAM;MACLD,QAAQ,CAACC,KAAK,GAAGF,SAAS,CAACG,OAAO;IACpC;IACA,OAAO,MAAM;MACXF,QAAQ,CAACC,KAAK,GAAGF,SAAS,CAACG,OAAO;IACpC,CAAC;EACH,CAAC,EAAE,CAACzB,OAAO,CAAC,CAAC;;EAEb;EACAzC,SAAS,CAAC,MAAM;IACd,IAAIyC,OAAO,IAAIG,UAAU,EAAE;MACzBQ,iBAAiB,CAACR,UAAU,CAAC;MAC7B;MACA,IAAIA,UAAU,KAAK,OAAO,EAAEY,YAAY,CAAC,WAAW,CAAC;MACrD,IAAIZ,UAAU,KAAK,MAAM,EAAEY,YAAY,CAAC,QAAQ,CAAC;MACjD,IAAIZ,UAAU,KAAK,SAAS,EAAEY,YAAY,CAAC,SAAS,CAAC;IACvD;EACF,CAAC,EAAE,CAACf,OAAO,EAAEG,UAAU,CAAC,CAAC;EAEzB,MAAMuB,KAAK,GAAGtD,WAAW,CAAEsD,KAAK,KAAM;IACpCC,WAAW,EAAED,KAAK,CAACE,KAAK,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC;EACvD,CAAC,CAAC,EAAE1D,YAAY,CAAC;EACjB,MAAMyD,WAAW,GAAGD,KAAK,CAACC,WAAW,IAAI,EAAE;EAC3C;EACA;EACA,MAAM;IAAEE,IAAI,EAAE;MAAEC,aAAa,EAAEC,SAAS;MAAEC,cAAc,EAAEC,cAAc;MAAEC;IAAmB,CAAC,CAAC;IAAA,EAC3F;MAAEJ,aAAa,EAAEK,SAAS;MAAEH,cAAc,EAAEG,SAAS;MAAED,kBAAkB,EAAEC;IAAU;EACzF,CAAC,GAAG5C,qCAAqC,CAAC;IAAEW,MAAM;IAAEkC,OAAO,EAAExB,cAAc;IAAEyB,OAAO,EAAE;EAAK,CAAC,CAAC;;EAE7F;EACA9E,SAAS,CAAC,MAAM;IACd,IACEmD,cAAc,KAAK,OAAO,IAC1B,EAAEqB,SAAS,IAAIE,cAAc,IAAIC,kBAAkB,CAAC,IACpDpB,SAAS,KAAK,QAAQ,EACtB;MACAC,YAAY,CAAC,QAAQ,CAAC;IACxB;EACF,CAAC,EAAE,CAACL,cAAc,EAAEqB,SAAS,EAAEE,cAAc,EAAEC,kBAAkB,EAAEpB,SAAS,CAAC,CAAC;;EAE9E;EACAvD,SAAS,CAAC,MAAM;IACd+E,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEZ,WAAW,CAAC;IAChE,IAAI,CAACA,WAAW,IAAI,EAAE,EAAEa,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAC,iBAAA;MAClC,MAAMC,aAAa,GAAG,EAAAD,iBAAA,GAAAd,WAAW,CAACgB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAKxE,cAAc,CAACyE,gBAAgB,CAAC,cAAAL,iBAAA,uBAA1EA,iBAAA,CAA4EM,YAAY,KAAI,CAAC;MACnHT,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEG,aAAa,CAAC;MACrEnC,cAAc,CAACmC,aAAa,CAAC;IAC/B;EACF,CAAC,EAAE,CAACf,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMqB,mBAAmB,GAAGA,CAAA,KAAM;IAChCrC,iBAAiB,CAAC,MAAM,CAAC;IACzB;IACA,MAAMsC,aAAa,GAAGtB,WAAW,CAACgB,IAAI,CAACO,IAAI,IAAIA,IAAI,CAACL,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,IAAII,aAAa,EAAE;MACjB9B,kBAAkB,CAAC8B,aAAa,CAACE,OAAO,CAAC;MACzC9B,oBAAoB,CAAC4B,aAAa,CAACJ,aAAa,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMO,mBAAmB,GAAGA,CAACC,OAAO,EAAEjB,OAAO,GAAG,IAAI,EAAEkB,GAAG,GAAG,IAAI,KAAK;IACnEhB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEc,OAAO,EAAEjB,OAAO,EAAEkB,GAAG,CAAC;IACxE3C,iBAAiB,CAAC0C,OAAO,CAAC;IAC1BxC,iBAAiB,CAACuB,OAAO,CAAC;IAC1B,IAAIkB,GAAG,EAAE;MACPvC,YAAY,CAACuC,GAAG,CAAC;IACnB,CAAC,MAAM,IAAID,OAAO,KAAK,OAAO,EAAE;MAC9B;MACAtC,YAAY,CAAC,WAAW,CAAC;IAC3B,CAAC,MAAM,IAAIsC,OAAO,KAAK,MAAM,EAAE;MAC7B;MACAtC,YAAY,CAAC,QAAQ,CAAC;IACxB,CAAC,MAAM,IAAIsC,OAAO,KAAK,OAAO,EAAE;MAC9B;MACAtC,YAAY,CAAC,MAAM,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMwC,aAAa,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IAC1B,QAAQjD,cAAc;MACpB,KAAK,OAAO;QACV,oBACEjB,OAAA,CAAC5B,IAAI;UAAC+F,SAAS,EAAE9C,SAAU;UAAC+C,QAAQ,EAAE9C,YAAa;UAAA+C,QAAA,gBACjDrE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,0BAAM;YAAAQ,QAAA,eACjBrE,OAAA,CAACjB,YAAY;cAAC0B,MAAM,EAAEA;YAAO;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADV,WAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE1B,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,qCAAY;YAAAQ,QAAA,eACvBrE,OAAA,CAAChB,iBAAiB;cAACyB,MAAM,EAAEA;YAAO;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADT,MAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3B,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,sCAAQ;YAAAQ,QAAA,eACnBrE,OAAA,CAACf,sBAAsB;cAACwB,MAAM,EAAEA;YAAO;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADlB,WAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,0BAAM;YAAAQ,QAAA,eACjBrE,OAAA,CAACd,wBAAwB;cAACuB,MAAM,EAAEA;YAAO;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADtB,QAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEX,KAAK,SAAS;QACZ,oBAAOzE,OAAA,CAACb,gBAAgB;UAACsB,MAAM,EAAEA,MAAO;UAACE,SAAS,EAAEA,SAAU;UAACJ,OAAO,EAAEA;QAAQ;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MACpF,KAAK,MAAM;QACT,oBACEzE,OAAA,CAAC5B,IAAI;UAAC+F,SAAS,EAAE9C,SAAU;UAAC+C,QAAQ,EAAE9C,YAAa;UAAA+C,QAAA,gBACjDrE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,cAAI;YAAAQ,QAAA,eACfrE,OAAA,CAACZ,SAAS;cACRqB,MAAM,EAAEA,MAAO;cACfkC,OAAO,EAAExB,cAAe;cACxBuD,UAAU,EAAE,IAAK;cACjBC,cAAc,EAAElD,eAAe,MAAAsC,kBAAA,GAAK7B,WAAW,CAACgB,IAAI,CAACO,IAAI,IAAIA,IAAI,CAACL,aAAa,KAAKxE,cAAc,CAACgG,iBAAiB,CAAC,cAAAb,kBAAA,uBAAjFA,kBAAA,CAAmFL,OAAO,CAAE;cAChImB,oBAAoB,EAAElD,iBAAiB,MAAAqC,kBAAA,GAAK9B,WAAW,CAACgB,IAAI,CAACO,IAAI,IAAIA,IAAI,CAACL,aAAa,KAAKxE,cAAc,CAACgG,iBAAiB,CAAC,cAAAZ,kBAAA,uBAAjFA,kBAAA,CAAmFZ,aAAa;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/I;UAAC,GAPkB,QAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQrB,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,oBAAK;YAAAQ,QAAA,eAChBrE,OAAA,CAACX,QAAQ;cAACoB,MAAM,EAAEA,MAAO;cAACkC,OAAO,EAAExB,cAAe;cAACuD,UAAU,EAAE;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADlD,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,0BAAM;YAAAQ,QAAA,eACjBrE,OAAA,CAACV,aAAa;cAACmB,MAAM,EAAEA,MAAO;cAACkC,OAAO,EAAExB;YAAe;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADpC,QAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEX,KAAK,UAAU;QACb,oBAAOzE,OAAA,CAACT,QAAQ;UAACkB,MAAM,EAAEA;QAAO;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrC,KAAK,OAAO;QACV;QACA,IAAI,EAAEnC,SAAS,IAAIE,cAAc,IAAIC,kBAAkB,CAAC,EAAE;UACxD,MAAMqC,cAAc,GAAG5C,WAAW,CAACgB,IAAI,CAACO,IAAI,IAAIA,IAAI,CAACL,aAAa,KAAKxE,cAAc,CAACgG,iBAAiB,CAAC;UACxG,oBACE5E,OAAA,CAAC5B,IAAI;YAAC+F,SAAS,EAAE,QAAS;YAACC,QAAQ,EAAE9C,YAAa;YAAA+C,QAAA,eAChDrE,OAAA,CAACK,OAAO;cAACwD,GAAG,EAAC,cAAI;cAAAQ,QAAA,eACfrE,OAAA,CAACZ,SAAS;gBACRqB,MAAM,EAAEA,MAAO;gBACfkC,OAAO,EAAExB,cAAe;gBACxBuD,UAAU,EAAE,IAAK;gBACjBC,cAAc,EAAEG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEpB,OAAQ;gBACxCmB,oBAAoB,EAAEC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE1B;cAAc;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC,GAPkB,QAAQ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAEX;QACA;QACA,oBACEzE,OAAA,CAAC5B,IAAI;UAAC+F,SAAS,EAAE9C,SAAU;UAAC+C,QAAQ,EAAE9C,YAAa;UAAA+C,QAAA,gBACjDrE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,0BAAM;YAAAQ,QAAA,eACjBrE,OAAA,CAACR,YAAY;cAACiB,MAAM,EAAEA,MAAO;cAACkC,OAAO,EAAExB,cAAe;cAACuD,UAAU,EAAE;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADrD,MAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,cAAI;YAAAQ,QAAA,eACfrE,OAAA,CAACZ,SAAS;cACRqB,MAAM,EAAEA,MAAO;cACfkC,OAAO,EAAExB,cAAe;cACxBuD,UAAU,EAAE,IAAK;cACjBC,cAAc,GAAAV,kBAAA,GAAE/B,WAAW,CAACgB,IAAI,CAACO,IAAI,IAAIA,IAAI,CAACL,aAAa,KAAKxE,cAAc,CAACgG,iBAAiB,CAAC,cAAAX,kBAAA,uBAAjFA,kBAAA,CAAmFP,OAAQ;cAC3GmB,oBAAoB,GAAAX,kBAAA,GAAEhC,WAAW,CAACgB,IAAI,CAACO,IAAI,IAAIA,IAAI,CAACL,aAAa,KAAKxE,cAAc,CAACgG,iBAAiB,CAAC,cAAAV,kBAAA,uBAAjFA,kBAAA,CAAmFd;YAAc;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxH;UAAC,GAPkB,QAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQrB,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,oBAAK;YAAAQ,QAAA,eAChBrE,OAAA,CAACP,aAAa;cAACgB,MAAM,EAAEA,MAAO;cAACkC,OAAO,EAAExB;YAAe;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADrC,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,0BAAM;YAAAQ,QAAA,eACjBrE,OAAA,CAACN,wBAAwB;cAACe,MAAM,EAAEA,MAAO;cAACkC,OAAO,EAAExB;YAAe;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAD/C,kBAAkB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEjC,CAAC,eACVzE,OAAA,CAACK,OAAO;YAACwD,GAAG,EAAC,0BAAM;YAAAQ,QAAA,eACjBrE,OAAA,CAACV,aAAa;cAACmB,MAAM,EAAEA,MAAO;cAACkC,OAAO,EAAExB;YAAe;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADpC,QAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEX,KAAK,UAAU;QACb,MAAMjB,aAAa,GAAGtB,WAAW,CAACgB,IAAI,CAACO,IAAI,IAAIA,IAAI,CAACL,aAAa,KAAKxE,cAAc,CAACyE,gBAAgB,CAAC;QACtG,IAAIG,aAAa,EAAE;UACjB,oBAAOxD,OAAA,CAACL,kBAAkB;YACxBc,MAAM,EAAEA,MAAO;YACfkC,OAAO,EAAE,IAAK;YACdoC,eAAe,EAAEvB,aAAa,CAACE,OAAQ;YACvCsB,qBAAqB,EAAExB,aAAa,CAACJ;UAAc;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QACJ,CAAC,MAAM;UACL,oBAAOzE,OAAA;YAAAqE,QAAA,EAAK;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAC7B;MACF;QACE,oBAAOzE,OAAA;UAAAqE,QAAA,EAAK;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzD,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EACD,MAAM0D,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1D,sBAAsB,CAAC,KAAK,CAAC;IAC7BhB,OAAO,IAAIA,OAAO,CAAC,CAAC;EACtB,CAAC;EAED,oBACER,OAAA,CAAAE,SAAA;IAAAmE,QAAA,gBACErE,OAAA,CAACH,eAAe;MACdkC,KAAK,eACH/B,OAAA;QAAKmF,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAjB,QAAA,gBACrFrE,OAAA;UAAAqE,QAAA,EAAM;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfzE,OAAA,CAAC7B,KAAK;UAACoH,IAAI,EAAE,EAAG;UAAAlB,QAAA,GACf,CAAC,CAACxD,WAAW,iBACZb,OAAA,CAAC9B,MAAM;YACLsH,IAAI,EAAC,MAAM;YACXC,OAAO,EAAElC,mBAAoB;YAC7B4B,KAAK,EAAE;cAAEO,OAAO,EAAE;YAAE,CAAE;YAAArB,QAAA,GACvB,2BACM,EAACxD,WAAW,EAAC,GACpB;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,EAEA,CAACnC,SAAS,IAAIE,cAAc,IAAIC,kBAAkB;UAAA;UAAO;UACxDzC,OAAA,CAAC9B,MAAM;YAACsH,IAAI,EAAC,SAAS;YAACG,SAAS,EAAC,YAAY;YAACF,OAAO,EAAEA,CAAA,KAAIzE,6BAA6B,CAAC,IAAI,CAAE;YAAAqD,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAC7G;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN;MACDmB,SAAS,EAAC,OAAO;MACjBC,KAAK,EAAC,KAAK;MACXC,QAAQ,EAAC,KAAK;MACdC,QAAQ,EAAC,KAAK;MACdC,aAAa,EAAE,IAAK;MACpBC,IAAI,EAAE1F,OAAQ;MACdC,OAAO,EAAEyE,iBAAkB;MAC3BU,SAAS,EAAC,iBAAiB;MAAAtB,QAAA,gBAE3BrE,OAAA,CAAChC,MAAM;QAAC2H,SAAS,EAAC,kBAAkB;QAAAtB,QAAA,gBAClCrE,OAAA,CAACG,KAAK;UAAC0F,KAAK,EAAE,GAAI;UAACF,SAAS,EAAC,wBAAwB;UAAAtB,QAAA,eACnDrE,OAAA,CAAClB,eAAe;YACd2B,MAAM,EAAEA,MAAO;YACfiE,UAAU,EAAE,IAAK;YACjBwB,eAAe,EAAEvC,mBAAoB;YACrCQ,SAAS,EAAElD;UAAe;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACRzE,OAAA,CAACI,OAAO;UAACuF,SAAS,EAAC,0BAA0B;UAAAtB,QAAA,eAC3CrE,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAACR,KAAK,EAAE;cAAEO,OAAO,EAAE;YAAY,CAAE;YAAArB,QAAA,EAC7EP,aAAa,CAAC;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACTzE,OAAA,CAACnB,mBAAmB;QAACsH,aAAa,EAAEpF,0BAA2B;QAACP,OAAO,EAAEA,CAAA,KAAIQ,6BAA6B,CAAC,KAAK;MAAE;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrG,CAAC,eAClBzE,OAAA,CAACJ,cAAc;MACb+F,SAAS,EAAC,+BAA+B;MACzC5D,KAAK,EAAC,cAAI;MACVqE,IAAI,EAAC,SAAS;MACdP,KAAK,EAAE,GAAI;MACXQ,cAAc,EAAE,IAAK;MACrBJ,IAAI,EAAE1E,mBAAoB;MAC1B+E,IAAI,EAAEpB,kBAAmB;MACzBqB,QAAQ,EAAEA,CAAA,KAAM/E,sBAAsB,CAAC,KAAK,CAAE;MAC9CoC,OAAO,eAAE5D,OAAA;QAAAqE,QAAA,EAAM;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA,eACF,CAAC;AAEP;AAAC7D,EAAA,CAjRuBN,cAAc;EAAA,QAoCtB3B,WAAW,EAQrBmB,qCAAqC;AAAA;AAAA0G,EAAA,GA5CnBlG,cAAc;AAAA,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}